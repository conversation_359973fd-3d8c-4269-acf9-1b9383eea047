<?php
/**
 * Script para probar el plugin SoloYLibre Gallery Pro corregido
 * Desarrollado por JEYKO AI para <PERSON> Encarnacion (JoseTusabe)
 */

// Cargar WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "🔧 PROBANDO PLUGIN SOLOYLIBRE GALLERY PRO CORREGIDO\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// 1. Verificar plugin activo
$plugin_file = 'Archive/soloylibre-gallery-plugin.php';
$is_active = is_plugin_active($plugin_file);
echo "1. 🔌 Plugin activo: " . ($is_active ? '✅ SÍ' : '❌ NO') . "\n";

// 2. Verificar clases cargadas
echo "\n2. 📚 Clases disponibles:\n";
$classes_to_check = array(
    'SoloYLibre_Gallery_Plugin' => 'Clase principal',
    'SoloYLibre_Gallery_Styles' => 'Estilos de galería',
    'SoloYLibre_Fullscreen_Wizard' => 'Wizard de pantalla completa',
    'SoloYLibre_Content_Protection' => 'Protección de contenido',
    'SoloYLibre_Album_Manager' => 'Gestor de álbumes'
);

foreach ($classes_to_check as $class_name => $description) {
    $exists = class_exists($class_name);
    echo "   " . ($exists ? '✅' : '❌') . " $class_name - $description\n";
}

// 3. Verificar menús admin
echo "\n3. 📋 Menús de administración:\n";
global $menu;
$soloylibre_menus = array();

if (is_array($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && (
            strpos($menu_item[2], 'soloylibre') !== false || 
            strpos($menu_item[0], 'SoloYLibre') !== false ||
            strpos($menu_item[0], '📸') !== false
        )) {
            $soloylibre_menus[] = $menu_item;
        }
    }
}

if (!empty($soloylibre_menus)) {
    foreach ($soloylibre_menus as $menu_item) {
        echo "   ✅ " . strip_tags($menu_item[0]) . " (" . $menu_item[2] . ")\n";
    }
} else {
    echo "   ❌ No se encontraron menús de SoloYLibre\n";
}

// 4. Verificar archivos del wizard
echo "\n4. 🧙‍♂️ Archivos del Wizard:\n";
$wizard_files = array(
    'includes/class-fullscreen-wizard.php' => 'Clase del wizard',
    'assets/css/fullscreen-wizard.css' => 'Estilos del wizard',
    'assets/js/fullscreen-wizard.js' => 'JavaScript del wizard'
);

$plugin_dir = WP_PLUGIN_DIR . '/Archive/';
foreach ($wizard_files as $file => $description) {
    $exists = file_exists($plugin_dir . $file);
    echo "   " . ($exists ? '✅' : '❌') . " $file - $description\n";
    if ($exists) {
        $size = filesize($plugin_dir . $file);
        echo "      📏 Tamaño: " . number_format($size) . " bytes\n";
    }
}

// 5. Verificar shortcodes
echo "\n5. 🔗 Shortcodes disponibles:\n";
global $shortcode_tags;
$soloylibre_shortcodes = array();

foreach ($shortcode_tags as $tag => $callback) {
    if (strpos($tag, 'soloylibre') !== false) {
        $soloylibre_shortcodes[] = $tag;
    }
}

if (!empty($soloylibre_shortcodes)) {
    foreach ($soloylibre_shortcodes as $shortcode) {
        echo "   ✅ [$shortcode]\n";
    }
} else {
    echo "   ❌ No se encontraron shortcodes de SoloYLibre\n";
}

// 6. Verificar custom post types
echo "\n6. 📝 Custom Post Types:\n";
$post_types = get_post_types(array('_builtin' => false), 'objects');
$soloylibre_post_types = array();

foreach ($post_types as $post_type) {
    if (strpos($post_type->name, 'soloylibre') !== false) {
        $soloylibre_post_types[] = $post_type;
    }
}

if (!empty($soloylibre_post_types)) {
    foreach ($soloylibre_post_types as $post_type) {
        echo "   ✅ " . $post_type->name . " - " . $post_type->label . "\n";
        
        // Contar posts
        $count = wp_count_posts($post_type->name);
        echo "      📊 Publicados: " . ($count->publish ?? 0) . ", Borradores: " . ($count->draft ?? 0) . "\n";
    }
} else {
    echo "   ❌ No se encontraron custom post types de SoloYLibre\n";
}

// 7. Verificar base de datos
echo "\n7. 🗄️ Tablas de base de datos:\n";
global $wpdb;
$tables = array(
    $wpdb->prefix . 'soloylibre_albums',
    $wpdb->prefix . 'soloylibre_photo_meta',
    $wpdb->prefix . 'soloylibre_user_interactions',
    $wpdb->prefix . 'soloylibre_projects',
    $wpdb->prefix . 'soloylibre_photographer_settings'
);

foreach ($tables as $table) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
    echo "   " . ($exists ? '✅' : '❌') . " $table\n";
    
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
        echo "      📊 Registros: $count\n";
    }
}

// 8. Verificar opciones del plugin
echo "\n8. ⚙️ Opciones configuradas:\n";
$options = array(
    'soloylibre_gallery_gallery_style' => 'Estilo de galería',
    'soloylibre_gallery_photos_per_page' => 'Fotos por página',
    'soloylibre_gallery_photographer_name' => 'Nombre del fotógrafo',
    'soloylibre_gallery_photographer_brand' => 'Marca'
);

foreach ($options as $option_key => $description) {
    $value = get_option($option_key);
    $exists = $value !== false;
    echo "   " . ($exists ? '✅' : '❌') . " $description: " . ($exists ? $value : 'No configurado') . "\n";
}

// 9. Probar instancia del plugin
echo "\n9. 🔧 Instancia del plugin:\n";
try {
    if (class_exists('SoloYLibre_Gallery_Plugin')) {
        $instance = SoloYLibre_Gallery_Plugin::get_instance();
        echo "   ✅ Instancia creada correctamente\n";
        
        // Verificar propiedades
        $properties = array('gallery_styles', 'fullscreen_wizard', 'content_protection');
        foreach ($properties as $property) {
            if (property_exists($instance, $property) && isset($instance->$property)) {
                echo "   ✅ Propiedad '$property' inicializada\n";
            } else {
                echo "   ❌ Propiedad '$property' no inicializada\n";
            }
        }
    } else {
        echo "   ❌ Clase principal no disponible\n";
    }
} catch (Exception $e) {
    echo "   ❌ Error: " . $e->getMessage() . "\n";
}

// 10. URLs importantes
echo "\n10. 🔗 URLs importantes:\n";
echo "   🌐 Sitio: " . home_url() . "\n";
echo "   ⚙️ Admin: " . admin_url() . "\n";
echo "   🧙‍♂️ Wizard: " . admin_url('admin.php?page=soloylibre-wizard') . "\n";
echo "   📸 Fotos: " . admin_url('edit.php?post_type=soloylibre_photo') . "\n";

// 11. Verificar si hay errores PHP recientes
echo "\n11. 🐛 Verificación de errores:\n";
$error_log_paths = array(
    WP_CONTENT_DIR . '/debug.log',
    ini_get('error_log'),
    '/tmp/php_errors.log'
);

$recent_errors = array();
foreach ($error_log_paths as $log_path) {
    if ($log_path && file_exists($log_path)) {
        $log_content = file_get_contents($log_path);
        $lines = explode("\n", $log_content);
        $recent_lines = array_slice($lines, -20); // Últimas 20 líneas
        
        foreach ($recent_lines as $line) {
            if (!empty($line) && strpos($line, 'soloylibre') !== false) {
                $recent_errors[] = $line;
            }
        }
    }
}

if (!empty($recent_errors)) {
    echo "   ⚠️ Errores recientes encontrados:\n";
    foreach (array_slice($recent_errors, -5) as $error) { // Solo últimos 5
        echo "      " . substr($error, 0, 100) . "...\n";
    }
} else {
    echo "   ✅ No se encontraron errores recientes relacionados con SoloYLibre\n";
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "🎯 RESUMEN FINAL\n";
echo str_repeat("=", 70) . "\n";

$total_checks = 11;
$passed_checks = 0;

// Contar checks pasados
if ($is_active) $passed_checks++;
if (class_exists('SoloYLibre_Gallery_Plugin')) $passed_checks++;
if (!empty($soloylibre_menus)) $passed_checks++;
if (file_exists($plugin_dir . 'includes/class-fullscreen-wizard.php')) $passed_checks++;
if (!empty($soloylibre_shortcodes)) $passed_checks++;
if (!empty($soloylibre_post_types)) $passed_checks++;
if ($wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}soloylibre_albums'") == $wpdb->prefix . 'soloylibre_albums') $passed_checks++;
if (get_option('soloylibre_gallery_photographer_name')) $passed_checks++;
if (empty($recent_errors)) $passed_checks++;

$percentage = round(($passed_checks / $total_checks) * 100);

echo "📊 Estado general: $passed_checks/$total_checks checks pasados ($percentage%)\n\n";

if ($percentage >= 80) {
    echo "🎉 ¡PLUGIN EN BUEN ESTADO!\n";
    echo "✅ El plugin SoloYLibre Gallery Pro está funcionando correctamente.\n";
    echo "🧙‍♂️ Puedes acceder al wizard en: " . admin_url('admin.php?page=soloylibre-wizard') . "\n";
} elseif ($percentage >= 60) {
    echo "⚠️ PLUGIN FUNCIONAL CON MEJORAS NECESARIAS\n";
    echo "🔧 El plugin funciona pero necesita algunas correcciones.\n";
} else {
    echo "❌ PLUGIN NECESITA REPARACIÓN\n";
    echo "🛠️ El plugin tiene problemas críticos que deben solucionarse.\n";
}

echo "\n📞 Información de contacto:\n";
echo "   👨‍💻 Desarrollador: JEYKO AI\n";
echo "   📧 Cliente: Jose L Encarnacion (JoseTusabe)\n";
echo "   📱 Teléfono: ************\n";
echo "   🌐 Sitios: josetusabe.com, soloylibre.com\n";
echo "   🇩🇴 Ubicación: San José de Ocoa, República Dominicana / USA\n";

?>
