<?php
/**
 * <PERSON>ript para crear fotos de muestra en SoloYLibre Gallery Pro
 * Desarrollado por JEYKO AI para Jose L Encarnacion (JoseTusabe)
 */

// Cargar WordPress
require_once('wp-config.php');
require_once('wp-load.php');
require_once('wp-admin/includes/media.php');
require_once('wp-admin/includes/file.php');
require_once('wp-admin/includes/image.php');

echo "📸 Creando fotos de muestra para SoloYLibre Gallery...\n\n";

// Datos de fotos de muestra dominicanas
$sample_photos = array(
    array(
        'title' => 'Amanecer en San José de Ocoa',
        'description' => 'Hermoso amanecer capturado desde las montañas de San José de Ocoa, República Dominicana.',
        'location' => 'San José de Ocoa, República Dominicana',
        'camera_settings' => 'Canon EOS R5, 24-70mm f/2.8, ISO 100, f/8, 1/125s',
        'tags' => 'amanecer, montañas, dominicana, paisaje',
        'category' => 'paisajes'
    ),
    array(
        'title' => 'Playa Caribe Dominicana',
        'description' => 'Aguas cristalinas del Caribe dominicano con palmeras y arena blanca.',
        'location' => 'Punta Cana, República Dominicana',
        'camera_settings' => 'Canon EOS R5, 16-35mm f/2.8, ISO 200, f/11, 1/250s',
        'tags' => 'playa, caribe, palmeras, turismo',
        'category' => 'paisajes'
    ),
    array(
        'title' => 'Arquitectura Colonial Santo Domingo',
        'description' => 'Hermosa arquitectura colonial en la Zona Colonial de Santo Domingo.',
        'location' => 'Santo Domingo, República Dominicana',
        'camera_settings' => 'Canon EOS R5, 50mm f/1.4, ISO 400, f/5.6, 1/60s',
        'tags' => 'arquitectura, colonial, historia, cultura',
        'category' => 'arquitectura'
    ),
    array(
        'title' => 'Retrato Dominicano',
        'description' => 'Retrato profesional capturando la esencia y belleza del pueblo dominicano.',
        'location' => 'Santiago, República Dominicana',
        'camera_settings' => 'Canon EOS R5, 85mm f/1.2, ISO 800, f/2.8, 1/125s',
        'tags' => 'retrato, personas, cultura, dominicana',
        'category' => 'retratos'
    ),
    array(
        'title' => 'Cascada El Limón',
        'description' => 'Espectacular cascada El Limón en Samaná, una joya natural dominicana.',
        'location' => 'Samaná, República Dominicana',
        'camera_settings' => 'Canon EOS R5, 24-70mm f/2.8, ISO 100, f/16, 2s',
        'tags' => 'cascada, naturaleza, samana, agua',
        'category' => 'naturaleza'
    ),
    array(
        'title' => 'Merengue y Bachata',
        'description' => 'Capturando la pasión del baile dominicano en las calles de Santiago.',
        'location' => 'Santiago, República Dominicana',
        'camera_settings' => 'Canon EOS R5, 35mm f/1.4, ISO 1600, f/2.8, 1/250s',
        'tags' => 'baile, merengue, bachata, cultura',
        'category' => 'cultura'
    )
);

// Crear categorías si no existen
$categories = array('paisajes', 'arquitectura', 'retratos', 'naturaleza', 'cultura');
foreach ($categories as $cat_slug) {
    $cat_name = ucfirst($cat_slug);
    if (!term_exists($cat_slug, 'photo_category')) {
        wp_insert_term($cat_name, 'photo_category', array('slug' => $cat_slug));
        echo "✅ Categoría creada: $cat_name\n";
    }
}

// Función para crear imagen placeholder
function create_placeholder_image($width = 800, $height = 600, $text = 'SoloYLibre') {
    $upload_dir = wp_upload_dir();
    $filename = 'soloylibre-placeholder-' . uniqid() . '.jpg';
    $filepath = $upload_dir['path'] . '/' . $filename;
    
    // Crear imagen simple con GD
    if (extension_loaded('gd')) {
        $image = imagecreate($width, $height);
        
        // Colores dominicanos
        $red = imagecolorallocate($image, 206, 17, 38);
        $blue = imagecolorallocate($image, 0, 45, 98);
        $white = imagecolorallocate($image, 255, 255, 255);
        
        // Fondo degradado simulado
        imagefill($image, 0, 0, $blue);
        
        // Texto
        $font_size = 5;
        $text_width = imagefontwidth($font_size) * strlen($text);
        $text_height = imagefontheight($font_size);
        $x = ($width - $text_width) / 2;
        $y = ($height - $text_height) / 2;
        
        imagestring($image, $font_size, $x, $y, $text, $white);
        
        // Guardar imagen
        imagejpeg($image, $filepath, 90);
        imagedestroy($image);
        
        return array(
            'file' => $filepath,
            'url' => $upload_dir['url'] . '/' . $filename,
            'type' => 'image/jpeg'
        );
    }
    
    return false;
}

// Crear fotos de muestra
foreach ($sample_photos as $index => $photo_data) {
    echo "📷 Creando foto: " . $photo_data['title'] . "\n";
    
    // Crear imagen placeholder
    $image_info = create_placeholder_image(800, 600, "SoloYLibre\n" . ($index + 1));
    
    if ($image_info) {
        // Crear attachment
        $attachment = array(
            'guid' => $image_info['url'],
            'post_mime_type' => $image_info['type'],
            'post_title' => $photo_data['title'],
            'post_content' => '',
            'post_status' => 'inherit'
        );
        
        $attach_id = wp_insert_attachment($attachment, $image_info['file']);
        
        if ($attach_id) {
            // Generar metadata
            $attach_data = wp_generate_attachment_metadata($attach_id, $image_info['file']);
            wp_update_attachment_metadata($attach_id, $attach_data);
            
            // Crear post de foto
            $photo_post = array(
                'post_title' => $photo_data['title'],
                'post_content' => $photo_data['description'],
                'post_status' => 'publish',
                'post_type' => 'soloylibre_photo',
                'post_author' => 1
            );
            
            $photo_id = wp_insert_post($photo_post);
            
            if ($photo_id) {
                // Asignar imagen destacada
                set_post_thumbnail($photo_id, $attach_id);
                
                // Agregar metadata personalizada
                update_post_meta($photo_id, '_soloylibre_location', $photo_data['location']);
                update_post_meta($photo_id, '_soloylibre_camera_settings', $photo_data['camera_settings']);
                update_post_meta($photo_id, '_soloylibre_is_publishable', 1);
                update_post_meta($photo_id, '_soloylibre_views_count', rand(50, 500));
                update_post_meta($photo_id, '_soloylibre_likes_count', rand(10, 100));
                
                // Asignar categoría
                wp_set_object_terms($photo_id, $photo_data['category'], 'photo_category');
                
                // Asignar tags
                wp_set_object_terms($photo_id, explode(', ', $photo_data['tags']), 'photo_tag');
                
                echo "   ✅ Foto creada con ID: $photo_id\n";
            } else {
                echo "   ❌ Error creando post de foto\n";
            }
        } else {
            echo "   ❌ Error creando attachment\n";
        }
    } else {
        echo "   ❌ Error creando imagen placeholder\n";
    }
}

// Crear álbum de muestra
global $wpdb;
$albums_table = $wpdb->prefix . 'soloylibre_albums';

// Verificar si la tabla existe
if ($wpdb->get_var("SHOW TABLES LIKE '$albums_table'") == $albums_table) {
    $album_data = array(
        'name' => 'República Dominicana - Colección JoseTusabe',
        'description' => 'Una colección especial de fotografías capturando la belleza y cultura de República Dominicana por Jose L Encarnacion (JoseTusabe).',
        'membership_level' => 'public',
        'is_published' => 1,
        'sort_order' => 1
    );
    
    $wpdb->insert($albums_table, $album_data);
    $album_id = $wpdb->insert_id;
    
    if ($album_id) {
        echo "✅ Álbum creado: República Dominicana - Colección JoseTusabe (ID: $album_id)\n";
    }
}

echo "\n🎉 ¡Fotos de muestra creadas exitosamente!\n";
echo "📊 Estadísticas:\n";
echo "   📸 Fotos creadas: " . count($sample_photos) . "\n";
echo "   📁 Categorías: " . count($categories) . "\n";
echo "   🏷️ Tags: Múltiples por foto\n";

echo "\n🔗 Enlaces útiles:\n";
echo "   🌐 Ver galería: " . home_url('/soloylibre-gallery/') . "\n";
echo "   ⚙️ Admin plugin: " . admin_url('admin.php?page=soloylibre-gallery') . "\n";
echo "   📝 Gestionar fotos: " . admin_url('edit.php?post_type=soloylibre_photo') . "\n";

echo "\n📋 Shortcode para usar en páginas/posts:\n";
echo "   [soloylibre_gallery style=\"grid\"]\n";
echo "   [soloylibre_gallery style=\"dominican\"]\n";
echo "   [soloylibre_gallery style=\"tiktok\"]\n";
echo "   [soloylibre_gallery category=\"paisajes\"]\n";

?>
