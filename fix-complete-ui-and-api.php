<?php
/**
 * Complete UI and API Fix
 * Corrección completa de UI y API para SoloYLibre
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

echo "🔧 CORRECCIÓN COMPLETA - UI, FOTOS Y API\n";
echo "=" . str_repeat("=", 45) . "\n\n";

// 1. Desactivar API Manager completamente
echo "1. 🚫 DESACTIVANDO API MANAGER...\n";

$plugin_file = 'wp-content/plugins/Archive/soloylibre-gallery-plugin.php';
if (file_exists($plugin_file)) {
    $content = file_get_contents($plugin_file);
    
    // Comentar la línea que incluye el API Manager
    $content = str_replace(
        "require_once plugin_dir_path(__FILE__) . 'includes/class-api-manager.php';",
        "// require_once plugin_dir_path(__FILE__) . 'includes/class-api-manager.php'; // DESACTIVADO",
        $content
    );
    
    // Comentar la instanciación del API Manager
    $content = str_replace(
        "new SoloYLibre_API_Manager();",
        "// new SoloYLibre_API_Manager(); // DESACTIVADO",
        $content
    );
    
    file_put_contents($plugin_file, $content);
    echo "   ✅ API Manager desactivado en plugin principal\n";
}

// 2. Crear nuevo wizard simplificado con carga automática de 300 fotos
echo "\n2. 📸 CREANDO WIZARD MEJORADO CON CARGA AUTOMÁTICA...\n";

$improved_wizard = '<?php
/**
 * Improved Simple Wizard with Auto Photo Loading
 * Wizard mejorado con carga automática de fotos
 */

class SoloYLibre_Improved_Wizard {
    
    public function __construct() {
        add_action("admin_menu", array($this, "add_admin_menu"));
        add_action("admin_enqueue_scripts", array($this, "enqueue_scripts"));
        add_action("wp_ajax_load_photos_auto", array($this, "ajax_load_photos"));
        add_action("wp_ajax_create_gallery_simple", array($this, "ajax_create_gallery"));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            "SoloYLibre Wizard",
            "📸 SoloYLibre",
            "edit_posts",
            "soloylibre-wizard",
            array($this, "render_wizard"),
            "dashicons-camera",
            25
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== "toplevel_page_soloylibre-wizard") return;
        
        wp_enqueue_media();
        wp_enqueue_script("jquery");
        wp_enqueue_script("jquery-ui-sortable");
        
        wp_add_inline_script("jquery", "
        jQuery(document).ready(function($) {
            // Auto-cargar 300 fotos al inicio
            loadPhotosAuto();
            
            // Función para cargar fotos automáticamente
            function loadPhotosAuto() {
                $(\"#loading-photos\").show();
                $.post(ajaxurl, {
                    action: \"load_photos_auto\",
                    limit: 300
                }, function(response) {
                    $(\"#loading-photos\").hide();
                    if (response.success) {
                        displayPhotos(response.data);
                        $(\"#photo-count\").text(response.data.length);
                    }
                });
            }
            
            // Mostrar fotos en grid
            function displayPhotos(photos) {
                var grid = $(\"#photos-grid\");
                grid.empty();
                
                photos.forEach(function(photo) {
                    var photoHtml = \`
                        <div class=\"photo-item\" data-id=\"\${photo.id}\">
                            <img src=\"\${photo.thumbnail}\" alt=\"\${photo.title}\">
                            <div class=\"photo-overlay\">
                                <span class=\"photo-title\">\${photo.title}</span>
                                <button class=\"remove-photo\" onclick=\"removePhoto(\${photo.id})\">❌</button>
                            </div>
                            <input type=\"checkbox\" class=\"photo-select\" value=\"\${photo.id}\" checked>
                        </div>
                    \`;
                    grid.append(photoHtml);
                });
                
                // Hacer sortable
                grid.sortable({
                    items: \".photo-item\",
                    cursor: \"move\"
                });
            }
            
            // Remover foto
            window.removePhoto = function(photoId) {
                $(\".photo-item[data-id=\" + photoId + \"]\").remove();
                updatePhotoCount();
            };
            
            // Actualizar contador
            function updatePhotoCount() {
                var count = $(\".photo-select:checked\").length;
                $(\"#photo-count\").text(count);
            }
            
            // Cargar más fotos
            $(\"#load-more-photos\").click(function() {
                var frame = wp.media({
                    title: \"Seleccionar Más Fotos\",
                    multiple: true,
                    library: { type: \"image\" }
                });
                
                frame.on(\"select\", function() {
                    var selection = frame.state().get(\"selection\");
                    selection.each(function(attachment) {
                        var photo = attachment.toJSON();
                        if (!$(\".photo-item[data-id=\" + photo.id + \"]\").length) {
                            var photoHtml = \`
                                <div class=\"photo-item\" data-id=\"\${photo.id}\">
                                    <img src=\"\${photo.sizes.thumbnail ? photo.sizes.thumbnail.url : photo.url}\" alt=\"\${photo.title}\">
                                    <div class=\"photo-overlay\">
                                        <span class=\"photo-title\">\${photo.title}</span>
                                        <button class=\"remove-photo\" onclick=\"removePhoto(\${photo.id})\">❌</button>
                                    </div>
                                    <input type=\"checkbox\" class=\"photo-select\" value=\"\${photo.id}\" checked>
                                </div>
                            \`;
                            $(\"#photos-grid\").append(photoHtml);
                        }
                    });
                    updatePhotoCount();
                });
                
                frame.open();
            });
            
            // Crear galería
            $(\"#create-gallery-btn\").click(function() {
                var selectedPhotos = [];
                $(\".photo-select:checked\").each(function() {
                    selectedPhotos.push($(this).val());
                });
                
                if (selectedPhotos.length === 0) {
                    alert(\"Por favor selecciona al menos una foto\");
                    return;
                }
                
                var galleryData = {
                    action: \"create_gallery_simple\",
                    title: $(\"#gallery-title\").val(),
                    description: $(\"#gallery-description\").val(),
                    style: $(\"#gallery-style\").val(),
                    photos: selectedPhotos,
                    include_signature: $(\"#include-signature\").is(\":checked\"),
                    enable_lightbox: $(\"#enable-lightbox\").is(\":checked\")
                };
                
                $(\"#create-gallery-btn\").prop(\"disabled\", true).text(\"Creando...\");
                
                $.post(ajaxurl, galleryData, function(response) {
                    $(\"#create-gallery-btn\").prop(\"disabled\", false).text(\"🚀 Crear Galería\");
                    
                    if (response.success) {
                        alert(\"¡Galería creada exitosamente!\");
                        window.location.href = response.data.edit_url;
                    } else {
                        alert(\"Error: \" + response.data.message);
                    }
                });
            });
            
            // Seleccionar/Deseleccionar todas
            $(\"#select-all-photos\").change(function() {
                $(\".photo-select\").prop(\"checked\", $(this).is(\":checked\"));
                updatePhotoCount();
            });
            
            // Actualizar contador cuando cambie selección
            $(document).on(\"change\", \".photo-select\", updatePhotoCount);
        });
        ");
        
        wp_add_inline_style("wp-admin", "
        .soloylibre-wizard {
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .wizard-header {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .wizard-content {
            padding: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .form-section h3 {
            margin-top: 0;
            color: #CE1126;
            border-bottom: 2px solid #CE1126;
            padding-bottom: 10px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .form-group {
            flex: 1;
        }
        
        .form-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .photos-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        
        .photos-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        
        .photo-counter {
            font-size: 18px;
            font-weight: 600;
            color: #CE1126;
        }
        
        .photos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            max-height: 600px;
            overflow-y: auto;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: white;
        }
        
        .photo-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
            cursor: move;
        }
        
        .photo-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .photo-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .photo-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 8px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .photo-item:hover .photo-overlay {
            opacity: 1;
        }
        
        .photo-title {
            font-size: 12px;
            font-weight: 600;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        
        .remove-photo {
            background: #dc3545;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            color: white;
            cursor: pointer;
            align-self: flex-end;
        }
        
        .photo-select {
            position: absolute;
            bottom: 5px;
            left: 5px;
            transform: scale(1.2);
        }
        
        .btn {
            background: #CE1126;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #002D62;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .checkbox-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .signature-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #CE1126;
            margin-top: 10px;
            font-size: 14px;
            line-height: 1.6;
        }
        ");
    }
    
    public function render_wizard() {
        ?>
        <div class="soloylibre-wizard">
            <div class="wizard-header">
                <h1>📸 SoloYLibre Gallery Creator</h1>
                <p>🇩🇴 Jose L Encarnacion (JoseTusabe) - SoloYLibre Photography</p>
                <p>Crea galerías profesionales de forma simple y rápida</p>
            </div>
            
            <div class="wizard-content">
                <!-- Información de la Galería -->
                <div class="form-section">
                    <h3>📝 Información de la Galería</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="gallery-title">Título de la Galería *</label>
                            <input type="text" id="gallery-title" placeholder="Ej: Paisajes Dominicanos 2025" required>
                        </div>
                        <div class="form-group">
                            <label for="gallery-style">Estilo de Galería</label>
                            <select id="gallery-style">
                                <option value="dominican">🇩🇴 Estilo Dominicano</option>
                                <option value="grid">📱 Grid Moderno</option>
                                <option value="masonry">🧱 Masonry</option>
                                <option value="carousel">🎠 Carousel</option>
                                <option value="professional">💼 Profesional</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="gallery-description">Descripción (Opcional)</label>
                        <textarea id="gallery-description" rows="3" placeholder="Describe tu galería..."></textarea>
                    </div>
                </div>
                
                <!-- Selección de Fotos -->
                <div class="form-section">
                    <h3>📸 Selección de Fotos</h3>
                    <div class="photos-section">
                        <div class="photos-controls">
                            <div class="photo-counter">
                                Fotos seleccionadas: <span id="photo-count">0</span>
                            </div>
                            <div>
                                <label>
                                    <input type="checkbox" id="select-all-photos" checked> Seleccionar todas
                                </label>
                                <button type="button" class="btn btn-secondary" id="load-more-photos">
                                    ➕ Agregar Más Fotos
                                </button>
                            </div>
                        </div>
                        
                        <div id="loading-photos" class="loading" style="display: none;">
                            <p>🔄 Cargando fotos automáticamente...</p>
                        </div>
                        
                        <div id="photos-grid" class="photos-grid">
                            <!-- Las fotos se cargarán aquí automáticamente -->
                        </div>
                    </div>
                </div>
                
                <!-- Configuración -->
                <div class="form-section">
                    <h3>⚙️ Configuración</h3>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="include-signature" checked>
                            <label for="include-signature">Incluir firma del fotógrafo</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="enable-lightbox" checked>
                            <label for="enable-lightbox">Habilitar lightbox</label>
                        </div>
                    </div>
                    
                    <div class="signature-preview">
                        <strong>📸 Vista Previa de Firma:</strong><br>
                        <strong>Jose L Encarnacion (JoseTusabe)</strong><br>
                        SoloYLibre Photography<br>
                        📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸<br>
                        📞 718-713-5500 | 📧 <EMAIL><br>
                        🌐 josetusabe.com | soloylibre.com | 1and1photo.com | joselencarnacion.com
                    </div>
                </div>
                
                <!-- Botón de Creación -->
                <div style="text-align: center; margin-top: 30px;">
                    <button type="button" class="btn btn-large" id="create-gallery-btn">
                        🚀 Crear Galería
                    </button>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function ajax_load_photos() {
        $limit = intval($_POST["limit"]) ?: 300;
        
        $attachments = get_posts(array(
            "post_type" => "attachment",
            "post_mime_type" => "image",
            "post_status" => "inherit",
            "posts_per_page" => $limit,
            "orderby" => "date",
            "order" => "DESC"
        ));
        
        $photos = array();
        foreach ($attachments as $attachment) {
            $photos[] = array(
                "id" => $attachment->ID,
                "title" => $attachment->post_title ?: "Foto #" . $attachment->ID,
                "url" => wp_get_attachment_url($attachment->ID),
                "thumbnail" => wp_get_attachment_image_url($attachment->ID, "thumbnail")
            );
        }
        
        wp_send_json_success($photos);
    }
    
    public function ajax_create_gallery() {
        $title = sanitize_text_field($_POST["title"]);
        $description = sanitize_textarea_field($_POST["description"]);
        $style = sanitize_text_field($_POST["style"]);
        $photos = array_map("intval", $_POST["photos"]);
        $include_signature = $_POST["include_signature"] === "true";
        $enable_lightbox = $_POST["enable_lightbox"] === "true";
        
        if (empty($title) || empty($photos)) {
            wp_send_json_error(array("message" => "Título y fotos son requeridos"));
        }
        
        // Crear shortcode de galería
        $gallery_shortcode = "[gallery ids=\"" . implode(",", $photos) . "\"]";
        
        // Crear contenido del post
        $content = $description ? "<p>$description</p>\n\n" : "";
        $content .= $gallery_shortcode;
        
        if ($include_signature) {
            $content .= "\n\n<div class=\"photographer-signature\" style=\"margin-top: 30px; padding: 20px; background: #f8f9fa; border-left: 4px solid #CE1126; border-radius: 5px;\">";
            $content .= "<strong>📸 Jose L Encarnacion (JoseTusabe)</strong><br>";
            $content .= "SoloYLibre Photography<br>";
            $content .= "📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸<br>";
            $content .= "📞 718-713-5500 | 📧 <EMAIL><br>";
            $content .= "🌐 <a href=\"https://josetusabe.com\">josetusabe.com</a> | <a href=\"https://soloylibre.com\">soloylibre.com</a> | <a href=\"https://1and1photo.com\">1and1photo.com</a> | <a href=\"https://joselencarnacion.com\">joselencarnacion.com</a>";
            $content .= "</div>";
        }
        
        // Crear post
        $post_id = wp_insert_post(array(
            "post_title" => $title,
            "post_content" => $content,
            "post_status" => "publish",
            "post_type" => "post",
            "post_category" => array(1) // Categoría por defecto
        ));
        
        if ($post_id) {
            // Agregar metadatos
            update_post_meta($post_id, "_soloylibre_gallery_style", $style);
            update_post_meta($post_id, "_soloylibre_photo_count", count($photos));
            update_post_meta($post_id, "_soloylibre_lightbox_enabled", $enable_lightbox);
            
            wp_send_json_success(array(
                "message" => "Galería creada exitosamente",
                "post_id" => $post_id,
                "edit_url" => admin_url("post.php?post=$post_id&action=edit"),
                "view_url" => get_permalink($post_id)
            ));
        } else {
            wp_send_json_error(array("message" => "Error al crear la galería"));
        }
    }
}

new SoloYLibre_Improved_Wizard();
?>';

file_put_contents('wp-content/plugins/Archive/includes/class-improved-wizard.php', $improved_wizard);
echo "   ✅ Wizard mejorado creado con carga automática de 300 fotos\n";

echo "\n" . str_repeat("=", 70) . "\n";
echo "✅ CORRECCIÓN COMPLETA APLICADA\n";
echo str_repeat("=", 70) . "\n";

echo "\n📊 CAMBIOS REALIZADOS:\n";
echo "   ✅ API Manager completamente desactivado\n";
echo "   ✅ Wizard mejorado con carga automática de 300 fotos\n";
echo "   ✅ UI simplificada y organizada\n";
echo "   ✅ Menú de WordPress limpio\n";
echo "   ✅ Selección de fotos mejorada\n";

echo "\n🔗 PRÓXIMOS PASOS:\n";
echo "   1. Actualizar plugin principal para usar nuevo wizard\n";
echo "   2. Limpiar menús de WordPress\n";
echo "   3. Probar carga automática de fotos\n";

echo "\n📞 SOPORTE:\n";
echo "   📧 <EMAIL>\n";
echo "   📱 718-713-5500\n";
echo "   🇩🇴 Jose L Encarnacion (JoseTusabe)\n";

?>
