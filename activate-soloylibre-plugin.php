<?php
/**
 * <PERSON>ript para activar SoloYLibre Gallery Pro
 * Desarrollado por JEYKO AI para <PERSON> (JoseTusabe)
 */

// Cargar WordPress
require_once('wp-config.php');
require_once('wp-load.php');
require_once('wp-admin/includes/plugin.php');
require_once('wp-admin/includes/user.php');

echo "🚀 Activando SoloYLibre Gallery Pro...\n\n";

// Verificar si el plugin existe
$plugin_file = 'Archive/soloylibre-gallery-plugin.php';
$plugin_path = WP_PLUGIN_DIR . '/' . $plugin_file;

if (!file_exists($plugin_path)) {
    echo "❌ Error: Plugin no encontrado en $plugin_path\n";
    exit;
}

echo "✅ Plugin encontrado: $plugin_path\n";

// Activar el plugin
if (!is_plugin_active($plugin_file)) {
    $result = activate_plugin($plugin_file);
    
    if (is_wp_error($result)) {
        echo "❌ Error activando plugin: " . $result->get_error_message() . "\n";
    } else {
        echo "✅ Plugin SoloYLibre Gallery Pro activado exitosamente!\n\n";
    }
} else {
    echo "✅ Plugin ya está activo!\n\n";
}

// Verificar usuario administrador
$username = 'admin_soloylibre';
$password = 'JoseTusabe2025!';
$email = '<EMAIL>';

$user_id = username_exists($username);

if (!$user_id) {
    $user_id = wp_create_user($username, $password, $email);
    
    if (is_wp_error($user_id)) {
        echo "❌ Error creando usuario: " . $user_id->get_error_message() . "\n";
    } else {
        // Hacer al usuario administrador
        $user = new WP_User($user_id);
        $user->set_role('administrator');
        
        echo "✅ Usuario administrador creado:\n";
        echo "   👤 Usuario: $username\n";
        echo "   🔑 Contraseña: $password\n";
        echo "   📧 Email: $email\n\n";
    }
} else {
    echo "✅ Usuario administrador ya existe:\n";
    echo "   👤 Usuario: $username\n";
    echo "   🔑 Contraseña: $password\n\n";
}

// Verificar estado del plugin
$active_plugins = get_option('active_plugins');
echo "📋 Plugins activos:\n";
foreach ($active_plugins as $plugin) {
    echo "   - $plugin\n";
}

echo "\n🎯 Estado del Sistema:\n";
echo "   📁 WordPress: " . get_bloginfo('version') . "\n";
echo "   🔌 Plugin SoloYLibre: " . (is_plugin_active($plugin_file) ? 'Activo' : 'Inactivo') . "\n";
echo "   🌐 URL Admin: " . admin_url() . "\n";
echo "   🌐 URL Sitio: " . home_url() . "\n";

// Crear algunas opciones por defecto
$default_options = array(
    'soloylibre_gallery_gallery_style' => 'grid',
    'soloylibre_gallery_photos_per_page' => 12,
    'soloylibre_gallery_enable_infinite_scroll' => true,
    'soloylibre_gallery_photographer_name' => 'Jose L Encarnacion',
    'soloylibre_gallery_photographer_brand' => 'SoloYLibre',
    'soloylibre_gallery_photographer_email' => '<EMAIL>',
    'soloylibre_gallery_photographer_phone' => '************',
    'soloylibre_gallery_photographer_location' => 'San José de Ocoa, Dom. Rep. / USA'
);

foreach ($default_options as $key => $value) {
    if (!get_option($key)) {
        add_option($key, $value);
        echo "   ✅ Opción creada: $key\n";
    }
}

echo "\n🎉 ¡Instalación completada!\n";
echo "🔗 Accede al admin: " . admin_url('admin.php?page=soloylibre-gallery') . "\n";
echo "👤 Usuario: $username\n";
echo "🔑 Contraseña: $password\n";
?>
