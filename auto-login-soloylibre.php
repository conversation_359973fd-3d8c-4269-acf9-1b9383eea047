<?php
// Login automático para SoloYLibre
require_once("wp-config.php");
require_once("wp-load.php");

// Buscar usuario admin_soloylibre
$user = get_user_by("login", "admin_soloylibre");
if ($user) {
    wp_set_current_user($user->ID);
    wp_set_auth_cookie($user->ID, true);
    
    // Redirigir al wizard
    $redirect_url = admin_url("admin.php?page=soloylibre-simple-wizard");
    header("Location: " . $redirect_url);
    exit;
} else {
    echo "Error: Usuario no encontrado";
}
?>