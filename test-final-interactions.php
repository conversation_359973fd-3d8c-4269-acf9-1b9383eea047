<?php
/**
 * Test Final Interactions - Complete System Verification
 * Prueba final de interacciones - Verificación completa del sistema
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

require_once('wp-load.php');

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🎯 Prueba Final - Sistema de Interacciones SoloYLibre</title>";
echo "<meta charset=\"UTF-8\">";
echo "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">";

wp_head();

echo "<style>";
echo "body { margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-family: Arial, sans-serif; }";
echo ".container { max-width: 1200px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }";
echo ".test-section { background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 15px; padding: 25px; margin: 20px 0; border: 1px solid rgba(255,255,255,0.2); }";
echo ".test-title { color: #FFD700; font-size: 1.5rem; margin-bottom: 15px; }";
echo ".btn { background: #CE1126; color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: pointer; margin: 5px; font-weight: 600; transition: all 0.3s ease; }";
echo ".btn:hover { background: #002D62; transform: translateY(-2px); }";
echo ".success { color: #00ff88; }";
echo ".error { color: #ff6b6b; }";
echo ".info { color: #74b9ff; }";
echo "</style>";

echo "<script src=\"https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js\"></script>";

echo "</head>";
echo "<body>";

echo "<div class=\"container\">";

echo "<div class=\"header\">";
echo "<h1>🎯 Prueba Final del Sistema de Interacciones</h1>";
echo "<h2>SoloYLibre Gallery Pro v5.0.3</h2>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe) - Sistema Completamente Corregido</p>";
echo "</div>";

// 1. Verificar configuración del sistema
echo "<div class=\"test-section\">";
echo "<h2 class=\"test-title\">1. 🔧 Verificación del Sistema</h2>";

global $wpdb;
$table_name = $wpdb->prefix . 'soloylibre_interactions';

// Verificar tabla
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
echo "<p><strong>Tabla de interacciones:</strong> " . ($table_exists ? '<span class="success">✅ Existe</span>' : '<span class="error">❌ No existe</span>') . "</p>";

if ($table_exists) {
    // Verificar columnas
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    $required_columns = array('id', 'photo_id', 'interaction_type', 'interaction_count', 'user_ip', 'user_agent', 'created_at', 'updated_at');
    $existing_columns = wp_list_pluck($columns, 'Field');
    
    echo "<p><strong>Columnas requeridas:</strong></p>";
    echo "<ul>";
    foreach ($required_columns as $col) {
        $exists = in_array($col, $existing_columns);
        echo "<li>$col: " . ($exists ? '<span class="success">✅</span>' : '<span class="error">❌</span>') . "</li>";
    }
    echo "</ul>";
    
    // Verificar datos
    $total_interactions = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    echo "<p><strong>Total interacciones:</strong> <span class=\"info\">$total_interactions</span></p>";
}

// Verificar AJAX
$ajax_registered = has_action('wp_ajax_gallery_interaction') && has_action('wp_ajax_nopriv_gallery_interaction');
echo "<p><strong>Acciones AJAX:</strong> " . ($ajax_registered ? '<span class="success">✅ Registradas</span>' : '<span class="error">❌ No registradas</span>') . "</p>";

// Verificar nonce
$nonce = wp_create_nonce('gallery_interaction');
echo "<p><strong>Nonce generado:</strong> <span class=\"info\">" . substr($nonce, 0, 10) . "...</span></p>";

echo "</div>";

// 2. Galería de prueba con interacciones
echo "<div class=\"test-section\">";
echo "<h2 class=\"test-title\">2. 🎨 Galería de Prueba con Interacciones</h2>";

$test_photos = get_posts(array(
    'post_type' => 'attachment',
    'post_mime_type' => 'image',
    'posts_per_page' => 6,
    'post_status' => 'inherit'
));

if (!empty($test_photos)) {
    $photo_ids = implode(',', wp_list_pluck($test_photos, 'ID'));
    
    echo "<p><strong>Fotos de prueba:</strong> " . count($test_photos) . " imágenes</p>";
    echo "<p class=\"info\">Haz clic en los botones de interacción (❤️, 👁️, 📤) para probar el sistema</p>";
    
    echo do_shortcode("[soloylibre_gallery style=\"dominican\" ids=\"$photo_ids\" columns=\"3\" enable_interactions=\"true\" auto_confetti=\"true\"]");
    
} else {
    echo "<p class=\"error\">❌ No se encontraron fotos para la prueba</p>";
    echo "<p>Sube algunas imágenes a la biblioteca de medios de WordPress para probar el sistema.</p>";
}

echo "</div>";

// 3. Prueba manual de AJAX
echo "<div class=\"test-section\">";
echo "<h2 class=\"test-title\">3. 🧪 Prueba Manual de AJAX</h2>";

if (!empty($test_photos)) {
    $test_photo_id = $test_photos[0]->ID;
    
    echo "<p><strong>Foto de prueba:</strong> ID $test_photo_id</p>";
    echo "<div style=\"margin: 15px 0;\">";
    echo "<button class=\"btn\" onclick=\"testDirectAjax($test_photo_id, 'like')\">❤️ Probar Like Directo</button>";
    echo "<button class=\"btn\" onclick=\"testDirectAjax($test_photo_id, 'view')\">👁️ Probar Vista Directa</button>";
    echo "<button class=\"btn\" onclick=\"testDirectAjax($test_photo_id, 'share')\">📤 Probar Compartir Directo</button>";
    echo "</div>";
    
    echo "<div id=\"ajax-test-results\" style=\"margin-top: 15px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 10px; min-height: 50px;\"></div>";
}

echo "</div>";

// 4. Estadísticas en tiempo real
echo "<div class=\"test-section\">";
echo "<h2 class=\"test-title\">4. 📊 Estadísticas en Tiempo Real</h2>";

if ($table_exists) {
    $stats = $wpdb->get_results("SELECT interaction_type, SUM(interaction_count) as total FROM $table_name GROUP BY interaction_type");
    
    if (!empty($stats)) {
        echo "<div style=\"display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;\">";
        foreach ($stats as $stat) {
            $icon = $stat->interaction_type == 'like' ? '❤️' : ($stat->interaction_type == 'view' ? '👁️' : '📤');
            echo "<div style=\"background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; text-align: center;\">";
            echo "<div style=\"font-size: 2rem;\">$icon</div>";
            echo "<div style=\"font-size: 1.5rem; font-weight: bold;\">{$stat->total}</div>";
            echo "<div>" . ucfirst($stat->interaction_type) . "s</div>";
            echo "</div>";
        }
        echo "</div>";
    } else {
        echo "<p class=\"info\">No hay estadísticas disponibles aún. Prueba las interacciones arriba.</p>";
    }
}

echo "</div>";

echo "</div>"; // container

// JavaScript mejorado
echo "<script>";
echo "console.log('🎯 SoloYLibre: Página de prueba final cargada');";
echo "console.log('AJAX Config:', typeof soloylibre_ajax !== 'undefined' ? soloylibre_ajax : 'No definido');";

echo "function testDirectAjax(photoId, action) {";
echo "    const resultsDiv = document.getElementById('ajax-test-results');";
echo "    resultsDiv.innerHTML = '⏳ Probando ' + action + ' para foto ' + photoId + '...';";
echo "    ";
echo "    if (typeof soloylibre_ajax === 'undefined') {";
echo "        resultsDiv.innerHTML = '<span class=\"error\">❌ Error: soloylibre_ajax no está definido</span>';";
echo "        return;";
echo "    }";
echo "    ";
echo "    console.log('🧪 Prueba directa:', {photoId: photoId, action: action, nonce: soloylibre_ajax.nonce});";
echo "    ";
echo "    jQuery.post(soloylibre_ajax.ajax_url, {";
echo "        action: 'gallery_interaction',";
echo "        photo_id: photoId,";
echo "        action_type: action,";
echo "        nonce: soloylibre_ajax.nonce";
echo "    }, function(response) {";
echo "        console.log('🧪 Respuesta recibida:', response);";
echo "        ";
echo "        if (response.success) {";
echo "            resultsDiv.innerHTML = '<span class=\"success\">✅ Éxito: ' + response.data.message + '<br>Nuevo contador: ' + response.data.new_count + '</span>';";
echo "            ";
echo "            // Confetti de celebración";
echo "            if (typeof confetti !== 'undefined') {";
echo "                confetti({";
echo "                    particleCount: 100,";
echo "                    spread: 70,";
echo "                    origin: { y: 0.6 },";
echo "                    colors: ['#CE1126', '#002D62', '#FFD700']";
echo "                });";
echo "            }";
echo "            ";
echo "            // Recargar estadísticas";
echo "            setTimeout(function() {";
echo "                location.reload();";
echo "            }, 2000);";
echo "        } else {";
echo "            resultsDiv.innerHTML = '<span class=\"error\">❌ Error: ' + (response.data || 'Error desconocido') + '</span>';";
echo "        }";
echo "    }).fail(function(xhr, status, error) {";
echo "        console.error('🧪 Error AJAX:', {xhr: xhr, status: status, error: error});";
echo "        resultsDiv.innerHTML = '<span class=\"error\">❌ Error de conexión: ' + error + '</span>';";
echo "    });";
echo "}";

echo "// Confetti de bienvenida";
echo "setTimeout(function() {";
echo "    if (typeof confetti !== 'undefined') {";
echo "        confetti({";
echo "            particleCount: 150,";
echo "            spread: 70,";
echo "            origin: { y: 0.6 },";
echo "            colors: ['#CE1126', '#002D62', '#FFFFFF', '#FFD700']";
echo "        });";
echo "    }";
echo "}, 1000);";

echo "</script>";

wp_footer();

echo "</body>";
echo "</html>";
?>
