<?php
/**
 * Phase 2: Confetti Effects and Visual Enhancements
 * Fase 2: Efectos de confetti y mejoras visuales
 * Developed by JEYKO AI for Jose L <PERSON>carnac<PERSON> (JoseTusabe)
 */

echo "🎉 FASE 2: EFECTOS DE CONFETTI Y MEJORAS VISUALES\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// 1. Crear sistema de efectos de confetti
echo "1. 🎊 CREANDO SISTEMA DE EFECTOS DE CONFETTI...\n";

$confetti_system = '<?php
/**
 * SoloYLibre Confetti Effects System
 * Sistema de efectos de confetti
 */

class SoloYLibre_Confetti_System {

    public function __construct() {
        add_action("admin_enqueue_scripts", array($this, "enqueue_confetti_assets"));
        add_action("wp_enqueue_scripts", array($this, "enqueue_frontend_confetti"));
        add_action("admin_footer", array($this, "add_confetti_welcome"));
    }

    public function enqueue_confetti_assets($hook) {
        // Solo cargar en páginas del plugin
        if (strpos($hook, "soloylibre") === false) return;

        // Cargar librería de confetti desde CDN
        wp_enqueue_script(
            "confetti-js",
            "https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js",
            array(),
            "1.6.0",
            true
        );

        // CSS personalizado para efectos
        wp_add_inline_style("wp-admin", $this->get_confetti_css());

        // JavaScript personalizado
        wp_add_inline_script("confetti-js", $this->get_confetti_js());
    }

    public function enqueue_frontend_confetti() {
        // Cargar confetti en frontend solo si está habilitado
        $settings = new SoloYLibre_Settings_Manager();
        if ($settings->get_option("enable_confetti", true)) {
            wp_enqueue_script(
                "confetti-js",
                "https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js",
                array("jquery"),
                "1.6.0",
                true
            );

            wp_add_inline_script("confetti-js", $this->get_frontend_confetti_js());
        }
    }

    public function add_confetti_welcome() {
        global $hook_suffix;

        // Solo mostrar en páginas del plugin
        if (strpos($hook_suffix, "soloylibre") === false) return;

        $settings = new SoloYLibre_Settings_Manager();
        if (!$settings->get_option("enable_confetti", true)) return;

        ?>
        <script>
        jQuery(document).ready(function($) {
            // Confetti de bienvenida
            setTimeout(function() {
                if (typeof confetti !== "undefined") {
                    // Efecto de bienvenida dominicano
                    confetti({
                        particleCount: 100,
                        spread: 70,
                        origin: { y: 0.6 },
                        colors: ["#CE1126", "#002D62", "#FFFFFF"]
                    });

                    // Segundo efecto después de 500ms
                    setTimeout(function() {
                        confetti({
                            particleCount: 50,
                            angle: 60,
                            spread: 55,
                            origin: { x: 0 },
                            colors: ["#CE1126", "#002D62"]
                        });
                        confetti({
                            particleCount: 50,
                            angle: 120,
                            spread: 55,
                            origin: { x: 1 },
                            colors: ["#CE1126", "#002D62"]
                        });
                    }, 500);
                }
            }, 1000);
        });
        </script>
        <?php
    }

    private function get_confetti_css() {
        return "
        .soloylibre-confetti-trigger {
            position: relative;
            overflow: hidden;
        }

        .soloylibre-confetti-trigger::before {
            content: \"🎉\";
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 20px;
            animation: bounce 2s infinite;
            z-index: 1000;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .soloylibre-welcome-banner {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .soloylibre-welcome-banner::before {
            content: \"\";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% {
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
            100% {
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
        }

        .soloylibre-success-animation {
            animation: successPulse 0.6s ease-in-out;
        }

        @keyframes successPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }
        ";
    }

    private function get_confetti_js() {
        return "
        // Funciones de confetti personalizadas
        window.SoloYLibreConfetti = {
            welcome: function() {
                if (typeof confetti === \"undefined\") return;

                confetti({
                    particleCount: 100,
                    spread: 70,
                    origin: { y: 0.6 },
                    colors: [\"#CE1126\", \"#002D62\", \"#FFFFFF\"]
                });
            },

            success: function() {
                if (typeof confetti === \"undefined\") return;

                confetti({
                    particleCount: 50,
                    spread: 45,
                    origin: { y: 0.7 },
                    colors: [\"#28a745\", \"#20c997\", \"#17a2b8\"]
                });
            },

            dominican: function() {
                if (typeof confetti === \"undefined\") return;

                // Colores de la bandera dominicana
                const colors = [\"#CE1126\", \"#002D62\", \"#FFFFFF\"];

                confetti({
                    particleCount: 150,
                    spread: 60,
                    origin: { y: 0.6 },
                    colors: colors
                });

                // Efecto adicional en forma de cruz
                setTimeout(function() {
                    confetti({
                        particleCount: 30,
                        angle: 90,
                        spread: 45,
                        origin: { x: 0.5, y: 0.3 },
                        colors: [\"#FFFFFF\"]
                    });
                }, 300);
            },

            celebration: function() {
                if (typeof confetti === \"undefined\") return;

                // Múltiples explosiones
                const count = 200;
                const defaults = {
                    origin: { y: 0.7 },
                    colors: [\"#CE1126\", \"#002D62\", \"#FFD700\", \"#FF6B6B\", \"#4ECDC4\"]
                };

                function fire(particleRatio, opts) {
                    confetti(Object.assign({}, defaults, opts, {
                        particleCount: Math.floor(count * particleRatio)
                    }));
                }

                fire(0.25, {
                    spread: 26,
                    startVelocity: 55,
                });
                fire(0.2, {
                    spread: 60,
                });
                fire(0.35, {
                    spread: 100,
                    decay: 0.91,
                    scalar: 0.8
                });
                fire(0.1, {
                    spread: 120,
                    startVelocity: 25,
                    decay: 0.92,
                    scalar: 1.2
                });
                fire(0.1, {
                    spread: 120,
                    startVelocity: 45,
                });
            }
        };

        // Auto-ejecutar confetti de bienvenida
        jQuery(document).ready(function($) {
            // Agregar efectos a botones importantes
            $(\".button-primary\").addClass(\"soloylibre-confetti-trigger\");

            // Confetti al hacer clic en botones de éxito
            $(document).on(\"click\", \".button-primary\", function() {
                setTimeout(function() {
                    SoloYLibreConfetti.success();
                }, 200);
            });

            // Confetti especial para el wizard
            if (window.location.href.indexOf(\"soloylibre-wizard\") > -1) {
                setTimeout(function() {
                    SoloYLibreConfetti.dominican();
                }, 1500);
            }
        });
        ";
    }

    private function get_frontend_confetti_js() {
        return "
        jQuery(document).ready(function($) {
            // Confetti en galerías del frontend
            $(\".soloylibre-gallery\").each(function() {
                const gallery = $(this);

                // Confetti al cargar galería
                if (typeof confetti !== \"undefined\") {
                    const observer = new IntersectionObserver(function(entries) {
                        entries.forEach(function(entry) {
                            if (entry.isIntersecting) {
                                setTimeout(function() {
                                    confetti({
                                        particleCount: 30,
                                        spread: 50,
                                        origin: {
                                            x: Math.random(),
                                            y: Math.random() * 0.5 + 0.5
                                        },
                                        colors: [\"#CE1126\", \"#002D62\"]
                                    });
                                }, Math.random() * 1000);
                                observer.unobserve(entry.target);
                            }
                        });
                    });

                    observer.observe(this);
                }
            });

            // Confetti al hacer like
            $(document).on(\"click\", \".gallery-like-btn\", function() {
                if (typeof confetti !== \"undefined\") {
                    confetti({
                        particleCount: 20,
                        spread: 30,
                        origin: {
                            x: Math.random(),
                            y: Math.random() * 0.3 + 0.7
                        },
                        colors: [\"#ff6b6b\", \"#ff8e8e\", \"#ffb3b3\"]
                    });
                }
            });
        });
        ";
    }
}

new SoloYLibre_Confetti_System();
?>';

file_put_contents('wp-content/plugins/Archive/includes/class-confetti-system.php', $confetti_system);
echo "   ✅ Sistema de confetti creado\n";

// 2. Actualizar versión del plugin
echo "\n2. 📦 ACTUALIZANDO VERSIÓN DEL PLUGIN (4.0.1 → 4.0.2)...\n";

$plugin_main = 'wp-content/plugins/Archive/soloylibre-gallery-plugin.php';
if (file_exists($plugin_main)) {
    $content = file_get_contents($plugin_main);
    $content = preg_replace('/Version:\s*4\.0\.1/', 'Version: 4.0.2', $content);
    file_put_contents($plugin_main, $content);
    echo "   ✅ Versión actualizada a 4.0.2\n";
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "✅ FASE 2 COMPLETADA - EFECTOS DE CONFETTI\n";
echo str_repeat("=", 70) . "\n";

echo "\n📊 RESUMEN FASE 2:\n";
echo "   🎊 Sistema de confetti: ✅ Creado\n";
echo "   📦 Versión actualizada: 4.0.2\n";
echo "   🎨 Efectos visuales: ✅ Implementados\n";

echo "\n🔗 PRÓXIMA FASE:\n";
echo "   3. Mejoras en shortcodes y frontend\n";

?>