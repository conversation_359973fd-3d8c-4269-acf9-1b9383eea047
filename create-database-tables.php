<?php
/**
 * <PERSON><PERSON><PERSON> para crear tablas de base de datos faltantes
 * Desarrollado por JEYKO AI para <PERSON> (JoseTusabe)
 */

// Cargar WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "🗄️ Creando tablas de base de datos para SoloYLibre Gallery...\n\n";

global $wpdb;

$charset_collate = $wpdb->get_charset_collate();

// Tabla de álbumes
$albums_table = $wpdb->prefix . 'soloylibre_albums';
$albums_sql = "CREATE TABLE $albums_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    description text,
    cover_image_id bigint(20),
    membership_level varchar(50) DEFAULT 'public',
    is_published tinyint(1) DEFAULT 1,
    sort_order int(11) DEFAULT 0,
    photographer_id bigint(20) DEFAULT 1,
    project_type varchar(100),
    client_name varchar(255),
    event_date date,
    location varchar(255),
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY photographer_id (photographer_id),
    KEY is_published (is_published),
    KEY sort_order (sort_order)
) $charset_collate;";

// Tabla de metadata de fotos
$photos_table = $wpdb->prefix . 'soloylibre_photo_meta';
$photos_sql = "CREATE TABLE $photos_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    post_id bigint(20) NOT NULL,
    album_id mediumint(9),
    photo_state varchar(50) DEFAULT 'selection',
    is_publishable tinyint(1) DEFAULT 0,
    is_reviewed tinyint(1) DEFAULT 0,
    is_featured tinyint(1) DEFAULT 0,
    membership_level varchar(50) DEFAULT 'public',
    location varchar(255),
    gps_latitude decimal(10, 8),
    gps_longitude decimal(11, 8),
    camera_make varchar(100),
    camera_model varchar(100),
    camera_settings text,
    lens_info varchar(255),
    focal_length varchar(50),
    aperture varchar(50),
    shutter_speed varchar(50),
    iso varchar(50),
    flash_used tinyint(1) DEFAULT 0,
    color_profile varchar(100),
    keywords text,
    tags text,
    people_tagged text,
    client_rating int(1) DEFAULT 0,
    photographer_rating int(1) DEFAULT 0,
    sort_order int(11) DEFAULT 0,
    views_count int(11) DEFAULT 0,
    likes_count int(11) DEFAULT 0,
    downloads_count int(11) DEFAULT 0,
    shares_count int(11) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY post_id (post_id),
    KEY album_id (album_id),
    KEY photo_state (photo_state),
    KEY is_publishable (is_publishable),
    KEY is_featured (is_featured),
    KEY photographer_rating (photographer_rating),
    KEY client_rating (client_rating)
) $charset_collate;";

// Tabla de interacciones de usuarios
$interactions_table = $wpdb->prefix . 'soloylibre_user_interactions';
$interactions_sql = "CREATE TABLE $interactions_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    user_id bigint(20),
    photo_id bigint(20) NOT NULL,
    interaction_type varchar(50) NOT NULL,
    interaction_value text,
    ip_address varchar(45),
    user_agent text,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY user_id (user_id),
    KEY photo_id (photo_id),
    KEY interaction_type (interaction_type),
    KEY created_at (created_at)
) $charset_collate;";

// Tabla de proyectos (para organización jerárquica)
$projects_table = $wpdb->prefix . 'soloylibre_projects';
$projects_sql = "CREATE TABLE $projects_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    description text,
    project_type varchar(100),
    client_name varchar(255),
    client_email varchar(255),
    client_phone varchar(50),
    start_date date,
    end_date date,
    location varchar(255),
    budget decimal(10, 2),
    status varchar(50) DEFAULT 'active',
    cover_image_id bigint(20),
    photographer_id bigint(20) DEFAULT 1,
    is_public tinyint(1) DEFAULT 0,
    password_protected tinyint(1) DEFAULT 0,
    access_password varchar(255),
    sort_order int(11) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY photographer_id (photographer_id),
    KEY status (status),
    KEY is_public (is_public),
    KEY sort_order (sort_order)
) $charset_collate;";

// Tabla de configuraciones del fotógrafo
$settings_table = $wpdb->prefix . 'soloylibre_photographer_settings';
$settings_sql = "CREATE TABLE $settings_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    photographer_id bigint(20) NOT NULL,
    setting_key varchar(255) NOT NULL,
    setting_value longtext,
    setting_type varchar(50) DEFAULT 'string',
    is_public tinyint(1) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY photographer_setting (photographer_id, setting_key),
    KEY photographer_id (photographer_id),
    KEY is_public (is_public)
) $charset_collate;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

// Crear las tablas
echo "📊 Creando tabla de álbumes...\n";
$result1 = dbDelta($albums_sql);
if ($wpdb->get_var("SHOW TABLES LIKE '$albums_table'") == $albums_table) {
    echo "   ✅ Tabla $albums_table creada exitosamente\n";
} else {
    echo "   ❌ Error creando tabla $albums_table\n";
}

echo "📸 Creando tabla de metadata de fotos...\n";
$result2 = dbDelta($photos_sql);
if ($wpdb->get_var("SHOW TABLES LIKE '$photos_table'") == $photos_table) {
    echo "   ✅ Tabla $photos_table creada exitosamente\n";
} else {
    echo "   ❌ Error creando tabla $photos_table\n";
}

echo "👥 Creando tabla de interacciones...\n";
$result3 = dbDelta($interactions_sql);
if ($wpdb->get_var("SHOW TABLES LIKE '$interactions_table'") == $interactions_table) {
    echo "   ✅ Tabla $interactions_table creada exitosamente\n";
} else {
    echo "   ❌ Error creando tabla $interactions_table\n";
}

echo "📂 Creando tabla de proyectos...\n";
$result4 = dbDelta($projects_sql);
if ($wpdb->get_var("SHOW TABLES LIKE '$projects_table'") == $projects_table) {
    echo "   ✅ Tabla $projects_table creada exitosamente\n";
} else {
    echo "   ❌ Error creando tabla $projects_table\n";
}

echo "⚙️ Creando tabla de configuraciones...\n";
$result5 = dbDelta($settings_sql);
if ($wpdb->get_var("SHOW TABLES LIKE '$settings_table'") == $settings_table) {
    echo "   ✅ Tabla $settings_table creada exitosamente\n";
} else {
    echo "   ❌ Error creando tabla $settings_table\n";
}

// Insertar datos de ejemplo
echo "\n📝 Insertando datos de ejemplo...\n";

// Crear proyecto de ejemplo
$project_data = array(
    'name' => 'Colección República Dominicana 2025',
    'description' => 'Proyecto fotográfico capturando la belleza natural y cultural de República Dominicana',
    'project_type' => 'personal',
    'client_name' => 'Jose L Encarnacion',
    'client_email' => '<EMAIL>',
    'start_date' => '2025-01-01',
    'location' => 'República Dominicana',
    'status' => 'active',
    'photographer_id' => 1,
    'is_public' => 1
);

$wpdb->insert($projects_table, $project_data);
$project_id = $wpdb->insert_id;

if ($project_id) {
    echo "   ✅ Proyecto creado: Colección República Dominicana 2025 (ID: $project_id)\n";
}

// Crear álbum de ejemplo
$album_data = array(
    'name' => 'Paisajes Dominicanos',
    'description' => 'Hermosos paisajes de República Dominicana capturados por Jose L Encarnacion',
    'membership_level' => 'public',
    'is_published' => 1,
    'photographer_id' => 1,
    'project_type' => 'paisajes',
    'location' => 'República Dominicana'
);

$wpdb->insert($albums_table, $album_data);
$album_id = $wpdb->insert_id;

if ($album_id) {
    echo "   ✅ Álbum creado: Paisajes Dominicanos (ID: $album_id)\n";
}

// Actualizar fotos existentes con metadata
$existing_photos = get_posts(array(
    'post_type' => 'soloylibre_photo',
    'posts_per_page' => -1,
    'post_status' => array('publish', 'draft')
));

foreach ($existing_photos as $photo) {
    $photo_meta = array(
        'post_id' => $photo->ID,
        'album_id' => $album_id,
        'photo_state' => 'ready',
        'is_publishable' => 1,
        'is_reviewed' => 1,
        'membership_level' => 'public',
        'location' => 'República Dominicana',
        'camera_make' => 'Canon',
        'camera_model' => 'EOS R5',
        'photographer_rating' => 5,
        'views_count' => rand(50, 500),
        'likes_count' => rand(10, 100)
    );
    
    $wpdb->insert($photos_table, $photo_meta);
}

echo "   ✅ Metadata agregada a " . count($existing_photos) . " fotos existentes\n";

// Insertar configuraciones del fotógrafo
$photographer_settings = array(
    array('setting_key' => 'business_name', 'setting_value' => 'SoloYLibre Photography', 'is_public' => 1),
    array('setting_key' => 'photographer_name', 'setting_value' => 'Jose L Encarnacion', 'is_public' => 1),
    array('setting_key' => 'photographer_alias', 'setting_value' => 'JoseTusabe', 'is_public' => 1),
    array('setting_key' => 'location', 'setting_value' => 'San José de Ocoa, República Dominicana / USA', 'is_public' => 1),
    array('setting_key' => 'phone', 'setting_value' => '************', 'is_public' => 1),
    array('setting_key' => 'email', 'setting_value' => '<EMAIL>', 'is_public' => 1),
    array('setting_key' => 'website_primary', 'setting_value' => 'josetusabe.com', 'is_public' => 1),
    array('setting_key' => 'website_business', 'setting_value' => 'soloylibre.com', 'is_public' => 1),
    array('setting_key' => 'website_portfolio', 'setting_value' => '1and1photo.com', 'is_public' => 1),
    array('setting_key' => 'website_personal', 'setting_value' => 'joselencarnacion.com', 'is_public' => 1),
    array('setting_key' => 'specialties', 'setting_value' => 'Fotografía dominicana, Paisajes, Retratos, Cultura', 'is_public' => 1),
    array('setting_key' => 'equipment_primary', 'setting_value' => 'Canon EOS R5', 'is_public' => 0),
    array('setting_key' => 'years_experience', 'setting_value' => '10+', 'is_public' => 1)
);

foreach ($photographer_settings as $setting) {
    $setting['photographer_id'] = 1;
    $wpdb->insert($settings_table, $setting);
}

echo "   ✅ Configuraciones del fotógrafo insertadas\n";

echo "\n🎉 ¡Base de datos completamente configurada!\n";
echo "📊 Resumen de tablas creadas:\n";
echo "   📁 $albums_table - Gestión de álbumes\n";
echo "   📸 $photos_table - Metadata detallada de fotos\n";
echo "   👥 $interactions_table - Interacciones de usuarios\n";
echo "   📂 $projects_table - Organización por proyectos\n";
echo "   ⚙️ $settings_table - Configuraciones del fotógrafo\n";

echo "\n🔗 Enlaces útiles:\n";
echo "   🌐 Ver galería: " . home_url('/soloylibre-gallery-jose-l-encarnacion-photography/') . "\n";
echo "   ⚙️ Admin plugin: " . admin_url('admin.php?page=soloylibre-gallery') . "\n";
echo "   📝 Gestionar fotos: " . admin_url('edit.php?post_type=soloylibre_photo') . "\n";

?>
