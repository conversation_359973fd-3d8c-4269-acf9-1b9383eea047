<?php
/**
 * Complete Plugin Audit and Fix
 * Auditoría completa y corrección del plugin
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

echo "🔍 AUDITORÍA COMPLETA Y CORRECCIÓN DEL PLUGIN\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// 1. Auditoría de archivos del plugin
echo "1. 📁 AUDITORÍA DE ARCHIVOS DEL PLUGIN...\n";

$plugin_structure = array(
    'wp-content/plugins/Archive/soloylibre-gallery-plugin.php' => 'Plugin Principal',
    'wp-content/plugins/Archive/includes/class-database.php' => 'Base de Datos',
    'wp-content/plugins/Archive/includes/class-improved-wizard.php' => 'Wizard Mejorado',
    'wp-content/plugins/Archive/includes/class-statistics-dashboard.php' => 'Dashboard',
    'wp-content/plugins/Archive/includes/class-api-manager.php' => 'API Manager (Desactivado)',
    'wp-content/plugins/Archive/includes/class-simple-wizard.php' => 'Wizard Simple (Legacy)',
    'wp-content/plugins/Archive/includes/class-photo-wizard.php' => 'Photo Wizard (Legacy)'
);

$missing_files = array();
$existing_files = array();

foreach ($plugin_structure as $file => $description) {
    if (file_exists($file)) {
        $existing_files[] = $file;
        echo "   ✅ $description\n";
    } else {
        $missing_files[] = $file;
        echo "   ❌ $description - FALTANTE\n";
    }
}

// 2. Buscar errores comunes en logs
echo "\n2. 🐛 BUSCANDO ERRORES COMUNES...\n";

$error_patterns = array(
    'Fatal error' => 'Errores fatales',
    'Parse error' => 'Errores de sintaxis',
    'Call to undefined' => 'Métodos/funciones no definidos',
    'Class not found' => 'Clases no encontradas',
    'Cannot redeclare' => 'Declaraciones duplicadas',
    'foreach()' => 'Errores de foreach',
    'API_Manager' => 'Errores de API Manager'
);

$log_files = array('wp-content/debug.log', 'error_log', 'php_error.log');
$errors_found = array();

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        $content = file_get_contents($log_file);
        if (!empty(trim($content))) {
            foreach ($error_patterns as $pattern => $description) {
                if (stripos($content, $pattern) !== false) {
                    $errors_found[$pattern] = $description;
                }
            }
        }
    }
}

if (empty($errors_found)) {
    echo "   ✅ No se encontraron errores comunes en logs\n";
} else {
    echo "   ❌ Errores encontrados:\n";
    foreach ($errors_found as $pattern => $description) {
        echo "      - $description ($pattern)\n";
    }
}

// 3. Verificar shortcodes
echo "\n3. 🏷️ VERIFICANDO SHORTCODES...\n";

$plugin_file = 'wp-content/plugins/Archive/soloylibre-gallery-plugin.php';
if (file_exists($plugin_file)) {
    $content = file_get_contents($plugin_file);
    
    if (strpos($content, 'add_shortcode') !== false) {
        echo "   ✅ Shortcodes registrados encontrados\n";
        
        // Buscar el método del shortcode
        if (strpos($content, 'gallery_shortcode') !== false) {
            echo "   ✅ Método gallery_shortcode encontrado\n";
        } else {
            echo "   ❌ Método gallery_shortcode NO encontrado\n";
        }
    } else {
        echo "   ❌ No se encontraron registros de shortcodes\n";
    }
} else {
    echo "   ❌ Archivo principal del plugin no encontrado\n";
}

// 4. Crear shortcode mejorado
echo "\n4. 🛠️ CREANDO SHORTCODE MEJORADO...\n";

$shortcode_class = '<?php
/**
 * Advanced Gallery Shortcode Handler
 * Manejador avanzado de shortcodes para galerías
 */

class SoloYLibre_Advanced_Shortcode {
    
    public function __construct() {
        add_shortcode("soloylibre_gallery", array($this, "render_gallery"));
        add_action("wp_enqueue_scripts", array($this, "enqueue_gallery_assets"));
    }
    
    public function enqueue_gallery_assets() {
        // CSS para galerías
        wp_add_inline_style("wp-block-library", $this->get_gallery_css());
        
        // JavaScript para lightbox y funcionalidades
        wp_add_inline_script("jquery", $this->get_gallery_js());
    }
    
    public function render_gallery($atts) {
        $atts = shortcode_atts(array(
            "ids" => "",
            "style" => "grid",
            "columns" => "3",
            "size" => "medium",
            "lightbox" => "true",
            "show_metadata" => "false",
            "show_title" => "true",
            "show_caption" => "false",
            "hover_effect" => "zoom",
            "border_radius" => "8",
            "spacing" => "15",
            "animation" => "fadeIn",
            "photographer_signature" => "true",
            "watermark" => "false",
            "download_button" => "false",
            "social_share" => "false",
            "autoplay" => "false",
            "autoplay_speed" => "3000"
        ), $atts, "soloylibre_gallery");
        
        if (empty($atts["ids"])) {
            return "<p>Error: No se especificaron IDs de imágenes para la galería.</p>";
        }
        
        $image_ids = explode(",", $atts["ids"]);
        $image_ids = array_map("trim", $image_ids);
        $image_ids = array_filter($image_ids, "is_numeric");
        
        if (empty($image_ids)) {
            return "<p>Error: IDs de imágenes no válidos.</p>";
        }
        
        $gallery_id = "soloylibre-gallery-" . uniqid();
        $output = "";
        
        // Contenedor principal
        $output .= "<div id=\"$gallery_id\" class=\"soloylibre-gallery soloylibre-style-{$atts["style"]}\" data-lightbox=\"{$atts["lightbox"]}\" data-animation=\"{$atts["animation"]}\">";
        
        // Título de la galería (opcional)
        if (!empty($atts["title"])) {
            $output .= "<h3 class=\"gallery-title\">{$atts["title"]}</h3>";
        }
        
        // Grid de imágenes
        $output .= "<div class=\"gallery-grid\" style=\"grid-template-columns: repeat({$atts["columns"]}, 1fr); gap: {$atts["spacing"]}px;\">";
        
        foreach ($image_ids as $image_id) {
            $image_id = intval($image_id);
            $image = wp_get_attachment_image_src($image_id, $atts["size"]);
            $full_image = wp_get_attachment_image_src($image_id, "full");
            $metadata = wp_get_attachment_metadata($image_id);
            $title = get_the_title($image_id);
            $caption = wp_get_attachment_caption($image_id);
            $alt = get_post_meta($image_id, "_wp_attachment_image_alt", true);
            
            if (!$image) continue;
            
            $output .= "<div class=\"gallery-item hover-{$atts["hover_effect"]}\" style=\"border-radius: {$atts["border_radius"]}px;\">";
            
            // Imagen
            $lightbox_attr = $atts["lightbox"] === "true" ? "data-lightbox=\"gallery-$gallery_id\" data-title=\"$title\"" : "";
            $full_url = $atts["lightbox"] === "true" ? $full_image[0] : "#";
            
            $output .= "<a href=\"$full_url\" $lightbox_attr class=\"gallery-link\">";
            $output .= "<img src=\"{$image[0]}\" alt=\"$alt\" title=\"$title\" loading=\"lazy\">";
            
            // Overlay con información
            if ($atts["show_title"] === "true" || $atts["show_caption"] === "true" || $atts["show_metadata"] === "true") {
                $output .= "<div class=\"gallery-overlay\">";
                
                if ($atts["show_title"] === "true" && !empty($title)) {
                    $output .= "<h4 class=\"image-title\">$title</h4>";
                }
                
                if ($atts["show_caption"] === "true" && !empty($caption)) {
                    $output .= "<p class=\"image-caption\">$caption</p>";
                }
                
                if ($atts["show_metadata"] === "true" && $metadata) {
                    $output .= "<div class=\"image-metadata\">";
                    if (isset($metadata["width"]) && isset($metadata["height"])) {
                        $output .= "<span class=\"meta-dimensions\">{$metadata["width"]} × {$metadata["height"]}</span>";
                    }
                    if (isset($metadata["filesize"])) {
                        $size_mb = round($metadata["filesize"] / 1024 / 1024, 2);
                        $output .= "<span class=\"meta-size\">{$size_mb} MB</span>";
                    }
                    $output .= "</div>";
                }
                
                $output .= "</div>";
            }
            
            $output .= "</a>";
            
            // Botones adicionales
            if ($atts["download_button"] === "true" || $atts["social_share"] === "true") {
                $output .= "<div class=\"gallery-actions\">";
                
                if ($atts["download_button"] === "true") {
                    $output .= "<a href=\"{$full_image[0]}\" download class=\"btn-download\" title=\"Descargar\">📥</a>";
                }
                
                if ($atts["social_share"] === "true") {
                    $share_url = urlencode(get_permalink());
                    $share_text = urlencode("Mira esta foto de SoloYLibre Photography");
                    $output .= "<a href=\"https://twitter.com/intent/tweet?url=$share_url&text=$share_text\" target=\"_blank\" class=\"btn-share\" title=\"Compartir\">📤</a>";
                }
                
                $output .= "</div>";
            }
            
            $output .= "</div>";
        }
        
        $output .= "</div>"; // Cierre gallery-grid
        
        // Firma del fotógrafo
        if ($atts["photographer_signature"] === "true") {
            $output .= "<div class=\"photographer-signature\">";
            $output .= "<p><strong>📸 Jose L Encarnacion (JoseTusabe)</strong><br>";
            $output .= "SoloYLibre Photography<br>";
            $output .= "📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸<br>";
            $output .= "📞 718-713-5500 | 📧 <EMAIL><br>";
            $output .= "🌐 <a href=\"https://josetusabe.com\">josetusabe.com</a> | <a href=\"https://soloylibre.com\">soloylibre.com</a></p>";
            $output .= "</div>";
        }
        
        $output .= "</div>"; // Cierre contenedor principal
        
        return $output;
    }
    
    private function get_gallery_css() {
        return "
        .soloylibre-gallery {
            margin: 20px 0;
            font-family: Arial, sans-serif;
        }
        
        .gallery-title {
            text-align: center;
            color: #CE1126;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .gallery-grid {
            display: grid;
            margin-bottom: 20px;
        }
        
        .gallery-item {
            position: relative;
            overflow: hidden;
            background: #f8f9fa;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .gallery-link {
            display: block;
            position: relative;
            text-decoration: none;
        }
        
        .gallery-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .hover-zoom .gallery-item:hover img {
            transform: scale(1.1);
        }
        
        .hover-fade .gallery-item:hover img {
            opacity: 0.8;
        }
        
        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 15px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover .gallery-overlay {
            transform: translateY(0);
        }
        
        .image-title {
            margin: 0 0 5px 0;
            font-size: 1rem;
            font-weight: 600;
        }
        
        .image-caption {
            margin: 0 0 10px 0;
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .image-metadata {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .meta-dimensions, .meta-size {
            margin-right: 10px;
        }
        
        .gallery-actions {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }
        
        .btn-download, .btn-share {
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px;
            border-radius: 50%;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }
        
        .btn-download:hover, .btn-share:hover {
            background: #CE1126;
        }
        
        .photographer-signature {
            background: #f8f9fa;
            border-left: 4px solid #CE1126;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 0.9rem;
            line-height: 1.6;
        }
        
        .photographer-signature a {
            color: #CE1126;
            text-decoration: none;
        }
        
        .photographer-signature a:hover {
            text-decoration: underline;
        }
        
        /* Estilos específicos por tipo */
        .soloylibre-style-dominican .gallery-item {
            border: 2px solid #CE1126;
        }
        
        .soloylibre-style-dominican .gallery-title {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
        }
        
        .soloylibre-style-masonry .gallery-grid {
            column-count: var(--columns, 3);
            column-gap: 15px;
        }
        
        .soloylibre-style-masonry .gallery-item {
            break-inside: avoid;
            margin-bottom: 15px;
        }
        
        .soloylibre-style-carousel {
            overflow-x: auto;
        }
        
        .soloylibre-style-carousel .gallery-grid {
            display: flex;
            gap: 15px;
            min-width: max-content;
        }
        
        .soloylibre-style-carousel .gallery-item {
            flex: 0 0 300px;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: repeat(2, 1fr) !important;
            }
            
            .soloylibre-style-carousel .gallery-item {
                flex: 0 0 250px;
            }
        }
        
        @media (max-width: 480px) {
            .gallery-grid {
                grid-template-columns: 1fr !important;
            }
            
            .soloylibre-style-carousel .gallery-item {
                flex: 0 0 200px;
            }
        }
        ";
    }
    
    private function get_gallery_js() {
        return "
        jQuery(document).ready(function($) {
            // Simple lightbox functionality
            $(\".soloylibre-gallery a[data-lightbox]\").click(function(e) {
                e.preventDefault();
                
                var src = $(this).attr(\"href\");
                var title = $(this).data(\"title\") || \"\";
                
                var lightbox = $(\"<div class=\\\"soloylibre-lightbox\\\" style=\\\"position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.9); z-index: 9999; display: flex; align-items: center; justify-content: center; cursor: pointer;\\\">\");
                var img = $(\"<img style=\\\"max-width: 90%; max-height: 90%; object-fit: contain;\\\" src=\\\"\" + src + \"\\\" alt=\\\"\" + title + \"\\\">\");
                var close = $(\"<span style=\\\"position: absolute; top: 20px; right: 30px; color: white; font-size: 2rem; cursor: pointer;\\\">&times;</span>\");
                
                lightbox.append(img).append(close);
                $(\"body\").append(lightbox);
                
                lightbox.fadeIn(300);
                
                lightbox.click(function() {
                    lightbox.fadeOut(300, function() {
                        lightbox.remove();
                    });
                });
                
                img.click(function(e) {
                    e.stopPropagation();
                });
            });
            
            // Animation on scroll
            if (typeof IntersectionObserver !== \"undefined\") {
                var observer = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            $(entry.target).find(\".gallery-item\").each(function(index) {
                                var item = $(this);
                                setTimeout(function() {
                                    item.css({
                                        \"opacity\": \"1\",
                                        \"transform\": \"translateY(0)\"
                                    });
                                }, index * 100);
                            });
                        }
                    });
                });
                
                $(\".soloylibre-gallery\").each(function() {
                    $(this).find(\".gallery-item\").css({
                        \"opacity\": \"0\",
                        \"transform\": \"translateY(20px)\",
                        \"transition\": \"opacity 0.6s ease, transform 0.6s ease\"
                    });
                    observer.observe(this);
                });
            }
        });
        ";
    }
}

new SoloYLibre_Advanced_Shortcode();
?>';

file_put_contents('wp-content/plugins/Archive/includes/class-advanced-shortcode.php', $shortcode_class);
echo "   ✅ Clase de shortcode avanzado creada\n";

echo "\n" . str_repeat("=", 70) . "\n";
echo "✅ AUDITORÍA COMPLETA REALIZADA\n";
echo str_repeat("=", 70) . "\n";

echo "\n📊 RESUMEN DE AUDITORÍA:\n";
echo "   📁 Archivos existentes: " . count($existing_files) . "/" . count($plugin_structure) . "\n";
echo "   🐛 Errores encontrados: " . count($errors_found) . "\n";
echo "   🏷️ Shortcode mejorado: ✅ Creado\n";

if (!empty($missing_files)) {
    echo "\n❌ ARCHIVOS FALTANTES:\n";
    foreach ($missing_files as $file) {
        echo "   - " . basename($file) . "\n";
    }
}

if (!empty($errors_found)) {
    echo "\n🐛 ERRORES DETECTADOS:\n";
    foreach ($errors_found as $pattern => $description) {
        echo "   - $description\n";
    }
}

echo "\n🎯 PRÓXIMOS PASOS:\n";
echo "   1. Integrar shortcode avanzado en plugin principal\n";
echo "   2. Crear ejemplos de uso\n";
echo "   3. Probar todas las opciones\n";
echo "   4. Documentar funcionalidades\n";

echo "\n📞 SOPORTE:\n";
echo "   📧 <EMAIL>\n";
echo "   📱 718-713-5500\n";
echo "   🇩🇴 Jose L Encarnacion (JoseTusabe)\n";

?>
