<?php
/**
 * Test Photo Orientations and Aspect Ratios
 * Prueba de orientaciones y proporciones de fotos
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

require_once('wp-load.php');

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>📐 Orientaciones de Fotos - SoloYLibre Gallery Pro</title>";
echo "<meta charset=\"UTF-8\">";
echo "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">";

wp_head();

echo "<style>";
echo "body { margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-family: Arial, sans-serif; }";
echo ".container { max-width: 1400px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }";
echo ".demo-section { background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 15px; padding: 25px; margin: 20px 0; border: 1px solid rgba(255,255,255,0.2); }";
echo ".demo-title { color: #FFD700; font-size: 1.5rem; margin-bottom: 15px; }";
echo ".shortcode-display { background: #2d3748; color: #e2e8f0; padding: 12px; border-radius: 8px; font-family: monospace; margin: 10px 0; font-size: 0.9rem; }";
echo ".info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }";
echo ".info-card { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; }";
echo ".success { color: #00ff88; }";
echo ".info { color: #74b9ff; }";
echo ".warning { color: #ffb347; }";
echo "</style>";

echo "<script src=\"https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js\"></script>";

echo "</head>";
echo "<body>";

echo "<div class=\"container\">";

echo "<div class=\"header\">";
echo "<h1>📐 Orientaciones y Proporciones de Fotos</h1>";
echo "<h2>SoloYLibre Gallery Pro v5.0.6</h2>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe) - Fotos Completas Sin Recortar</p>";
echo "</div>";

// Obtener fotos de ejemplo
$all_photos = get_posts(array(
    'post_type' => 'attachment',
    'post_mime_type' => 'image',
    'posts_per_page' => 12,
    'post_status' => 'inherit'
));

if (!empty($all_photos)) {
    $photo_ids = implode(',', wp_list_pluck($all_photos, 'ID'));
    
    // 1. Modo Automático (Respeta orientación natural)
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">1. 🎯 Modo Automático - Respeta Orientación Natural</h2>";
    echo "<p class=\"success\">✅ Las fotos portrait se ven verticales completas</p>";
    echo "<p class=\"success\">✅ Las fotos landscape se ven horizontales completas</p>";
    echo "<p class=\"success\">✅ Sin recortar, sin distorsionar</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"dominican\" ids=\"$photo_ids\" columns=\"4\" image_fit=\"auto\" aspect_ratio=\"natural\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"dominican\" ids=\"$photo_ids\" columns=\"4\" image_fit=\"auto\" aspect_ratio=\"natural\"]");
    echo "</div>";
    
    // 2. Estilo Masonry - Perfecto para orientaciones mixtas
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">2. 🧱 Estilo Masonry - Orientaciones Mixtas</h2>";
    echo "<p class=\"info\">📐 Layout tipo Pinterest que se adapta automáticamente</p>";
    echo "<p class=\"success\">✅ Cada foto mantiene su proporción original</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"masonry\" ids=\"$photo_ids\" columns=\"3\" aspect_ratio=\"natural\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"masonry\" ids=\"$photo_ids\" columns=\"3\" aspect_ratio=\"natural\"]");
    echo "</div>";
    
    // 3. Modo Contain - Fotos completas con fondo
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">3. 📦 Modo Contain - Fotos Completas con Fondo</h2>";
    echo "<p class=\"info\">🖼️ Muestra la foto completa dentro de un contenedor fijo</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"grid\" ids=\"$photo_ids\" columns=\"3\" image_fit=\"contain\" max_height=\"350\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"grid\" ids=\"$photo_ids\" columns=\"3\" image_fit=\"contain\" max_height=\"350\"]");
    echo "</div>";
    
    // 4. Comparación: Antes vs Después
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">4. 📊 Comparación: Antes vs Después</h2>";
    
    echo "<div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 30px;\">";
    
    echo "<div>";
    echo "<h4 style=\"color: #ff6b6b;\">❌ ANTES (Modo Cover - Recortado)</h4>";
    echo "<p class=\"warning\">Todas las fotos se recortan a cuadrados</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"professional\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\" image_fit=\"cover\"]</div>";
    echo do_shortcode("[soloylibre_gallery style=\"professional\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\" image_fit=\"cover\"]");
    echo "</div>";
    
    echo "<div>";
    echo "<h4 style=\"color: #00ff88;\">✅ DESPUÉS (Modo Natural - Completo)</h4>";
    echo "<p class=\"success\">Cada foto mantiene su proporción original</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"dominican\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\" aspect_ratio=\"natural\"]</div>";
    echo do_shortcode("[soloylibre_gallery style=\"dominican\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\" aspect_ratio=\"natural\"]");
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
} else {
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">❌ No hay fotos disponibles</h2>";
    echo "<p>Sube algunas imágenes a la biblioteca de medios para probar las orientaciones.</p>";
    echo "</div>";
}

// Información técnica
echo "<div class=\"demo-section\">";
echo "<h2 class=\"demo-title\">🔧 Nuevas Opciones Implementadas</h2>";

echo "<div class=\"info-grid\">";

echo "<div class=\"info-card\">";
echo "<h4>📐 image_fit (Ajuste de Imagen)</h4>";
echo "<ul>";
echo "<li><strong>auto:</strong> Automático, respeta orientación</li>";
echo "<li><strong>cover:</strong> Recorta para llenar (anterior)</li>";
echo "<li><strong>contain:</strong> Muestra completa con fondo</li>";
echo "<li><strong>fill:</strong> Estira para llenar</li>";
echo "</ul>";
echo "</div>";

echo "<div class=\"info-card\">";
echo "<h4>📏 aspect_ratio (Proporción)</h4>";
echo "<ul>";
echo "<li><strong>natural:</strong> Mantiene proporción original</li>";
echo "<li><strong>square:</strong> Fuerza cuadrado</li>";
echo "<li><strong>16:9:</strong> Proporción widescreen</li>";
echo "<li><strong>4:3:</strong> Proporción clásica</li>";
echo "</ul>";
echo "</div>";

echo "<div class=\"info-card\">";
echo "<h4>📏 Dimensiones Personalizables</h4>";
echo "<ul>";
echo "<li><strong>min_height:</strong> Altura mínima (px)</li>";
echo "<li><strong>max_height:</strong> Altura máxima (px)</li>";
echo "<li><strong>border_radius:</strong> Radio de bordes</li>";
echo "<li><strong>spacing:</strong> Espaciado entre fotos</li>";
echo "</ul>";
echo "</div>";

echo "<div class=\"info-card\">";
echo "<h4>🎨 Detección Automática</h4>";
echo "<ul>";
echo "<li><strong>Portrait:</strong> Alto > Ancho (vertical)</li>";
echo "<li><strong>Landscape:</strong> Ancho > Alto (horizontal)</li>";
echo "<li><strong>Square:</strong> Ancho ≈ Alto (cuadrado)</li>";
echo "<li><strong>Auto-CSS:</strong> Clases aplicadas automáticamente</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "</div>";

// Ejemplos de shortcodes
echo "<div class=\"demo-section\">";
echo "<h2 class=\"demo-title\">📝 Ejemplos de Shortcodes</h2>";

echo "<h4>🎯 Para fotos portrait (verticales):</h4>";
echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"dominican\" image_fit=\"auto\" aspect_ratio=\"natural\" max_height=\"500\"]</div>";

echo "<h4>🌄 Para fotos landscape (horizontales):</h4>";
echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"carousel\" image_fit=\"contain\" max_height=\"300\"]</div>";

echo "<h4>🧱 Para mezcla de orientaciones:</h4>";
echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"masonry\" columns=\"4\" aspect_ratio=\"natural\"]</div>";

echo "<h4>📦 Para mantener tamaño uniforme:</h4>";
echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"grid\" image_fit=\"contain\" min_height=\"250\" max_height=\"350\"]</div>";

echo "</div>";

echo "</div>"; // container

// JavaScript
echo "<script>";
echo "console.log('📐 SoloYLibre: Página de orientaciones cargada');";

echo "// Confetti de bienvenida";
echo "setTimeout(function() {";
echo "    if (typeof confetti !== 'undefined') {";
echo "        confetti({";
echo "            particleCount: 100,";
echo "            spread: 70,";
echo "            origin: { y: 0.6 },";
echo "            colors: ['#CE1126', '#002D62', '#FFD700']";
echo "        });";
echo "    }";
echo "}, 1000);";

echo "// Mostrar información de orientación al hacer hover";
echo "jQuery(document).ready(function($) {";
echo "    $('.gallery-image').hover(function() {";
echo "        var orientation = $(this).data('orientation');";
echo "        var ratio = $(this).data('ratio');";
echo "        if (orientation && ratio) {";
echo "            console.log('Foto:', {orientation: orientation, ratio: ratio});";
echo "        }";
echo "    });";
echo "});";

echo "</script>";

wp_footer();

echo "</body>";
echo "</html>";
?>
