<?php
/**
 * SoloYLibre Gallery Shortcode Examples and Documentation
 * Ejemplos y documentación de shortcodes
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>📚 Shortcodes SoloYLibre - Ejemplos y Documentación</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); }";
echo ".container { background: white; padding: 40px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); max-width: 1200px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; }";
echo ".section { margin: 30px 0; padding: 25px; border-radius: 10px; border: 1px solid #e0e0e0; }";
echo ".example { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #CE1126; }";
echo ".code { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; margin: 10px 0; overflow-x: auto; }";
echo ".attribute-table { width: 100%; border-collapse: collapse; margin: 15px 0; }";
echo ".attribute-table th, .attribute-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }";
echo ".attribute-table th { background: #CE1126; color: white; }";
echo ".attribute-table tr:nth-child(even) { background: #f9f9f9; }";
echo ".btn { background: #CE1126; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; display: inline-block; margin: 10px 5px; font-weight: 600; transition: all 0.3s ease; }";
echo ".btn:hover { background: #002D62; transform: translateY(-2px); }";
echo ".highlight { background: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 10px 0; }";
echo ".success { background: #d4edda; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745; margin: 10px 0; }";
echo ".toc { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }";
echo ".toc ul { list-style: none; padding: 0; }";
echo ".toc li { margin: 8px 0; }";
echo ".toc a { color: #CE1126; text-decoration: none; font-weight: 500; }";
echo ".toc a:hover { text-decoration: underline; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class=\"container\">";

echo "<div class=\"header\">";
echo "<h1>📚 SoloYLibre Gallery Shortcodes</h1>";
echo "<p>🇩🇴 Documentación Completa y Ejemplos Avanzados</p>";
echo "<p>Jose L Encarnacion (JoseTusabe) - SoloYLibre Photography</p>";
echo "<p>⏰ Actualizado: " . date("Y-m-d H:i:s") . "</p>";
echo "</div>";

// Tabla de contenidos
echo "<div class=\"toc\">";
echo "<h3>📋 Tabla de Contenidos</h3>";
echo "<ul>";
echo "<li><a href=\"#basic-usage\">🔰 Uso Básico</a></li>";
echo "<li><a href=\"#attributes\">⚙️ Atributos Disponibles</a></li>";
echo "<li><a href=\"#examples\">📸 Ejemplos Prácticos</a></li>";
echo "<li><a href=\"#styles\">🎨 Estilos de Galería</a></li>";
echo "<li><a href=\"#advanced\">🚀 Opciones Avanzadas</a></li>";
echo "<li><a href=\"#troubleshooting\">🔧 Solución de Problemas</a></li>";
echo "</ul>";
echo "</div>";

// Uso básico
echo "<div class=\"section\" id=\"basic-usage\">";
echo "<h2>🔰 Uso Básico del Shortcode</h2>";
echo "<p>El shortcode <code>[soloylibre_gallery]</code> permite crear galerías profesionales con múltiples opciones de personalización.</p>";

echo "<div class=\"example\">";
echo "<h4>Ejemplo Básico:</h4>";
echo "<div class=\"code\">[soloylibre_gallery ids=\"175,174,173,172,171\"]</div>";
echo "<p><strong>Resultado:</strong> Galería simple con las imágenes especificadas.</p>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>Ejemplo con Estilo Dominicano:</h4>";
echo "<div class=\"code\">[soloylibre_gallery style=\"dominican\" ids=\"175,174,173,172,171,170,169,168,167\" lightbox=\"true\" show_metadata=\"true\"]</div>";
echo "<p><strong>Resultado:</strong> Galería con colores dominicanos, lightbox y metadatos.</p>";
echo "</div>";
echo "</div>";

// Atributos disponibles
echo "<div class=\"section\" id=\"attributes\">";
echo "<h2>⚙️ Atributos Disponibles</h2>";
echo "<p>Lista completa de todos los atributos que puedes usar en el shortcode:</p>";

echo "<table class=\"attribute-table\">";
echo "<tr><th>Atributo</th><th>Valores</th><th>Por Defecto</th><th>Descripción</th></tr>";
echo "<tr><td><code>ids</code></td><td>Lista de IDs</td><td>-</td><td><strong>Requerido.</strong> IDs de imágenes separados por comas</td></tr>";
echo "<tr><td><code>style</code></td><td>grid, dominican, masonry, carousel, professional</td><td>grid</td><td>Estilo visual de la galería</td></tr>";
echo "<tr><td><code>columns</code></td><td>1-6</td><td>3</td><td>Número de columnas en el grid</td></tr>";
echo "<tr><td><code>size</code></td><td>thumbnail, medium, large, full</td><td>medium</td><td>Tamaño de las imágenes</td></tr>";
echo "<tr><td><code>lightbox</code></td><td>true, false</td><td>true</td><td>Habilitar lightbox al hacer clic</td></tr>";
echo "<tr><td><code>show_metadata</code></td><td>true, false</td><td>false</td><td>Mostrar dimensiones y tamaño</td></tr>";
echo "<tr><td><code>show_title</code></td><td>true, false</td><td>true</td><td>Mostrar título de la imagen</td></tr>";
echo "<tr><td><code>show_caption</code></td><td>true, false</td><td>false</td><td>Mostrar descripción de la imagen</td></tr>";
echo "<tr><td><code>hover_effect</code></td><td>zoom, fade, none</td><td>zoom</td><td>Efecto al pasar el mouse</td></tr>";
echo "<tr><td><code>border_radius</code></td><td>0-50</td><td>8</td><td>Radio de bordes en píxeles</td></tr>";
echo "<tr><td><code>spacing</code></td><td>0-50</td><td>15</td><td>Espaciado entre imágenes</td></tr>";
echo "<tr><td><code>animation</code></td><td>fadeIn, slideUp, none</td><td>fadeIn</td><td>Animación de entrada</td></tr>";
echo "<tr><td><code>photographer_signature</code></td><td>true, false</td><td>true</td><td>Mostrar firma del fotógrafo</td></tr>";
echo "<tr><td><code>download_button</code></td><td>true, false</td><td>false</td><td>Botón de descarga en cada imagen</td></tr>";
echo "<tr><td><code>social_share</code></td><td>true, false</td><td>false</td><td>Botón de compartir en redes</td></tr>";
echo "</table>";
echo "</div>";

// Ejemplos prácticos
echo "<div class=\"section\" id=\"examples\">";
echo "<h2>📸 Ejemplos Prácticos</h2>";

echo "<div class=\"example\">";
echo "<h4>🇩🇴 Galería Estilo Dominicano Completa:</h4>";
echo "<div class=\"code\">[soloylibre_gallery style=\"dominican\" ids=\"175,174,173,172,171,170,169,168,167\" columns=\"3\" lightbox=\"true\" show_metadata=\"true\" show_title=\"true\" hover_effect=\"zoom\" photographer_signature=\"true\"]</div>";
echo "<p><strong>Características:</strong> Colores dominicanos, 3 columnas, lightbox, metadatos, títulos, efecto zoom, firma del fotógrafo.</p>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>📱 Galería Responsive para Móviles:</h4>";
echo "<div class=\"code\">[soloylibre_gallery style=\"grid\" ids=\"175,174,173,172\" columns=\"2\" size=\"medium\" spacing=\"10\" border_radius=\"12\" hover_effect=\"fade\"]</div>";
echo "<p><strong>Características:</strong> 2 columnas, tamaño medio, espaciado reducido, bordes redondeados, efecto fade.</p>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>🎠 Galería Carousel Horizontal:</h4>";
echo "<div class=\"code\">[soloylibre_gallery style=\"carousel\" ids=\"175,174,173,172,171,170,169\" size=\"large\" lightbox=\"true\" show_caption=\"true\"]</div>";
echo "<p><strong>Características:</strong> Scroll horizontal, imágenes grandes, lightbox, descripciones visibles.</p>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>🧱 Galería Masonry (Pinterest Style):</h4>";
echo "<div class=\"code\">[soloylibre_gallery style=\"masonry\" ids=\"175,174,173,172,171,170\" columns=\"4\" show_title=\"true\" show_metadata=\"true\" animation=\"slideUp\"]</div>";
echo "<p><strong>Características:</strong> Estilo Pinterest, 4 columnas, títulos, metadatos, animación slideUp.</p>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>💼 Galería Profesional con Todas las Opciones:</h4>";
echo "<div class=\"code\">[soloylibre_gallery style=\"professional\" ids=\"175,174,173,172,171\" columns=\"3\" size=\"large\" lightbox=\"true\" show_metadata=\"true\" show_title=\"true\" show_caption=\"true\" hover_effect=\"zoom\" download_button=\"true\" social_share=\"true\" photographer_signature=\"true\"]</div>";
echo "<p><strong>Características:</strong> Estilo profesional, todas las opciones habilitadas, botones de descarga y compartir.</p>";
echo "</div>";
echo "</div>";

// Estilos de galería
echo "<div class=\"section\" id=\"styles\">";
echo "<h2>🎨 Estilos de Galería Disponibles</h2>";

echo "<div class=\"example\">";
echo "<h4>🇩🇴 dominican</h4>";
echo "<p>Estilo con colores de la bandera dominicana (#CE1126, #002D62). Ideal para fotografías de República Dominicana.</p>";
echo "<div class=\"code\">[soloylibre_gallery style=\"dominican\" ids=\"175,174,173\"]</div>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>📱 grid</h4>";
echo "<p>Grid clásico y limpio. Perfecto para cualquier tipo de fotografía.</p>";
echo "<div class=\"code\">[soloylibre_gallery style=\"grid\" ids=\"175,174,173\"]</div>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>🧱 masonry</h4>";
echo "<p>Estilo Pinterest con alturas variables. Ideal para portfolios artísticos.</p>";
echo "<div class=\"code\">[soloylibre_gallery style=\"masonry\" ids=\"175,174,173\"]</div>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>🎠 carousel</h4>";
echo "<p>Galería horizontal con scroll. Perfecta para mostrar secuencias.</p>";
echo "<div class=\"code\">[soloylibre_gallery style=\"carousel\" ids=\"175,174,173\"]</div>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>💼 professional</h4>";
echo "<p>Estilo elegante para uso comercial y profesional.</p>";
echo "<div class=\"code\">[soloylibre_gallery style=\"professional\" ids=\"175,174,173\"]</div>";
echo "</div>";
echo "</div>";

// Opciones avanzadas
echo "<div class=\"section\" id=\"advanced\">";
echo "<h2>🚀 Opciones Avanzadas</h2>";

echo "<div class=\"highlight\">";
echo "<h4>💡 Consejos para Mejores Resultados:</h4>";
echo "<ul>";
echo "<li><strong>IDs de Imágenes:</strong> Usa IDs consecutivos para mejor organización</li>";
echo "<li><strong>Tamaños:</strong> 'medium' es ideal para web, 'large' para impresión</li>";
echo "<li><strong>Columnas:</strong> 3-4 columnas funcionan mejor en desktop, 1-2 en móvil</li>";
echo "<li><strong>Lightbox:</strong> Siempre recomendado para mejor experiencia</li>";
echo "</ul>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>🔧 Personalización Avanzada:</h4>";
echo "<div class=\"code\">[soloylibre_gallery style=\"dominican\" ids=\"175,174,173,172,171\" columns=\"3\" size=\"large\" lightbox=\"true\" show_metadata=\"true\" show_title=\"true\" hover_effect=\"zoom\" border_radius=\"15\" spacing=\"20\" animation=\"fadeIn\" photographer_signature=\"true\"]</div>";
echo "<p><strong>Resultado:</strong> Galería completamente personalizada con todos los parámetros ajustados.</p>";
echo "</div>";

echo "<div class=\"success\">";
echo "<h4>✅ Mejores Prácticas:</h4>";
echo "<ul>";
echo "<li>Siempre incluye el atributo <code>ids</code></li>";
echo "<li>Usa <code>photographer_signature=\"true\"</code> para branding</li>";
echo "<li>Habilita <code>lightbox=\"true\"</code> para mejor UX</li>";
echo "<li>Ajusta <code>columns</code> según el dispositivo objetivo</li>";
echo "<li>Usa <code>show_metadata=\"true\"</code> para galerías técnicas</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

// Solución de problemas
echo "<div class=\"section\" id=\"troubleshooting\">";
echo "<h2>🔧 Solución de Problemas</h2>";

echo "<div class=\"example\">";
echo "<h4>❌ Problema: \"No se especificaron IDs de imágenes\"</h4>";
echo "<p><strong>Solución:</strong> Asegúrate de incluir el atributo <code>ids</code> con valores válidos.</p>";
echo "<div class=\"code\">✅ Correcto: [soloylibre_gallery ids=\"175,174,173\"]<br>❌ Incorrecto: [soloylibre_gallery]</div>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>❌ Problema: \"IDs de imágenes no válidos\"</h4>";
echo "<p><strong>Solución:</strong> Verifica que los IDs existan en tu biblioteca de medios.</p>";
echo "<div class=\"code\">✅ Correcto: [soloylibre_gallery ids=\"175,174,173\"]<br>❌ Incorrecto: [soloylibre_gallery ids=\"abc,def,ghi\"]</div>";
echo "</div>";

echo "<div class=\"example\">";
echo "<h4>❌ Problema: Galería no se muestra correctamente</h4>";
echo "<p><strong>Soluciones:</strong></p>";
echo "<ul>";
echo "<li>Verifica que el plugin esté activado</li>";
echo "<li>Comprueba que las imágenes existan</li>";
echo "<li>Revisa la sintaxis del shortcode</li>";
echo "<li>Asegúrate de que no haya espacios extra</li>";
echo "</ul>";
echo "</div>";

echo "<div class=\"highlight\">";
echo "<h4>🆘 Soporte Técnico:</h4>";
echo "<p>Si necesitas ayuda adicional, contacta:</p>";
echo "<ul>";
echo "<li><strong>📧 Email:</strong> <EMAIL></li>";
echo "<li><strong>📱 Teléfono:</strong> ************</li>";
echo "<li><strong>👨‍💻 Desarrollador:</strong> JEYKO AI</li>";
echo "<li><strong>📸 Fotógrafo:</strong> Jose L Encarnacion (JoseTusabe)</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

// Enlaces útiles
echo "<h2>🔗 Enlaces Útiles</h2>";
echo "<div style=\"text-align: center;\">";
echo "<a href=\"auto-login-soloylibre.php\" class=\"btn\">🔑 Probar Plugin</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-wizard\" class=\"btn\">🧙‍♂️ Crear Galería</a>";
echo "<a href=\"test-sistema-mejorado.php\" class=\"btn\">🧪 Verificar Sistema</a>";
echo "<a href=\"monitor-errores.php\" class=\"btn\">📊 Monitor</a>";
echo "</div>";

// Footer
echo "<hr>";
echo "<div style=\"background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 25px; border-radius: 15px; text-align: center;\">";
echo "<h3>📞 SoloYLibre Photography</h3>";
echo "<h4>👨‍💻 Jose L Encarnacion (JoseTusabe)</h4>";
echo "<p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
echo "<p>📞 ************ | 📧 <EMAIL></p>";
echo "<div style=\"margin-top: 15px;\">";
echo "<span style=\"margin: 0 10px;\">🌐 josetusabe.com</span>";
echo "<span style=\"margin: 0 10px;\">🌐 soloylibre.com</span>";
echo "<span style=\"margin: 0 10px;\">📸 1and1photo.com</span>";
echo "<span style=\"margin: 0 10px;\">👨‍💼 joselencarnacion.com</span>";
echo "</div>";
echo "<p style=\"margin-top: 15px; font-style: italic;\">\"Capturando la belleza de República Dominicana, una foto a la vez\" 🇩🇴</p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
