<?php
// Página de descarga SoloYLibre Gallery Pro v4.0.0
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>📦 Descarga - SoloYLibre Gallery Pro v4.0.0</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); }";
echo ".container { background: white; padding: 40px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); max-width: 800px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; }";
echo ".download-section { background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 20px 0; border: 2px solid #28a745; }";
echo ".btn { background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; display: inline-block; margin: 10px 5px; font-weight: 600; font-size: 16px; transition: all 0.3s ease; }";
echo ".btn:hover { background: #218838; transform: translateY(-2px); }";
echo ".btn-primary { background: #CE1126; }";
echo ".btn-primary:hover { background: #002D62; }";
echo ".feature-list { background: white; padding: 20px; border-radius: 8px; margin: 15px 0; }";
echo ".version-info { background: #d1ecf1; padding: 20px; border-radius: 8px; border-left: 4px solid #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class=\"container\">";
echo "<div class=\"header\">";
echo "<h1>📦 SoloYLibre Gallery Pro v4.0.0</h1>";
echo "<p>🇩🇴 Plugin Profesional de Galerías para WordPress</p>";
echo "<p>Jose L Encarnacion (JoseTusabe) - SoloYLibre Photography</p>";
echo "<p>⏰ Actualizado: " . date("Y-m-d H:i:s") . "</p>";
echo "</div>";

echo "<div class=\"version-info\">";
echo "<h3>🎉 Nueva Versión 4.0.0 - ACTUALIZACIÓN MAYOR</h3>";
echo "<p><strong>Fecha de lanzamiento:</strong> " . date("Y-m-d") . "</p>";
echo "<p><strong>Tamaño del archivo:</strong> ";
if (file_exists("soloylibre-gallery-pro-v4.0.0-2025-07-06-09-51-36.zip")) {
    echo round(filesize("soloylibre-gallery-pro-v4.0.0-2025-07-06-09-51-36.zip") / 1024, 2) . " KB";
} else {
    echo "Calculando...";
}
echo "</p>";
echo "<p><strong>Compatibilidad:</strong> WordPress 5.0+ | PHP 7.4+</p>";
echo "</div>";

echo "<div class=\"download-section\">";
echo "<h2>⬇️ Descarga Directa</h2>";
echo "<p><strong>Archivo:</strong> soloylibre-gallery-pro-v4.0.0-2025-07-06-09-51-36.zip</p>";
echo "<div style=\"text-align: center;\">";
if (file_exists("soloylibre-gallery-pro-v4.0.0-2025-07-06-09-51-36.zip")) {
    echo "<a href=\"soloylibre-gallery-pro-v4.0.0-2025-07-06-09-51-36.zip\" class=\"btn\" download>📥 Descargar Plugin v4.0.0</a>";
} else {
    echo "<p style=\"color: #dc3545;\">❌ Archivo no disponible</p>";
}
echo "</div>";
echo "</div>";

echo "<div class=\"feature-list\">";
echo "<h3>✨ Nuevas Características v4.0.0</h3>";
echo "<ul>";
echo "<li>✅ <strong>Carga automática de 300 fotos</strong> en el wizard</li>";
echo "<li>✅ <strong>API Manager eliminado</strong> (sin errores)</li>";
echo "<li>✅ <strong>UI completamente rediseñada</strong> y simplificada</li>";
echo "<li>✅ <strong>Menú de WordPress limpio</strong> (solo 2 opciones)</li>";
echo "<li>✅ <strong>Selección avanzada de fotos</strong> con drag & drop</li>";
echo "<li>✅ <strong>Sistema más estable</strong> sin dependencias de API</li>";
echo "<li>✅ <strong>Agregar/quitar fotos</strong> fácilmente</li>";
echo "<li>✅ <strong>Grid responsivo</strong> con hover effects</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🚀 Instrucciones de Instalación</h3>";
echo "<ol>";
echo "<li>Descargar el archivo ZIP</li>";
echo "<li>Ir a WordPress Admin → Plugins → Añadir nuevo</li>";
echo "<li>Hacer clic en \"Subir plugin\"</li>";
echo "<li>Seleccionar el archivo descargado</li>";
echo "<li>Activar el plugin</li>";
echo "<li>Ir a <strong>📸 SoloYLibre</strong> en el menú</li>";
echo "</ol>";

echo "<h3>🔗 Enlaces Útiles</h3>";
echo "<div style=\"text-align: center;\">";
echo "<a href=\"auto-login-soloylibre.php\" class=\"btn btn-primary\">🔑 Probar Plugin</a>";
echo "<a href=\"test-sistema-mejorado.php\" class=\"btn btn-primary\">🧪 Ver Mejoras</a>";
echo "<a href=\"monitor-errores.php\" class=\"btn btn-primary\">📊 Monitor</a>";
echo "</div>";

echo "<hr>";
echo "<div style=\"background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 25px; border-radius: 15px; text-align: center;\">";
echo "<h3>📞 SoloYLibre Photography</h3>";
echo "<h4>👨‍💻 Jose L Encarnacion (JoseTusabe)</h4>";
echo "<p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
echo "<p>📞 718-713-5500 | 📧 <EMAIL></p>";
echo "<div style=\"margin-top: 15px;\">";
echo "<span style=\"margin: 0 10px;\">🌐 josetusabe.com</span>";
echo "<span style=\"margin: 0 10px;\">🌐 soloylibre.com</span>";
echo "<span style=\"margin: 0 10px;\">📸 1and1photo.com</span>";
echo "<span style=\"margin: 0 10px;\">👨‍💼 joselencarnacion.com</span>";
echo "</div>";
echo "<p style=\"margin-top: 15px; font-style: italic;\">\"Capturando la belleza de República Dominicana, una foto a la vez\" 🇩🇴</p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>