<?php
// Verificación final del API Manager
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🔧 Verificación API Manager - SoloYLibre</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); }";
echo ".container { background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); max-width: 900px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 30px; text-align: center; }";
echo ".test-section { margin: 20px 0; padding: 20px; border-radius: 10px; }";
echo ".success { background: #d4edda; border: 2px solid #c3e6cb; color: #155724; }";
echo ".error { background: #f8d7da; border: 2px solid #f5c6cb; color: #721c24; }";
echo ".warning { background: #fff3cd; border: 2px solid #ffeaa7; color: #856404; }";
echo ".info { background: #d1ecf1; border: 2px solid #bee5eb; color: #0c5460; }";
echo ".btn { background: #CE1126; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; display: inline-block; margin: 10px 5px; font-weight: 600; transition: all 0.3s ease; }";
echo ".btn:hover { background: #002D62; transform: translateY(-2px); }";
echo ".code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class=\"container\">";
echo "<div class=\"header\">";
echo "<h1>🔧 Verificación Final - API Manager</h1>";
echo "<p>🇩🇴 SoloYLibre Gallery Pro - Jose L Encarnacion (JoseTusabe)</p>";
echo "<p>⏰ " . date("Y-m-d H:i:s") . "</p>";
echo "</div>";

$all_tests_passed = true;

// Test 1: WordPress Loading
echo "<div class=\"test-section";
try {
    require_once("wp-load.php");
    echo " success\">";
    echo "<h3>✅ Test 1: WordPress Core</h3>";
    echo "<p><strong>Estado:</strong> Cargado correctamente</p>";
    echo "<p><strong>Versión:</strong> " . get_bloginfo("version") . "</p>";
    $wp_loaded = true;
} catch (Exception $e) {
    echo " error\">";
    echo "<h3>❌ Test 1: WordPress Core</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    $wp_loaded = false;
    $all_tests_passed = false;
}
echo "</div>";

if ($wp_loaded) {
    // Test 2: Plugin Status
    echo "<div class=\"test-section";
    $active_plugins = get_option("active_plugins", array());
    $plugin_active = false;
    foreach ($active_plugins as $plugin) {
        if (strpos($plugin, "Archive") !== false) {
            $plugin_active = true;
            break;
        }
    }
    
    if ($plugin_active) {
        echo " success\">";
        echo "<h3>✅ Test 2: Plugin SoloYLibre</h3>";
        echo "<p><strong>Estado:</strong> Activo y funcionando</p>";
    } else {
        echo " error\">";
        echo "<h3>❌ Test 2: Plugin SoloYLibre</h3>";
        echo "<p><strong>Estado:</strong> No está activo</p>";
        $all_tests_passed = false;
    }
    echo "</div>";
    
    // Test 3: API Manager Class
    echo "<div class=\"test-section";
    if (class_exists("SoloYLibre_API_Manager")) {
        echo " success\">";
        echo "<h3>✅ Test 3: API Manager</h3>";
        echo "<p><strong>Clase:</strong> SoloYLibre_API_Manager cargada</p>";
        echo "<p><strong>Namespace:</strong> soloylibre/v1</p>";
        
        // Try to instantiate (carefully)
        try {
            $reflection = new ReflectionClass("SoloYLibre_API_Manager");
            $methods = $reflection->getMethods(ReflectionMethod::IS_PRIVATE);
            $route_methods = array();
            foreach ($methods as $method) {
                if (strpos($method->getName(), "register_") === 0 && strpos($method->getName(), "_routes") !== false) {
                    $route_methods[] = $method->getName();
                }
            }
            echo "<p><strong>Métodos de rutas:</strong> " . implode(", ", $route_methods) . "</p>";
        } catch (Exception $e) {
            echo "<p><strong>Advertencia:</strong> No se pudo analizar la clase</p>";
        }
        
    } else {
        echo " error\">";
        echo "<h3>❌ Test 3: API Manager</h3>";
        echo "<p><strong>Error:</strong> Clase SoloYLibre_API_Manager no encontrada</p>";
        $all_tests_passed = false;
    }
    echo "</div>";
    
    // Test 4: REST API Endpoints
    echo "<div class=\"test-section";
    $rest_server = rest_get_server();
    if ($rest_server) {
        echo " success\">";
        echo "<h3>✅ Test 4: REST API</h3>";
        echo "<p><strong>Servidor REST:</strong> Disponible</p>";
        echo "<p><strong>URL Base:</strong> " . rest_url() . "</p>";
        echo "<p><strong>Namespace SoloYLibre:</strong> " . rest_url("soloylibre/v1/") . "</p>";
    } else {
        echo " warning\">";
        echo "<h3>⚠️ Test 4: REST API</h3>";
        echo "<p><strong>Estado:</strong> Servidor REST no disponible</p>";
    }
    echo "</div>";
    
    // Test 5: Error Logs
    echo "<div class=\"test-section";
    $error_files = array("wp-content/debug.log", "error_log");
    $errors_found = false;
    $error_content = "";
    
    foreach ($error_files as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if (!empty(trim($content))) {
                $errors_found = true;
                $error_content .= "Archivo: $file\n" . substr($content, -300) . "\n\n";
            }
        }
    }
    
    if (!$errors_found) {
        echo " success\">";
        echo "<h3>✅ Test 5: Logs de Errores</h3>";
        echo "<p><strong>Estado:</strong> Sin errores registrados</p>";
    } else {
        echo " error\">";
        echo "<h3>❌ Test 5: Logs de Errores</h3>";
        echo "<p><strong>Errores encontrados:</strong></p>";
        echo "<div class=\"code\">" . htmlspecialchars($error_content) . "</div>";
        $all_tests_passed = false;
    }
    echo "</div>";
}

// Summary
echo "<div class=\"test-section";
if ($all_tests_passed) {
    echo " success\">";
    echo "<h2>🎉 RESUMEN: TODOS LOS TESTS PASARON</h2>";
    echo "<p><strong>Estado:</strong> API Manager funcionando correctamente</p>";
    echo "<p><strong>Plugin:</strong> Completamente operativo</p>";
    echo "<p><strong>Errores:</strong> Ninguno detectado</p>";
} else {
    echo " error\">";
    echo "<h2>⚠️ RESUMEN: ALGUNOS TESTS FALLARON</h2>";
    echo "<p><strong>Estado:</strong> Requiere atención</p>";
    echo "<p><strong>Acción:</strong> Revisar errores anteriores</p>";
}
echo "</div>";

// Action Links
echo "<h2>🔗 Acciones Disponibles</h2>";
echo "<div style=\"text-align: center;\">";
echo "<a href=\"auto-login-soloylibre.php\" class=\"btn\">🔑 Login Automático</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-simple-wizard\" class=\"btn\">🧙‍♂️ Wizard</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-statistics\" class=\"btn\">📊 Estadísticas</a>";
echo "<a href=\"monitor-errores.php\" class=\"btn\">📊 Monitor</a>";
echo "</div>";

// Contact
echo "<hr>";
echo "<div style=\"background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 25px; border-radius: 15px; text-align: center;\">";
echo "<h3>📞 SoloYLibre Photography</h3>";
echo "<h4>👨‍💻 Jose L Encarnacion (JoseTusabe)</h4>";
echo "<p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
echo "<p>📞 718-713-5500 | 📧 <EMAIL></p>";
echo "<div style=\"margin-top: 15px;\">";
echo "<span style=\"margin: 0 10px;\">🌐 josetusabe.com</span>";
echo "<span style=\"margin: 0 10px;\">🌐 soloylibre.com</span>";
echo "<span style=\"margin: 0 10px;\">📸 1and1photo.com</span>";
echo "<span style=\"margin: 0 10px;\">👨‍💼 joselencarnacion.com</span>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>