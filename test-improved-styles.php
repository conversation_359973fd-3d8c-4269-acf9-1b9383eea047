<?php
/**
 * Test Improved Styles with <PERSON><PERSON> Default
 * Prueba de estilos mejorados con Masonry por defecto
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

require_once('wp-load.php');

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🎨 Estilos Mejorados - SoloYLibre Gallery Pro</title>";
echo "<meta charset=\"UTF-8\">";
echo "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">";

wp_head();

echo "<style>";
echo "body { margin: 0; padding: 20px; background: #f8f9fa; color: #333; font-family: Arial, sans-serif; }";
echo ".container { max-width: 1400px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }";
echo ".demo-section { background: white; border-radius: 15px; padding: 25px; margin: 20px 0; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }";
echo ".demo-title { color: #CE1126; font-size: 1.5rem; margin-bottom: 15px; }";
echo ".shortcode-display { background: #2d3748; color: #e2e8f0; padding: 12px; border-radius: 8px; font-family: monospace; margin: 10px 0; font-size: 0.9rem; }";
echo ".style-comparison { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }";
echo ".style-card { background: #f8f9fa; padding: 20px; border-radius: 10px; border: 2px solid #e9ecef; }";
echo ".style-card.default { border-color: #CE1126; background: #fff5f5; }";
echo ".success { color: #28a745; }";
echo ".info { color: #17a2b8; }";
echo ".warning { color: #ffc107; }";
echo ".default-badge { background: #CE1126; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8rem; font-weight: bold; }";
echo "</style>";

echo "<script src=\"https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js\"></script>";

echo "</head>";
echo "<body>";

echo "<div class=\"container\">";

echo "<div class=\"header\">";
echo "<h1>🎨 Estilos Mejorados y Rediseñados</h1>";
echo "<h2>SoloYLibre Gallery Pro v5.0.8</h2>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe) - Masonry por defecto, estilos elegantes</p>";
echo "</div>";

// Obtener fotos de ejemplo
$all_photos = get_posts(array(
    'post_type' => 'attachment',
    'post_mime_type' => 'image',
    'posts_per_page' => 12,
    'post_status' => 'inherit'
));

if (!empty($all_photos)) {
    $photo_ids = implode(',', wp_list_pluck($all_photos, 'ID'));
    
    // 1. Masonry por defecto
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">🧱 Masonry - <span class=\"default-badge\">POR DEFECTO</span></h2>";
    echo "<p class=\"success\">✅ Ahora es el estilo por defecto - perfecto para todas las orientaciones</p>";
    echo "<p class=\"info\">📐 Se adapta automáticamente a fotos portrait, landscape y square</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery] <!-- Masonry por defecto --></div>";
    
    echo do_shortcode("[soloylibre_gallery ids=\"$photo_ids\" columns=\"4\"]");
    echo "</div>";
    
    // 2. Comparación de estilos mejorados
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">🎨 Todos los Estilos Rediseñados</h2>";
    
    echo "<div class=\"style-comparison\">";
    
    // Masonry (defecto)
    echo "<div class=\"style-card default\">";
    echo "<h4>🧱 Masonry <span class=\"default-badge\">DEFECTO</span></h4>";
    echo "<ul>";
    echo "<li>✅ Fondo blanco limpio</li>";
    echo "<li>✅ Sombras suaves</li>";
    echo "<li>✅ Perfecto para orientaciones mixtas</li>";
    echo "<li>✅ Layout tipo Pinterest</li>";
    echo "</ul>";
    echo "</div>";
    
    // Grid
    echo "<div class=\"style-card\">";
    echo "<h4>📱 Grid Moderno</h4>";
    echo "<ul>";
    echo "<li>✅ Fondo gris claro elegante</li>";
    echo "<li>✅ Bordes redondeados</li>";
    echo "<li>✅ Hover sutil</li>";
    echo "<li>✅ Layout uniforme</li>";
    echo "</ul>";
    echo "</div>";
    
    // Carousel
    echo "<div class=\"style-card\">";
    echo "<h4>🎠 Carousel</h4>";
    echo "<ul>";
    echo "<li>✅ Gradiente azul elegante</li>";
    echo "<li>✅ Scroll horizontal suave</li>";
    echo "<li>✅ Scrollbar personalizado</li>";
    echo "<li>✅ Efectos de escala</li>";
    echo "</ul>";
    echo "</div>";
    
    // Profesional
    echo "<div class=\"style-card\">";
    echo "<h4>💼 Profesional</h4>";
    echo "<ul>";
    echo "<li>✅ Fondo blanco minimalista</li>";
    echo "<li>✅ Bordes con acento dominicano</li>";
    echo "<li>✅ Tipografía serif elegante</li>";
    echo "<li>✅ Hover con color patriótico</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // 3. Ejemplos de cada estilo
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">📱 Grid Moderno - Rediseñado</h2>";
    echo "<p class=\"info\">Fondo gris claro, sombras suaves, hover elegante</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"grid\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"grid\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\"]");
    echo "</div>";
    
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">🎠 Carousel - Horizontal Elegante</h2>";
    echo "<p class=\"info\">Gradiente azul, scroll suave, scrollbar personalizado</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"carousel\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 8)) . "\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"carousel\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 8)) . "\"]");
    echo "</div>";
    
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">💼 Profesional - Minimalista</h2>";
    echo "<p class=\"info\">Fondo blanco, bordes con acento dominicano, tipografía serif</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"professional\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"professional\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\"]");
    echo "</div>";
    
} else {
    echo "<div class=\"demo-section\">";
    echo "<h2 class=\"demo-title\">❌ No hay fotos disponibles</h2>";
    echo "<p>Sube algunas imágenes a la biblioteca de medios para probar los estilos.</p>";
    echo "</div>";
}

// Colores personalizables
echo "<div class=\"demo-section\">";
echo "<h2 class=\"demo-title\">🎨 Colores de Fondo Personalizables</h2>";

if (!empty($all_photos)) {
    echo "<h4>🔵 Fondo Azul Personalizado:</h4>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"masonry\" background_color=\"#e3f2fd\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\"]</div>";
    echo do_shortcode("[soloylibre_gallery style=\"masonry\" background_color=\"#e3f2fd\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\"]");
    
    echo "<h4>🌅 Gradiente Personalizado:</h4>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"grid\" background_gradient=\"linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 6, 6)) . "\" columns=\"3\"]</div>";
    echo do_shortcode("[soloylibre_gallery style=\"grid\" background_gradient=\"linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 6, 6)) . "\" columns=\"3\"]");
}

echo "</div>";

// Información técnica
echo "<div class=\"demo-section\">";
echo "<h2 class=\"demo-title\">🔧 Mejoras Implementadas</h2>";

echo "<div style=\"display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;\">";

echo "<div>";
echo "<h4>🧱 Masonry (Nuevo Defecto)</h4>";
echo "<ul>";
echo "<li>✅ Fondo blanco limpio (#ffffff)</li>";
echo "<li>✅ Sombras suaves y elegantes</li>";
echo "<li>✅ Bordes redondeados (12px)</li>";
echo "<li>✅ Hover con elevación sutil</li>";
echo "<li>✅ Perfecto para todas las orientaciones</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>📱 Grid Rediseñado</h4>";
echo "<ul>";
echo "<li>✅ Fondo gris claro (#f8f9fa)</li>";
echo "<li>✅ Eliminado gradiente llamativo</li>";
echo "<li>✅ Sombras más sutiles</li>";
echo "<li>✅ Hover menos agresivo</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>🎠 Carousel Mejorado</h4>";
echo "<ul>";
echo "<li>✅ Gradiente azul elegante</li>";
echo "<li>✅ Scrollbar personalizado</li>";
echo "<li>✅ Elementos más pequeños (320px)</li>";
echo "<li>✅ Hover con escala suave</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>💼 Profesional Minimalista</h4>";
echo "<ul>";
echo "<li>✅ Fondo blanco puro</li>";
echo "<li>✅ Bordes con acento dominicano</li>";
echo "<li>✅ Tipografía serif (Georgia)</li>";
echo "<li>✅ Hover con color patriótico</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>🎨 Colores Personalizables</h4>";
echo "<ul>";
echo "<li>✅ <code>background_color</code>: Color sólido</li>";
echo "<li>✅ <code>background_gradient</code>: Gradiente CSS</li>";
echo "<li>✅ Variables CSS personalizadas</li>";
echo "<li>✅ Compatible con todos los estilos</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>🔧 Cambios Técnicos</h4>";
echo "<ul>";
echo "<li>✅ Masonry como estilo por defecto</li>";
echo "<li>✅ CSS más limpio y elegante</li>";
echo "<li>✅ Colores menos saturados</li>";
echo "<li>✅ Efectos más sutiles</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "</div>";

echo "</div>"; // container

// JavaScript
echo "<script>";
echo "console.log('🎨 SoloYLibre: Estilos mejorados cargados');";

echo "// Confetti de celebración";
echo "setTimeout(function() {";
echo "    if (typeof confetti !== 'undefined') {";
echo "        confetti({";
echo "            particleCount: 100,";
echo "            spread: 70,";
echo "            origin: { y: 0.6 },";
echo "            colors: ['#CE1126', '#002D62', '#FFD700']";
echo "        });";
echo "    }";
echo "}, 1000);";

echo "</script>";

wp_footer();

echo "</body>";
echo "</html>";
?>
