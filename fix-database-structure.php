<?php
/**
 * Fix SoloYLibre Database Structure
 * Crea las tablas y columnas faltantes para el plugin
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

echo "🗄️ REPARANDO ESTRUCTURA DE BASE DE DATOS - SOLOYLIBRE\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Configuración de la base de datos
$host = 'localhost';
$username = 'root';
$password = 'root';
$database = 'wp';

try {
    $mysqli = new mysqli($host, $username, $password, $database);
    
    if ($mysqli->connect_error) {
        die("❌ Error de conexión: " . $mysqli->connect_error);
    }
    
    echo "✅ Conectado a la base de datos\n\n";
    
    // 1. Verificar tablas existentes
    echo "1. 📋 VERIFICANDO TABLAS EXISTENTES...\n";
    
    $required_tables = array(
        'wp_soloylibre_albums',
        'wp_soloylibre_photos', 
        'wp_soloylibre_interactions',
        'wp_soloylibre_photo_meta',
        'wp_soloylibre_user_interactions',
        'wp_soloylibre_projects',
        'wp_soloylibre_photographer_settings'
    );
    
    $existing_tables = array();
    foreach ($required_tables as $table) {
        $result = $mysqli->query("SHOW TABLES LIKE '$table'");
        $exists = $result && $result->num_rows > 0;
        $existing_tables[$table] = $exists;
        echo "   " . ($exists ? "✅" : "❌") . " $table\n";
    }
    
    // 2. Crear tablas faltantes
    echo "\n2. 🔧 CREANDO TABLAS FALTANTES...\n";
    
    // Tabla de álbumes
    if (!$existing_tables['wp_soloylibre_albums']) {
        $create_albums = "
        CREATE TABLE wp_soloylibre_albums (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            cover_photo_id int(11),
            state enum('public', 'private', 'draft') DEFAULT 'public',
            is_published tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            photographer_id int(11),
            sort_order int(11) DEFAULT 0,
            PRIMARY KEY (id),
            KEY idx_state (state),
            KEY idx_published (is_published),
            KEY idx_photographer (photographer_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($mysqli->query($create_albums)) {
            echo "   ✅ Tabla wp_soloylibre_albums creada\n";
        } else {
            echo "   ❌ Error creando wp_soloylibre_albums: " . $mysqli->error . "\n";
        }
    } else {
        // Verificar y agregar columna 'state' si no existe
        $check_state = $mysqli->query("SHOW COLUMNS FROM wp_soloylibre_albums LIKE 'state'");
        if (!$check_state || $check_state->num_rows == 0) {
            $add_state = "ALTER TABLE wp_soloylibre_albums ADD COLUMN state enum('public', 'private', 'draft') DEFAULT 'public' AFTER description";
            if ($mysqli->query($add_state)) {
                echo "   ✅ Columna 'state' agregada a wp_soloylibre_albums\n";
            } else {
                echo "   ❌ Error agregando columna 'state': " . $mysqli->error . "\n";
            }
        }
    }
    
    // Tabla de fotos
    if (!$existing_tables['wp_soloylibre_photos']) {
        $create_photos = "
        CREATE TABLE wp_soloylibre_photos (
            id int(11) NOT NULL AUTO_INCREMENT,
            post_id int(11) NOT NULL,
            album_id int(11),
            title varchar(255),
            description text,
            file_path varchar(500),
            file_url varchar(500),
            thumbnail_url varchar(500),
            state enum('public', 'private', 'draft') DEFAULT 'public',
            views_count int(11) DEFAULT 0,
            likes_count int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            photographer_id int(11),
            sort_order int(11) DEFAULT 0,
            metadata json,
            PRIMARY KEY (id),
            KEY idx_post_id (post_id),
            KEY idx_album_id (album_id),
            KEY idx_state (state),
            KEY idx_photographer (photographer_id),
            FOREIGN KEY (post_id) REFERENCES wp_posts(ID) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($mysqli->query($create_photos)) {
            echo "   ✅ Tabla wp_soloylibre_photos creada\n";
        } else {
            echo "   ❌ Error creando wp_soloylibre_photos: " . $mysqli->error . "\n";
        }
    }
    
    // Tabla de interacciones
    if (!$existing_tables['wp_soloylibre_interactions']) {
        $create_interactions = "
        CREATE TABLE wp_soloylibre_interactions (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11),
            photo_id int(11),
            album_id int(11),
            interaction_type enum('view', 'like', 'comment', 'share') NOT NULL,
            ip_address varchar(45),
            user_agent text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            metadata json,
            PRIMARY KEY (id),
            KEY idx_user_id (user_id),
            KEY idx_photo_id (photo_id),
            KEY idx_album_id (album_id),
            KEY idx_type (interaction_type),
            KEY idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($mysqli->query($create_interactions)) {
            echo "   ✅ Tabla wp_soloylibre_interactions creada\n";
        } else {
            echo "   ❌ Error creando wp_soloylibre_interactions: " . $mysqli->error . "\n";
        }
    }
    
    // Tabla de metadatos de fotos
    if (!$existing_tables['wp_soloylibre_photo_meta']) {
        $create_photo_meta = "
        CREATE TABLE wp_soloylibre_photo_meta (
            id int(11) NOT NULL AUTO_INCREMENT,
            photo_id int(11) NOT NULL,
            meta_key varchar(255) NOT NULL,
            meta_value longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_photo_id (photo_id),
            KEY idx_meta_key (meta_key),
            UNIQUE KEY unique_photo_meta (photo_id, meta_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($mysqli->query($create_photo_meta)) {
            echo "   ✅ Tabla wp_soloylibre_photo_meta creada\n";
        } else {
            echo "   ❌ Error creando wp_soloylibre_photo_meta: " . $mysqli->error . "\n";
        }
    }
    
    // Tabla de interacciones de usuario
    if (!$existing_tables['wp_soloylibre_user_interactions']) {
        $create_user_interactions = "
        CREATE TABLE wp_soloylibre_user_interactions (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11),
            session_id varchar(255),
            content_type enum('photo', 'album', 'gallery') NOT NULL,
            content_id int(11) NOT NULL,
            action_type enum('view', 'like', 'download', 'share') NOT NULL,
            ip_address varchar(45),
            user_agent text,
            referrer varchar(500),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_user_id (user_id),
            KEY idx_content (content_type, content_id),
            KEY idx_action (action_type),
            KEY idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($mysqli->query($create_user_interactions)) {
            echo "   ✅ Tabla wp_soloylibre_user_interactions creada\n";
        } else {
            echo "   ❌ Error creando wp_soloylibre_user_interactions: " . $mysqli->error . "\n";
        }
    }
    
    // Tabla de proyectos
    if (!$existing_tables['wp_soloylibre_projects']) {
        $create_projects = "
        CREATE TABLE wp_soloylibre_projects (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            status enum('active', 'completed', 'archived') DEFAULT 'active',
            start_date date,
            end_date date,
            photographer_id int(11),
            client_name varchar(255),
            client_email varchar(255),
            budget decimal(10,2),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_status (status),
            KEY idx_photographer (photographer_id),
            KEY idx_dates (start_date, end_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($mysqli->query($create_projects)) {
            echo "   ✅ Tabla wp_soloylibre_projects creada\n";
        } else {
            echo "   ❌ Error creando wp_soloylibre_projects: " . $mysqli->error . "\n";
        }
    }
    
    // Tabla de configuraciones del fotógrafo
    if (!$existing_tables['wp_soloylibre_photographer_settings']) {
        $create_photographer_settings = "
        CREATE TABLE wp_soloylibre_photographer_settings (
            id int(11) NOT NULL AUTO_INCREMENT,
            photographer_id int(11) NOT NULL,
            setting_key varchar(255) NOT NULL,
            setting_value longtext,
            setting_type enum('string', 'number', 'boolean', 'json', 'array') DEFAULT 'string',
            is_public tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_photographer (photographer_id),
            KEY idx_setting_key (setting_key),
            UNIQUE KEY unique_photographer_setting (photographer_id, setting_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($mysqli->query($create_photographer_settings)) {
            echo "   ✅ Tabla wp_soloylibre_photographer_settings creada\n";
        } else {
            echo "   ❌ Error creando wp_soloylibre_photographer_settings: " . $mysqli->error . "\n";
        }
    }
    
    // 3. Insertar datos de ejemplo
    echo "\n3. 📊 INSERTANDO DATOS DE EJEMPLO...\n";
    
    // Álbumes de ejemplo
    $sample_albums = array(
        array('Paisajes Dominicanos', 'Hermosos paisajes de República Dominicana', 'public'),
        array('Retratos Profesionales', 'Sesiones de retratos profesionales', 'public'),
        array('Eventos y Celebraciones', 'Fotografía de eventos especiales', 'public'),
        array('Arquitectura Colonial', 'Arquitectura histórica dominicana', 'public'),
        array('Naturaleza y Vida Silvestre', 'Flora y fauna dominicana', 'public')
    );
    
    foreach ($sample_albums as $album) {
        $check_album = $mysqli->query("SELECT id FROM wp_soloylibre_albums WHERE name = '" . $mysqli->real_escape_string($album[0]) . "'");
        if (!$check_album || $check_album->num_rows == 0) {
            $insert_album = "INSERT INTO wp_soloylibre_albums (name, description, state, photographer_id) VALUES (?, ?, ?, 1)";
            $stmt = $mysqli->prepare($insert_album);
            $stmt->bind_param('sss', $album[0], $album[1], $album[2]);
            
            if ($stmt->execute()) {
                echo "   ✅ Álbum creado: " . $album[0] . "\n";
            }
            $stmt->close();
        }
    }
    
    // Configuraciones del fotógrafo
    $photographer_settings = array(
        array('name', 'Jose L Encarnacion', 'string', 1),
        array('alias', 'JoseTusabe', 'string', 1),
        array('brand', 'SoloYLibre Photography', 'string', 1),
        array('email', '<EMAIL>', 'string', 1),
        array('phone', '************', 'string', 1),
        array('location', 'San José de Ocoa, República Dominicana / USA', 'string', 1),
        array('websites', '["josetusabe.com","soloylibre.com","1and1photo.com","joselencarnacion.com"]', 'json', 1),
        array('style_preference', 'dominican', 'string', 0),
        array('watermark_enabled', 'true', 'boolean', 0),
        array('auto_backup', 'true', 'boolean', 0)
    );
    
    foreach ($photographer_settings as $setting) {
        $check_setting = $mysqli->query("SELECT id FROM wp_soloylibre_photographer_settings WHERE photographer_id = 1 AND setting_key = '" . $mysqli->real_escape_string($setting[0]) . "'");
        if (!$check_setting || $check_setting->num_rows == 0) {
            $insert_setting = "INSERT INTO wp_soloylibre_photographer_settings (photographer_id, setting_key, setting_value, setting_type, is_public) VALUES (1, ?, ?, ?, ?)";
            $stmt = $mysqli->prepare($insert_setting);
            $stmt->bind_param('sssi', $setting[0], $setting[1], $setting[2], $setting[3]);
            
            if ($stmt->execute()) {
                echo "   ✅ Configuración creada: " . $setting[0] . "\n";
            }
            $stmt->close();
        }
    }
    
    // 4. Crear índices adicionales para optimización
    echo "\n4. 🚀 OPTIMIZANDO ÍNDICES...\n";
    
    $optimization_queries = array(
        "CREATE INDEX IF NOT EXISTS idx_albums_state_published ON wp_soloylibre_albums (state, is_published)",
        "CREATE INDEX IF NOT EXISTS idx_photos_album_state ON wp_soloylibre_photos (album_id, state)",
        "CREATE INDEX IF NOT EXISTS idx_interactions_date_type ON wp_soloylibre_interactions (created_at, interaction_type)",
        "CREATE INDEX IF NOT EXISTS idx_user_interactions_user_date ON wp_soloylibre_user_interactions (user_id, created_at)"
    );
    
    foreach ($optimization_queries as $query) {
        if ($mysqli->query($query)) {
            echo "   ✅ Índice optimizado\n";
        }
    }
    
    // 5. Verificar estructura final
    echo "\n5. ✅ VERIFICACIÓN FINAL...\n";
    
    foreach ($required_tables as $table) {
        $result = $mysqli->query("SHOW TABLES LIKE '$table'");
        $exists = $result && $result->num_rows > 0;
        echo "   " . ($exists ? "✅" : "❌") . " $table\n";
        
        if ($exists) {
            $count_result = $mysqli->query("SELECT COUNT(*) as count FROM $table");
            if ($count_result) {
                $count_row = $count_result->fetch_assoc();
                echo "      📊 Registros: " . $count_row['count'] . "\n";
            }
        }
    }
    
    $mysqli->close();
    
    echo "\n" . str_repeat("=", 70) . "\n";
    echo "✅ ESTRUCTURA DE BASE DE DATOS REPARADA EXITOSAMENTE\n";
    echo str_repeat("=", 70) . "\n";
    
    echo "\n📊 RESUMEN:\n";
    echo "   ✅ Todas las tablas requeridas creadas\n";
    echo "   ✅ Columnas faltantes agregadas\n";
    echo "   ✅ Datos de ejemplo insertados\n";
    echo "   ✅ Índices optimizados\n";
    echo "   ✅ Configuración del fotógrafo establecida\n";
    
    echo "\n🔗 PRÓXIMOS PASOS:\n";
    echo "   1. Probar el wizard SoloYLibre\n";
    echo "   2. Verificar estadísticas sin errores\n";
    echo "   3. Crear primera galería de prueba\n";
    
    echo "\n📞 SOPORTE:\n";
    echo "   📧 <EMAIL>\n";
    echo "   📱 ************\n";
    echo "   🇩🇴 Jose L Encarnacion (JoseTusabe)\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
