<?php
/**
 * Fix Database Structure for Interactions
 * Corregir estructura de base de datos para interacciones
 */

require_once('wp-load.php');

echo "🔧 DIAGNÓSTICO Y CORRECCIÓN DE BASE DE DATOS\n";
echo "=" . str_repeat("=", 50) . "\n\n";

global $wpdb;
$table_name = $wpdb->prefix . 'soloylibre_interactions';

// 1. Verificar si la tabla existe
echo "1. 🔍 VERIFICANDO EXISTENCIA DE TABLA...\n";
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo "   ✅ Tabla existe: $table_name\n";
    
    // 2. Verificar estructura actual
    echo "\n2. 📋 ESTRUCTURA ACTUAL DE LA TABLA...\n";
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    
    echo "   Columnas encontradas:\n";
    $has_interaction_count = false;
    foreach ($columns as $column) {
        echo "   - {$column->Field} ({$column->Type})\n";
        if ($column->Field === 'interaction_count') {
            $has_interaction_count = true;
        }
    }
    
    if (!$has_interaction_count) {
        echo "\n   ❌ PROBLEMA ENCONTRADO: Falta la columna 'interaction_count'\n";
        
        // 3. Agregar columna faltante
        echo "\n3. 🔧 AGREGANDO COLUMNA FALTANTE...\n";
        
        $alter_sql = "ALTER TABLE $table_name ADD COLUMN interaction_count INT(11) DEFAULT 1 AFTER interaction_type";
        $result = $wpdb->query($alter_sql);
        
        if ($result !== false) {
            echo "   ✅ Columna 'interaction_count' agregada exitosamente\n";
        } else {
            echo "   ❌ Error al agregar columna: " . $wpdb->last_error . "\n";
        }
        
    } else {
        echo "\n   ✅ La columna 'interaction_count' ya existe\n";
    }
    
} else {
    echo "   ❌ Tabla no existe, creando...\n";
    
    // Crear tabla completa
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        photo_id bigint(20) NOT NULL,
        interaction_type varchar(20) NOT NULL,
        interaction_count int(11) DEFAULT 1,
        user_ip varchar(45) DEFAULT NULL,
        user_agent text DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY photo_id (photo_id),
        KEY interaction_type (interaction_type),
        UNIQUE KEY unique_photo_interaction (photo_id, interaction_type)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    echo "   ✅ Tabla creada con estructura completa\n";
}

// Limpiar logs de errores
echo "\n4. 🧹 LIMPIANDO LOGS DE ERRORES...\n";
$log_files = array('wp-content/debug.log', 'error_log');
foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        file_put_contents($log_file, '');
        echo "   ✅ Limpiado: $log_file\n";
    }
}

echo "\n✅ CORRECCIÓN COMPLETADA\n";
?>
