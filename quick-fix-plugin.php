<?php
/**
 * Quick fix para el plugin SoloYLibre Gallery Pro
 * Desactiva temporalmente el plugin para diagnosticar errores
 */

// Cargar solo la configuración de WordPress
define('WP_USE_THEMES', false);
require_once('wp-config.php');

echo "🔧 QUICK FIX - PLUGIN SOLOYLIBRE GALLERY PRO\n\n";

// Conectar a la base de datos directamente
$mysqli = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);

if ($mysqli->connect_error) {
    die("Error de conexión: " . $mysqli->connect_error);
}

echo "✅ Conectado a la base de datos\n";

// Verificar plugins activos
$table_name = DB_NAME . '.wp_options';
$query = "SELECT option_value FROM $table_name WHERE option_name = 'active_plugins'";
$result = $mysqli->query($query);

if ($result) {
    $row = $result->fetch_assoc();
    $active_plugins = unserialize($row['option_value']);
    
    echo "📋 Plugins activos encontrados:\n";
    foreach ($active_plugins as $plugin) {
        echo "   - $plugin\n";
    }
    
    // Buscar el plugin SoloYLibre
    $soloylibre_plugin = null;
    foreach ($active_plugins as $key => $plugin) {
        if (strpos($plugin, 'soloylibre') !== false) {
            $soloylibre_plugin = $key;
            break;
        }
    }
    
    if ($soloylibre_plugin !== null) {
        echo "\n🎯 Plugin SoloYLibre encontrado: " . $active_plugins[$soloylibre_plugin] . "\n";
        
        // Desactivar temporalmente
        unset($active_plugins[$soloylibre_plugin]);
        $new_active_plugins = serialize(array_values($active_plugins));
        
        $update_query = "UPDATE $table_name SET option_value = ? WHERE option_name = 'active_plugins'";
        $stmt = $mysqli->prepare($update_query);
        $stmt->bind_param('s', $new_active_plugins);
        
        if ($stmt->execute()) {
            echo "✅ Plugin SoloYLibre desactivado temporalmente\n";
            echo "🌐 Prueba acceder a: http://localhost:8888/wp/wordpress/wp-admin/\n";
            echo "\n⚠️ Para reactivar el plugin, ejecuta este script de nuevo con el parámetro 'reactivate'\n";
        } else {
            echo "❌ Error al desactivar el plugin\n";
        }
        
        $stmt->close();
    } else {
        echo "\n❌ Plugin SoloYLibre no encontrado en la lista de plugins activos\n";
    }
} else {
    echo "❌ Error al consultar plugins activos\n";
}

// Verificar si se quiere reactivar
if (isset($argv[1]) && $argv[1] === 'reactivate') {
    echo "\n🔄 Reactivando plugin SoloYLibre...\n";
    
    // Obtener plugins activos actuales
    $result = $mysqli->query($query);
    if ($result) {
        $row = $result->fetch_assoc();
        $active_plugins = unserialize($row['option_value']);
        
        // Agregar el plugin SoloYLibre de vuelta
        $active_plugins[] = 'Archive/soloylibre-gallery-plugin.php';
        $new_active_plugins = serialize($active_plugins);
        
        $update_query = "UPDATE $table_name SET option_value = ? WHERE option_name = 'active_plugins'";
        $stmt = $mysqli->prepare($update_query);
        $stmt->bind_param('s', $new_active_plugins);
        
        if ($stmt->execute()) {
            echo "✅ Plugin SoloYLibre reactivado\n";
        } else {
            echo "❌ Error al reactivar el plugin\n";
        }
        
        $stmt->close();
    }
}

// Verificar errores en la base de datos
echo "\n🔍 Verificando configuración de WordPress...\n";

$wp_options_check = "SELECT option_name, option_value FROM $table_name WHERE option_name IN ('siteurl', 'home', 'template', 'stylesheet')";
$result = $mysqli->query($wp_options_check);

if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo "   " . $row['option_name'] . ": " . $row['option_value'] . "\n";
    }
} else {
    echo "❌ Error al verificar configuración de WordPress\n";
}

// Verificar si hay tablas de SoloYLibre que puedan estar causando problemas
echo "\n🗄️ Verificando tablas de SoloYLibre...\n";
$tables_query = "SHOW TABLES LIKE 'wp_soloylibre%'";
$result = $mysqli->query($tables_query);

if ($result) {
    $tables_found = array();
    while ($row = $result->fetch_array()) {
        $tables_found[] = $row[0];
    }
    
    if (!empty($tables_found)) {
        echo "📊 Tablas encontradas:\n";
        foreach ($tables_found as $table) {
            echo "   - $table\n";
            
            // Verificar si la tabla tiene datos
            $count_query = "SELECT COUNT(*) as count FROM $table";
            $count_result = $mysqli->query($count_query);
            if ($count_result) {
                $count_row = $count_result->fetch_assoc();
                echo "     Registros: " . $count_row['count'] . "\n";
            }
        }
    } else {
        echo "ℹ️ No se encontraron tablas de SoloYLibre\n";
    }
} else {
    echo "❌ Error al verificar tablas\n";
}

$mysqli->close();

echo "\n" . str_repeat("=", 50) . "\n";
echo "🎯 INSTRUCCIONES:\n";
echo "1. Accede a WordPress admin para verificar si funciona\n";
echo "2. Si funciona, el problema está en el plugin SoloYLibre\n";
echo "3. Para reactivar: php quick-fix-plugin.php reactivate\n";
echo "4. Si no funciona, el problema es más profundo\n";
echo "\n🔗 URLs para probar:\n";
echo "   Admin: http://localhost:8888/wp/wordpress/wp-admin/\n";
echo "   Sitio: http://localhost:8888/wp/wordpress/\n";

?>
