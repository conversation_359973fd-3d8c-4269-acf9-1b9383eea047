<?php
/**
 * Acceso directo a la base de datos para desactivar plugin problemático
 */

echo "🔧 ACCESO DIRECTO A BASE DE DATOS\n\n";

// Configuración de la base de datos (ajustar según tu configuración)
$host = 'localhost';
$username = 'root';
$password = 'root';
$database = 'wp';

try {
    $mysqli = new mysqli($host, $username, $password, $database);
    
    if ($mysqli->connect_error) {
        die("Error de conexión: " . $mysqli->connect_error);
    }
    
    echo "✅ Conectado a la base de datos\n";
    
    // Obtener plugins activos
    $query = "SELECT option_value FROM wp_options WHERE option_name = 'active_plugins'";
    $result = $mysqli->query($query);
    
    if ($result && $row = $result->fetch_assoc()) {
        $active_plugins = unserialize($row['option_value']);
        
        echo "📋 Plugins activos:\n";
        foreach ($active_plugins as $key => $plugin) {
            echo "   $key: $plugin\n";
        }
        
        // Filtrar plugins de SoloYLibre
        $filtered_plugins = array();
        foreach ($active_plugins as $plugin) {
            if (strpos($plugin, 'soloylibre') === false && strpos($plugin, 'Archive') === false) {
                $filtered_plugins[] = $plugin;
            } else {
                echo "🚫 Removiendo: $plugin\n";
            }
        }
        
        // Actualizar plugins activos
        $new_plugins_serialized = serialize($filtered_plugins);
        $update_query = "UPDATE wp_options SET option_value = ? WHERE option_name = 'active_plugins'";
        $stmt = $mysqli->prepare($update_query);
        $stmt->bind_param('s', $new_plugins_serialized);
        
        if ($stmt->execute()) {
            echo "✅ Plugins de SoloYLibre desactivados\n";
        } else {
            echo "❌ Error al actualizar plugins\n";
        }
        
        $stmt->close();
    }
    
    // Verificar configuración básica de WordPress
    echo "\n🔍 Verificando configuración de WordPress:\n";
    $config_query = "SELECT option_name, option_value FROM wp_options WHERE option_name IN ('siteurl', 'home', 'blogname')";
    $result = $mysqli->query($config_query);
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo "   " . $row['option_name'] . ": " . $row['option_value'] . "\n";
        }
    }
    
    $mysqli->close();
    
    echo "\n🎉 ¡Listo! Intenta acceder a WordPress ahora:\n";
    echo "🔗 http://localhost:8888/wp/wordpress/wp-admin/\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
