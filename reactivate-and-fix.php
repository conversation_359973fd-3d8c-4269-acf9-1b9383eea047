<?php
/**
 * Reactivar plugin y solucionar problemas de acceso
 * Developed by JEYKO AI for Jose L <PERSON> (JoseTusabe)
 */

echo "🔧 REACTIVANDO PLUGIN Y SOLUCIONANDO ACCESO\n\n";

// Configuración de la base de datos
$host = 'localhost';
$username = 'root';
$password = 'root';
$database = 'wp';

try {
    $mysqli = new mysqli($host, $username, $password, $database);
    
    if ($mysqli->connect_error) {
        die("Error de conexión: " . $mysqli->connect_error);
    }
    
    echo "✅ Conectado a la base de datos\n";
    
    // 1. Reactivar el plugin
    echo "\n1. 🔌 Reactivando plugin...\n";
    
    $query = "SELECT option_value FROM wp_options WHERE option_name = 'active_plugins'";
    $result = $mysqli->query($query);
    
    if ($result && $row = $result->fetch_assoc()) {
        $active_plugins = unserialize($row['option_value']);
        
        // Remover plugins antiguos de SoloYLibre
        $active_plugins = array_filter($active_plugins, function($plugin) {
            return strpos($plugin, 'soloylibre') === false && strpos($plugin, 'Archive') === false;
        });
        
        // Agregar el plugin correcto
        $active_plugins[] = 'Archive/soloylibre-gallery-plugin.php';
        $active_plugins = array_values(array_unique($active_plugins));
        
        $new_plugins_serialized = serialize($active_plugins);
        
        $update_query = "UPDATE wp_options SET option_value = ? WHERE option_name = 'active_plugins'";
        $stmt = $mysqli->prepare($update_query);
        $stmt->bind_param('s', $new_plugins_serialized);
        
        if ($stmt->execute()) {
            echo "   ✅ Plugin reactivado exitosamente\n";
        } else {
            echo "   ❌ Error al reactivar el plugin\n";
        }
        
        $stmt->close();
    }
    
    // 2. Verificar usuario admin
    echo "\n2. 👤 Verificando usuario administrador...\n";
    
    $user_query = "SELECT ID, user_login FROM wp_users WHERE user_login = 'admin_soloylibre'";
    $user_result = $mysqli->query($user_query);
    
    if ($user_result && $user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();
        echo "   ✅ Usuario encontrado: " . $user['user_login'] . " (ID: " . $user['ID'] . ")\n";
        
        // Verificar que sea administrador
        $meta_query = "SELECT meta_value FROM wp_usermeta WHERE user_id = ? AND meta_key = 'wp_capabilities'";
        $meta_stmt = $mysqli->prepare($meta_query);
        $meta_stmt->bind_param('i', $user['ID']);
        $meta_stmt->execute();
        $meta_result = $meta_stmt->get_result();
        
        if ($meta_result && $meta_row = $meta_result->fetch_assoc()) {
            $capabilities = unserialize($meta_row['meta_value']);
            if (isset($capabilities['administrator'])) {
                echo "   ✅ Usuario tiene permisos de administrador\n";
            } else {
                echo "   🔧 Otorgando permisos de administrador...\n";
                $capabilities['administrator'] = true;
                $new_caps = serialize($capabilities);
                
                $update_caps = "UPDATE wp_usermeta SET meta_value = ? WHERE user_id = ? AND meta_key = 'wp_capabilities'";
                $caps_stmt = $mysqli->prepare($update_caps);
                $caps_stmt->bind_param('si', $new_caps, $user['ID']);
                $caps_stmt->execute();
                $caps_stmt->close();
                echo "   ✅ Permisos otorgados\n";
            }
        }
        
        $meta_stmt->close();
    } else {
        echo "   🔧 Creando usuario administrador...\n";
        
        // Crear usuario
        $password_hash = wp_hash_password('SoloYLibre2025!');
        $insert_user = "INSERT INTO wp_users (user_login, user_pass, user_nicename, user_email, user_registered, display_name) VALUES (?, ?, ?, ?, NOW(), ?)";
        $user_stmt = $mysqli->prepare($insert_user);
        $user_login = 'admin_soloylibre';
        $user_nicename = 'admin_soloylibre';
        $user_email = '<EMAIL>';
        $display_name = 'Jose L Encarnacion (JoseTusabe)';
        $user_stmt->bind_param('sssss', $user_login, $password_hash, $user_nicename, $user_email, $display_name);
        
        if ($user_stmt->execute()) {
            $user_id = $mysqli->insert_id;
            echo "   ✅ Usuario creado con ID: $user_id\n";
            
            // Agregar capacidades de administrador
            $capabilities = serialize(array('administrator' => true));
            $insert_caps = "INSERT INTO wp_usermeta (user_id, meta_key, meta_value) VALUES (?, 'wp_capabilities', ?)";
            $caps_stmt = $mysqli->prepare($insert_caps);
            $caps_stmt->bind_param('is', $user_id, $capabilities);
            $caps_stmt->execute();
            $caps_stmt->close();
            
            // Agregar nivel de usuario
            $user_level = "INSERT INTO wp_usermeta (user_id, meta_key, meta_value) VALUES (?, 'wp_user_level', '10')";
            $level_stmt = $mysqli->prepare($user_level);
            $level_stmt->bind_param('i', $user_id);
            $level_stmt->execute();
            $level_stmt->close();
            
            echo "   ✅ Permisos de administrador asignados\n";
        }
        
        $user_stmt->close();
    }
    
    // 3. Limpiar cache y transients
    echo "\n3. 🧹 Limpiando cache...\n";
    
    $cache_queries = array(
        "DELETE FROM wp_options WHERE option_name LIKE '_transient_%'",
        "DELETE FROM wp_options WHERE option_name LIKE '_site_transient_%'",
        "DELETE FROM wp_options WHERE option_name = 'rewrite_rules'"
    );
    
    foreach ($cache_queries as $query) {
        if ($mysqli->query($query)) {
            echo "   ✅ Cache limpiado\n";
        }
    }
    
    // 4. Verificar opciones del plugin
    echo "\n4. ⚙️ Verificando opciones del plugin...\n";
    
    $plugin_options = array(
        'soloylibre_gallery_photographer_name' => 'Jose L Encarnacion',
        'soloylibre_gallery_photographer_alias' => 'JoseTusabe',
        'soloylibre_gallery_photographer_brand' => 'SoloYLibre Photography',
        'soloylibre_gallery_photographer_email' => '<EMAIL>',
        'soloylibre_gallery_photographer_phone' => '************',
        'soloylibre_gallery_photographer_location' => 'San José de Ocoa, República Dominicana / USA'
    );
    
    foreach ($plugin_options as $option_name => $option_value) {
        $check_query = "SELECT COUNT(*) as count FROM wp_options WHERE option_name = ?";
        $check_stmt = $mysqli->prepare($check_query);
        $check_stmt->bind_param('s', $option_name);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        $check_row = $check_result->fetch_assoc();
        
        if ($check_row['count'] == 0) {
            $insert_query = "INSERT INTO wp_options (option_name, option_value, autoload) VALUES (?, ?, 'yes')";
            $insert_stmt = $mysqli->prepare($insert_query);
            $insert_stmt->bind_param('ss', $option_name, $option_value);
            
            if ($insert_stmt->execute()) {
                echo "   ✅ Opción creada: $option_name\n";
            }
            
            $insert_stmt->close();
        } else {
            echo "   ℹ️ Opción ya existe: $option_name\n";
        }
        
        $check_stmt->close();
    }
    
    // 5. Crear página de login personalizada
    echo "\n5. 🔑 Creando acceso directo...\n";
    
    $login_page_content = '<?php
// Login automático para SoloYLibre
require_once("wp-config.php");
require_once("wp-load.php");

// Buscar usuario admin_soloylibre
$user = get_user_by("login", "admin_soloylibre");
if ($user) {
    wp_set_current_user($user->ID);
    wp_set_auth_cookie($user->ID, true);
    
    // Redirigir al wizard
    $redirect_url = admin_url("admin.php?page=soloylibre-simple-wizard");
    header("Location: " . $redirect_url);
    exit;
} else {
    echo "Error: Usuario no encontrado";
}
?>';
    
    file_put_contents('auto-login-soloylibre.php', $login_page_content);
    echo "   ✅ Página de login automático creada: auto-login-soloylibre.php\n";
    
    $mysqli->close();
    
    echo "\n" . str_repeat("=", 70) . "\n";
    echo "🎉 PLUGIN REACTIVADO Y CONFIGURADO\n";
    echo str_repeat("=", 70) . "\n";
    
    echo "\n🔗 URLS PARA ACCEDER:\n";
    echo "   🔑 Login automático: http://localhost:8888/wp/wordpress/auto-login-soloylibre.php\n";
    echo "   🌐 Admin normal: http://localhost:8888/wp/wordpress/wp-admin/\n";
    echo "   🧙‍♂️ Wizard directo: http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-simple-wizard\n";
    
    echo "\n👤 CREDENCIALES DE ACCESO:\n";
    echo "   Usuario: admin_soloylibre\n";
    echo "   Contraseña: SoloYLibre2025!\n";
    echo "   Email: <EMAIL>\n";
    
    echo "\n📦 ESTADO DEL PLUGIN:\n";
    echo "   ✅ Plugin reactivado\n";
    echo "   ✅ Usuario administrador verificado\n";
    echo "   ✅ Cache limpiado\n";
    echo "   ✅ Opciones configuradas\n";
    echo "   ✅ Login automático disponible\n";
    
    echo "\n🚀 PRÓXIMOS PASOS:\n";
    echo "   1. Hacer clic en el enlace de login automático\n";
    echo "   2. Serás redirigido automáticamente al wizard\n";
    echo "   3. Si hay problemas, usar las credenciales manuales\n";
    echo "   4. Probar el botón '🐛 Debug Info' en el wizard\n";
    
    echo "\n📞 SOPORTE:\n";
    echo "   📧 <EMAIL>\n";
    echo "   📱 ************\n";
    echo "   🇩🇴 Jose L Encarnacion (JoseTusabe)\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

// Función para hash de contraseña (simplificada)
function wp_hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

?>
