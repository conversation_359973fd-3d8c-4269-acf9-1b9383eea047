<?php
/**
 * Plugin Verification Script
 * Script de verificación del plugin
 */

// Verificar que WordPress esté cargado
if (!defined("ABSPATH")) {
    require_once("wp-load.php");
}

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🔧 Verificación del Plugin - SoloYLibre</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f7fa; }";
echo ".container { background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); max-width: 800px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }";
echo ".test-item { margin: 15px 0; padding: 15px; border-radius: 8px; }";
echo ".success { background: #d4edda; border-left: 4px solid #28a745; }";
echo ".error { background: #f8d7da; border-left: 4px solid #dc3545; }";
echo ".warning { background: #fff3cd; border-left: 4px solid #ffc107; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class=\"container\">";

echo "<div class=\"header\">";
echo "<h1>🔧 Verificación del Plugin SoloYLibre</h1>";
echo "<p>Estado actual del sistema después de las correcciones</p>";
echo "</div>";

$all_tests_passed = true;

// Test 1: Verificar clases principales
echo "<div class=\"test-item";
$classes_to_check = array(
    "SoloYLibre_Gallery_Plugin",
    "SoloYLibre_Settings_Manager", 
    "SoloYLibre_Enhanced_Shortcode",
    "SoloYLibre_Confetti_System"
);

$classes_loaded = 0;
foreach ($classes_to_check as $class) {
    if (class_exists($class)) {
        $classes_loaded++;
    }
}

if ($classes_loaded == count($classes_to_check)) {
    echo " success\">";
    echo "<h3>✅ Test 1: Clases del Plugin</h3>";
    echo "<p><strong>Estado:</strong> Todas las clases principales cargadas correctamente</p>";
    foreach ($classes_to_check as $class) {
        echo "<p>• $class: " . (class_exists($class) ? "✅" : "❌") . "</p>";
    }
} else {
    echo " error\">";
    echo "<h3>❌ Test 1: Clases del Plugin</h3>";
    echo "<p><strong>Estado:</strong> $classes_loaded/" . count($classes_to_check) . " clases cargadas</p>";
    $all_tests_passed = false;
}
echo "</div>";

// Test 2: Verificar shortcodes
echo "<div class=\"test-item";
global $shortcode_tags;
if (isset($shortcode_tags["soloylibre_gallery"])) {
    echo " success\">";
    echo "<h3>✅ Test 2: Shortcodes</h3>";
    echo "<p><strong>Estado:</strong> Shortcode [soloylibre_gallery] registrado correctamente</p>";
} else {
    echo " error\">";
    echo "<h3>❌ Test 2: Shortcodes</h3>";
    echo "<p><strong>Estado:</strong> Shortcode no registrado</p>";
    $all_tests_passed = false;
}
echo "</div>";

// Test 3: Verificar base de datos
echo "<div class=\"test-item";
global $wpdb;
$tables = array("wp_soloylibre_albums", "wp_soloylibre_photos", "wp_soloylibre_interactions");
$tables_ok = 0;

foreach ($tables as $table) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE \"$table\"") == $table;
    if ($exists) {
        $tables_ok++;
    }
}

if ($tables_ok >= 2) {
    echo " success\">";
    echo "<h3>✅ Test 3: Base de Datos</h3>";
    echo "<p><strong>Estado:</strong> $tables_ok/" . count($tables) . " tablas disponibles</p>";
} else {
    echo " warning\">";
    echo "<h3>⚠️ Test 3: Base de Datos</h3>";
    echo "<p><strong>Estado:</strong> $tables_ok/" . count($tables) . " tablas disponibles</p>";
}
echo "</div>";

// Test 4: Verificar configuraciones
echo "<div class=\"test-item";
$options = get_option("soloylibre_options", array());
if (!empty($options) || class_exists("SoloYLibre_Settings_Manager")) {
    echo " success\">";
    echo "<h3>✅ Test 4: Sistema de Configuraciones</h3>";
    echo "<p><strong>Estado:</strong> Sistema de configuraciones disponible</p>";
} else {
    echo " warning\">";
    echo "<h3>⚠️ Test 4: Sistema de Configuraciones</h3>";
    echo "<p><strong>Estado:</strong> Configuraciones no inicializadas</p>";
}
echo "</div>";

// Resumen final
echo "<div class=\"test-item";
if ($all_tests_passed) {
    echo " success\">";
    echo "<h2>🎉 RESUMEN: PLUGIN FUNCIONANDO CORRECTAMENTE</h2>";
    echo "<p><strong>Estado:</strong> Todos los tests críticos pasaron</p>";
    echo "<p><strong>Plugin:</strong> Listo para usar</p>";
} else {
    echo " warning\">";
    echo "<h2>⚠️ RESUMEN: PLUGIN PARCIALMENTE FUNCIONAL</h2>";
    echo "<p><strong>Estado:</strong> Algunos tests fallaron pero el plugin debería funcionar</p>";
}
echo "</div>";

echo "<h3>🔗 Enlaces de Prueba</h3>";
echo "<p><a href=\"test-enhanced-galleries.php\" target=\"_blank\">🎨 Probar Galerías Mejoradas</a></p>";
echo "<p><a href=\"monitor-errores.php\" target=\"_blank\">🔍 Monitor de Errores</a></p>";
echo "<p><a href=\"auto-login-soloylibre.php\" target=\"_blank\">🔑 Login Automático</a></p>";

echo "</div>";
echo "</body>";
echo "</html>";
?>