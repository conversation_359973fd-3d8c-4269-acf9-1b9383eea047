<?php
/**
 * Test Improved System
 * Prueba del sistema mejorado sin API
 * Developed by JEYKO AI for Jose L <PERSON> (JoseTusabe)
 */

echo "🧪 PROBANDO SISTEMA MEJORADO SIN API\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// 1. Limpiar logs completamente
echo "1. 🧹 LIMPIANDO LOGS COMPLETAMENTE...\n";

$log_files = array(
    'wp-content/debug.log',
    'error_log',
    'php_error.log',
    'wp-content/uploads/debug.log'
);

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        file_put_contents($log_file, '');
        echo "   ✅ Limpiado: $log_file\n";
    }
}

// 2. Crear página de prueba del sistema mejorado
echo "\n2. 📋 CREANDO PÁGINA DE PRUEBA DEL SISTEMA MEJORADO...\n";

$test_page = '<?php
// Prueba del sistema mejorado SoloYLibre
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🧪 Sistema Mejorado - SoloYLibre</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); }";
echo ".container { background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); max-width: 1000px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 30px; text-align: center; }";
echo ".feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }";
echo ".feature-card { border: 2px solid #e9ecef; border-radius: 10px; padding: 20px; transition: all 0.3s ease; background: white; }";
echo ".feature-card.success { border-color: #28a745; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); }";
echo ".feature-card.improved { border-color: #007bff; background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); }";
echo ".btn { background: #CE1126; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; display: inline-block; margin: 10px 5px; font-weight: 600; transition: all 0.3s ease; }";
echo ".btn:hover { background: #002D62; transform: translateY(-2px); }";
echo ".icon { font-size: 2rem; margin-bottom: 10px; }";
echo ".improvement { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class=\"container\">";
echo "<div class=\"header\">";
echo "<h1>🧪 Sistema Mejorado - SoloYLibre Gallery Pro</h1>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe) - Sin API, UI Simplificada</p>";
echo "<p>⏰ " . date("Y-m-d H:i:s") . "</p>";
echo "</div>";

// Test WordPress
echo "<h2>🔧 Estado del Sistema</h2>";
try {
    require_once("wp-load.php");
    echo "<div class=\"feature-card success\">";
    echo "<div class=\"icon\">✅</div>";
    echo "<h3>WordPress Core</h3>";
    echo "<p><strong>Estado:</strong> Funcionando correctamente</p>";
    echo "<p><strong>Versión:</strong> " . get_bloginfo("version") . "</p>";
    echo "</div>";
    $wp_ok = true;
} catch (Exception $e) {
    echo "<div class=\"feature-card error\">";
    echo "<div class=\"icon\">❌</div>";
    echo "<h3>WordPress Core</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
    $wp_ok = false;
}

if ($wp_ok) {
    echo "<div class=\"feature-grid\">";
    
    // Plugin Status
    $active_plugins = get_option("active_plugins", array());
    $plugin_active = false;
    foreach ($active_plugins as $plugin) {
        if (strpos($plugin, "Archive") !== false) {
            $plugin_active = true;
            break;
        }
    }
    
    echo "<div class=\"feature-card " . ($plugin_active ? "success" : "error") . "\">";
    echo "<div class=\"icon\">" . ($plugin_active ? "✅" : "❌") . "</div>";
    echo "<h3>Plugin SoloYLibre</h3>";
    echo "<p><strong>Estado:</strong> " . ($plugin_active ? "Activo" : "Inactivo") . "</p>";
    if ($plugin_active) {
        echo "<p><strong>API:</strong> Desactivada (sin errores)</p>";
    }
    echo "</div>";
    
    // Improved Wizard
    echo "<div class=\"feature-card improved\">";
    echo "<div class=\"icon\">🧙‍♂️</div>";
    echo "<h3>Wizard Mejorado</h3>";
    echo "<p><strong>Funcionalidad:</strong> Carga automática de 300 fotos</p>";
    echo "<p><strong>UI:</strong> Simplificada y organizada</p>";
    echo "<p><strong>Selección:</strong> Drag & drop, agregar/quitar fotos</p>";
    echo "</div>";
    
    // Menu System
    echo "<div class=\"feature-card improved\">";
    echo "<div class=\"icon\">📋</div>";
    echo "<h3>Menú Simplificado</h3>";
    echo "<p><strong>Menús:</strong> Solo 2 opciones esenciales</p>";
    echo "<p><strong>Navegación:</strong> Directa al wizard y estadísticas</p>";
    echo "<p><strong>Confusión:</strong> Eliminada</p>";
    echo "</div>";
    
    // Database
    global $wpdb;
    $tables = array("wp_soloylibre_albums", "wp_soloylibre_photos", "wp_soloylibre_interactions");
    $tables_ok = 0;
    foreach ($tables as $table) {
        $exists = $wpdb->get_var("SHOW TABLES LIKE \"$table\"") == $table;
        if ($exists) $tables_ok++;
    }
    
    echo "<div class=\"feature-card " . ($tables_ok == 3 ? "success" : "warning") . "\">";
    echo "<div class=\"icon\">" . ($tables_ok == 3 ? "✅" : "⚠️") . "</div>";
    echo "<h3>Base de Datos</h3>";
    echo "<p><strong>Tablas:</strong> $tables_ok/3 funcionando</p>";
    if ($tables_ok == 3) {
        foreach ($tables as $table) {
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
            echo "<p><strong>" . basename($table) . ":</strong> $count registros</p>";
        }
    }
    echo "</div>";
    
    // User Access
    $current_user = wp_get_current_user();
    echo "<div class=\"feature-card " . ($current_user->ID > 0 ? "success" : "warning") . "\">";
    echo "<div class=\"icon\">" . ($current_user->ID > 0 ? "✅" : "⚠️") . "</div>";
    echo "<h3>Usuario Actual</h3>";
    if ($current_user->ID > 0) {
        echo "<p><strong>Usuario:</strong> " . $current_user->user_login . "</p>";
        echo "<p><strong>Permisos:</strong> " . (current_user_can("manage_options") ? "Administrador" : "Limitados") . "</p>";
    } else {
        echo "<p><strong>Estado:</strong> No logueado</p>";
    }
    echo "</div>";
    
    // Error Status
    $error_files = array("wp-content/debug.log", "error_log");
    $errors_found = false;
    foreach ($error_files as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if (!empty(trim($content))) {
                $errors_found = true;
                break;
            }
        }
    }
    
    echo "<div class=\"feature-card " . (!$errors_found ? "success" : "error") . "\">";
    echo "<div class=\"icon\">" . (!$errors_found ? "✅" : "❌") . "</div>";
    echo "<h3>Logs de Errores</h3>";
    echo "<p><strong>Estado:</strong> " . (!$errors_found ? "Sin errores" : "Errores encontrados") . "</p>";
    echo "<p><strong>API Errors:</strong> Eliminados</p>";
    echo "</div>";
    
    echo "</div>";
}

// Improvements Summary
echo "<h2>🚀 Mejoras Implementadas</h2>";
echo "<div class=\"improvement\">";
echo "<h4>📸 Selección de Fotos Mejorada</h4>";
echo "<ul>";
echo "<li>✅ Carga automática de 300 fotos al abrir el wizard</li>";
echo "<li>✅ Opción para agregar más fotos desde biblioteca de medios</li>";
echo "<li>✅ Opción para remover fotos individualmente</li>";
echo "<li>✅ Seleccionar/deseleccionar todas las fotos</li>";
echo "<li>✅ Drag & drop para reordenar fotos</li>";
echo "<li>✅ Vista previa con thumbnails</li>";
echo "</ul>";
echo "</div>";

echo "<div class=\"improvement\">";
echo "<h4>📋 Menú de WordPress Simplificado</h4>";
echo "<ul>";
echo "<li>✅ Solo 2 opciones en el menú: Crear Galería y Estadísticas</li>";
echo "<li>✅ Eliminadas opciones confusas y redundantes</li>";
echo "<li>✅ Navegación directa a funcionalidades principales</li>";
echo "<li>✅ Icono mejorado (📸) para fácil identificación</li>";
echo "</ul>";
echo "</div>";

echo "<div class=\"improvement\">";
echo "<h4>🚫 API Manager Eliminado</h4>";
echo "<ul>";
echo "<li>✅ API completamente desactivada para evitar errores</li>";
echo "<li>✅ Sin más errores de métodos no implementados</li>";
echo "<li>✅ Plugin más estable y confiable</li>";
echo "<li>✅ Logs limpios sin errores de API</li>";
echo "</ul>";
echo "</div>";

echo "<div class=\"improvement\">";
echo "<h4>🎨 UI Mejorada</h4>";
echo "<ul>";
echo "<li>✅ Interfaz más limpia y organizada</li>";
echo "<li>✅ Secciones claramente definidas</li>";
echo "<li>✅ Controles intuitivos</li>";
echo "<li>✅ Responsive design</li>";
echo "<li>✅ Colores dominicanos integrados</li>";
echo "</ul>";
echo "</div>";

// Action Links
echo "<h2>🔗 Acciones Disponibles</h2>";
echo "<div style=\"text-align: center;\">";
echo "<a href=\"auto-login-soloylibre.php\" class=\"btn\">🔑 Login Automático</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-wizard\" class=\"btn\">🧙‍♂️ Nuevo Wizard</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-statistics\" class=\"btn\">📊 Estadísticas</a>";
echo "<a href=\"monitor-errores.php\" class=\"btn\">📊 Monitor</a>";
echo "</div>";

// Contact
echo "<hr>";
echo "<div style=\"background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 25px; border-radius: 15px; text-align: center;\">";
echo "<h3>📞 SoloYLibre Photography</h3>";
echo "<h4>👨‍💻 Jose L Encarnacion (JoseTusabe)</h4>";
echo "<p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
echo "<p>📞 718-713-5500 | 📧 <EMAIL></p>";
echo "<div style=\"margin-top: 15px;\">";
echo "<span style=\"margin: 0 10px;\">🌐 josetusabe.com</span>";
echo "<span style=\"margin: 0 10px;\">🌐 soloylibre.com</span>";
echo "<span style=\"margin: 0 10px;\">📸 1and1photo.com</span>";
echo "<span style=\"margin: 0 10px;\">👨‍💼 joselencarnacion.com</span>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>';

file_put_contents('test-sistema-mejorado.php', $test_page);
echo "   ✅ Página de prueba del sistema mejorado creada\n";

echo "\n" . str_repeat("=", 70) . "\n";
echo "✅ SISTEMA MEJORADO LISTO PARA PRUEBAS\n";
echo str_repeat("=", 70) . "\n";

echo "\n📊 MEJORAS IMPLEMENTADAS:\n";
echo "   ✅ API Manager completamente desactivado\n";
echo "   ✅ Wizard con carga automática de 300 fotos\n";
echo "   ✅ UI simplificada y organizada\n";
echo "   ✅ Menú de WordPress limpio (solo 2 opciones)\n";
echo "   ✅ Selección de fotos mejorada\n";
echo "   ✅ Logs de errores limpiados\n";

echo "\n🔗 URLS PARA PROBAR:\n";
echo "   🧪 Sistema mejorado: http://localhost:8888/wp/wordpress/test-sistema-mejorado.php\n";
echo "   🔑 Login automático: http://localhost:8888/wp/wordpress/auto-login-soloylibre.php\n";
echo "   🧙‍♂️ Nuevo wizard: http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-wizard\n";

echo "\n🎯 CARACTERÍSTICAS DEL NUEVO WIZARD:\n";
echo "   📸 Carga automática de 300 fotos\n";
echo "   ➕ Opción para agregar más fotos\n";
echo "   ❌ Opción para remover fotos\n";
echo "   🔄 Drag & drop para reordenar\n";
echo "   ✅ Seleccionar/deseleccionar todas\n";
echo "   🎨 UI limpia y organizada\n";

echo "\n📞 SOPORTE:\n";
echo "   📧 <EMAIL>\n";
echo "   📱 718-713-5500\n";
echo "   🇩🇴 Jose L Encarnacion (JoseTusabe)\n";

?>
