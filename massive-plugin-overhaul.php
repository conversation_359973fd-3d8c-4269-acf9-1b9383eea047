<?php
/**
 * Massive Plugin Overhaul - SoloYLibre Gallery Pro
 * Revisión masiva completa del plugin
 * Developed by JEYKO AI for Jose L <PERSON>carnacion (JoseTusabe)
 */

echo "🚀 REVISIÓN MASIVA COMPLETA - SOLOYLIBRE GALLERY PRO\n";
echo "=" . str_repeat("=", 55) . "\n\n";

// 1. Auditoría completa de archivos
echo "1. 📁 AUDITORÍA COMPLETA DE ARCHIVOS...\n";

$plugin_files = array(
    'wp-content/plugins/Archive/soloylibre-gallery-plugin.php' => 'Plugin Principal',
    'wp-content/plugins/Archive/includes/class-database.php' => 'Base de Datos',
    'wp-content/plugins/Archive/includes/class-improved-wizard.php' => 'Wizard Mejorado',
    'wp-content/plugins/Archive/includes/class-statistics-dashboard.php' => 'Dashboard',
    'wp-content/plugins/Archive/includes/class-shortcode-handler.php' => 'Shortcode Handler'
);

$files_status = array();
foreach ($plugin_files as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        $lines = count(file($file));
        echo "   ✅ $description ($lines líneas, " . round($size/1024, 1) . " KB)\n";
        $files_status[$file] = 'OK';
    } else {
        echo "   ❌ $description - FALTANTE\n";
        $files_status[$file] = 'MISSING';
    }
}

// 2. Actualizar versión del plugin
echo "\n2. 📦 ACTUALIZANDO VERSIÓN DEL PLUGIN...\n";

$plugin_main = 'wp-content/plugins/Archive/soloylibre-gallery-plugin.php';
if (file_exists($plugin_main)) {
    $content = file_get_contents($plugin_main);
    
    // Buscar versión actual
    if (preg_match('/Version:\s*([0-9.]+)/', $content, $matches)) {
        $current_version = $matches[1];
        $version_parts = explode('.', $current_version);
        $version_parts[2] = intval($version_parts[2]) + 1; // Incrementar patch
        $new_version = implode('.', $version_parts);
        
        // Actualizar versión
        $content = preg_replace('/Version:\s*[0-9.]+/', "Version: $new_version", $content);
        file_put_contents($plugin_main, $content);
        
        echo "   ✅ Versión actualizada: $current_version → $new_version\n";
    } else {
        echo "   ❌ No se pudo detectar la versión actual\n";
        $new_version = "5.0.0";
    }
} else {
    echo "   ❌ Archivo principal no encontrado\n";
    $new_version = "5.0.0";
}

// 3. Crear sistema de configuraciones centralizadas
echo "\n3. ⚙️ CREANDO SISTEMA DE CONFIGURACIONES...\n";

$settings_class = '<?php
/**
 * SoloYLibre Settings Manager
 * Gestor de configuraciones centralizadas
 */

class SoloYLibre_Settings_Manager {
    
    private $options_group = "soloylibre_settings";
    private $options_name = "soloylibre_options";
    
    public function __construct() {
        add_action("admin_menu", array($this, "add_settings_page"));
        add_action("admin_init", array($this, "register_settings"));
        add_action("wp_ajax_toggle_confetti", array($this, "ajax_toggle_confetti"));
        add_action("wp_ajax_generate_interactions", array($this, "ajax_generate_interactions"));
    }
    
    public function add_settings_page() {
        add_submenu_page(
            "soloylibre-main",
            "Configuraciones",
            "⚙️ Configuraciones",
            "manage_options",
            "soloylibre-settings",
            array($this, "render_settings_page")
        );
    }
    
    public function register_settings() {
        register_setting($this->options_group, $this->options_name);
        
        // Sección General
        add_settings_section(
            "general_section",
            "Configuraciones Generales",
            null,
            "soloylibre_settings"
        );
        
        // Campos de configuración
        $fields = array(
            "enable_confetti" => array(
                "title" => "Efectos de Confetti",
                "type" => "checkbox",
                "description" => "Mostrar efectos de confetti al usar el plugin"
            ),
            "gallery_default_style" => array(
                "title" => "Estilo por Defecto",
                "type" => "select",
                "options" => array(
                    "dominican" => "🇩🇴 Estilo Dominicano",
                    "grid" => "📱 Grid Moderno",
                    "masonry" => "🧱 Masonry",
                    "carousel" => "🎠 Carousel",
                    "professional" => "💼 Profesional"
                ),
                "description" => "Estilo por defecto para nuevas galerías"
            ),
            "auto_load_photos" => array(
                "title" => "Fotos Auto-cargadas",
                "type" => "number",
                "description" => "Número de fotos a cargar automáticamente (50-500)"
            ),
            "enable_interactions" => array(
                "title" => "Interacciones de Usuario",
                "type" => "checkbox",
                "description" => "Habilitar likes, vistas y compartir"
            ),
            "photographer_signature" => array(
                "title" => "Firma del Fotógrafo",
                "type" => "checkbox",
                "description" => "Mostrar firma en todas las galerías"
            ),
            "lightbox_enabled" => array(
                "title" => "Lightbox por Defecto",
                "type" => "checkbox",
                "description" => "Habilitar lightbox en todas las galerías"
            ),
            "random_interactions" => array(
                "title" => "Interacciones Aleatorias",
                "type" => "checkbox",
                "description" => "Generar likes y vistas aleatorias"
            )
        );
        
        foreach ($fields as $field_id => $field) {
            add_settings_field(
                $field_id,
                $field["title"],
                array($this, "render_field"),
                "soloylibre_settings",
                "general_section",
                array("field_id" => $field_id, "field" => $field)
            );
        }
    }
    
    public function render_field($args) {
        $field_id = $args["field_id"];
        $field = $args["field"];
        $options = get_option($this->options_name, array());
        $value = isset($options[$field_id]) ? $options[$field_id] : "";
        
        $name = $this->options_name . "[" . $field_id . "]";
        
        switch ($field["type"]) {
            case "checkbox":
                echo "<input type=\"checkbox\" name=\"$name\" value=\"1\" " . checked(1, $value, false) . ">";
                break;
                
            case "select":
                echo "<select name=\"$name\">";
                foreach ($field["options"] as $option_value => $option_label) {
                    echo "<option value=\"$option_value\" " . selected($option_value, $value, false) . ">$option_label</option>";
                }
                echo "</select>";
                break;
                
            case "number":
                $default = $field_id === "auto_load_photos" ? 300 : 0;
                $value = $value ?: $default;
                echo "<input type=\"number\" name=\"$name\" value=\"$value\" min=\"50\" max=\"500\">";
                break;
                
            default:
                echo "<input type=\"text\" name=\"$name\" value=\"$value\">";
        }
        
        if (isset($field["description"])) {
            echo "<p class=\"description\">" . $field["description"] . "</p>";
        }
    }
    
    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1>⚙️ Configuraciones SoloYLibre Gallery Pro</h1>
            
            <div style="background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h2 style="color: white; margin: 0;">🇩🇴 SoloYLibre Photography</h2>
                <p style="margin: 5px 0;">Jose L Encarnacion (JoseTusabe) - San José de Ocoa, República Dominicana</p>
            </div>
            
            <div class="soloylibre-settings-container">
                <form method="post" action="options.php">
                    <?php
                    settings_fields($this->options_group);
                    do_settings_sections("soloylibre_settings");
                    submit_button("💾 Guardar Configuraciones");
                    ?>
                </form>
                
                <div class="soloylibre-actions" style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h3>🚀 Acciones Rápidas</h3>
                    
                    <button type="button" class="button button-secondary" onclick="toggleConfetti()">
                        🎉 Probar Confetti
                    </button>
                    
                    <button type="button" class="button button-secondary" onclick="generateInteractions()">
                        📊 Generar Interacciones Aleatorias
                    </button>
                    
                    <button type="button" class="button button-primary" onclick="window.open(\'<?php echo admin_url("admin.php?page=soloylibre-wizard"); ?>\', \'_blank\')">
                        🧙‍♂️ Abrir Wizard
                    </button>
                </div>
            </div>
            
            <style>
            .soloylibre-settings-container {
                max-width: 800px;
            }
            
            .form-table th {
                width: 200px;
            }
            
            .soloylibre-actions button {
                margin-right: 10px;
                margin-bottom: 10px;
            }
            </style>
            
            <script>
            function toggleConfetti() {
                // Crear efecto de confetti
                if (typeof confetti !== "undefined") {
                    confetti({
                        particleCount: 100,
                        spread: 70,
                        origin: { y: 0.6 }
                    });
                } else {
                    alert("¡Confetti activado! 🎉");
                }
            }
            
            function generateInteractions() {
                if (confirm("¿Generar interacciones aleatorias para las galerías?")) {
                    jQuery.post(ajaxurl, {
                        action: "generate_interactions",
                        nonce: "<?php echo wp_create_nonce("generate_interactions"); ?>"
                    }, function(response) {
                        if (response.success) {
                            alert("✅ " + response.data.message);
                        } else {
                            alert("❌ Error: " + response.data.message);
                        }
                    });
                }
            }
            </script>
        </div>
        <?php
    }
    
    public function ajax_toggle_confetti() {
        wp_send_json_success(array("message" => "Confetti activado"));
    }
    
    public function ajax_generate_interactions() {
        check_ajax_referer("generate_interactions", "nonce");
        
        global $wpdb;
        
        // Obtener fotos de la biblioteca
        $photos = get_posts(array(
            "post_type" => "attachment",
            "post_mime_type" => "image",
            "posts_per_page" => 20,
            "post_status" => "inherit"
        ));
        
        $interactions_created = 0;
        
        foreach ($photos as $photo) {
            // Generar interacciones aleatorias
            $views = rand(10, 500);
            $likes = rand(1, 50);
            $shares = rand(0, 10);
            
            // Insertar en base de datos
            $wpdb->insert(
                $wpdb->prefix . "soloylibre_interactions",
                array(
                    "photo_id" => $photo->ID,
                    "interaction_type" => "view",
                    "interaction_count" => $views,
                    "created_at" => current_time("mysql")
                )
            );
            
            $wpdb->insert(
                $wpdb->prefix . "soloylibre_interactions",
                array(
                    "photo_id" => $photo->ID,
                    "interaction_type" => "like",
                    "interaction_count" => $likes,
                    "created_at" => current_time("mysql")
                )
            );
            
            $wpdb->insert(
                $wpdb->prefix . "soloylibre_interactions",
                array(
                    "photo_id" => $photo->ID,
                    "interaction_type" => "share",
                    "interaction_count" => $shares,
                    "created_at" => current_time("mysql")
                )
            );
            
            $interactions_created += 3;
        }
        
        wp_send_json_success(array(
            "message" => "Se generaron $interactions_created interacciones para " . count($photos) . " fotos"
        ));
    }
    
    public function get_option($key, $default = null) {
        $options = get_option($this->options_name, array());
        return isset($options[$key]) ? $options[$key] : $default;
    }
}

new SoloYLibre_Settings_Manager();
?>';

file_put_contents('wp-content/plugins/Archive/includes/class-settings-manager.php', $settings_class);
echo "   ✅ Sistema de configuraciones creado\n";

echo "\n" . str_repeat("=", 70) . "\n";
echo "✅ FASE 1 COMPLETADA - AUDITORÍA Y CONFIGURACIONES\n";
echo str_repeat("=", 70) . "\n";

echo "\n📊 RESUMEN FASE 1:\n";
echo "   📁 Archivos auditados: " . count($plugin_files) . "\n";
echo "   📦 Versión actualizada: $new_version\n";
echo "   ⚙️ Sistema de configuraciones: ✅ Creado\n";

echo "\n🔗 PRÓXIMAS FASES:\n";
echo "   2. Efectos de confetti y animaciones\n";
echo "   3. Mejoras en shortcodes y frontend\n";
echo "   4. Sistema de interacciones mejorado\n";
echo "   5. Optimización de base de datos\n";

echo "\n📞 SOPORTE:\n";
echo "   📧 <EMAIL>\n";
echo "   📱 718-713-5500\n";
echo "   🇩🇴 Jose L Encarnacion (JoseTusabe)\n";

?>
