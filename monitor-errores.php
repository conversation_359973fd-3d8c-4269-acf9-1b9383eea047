<?php
// Monitor de errores en tiempo real para SoloYLibre
header("Content-Type: text/plain");
header("Refresh: 5"); // Auto-refresh cada 5 segundos

echo "🔍 MONITOR DE ERRORES - SOLOYLIBRE GALLERY PRO\n";
echo "=" . str_repeat("=", 55) . "\n";
echo "⏰ Última actualización: " . date("Y-m-d H:i:s") . "\n\n";

$log_files = array(
    "wp-content/debug.log" => "WordPress Debug Log",
    "error_log" => "PHP Error Log"
);

foreach ($log_files as $file => $name) {
    echo "📄 $name ($file):\n";
    echo "-" . str_repeat("-", 50) . "\n";
    
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (empty(trim($content))) {
            echo "✅ Sin errores registrados\n";
        } else {
            $lines = explode("\n", $content);
            $recent_lines = array_slice($lines, -10); // Últimas 10 líneas
            
            foreach ($recent_lines as $line) {
                if (!empty(trim($line))) {
                    if (strpos($line, "Fatal error") !== false) {
                        echo "🚨 $line\n";
                    } elseif (strpos($line, "Warning") !== false) {
                        echo "⚠️ $line\n";
                    } elseif (strpos($line, "Notice") !== false) {
                        echo "ℹ️ $line\n";
                    } else {
                        echo "📝 $line\n";
                    }
                }
            }
        }
    } else {
        echo "❌ Archivo no encontrado\n";
    }
    echo "\n";
}

echo "🔗 ACCIONES:\n";
echo "- Actualizar página para ver cambios\n";
echo "- Ir a: test-funcional-soloylibre.php para pruebas\n";
echo "- Contactar: <EMAIL> si hay errores\n";
?>