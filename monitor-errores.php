<?php
/**
 * Enhanced Error Monitor with One-Click Copy
 * Monitor de errores mejorado con copia de un clic
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Manejar acciones AJAX
if (isset($_POST['action'])) {
    header('Content-Type: application/json');

    switch ($_POST['action']) {
        case 'clear_logs':
            $cleared = 0;
            $log_files = array(
                'wp-content/debug.log',
                'error_log',
                'php_error.log',
                'wp-content/uploads/debug.log'
            );

            foreach ($log_files as $file) {
                if (file_exists($file)) {
                    file_put_contents($file, '');
                    $cleared++;
                }
            }

            echo json_encode(array('success' => true, 'message' => "✅ $cleared archivos de log limpiados"));
            exit;

        case 'get_full_log':
            $file = sanitize_text_field($_POST['file']);
            if (file_exists($file)) {
                $content = file_get_contents($file);
                echo json_encode(array('success' => true, 'content' => $content));
            } else {
                echo json_encode(array('success' => false, 'message' => 'Archivo no encontrado'));
            }
            exit;

        case 'fix_common_errors':
            $fixes_applied = array();

            // Fix 1: Verificar y corregir permisos de archivos
            $plugin_dir = 'wp-content/plugins/Archive';
            if (is_dir($plugin_dir)) {
                chmod($plugin_dir, 0755);
                $fixes_applied[] = 'Permisos de directorio corregidos';
            }

            // Fix 2: Limpiar cache de opciones
            if (function_exists('wp_cache_flush')) {
                wp_cache_flush();
                $fixes_applied[] = 'Cache limpiado';
            }

            // Fix 3: Verificar archivos críticos
            $critical_files = array(
                'wp-content/plugins/Archive/soloylibre-gallery-plugin.php',
                'wp-content/plugins/Archive/includes/class-enhanced-shortcode.php'
            );

            foreach ($critical_files as $file) {
                if (file_exists($file)) {
                    $content = file_get_contents($file);
                    // Verificar sintaxis básica
                    if (strpos($content, '<?php') === 0) {
                        $fixes_applied[] = 'Archivo ' . basename($file) . ' verificado';
                    }
                }
            }

            echo json_encode(array('success' => true, 'fixes' => $fixes_applied));
            exit;
    }
}

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🔍 Monitor de Errores Mejorado - SoloYLibre</title>";
echo "<meta charset=\"UTF-8\">";
echo "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">";
echo "<style>";
echo "body { font-family: 'Courier New', monospace; margin: 0; padding: 20px; background: #1a1a1a; color: #00ff00; }";
echo ".container { max-width: 1200px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }";
echo ".log-section { background: #2d2d2d; border: 1px solid #444; border-radius: 8px; margin: 15px 0; overflow: hidden; }";
echo ".log-header { background: #333; padding: 15px; border-bottom: 1px solid #444; display: flex; justify-content: space-between; align-items: center; }";
echo ".log-content { padding: 15px; max-height: 400px; overflow-y: auto; font-size: 12px; line-height: 1.4; }";
echo ".log-line { margin: 2px 0; padding: 2px 0; }";
echo ".error { color: #ff4444; }";
echo ".warning { color: #ffaa00; }";
echo ".notice { color: #00aaff; }";
echo ".success { color: #00ff00; }";
echo ".btn { background: #CE1126; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin: 0 5px; font-size: 12px; transition: all 0.3s ease; }";
echo ".btn:hover { background: #002D62; transform: translateY(-1px); }";
echo ".btn-small { padding: 5px 10px; font-size: 11px; }";
echo ".btn-success { background: #28a745; }";
echo ".btn-warning { background: #ffc107; color: #000; }";
echo ".btn-danger { background: #dc3545; }";
echo ".stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }";
echo ".stat-card { background: #2d2d2d; border: 1px solid #444; border-radius: 8px; padding: 15px; text-align: center; }";
echo ".stat-number { font-size: 2rem; font-weight: bold; color: #00ff00; }";
echo ".stat-label { color: #ccc; font-size: 0.9rem; }";
echo ".actions { background: #2d2d2d; border: 1px solid #444; border-radius: 8px; padding: 20px; margin: 20px 0; }";
echo ".copy-btn { background: #17a2b8; }";
echo ".copy-btn:hover { background: #138496; }";
echo ".status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 8px; }";
echo ".status-ok { background: #28a745; }";
echo ".status-warning { background: #ffc107; }";
echo ".status-error { background: #dc3545; }";
echo ".auto-refresh { position: fixed; top: 20px; right: 20px; background: rgba(0,0,0,0.8); padding: 10px; border-radius: 5px; }";
echo ".modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; }";
echo ".modal-content { background: #2d2d2d; margin: 5% auto; padding: 20px; border-radius: 10px; width: 80%; max-width: 800px; max-height: 80%; overflow-y: auto; }";
echo ".close { color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; }";
echo ".close:hover { color: #fff; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class=\"container\">";

echo "<div class=\"header\">";
echo "<h1>🔍 Monitor de Errores Mejorado - SoloYLibre Gallery Pro v5.0.0</h1>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe) - Monitor en tiempo real con copia de un clic</p>";
echo "<p>⏰ Última actualización: " . date("Y-m-d H:i:s") . "</p>";
echo "</div>";

// Auto-refresh indicator
echo "<div class=\"auto-refresh\">";
echo "<span id=\"refresh-countdown\">Actualizando en <span id=\"countdown\">30</span>s</span>";
echo "<button class=\"btn btn-small\" onclick=\"toggleAutoRefresh()\">⏸️ Pausar</button>";
echo "</div>";

// Estadísticas
$log_files = array(
    'wp-content/debug.log' => 'WordPress Debug Log',
    'error_log' => 'PHP Error Log',
    'php_error.log' => 'PHP Error Log Alt',
    'wp-content/uploads/debug.log' => 'Uploads Debug Log'
);

$total_errors = 0;
$total_warnings = 0;
$total_notices = 0;
$files_with_errors = 0;

foreach ($log_files as $file => $name) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (!empty(trim($content))) {
            $files_with_errors++;
            $total_errors += substr_count($content, 'Fatal error');
            $total_warnings += substr_count($content, 'Warning');
            $total_notices += substr_count($content, 'Notice');
        }
    }
}

echo "<div class=\"stats\">";
echo "<div class=\"stat-card\">";
echo "<div class=\"stat-number\">$total_errors</div>";
echo "<div class=\"stat-label\">🚨 Errores Fatales</div>";
echo "</div>";
echo "<div class=\"stat-card\">";
echo "<div class=\"stat-number\">$total_warnings</div>";
echo "<div class=\"stat-label\">⚠️ Advertencias</div>";
echo "</div>";
echo "<div class=\"stat-card\">";
echo "<div class=\"stat-number\">$total_notices</div>";
echo "<div class=\"stat-label\">ℹ️ Avisos</div>";
echo "</div>";
echo "<div class=\"stat-card\">";
echo "<div class=\"stat-number\">$files_with_errors</div>";
echo "<div class=\"stat-label\">📄 Archivos con Errores</div>";
echo "</div>";
echo "</div>";

// Acciones rápidas
echo "<div class=\"actions\">";
echo "<h3>🚀 Acciones Rápidas</h3>";
echo "<button class=\"btn btn-danger\" onclick=\"clearAllLogs()\">🧹 Limpiar Todos los Logs</button>";
echo "<button class=\"btn btn-warning\" onclick=\"fixCommonErrors()\">🔧 Corregir Errores Comunes</button>";
echo "<button class=\"btn btn-success\" onclick=\"window.open('test-enhanced-galleries.php', '_blank')\">🧪 Probar Sistema</button>";
echo "<button class=\"btn\" onclick=\"window.open('auto-login-soloylibre.php', '_blank')\">🔑 Login Automático</button>";
echo "</div>";

// Mostrar logs
foreach ($log_files as $file => $name) {
    $status_class = 'status-ok';
    $error_count = 0;
    $content = '';

    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (!empty(trim($content))) {
            $error_count = substr_count($content, 'Fatal error') + substr_count($content, 'Warning');
            $status_class = $error_count > 0 ? 'status-error' : 'status-warning';
        }
    } else {
        $status_class = 'status-warning';
    }

    echo "<div class=\"log-section\">";
    echo "<div class=\"log-header\">";
    echo "<div>";
    echo "<span class=\"status-indicator $status_class\"></span>";
    echo "<strong>$name</strong> ($file)";
    if ($error_count > 0) {
        echo " <span style=\"color: #ff4444;\">($error_count errores)</span>";
    }
    echo "</div>";
    echo "<div>";
    echo "<button class=\"btn btn-small copy-btn\" onclick=\"copyLogContent('" . str_replace('/', '-', $file) . "')\">📋 Copiar</button>";
    echo "<button class=\"btn btn-small\" onclick=\"showFullLog('$file', '$name')\">🔍 Ver Completo</button>";
    echo "<button class=\"btn btn-small btn-danger\" onclick=\"clearSingleLog('$file')\">🗑️ Limpiar</button>";
    echo "</div>";
    echo "</div>";

    $safe_file_id = str_replace('/', '-', $file);
    echo "<div class=\"log-content\" id=\"log-content-$safe_file_id\">";

    if (file_exists($file)) {
        if (empty(trim($content))) {
            echo "<div class=\"log-line success\">✅ Sin errores registrados</div>";
        } else {
            $lines = explode("\n", $content);
            $recent_lines = array_slice($lines, -20); // Últimas 20 líneas

            foreach ($recent_lines as $line) {
                if (!empty(trim($line))) {
                    $class = 'log-line';
                    $icon = '📝';

                    if (strpos($line, 'Fatal error') !== false) {
                        $class .= ' error';
                        $icon = '🚨';
                    } elseif (strpos($line, 'Warning') !== false) {
                        $class .= ' warning';
                        $icon = '⚠️';
                    } elseif (strpos($line, 'Notice') !== false) {
                        $class .= ' notice';
                        $icon = 'ℹ️';
                    }

                    echo "<div class=\"$class\">$icon " . htmlspecialchars($line) . "</div>";
                }
            }

            if (count($lines) > 20) {
                echo "<div class=\"log-line notice\">... y " . (count($lines) - 20) . " líneas más (usar 'Ver Completo')</div>";
            }
        }
    } else {
        echo "<div class=\"log-line warning\">❌ Archivo no encontrado</div>";
    }

    echo "</div>";
    echo "</div>";
}

// Modal para log completo
echo "<div id=\"fullLogModal\" class=\"modal\">";
echo "<div class=\"modal-content\">";
echo "<span class=\"close\" onclick=\"closeModal()\">&times;</span>";
echo "<h2 id=\"modalTitle\">Log Completo</h2>";
echo "<button class=\"btn copy-btn\" onclick=\"copyModalContent()\">📋 Copiar Todo</button>";
echo "<pre id=\"modalContent\" style=\"background: #1a1a1a; color: #00ff00; padding: 15px; border-radius: 5px; max-height: 500px; overflow-y: auto;\"></pre>";
echo "</div>";
echo "</div>";

// Información del sistema
echo "<div class=\"log-section\">";
echo "<div class=\"log-header\">";
echo "<div><strong>📊 Información del Sistema</strong></div>";
echo "</div>";
echo "<div class=\"log-content\">";
echo "<div class=\"log-line success\">🐘 PHP Version: " . phpversion() . "</div>";
echo "<div class=\"log-line success\">💾 Memory Limit: " . ini_get('memory_limit') . "</div>";
echo "<div class=\"log-line success\">⏱️ Max Execution Time: " . ini_get('max_execution_time') . "s</div>";
echo "<div class=\"log-line success\">📁 Upload Max Size: " . ini_get('upload_max_filesize') . "</div>";
echo "<div class=\"log-line success\">🔧 Error Reporting: " . (error_reporting() ? 'Enabled' : 'Disabled') . "</div>";
echo "<div class=\"log-line success\">📝 Log Errors: " . (ini_get('log_errors') ? 'Enabled' : 'Disabled') . "</div>";
echo "</div>";
echo "</div>";

// Enlaces útiles
echo "<div class=\"actions\">";
echo "<h3>🔗 Enlaces Útiles</h3>";
echo "<a href=\"test-enhanced-galleries.php\" class=\"btn\" target=\"_blank\">🎨 Galerías Mejoradas</a>";
echo "<a href=\"massive-overhaul-completed.php\" class=\"btn\" target=\"_blank\">🎯 Resumen Final</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-settings\" class=\"btn\" target=\"_blank\">⚙️ Configuraciones</a>";
echo "<a href=\"shortcode-examples-and-docs.php\" class=\"btn\" target=\"_blank\">📚 Documentación</a>";
echo "</div>";

// Footer
echo "<div style=\"background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-top: 30px;\">";
echo "<h3>📞 SoloYLibre Photography</h3>";
echo "<p>👨‍💻 Jose L Encarnacion (JoseTusabe)</p>";
echo "<p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
echo "<p>📞 ************ | 📧 <EMAIL></p>";
echo "<p style=\"margin-top: 10px; font-style: italic;\">Monitor de errores mejorado con copia de un clic</p>";
echo "</div>";

echo "</div>"; // Cierre container

// JavaScript
echo "<script>";
echo "let autoRefresh = true;";
echo "let countdown = 30;";

echo "function updateCountdown() {";
echo "    if (autoRefresh) {";
echo "        countdown--;";
echo "        document.getElementById('countdown').textContent = countdown;";
echo "        if (countdown <= 0) {";
echo "            location.reload();";
echo "        }";
echo "    }";
echo "}";

echo "function toggleAutoRefresh() {";
echo "    autoRefresh = !autoRefresh;";
echo "    const btn = event.target;";
echo "    if (autoRefresh) {";
echo "        btn.textContent = '⏸️ Pausar';";
echo "        countdown = 30;";
echo "    } else {";
echo "        btn.textContent = '▶️ Reanudar';";
echo "    }";
echo "}";

echo "function copyLogContent(fileId) {";
echo "    const content = document.getElementById('log-content-' + fileId).textContent;";
echo "    navigator.clipboard.writeText(content).then(function() {";
echo "        alert('✅ Log copiado al portapapeles');";
echo "    }).catch(function() {";
echo "        // Fallback para navegadores antiguos";
echo "        const textArea = document.createElement('textarea');";
echo "        textArea.value = content;";
echo "        document.body.appendChild(textArea);";
echo "        textArea.select();";
echo "        document.execCommand('copy');";
echo "        document.body.removeChild(textArea);";
echo "        alert('✅ Log copiado al portapapeles');";
echo "    });";
echo "}";

echo "function showFullLog(file, name) {";
echo "    document.getElementById('modalTitle').textContent = 'Log Completo: ' + name;";
echo "    ";
echo "    fetch(window.location.href, {";
echo "        method: 'POST',";
echo "        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },";
echo "        body: 'action=get_full_log&file=' + encodeURIComponent(file)";
echo "    })";
echo "    .then(response => response.json())";
echo "    .then(data => {";
echo "        if (data.success) {";
echo "            document.getElementById('modalContent').textContent = data.content || 'Log vacío';";
echo "            document.getElementById('fullLogModal').style.display = 'block';";
echo "        } else {";
echo "            alert('❌ Error: ' + data.message);";
echo "        }";
echo "    });";
echo "}";

echo "function copyModalContent() {";
echo "    const content = document.getElementById('modalContent').textContent;";
echo "    navigator.clipboard.writeText(content).then(function() {";
echo "        alert('✅ Log completo copiado al portapapeles');";
echo "    });";
echo "}";

echo "function closeModal() {";
echo "    document.getElementById('fullLogModal').style.display = 'none';";
echo "}";

echo "function clearAllLogs() {";
echo "    if (confirm('¿Estás seguro de que quieres limpiar todos los logs?')) {";
echo "        fetch(window.location.href, {";
echo "            method: 'POST',";
echo "            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },";
echo "            body: 'action=clear_logs'";
echo "        })";
echo "        .then(response => response.json())";
echo "        .then(data => {";
echo "            alert(data.message);";
echo "            if (data.success) location.reload();";
echo "        });";
echo "    }";
echo "}";

echo "function clearSingleLog(file) {";
echo "    if (confirm('¿Limpiar el log: ' + file + '?')) {";
echo "        fetch(window.location.href, {";
echo "            method: 'POST',";
echo "            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },";
echo "            body: 'action=clear_logs'";
echo "        })";
echo "        .then(response => response.json())";
echo "        .then(data => {";
echo "            alert(data.message);";
echo "            if (data.success) location.reload();";
echo "        });";
echo "    }";
echo "}";

echo "function fixCommonErrors() {";
echo "    if (confirm('¿Aplicar correcciones automáticas para errores comunes?')) {";
echo "        fetch(window.location.href, {";
echo "            method: 'POST',";
echo "            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },";
echo "            body: 'action=fix_common_errors'";
echo "        })";
echo "        .then(response => response.json())";
echo "        .then(data => {";
echo "            if (data.success) {";
echo "                alert('✅ Correcciones aplicadas:\\n' + data.fixes.join('\\n'));";
echo "                location.reload();";
echo "            }";
echo "        });";
echo "    }";
echo "}";

echo "// Cerrar modal al hacer clic fuera";
echo "window.onclick = function(event) {";
echo "    const modal = document.getElementById('fullLogModal');";
echo "    if (event.target == modal) {";
echo "        modal.style.display = 'none';";
echo "    }";
echo "}";

echo "// Iniciar countdown";
echo "setInterval(updateCountdown, 1000);";

echo "</script>";

echo "</body>";
echo "</html>";
?>