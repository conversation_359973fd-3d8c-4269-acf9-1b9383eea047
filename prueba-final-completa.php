<?php
// Prueba final completa del plugin SoloYLibre
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🎯 Prueba Final - SoloYLibre Gallery Pro</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); }";
echo ".container { background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); max-width: 1000px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 30px; text-align: center; }";
echo ".test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }";
echo ".test-card { border: 2px solid #e9ecef; border-radius: 10px; padding: 20px; transition: all 0.3s ease; }";
echo ".test-card.success { border-color: #28a745; background: #d4edda; }";
echo ".test-card.error { border-color: #dc3545; background: #f8d7da; }";
echo ".test-card.warning { border-color: #ffc107; background: #fff3cd; }";
echo ".btn { background: #CE1126; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; display: inline-block; margin: 10px 5px; font-weight: 600; transition: all 0.3s ease; }";
echo ".btn:hover { background: #002D62; transform: translateY(-2px); }";
echo ".status-icon { font-size: 2rem; margin-bottom: 10px; }";
echo ".summary { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class=\"container\">";
echo "<div class=\"header\">";
echo "<h1>🎯 Prueba Final - SoloYLibre Gallery Pro</h1>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe) - Verificación Completa del Sistema</p>";
echo "<p>⏰ " . date("Y-m-d H:i:s") . "</p>";
echo "</div>";

$tests_passed = 0;
$total_tests = 0;

// Test 1: WordPress Loading
echo "<div class=\"test-grid\">";
echo "<div class=\"test-card";
$total_tests++;
try {
    require_once("wp-load.php");
    echo " success\">";
    echo "<div class=\"status-icon\">✅</div>";
    echo "<h3>WordPress Core</h3>";
    echo "<p><strong>Estado:</strong> Funcionando correctamente</p>";
    echo "<p><strong>Versión:</strong> " . get_bloginfo("version") . "</p>";
    $tests_passed++;
    $wp_loaded = true;
} catch (Exception $e) {
    echo " error\">";
    echo "<div class=\"status-icon\">❌</div>";
    echo "<h3>WordPress Core</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    $wp_loaded = false;
}
echo "</div>";

if ($wp_loaded) {
    // Test 2: Plugin Status
    echo "<div class=\"test-card";
    $total_tests++;
    $active_plugins = get_option("active_plugins", array());
    $soloylibre_active = false;
    foreach ($active_plugins as $plugin) {
        if (strpos($plugin, "Archive") !== false) {
            $soloylibre_active = true;
            break;
        }
    }
    
    if ($soloylibre_active) {
        echo " success\">";
        echo "<div class=\"status-icon\">✅</div>";
        echo "<h3>Plugin SoloYLibre</h3>";
        echo "<p><strong>Estado:</strong> Activo y funcionando</p>";
        $tests_passed++;
    } else {
        echo " error\">";
        echo "<div class=\"status-icon\">❌</div>";
        echo "<h3>Plugin SoloYLibre</h3>";
        echo "<p><strong>Estado:</strong> No está activo</p>";
    }
    echo "</div>";
    
    // Test 3: Classes Loading
    echo "<div class=\"test-card";
    $total_tests++;
    $classes = array(
        "SoloYLibre_Gallery_Plugin",
        "SoloYLibre_Simple_Wizard", 
        "SoloYLibre_API_Manager",
        "SoloYLibre_Statistics_Dashboard"
    );
    
    $classes_loaded = 0;
    foreach ($classes as $class) {
        if (class_exists($class)) {
            $classes_loaded++;
        }
    }
    
    if ($classes_loaded == count($classes)) {
        echo " success\">";
        echo "<div class=\"status-icon\">✅</div>";
        echo "<h3>Clases del Plugin</h3>";
        echo "<p><strong>Cargadas:</strong> $classes_loaded/" . count($classes) . "</p>";
        $tests_passed++;
    } else {
        echo " warning\">";
        echo "<div class=\"status-icon\">⚠️</div>";
        echo "<h3>Clases del Plugin</h3>";
        echo "<p><strong>Cargadas:</strong> $classes_loaded/" . count($classes) . "</p>";
    }
    echo "</div>";
    
    // Test 4: Database Tables
    echo "<div class=\"test-card";
    $total_tests++;
    global $wpdb;
    
    $tables = array(
        "wp_soloylibre_albums",
        "wp_soloylibre_photos",
        "wp_soloylibre_interactions"
    );
    
    $tables_ok = 0;
    $table_info = array();
    foreach ($tables as $table) {
        $exists = $wpdb->get_var("SHOW TABLES LIKE \"$table\"") == $table;
        if ($exists) {
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
            $table_info[] = basename($table) . " ($count registros)";
            $tables_ok++;
        }
    }
    
    if ($tables_ok == count($tables)) {
        echo " success\">";
        echo "<div class=\"status-icon\">✅</div>";
        echo "<h3>Base de Datos</h3>";
        echo "<p><strong>Tablas:</strong> $tables_ok/" . count($tables) . " funcionando</p>";
        foreach ($table_info as $info) {
            echo "<p>• $info</p>";
        }
        $tests_passed++;
    } else {
        echo " error\">";
        echo "<div class=\"status-icon\">❌</div>";
        echo "<h3>Base de Datos</h3>";
        echo "<p><strong>Tablas:</strong> $tables_ok/" . count($tables) . " funcionando</p>";
    }
    echo "</div>";
    
    // Test 5: User Access
    echo "<div class=\"test-card";
    $total_tests++;
    $current_user = wp_get_current_user();
    if ($current_user->ID > 0 && current_user_can("manage_options")) {
        echo " success\">";
        echo "<div class=\"status-icon\">✅</div>";
        echo "<h3>Acceso de Usuario</h3>";
        echo "<p><strong>Usuario:</strong> " . $current_user->user_login . "</p>";
        echo "<p><strong>Permisos:</strong> Administrador</p>";
        $tests_passed++;
    } else {
        echo " warning\">";
        echo "<div class=\"status-icon\">⚠️</div>";
        echo "<h3>Acceso de Usuario</h3>";
        if ($current_user->ID > 0) {
            echo "<p><strong>Usuario:</strong> " . $current_user->user_login . "</p>";
            echo "<p><strong>Permisos:</strong> Limitados</p>";
        } else {
            echo "<p><strong>Estado:</strong> No logueado</p>";
        }
    }
    echo "</div>";
    
    // Test 6: API Endpoints
    echo "<div class=\"test-card";
    $total_tests++;
    if (class_exists("SoloYLibre_API_Manager")) {
        echo " success\">";
        echo "<div class=\"status-icon\">✅</div>";
        echo "<h3>API REST</h3>";
        echo "<p><strong>Estado:</strong> Disponible</p>";
        echo "<p><strong>Namespace:</strong> soloylibre/v1</p>";
        $tests_passed++;
    } else {
        echo " error\">";
        echo "<div class=\"status-icon\">❌</div>";
        echo "<h3>API REST</h3>";
        echo "<p><strong>Estado:</strong> No disponible</p>";
    }
    echo "</div>";
}

echo "</div>";

// Summary
echo "<div class=\"summary\">";
echo "<h2>📊 Resumen de Pruebas</h2>";
$success_rate = ($tests_passed / $total_tests) * 100;

if ($success_rate >= 90) {
    echo "<div style=\"color: #28a745; font-size: 1.2rem; font-weight: bold;\">";
    echo "🎉 ¡EXCELENTE! Todas las pruebas críticas pasaron ($tests_passed/$total_tests - " . round($success_rate) . "%)";
    echo "</div>";
    echo "<p>El plugin SoloYLibre Gallery Pro está funcionando perfectamente.</p>";
} elseif ($success_rate >= 70) {
    echo "<div style=\"color: #ffc107; font-size: 1.2rem; font-weight: bold;\">";
    echo "⚠️ BUENO - La mayoría de pruebas pasaron ($tests_passed/$total_tests - " . round($success_rate) . "%)";
    echo "</div>";
    echo "<p>El plugin funciona con algunas limitaciones menores.</p>";
} else {
    echo "<div style=\"color: #dc3545; font-size: 1.2rem; font-weight: bold;\">";
    echo "❌ ATENCIÓN - Varias pruebas fallaron ($tests_passed/$total_tests - " . round($success_rate) . "%)";
    echo "</div>";
    echo "<p>Se requiere atención para solucionar problemas.</p>";
}
echo "</div>";

// Action Links
echo "<h2>🔗 Acciones Disponibles</h2>";
echo "<div style=\"text-align: center;\">";
echo "<a href=\"auto-login-soloylibre.php\" class=\"btn\">🔑 Login Automático</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-simple-wizard\" class=\"btn\">🧙‍♂️ Wizard Simple</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-statistics\" class=\"btn\">📊 Estadísticas</a>";
echo "<a href=\"monitor-errores.php\" class=\"btn\">📊 Monitor Errores</a>";
echo "</div>";

// Contact Info
echo "<hr>";
echo "<div style=\"background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 25px; border-radius: 15px; text-align: center;\">";
echo "<h3>📞 SoloYLibre Photography</h3>";
echo "<h4>👨‍💻 Jose L Encarnacion (JoseTusabe)</h4>";
echo "<p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
echo "<p>📞 718-713-5500 | 📧 <EMAIL></p>";
echo "<div style=\"margin-top: 15px;\">";
echo "<span style=\"margin: 0 10px;\">🌐 josetusabe.com</span>";
echo "<span style=\"margin: 0 10px;\">🌐 soloylibre.com</span>";
echo "<span style=\"margin: 0 10px;\">📸 1and1photo.com</span>";
echo "<span style=\"margin: 0 10px;\">👨‍💼 joselencarnacion.com</span>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>