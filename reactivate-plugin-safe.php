<?php
/**
 * Reactivar plugin SoloYLibre de forma segura
 * Con configuración inicial y estadísticas
 */

echo "🔧 REACTIVANDO PLUGIN SOLOYLIBRE GALLERY PRO\n\n";

// Configuración de la base de datos
$host = 'localhost';
$username = 'root';
$password = 'root';
$database = 'wp';

try {
    $mysqli = new mysqli($host, $username, $password, $database);
    
    if ($mysqli->connect_error) {
        die("Error de conexión: " . $mysqli->connect_error);
    }
    
    echo "✅ Conectado a la base de datos\n";
    
    // Obtener plugins activos actuales
    $query = "SELECT option_value FROM wp_options WHERE option_name = 'active_plugins'";
    $result = $mysqli->query($query);
    
    if ($result && $row = $result->fetch_assoc()) {
        $active_plugins = unserialize($row['option_value']);
        
        echo "📋 Plugins activos actuales:\n";
        foreach ($active_plugins as $plugin) {
            echo "   - $plugin\n";
        }
        
        // Verificar si SoloYLibre ya está activo
        $soloylibre_active = false;
        foreach ($active_plugins as $plugin) {
            if (strpos($plugin, 'soloylibre') !== false || strpos($plugin, 'Archive') !== false) {
                $soloylibre_active = true;
                break;
            }
        }
        
        if (!$soloylibre_active) {
            // Agregar el plugin SoloYLibre
            $active_plugins[] = 'Archive/soloylibre-gallery-plugin.php';
            $new_plugins_serialized = serialize($active_plugins);
            
            $update_query = "UPDATE wp_options SET option_value = ? WHERE option_name = 'active_plugins'";
            $stmt = $mysqli->prepare($update_query);
            $stmt->bind_param('s', $new_plugins_serialized);
            
            if ($stmt->execute()) {
                echo "✅ Plugin SoloYLibre reactivado exitosamente\n";
            } else {
                echo "❌ Error al reactivar el plugin\n";
            }
            
            $stmt->close();
        } else {
            echo "ℹ️ Plugin SoloYLibre ya está activo\n";
        }
        
        // Configurar opciones iniciales si no existen
        echo "\n⚙️ Configurando opciones iniciales...\n";
        
        $initial_options = array(
            array('soloylibre_gallery_photographer_name', 'Jose L Encarnacion'),
            array('soloylibre_gallery_photographer_alias', 'JoseTusabe'),
            array('soloylibre_gallery_photographer_brand', 'SoloYLibre Photography'),
            array('soloylibre_gallery_photographer_email', '<EMAIL>'),
            array('soloylibre_gallery_photographer_phone', '************'),
            array('soloylibre_gallery_photographer_location', 'San José de Ocoa, República Dominicana / USA'),
            array('soloylibre_gallery_gallery_style', 'dominican'),
            array('soloylibre_gallery_photos_per_page', '12'),
            array('soloylibre_gallery_enable_infinite_scroll', '1'),
            array('soloylibre_gallery_enable_lightbox', '1'),
            array('soloylibre_gallery_show_photographer_info', '1'),
            array('soloylibre_gallery_dominican_theme', '1')
        );
        
        foreach ($initial_options as $option) {
            $option_name = $option[0];
            $option_value = $option[1];
            
            // Verificar si la opción ya existe
            $check_query = "SELECT COUNT(*) as count FROM wp_options WHERE option_name = ?";
            $check_stmt = $mysqli->prepare($check_query);
            $check_stmt->bind_param('s', $option_name);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            $check_row = $check_result->fetch_assoc();
            
            if ($check_row['count'] == 0) {
                // Insertar nueva opción
                $insert_query = "INSERT INTO wp_options (option_name, option_value, autoload) VALUES (?, ?, 'yes')";
                $insert_stmt = $mysqli->prepare($insert_query);
                $insert_stmt->bind_param('ss', $option_name, $option_value);
                
                if ($insert_stmt->execute()) {
                    echo "   ✅ Opción creada: $option_name = $option_value\n";
                } else {
                    echo "   ❌ Error creando opción: $option_name\n";
                }
                
                $insert_stmt->close();
            } else {
                echo "   ℹ️ Opción ya existe: $option_name\n";
            }
            
            $check_stmt->close();
        }
        
        // Crear sitios web como array serializado
        $websites = array('josetusabe.com', 'soloylibre.com', '1and1photo.com', 'joselencarnacion.com');
        $websites_serialized = serialize($websites);
        
        $websites_query = "INSERT INTO wp_options (option_name, option_value, autoload) VALUES ('soloylibre_gallery_websites', ?, 'yes') 
                          ON DUPLICATE KEY UPDATE option_value = ?";
        $websites_stmt = $mysqli->prepare($websites_query);
        $websites_stmt->bind_param('ss', $websites_serialized, $websites_serialized);
        
        if ($websites_stmt->execute()) {
            echo "   ✅ Sitios web configurados\n";
        }
        
        $websites_stmt->close();
        
    } else {
        echo "❌ Error al consultar plugins activos\n";
    }
    
    $mysqli->close();
    
    echo "\n🎉 ¡Plugin reactivado y configurado!\n";
    echo "\n🔗 URLs para probar:\n";
    echo "   🌐 Admin: http://localhost:8888/wp/wordpress/wp-admin/\n";
    echo "   🧙‍♂️ Wizard: http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-wizard\n";
    echo "   📊 Estadísticas: http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-statistics\n";
    echo "   📸 Fotos: http://localhost:8888/wp/wordpress/wp-admin/edit.php?post_type=soloylibre_photo\n";
    
    echo "\n📋 Funcionalidades disponibles:\n";
    echo "   ✅ Configuración inicial automática\n";
    echo "   ✅ Wizard de pantalla completa\n";
    echo "   ✅ Dashboard de estadísticas profesionales\n";
    echo "   ✅ Sistema basado en posts de WordPress\n";
    echo "   ✅ Información del fotógrafo preconfigurada\n";
    echo "   ✅ Temática dominicana integrada\n";
    
    echo "\n📞 Información del fotógrafo:\n";
    echo "   👨‍💻 Jose L Encarnacion (JoseTusabe)\n";
    echo "   🏢 SoloYLibre Photography\n";
    echo "   📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸\n";
    echo "   📞 ************\n";
    echo "   📧 <EMAIL>\n";
    echo "   🌐 josetusabe.com | soloylibre.com | 1and1photo.com | joselencarnacion.com\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
