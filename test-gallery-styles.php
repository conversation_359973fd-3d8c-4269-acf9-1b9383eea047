<?php
require_once("wp-load.php");

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🎨 Prueba de Estilos de Galerías - SoloYLibre</title>";
echo "<meta charset=\"UTF-8\">";
echo "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">";

wp_head();

echo "<style>";
echo "body { margin: 0; padding: 20px; background: #f5f7fa; }";
echo ".container { max-width: 1200px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }";
echo ".style-section { margin: 40px 0; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }";
echo ".style-title { color: #CE1126; font-size: 1.5rem; margin-bottom: 15px; }";
echo ".shortcode-display { background: #2d3748; color: #e2e8f0; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class=\"container\">";
echo "<div class=\"header\">";
echo "<h1>🎨 Prueba de Estilos de Galerías SoloYLibre</h1>";
echo "<p>Verificación de los 5 estilos diferentes implementados</p>";
echo "</div>";

// Obtener fotos de ejemplo
$photos = get_posts(array(
    "post_type" => "attachment",
    "post_mime_type" => "image",
    "posts_per_page" => 6,
    "post_status" => "inherit"
));

if (!empty($photos)) {
    $photo_ids = implode(",", wp_list_pluck($photos, "ID"));
    
    $styles = array(
        "dominican" => "🇩🇴 Estilo Dominicano",
        "grid" => "📱 Grid Moderno", 
        "masonry" => "🧱 Masonry (Pinterest)",
        "carousel" => "🎠 Carousel Horizontal",
        "professional" => "💼 Profesional"
    );
    
    foreach ($styles as $style => $name) {
        echo "<div class=\"style-section\">";
        echo "<h2 class=\"style-title\">$name</h2>";
        echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"$style\" ids=\"$photo_ids\" columns=\"3\" enable_interactions=\"true\"]</div>";
        
        echo do_shortcode("[soloylibre_gallery style=\"$style\" ids=\"$photo_ids\" columns=\"3\" enable_interactions=\"true\"]");
        
        echo "</div>";
    }
} else {
    echo "<div class=\"style-section\">";
    echo "<h2>❌ No hay fotos disponibles</h2>";
    echo "<p>Sube algunas imágenes a la biblioteca de medios para probar los estilos.</p>";
    echo "</div>";
}

echo "</div>";

wp_footer();

echo "</body>";
echo "</html>";
?>