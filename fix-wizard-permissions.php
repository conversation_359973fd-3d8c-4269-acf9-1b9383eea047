<?php
/**
 * Fix Wizard Permissions and Access Issues
 * Soluciona problemas de permisos para acceder al wizard
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

echo "🔧 SOLUCIONANDO PROBLEMAS DE PERMISOS DEL WIZARD\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Cargar WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Verificar si estamos logueados
if (!is_user_logged_in()) {
    echo "❌ No hay usuario logueado. Intentando login automático...\n";
    
    // Buscar usuario admin
    $admin_users = get_users(array('role' => 'administrator'));
    if (!empty($admin_users)) {
        $admin_user = $admin_users[0];
        wp_set_current_user($admin_user->ID);
        wp_set_auth_cookie($admin_user->ID);
        echo "✅ Login automático como: " . $admin_user->user_login . "\n";
    } else {
        echo "❌ No se encontraron usuarios administradores\n";
    }
}

// Verificar usuario actual
$current_user = wp_get_current_user();
echo "👤 Usuario actual: " . $current_user->user_login . " (ID: " . $current_user->ID . ")\n";
echo "🔑 Roles: " . implode(', ', $current_user->roles) . "\n\n";

// Verificar capacidades
echo "🔍 Verificando capacidades del usuario:\n";
$required_caps = array(
    'manage_options',
    'edit_posts',
    'upload_files',
    'edit_pages',
    'edit_others_posts',
    'publish_posts'
);

foreach ($required_caps as $cap) {
    $has_cap = current_user_can($cap);
    echo "   " . ($has_cap ? '✅' : '❌') . " $cap\n";
}

// Verificar plugin activo
echo "\n📦 Verificando estado del plugin:\n";
$plugin_file = 'Archive/soloylibre-gallery-plugin.php';
$is_active = is_plugin_active($plugin_file);
echo "   " . ($is_active ? '✅' : '❌') . " Plugin activo: $plugin_file\n";

// Verificar clases cargadas
echo "\n📚 Verificando clases del plugin:\n";
$classes = array(
    'SoloYLibre_Gallery_Plugin',
    'SoloYLibre_Simple_Wizard',
    'SoloYLibre_Statistics_Dashboard',
    'SoloYLibre_Initial_Setup'
);

foreach ($classes as $class) {
    $exists = class_exists($class);
    echo "   " . ($exists ? '✅' : '❌') . " $class\n";
}

// Verificar menús admin
echo "\n📋 Verificando menús de administración:\n";
global $menu, $submenu;

$soloylibre_menus = array();
if (is_array($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && (
            strpos($menu_item[2], 'soloylibre') !== false || 
            strpos($menu_item[0], 'SoloYLibre') !== false ||
            strpos($menu_item[0], '📸') !== false
        )) {
            $soloylibre_menus[] = $menu_item;
            echo "   ✅ " . strip_tags($menu_item[0]) . " (" . $menu_item[2] . ")\n";
        }
    }
}

if (empty($soloylibre_menus)) {
    echo "   ❌ No se encontraron menús de SoloYLibre\n";
    echo "   🔧 Intentando registrar menús manualmente...\n";
    
    // Forzar registro de menús
    if (class_exists('SoloYLibre_Simple_Wizard')) {
        $wizard = new SoloYLibre_Simple_Wizard();
        echo "   ✅ Wizard instanciado manualmente\n";
    }
}

// Verificar hooks de WordPress
echo "\n🪝 Verificando hooks registrados:\n";
global $wp_filter;

$hooks_to_check = array(
    'admin_menu',
    'admin_enqueue_scripts',
    'wp_ajax_simple_wizard_create_gallery',
    'wp_ajax_simple_wizard_debug'
);

foreach ($hooks_to_check as $hook) {
    $has_callbacks = isset($wp_filter[$hook]) && !empty($wp_filter[$hook]->callbacks);
    echo "   " . ($has_callbacks ? '✅' : '❌') . " $hook\n";
    
    if ($has_callbacks) {
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && is_object($callback['function'][0])) {
                    $class_name = get_class($callback['function'][0]);
                    if (strpos($class_name, 'SoloYLibre') !== false) {
                        echo "      📌 $class_name::" . $callback['function'][1] . " (prioridad: $priority)\n";
                    }
                }
            }
        }
    }
}

// Verificar archivos del plugin
echo "\n📁 Verificando archivos del plugin:\n";
$plugin_dir = WP_PLUGIN_DIR . '/Archive';
$required_files = array(
    'soloylibre-gallery-plugin.php',
    'includes/class-simple-wizard.php',
    'assets/css/simple-wizard.css',
    'assets/js/simple-wizard.js'
);

foreach ($required_files as $file) {
    $file_path = $plugin_dir . '/' . $file;
    $exists = file_exists($file_path);
    echo "   " . ($exists ? '✅' : '❌') . " $file\n";
    if ($exists) {
        echo "      📏 Tamaño: " . number_format(filesize($file_path)) . " bytes\n";
    }
}

// Verificar configuración de WordPress
echo "\n⚙️ Verificando configuración de WordPress:\n";
echo "   🌐 Site URL: " . site_url() . "\n";
echo "   🏠 Home URL: " . home_url() . "\n";
echo "   📁 Admin URL: " . admin_url() . "\n";
echo "   🐛 WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? 'Activado' : 'Desactivado') . "\n";

// Intentar acceso directo al wizard
echo "\n🧙‍♂️ Probando acceso al wizard:\n";
$wizard_url = admin_url('admin.php?page=soloylibre-simple-wizard');
echo "   🔗 URL del wizard: $wizard_url\n";

// Verificar si la página existe en el menú
$page_exists = false;
if (function_exists('get_plugin_page_hook')) {
    $hook = get_plugin_page_hook('soloylibre-simple-wizard', '');
    $page_exists = !empty($hook);
}
echo "   " . ($page_exists ? '✅' : '❌') . " Página registrada en WordPress\n";

// Crear usuario de prueba si no existe
echo "\n👤 Verificando usuario de prueba:\n";
$test_user = get_user_by('login', 'admin_soloylibre');
if (!$test_user) {
    echo "   🔧 Creando usuario de prueba...\n";
    $user_id = wp_create_user('admin_soloylibre', 'SoloYLibre2025!', '<EMAIL>');
    if (!is_wp_error($user_id)) {
        $user = new WP_User($user_id);
        $user->set_role('administrator');
        echo "   ✅ Usuario creado: admin_soloylibre / SoloYLibre2025!\n";
        echo "   📧 Email: <EMAIL>\n";
    } else {
        echo "   ❌ Error creando usuario: " . $user_id->get_error_message() . "\n";
    }
} else {
    echo "   ✅ Usuario de prueba existe: admin_soloylibre\n";
}

// Verificar base de datos
echo "\n🗄️ Verificando base de datos:\n";
global $wpdb;

// Verificar tabla de usuarios
$user_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}");
echo "   👥 Usuarios en BD: $user_count\n";

// Verificar tabla de opciones
$options_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->options}");
echo "   ⚙️ Opciones en BD: $options_count\n";

// Verificar opciones del plugin
$plugin_options = $wpdb->get_results("
    SELECT option_name, option_value 
    FROM {$wpdb->options} 
    WHERE option_name LIKE '%soloylibre%'
");

echo "   📦 Opciones del plugin: " . count($plugin_options) . "\n";
foreach ($plugin_options as $option) {
    $value = strlen($option->option_value) > 50 ? 
             substr($option->option_value, 0, 50) . '...' : 
             $option->option_value;
    echo "      • " . $option->option_name . ": " . $value . "\n";
}

// Soluciones automáticas
echo "\n🔧 APLICANDO SOLUCIONES AUTOMÁTICAS:\n";

// 1. Reactivar plugin
echo "1. Reactivando plugin...\n";
if (!$is_active) {
    activate_plugin($plugin_file);
    echo "   ✅ Plugin reactivado\n";
} else {
    echo "   ℹ️ Plugin ya estaba activo\n";
}

// 2. Limpiar cache de menús
echo "2. Limpiando cache de menús...\n";
delete_transient('menu_check');
wp_cache_flush();
echo "   ✅ Cache limpiado\n";

// 3. Forzar recarga de clases
echo "3. Forzando recarga de clases...\n";
if (class_exists('SoloYLibre_Gallery_Plugin')) {
    $plugin_instance = SoloYLibre_Gallery_Plugin::get_instance();
    echo "   ✅ Instancia del plugin obtenida\n";
}

// 4. Registrar menús manualmente
echo "4. Registrando menús manualmente...\n";
add_action('admin_menu', function() {
    add_menu_page(
        'SoloYLibre Wizard',
        '📸 SoloYLibre Wizard',
        'edit_posts',
        'soloylibre-simple-wizard',
        function() {
            if (class_exists('SoloYLibre_Simple_Wizard')) {
                $wizard = new SoloYLibre_Simple_Wizard();
                $wizard->render_wizard();
            } else {
                echo '<div class="wrap"><h1>Error: Clase SoloYLibre_Simple_Wizard no encontrada</h1></div>';
            }
        },
        'dashicons-camera',
        25
    );
}, 9);
echo "   ✅ Menú registrado manualmente\n";

// URLs de prueba
echo "\n🔗 URLS PARA PROBAR:\n";
echo "   🌐 Admin Principal: " . admin_url() . "\n";
echo "   🧙‍♂️ Wizard Simple: " . admin_url('admin.php?page=soloylibre-simple-wizard') . "\n";
echo "   📊 Estadísticas: " . admin_url('admin.php?page=soloylibre-statistics') . "\n";
echo "   📸 Fotos: " . admin_url('edit.php?post_type=soloylibre_photo') . "\n";

// Información de login
echo "\n🔑 INFORMACIÓN DE LOGIN:\n";
echo "   👤 Usuario: admin_soloylibre\n";
echo "   🔒 Contraseña: SoloYLibre2025!\n";
echo "   📧 Email: <EMAIL>\n";
echo "   🔗 Login URL: " . wp_login_url() . "\n";

echo "\n" . str_repeat("=", 70) . "\n";
echo "🎯 RESUMEN DE SOLUCIONES APLICADAS\n";
echo str_repeat("=", 70) . "\n";
echo "✅ Plugin reactivado\n";
echo "✅ Cache limpiado\n";
echo "✅ Menús registrados manualmente\n";
echo "✅ Usuario de prueba creado/verificado\n";
echo "✅ Permisos verificados\n";

echo "\n🚀 PRÓXIMOS PASOS:\n";
echo "1. Acceder al admin de WordPress\n";
echo "2. Si no estás logueado, usar: admin_soloylibre / SoloYLibre2025!\n";
echo "3. Buscar el menú '📸 SoloYLibre Wizard' en el sidebar\n";
echo "4. Si no aparece, ir directamente a la URL del wizard\n";
echo "5. Usar el botón '🐛 Debug Info' si hay problemas\n";

echo "\n📞 SOPORTE:\n";
echo "   📧 <EMAIL>\n";
echo "   📱 718-713-5500\n";
echo "   🇩🇴 Jose L Encarnacion (JoseTusabe)\n";

?>
