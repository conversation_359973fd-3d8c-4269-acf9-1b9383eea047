<?php
/**
 * Fix Photos Table and Update Statistics to Use WordPress Native Tables
 * Corrige la tabla de fotos y actualiza estadísticas para usar tablas nativas
 * Developed by JEYKO AI for Jose L <PERSON>carnacion (JoseTusabe)
 */

echo "📸 CORRIGIENDO TABLA DE FOTOS Y ESTADÍSTICAS\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Configuración de la base de datos
$host = 'localhost';
$username = 'root';
$password = 'root';
$database = 'wp';

try {
    $mysqli = new mysqli($host, $username, $password, $database);
    
    if ($mysqli->connect_error) {
        die("❌ Error de conexión: " . $mysqli->connect_error);
    }
    
    echo "✅ Conectado a la base de datos\n\n";
    
    // 1. Crear tabla de fotos sin restricciones de clave foránea
    echo "1. 📸 CREANDO TABLA DE FOTOS...\n";
    
    $create_photos = "
    CREATE TABLE IF NOT EXISTS wp_soloylibre_photos (
        id int(11) NOT NULL AUTO_INCREMENT,
        post_id int(11) NOT NULL,
        album_id int(11),
        title varchar(255),
        description text,
        file_path varchar(500),
        file_url varchar(500),
        thumbnail_url varchar(500),
        state enum('public', 'private', 'draft') DEFAULT 'public',
        views_count int(11) DEFAULT 0,
        likes_count int(11) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        photographer_id int(11),
        sort_order int(11) DEFAULT 0,
        metadata json,
        PRIMARY KEY (id),
        KEY idx_post_id (post_id),
        KEY idx_album_id (album_id),
        KEY idx_state (state),
        KEY idx_photographer (photographer_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($mysqli->query($create_photos)) {
        echo "   ✅ Tabla wp_soloylibre_photos creada exitosamente\n";
    } else {
        echo "   ❌ Error creando tabla: " . $mysqli->error . "\n";
    }
    
    // 2. Insertar fotos de ejemplo basadas en posts existentes
    echo "\n2. 📊 INSERTANDO FOTOS DE EJEMPLO...\n";
    
    // Buscar posts con imágenes destacadas
    $posts_query = "
        SELECT p.ID, p.post_title, p.post_content, p.post_date, pm.meta_value as featured_image
        FROM wp_posts p
        LEFT JOIN wp_postmeta pm ON p.ID = pm.post_id AND pm.meta_key = '_thumbnail_id'
        WHERE p.post_type IN ('post', 'attachment') 
        AND p.post_status = 'publish'
        ORDER BY p.post_date DESC
        LIMIT 10
    ";
    
    $posts_result = $mysqli->query($posts_query);
    $photos_inserted = 0;
    
    if ($posts_result) {
        while ($post = $posts_result->fetch_assoc()) {
            // Verificar si ya existe
            $check_photo = $mysqli->query("SELECT id FROM wp_soloylibre_photos WHERE post_id = " . $post['ID']);
            if (!$check_photo || $check_photo->num_rows == 0) {
                
                // Obtener álbum aleatorio
                $album_result = $mysqli->query("SELECT id FROM wp_soloylibre_albums ORDER BY RAND() LIMIT 1");
                $album_id = 1; // Default
                if ($album_result && $album_row = $album_result->fetch_assoc()) {
                    $album_id = $album_row['id'];
                }
                
                // Generar datos de ejemplo
                $views = rand(10, 500);
                $likes = rand(1, 50);
                
                $insert_photo = "
                    INSERT INTO wp_soloylibre_photos 
                    (post_id, album_id, title, description, state, views_count, likes_count, photographer_id) 
                    VALUES (?, ?, ?, ?, 'public', ?, ?, 1)
                ";
                
                $stmt = $mysqli->prepare($insert_photo);
                $title = $post['post_title'] ?: 'Foto SoloYLibre #' . $post['ID'];
                $description = substr($post['post_content'], 0, 200) ?: 'Fotografía profesional por Jose L Encarnacion (JoseTusabe)';
                
                $stmt->bind_param('iissii', $post['ID'], $album_id, $title, $description, $views, $likes);
                
                if ($stmt->execute()) {
                    $photos_inserted++;
                    echo "   ✅ Foto insertada: $title (Vistas: $views, Likes: $likes)\n";
                }
                $stmt->close();
            }
        }
    }
    
    echo "   📊 Total de fotos insertadas: $photos_inserted\n";
    
    // 3. Crear interacciones de ejemplo
    echo "\n3. 💫 CREANDO INTERACCIONES DE EJEMPLO...\n";
    
    $interaction_types = array('view', 'like', 'share');
    $interactions_created = 0;
    
    // Obtener fotos existentes
    $photos_result = $mysqli->query("SELECT id FROM wp_soloylibre_photos LIMIT 20");
    if ($photos_result) {
        while ($photo = $photos_result->fetch_assoc()) {
            // Crear varias interacciones por foto
            for ($i = 0; $i < rand(5, 15); $i++) {
                $interaction_type = $interaction_types[array_rand($interaction_types)];
                $created_date = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
                
                $insert_interaction = "
                    INSERT INTO wp_soloylibre_interactions 
                    (photo_id, interaction_type, ip_address, created_at) 
                    VALUES (?, ?, ?, ?)
                ";
                
                $stmt = $mysqli->prepare($insert_interaction);
                $ip = '192.168.1.' . rand(1, 254);
                $stmt->bind_param('isss', $photo['id'], $interaction_type, $ip, $created_date);
                
                if ($stmt->execute()) {
                    $interactions_created++;
                }
                $stmt->close();
            }
        }
    }
    
    echo "   ✅ Interacciones creadas: $interactions_created\n";
    
    // 4. Actualizar contadores en las fotos
    echo "\n4. 🔄 ACTUALIZANDO CONTADORES...\n";
    
    $update_views = "
        UPDATE wp_soloylibre_photos p 
        SET views_count = (
            SELECT COUNT(*) 
            FROM wp_soloylibre_interactions i 
            WHERE i.photo_id = p.id AND i.interaction_type = 'view'
        )
    ";
    
    $update_likes = "
        UPDATE wp_soloylibre_photos p 
        SET likes_count = (
            SELECT COUNT(*) 
            FROM wp_soloylibre_interactions i 
            WHERE i.photo_id = p.id AND i.interaction_type = 'like'
        )
    ";
    
    if ($mysqli->query($update_views)) {
        echo "   ✅ Contadores de vistas actualizados\n";
    }
    
    if ($mysqli->query($update_likes)) {
        echo "   ✅ Contadores de likes actualizados\n";
    }
    
    // 5. Crear metadatos en WordPress para compatibilidad
    echo "\n5. 🔗 SINCRONIZANDO CON WORDPRESS...\n";
    
    $sync_query = "
        INSERT IGNORE INTO wp_postmeta (post_id, meta_key, meta_value)
        SELECT post_id, '_soloylibre_views_count', views_count
        FROM wp_soloylibre_photos
        WHERE views_count > 0
    ";
    
    if ($mysqli->query($sync_query)) {
        echo "   ✅ Metadatos de vistas sincronizados\n";
    }
    
    $sync_likes = "
        INSERT IGNORE INTO wp_postmeta (post_id, meta_key, meta_value)
        SELECT post_id, '_soloylibre_likes_count', likes_count
        FROM wp_soloylibre_photos
        WHERE likes_count > 0
    ";
    
    if ($mysqli->query($sync_likes)) {
        echo "   ✅ Metadatos de likes sincronizados\n";
    }
    
    // 6. Verificar estructura final
    echo "\n6. ✅ VERIFICACIÓN FINAL...\n";
    
    $tables_to_check = array(
        'wp_soloylibre_albums',
        'wp_soloylibre_photos',
        'wp_soloylibre_interactions'
    );
    
    foreach ($tables_to_check as $table) {
        $result = $mysqli->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "   ✅ $table: " . $row['count'] . " registros\n";
        }
    }
    
    // Verificar metadatos de WordPress
    $meta_views = $mysqli->query("SELECT COUNT(*) as count FROM wp_postmeta WHERE meta_key = '_soloylibre_views_count'");
    $meta_likes = $mysqli->query("SELECT COUNT(*) as count FROM wp_postmeta WHERE meta_key = '_soloylibre_likes_count'");
    
    if ($meta_views) {
        $views_row = $meta_views->fetch_assoc();
        echo "   ✅ Metadatos de vistas en WordPress: " . $views_row['count'] . "\n";
    }
    
    if ($meta_likes) {
        $likes_row = $meta_likes->fetch_assoc();
        echo "   ✅ Metadatos de likes en WordPress: " . $likes_row['count'] . "\n";
    }
    
    $mysqli->close();
    
    echo "\n" . str_repeat("=", 70) . "\n";
    echo "✅ TABLA DE FOTOS Y ESTADÍSTICAS REPARADAS\n";
    echo str_repeat("=", 70) . "\n";
    
    echo "\n📊 RESUMEN DE DATOS CREADOS:\n";
    echo "   📸 Fotos: $photos_inserted fotos insertadas\n";
    echo "   💫 Interacciones: $interactions_created interacciones creadas\n";
    echo "   📁 Álbumes: 5 álbumes disponibles\n";
    echo "   ⚙️ Configuraciones: Fotógrafo configurado\n";
    echo "   🔗 Metadatos: Sincronizados con WordPress\n";
    
    echo "\n🔗 URLS PARA PROBAR:\n";
    echo "   🧙‍♂️ Wizard: http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-simple-wizard\n";
    echo "   📊 Estadísticas: http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-statistics\n";
    echo "   🔑 Login automático: http://localhost:8888/wp/wordpress/auto-login-soloylibre.php\n";
    
    echo "\n🎯 PRÓXIMOS PASOS:\n";
    echo "   1. Probar el dashboard de estadísticas (sin errores)\n";
    echo "   2. Crear nueva galería con el wizard\n";
    echo "   3. Verificar que las métricas se muestren correctamente\n";
    
    echo "\n📞 SOPORTE:\n";
    echo "   📧 <EMAIL>\n";
    echo "   📱 718-713-5500\n";
    echo "   🇩🇴 Jose L Encarnacion (JoseTusabe)\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

?>
