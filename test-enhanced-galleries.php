<?php
/**
 * Test Enhanced Galleries
 * Prueba de galerías mejoradas
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

// Cargar WordPress
require_once('wp-load.php');

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🎨 Galerías <PERSON> - SoloYLibre Gallery Pro</title>";
echo "<meta charset=\"UTF-8\">";
echo "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">";

// Cargar estilos de WordPress
wp_head();

echo "<style>";
echo "body { margin: 0; padding: 20px; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); min-height: 100vh; }";
echo ".container { max-width: 1200px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; }";
echo ".example-section { margin: 40px 0; padding: 30px; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }";
echo ".example-title { color: #CE1126; font-size: 1.8rem; margin-bottom: 15px; font-weight: 700; }";
echo ".example-description { color: #6c757d; margin-bottom: 20px; font-size: 1.1rem; }";
echo ".shortcode-display { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-family: monospace; margin: 15px 0; overflow-x: auto; }";
echo ".btn { background: #CE1126; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; display: inline-block; margin: 10px 5px; font-weight: 600; transition: all 0.3s ease; border: none; cursor: pointer; }";
echo ".btn:hover { background: #002D62; transform: translateY(-2px); }";
echo ".stats-info { background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #0c5460; }";
echo "</style>";

// Cargar confetti
echo "<script src=\"https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js\"></script>";

echo "</head>";
echo "<body>";

echo "<div class=\"container\">";

echo "<div class=\"header\">";
echo "<h1>🎨 SoloYLibre Gallery Pro - Galerías Mejoradas</h1>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe) - Versión 4.0.3</p>";
echo "<p>Ejemplos de galerías con efectos avanzados, interacciones y confetti</p>";
echo "<button class=\"btn\" onclick=\"testConfetti()\">🎉 Probar Confetti</button>";
echo "</div>";

// Obtener algunas fotos de ejemplo
$sample_photos = get_posts(array(
    'post_type' => 'attachment',
    'post_mime_type' => 'image',
    'posts_per_page' => 12,
    'post_status' => 'inherit',
    'orderby' => 'date',
    'order' => 'DESC'
));

if (empty($sample_photos)) {
    echo "<div class=\"example-section\">";
    echo "<h2>❌ No hay fotos disponibles</h2>";
    echo "<p>No se encontraron imágenes en la biblioteca de medios de WordPress.</p>";
    echo "<p>Por favor, sube algunas imágenes para probar las galerías.</p>";
    echo "</div>";
} else {
    $photo_ids = array_slice(wp_list_pluck($sample_photos, 'ID'), 0, 9);
    $photo_ids_str = implode(',', $photo_ids);
    
    echo "<div class=\"stats-info\">";
    echo "<h4>📊 Información del Sistema</h4>";
    echo "<p><strong>Fotos disponibles:</strong> " . count($sample_photos) . "</p>";
    echo "<p><strong>Fotos en ejemplos:</strong> " . count($photo_ids) . "</p>";
    echo "<p><strong>Plugin versión:</strong> 4.0.3</p>";
    echo "<p><strong>Características:</strong> Confetti ✅ | Interacciones ✅ | Lightbox mejorado ✅</p>";
    echo "</div>";
    
    // Ejemplo 1: Galería Estilo Dominicano
    echo "<div class=\"example-section\">";
    echo "<h2 class=\"example-title\">🇩🇴 Galería Estilo Dominicano</h2>";
    echo "<p class=\"example-description\">Galería con colores de la bandera dominicana, efectos de confetti y todas las interacciones habilitadas.</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"dominican\" ids=\"$photo_ids_str\" columns=\"3\" enable_interactions=\"true\" auto_confetti=\"true\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"dominican\" ids=\"$photo_ids_str\" columns=\"3\" enable_interactions=\"true\" auto_confetti=\"true\"]");
    echo "</div>";
    
    // Ejemplo 2: Galería Grid Moderna
    echo "<div class=\"example-section\">";
    echo "<h2 class=\"example-title\">📱 Galería Grid Moderna</h2>";
    echo "<p class=\"example-description\">Grid limpio y moderno con hover effects y lightbox mejorado.</p>";
    $grid_ids = implode(',', array_slice($photo_ids, 0, 6));
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"grid\" ids=\"$grid_ids\" columns=\"2\" hover_effect=\"fade\" border_radius=\"15\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"grid\" ids=\"$grid_ids\" columns=\"2\" hover_effect=\"fade\" border_radius=\"15\"]");
    echo "</div>";
    
    // Ejemplo 3: Galería Profesional
    echo "<div class=\"example-section\">";
    echo "<h2 class=\"example-title\">💼 Galería Profesional</h2>";
    echo "<p class=\"example-description\">Estilo profesional con metadatos, títulos y todas las opciones habilitadas.</p>";
    $pro_ids = implode(',', array_slice($photo_ids, 0, 4));
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"professional\" ids=\"$pro_ids\" columns=\"4\" show_metadata=\"true\" show_title=\"true\" spacing=\"25\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"professional\" ids=\"$pro_ids\" columns=\"4\" show_metadata=\"true\" show_title=\"true\" spacing=\"25\"]");
    echo "</div>";
    
    // Ejemplo 4: Galería Auto-cargada
    echo "<div class=\"example-section\">";
    echo "<h2 class=\"example-title\">🔄 Galería Auto-cargada</h2>";
    echo "<p class=\"example-description\">Galería que carga automáticamente las fotos más recientes (sin especificar IDs).</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"dominican\" columns=\"3\" auto_confetti=\"true\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"dominican\" columns=\"3\" auto_confetti=\"true\"]");
    echo "</div>";
}

// Información técnica
echo "<div class=\"example-section\">";
echo "<h2 class=\"example-title\">🔧 Información Técnica</h2>";
echo "<div style=\"display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;\">";

echo "<div>";
echo "<h4>🎨 Características Visuales</h4>";
echo "<ul>";
echo "<li>✅ Efectos de confetti automáticos</li>";
echo "<li>✅ Animaciones CSS avanzadas</li>";
echo "<li>✅ Lightbox mejorado con blur</li>";
echo "<li>✅ Hover effects personalizables</li>";
echo "<li>✅ Gradientes dominicanos</li>";
echo "<li>✅ Responsive design completo</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>📊 Sistema de Interacciones</h4>";
echo "<ul>";
echo "<li>❤️ Likes con confetti</li>";
echo "<li>👁️ Vistas automáticas</li>";
echo "<li>📤 Compartir en redes</li>";
echo "<li>📈 Estadísticas en tiempo real</li>";
echo "<li>💾 Almacenamiento en BD</li>";
echo "<li>🎯 AJAX sin recargar página</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>⚙️ Configuraciones</h4>";
echo "<ul>";
echo "<li>🎊 Confetti activable/desactivable</li>";
echo "<li>📸 Auto-carga de fotos configurable</li>";
echo "<li>🎨 Estilos personalizables</li>";
echo "<li>📱 Columnas responsivas</li>";
echo "<li>🖼️ Tamaños de imagen ajustables</li>";
echo "<li>✍️ Firma del fotógrafo opcional</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</div>";

// Enlaces útiles
echo "<div class=\"example-section\">";
echo "<h2 class=\"example-title\">🔗 Enlaces Útiles</h2>";
echo "<div style=\"text-align: center;\">";
echo "<a href=\"auto-login-soloylibre.php\" class=\"btn\">🔑 Login Automático</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-wizard\" class=\"btn\">🧙‍♂️ Wizard Mejorado</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-settings\" class=\"btn\">⚙️ Configuraciones</a>";
echo "<a href=\"shortcode-examples-and-docs.php\" class=\"btn\">📚 Documentación</a>";
echo "<button class=\"btn\" onclick=\"generateRandomInteractions()\">📊 Generar Interacciones</button>";
echo "</div>";
echo "</div>";

// Footer
echo "<div style=\"background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 25px; border-radius: 15px; text-align: center; margin-top: 30px;\">";
echo "<h3>📞 SoloYLibre Photography</h3>";
echo "<h4>👨‍💻 Jose L Encarnacion (JoseTusabe)</h4>";
echo "<p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
echo "<p>📞 718-713-5500 | 📧 <EMAIL></p>";
echo "<div style=\"margin-top: 15px;\">";
echo "<span style=\"margin: 0 10px;\">🌐 josetusabe.com</span>";
echo "<span style=\"margin: 0 10px;\">🌐 soloylibre.com</span>";
echo "<span style=\"margin: 0 10px;\">📸 1and1photo.com</span>";
echo "<span style=\"margin: 0 10px;\">👨‍💼 joselencarnacion.com</span>";
echo "</div>";
echo "<p style=\"margin-top: 15px; font-style: italic;\">\"Capturando la belleza de República Dominicana, una foto a la vez\" 🇩🇴</p>";
echo "</div>";

echo "</div>"; // Cierre container

// JavaScript personalizado
echo "<script>";
echo "function testConfetti() {";
echo "    if (typeof confetti !== 'undefined') {";
echo "        confetti({";
echo "            particleCount: 100,";
echo "            spread: 70,";
echo "            origin: { y: 0.6 },";
echo "            colors: ['#CE1126', '#002D62', '#FFFFFF']";
echo "        });";
echo "        setTimeout(function() {";
echo "            confetti({";
echo "                particleCount: 50,";
echo "                angle: 60,";
echo "                spread: 55,";
echo "                origin: { x: 0 },";
echo "                colors: ['#CE1126', '#002D62']";
echo "            });";
echo "            confetti({";
echo "                particleCount: 50,";
echo "                angle: 120,";
echo "                spread: 55,";
echo "                origin: { x: 1 },";
echo "                colors: ['#CE1126', '#002D62']";
echo "            });";
echo "        }, 500);";
echo "    } else {";
echo "        alert('¡Confetti activado! 🎉');";
echo "    }";
echo "}";

echo "function generateRandomInteractions() {";
echo "    if (confirm('¿Generar interacciones aleatorias para las fotos mostradas?')) {";
echo "        jQuery.post('" . admin_url('admin-ajax.php') . "', {";
echo "            action: 'generate_interactions',";
echo "            nonce: '" . wp_create_nonce('generate_interactions') . "'";
echo "        }, function(response) {";
echo "            if (response.success) {";
echo "                alert('✅ ' + response.data.message);";
echo "                location.reload();";
echo "            } else {";
echo "                alert('❌ Error: ' + response.data.message);";
echo "            }";
echo "        });";
echo "    }";
echo "}";

echo "// Confetti de bienvenida";
echo "setTimeout(function() {";
echo "    testConfetti();";
echo "}, 1000);";

echo "</script>";

wp_footer();

echo "</body>";
echo "</html>";
?>
