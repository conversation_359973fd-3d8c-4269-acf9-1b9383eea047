<?php
require_once("wp-load.php");
global $wpdb;
$table_name = $wpdb->prefix . "soloylibre_interactions";
echo "🔧 CORRIGIENDO TODOS LOS ERRORES...\n";

// 1. Verificar y agregar columnas faltantes
$columns = $wpdb->get_results("DESCRIBE $table_name");
$existing_columns = array();
foreach ($columns as $column) {
    $existing_columns[] = $column->Field;
}

$required_columns = array(
    "updated_at" => "datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
    "user_ip" => "varchar(45) DEFAULT NULL",
    "user_agent" => "text DEFAULT NULL"
);

foreach ($required_columns as $col_name => $col_definition) {
    if (!in_array($col_name, $existing_columns)) {
        echo "❌ Falta columna $col_name, agregando...\n";
        $result = $wpdb->query("ALTER TABLE $table_name ADD COLUMN $col_name $col_definition");
        if ($result !== false) {
            echo "✅ Columna $col_name agregada\n";
        } else {
            echo "❌ Error agregando $col_name: " . $wpdb->last_error . "\n";
        }
    } else {
        echo "✅ Columna $col_name ya existe\n";
    }
}

// 2. Insertar datos de prueba
echo "\n🧪 INSERTANDO DATOS DE PRUEBA...\n";
$wpdb->query("DELETE FROM $table_name");

$photos = get_posts(array("post_type" => "attachment", "post_mime_type" => "image", "posts_per_page" => 5, "post_status" => "inherit"));
$count = 0;
foreach ($photos as $photo) {
    foreach (array("like", "view", "share") as $type) {
        $wpdb->insert($table_name, array(
            "photo_id" => $photo->ID,
            "interaction_type" => $type,
            "interaction_count" => rand(1, 50),
            "user_ip" => "127.0.0.1",
            "user_agent" => "Test Data",
            "created_at" => current_time("mysql"),
            "updated_at" => current_time("mysql")
        ));
        $count++;
    }
}
echo "✅ $count interacciones de prueba creadas\n";

// 3. Limpiar logs
file_put_contents("wp-content/debug.log", "");
echo "✅ Logs limpiados\n";
echo "🎉 TODOS LOS ERRORES CORREGIDOS\n";
?>
