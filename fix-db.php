<?php
require_once("wp-load.php");
global $wpdb;
$table_name = $wpdb->prefix . "soloylibre_interactions";
echo "🔧 CORRIGIENDO BASE DE DATOS...\n";
$columns = $wpdb->get_results("DESCRIBE $table_name");
$has_interaction_count = false;
foreach ($columns as $column) {
    if ($column->Field === "interaction_count") {
        $has_interaction_count = true;
        break;
    }
}
if (!$has_interaction_count) {
    echo "❌ Falta columna interaction_count, agregando...\n";
    $result = $wpdb->query("ALTER TABLE $table_name ADD COLUMN interaction_count INT(11) DEFAULT 1 AFTER interaction_type");
    if ($result !== false) {
        echo "✅ Columna agregada exitosamente\n";
    } else {
        echo "❌ Error: " . $wpdb->last_error . "\n";
    }
} else {
    echo "✅ Columna interaction_count ya existe\n";
}
file_put_contents("wp-content/debug.log", "");
echo "✅ Logs limpiados\n";
echo "🎉 CORRECCIÓN COMPLETADA\n";
?>
