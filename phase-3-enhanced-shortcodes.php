<?php
/**
 * Phase 3: Enhanced Shortcodes and Frontend Improvements
 * Fase 3: Shortcodes mejorados y mejoras de frontend
 * Developed by JEYKO AI for Jose L <PERSON> (JoseTusabe)
 */

echo "🚀 FASE 3: SHORTCODES MEJORADOS Y FRONTEND\n";
echo "=" . str_repeat("=", 45) . "\n\n";

// 1. Crear shortcode handler completamente mejorado
echo "1. 🏷️ CREANDO SHORTCODE HANDLER MEJORADO...\n";

$enhanced_shortcode = '<?php
/**
 * Enhanced SoloYLibre Gallery Shortcode Handler
 * Manejador mejorado de shortcodes con interacciones
 */

class SoloYLibre_Enhanced_Shortcode {
    
    private $settings;
    
    public function __construct() {
        $this->settings = new SoloYLibre_Settings_Manager();
        
        add_shortcode("soloylibre_gallery", array($this, "render_gallery"));
        add_action("wp_enqueue_scripts", array($this, "enqueue_assets"));
        add_action("wp_ajax_gallery_interaction", array($this, "handle_interaction"));
        add_action("wp_ajax_nopriv_gallery_interaction", array($this, "handle_interaction"));
    }
    
    public function enqueue_assets() {
        wp_enqueue_script("jquery");
        
        // CSS mejorado
        wp_add_inline_style("wp-block-library", $this->get_enhanced_css());
        
        // JavaScript con interacciones
        wp_add_inline_script("jquery", $this->get_enhanced_js());
        
        // Localizar script para AJAX
        wp_localize_script("jquery", "soloylibre_ajax", array(
            "ajax_url" => admin_url("admin-ajax.php"),
            "nonce" => wp_create_nonce("gallery_interaction")
        ));
    }
    
    public function render_gallery($atts) {
        $defaults = array(
            "ids" => "",
            "style" => $this->settings->get_option("gallery_default_style", "dominican"),
            "columns" => "3",
            "size" => "large",
            "lightbox" => $this->settings->get_option("lightbox_enabled", true) ? "true" : "false",
            "show_metadata" => "true",
            "show_title" => "true",
            "show_caption" => "false",
            "hover_effect" => "zoom",
            "border_radius" => "12",
            "spacing" => "20",
            "photographer_signature" => $this->settings->get_option("photographer_signature", true) ? "true" : "false",
            "enable_interactions" => $this->settings->get_option("enable_interactions", true) ? "true" : "false",
            "auto_confetti" => "true"
        );
        
        $atts = shortcode_atts($defaults, $atts, "soloylibre_gallery");
        
        if (empty($atts["ids"])) {
            // Si no hay IDs, cargar fotos automáticamente
            $auto_load_count = $this->settings->get_option("auto_load_photos", 300);
            $photos = get_posts(array(
                "post_type" => "attachment",
                "post_mime_type" => "image",
                "posts_per_page" => min($auto_load_count, 50), // Límite para shortcode
                "post_status" => "inherit",
                "orderby" => "date",
                "order" => "DESC"
            ));
            
            if (empty($photos)) {
                return "<p style=\"color: #dc3545; padding: 20px; background: #f8d7da; border-radius: 8px; text-align: center;\">📸 No se encontraron imágenes en la biblioteca de medios.</p>";
            }
            
            $atts["ids"] = implode(",", wp_list_pluck($photos, "ID"));
        }
        
        $image_ids = explode(",", $atts["ids"]);
        $image_ids = array_map("trim", $image_ids);
        $image_ids = array_filter($image_ids, "is_numeric");
        
        if (empty($image_ids)) {
            return "<p style=\"color: #dc3545; padding: 20px; background: #f8d7da; border-radius: 8px; text-align: center;\">❌ Error: IDs de imágenes no válidos.</p>";
        }
        
        $gallery_id = "soloylibre-gallery-" . uniqid();
        $output = "";
        
        // Contenedor principal con datos
        $output .= "<div id=\"$gallery_id\" class=\"soloylibre-gallery soloylibre-style-{$atts["style"]}\" data-confetti=\"{$atts["auto_confetti"]}\" data-interactions=\"{$atts["enable_interactions"]}\">";
        
        // Título de la galería
        $output .= "<div class=\"gallery-header\">";
        $output .= "<h3 class=\"gallery-title\">📸 Galería SoloYLibre</h3>";
        $output .= "<p class=\"gallery-subtitle\">🇩🇴 Jose L Encarnacion (JoseTusabe) - " . count($image_ids) . " fotos</p>";
        $output .= "</div>";
        
        // Grid de imágenes mejorado
        $columns = intval($atts["columns"]);
        $spacing = intval($atts["spacing"]);
        $border_radius = intval($atts["border_radius"]);
        
        $output .= "<div class=\"gallery-grid enhanced-grid\" style=\"grid-template-columns: repeat($columns, 1fr); gap: {$spacing}px;\">";
        
        foreach ($image_ids as $index => $image_id) {
            $image_id = intval($image_id);
            $image = wp_get_attachment_image_src($image_id, $atts["size"]);
            $full_image = wp_get_attachment_image_src($image_id, "full");
            $metadata = wp_get_attachment_metadata($image_id);
            $title = get_the_title($image_id) ?: "Foto #$image_id";
            $caption = wp_get_attachment_caption($image_id);
            $alt = get_post_meta($image_id, "_wp_attachment_image_alt", true) ?: $title;
            
            if (!$image) continue;
            
            // Obtener estadísticas de interacción
            $interactions = $this->get_photo_interactions($image_id);
            
            $output .= "<div class=\"gallery-item enhanced-item hover-{$atts["hover_effect"]}\" style=\"border-radius: {$border_radius}px;\" data-photo-id=\"$image_id\" data-index=\"$index\">";
            
            // Imagen principal
            if ($atts["lightbox"] === "true" && $full_image) {
                $output .= "<a href=\"{$full_image[0]}\" class=\"gallery-link enhanced-link\" data-lightbox=\"gallery-$gallery_id\" data-title=\"" . esc_attr($title) . "\">";
            } else {
                $output .= "<div class=\"gallery-link enhanced-link\">";
            }
            
            $output .= "<img src=\"{$image[0]}\" alt=\"" . esc_attr($alt) . "\" title=\"" . esc_attr($title) . "\" loading=\"lazy\" class=\"gallery-image\">";
            
            // Overlay mejorado con información
            $output .= "<div class=\"gallery-overlay enhanced-overlay\">";
            
            if ($atts["show_title"] === "true") {
                $output .= "<h4 class=\"image-title\">$title</h4>";
            }
            
            if ($atts["show_caption"] === "true" && !empty($caption)) {
                $output .= "<p class=\"image-caption\">$caption</p>";
            }
            
            if ($atts["show_metadata"] === "true" && $metadata) {
                $output .= "<div class=\"image-metadata\">";
                if (isset($metadata["width"]) && isset($metadata["height"])) {
                    $output .= "<span class=\"meta-item\">📐 {$metadata["width"]} × {$metadata["height"]}</span>";
                }
                if (isset($metadata["filesize"])) {
                    $size_mb = round($metadata["filesize"] / 1024 / 1024, 2);
                    $output .= "<span class=\"meta-item\">💾 {$size_mb} MB</span>";
                }
                $output .= "</div>";
            }
            
            $output .= "</div>";
            
            if ($atts["lightbox"] === "true") {
                $output .= "</a>";
            } else {
                $output .= "</div>";
            }
            
            // Panel de interacciones
            if ($atts["enable_interactions"] === "true") {
                $output .= "<div class=\"gallery-interactions\">";
                $output .= "<button class=\"interaction-btn like-btn\" data-action=\"like\" data-photo-id=\"$image_id\">";
                $output .= "<span class=\"icon\">❤️</span>";
                $output .= "<span class=\"count\">{$interactions["likes"]}</span>";
                $output .= "</button>";
                
                $output .= "<button class=\"interaction-btn view-btn\" data-action=\"view\" data-photo-id=\"$image_id\">";
                $output .= "<span class=\"icon\">👁️</span>";
                $output .= "<span class=\"count\">{$interactions["views"]}</span>";
                $output .= "</button>";
                
                $output .= "<button class=\"interaction-btn share-btn\" data-action=\"share\" data-photo-id=\"$image_id\">";
                $output .= "<span class=\"icon\">📤</span>";
                $output .= "<span class=\"count\">{$interactions["shares"]}</span>";
                $output .= "</button>";
                $output .= "</div>";
            }
            
            $output .= "</div>";
        }
        
        $output .= "</div>"; // Cierre gallery-grid
        
        // Estadísticas de la galería
        if ($atts["enable_interactions"] === "true") {
            $total_stats = $this->get_gallery_stats($image_ids);
            $output .= "<div class=\"gallery-stats\">";
            $output .= "<div class=\"stat-item\"><span class=\"icon\">❤️</span> {$total_stats["total_likes"]} likes</div>";
            $output .= "<div class=\"stat-item\"><span class=\"icon\">👁️</span> {$total_stats["total_views"]} vistas</div>";
            $output .= "<div class=\"stat-item\"><span class=\"icon\">📤</span> {$total_stats["total_shares"]} compartidas</div>";
            $output .= "</div>";
        }
        
        // Firma del fotógrafo mejorada
        if ($atts["photographer_signature"] === "true") {
            $output .= "<div class=\"photographer-signature enhanced-signature\">";
            $output .= "<div class=\"signature-content\">";
            $output .= "<div class=\"photographer-info\">";
            $output .= "<h4>📸 Jose L Encarnacion (JoseTusabe)</h4>";
            $output .= "<p class=\"brand\">SoloYLibre Photography</p>";
            $output .= "<p class=\"location\">📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
            $output .= "</div>";
            $output .= "<div class=\"contact-info\">";
            $output .= "<p>📞 718-713-5500 | 📧 <EMAIL></p>";
            $output .= "<div class=\"websites\">";
            $output .= "<a href=\"https://josetusabe.com\" target=\"_blank\">🌐 josetusabe.com</a>";
            $output .= "<a href=\"https://soloylibre.com\" target=\"_blank\">🌐 soloylibre.com</a>";
            $output .= "<a href=\"https://1and1photo.com\" target=\"_blank\">📸 1and1photo.com</a>";
            $output .= "</div>";
            $output .= "</div>";
            $output .= "</div>";
            $output .= "</div>";
        }
        
        $output .= "</div>"; // Cierre contenedor principal
        
        return $output;
    }
    
    private function get_photo_interactions($photo_id) {
        global $wpdb;
        
        $interactions = array(
            "likes" => 0,
            "views" => 0,
            "shares" => 0
        );
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT interaction_type, SUM(interaction_count) as total 
             FROM {$wpdb->prefix}soloylibre_interactions 
             WHERE photo_id = %d 
             GROUP BY interaction_type",
            $photo_id
        ));
        
        foreach ($results as $result) {
            if (isset($interactions[$result->interaction_type])) {
                $interactions[$result->interaction_type] = intval($result->total);
            }
        }
        
        return $interactions;
    }
    
    private function get_gallery_stats($image_ids) {
        global $wpdb;
        
        $ids_placeholder = implode(",", array_fill(0, count($image_ids), "%d"));
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT interaction_type, SUM(interaction_count) as total 
             FROM {$wpdb->prefix}soloylibre_interactions 
             WHERE photo_id IN ($ids_placeholder) 
             GROUP BY interaction_type",
            ...$image_ids
        ));
        
        $stats = array(
            "total_likes" => 0,
            "total_views" => 0,
            "total_shares" => 0
        );
        
        foreach ($results as $result) {
            $key = "total_" . $result->interaction_type . "s";
            if (isset($stats[$key])) {
                $stats[$key] = intval($result->total);
            }
        }
        
        return $stats;
    }
    
    public function handle_interaction() {
        check_ajax_referer("gallery_interaction", "nonce");
        
        $photo_id = intval($_POST["photo_id"]);
        $action = sanitize_text_field($_POST["action_type"]);
        
        if (!$photo_id || !in_array($action, array("like", "view", "share"))) {
            wp_send_json_error("Datos inválidos");
        }
        
        global $wpdb;
        
        // Incrementar contador
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT interaction_count FROM {$wpdb->prefix}soloylibre_interactions 
             WHERE photo_id = %d AND interaction_type = %s",
            $photo_id, $action
        ));
        
        if ($existing !== null) {
            $wpdb->update(
                $wpdb->prefix . "soloylibre_interactions",
                array("interaction_count" => $existing + 1),
                array("photo_id" => $photo_id, "interaction_type" => $action)
            );
        } else {
            $wpdb->insert(
                $wpdb->prefix . "soloylibre_interactions",
                array(
                    "photo_id" => $photo_id,
                    "interaction_type" => $action,
                    "interaction_count" => 1,
                    "created_at" => current_time("mysql")
                )
            );
        }
        
        $new_count = $existing ? $existing + 1 : 1;
        
        wp_send_json_success(array(
            "new_count" => $new_count,
            "message" => "Interacción registrada"
        ));
    }
}

// Reemplazar el shortcode handler anterior
if (class_exists("SoloYLibre_Shortcode_Handler")) {
    // Remover shortcode anterior
    remove_shortcode("soloylibre_gallery");
}

new SoloYLibre_Enhanced_Shortcode();
?>';

// Reemplazar el archivo anterior
file_put_contents('wp-content/plugins/Archive/includes/class-enhanced-shortcode.php', $enhanced_shortcode);
echo "   ✅ Shortcode handler mejorado creado\n";

// 2. Actualizar versión del plugin
echo "\n2. 📦 ACTUALIZANDO VERSIÓN DEL PLUGIN (4.0.2 → 4.0.3)...\n";

$plugin_main = 'wp-content/plugins/Archive/soloylibre-gallery-plugin.php';
if (file_exists($plugin_main)) {
    $content = file_get_contents($plugin_main);
    $content = preg_replace('/Version:\s*4\.0\.2/', 'Version: 4.0.3', $content);
    file_put_contents($plugin_main, $content);
    echo "   ✅ Versión actualizada a 4.0.3\n";
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "✅ FASE 3 COMPLETADA - SHORTCODES MEJORADOS\n";
echo str_repeat("=", 70) . "\n";

echo "\n📊 RESUMEN FASE 3:\n";
echo "   🏷️ Shortcode mejorado: ✅ Creado\n";
echo "   📦 Versión actualizada: 4.0.3\n";
echo "   🎨 Frontend mejorado: ✅ Implementado\n";
echo "   📊 Interacciones: ✅ Sistema completo\n";

echo "\n🔗 PRÓXIMA FASE:\n";
echo "   4. CSS y JavaScript mejorados\n";

?>
