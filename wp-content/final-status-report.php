<?php
/**
 * Final Status Report
 * Complete system status after database fix
 * Developed by JEYKO AI for Jose <PERSON> (JoseTusabe)
 */

echo "<h1>📋 Final System Status Report</h1>";
echo "<p>🇩🇴 <PERSON><PERSON><PERSON>be Photography - Complete system status after database fix</p>";

// Load WordPress
try {
    if (!defined('ABSPATH')) {
        require_once('wp-config.php');
    }
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ WordPress loaded successfully";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Error: " . $e->getMessage();
    echo "</div>";
    die();
}

echo "<h2>🌐 1. Website Status</h2>";

// Test frontend
$home_url = home_url();
$frontend_response = wp_remote_get($home_url, array('timeout' => 10));
$frontend_working = false;
$no_critical_error = false;

if (!is_wp_error($frontend_response)) {
    $response_code = wp_remote_retrieve_response_code($frontend_response);
    $response_body = wp_remote_retrieve_body($frontend_response);
    
    if ($response_code === 200) {
        $frontend_working = true;
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Website accessible at: <a href='$home_url' target='_blank'>$home_url</a>";
        echo "</div>";
        
        if (strpos($response_body, 'critical error') === false && strpos($response_body, 'Critical Error') === false) {
            $no_critical_error = true;
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ NO critical error message detected";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ Critical error message still present";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Website error - HTTP $response_code";
        echo "</div>";
    }
} else {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ Website request failed: " . $frontend_response->get_error_message();
    echo "</div>";
}

echo "<h2>🏠 2. WordPress Dashboard Status</h2>";

// Test admin
$admin_url = admin_url();
$admin_response = wp_remote_get($admin_url, array('timeout' => 10));
$admin_working = false;

if (!is_wp_error($admin_response)) {
    $response_code = wp_remote_retrieve_response_code($admin_response);
    
    if ($response_code === 200 || $response_code === 302) {
        $admin_working = true;
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Dashboard accessible at: <a href='$admin_url' target='_blank'>$admin_url</a>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Dashboard error - HTTP $response_code";
        echo "</div>";
    }
} else {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ Dashboard request failed: " . $admin_response->get_error_message();
    echo "</div>";
}

echo "<h2>🗄️ 3. Database Status</h2>";

global $wpdb;

// Test database connection
try {
    $db_version = $wpdb->get_var("SELECT VERSION()");
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Database connected - Version: $db_version";
    echo "</div>";
    
    // Check WordPress tables
    $wp_tables = array('posts', 'options', 'users', 'postmeta', 'usermeta');
    $tables_ok = 0;
    
    foreach ($wp_tables as $table) {
        $full_table = $wpdb->prefix . $table;
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $full_table");
        if ($count !== null) {
            $tables_ok++;
            echo "<div style='background: #d4edda; padding: 8px; border-radius: 3px; margin: 3px 0; font-size: 14px;'>";
            echo "✅ Table $table: $count records";
            echo "</div>";
        }
    }
    
    if ($tables_ok == count($wp_tables)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ All WordPress tables working properly";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ Database error: " . $e->getMessage();
    echo "</div>";
}

echo "<h2>🔌 4. Plugin Status</h2>";

// Check plugins
$active_plugins = get_option('active_plugins', array());
$bulletproof_active = false;

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "📋 Active plugins: " . count($active_plugins);
echo "</div>";

foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'soloylibre-bulletproof') !== false) {
        $bulletproof_active = true;
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ SoloYLibre Bulletproof plugin active";
        echo "</div>";
        
        // Test plugin page
        $plugin_url = admin_url('admin.php?page=soloylibre-bp');
        echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "🔗 Plugin dashboard: <a href='$plugin_url' target='_blank'>SoloYLibre Gallery</a>";
        echo "</div>";
    }
}

if (!$bulletproof_active) {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "⚠️ SoloYLibre Bulletproof plugin not active";
    echo "</div>";
}

echo "<h2>📸 5. SoloYLibre Data Status</h2>";

// Check SoloYLibre options
$soloylibre_options = array(
    'soloylibre_published_photos' => 'Published photos',
    'soloylibre_private_photos' => 'Private photos',
    'soloylibre_bp_version' => 'Plugin version'
);

foreach ($soloylibre_options as $option => $description) {
    $value = get_option($option);
    if ($value !== false) {
        if (is_array($value)) {
            $count = count($value);
            echo "<div style='background: #d4edda; padding: 8px; border-radius: 3px; margin: 3px 0; font-size: 14px;'>";
            echo "✅ $description: $count items";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; padding: 8px; border-radius: 3px; margin: 3px 0; font-size: 14px;'>";
            echo "✅ $description: $value";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 8px; border-radius: 3px; margin: 3px 0; font-size: 14px;'>";
        echo "⚠️ $description: Not found";
        echo "</div>";
    }
}

echo "<h2>⚙️ 6. System Configuration</h2>";

// System info
$system_info = array(
    'WordPress Version' => get_bloginfo('version'),
    'PHP Version' => PHP_VERSION,
    'Memory Limit' => ini_get('memory_limit'),
    'Max Execution Time' => ini_get('max_execution_time'),
    'Upload Max Size' => ini_get('upload_max_filesize'),
    'Post Max Size' => ini_get('post_max_size'),
    'Display Errors' => ini_get('display_errors') ? 'On' : 'Off',
    'Log Errors' => ini_get('log_errors') ? 'On' : 'Off'
);

foreach ($system_info as $setting => $value) {
    echo "<div style='background: #f8f9fa; padding: 8px; border-radius: 3px; margin: 3px 0; font-size: 14px;'>";
    echo "<strong>$setting:</strong> $value";
    echo "</div>";
}

echo "<h2>📊 7. Overall System Health</h2>";

// Calculate system health score
$health_checks = array(
    'Frontend working' => $frontend_working,
    'No critical errors' => $no_critical_error,
    'Admin working' => $admin_working,
    'Database working' => isset($db_version) && !empty($db_version),
    'WordPress tables OK' => isset($tables_ok) && $tables_ok == 5,
    'Bulletproof plugin active' => $bulletproof_active,
    'Memory sufficient' => wp_convert_hr_to_bytes(ini_get('memory_limit')) >= wp_convert_hr_to_bytes('256M')
);

$health_score = 0;
$total_checks = count($health_checks);

foreach ($health_checks as $check => $status) {
    if ($status) {
        $health_score++;
        echo "<div style='background: #d4edda; padding: 8px; border-radius: 3px; margin: 3px 0; font-size: 14px;'>";
        echo "✅ $check";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 8px; border-radius: 3px; margin: 3px 0; font-size: 14px;'>";
        echo "❌ $check";
        echo "</div>";
    }
}

$health_percentage = ($health_score / $total_checks) * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$health_score</div>";
echo "<div>✅ Checks Passed</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . ($total_checks - $health_score) . "</div>";
echo "<div>❌ Issues Found</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($health_percentage, 1) . "%</div>";
echo "<div>📈 Health Score</div>";
echo "</div>";

echo "</div>";

// Final status
if ($health_percentage >= 95) {
    echo "<div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #155724;'>🎉 SYSTEM FULLY OPERATIONAL!</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #155724;'>";
    echo "All systems are working perfectly. Database has been fixed and WordPress is fully functional.";
    echo "</p>";
    echo "</div>";
} elseif ($health_percentage >= 80) {
    echo "<div style='background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #856404;'>⚠️ System Mostly Working</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #856404;'>";
    echo "Most systems are operational but some issues remain.";
    echo "</p>";
    echo "</div>";
} else {
    echo "<div style='background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #721c24;'>❌ System Needs Attention</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #721c24;'>";
    echo "Significant issues detected that need immediate attention.";
    echo "</p>";
    echo "</div>";
}

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔗 Quick Access Links:</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
echo "<a href='$home_url' target='_blank' style='background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>🌐 Website</a>";
echo "<a href='$admin_url' target='_blank' style='background: #007bff; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>🏠 Dashboard</a>";
if ($bulletproof_active) {
    echo "<a href='" . admin_url('admin.php?page=soloylibre-bp') . "' target='_blank' style='background: #ffc107; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>📸 SoloYLibre</a>";
    echo "<a href='" . admin_url('admin.php?page=soloylibre-bp-posts') . "' target='_blank' style='background: #17a2b8; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>⚡ Quick Posts</a>";
}
echo "</div>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Final system status report</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Developed by JEYKO AI for Jose L Encarnacion</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 12px;'>Database fixed - System operational - Ready for production</p>";
echo "</div>";
?>
