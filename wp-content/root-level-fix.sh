#!/bin/bash

# Root Level System Fix for WordPress
# Complete system-level repair for <PERSON><PERSON>usa<PERSON> Photography
# Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)

echo "🔧 Root Level System Fix - JoseTusabe Photography"
echo "🇩🇴 <PERSON> José <PERSON>, República Dominicana"
echo "=================================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run as root (sudo)"
    echo "Usage: sudo bash root-level-fix.sh"
    exit 1
fi

echo "✅ Running with root privileges"

# Set variables
WORDPRESS_PATH="/Applications/MAMP/htdocs/wp/wordpress"
APACHE_USER="www-data"
MYSQL_USER="root"
MYSQL_PASS=""
DB_NAME="wordpress"

# Detect system type
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 macOS detected - MAMP environment"
    APACHE_USER="_www"
    WORDPRESS_PATH="/Applications/MAMP/htdocs/wp/wordpress"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🐧 Linux detected"
    APACHE_USER="www-data"
    WORDPRESS_PATH="/var/www/html/wordpress"
fi

echo "📁 WordPress path: $WORDPRESS_PATH"

# 1. Fix file permissions
echo ""
echo "🔒 1. Fixing File Permissions"
echo "=============================="

if [ -d "$WORDPRESS_PATH" ]; then
    echo "📁 Setting directory permissions to 755..."
    find "$WORDPRESS_PATH" -type d -exec chmod 755 {} \;
    
    echo "📄 Setting file permissions to 644..."
    find "$WORDPRESS_PATH" -type f -exec chmod 644 {} \;
    
    echo "🔧 Setting wp-config.php to 600..."
    if [ -f "$WORDPRESS_PATH/wp-config.php" ]; then
        chmod 600 "$WORDPRESS_PATH/wp-config.php"
    fi
    
    echo "📝 Setting .htaccess to 644..."
    if [ -f "$WORDPRESS_PATH/.htaccess" ]; then
        chmod 644 "$WORDPRESS_PATH/.htaccess"
    fi
    
    echo "👤 Setting ownership to $APACHE_USER..."
    chown -R "$APACHE_USER:$APACHE_USER" "$WORDPRESS_PATH"
    
    echo "✅ File permissions fixed"
else
    echo "❌ WordPress directory not found: $WORDPRESS_PATH"
fi

# 2. Fix PHP configuration
echo ""
echo "⚙️ 2. Optimizing PHP Configuration"
echo "=================================="

# Find PHP ini file
PHP_INI=""
if [[ "$OSTYPE" == "darwin"* ]]; then
    PHP_INI="/Applications/MAMP/bin/php/php8.2.0/conf/php.ini"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    PHP_INI=$(php --ini | grep "Loaded Configuration File" | cut -d: -f2 | xargs)
fi

if [ -f "$PHP_INI" ]; then
    echo "📝 Found PHP configuration: $PHP_INI"
    
    # Backup original
    cp "$PHP_INI" "$PHP_INI.backup.$(date +%Y%m%d_%H%M%S)"
    echo "💾 Backup created"
    
    # Update PHP settings
    sed -i.bak 's/memory_limit = .*/memory_limit = 512M/' "$PHP_INI"
    sed -i.bak 's/max_execution_time = .*/max_execution_time = 300/' "$PHP_INI"
    sed -i.bak 's/max_input_vars = .*/max_input_vars = 3000/' "$PHP_INI"
    sed -i.bak 's/post_max_size = .*/post_max_size = 64M/' "$PHP_INI"
    sed -i.bak 's/upload_max_filesize = .*/upload_max_filesize = 64M/' "$PHP_INI"
    sed -i.bak 's/display_errors = .*/display_errors = Off/' "$PHP_INI"
    sed -i.bak 's/log_errors = .*/log_errors = On/' "$PHP_INI"
    
    echo "✅ PHP configuration optimized"
else
    echo "⚠️ PHP configuration file not found"
fi

# 3. Fix MySQL/MariaDB
echo ""
echo "🗄️ 3. Optimizing Database"
echo "========================="

# Check if MySQL is running
if command -v mysql &> /dev/null; then
    echo "🔍 MySQL/MariaDB found"
    
    # Create database repair script
    cat > /tmp/db_repair.sql << EOF
USE $DB_NAME;

-- Repair all WordPress tables
REPAIR TABLE wp_posts;
REPAIR TABLE wp_postmeta;
REPAIR TABLE wp_options;
REPAIR TABLE wp_users;
REPAIR TABLE wp_usermeta;
REPAIR TABLE wp_comments;
REPAIR TABLE wp_commentmeta;
REPAIR TABLE wp_terms;
REPAIR TABLE wp_term_taxonomy;
REPAIR TABLE wp_term_relationships;

-- Optimize all tables
OPTIMIZE TABLE wp_posts;
OPTIMIZE TABLE wp_postmeta;
OPTIMIZE TABLE wp_options;
OPTIMIZE TABLE wp_users;
OPTIMIZE TABLE wp_usermeta;
OPTIMIZE TABLE wp_comments;
OPTIMIZE TABLE wp_commentmeta;
OPTIMIZE TABLE wp_terms;
OPTIMIZE TABLE wp_term_taxonomy;
OPTIMIZE TABLE wp_term_relationships;

-- Clean up orphaned data
DELETE FROM wp_postmeta WHERE post_id NOT IN (SELECT ID FROM wp_posts);
DELETE FROM wp_usermeta WHERE user_id NOT IN (SELECT ID FROM wp_users);

-- Clean expired transients
DELETE FROM wp_options WHERE option_name LIKE '_transient_%' AND option_name NOT LIKE '_transient_timeout_%';
DELETE FROM wp_options WHERE option_name LIKE '_transient_timeout_%' AND option_value < UNIX_TIMESTAMP();

-- Update essential options
UPDATE wp_options SET option_value = 'JoseTusabe Photography' WHERE option_name = 'blogname';
UPDATE wp_options SET option_value = '🇩🇴 San José de Ocoa, República Dominicana' WHERE option_name = 'blogdescription';
UPDATE wp_options SET option_value = '<EMAIL>' WHERE option_name = 'admin_email';
UPDATE wp_options SET option_value = 'twentytwentythree' WHERE option_name = 'template';
UPDATE wp_options SET option_value = 'twentytwentythree' WHERE option_name = 'stylesheet';
UPDATE wp_options SET option_value = 'a:1:{i:0;s:55:"soloylibre-bulletproof/soloylibre-bulletproof.php";}' WHERE option_name = 'active_plugins';

FLUSH TABLES;
EOF

    # Execute database repair
    if [[ "$OSTYPE" == "darwin"* ]]; then
        /Applications/MAMP/Library/bin/mysql -u "$MYSQL_USER" -p"$MYSQL_PASS" < /tmp/db_repair.sql
    else
        mysql -u "$MYSQL_USER" -p"$MYSQL_PASS" < /tmp/db_repair.sql
    fi
    
    echo "✅ Database optimized and repaired"
    rm /tmp/db_repair.sql
else
    echo "⚠️ MySQL/MariaDB not found"
fi

# 4. Fix Apache/Nginx configuration
echo ""
echo "🌐 4. Web Server Configuration"
echo "============================="

# Apache configuration
if command -v apache2 &> /dev/null || command -v httpd &> /dev/null; then
    echo "🔍 Apache detected"
    
    # Enable required modules
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        a2enmod rewrite
        a2enmod headers
        a2enmod expires
        echo "✅ Apache modules enabled"
    fi
    
elif command -v nginx &> /dev/null; then
    echo "🔍 Nginx detected"
    echo "ℹ️ Nginx configuration should be manually reviewed"
fi

# 5. Create optimized .htaccess
echo ""
echo "📝 5. Creating Optimized .htaccess"
echo "=================================="

cat > "$WORDPRESS_PATH/.htaccess" << 'EOF'
# BEGIN WordPress
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
RewriteBase /wp/wordpress/
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /wp/wordpress/index.php [L]
</IfModule>
# END WordPress

# Security Headers
<IfModule mod_headers.c>
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Disable directory browsing
Options -Indexes

# Protect wp-config.php
<Files wp-config.php>
order allow,deny
deny from all
</Files>

# Protect .htaccess
<Files .htaccess>
order allow,deny
deny from all
</Files>

# Enable compression
<IfModule mod_deflate.c>
AddOutputFilterByType DEFLATE text/plain
AddOutputFilterByType DEFLATE text/html
AddOutputFilterByType DEFLATE text/xml
AddOutputFilterByType DEFLATE text/css
AddOutputFilterByType DEFLATE application/xml
AddOutputFilterByType DEFLATE application/xhtml+xml
AddOutputFilterByType DEFLATE application/rss+xml
AddOutputFilterByType DEFLATE application/javascript
AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser caching
<IfModule mod_expires.c>
ExpiresActive On
ExpiresByType image/jpg "access plus 1 month"
ExpiresByType image/jpeg "access plus 1 month"
ExpiresByType image/gif "access plus 1 month"
ExpiresByType image/png "access plus 1 month"
ExpiresByType text/css "access plus 1 month"
ExpiresByType application/pdf "access plus 1 month"
ExpiresByType text/javascript "access plus 1 month"
ExpiresByType application/javascript "access plus 1 month"
ExpiresByType application/x-javascript "access plus 1 month"
ExpiresByType application/x-shockwave-flash "access plus 1 month"
ExpiresByType image/x-icon "access plus 1 year"
ExpiresDefault "access plus 2 days"
</IfModule>
EOF

chown "$APACHE_USER:$APACHE_USER" "$WORDPRESS_PATH/.htaccess"
chmod 644 "$WORDPRESS_PATH/.htaccess"
echo "✅ Optimized .htaccess created"

# 6. System cleanup
echo ""
echo "🧹 6. System Cleanup"
echo "==================="

# Clear system caches
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🗑️ Clearing system caches..."
    sync && echo 3 > /proc/sys/vm/drop_caches
fi

# Clear temporary files
echo "🗑️ Clearing temporary files..."
find /tmp -name "*.tmp" -type f -delete 2>/dev/null || true
find /tmp -name "php*" -type f -delete 2>/dev/null || true

echo "✅ System cleanup completed"

# 7. Restart services
echo ""
echo "🔄 7. Restarting Services"
echo "========================"

if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 MAMP detected - Please restart MAMP manually"
    echo "   1. Stop MAMP"
    echo "   2. Wait 10 seconds"
    echo "   3. Start MAMP"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "🔄 Restarting Apache..."
    systemctl restart apache2 2>/dev/null || service apache2 restart 2>/dev/null || true
    
    echo "🔄 Restarting MySQL..."
    systemctl restart mysql 2>/dev/null || service mysql restart 2>/dev/null || true
    
    echo "🔄 Restarting PHP-FPM..."
    systemctl restart php*-fpm 2>/dev/null || service php*-fpm restart 2>/dev/null || true
fi

# 8. Final verification
echo ""
echo "🔍 8. Final System Check"
echo "======================="

echo "📁 WordPress directory: $(ls -la $WORDPRESS_PATH | head -1)"
echo "👤 Ownership: $(ls -ld $WORDPRESS_PATH | awk '{print $3":"$4}')"
echo "🔒 Permissions: $(ls -ld $WORDPRESS_PATH | awk '{print $1}')"

if [ -f "$WORDPRESS_PATH/wp-config.php" ]; then
    echo "✅ wp-config.php exists"
else
    echo "❌ wp-config.php missing"
fi

if [ -f "$WORDPRESS_PATH/.htaccess" ]; then
    echo "✅ .htaccess exists"
else
    echo "❌ .htaccess missing"
fi

# Summary
echo ""
echo "🎉 ROOT LEVEL FIX COMPLETED"
echo "=========================="
echo "✅ File permissions fixed"
echo "✅ PHP configuration optimized"
echo "✅ Database repaired and optimized"
echo "✅ Web server configuration updated"
echo "✅ Security headers added"
echo "✅ System caches cleared"
echo "✅ Services restarted (if applicable)"
echo ""
echo "🌐 Test your website: http://localhost:8888/wp/wordpress/"
echo "🏠 Access dashboard: http://localhost:8888/wp/wordpress/wp-admin/"
echo ""
echo "🇩🇴 JoseTusabe Photography - System fully optimized!"
echo "📸 Developed by JEYKO AI for Jose L Encarnacion"
EOF
