"use strict";(self.webpackChunkhello_elementor=self.webpackChunkhello_elementor||[]).push([[299],{1299:function(e,L,C){C.r(L),C.d(L,{default:function(){return t}});var l=C(1609),n=C(3072),t=l.forwardRef(((e,L)=>l.createElement(n.A,{viewBox:"0 0 24 24",...e,ref:L},l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.0001 2C12.266 2 12.5089 2.15027 12.6266 2.38762L15.3384 7.85709L21.4017 8.73178C21.665 8.76976 21.8838 8.95331 21.9659 9.20518C22.0481 9.45705 21.9794 9.73351 21.7888 9.91822L17.395 14.1754L18.431 20.1871C18.476 20.4481 18.3681 20.7118 18.1528 20.8674C17.9375 21.0229 17.6522 21.0433 17.4168 20.9198L12.0063 18.0819L6.58287 20.9201C6.3475 21.0433 6.06229 21.0228 5.84715 20.8672C5.63202 20.7116 5.52429 20.448 5.56925 20.1871L6.60527 14.1754L2.21146 9.91822C2.02081 9.73351 1.95213 9.45705 2.0343 9.20518C2.11647 8.95331 2.33523 8.76976 2.59853 8.73178L8.66185 7.85709L11.3737 2.38762C11.4914 2.15027 11.7342 2 12.0001 2ZM12.0001 4.26658L9.75213 8.80054C9.65033 9.00586 9.45352 9.14813 9.22588 9.18097L4.20166 9.90576L7.84321 13.4341C8.0081 13.5938 8.08337 13.8242 8.04447 14.0499L7.18563 19.0334L11.6815 16.6806C11.8853 16.574 12.1287 16.5741 12.3323 16.6809L16.8143 19.0318L15.9558 14.0499C15.9169 13.8242 15.9921 13.5938 16.157 13.4341L19.7986 9.90576L14.7744 9.18097C14.5467 9.14813 14.3499 9.00586 14.2481 8.80054L12.0001 4.26658Z"}))))}}]);