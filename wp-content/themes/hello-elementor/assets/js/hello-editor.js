!function(){"use strict";class e extends $e.modules.hookUI.After{getCommand(){return"document/elements/settings"}getId(){return"hello-elementor-editor-controls-handler"}getHelloThemeControls(){return{hello_header_logo_display:{selector:".site-header .site-logo, .site-header .site-title",callback:(e,o)=>{this.toggleShowHideClass(e,o.settings.hello_header_logo_display)}},hello_header_menu_display:{selector:".site-header .site-navigation, .site-header .site-navigation-toggle-holder",callback:(e,o)=>{this.toggleShowHideClass(e,o.settings.hello_header_menu_display)}},hello_header_tagline_display:{selector:".site-header .site-description",callback:(e,o)=>{this.toggleShowHideClass(e,o.settings.hello_header_tagline_display)}},hello_header_logo_type:{selector:".site-header .site-branding",callback:(e,o)=>{const t=o.container.controls.hello_header_logo_type.options,l=o.settings.hello_header_logo_type;this.toggleLayoutClass(e,"show-",t,l)}},hello_header_layout:{selector:".site-header",callback:(e,o)=>{const t=o.container.controls.hello_header_layout.options,l=o.settings.hello_header_layout;this.toggleLayoutClass(e,"header-",t,l)}},hello_header_width:{selector:".site-header",callback:(e,o)=>{const t=o.container.controls.hello_header_width.options,l=o.settings.hello_header_width;this.toggleLayoutClass(e,"header-",t,l)}},hello_header_menu_layout:{selector:".site-header",callback:(e,o)=>{const t=o.container.controls.hello_header_menu_layout.options,l=o.settings.hello_header_menu_layout;e.find(".site-navigation-toggle-holder").removeClass("elementor-active"),e.find(".site-navigation-dropdown").removeClass("show"),this.toggleLayoutClass(e,"menu-layout-",t,l)}},hello_header_menu_dropdown:{selector:".site-header",callback:(e,o)=>{const t=o.container.controls.hello_header_menu_dropdown.options,l=o.settings.hello_header_menu_dropdown;this.toggleLayoutClass(e,"menu-dropdown-",t,l)}},hello_footer_logo_display:{selector:".site-footer .site-logo, .site-footer .site-title",callback:(e,o)=>{this.toggleShowHideClass(e,o.settings.hello_footer_logo_display)}},hello_footer_tagline_display:{selector:".site-footer .site-description",callback:(e,o)=>{this.toggleShowHideClass(e,o.settings.hello_footer_tagline_display)}},hello_footer_menu_display:{selector:".site-footer .site-navigation",callback:(e,o)=>{this.toggleShowHideClass(e,o.settings.hello_footer_menu_display)}},hello_footer_copyright_display:{selector:".site-footer .copyright",callback:(e,o)=>{const t=e.closest("#site-footer"),l=o.settings.hello_footer_copyright_display;this.toggleShowHideClass(e,l),t.toggleClass("footer-has-copyright","yes"===l)}},hello_footer_logo_type:{selector:".site-footer .site-branding",callback:(e,o)=>{const t=o.container.controls.hello_footer_logo_type.options,l=o.settings.hello_footer_logo_type;this.toggleLayoutClass(e,"show-",t,l)}},hello_footer_layout:{selector:".site-footer",callback:(e,o)=>{const t=o.container.controls.hello_footer_layout.options,l=o.settings.hello_footer_layout;this.toggleLayoutClass(e,"footer-",t,l)}},hello_footer_width:{selector:".site-footer",callback:(e,o)=>{const t=o.container.controls.hello_footer_width.options,l=o.settings.hello_footer_width;this.toggleLayoutClass(e,"footer-",t,l)}},hello_footer_copyright_text:{selector:".site-footer .copyright",callback:(e,o)=>{const t=o.settings.hello_footer_copyright_text;e.find("p").text(t)}}}}toggleShowHideClass(e,o){e.removeClass("hide").removeClass("show").addClass(o?"show":"hide")}toggleLayoutClass(e,o,t,l){Object.entries(t).forEach((([t])=>{e.removeClass(o+t)})),""!==l&&e.addClass(o+l)}getConditions(e){const o="kit"===elementor.documents.getCurrent().config.type,t=Object.keys(e.settings),l=1===t.length;return!!(o&&e.settings&&l)&&!!Object.keys(this.getHelloThemeControls()).includes(t[0])}apply(e){const o=this.getHelloThemeControls()[Object.keys(e.settings)[0]],t=elementor.$previewContents.find(o.selector);o.callback(t,e)}}var o=class extends $e.modules.ComponentBase{pages={};getNamespace(){return"hello-elementor"}defaultHooks(){return this.importHooks({ControlsHook:e})}};$e.components.register(new o)}();