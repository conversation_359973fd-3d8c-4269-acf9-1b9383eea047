*, ::before, ::after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x:  ;--tw-pan-y:  ;--tw-pinch-zoom:  ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position:  ;--tw-gradient-via-position:  ;--tw-gradient-to-position:  ;--tw-ordinal:  ;--tw-slashed-zero:  ;--tw-numeric-figure:  ;--tw-numeric-spacing:  ;--tw-numeric-fraction:  ;--tw-ring-inset:  ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / 0.5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur:  ;--tw-brightness:  ;--tw-contrast:  ;--tw-grayscale:  ;--tw-hue-rotate:  ;--tw-invert:  ;--tw-saturate:  ;--tw-sepia:  ;--tw-drop-shadow:  ;--tw-backdrop-blur:  ;--tw-backdrop-brightness:  ;--tw-backdrop-contrast:  ;--tw-backdrop-grayscale:  ;--tw-backdrop-hue-rotate:  ;--tw-backdrop-invert:  ;--tw-backdrop-opacity:  ;--tw-backdrop-saturate:  ;--tw-backdrop-sepia:  ;--tw-contain-size:  ;--tw-contain-layout:  ;--tw-contain-paint:  ;--tw-contain-style:  ;}::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x:  ;--tw-pan-y:  ;--tw-pinch-zoom:  ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position:  ;--tw-gradient-via-position:  ;--tw-gradient-to-position:  ;--tw-ordinal:  ;--tw-slashed-zero:  ;--tw-numeric-figure:  ;--tw-numeric-spacing:  ;--tw-numeric-fraction:  ;--tw-ring-inset:  ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / 0.5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur:  ;--tw-brightness:  ;--tw-contrast:  ;--tw-grayscale:  ;--tw-hue-rotate:  ;--tw-invert:  ;--tw-saturate:  ;--tw-sepia:  ;--tw-drop-shadow:  ;--tw-backdrop-blur:  ;--tw-backdrop-brightness:  ;--tw-backdrop-contrast:  ;--tw-backdrop-grayscale:  ;--tw-backdrop-hue-rotate:  ;--tw-backdrop-invert:  ;--tw-backdrop-opacity:  ;--tw-backdrop-saturate:  ;--tw-backdrop-sepia:  ;--tw-contain-size:  ;--tw-contain-layout:  ;--tw-contain-paint:  ;--tw-contain-style:  ;}[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select{-webkit-appearance: none;-moz-appearance: none;appearance: none;background-color: #fff;border-color: #6b7280;border-width: 1px;border-radius: 0px;padding-top: 0.5rem;padding-left: 0.75rem;padding-bottom: 0.5rem;padding-right: 0.75rem;font-size: 1rem;line-height: 1.5rem;--tw-shadow: 0 0 #0000;}[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus{outline: 2px solid transparent;outline-offset: 2px;--tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: #2563eb;--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);border-color: #2563eb;}input::-moz-placeholder, textarea::-moz-placeholder{color: #6b7280;opacity: 1;}input::placeholder,textarea::placeholder{color: #6b7280;opacity: 1;}::-webkit-datetime-edit-fields-wrapper{padding: 0;}::-webkit-date-and-time-value{min-height: 1.5em;}::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field{padding-top: 0;padding-bottom: 0;}select{background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%236b7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e");background-position: left 0.5rem center;background-repeat: no-repeat;background-size: 1.5em 1.5em;padding-left: 2.5rem;-webkit-print-color-adjust: exact;color-adjust: exact;}[multiple]{background-image: initial;background-position: initial;background-repeat: unset;background-size: initial;padding-left: 0.75rem;-webkit-print-color-adjust: unset;color-adjust: unset;}[type='checkbox'],[type='radio']{-webkit-appearance: none;-moz-appearance: none;appearance: none;padding: 0;-webkit-print-color-adjust: exact;color-adjust: exact;display: inline-block;vertical-align: middle;background-origin: border-box;-webkit-user-select: none;-moz-user-select: none;user-select: none;flex-shrink: 0;height: 1rem;width: 1rem;color: #2563eb;background-color: #fff;border-color: #6b7280;border-width: 1px;--tw-shadow: 0 0 #0000;}[type='checkbox']{border-radius: 0px;}[type='radio']{border-radius: 100%;}[type='checkbox']:focus,[type='radio']:focus{outline: 2px solid transparent;outline-offset: 2px;--tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);--tw-ring-offset-width: 2px;--tw-ring-offset-color: #fff;--tw-ring-color: #2563eb;--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);}[type='checkbox']:checked,[type='radio']:checked{border-color: transparent;background-color: currentColor;background-size: 100% 100%;background-position: center;background-repeat: no-repeat;}[type='checkbox']:checked{background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e");}[type='radio']:checked{background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");}[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus{border-color: transparent;background-color: currentColor;}[type='checkbox']:indeterminate{background-image: url("data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e");border-color: transparent;background-color: currentColor;background-size: 100% 100%;background-position: center;background-repeat: no-repeat;}[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus{border-color: transparent;background-color: currentColor;}[type='file']{background: unset;border-color: inherit;border-width: 0;border-radius: 0;padding: 0;font-size: unset;line-height: inherit;}[type='file']:focus{outline: 1px auto -webkit-focus-ring-color;}.container{width: 100%;}@media (min-width: 640px){.container{max-width: 640px;}}@media (min-width: 768px){.container{max-width: 768px;}}@media (min-width: 1024px){.container{max-width: 1024px;}}@media (min-width: 1280px){.container{max-width: 1280px;}}@media (min-width: 1536px){.container{max-width: 1536px;}}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sr-only{position: absolute;width: 1px;height: 1px;padding: 0;margin: -1px;overflow: hidden;clip: rect(0, 0, 0, 0);white-space: nowrap;border-width: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pointer-events-none{pointer-events: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pointer-events-auto{pointer-events: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .visible{visibility: visible;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .invisible{visibility: hidden;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .static{position: static;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .fixed{position: fixed;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .absolute{position: absolute;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .relative{position: relative;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-inset-1{inset: -0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .inset-0{inset: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .inset-x-0{right: 0px;left: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .inset-y-0{top: 0px;bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-bottom-6{bottom: -1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-bottom-px{bottom: -1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-top-\[1\.75rem\]{top: -1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-top-\[2\.8rem\]{top: -2.8rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bottom-0{bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bottom-1{bottom: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bottom-1\.5{bottom: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bottom-2{bottom: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .left-0{right: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .left-1\/2{right: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .left-4{right: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .left-\[calc\(50\%\+10px\)\]{right: calc(50% + 10px);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .left-\[calc\(50\%\+12px\)\]{right: calc(50% + 12px);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .left-\[calc\(50\%\+14px\)\]{right: calc(50% + 14px);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .right-0{left: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .right-1\/2{left: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .right-2{left: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .right-2\.5{left: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .right-3{left: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .right-4{left: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .right-\[calc\(-50\%\+10px\)\]{left: calc(-50% + 10px);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .right-\[calc\(-50\%\+12px\)\]{left: calc(-50% + 12px);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .right-\[calc\(-50\%\+14px\)\]{left: calc(-50% + 14px);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .top-0{top: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .top-1\/2{top: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .top-2\.5{top: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .top-2\/4{top: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .top-3{top: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .top-3\.5{top: 0.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .top-4{top: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .top-8{top: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .top-full{top: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-z-10{z-index: -10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .z-0{z-index: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .z-10{z-index: 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .z-20{z-index: 20;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .z-50{z-index: 50;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .z-999999{z-index: 999999;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .z-\[10000\]{z-index: 10000;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .z-\[1\]{z-index: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .z-\[99999999\]{z-index: 99999999;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .z-auto{z-index: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-1{order: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-10{order: 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-11{order: 11;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-12{order: 12;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-2{order: 2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-3{order: 3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-4{order: 4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-5{order: 5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-6{order: 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-7{order: 7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-8{order: 8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-9{order: 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-first{order: -9999;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-last{order: 9999;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .order-none{order: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-1{grid-column: span 1 / span 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-10{grid-column: span 10 / span 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-11{grid-column: span 11 / span 11;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-12{grid-column: span 12 / span 12;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-2{grid-column: span 2 / span 2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-3{grid-column: span 3 / span 3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-4{grid-column: span 4 / span 4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-5{grid-column: span 5 / span 5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-6{grid-column: span 6 / span 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-7{grid-column: span 7 / span 7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-8{grid-column: span 8 / span 8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-span-9{grid-column: span 9 / span 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-1{grid-column-start: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-10{grid-column-start: 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-11{grid-column-start: 11;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-12{grid-column-start: 12;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-2{grid-column-start: 2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-3{grid-column-start: 3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-4{grid-column-start: 4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-5{grid-column-start: 5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-6{grid-column-start: 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-7{grid-column-start: 7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-8{grid-column-start: 8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .col-start-9{grid-column-start: 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .m-0{margin: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .m-1{margin: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .m-auto{margin: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mx-0{margin-right: 0px;margin-left: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mx-1{margin-right: 0.25rem;margin-left: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mx-2{margin-right: 0.5rem;margin-left: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mx-auto{margin-right: auto;margin-left: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .my-0{margin-top: 0px;margin-bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .my-12{margin-top: 3rem;margin-bottom: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .my-2{margin-top: 0.5rem;margin-bottom: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .my-5{margin-top: 1.25rem;margin-bottom: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .my-\[2\.43rem\]{margin-top: 2.43rem;margin-bottom: 2.43rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .my-\[3\.75px\]{margin-top: 3.75px;margin-bottom: 3.75px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .\!ml-0{margin-right: 0px !important;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-mt-1{margin-top: -0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mb-0{margin-bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mb-1{margin-bottom: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mb-1\.5{margin-bottom: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mb-2{margin-bottom: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mb-3{margin-bottom: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mb-4{margin-bottom: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mb-5{margin-bottom: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mb-8{margin-bottom: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ml-0{margin-right: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ml-1{margin-right: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ml-1\.5{margin-right: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ml-10{margin-right: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ml-2{margin-right: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ml-4{margin-right: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ml-8{margin-right: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ml-auto{margin-right: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-0{margin-left: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-0\.5{margin-left: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-1{margin-left: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-10{margin-left: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-2{margin-left: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-3{margin-left: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-4{margin-left: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-6{margin-left: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-7{margin-left: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-\[35px\]{margin-left: 35px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mr-auto{margin-left: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-0{margin-top: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-1{margin-top: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-1\.5{margin-top: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-10{margin-top: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-14{margin-top: 3.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-2{margin-top: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-2\.5{margin-top: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-3{margin-top: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-4{margin-top: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-5{margin-top: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-\[-8px\]{margin-top: -8px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-\[2px\]{margin-top: 2px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .mt-auto{margin-top: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .box-border{box-sizing: border-box;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .block{display: block;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .inline-block{display: inline-block;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .inline{display: inline;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex{display: flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .inline-flex{display: inline-flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .table{display: table;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flow-root{display: flow-root;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid{display: grid;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .contents{display: contents;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hidden{display: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .aspect-\[16\/9\]{aspect-ratio: 16/9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .aspect-video{aspect-ratio: 16 / 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-1\.5{width: 0.375rem;height: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-10{width: 2.5rem;height: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-12{width: 3rem;height: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-2{width: 0.5rem;height: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-2\.5{width: 0.625rem;height: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-3{width: 0.75rem;height: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-3\.5{width: 0.875rem;height: 0.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-4{width: 1rem;height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-5{width: 1.25rem;height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-6{width: 1.5rem;height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-7{width: 1.75rem;height: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .size-8{width: 2rem;height: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .\!h-full{height: 100% !important;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-0{height: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-1{height: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-10{height: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-12{height: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-2{height: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-2\.5{height: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-20{height: 5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-28{height: 7rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-3{height: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-36{height: 9rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-4{height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-5{height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-6{height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-60{height: 15rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-7{height: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-8{height: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-\[120px\]{height: 120px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-\[14rem\]{height: 14rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-\[2\.6rem\]{height: 2.6rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-\[36rem\]{height: 36rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-auto{height: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-fit{height: -moz-fit-content;height: fit-content;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-full{height: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-max{height: -moz-max-content;height: max-content;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-px{height: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .h-screen{height: 100vh;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-h-4{max-height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-h-\[10\.75rem\]{max-height: 10.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-h-\[13\.5rem\]{max-height: 13.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-16{min-height: 4rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-5{min-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-6{min-height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-7{min-height: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-\[14px\]{min-height: 14px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-\[2\.5rem\]{min-height: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-\[2rem\]{min-height: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-\[3rem\]{min-height: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-\[calc\(100vh_-_10rem\)\]{min-height: calc(100vh - 10rem);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-\[calc\(100vh_-_8rem\)\]{min-height: calc(100vh - 8rem);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-fit{min-height: -moz-fit-content;min-height: fit-content;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-full{min-height: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-h-screen{min-height: 100vh;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-0{width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1{width: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/10{width: 10%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/11{width: 9.0909091%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/12{width: 8.3333333%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/2{width: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/3{width: 33.333333%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/4{width: 25%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/5{width: 20%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/6{width: 16.666667%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/7{width: 14.2857143%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/8{width: 12.5%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-1\/9{width: 11.1111111%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-10{width: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-11{width: 2.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-12{width: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-120{width: 30rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-16{width: 4rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-2{width: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-2\.5{width: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-3{width: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-4{width: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-4\/5{width: 80%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-5{width: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-6{width: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-60{width: 15rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-72{width: 18rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-8{width: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-80{width: 20rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-9{width: 2.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-96{width: 24rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-\[18\.5rem\]{width: 18.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-\[22\.5rem\]{width: 22.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-\[24rem\]{width: 24rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-\[350px\]{width: 350px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-\[4\.375rem\]{width: 4.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-\[400px\]{width: 400px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-\[calc\(100\%\+0\.75rem\)\]{width: calc(100% + 0.75rem);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-\[calc\(100\%\+1rem\)\]{width: calc(100% + 1rem);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-\[calc\(100\%_-_5\.5rem\)\]{width: calc(100% - 5.5rem);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-auto{width: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-fit{width: -moz-fit-content;width: fit-content;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-full{width: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-max{width: -moz-max-content;width: max-content;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-min{width: -moz-min-content;width: min-content;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .w-screen{width: 100vw;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-0{min-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-10{min-width: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-12{min-width: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-6{min-width: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-8{min-width: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-80{min-width: 20rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-\[14px\]{min-width: 14px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-\[18\%\]{min-width: 18%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-\[180px\]{min-width: 180px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-\[8rem\]{min-width: 8rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .min-w-full{min-width: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-2xl{max-width: 42rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-32{max-width: 8rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-3xl{max-width: 48rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-4xl{max-width: 56rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-5xl{max-width: 64rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-6xl{max-width: 72rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-80{max-width: 20rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-\[17\.125rem\]{max-width: 17.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-\[200px\]{max-width: 200px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-\[39rem\]{max-width: 39rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-\[77rem\]{max-width: 77rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-\[84\%\]{max-width: 84%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-full{max-width: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-md{max-width: 28rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .max-w-xs{max-width: 20rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-1{flex: 1 1 0%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-auto{flex: 1 1 auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-shrink-0{flex-shrink: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shrink{flex-shrink: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shrink-0{flex-shrink: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-grow{flex-grow: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grow{flex-grow: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grow-0{flex-grow: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .table-fixed{table-layout: fixed;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-collapse{border-collapse: collapse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-separate{border-collapse: separate;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-spacing-0{--tw-border-spacing-x: 0px;--tw-border-spacing-y: 0px;border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .origin-left{transform-origin: right;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-translate-x-1\/2{--tw-translate-x: -50%;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-translate-x-2\/4{--tw-translate-x: -50%;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-translate-y-1\/2{--tw-translate-y: -50%;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-translate-y-2\/4{--tw-translate-y: -50%;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .translate-x-0{--tw-translate-x: 0px;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .translate-x-5{--tw-translate-x: 1.25rem;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .translate-x-\[-0\.375rem\]{--tw-translate-x: -0.375rem;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .translate-x-\[-0\.5rem\]{--tw-translate-x: -0.5rem;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .translate-y-0{--tw-translate-y: 0px;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .translate-y-4{--tw-translate-y: 1rem;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .-rotate-90{--tw-rotate: -90deg;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rotate-0{--tw-rotate: 0deg;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rotate-180{--tw-rotate: 180deg;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rotate-45{--tw-rotate: 45deg;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transform{transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}@keyframes pulse{50%{opacity: .5;}}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .animate-pulse{animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;}@keyframes spin{to{transform: rotate(-360deg);}}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .animate-spin{animation: spin 1s linear infinite;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .cursor-auto{cursor: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .cursor-default{cursor: default;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .cursor-help{cursor: help;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .cursor-not-allowed{cursor: not-allowed;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .cursor-pointer{cursor: pointer;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .resize{resize: both;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .snap-start{scroll-snap-align: start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .list-none{list-style-type: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .appearance-none{-webkit-appearance: none;-moz-appearance: none;appearance: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .auto-cols-auto{grid-auto-columns: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-flow-row{grid-auto-flow: row;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-flow-col{grid-auto-flow: column;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-flow-row-dense{grid-auto-flow: row dense;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-flow-col-dense{grid-auto-flow: column dense;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .auto-rows-auto{grid-auto-rows: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-1{grid-template-columns: repeat(1, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-10{grid-template-columns: repeat(10, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-11{grid-template-columns: repeat(11, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-12{grid-template-columns: repeat(12, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-2{grid-template-columns: repeat(2, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-3{grid-template-columns: repeat(3, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-4{grid-template-columns: repeat(4, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-5{grid-template-columns: repeat(5, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-6{grid-template-columns: repeat(6, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-7{grid-template-columns: repeat(7, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-8{grid-template-columns: repeat(8, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-9{grid-template-columns: repeat(9, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-\[auto_1fr\]{grid-template-columns: auto 1fr;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-cols-subgrid{grid-template-columns: subgrid;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-rows-\[auto_1fr\]{grid-template-rows: auto 1fr;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .grid-rows-subgrid{grid-template-rows: subgrid;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .\!flex-row{flex-direction: row !important;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-row{flex-direction: row;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-row-reverse{flex-direction: row-reverse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-col{flex-direction: column;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-col-reverse{flex-direction: column-reverse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-wrap{flex-wrap: wrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-wrap-reverse{flex-wrap: wrap-reverse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .flex-nowrap{flex-wrap: nowrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .place-content-center{place-content: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .content-center{align-content: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .content-start{align-content: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .items-start{align-items: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .items-end{align-items: flex-end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .items-center{align-items: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .items-baseline{align-items: baseline;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .items-stretch{align-items: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-normal{justify-content: normal;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-start{justify-content: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-end{justify-content: flex-end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-center{justify-content: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-between{justify-content: space-between;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-around{justify-content: space-around;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-evenly{justify-content: space-evenly;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-stretch{justify-content: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-items-end{justify-items: end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-0{gap: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-0\.5{gap: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-1{gap: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-1\.5{gap: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-2{gap: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-2\.5{gap: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-3{gap: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-4{gap: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-5{gap: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-6{gap: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-8{gap: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-x-1{-moz-column-gap: 0.25rem;column-gap: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-x-2{-moz-column-gap: 0.5rem;column-gap: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-x-3{-moz-column-gap: 0.75rem;column-gap: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-x-4{-moz-column-gap: 1rem;column-gap: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-x-5{-moz-column-gap: 1.25rem;column-gap: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-x-6{-moz-column-gap: 1.5rem;column-gap: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-x-8{-moz-column-gap: 2rem;column-gap: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-y-2{row-gap: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-y-4{row-gap: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-y-5{row-gap: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-y-6{row-gap: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .gap-y-8{row-gap: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.space-x-1 > :not([hidden]) ~ :not([hidden])){--tw-space-x-reverse: 0;margin-left: calc(0.25rem * var(--tw-space-x-reverse));margin-right: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.space-x-3 > :not([hidden]) ~ :not([hidden])){--tw-space-x-reverse: 0;margin-left: calc(0.75rem * var(--tw-space-x-reverse));margin-right: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.space-y-0\.5 > :not([hidden]) ~ :not([hidden])){--tw-space-y-reverse: 0;margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.space-y-1 > :not([hidden]) ~ :not([hidden])){--tw-space-y-reverse: 0;margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.space-y-2 > :not([hidden]) ~ :not([hidden])){--tw-space-y-reverse: 0;margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.space-y-3 > :not([hidden]) ~ :not([hidden])){--tw-space-y-reverse: 0;margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.space-y-4 > :not([hidden]) ~ :not([hidden])){--tw-space-y-reverse: 0;margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom: calc(1rem * var(--tw-space-y-reverse));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.space-y-6 > :not([hidden]) ~ :not([hidden])){--tw-space-y-reverse: 0;margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.divide-x > :not([hidden]) ~ :not([hidden])){--tw-divide-x-reverse: 0;border-left-width: calc(1px * var(--tw-divide-x-reverse));border-right-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.divide-x-0 > :not([hidden]) ~ :not([hidden])){--tw-divide-x-reverse: 0;border-left-width: calc(0px * var(--tw-divide-x-reverse));border-right-width: calc(0px * calc(1 - var(--tw-divide-x-reverse)));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.divide-y > :not([hidden]) ~ :not([hidden])){--tw-divide-y-reverse: 0;border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));border-bottom-width: calc(1px * var(--tw-divide-y-reverse));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.divide-y-0\.5 > :not([hidden]) ~ :not([hidden])){--tw-divide-y-reverse: 0;border-top-width: calc(0.5px * calc(1 - var(--tw-divide-y-reverse)));border-bottom-width: calc(0.5px * var(--tw-divide-y-reverse));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.divide-solid > :not([hidden]) ~ :not([hidden])){border-style: solid;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.divide-border-subtle > :not([hidden]) ~ :not([hidden])){--tw-divide-opacity: 1;border-color: rgb(230 230 239 / var(--tw-divide-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.divide-slate-200 > :not([hidden]) ~ :not([hidden])){--tw-divide-opacity: 1;border-color: rgb(226 232 240 / var(--tw-divide-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .self-start{align-self: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .self-end{align-self: flex-end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .self-center{align-self: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .self-stretch{align-self: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .self-baseline{align-self: baseline;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-self-auto{justify-self: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-self-start{justify-self: start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-self-end{justify-self: end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-self-center{justify-self: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .justify-self-stretch{justify-self: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .overflow-auto{overflow: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .overflow-hidden{overflow: hidden;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .overflow-visible{overflow: visible;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .overflow-x-auto{overflow-x: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .overflow-y-auto{overflow-y: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .overflow-x-hidden{overflow-x: hidden;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .truncate{overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .whitespace-nowrap{white-space: nowrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-wrap{text-wrap: wrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-nowrap{text-wrap: nowrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded{border-radius: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-2xl{border-radius: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-\[0\.1875rem\]{border-radius: 0.1875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-\[4px\]{border-radius: 4px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-full{border-radius: 9999px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-lg{border-radius: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-md{border-radius: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-none{border-radius: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-sm{border-radius: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-xl{border-radius: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-b-none{border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-b-xl{border-bottom-left-radius: 0.75rem;border-bottom-right-radius: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-bl{border-bottom-right-radius: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-bl-md{border-bottom-right-radius: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-br{border-bottom-left-radius: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-br-md{border-bottom-left-radius: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-tl{border-top-right-radius: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-tl-md{border-top-right-radius: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-tl-none{border-top-right-radius: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-tr{border-top-left-radius: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-tr-md{border-top-left-radius: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rounded-tr-none{border-top-left-radius: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border{border-width: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-0{border-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-0\.5{border-width: 0.5px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-\[0\.5px\]{border-width: 0.5px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-x-0{border-right-width: 0px;border-left-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-y{border-top-width: 1px;border-bottom-width: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-y-0{border-top-width: 0px;border-bottom-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-b{border-bottom-width: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-b-0{border-bottom-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-b-0\.5{border-bottom-width: 0.5px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-b-2{border-bottom-width: 2px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-l{border-right-width: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-l-0{border-right-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-r{border-left-width: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-r-0{border-left-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-t{border-top-width: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-t-0{border-top-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-solid{border-style: solid;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-dashed{border-style: dashed;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-dotted{border-style: dotted;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-double{border-style: double;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-hidden{border-style: hidden;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-none{border-style: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-\[\#0EA5E9\]{--tw-border-opacity: 1;border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-\[\#E6E6EF\]{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-alert-border-danger{--tw-border-opacity: 1;border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-alert-border-green{--tw-border-opacity: 1;border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-alert-border-info{--tw-border-opacity: 1;border-color: rgb(186 230 253 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-alert-border-neutral{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-alert-border-warning{--tw-border-opacity: 1;border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-astra{--tw-border-opacity: 1;border-color: rgb(92 46 222 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-background-inverse{--tw-border-opacity: 1;border-color: rgb(20 19 56 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-background-secondary{--tw-border-opacity: 1;border-color: rgb(243 243 248 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-badge-border-disabled{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-badge-border-gray{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-badge-border-green{--tw-border-opacity: 1;border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-badge-border-red{--tw-border-opacity: 1;border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-badge-border-sky{--tw-border-opacity: 1;border-color: rgb(186 230 253 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-badge-border-yellow{--tw-border-opacity: 1;border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-border-disabled{--tw-border-opacity: 1;border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-border-strong{--tw-border-opacity: 1;border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-border-subtle{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-brand-primary-600{--tw-border-opacity: 1;border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-button-primary{--tw-border-opacity: 1;border-color: rgb(92 46 222 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-field-border{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-field-dropzone-color{--tw-border-opacity: 1;border-color: rgb(92 46 222 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-focus-error-border{--tw-border-opacity: 1;border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-gray-200{--tw-border-opacity: 1;border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-sky-500{--tw-border-opacity: 1;border-color: rgb(14 165 233 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-slate-200{--tw-border-opacity: 1;border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-slate-400{--tw-border-opacity: 1;border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-slate-800{--tw-border-opacity: 1;border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-tab-border{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-text-inverse{--tw-border-opacity: 1;border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-toggle-off-border{--tw-border-opacity: 1;border-color: rgb(232 207 248 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-transparent{border-color: transparent;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .border-b-border-subtle{--tw-border-opacity: 1;border-bottom-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-\[\#4F4E7C4D\]{background-color: #4F4E7C4D;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-\[\#E8CFF8\]{--tw-bg-opacity: 1;background-color: rgb(232 207 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-\[\#EFD7F9\]{--tw-bg-opacity: 1;background-color: rgb(239 215 249 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-\[\#F9FAFB\]{--tw-bg-opacity: 1;background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-alert-background-danger{--tw-bg-opacity: 1;background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-alert-background-green{--tw-bg-opacity: 1;background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-alert-background-info{--tw-bg-opacity: 1;background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-alert-background-neutral{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-alert-background-warning{--tw-bg-opacity: 1;background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-astra{--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-background-brand{--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-background-inverse{--tw-bg-opacity: 1;background-color: rgb(20 19 56 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-background-inverse\/90{background-color: rgb(20 19 56 / 0.9);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-background-primary{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-background-secondary{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-background-disabled{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-background-gray{--tw-bg-opacity: 1;background-color: rgb(249 250 252 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-background-green{--tw-bg-opacity: 1;background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-background-red{--tw-bg-opacity: 1;background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-background-sky{--tw-bg-opacity: 1;background-color: rgb(240 249 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-background-yellow{--tw-bg-opacity: 1;background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-hover-disabled{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-hover-gray{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-hover-green{--tw-bg-opacity: 1;background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-hover-red{--tw-bg-opacity: 1;background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-hover-sky{--tw-bg-opacity: 1;background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-badge-hover-yellow{--tw-bg-opacity: 1;background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-black{--tw-bg-opacity: 1;background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-black\/50{background-color: rgb(0 0 0 / 0.5);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-blue-50{--tw-bg-opacity: 1;background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-border-interactive{--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-border-subtle{--tw-bg-opacity: 1;background-color: rgb(230 230 239 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-brand-background-50{--tw-bg-opacity: 1;background-color: rgb(231 224 250 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-brand-primary-600{--tw-bg-opacity: 1;background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-button-danger{--tw-bg-opacity: 1;background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-button-disabled{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-button-primary{--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-button-secondary{--tw-bg-opacity: 1;background-color: rgb(239 215 249 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-button-tertiary{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-button-tertiary-hover{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-current{background-color: currentColor;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-field-background-disabled{--tw-bg-opacity: 1;background-color: rgb(249 250 252 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-field-background-error{--tw-bg-opacity: 1;background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-field-dropzone-background-hover{--tw-bg-opacity: 1;background-color: rgb(249 250 252 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-field-primary-background{--tw-bg-opacity: 1;background-color: rgb(249 250 252 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-field-secondary-background{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-gray-200{--tw-bg-opacity: 1;background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-gray-50{--tw-bg-opacity: 1;background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-icon-interactive{--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-misc-progress-background{--tw-bg-opacity: 1;background-color: rgb(230 230 239 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-slate-200{--tw-bg-opacity: 1;background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-slate-50{--tw-bg-opacity: 1;background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-slate-800{--tw-bg-opacity: 1;background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-tab-background{--tw-bg-opacity: 1;background-color: rgb(249 250 252 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-text-tertiary{--tw-bg-opacity: 1;background-color: rgb(159 159 191 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-toggle-dial-background{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-toggle-off{--tw-bg-opacity: 1;background-color: rgb(239 215 249 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-toggle-off-disabled{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-tooltip-background-dark{--tw-bg-opacity: 1;background-color: rgb(20 19 56 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-tooltip-background-light{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-transparent{background-color: transparent;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-white{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-opacity-50{--tw-bg-opacity: 0.5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-opacity-70{--tw-bg-opacity: 0.7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-opacity-90{--tw-bg-opacity: 0.9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .bg-gradient-to-b{background-image: linear-gradient(to bottom, var(--tw-gradient-stops));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .from-white{--tw-gradient-from: #fff var(--tw-gradient-from-position);--tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .to-\[\#f3f3f8\]{--tw-gradient-to: #f3f3f8 var(--tw-gradient-to-position);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .fill-current{fill: currentColor;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .stroke-current{stroke: currentColor;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .stroke-icon-primary{stroke: #141338;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .object-contain{-o-object-fit: contain;object-fit: contain;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .object-cover{-o-object-fit: cover;object-fit: cover;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .object-center{-o-object-position: center;object-position: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-0{padding: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-0\.5{padding: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-1{padding: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-1\.5{padding: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-10{padding: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-2{padding: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-2\.5{padding: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-3{padding: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-3\.5{padding: 0.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-4{padding: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-5{padding: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-6{padding: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-8{padding: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .p-\[0\.7rem\]{padding: 0.7rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-0\.5{padding-right: 0.125rem;padding-left: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-1{padding-right: 0.25rem;padding-left: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-1\.5{padding-right: 0.375rem;padding-left: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-12{padding-right: 3rem;padding-left: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-2{padding-right: 0.5rem;padding-left: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-2\.5{padding-right: 0.625rem;padding-left: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-3{padding-right: 0.75rem;padding-left: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-3\.5{padding-right: 0.875rem;padding-left: 0.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-4{padding-right: 1rem;padding-left: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-5{padding-right: 1.25rem;padding-left: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-5\.5{padding-right: 1.375rem;padding-left: 1.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-6{padding-right: 1.5rem;padding-left: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-8{padding-right: 2rem;padding-left: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-\[0\.8125rem\]{padding-right: 0.8125rem;padding-left: 0.8125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .px-\[2\.9375rem\]{padding-right: 2.9375rem;padding-left: 2.9375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-0{padding-top: 0px;padding-bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-0\.5{padding-top: 0.125rem;padding-bottom: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-1{padding-top: 0.25rem;padding-bottom: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-1\.5{padding-top: 0.375rem;padding-bottom: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-2{padding-top: 0.5rem;padding-bottom: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-2\.5{padding-top: 0.625rem;padding-bottom: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-3{padding-top: 0.75rem;padding-bottom: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-3\.5{padding-top: 0.875rem;padding-bottom: 0.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-4{padding-top: 1rem;padding-bottom: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-5{padding-top: 1.25rem;padding-bottom: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-6{padding-top: 1.5rem;padding-bottom: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-\[0\.6875rem\]{padding-top: 0.6875rem;padding-bottom: 0.6875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .py-\[0rem\]{padding-top: 0rem;padding-bottom: 0rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pb-1{padding-bottom: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pb-2{padding-bottom: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pb-3{padding-bottom: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pb-4{padding-bottom: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pb-5{padding-bottom: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pb-6{padding-bottom: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pb-9{padding-bottom: 2.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pb-\[17\.5rem\]{padding-bottom: 17.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-0{padding-right: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-1{padding-right: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-10{padding-right: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-2{padding-right: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-2\.5{padding-right: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-3{padding-right: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-3\.5{padding-right: 0.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-4{padding-right: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-8{padding-right: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-9{padding-right: 2.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pl-\[72px\]{padding-right: 72px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pr-10{padding-left: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pr-12{padding-left: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pr-2{padding-left: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pr-2\.5{padding-left: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pr-3{padding-left: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pr-8{padding-left: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pr-9{padding-left: 2.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pr-\[72px\]{padding-left: 72px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pt-0{padding-top: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pt-10{padding-top: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pt-12{padding-top: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pt-2{padding-top: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pt-3{padding-top: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pt-5{padding-top: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pt-6{padding-top: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .pt-\[4rem\]{padding-top: 4rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-left{text-align: right;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-center{text-align: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .align-top{vertical-align: top;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .align-middle{vertical-align: middle;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .font-mono{font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-2xl{font-size: 1.5rem;line-height: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-3xl{font-size: 1.875rem;line-height: 2.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-4xl{font-size: 2.25rem;line-height: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-\[0\.625rem\]{font-size: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-\[0\.75rem\]{font-size: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-\[10px\]{font-size: 10px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-base{font-size: 1rem;line-height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-lg{font-size: 1.125rem;line-height: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-sm{font-size: 0.875rem;line-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-tiny{font-size: 0.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-xl{font-size: 1.25rem;line-height: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-xs{font-size: 0.75rem;line-height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-xxs{font-size: 0.6875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .font-bold{font-weight: 700;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .font-medium{font-weight: 500;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .font-normal{font-weight: 400;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .font-semibold{font-weight: 600;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .uppercase{text-transform: uppercase;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .capitalize{text-transform: capitalize;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .italic{font-style: italic;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-11{line-height: 2.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-4{line-height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-5{line-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-6{line-height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-7{line-height: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-7\.5{line-height: 1.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-8{line-height: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-9\.5{line-height: 2.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-\[0\.875rem\]{line-height: 0.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-\[1\.375rem\]{line-height: 1.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-\[1\.625rem\]{line-height: 1.625rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-\[10px\]{line-height: 10px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-\[1rem\]{line-height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .leading-none{line-height: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .tracking-2{letter-spacing: 0.125em;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-\[\#046BD2\]{--tw-text-opacity: 1;color: rgb(4 107 210 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-\[\#475569\]{--tw-text-opacity: 1;color: rgb(71 85 105 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-\[\#4AB866\]{--tw-text-opacity: 1;color: rgb(74 184 102 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-\[\#4B5563\]{--tw-text-opacity: 1;color: rgb(75 85 99 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-\[\#CBD5E1\]{--tw-text-opacity: 1;color: rgb(203 213 225 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-astra{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-background-brand{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-background-primary{--tw-text-opacity: 1;color: rgb(255 255 255 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-badge-color-disabled{--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-badge-color-gray{--tw-text-opacity: 1;color: rgb(35 34 80 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-badge-color-green{--tw-text-opacity: 1;color: rgb(21 128 61 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-badge-color-red{--tw-text-opacity: 1;color: rgb(185 28 28 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-badge-color-sky{--tw-text-opacity: 1;color: rgb(3 105 161 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-badge-color-yellow{--tw-text-opacity: 1;color: rgb(161 98 7 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-border-strong{--tw-text-opacity: 1;color: rgb(107 114 128 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-brand-800{--tw-text-opacity: 1;color: rgb(30 64 175 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-brand-primary-600{--tw-text-opacity: 1;color: rgb(37 99 235 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-button-danger{--tw-text-opacity: 1;color: rgb(220 38 38 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-button-primary{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-button-secondary{--tw-text-opacity: 1;color: rgb(239 215 249 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-button-tertiary-color{--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-field-color-disabled{--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-field-dropzone-color{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-field-helper{--tw-text-opacity: 1;color: rgb(159 159 191 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-field-input{--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-field-label{--tw-text-opacity: 1;color: rgb(79 78 124 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-field-placeholder{--tw-text-opacity: 1;color: rgb(159 159 191 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-gray-600{--tw-text-opacity: 1;color: rgb(75 85 99 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-gray-800{--tw-text-opacity: 1;color: rgb(31 41 55 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-icon-disabled{--tw-text-opacity: 1;color: rgb(209 213 219 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-icon-inverse{--tw-text-opacity: 1;color: rgb(159 159 191 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-icon-on-color-disabled{--tw-text-opacity: 1;color: rgb(159 159 191 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-icon-primary{--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-icon-secondary{--tw-text-opacity: 1;color: rgb(111 107 153 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-link-inverse{--tw-text-opacity: 1;color: rgb(56 189 248 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-link-primary{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-purple-600{--tw-text-opacity: 1;color: rgb(147 51 234 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-purple-light{--tw-text-opacity: 1;color: rgb(217 171 255 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-slate-400{--tw-text-opacity: 1;color: rgb(148 163 184 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-slate-500{--tw-text-opacity: 1;color: rgb(100 116 139 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-slate-600{--tw-text-opacity: 1;color: rgb(71 85 105 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-slate-800{--tw-text-opacity: 1;color: rgb(30 41 59 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-support-error{--tw-text-opacity: 1;color: rgb(220 38 38 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-support-error-inverse{--tw-text-opacity: 1;color: rgb(248 113 113 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-support-info{--tw-text-opacity: 1;color: rgb(2 132 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-support-info-inverse{--tw-text-opacity: 1;color: rgb(56 189 248 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-support-success{--tw-text-opacity: 1;color: rgb(22 163 74 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-support-success-inverse{--tw-text-opacity: 1;color: rgb(74 222 128 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-support-warning{--tw-text-opacity: 1;color: rgb(234 179 8 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-support-warning-inverse{--tw-text-opacity: 1;color: rgb(253 224 71 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-text-disabled{--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-text-inverse{--tw-text-opacity: 1;color: rgb(255 255 255 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-text-on-color{--tw-text-opacity: 1;color: rgb(255 255 255 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-text-primary{--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-text-secondary{--tw-text-opacity: 1;color: rgb(79 78 124 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-text-tertiary{--tw-text-opacity: 1;color: rgb(159 159 191 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-tooltip-background-dark{--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-tooltip-background-light{--tw-text-opacity: 1;color: rgb(255 255 255 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .text-white{--tw-text-opacity: 1;color: rgb(255 255 255 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .underline{text-decoration-line: underline;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .no-underline{text-decoration-line: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .placeholder-text-tertiary::-moz-placeholder{--tw-placeholder-opacity: 1;color: rgb(159 159 191 / var(--tw-placeholder-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .placeholder-text-tertiary::placeholder{--tw-placeholder-opacity: 1;color: rgb(159 159 191 / var(--tw-placeholder-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .opacity-0{opacity: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .opacity-100{opacity: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .opacity-30{opacity: 0.3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .opacity-40{opacity: 0.4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .opacity-50{opacity: 0.5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-2xl{--tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);--tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-md{--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-none{--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-overlay-modal{--tw-shadow: 0px 32px 64px -24px rgba(0, 0, 0, 0.24);--tw-shadow-colored: 0px 32px 64px -24px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-sm{--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-soft-shadow-2xl{--tw-shadow: 0px 24px 64px -12px rgba(149, 160, 178, 0.32);--tw-shadow-colored: 0px 24px 64px -12px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-soft-shadow-lg{--tw-shadow: 0px 12px 32px -12px rgba(149, 160, 178, 0.24);--tw-shadow-colored: 0px 12px 32px -12px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-xl{--tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);--tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .shadow-xs{--tw-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);--tw-shadow-colored: 0px 1px 2px 0px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-none{outline: 2px solid transparent;outline-offset: 2px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline{outline-style: solid;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-1{outline-width: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-border-disabled{outline-color: #E5E7EB;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-border-interactive{outline-color: #5C2EDE;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-border-subtle{outline-color: #E6E6EF;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-button-danger{outline-color: #DC2626;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-button-danger-hover{outline-color: #B91C1C;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-button-disabled{outline-color: #F3F3F8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-button-primary{outline-color: #5C2EDE;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-button-secondary{outline-color: #EFD7F9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-field-border{outline-color: #E6E6EF;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-field-border-disabled{outline-color: #F3F3F8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-focus-error-border{outline-color: #FECACA;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .outline-transparent{outline-color: transparent;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-0{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-1{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-alert-border-danger{--tw-ring-opacity: 1;--tw-ring-color: rgb(254 202 202 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-alert-border-green{--tw-ring-opacity: 1;--tw-ring-color: rgb(187 247 208 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-alert-border-info{--tw-ring-opacity: 1;--tw-ring-color: rgb(186 230 253 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-alert-border-neutral{--tw-ring-opacity: 1;--tw-ring-color: rgb(230 230 239 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-alert-border-warning{--tw-ring-opacity: 1;--tw-ring-color: rgb(254 240 138 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-background-inverse{--tw-ring-opacity: 1;--tw-ring-color: rgb(20 19 56 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-border-subtle{--tw-ring-opacity: 1;--tw-ring-color: rgb(230 230 239 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-border-transparent-subtle{--tw-ring-color: #37415114;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-brand-primary-600{--tw-ring-opacity: 1;--tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(92 46 222 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-tab-border{--tw-ring-opacity: 1;--tw-ring-color: rgb(230 230 239 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ring-offset-0{--tw-ring-offset-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .blur-md{--tw-blur: blur(12px);filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .filter{filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transition{transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transition-\[box-shadow\2c color\2c background-color\]{transition-property: box-shadow,color,background-color;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transition-\[color\2c box-shadow\2c outline\]{transition-property: color,box-shadow,outline;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transition-\[color\2c outline\2c box-shadow\]{transition-property: color,outline,box-shadow;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transition-\[outline\2c background-color\2c color\2c box-shadow\]{transition-property: outline,background-color,color,box-shadow;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transition-all{transition-property: all;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transition-colors{transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transition-opacity{transition-property: opacity;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .transition-transform{transition-property: transform;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .duration-150{transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .duration-200{transition-duration: 200ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .duration-300{transition-duration: 300ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .duration-500{transition-duration: 500ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ease-in{transition-timing-function: cubic-bezier(0.4, 0, 1, 1);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ease-in-out{transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ease-linear{transition-timing-function: linear;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .ease-out{transition-timing-function: cubic-bezier(0, 0, 0.2, 1);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .\[grid-area\:1\/1\/2\/3\]{grid-area: 1/1/2/3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .\[word-break\:break-word\]{word-break: break-word;}:root{--accent-color: #2271b1;--accent-hv-color: #0a4b78;--background-light-color: #f0f0f1;--heading-color: #1d2327;--content-color: #3c434a;--link-color: #2271b1;--link-hv-color: #0a4b78;--metabox-background-color: #fff}#astra-dashboard-app [type=text]:focus,#astra-dashboard-app [type=email]:focus,#astra-dashboard-app [type=url]:focus,#astra-dashboard-app [type=password]:focus,#astra-dashboard-app [type=number]:focus,#astra-dashboard-app [type=date]:focus,#astra-dashboard-app [type=datetime-local]:focus,#astra-dashboard-app [type=month]:focus,#astra-dashboard-app [type=search]:focus,#astra-dashboard-app [type=tel]:focus,#astra-dashboard-app [type=time]:focus,#astra-dashboard-app [type=week]:focus{outline-offset:0}.wp-admin{overflow-y:scroll}#wpbody-content>.notice,#wpbody-content>.error{display:none !important}.ast-menu-page-wrapper,.ast-menu-page-wrapper *{box-sizing:border-box}.ast-menu-page-wrapper a:focus,.ast-menu-page-wrapper button:focus,.ast-menu-page-wrapper input:focus{box-shadow:none;outline:none}.toplevel_page_astra #wpcontent{padding-right:0}.toplevel_page_astra #wpbody-content{padding-bottom:40px}.doc-icon{cursor:pointer}.hover\:svg-hover-color:hover svg path{stroke:#1e293b}button:focus-visible .svg-focusable .svg-path{stroke:#1e293b}#wpwrap{background-color:#f8fafc}#wpwrap .astra-admin__input-field{color:#64748b;background-color:#fff;border:1px solid #e2e8f0;border-radius:.375rem;padding:.5rem .75rem;font-size:1rem;line-height:1.25rem}#wpwrap .astra-admin__input-field:focus{outline:none;border-color:#94a3b8;box-shadow:none}#wpwrap .astra-admin__input-field:focus+.astra-admin__input-field--end-display{border-color:#94a3b8;box-shadow:none}#wpwrap .astra-admin__dropdown{padding-left:2rem}#wpwrap .astra-admin__dropdown:hover{color:#2c3338}#wpwrap .astra-admin__block-label{box-sizing:content-box}.astra-dep-field-false{pointer-events:none;opacity:.4}.ast-menu-page-wrapper,.ast-menu-page-wrapper p,.ast-kb-section{font-family:"Figtree","Inter",sans-serif;font-size:.875rem;line-height:1.25rem}.astra-video-container{position:relative;width:100%}.astra-video-container .astra-video{position:absolute;top:0;right:0;width:100%;height:100%;border:0}.astra-icon-transition svg,.astra-icon-transition path{transition-property:stroke;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.astra-changelog-description strong{font-weight:500;font-size:.875rem;line-height:1.25rem;color:#1e293b}.astra-changelog-description ul{margin-top:.25rem;margin-bottom:.75rem}.astra-changelog-description ul:last-child{margin-top:0}.astra-changelog-description li{font-weight:400;font-size:.75rem;line-height:1rem;color:#64748b}.ast-welcome-screen .ast-licensing-wrap{padding-right:2rem;padding-left:2rem}.ast-kb-inner-wrap .ast-box-shadow-none{box-shadow:none}.ast-docs-search-fields[type=search]{padding:9px 0;padding-right:2.875rem;border:1px solid #cbd5e1;box-shadow:0px 1px 2px rgba(0,0,0,.05);border-radius:6px;line-height:1.625rem}.ast-docs-search-fields[type=search]::-webkit-search-cancel-button{height:1em;width:1em;font-size:2em;opacity:0;position:absolute;right:1rem;pointer-events:all;z-index:999}.astra-changelog-description *{margin-bottom:15px;line-height:1.8}[dir=rtl] .astra-dashboard-app button.bg-astra[aria-checked=false] span{left:0}.translate-x-5.toggle-bubble{--tw-translate-x: 1.02rem}[dir=rtl] .astra-dashboard-app button.bg-astra[aria-checked=true] .toggle-bubble{transform:translateX(-18px)}.ab-top-menu img,#adminmenumain img{display:initial;vertical-align:initial}#wpfooter{background:#f3f3f8}div.shadow-overlay-modal{--tw-shadow: 0px 32px 60px -20px rgba(0, 0, 0, 0.5);--tw-shadow-colored: 0px 32px 64px -24px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)),var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)),var(--tw-shadow)}.ast-footer-thankyou a{color:#5c2ede;text-decoration:underline}.extension-logo>svg{width:40px;height:40px}.installer-spinner{margin-right:8px;margin-left:8px;height:1rem;width:1rem}@media(max-width: 781px){div.ast-changelog-popup-wrap{margin-top:46px}div .tablet\:w-full{width:100%}div .tablet\:my-2{margin-top:.5rem;margin-bottom:.5rem}div .tablet\:my-4{margin-top:1rem;margin-bottom:1rem}div .tablet\:my-16{margin-top:4rem;margin-bottom:4rem}div .tablet\:block{display:block}div .tablet\:mr-2{margin-left:.5rem}div .-tablet\:mt\:10{margin-top:-2.5rem}div .tablet\:none{display:none}}@media(max-width: 600px){.ast-kb-section{margin-top:20px}.ast-kb-inner-wrap{padding:0}.ast-kb-caret{align-items:flex-start}.ast-kb-caret svg{margin-top:3px}}#astra-dashboard-app section.astra-child-field{border-width:0;border-top:1px solid #e6e6ef;padding:32px 0 0}#astra-dashboard-app section.astra-child-field h3,#astra-dashboard-app section.astra-child-field p,#astra-dashboard-app section.astra-child-field select{margin:0}#astra-dashboard-app section.astra-child-field input[type=file]{padding:.5rem !important}#astra-dashboard-app section.astra-child-field select,#astra-dashboard-app section.astra-child-field input[type=file]{box-shadow:none;border:1px solid #e6e6ef}#astra-dashboard-app section.astra-child-field select:focus,#astra-dashboard-app section.astra-child-field input[type=file]:focus{box-shadow:none;border:1px solid #5c2ede}#astra-dashboard-app section.astra-child-field button{height:40px;margin-right:.75rem;cursor:pointer}:is(#astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view) .focus\:ring-offset-2:focus{--tw-ring-offset-width: none !important}@media screen and (max-width: 782px){#astra-dashboard-app section.astra-child-field>div{flex-direction:column;align-items:start}#astra-dashboard-app section.astra-child-field form{margin:8px 0}}.card-border:hover{border:.5px solid #e6e6ef}.card-border{border:.5px solid rgba(255,0,0,0)}.extensions-page .extention-logo{width:42px;height:42px}.spectra-screen{border:1px solid}:root{--heading-font-family: "Inter";--heading-font-style: normal;--heading-weight-500: 500;--heading-weight-400: 400;--heading-font-size: 18px;--heading-line-height: 24px}.shadow-focused:focus.shadow-sm{box-shadow:0 1px 2px 0 rgba(0,0,0,.05) !important}.shadow-focused:focus.shadow{box-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1) !important}.shadow-focused:focus.shadow-md{box-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1) !important}.shadow-focused:focus.shadow-lg{box-shadow:0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -4px rgba(0,0,0,.1) !important}.shadow-focused:focus.shadow-xl{box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 8px 10px -6px rgba(0,0,0,.1) !important}.shadow-focused:focus.shadow-2xl{box-shadow:0 25px 50px -12px rgba(0,0,0,.25) !important}.shadow-focused:focus.shadow-inner{box-shadow:inset 0 2px 4px 0 rgba(0,0,0,.05) !important}div.whats-new-rss-flyout{font-family:"Figtree","Inter",sans-serif;font-size:.875rem;line-height:1.25rem}div.whats-new-rss-flyout .whats-new-rss-flyout-contents{top:32px !important;height:calc(100% - 32px) !important}div.whats-new-rss-flyout .whats-new-rss-flyout-contents .whats-new-rss-flyout-inner-header{padding:20px !important}div.whats-new-rss-flyout .whats-new-rss-flyout-contents .whats-new-rss-flyout-inner-content .new-post-badge{display:none}div.whats-new-rss-flyout .whats-new-rss-flyout-contents .whats-new-rss-flyout-inner-content .whats-new-rss-flyout-inner-content-item .rss-content-header h2{font-weight:600 !important}div.whats-new-rss-flyout .whats-new-rss-flyout-contents .whats-new-rss-flyout-inner-content .whats-new-rss-flyout-inner-content-item a:active,div.whats-new-rss-flyout .whats-new-rss-flyout-contents .whats-new-rss-flyout-inner-content .whats-new-rss-flyout-inner-content-item a:focus{box-shadow:none}@media screen and (max-width: 782px){.whats-new-rss-flyout .whats-new-rss-flyout-contents{top:46px !important}}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .file\:border-0::file-selector-button{border-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .file\:bg-transparent::file-selector-button{background-color: transparent;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .file\:text-text-tertiary::file-selector-button{--tw-text-opacity: 1;color: rgb(159 159 191 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .placeholder\:text-field-placeholder::-moz-placeholder{--tw-text-opacity: 1;color: rgb(159 159 191 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .placeholder\:text-field-placeholder::placeholder{--tw-text-opacity: 1;color: rgb(159 159 191 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .placeholder\:text-text-disabled::-moz-placeholder{--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .placeholder\:text-text-disabled::placeholder{--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:absolute::before{content: var(--tw-content);position: absolute;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:top-2\/4::before{content: var(--tw-content);top: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:hidden::before{content: var(--tw-content);display: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:h-10::before{content: var(--tw-content);height: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:w-10::before{content: var(--tw-content);width: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:-translate-x-2\/4::before{content: var(--tw-content);--tw-translate-x: -50%;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:-translate-y-2\/4::before{content: var(--tw-content);--tw-translate-y: -50%;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:rounded-full::before{content: var(--tw-content);border-radius: 9999px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:opacity-0::before{content: var(--tw-content);opacity: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:transition-opacity::before{content: var(--tw-content);transition-property: opacity;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .before\:content-\[\'\'\]::before{--tw-content: '';content: var(--tw-content);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .after\:ml-0::after{content: var(--tw-content);margin-right: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .after\:ml-0\.5::after{content: var(--tw-content);margin-right: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .after\:text-field-required::after{content: var(--tw-content);--tw-text-opacity: 1;color: rgb(220 38 38 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .after\:content-\[\'\*\'\]::after{--tw-content: '*';content: var(--tw-content);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .first\:rounded-bl:first-child{border-bottom-right-radius: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .first\:rounded-tl:first-child{border-top-right-radius: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .first\:border-0:first-child{border-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .first\:border-r:first-child{border-left-width: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .first\:border-border-subtle:first-child{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .last\:rounded-br:last-child{border-bottom-left-radius: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .last\:rounded-tr:last-child{border-top-left-radius: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .last\:border-0:last-child{border-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:border-border-interactive:checked{--tw-border-opacity: 1;border-color: rgb(92 46 222 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:border-toggle-on-border:checked{--tw-border-opacity: 1;border-color: rgb(99 74 224 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:bg-toggle-on:checked{--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:\[background-image\:none\]:checked{background-image: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:before\:hidden:checked::before{content: var(--tw-content);display: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:before\:content-\[\'\'\]:checked::before{--tw-content: '';content: var(--tw-content);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:z-10:focus-within{z-index: 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:border-focus-border:focus-within{--tw-border-opacity: 1;border-color: rgb(232 207 248 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:text-field-input:focus-within{--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:outline-none:focus-within{outline: 2px solid transparent;outline-offset: 2px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:outline-focus-border:focus-within{outline-color: #E8CFF8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:ring-2:focus-within{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:ring-focus:focus-within{--tw-ring-opacity: 1;--tw-ring-color: rgb(92 46 222 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:ring-offset-2:focus-within{--tw-ring-offset-width: 2px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:flex:hover{display: flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:border-border-disabled:hover{--tw-border-opacity: 1;border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:border-border-interactive:hover{--tw-border-opacity: 1;border-color: rgb(92 46 222 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:border-border-strong:hover{--tw-border-opacity: 1;border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:border-border-subtle:hover{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:border-button-primary:hover{--tw-border-opacity: 1;border-color: rgb(92 46 222 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:border-field-border:hover{--tw-border-opacity: 1;border-color: rgb(230 230 239 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:border-field-dropzone-color:hover{--tw-border-opacity: 1;border-color: rgb(92 46 222 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:border-slate-300:hover{--tw-border-opacity: 1;border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:border-text-inverse:hover{--tw-border-opacity: 1;border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-astra-hover:hover{--tw-bg-opacity: 1;background-color: rgb(173 56 226 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-background-brand:hover{--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-background-button-hover:hover{--tw-bg-opacity: 1;background-color: rgb(232 207 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-background-secondary:hover{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-badge-hover-disabled:hover{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-badge-hover-gray:hover{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-badge-hover-green:hover{--tw-bg-opacity: 1;background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-badge-hover-red:hover{--tw-bg-opacity: 1;background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-badge-hover-sky:hover{--tw-bg-opacity: 1;background-color: rgb(224 242 254 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-badge-hover-yellow:hover{--tw-bg-opacity: 1;background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-button-danger-hover:hover{--tw-bg-opacity: 1;background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-button-primary:hover{--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-button-primary-hover:hover{--tw-bg-opacity: 1;background-color: rgb(173 56 226 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-button-secondary:hover{--tw-bg-opacity: 1;background-color: rgb(239 215 249 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-button-secondary-hover:hover{--tw-bg-opacity: 1;background-color: rgb(59 58 106 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-button-tertiary-hover:hover{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-field-background-error:hover{--tw-bg-opacity: 1;background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-field-dropzone-background-hover:hover{--tw-bg-opacity: 1;background-color: rgb(249 250 252 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-gray-100:hover{--tw-bg-opacity: 1;background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-gray-50:hover{--tw-bg-opacity: 1;background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-link-primary-hover:hover{--tw-bg-opacity: 1;background-color: rgb(173 56 226 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-transparent:hover{background-color: transparent;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:bg-white:hover{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-\[\#1E293B\]:hover{--tw-text-opacity: 1;color: rgb(30 41 59 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-astra-hover:hover{--tw-text-opacity: 1;color: rgb(173 56 226 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-black:hover{--tw-text-opacity: 1;color: rgb(0 0 0 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-button-danger-secondary:hover{--tw-text-opacity: 1;color: rgb(220 38 38 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-button-primary-hover:hover{--tw-text-opacity: 1;color: rgb(173 56 226 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-link-primary:hover{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-link-primary-hover:hover{--tw-text-opacity: 1;color: rgb(173 56 226 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-purple-light-hover:hover{--tw-text-opacity: 1;color: rgb(179 120 229 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-slate-800:hover{--tw-text-opacity: 1;color: rgb(30 41 59 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-text-disabled:hover{--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-text-inverse:hover{--tw-text-opacity: 1;color: rgb(255 255 255 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-text-primary:hover{--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:text-white:hover{--tw-text-opacity: 1;color: rgb(255 255 255 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:underline:hover{text-decoration-line: underline;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:no-underline:hover{text-decoration-line: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:shadow-hover:hover{--tw-shadow: 0px 12px 24px -12px rgba(0, 0, 0, 0.12);--tw-shadow-colored: 0px 12px 24px -12px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:shadow-md:hover{--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:shadow-overlay-video:hover{--tw-shadow: 0px 4px 6px -1px #0000001A;--tw-shadow-colored: 0px 4px 6px -1px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:shadow-sm:hover{--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-border-disabled:hover{outline-color: #E5E7EB;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-border-interactive:hover{outline-color: #5C2EDE;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-border-strong:hover{outline-color: #6B7280;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-border-subtle:hover{outline-color: #E6E6EF;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-button-danger:hover{outline-color: #DC2626;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-button-danger-hover:hover{outline-color: #B91C1C;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-button-primary-hover:hover{outline-color: #AD38E2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-button-secondary:hover{outline-color: #EFD7F9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-button-secondary-hover:hover{outline-color: #3B3A6A;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:outline-field-border-disabled:hover{outline-color: #F3F3F8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:before\:opacity-10:hover::before{content: var(--tw-content);opacity: 0.1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:hover\:border-toggle-on-hover:hover:checked{--tw-border-opacity: 1;border-color: rgb(92 46 222 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:hover\:bg-toggle-on-hover:hover:checked{--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:hover\:border-focus-border:hover:focus-within{--tw-border-opacity: 1;border-color: rgb(232 207 248 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-within\:hover\:outline-focus-border:hover:focus-within{outline-color: #E8CFF8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .hover\:focus-within\:outline-focus-border:focus-within:hover{outline-color: #E8CFF8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:rounded-sm:focus{border-radius: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:border-border-interactive:focus{--tw-border-opacity: 1;border-color: rgb(92 46 222 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:border-focus-border:focus{--tw-border-opacity: 1;border-color: rgb(232 207 248 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:border-focus-error-border:focus{--tw-border-opacity: 1;border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:border-toggle-off-border:focus{--tw-border-opacity: 1;border-color: rgb(232 207 248 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:bg-background-secondary:focus{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:bg-button-tertiary-hover:focus{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:text-astra:focus{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:text-link-primary-hover:focus{--tw-text-opacity: 1;color: rgb(173 56 226 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:text-slate-400:focus{--tw-text-opacity: 1;color: rgb(148 163 184 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:shadow-none:focus{--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:outline-none:focus{outline: 2px solid transparent;outline-offset: 2px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:outline:focus{outline-style: solid;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:outline-0:focus{outline-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:outline-1:focus{outline-width: 1px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:outline-border-subtle:focus{outline-color: #E6E6EF;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:outline-focus-border:focus{outline-color: #E8CFF8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:outline-focus-error-border:focus{outline-color: #FECACA;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring:focus{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-0:focus{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-1:focus{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-2:focus{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-astra:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(92 46 222 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-astra-hover:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(173 56 226 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-border-interactive:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(92 46 222 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-field-color-error:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(220 38 38 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-focus:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(92 46 222 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-focus-error:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(220 38 38 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-toggle-on:focus{--tw-ring-opacity: 1;--tw-ring-color: rgb(92 46 222 / var(--tw-ring-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-transparent:focus{--tw-ring-color: transparent;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-offset-0:focus{--tw-ring-offset-width: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:ring-offset-2:focus{--tw-ring-offset-width: 2px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus\:\[box-shadow\:none\]:focus{box-shadow: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:focus\:border-toggle-on-border:focus:checked{--tw-border-opacity: 1;border-color: rgb(99 74 224 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-visible\:border-slate-300:focus-visible{--tw-border-opacity: 1;border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-visible\:bg-astra-hover:focus-visible{--tw-bg-opacity: 1;background-color: rgb(173 56 226 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-visible\:text-\[\#1E293B\]:focus-visible{--tw-text-opacity: 1;color: rgb(30 41 59 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-visible\:text-astra-hover:focus-visible{--tw-text-opacity: 1;color: rgb(173 56 226 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-visible\:text-link-primary:focus-visible{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-visible\:text-slate-500:focus-visible{--tw-text-opacity: 1;color: rgb(100 116 139 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-visible\:text-slate-800:focus-visible{--tw-text-opacity: 1;color: rgb(30 41 59 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .focus-visible\:outline-none:focus-visible{outline: 2px solid transparent;outline-offset: 2px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .active\:text-astra:active{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .active\:text-astra-hover:active{--tw-text-opacity: 1;color: rgb(173 56 226 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .active\:text-button-primary:active{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .active\:text-link-primary:active{--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .active\:text-slate-500:active{--tw-text-opacity: 1;color: rgb(100 116 139 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .active\:outline-none:active{outline: 2px solid transparent;outline-offset: 2px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:cursor-default:disabled{cursor: default;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:cursor-not-allowed:disabled{cursor: not-allowed;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:border-border-disabled:disabled{--tw-border-opacity: 1;border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:border-field-border-disabled:disabled{--tw-border-opacity: 1;border-color: rgb(243 243 248 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:border-transparent:disabled{border-color: transparent;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:bg-button-disabled:disabled{--tw-bg-opacity: 1;background-color: rgb(243 243 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:bg-button-tertiary:disabled{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:bg-white:disabled{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:text-text-disabled:disabled{--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:shadow-none:disabled{--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:shadow-toggle-disabled:disabled{--tw-shadow: 1px 1px 2px 0px rgba(0, 0, 0, 0.1) inset;--tw-shadow-colored: inset 1px 1px 2px 0px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:outline-border-disabled:disabled{outline-color: #E5E7EB;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:outline-button-disabled:disabled{outline-color: #F3F3F8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .disabled\:outline-field-border-disabled:disabled{outline-color: #F3F3F8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:disabled\:border-border-disabled:disabled:checked{--tw-border-opacity: 1;border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:disabled\:bg-toggle-on-disabled:disabled:checked{--tw-bg-opacity: 1;background-color: rgb(231 224 250 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .checked\:disabled\:bg-white:disabled:checked{--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:focus-within .group-focus-within\/switch\:size-3\.25){width: 0.8125rem;height: 0.8125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:focus-within .group-focus-within\/switch\:size-4){width: 1rem;height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:focus-within .group-focus-within\/switch\:size-5){width: 1.25rem;height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:focus-within .group-focus-within\:text-icon-primary){--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:hover .group-hover\:visible){visibility: visible;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .group-hover\/switch\:right-0\.5){left: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .group-hover\/switch\:size-3\.25){width: 0.8125rem;height: 0.8125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .group-hover\/switch\:size-4){width: 1rem;height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .group-hover\/switch\:size-5){width: 1.25rem;height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:hover .group-hover\:scale-110){--tw-scale-x: 1.1;--tw-scale-y: 1.1;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .group-hover\/switch\:border-toggle-on-border){--tw-border-opacity: 1;border-color: rgb(99 74 224 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .group-hover\/switch\:bg-toggle-off-hover){--tw-bg-opacity: 1;background-color: rgb(232 207 248 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:hover .group-hover\:bg-opacity-100){--tw-bg-opacity: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:hover .group-hover\:text-field-input){--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:hover .group-hover\:text-icon-primary){--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:hover .group-hover\:text-purple-800){--tw-text-opacity: 1;color: rgb(107 33 168 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:hover .group-hover\:opacity-100){opacity: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:hover .group-hover\:shadow-lg){--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .checked\:group-hover\/switch\:border-toggle-on-border:checked){--tw-border-opacity: 1;border-color: rgb(99 74 224 / var(--tw-border-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .checked\:group-hover\/switch\:bg-toggle-on-hover:checked){--tw-bg-opacity: 1;background-color: rgb(92 46 222 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:disabled .group-disabled\:text-field-color-disabled){--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group:disabled .group-disabled\:text-icon-disabled){--tw-text-opacity: 1;color: rgb(209 213 219 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.peer:checked ~ .peer-checked\:translate-x-3){--tw-translate-x: 0.75rem;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.peer:checked ~ .peer-checked\:translate-x-3\.75){--tw-translate-x: 0.9375rem;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.peer:checked ~ .peer-checked\:translate-x-5){--tw-translate-x: 1.25rem;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.peer:checked ~ .peer-checked\:opacity-100){opacity: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.peer:disabled ~ .peer-disabled\:cursor-not-allowed){cursor: not-allowed;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.peer:disabled ~ .peer-disabled\:text-border-disabled){--tw-text-opacity: 1;color: rgb(229 231 235 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .not-rtl\:left-1:not([dir="rtl"], [dir="rtl"] *){right: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .not-rtl\:left-2\/4:not([dir="rtl"], [dir="rtl"] *){right: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .not-rtl\:before\:left-2\/4:not([dir="rtl"], [dir="rtl"] *)::before{content: var(--tw-content);right: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:focus-within .not-rtl\:group-focus-within\/switch\:left-0\.5:not([dir="rtl"], [dir="rtl"] *)){right: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .not-rtl\:group-hover\/switch\:left-0\.5:not([dir="rtl"], [dir="rtl"] *)){right: 0.125rem;}@media (min-width: 640px){:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:static{position: static;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:inset-auto{inset: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:mb-0{margin-bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:ml-2{margin-right: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:ml-6{margin-right: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:ml-8{margin-right: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:mr-3{margin-left: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:mt-5{margin-top: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:flex{display: flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:inline-flex{display: inline-flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:h-10{height: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:h-120{height: 30rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:h-\[56px\]{height: 56px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:w-10{width: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:max-w-5xl{max-width: 64rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:translate-y-0{--tw-translate-y: 0px;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:scale-100{--tw-scale-x: 1;--tw-scale-y: 1;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:scale-95{--tw-scale-x: .95;--tw-scale-y: .95;transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:flex-row{flex-direction: row;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:items-center{align-items: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:items-stretch{align-items: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:justify-start{justify-content: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:gap-0{gap: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:p-0{padding: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:p-1{padding: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:p-6{padding: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:px-6{padding-right: 1.5rem;padding-left: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:pl-3{padding-right: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:pr-0{padding-left: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:text-sm{font-size: 0.875rem;line-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .sm\:leading-\[0\.875rem\]{line-height: 0.875rem;}}@media (min-width: 768px){:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:relative{position: relative;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:bottom-0{bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:top-0{top: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-1{order: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-10{order: 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-11{order: 11;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-12{order: 12;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-2{order: 2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-3{order: 3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-4{order: 4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-5{order: 5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-6{order: 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-7{order: 7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-8{order: 8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-9{order: 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-first{order: -9999;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-last{order: 9999;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:order-none{order: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-1{grid-column: span 1 / span 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-10{grid-column: span 10 / span 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-11{grid-column: span 11 / span 11;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-12{grid-column: span 12 / span 12;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-2{grid-column: span 2 / span 2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-3{grid-column: span 3 / span 3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-4{grid-column: span 4 / span 4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-5{grid-column: span 5 / span 5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-6{grid-column: span 6 / span 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-7{grid-column: span 7 / span 7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-8{grid-column: span 8 / span 8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-span-9{grid-column: span 9 / span 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-1{grid-column-start: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-10{grid-column-start: 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-11{grid-column-start: 11;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-12{grid-column-start: 12;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-2{grid-column-start: 2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-3{grid-column-start: 3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-4{grid-column-start: 4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-5{grid-column-start: 5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-6{grid-column-start: 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-7{grid-column-start: 7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-8{grid-column-start: 8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:col-start-9{grid-column-start: 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:my-10{margin-top: 2.5rem;margin-bottom: 2.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:flex{display: flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:hidden{display: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:h-\[19rem\]{height: 19rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:h-full{height: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/10{width: 10%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/11{width: 9.0909091%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/12{width: 8.3333333%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/2{width: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/3{width: 33.333333%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/4{width: 25%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/5{width: 20%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/6{width: 16.666667%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/7{width: 14.2857143%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/8{width: 12.5%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-1\/9{width: 11.1111111%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-\[34rem\]{width: 34rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-\[calc\(100\%-24px\)\]{width: calc(100% - 24px);}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-auto{width: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-full{width: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:w-max{width: -moz-max-content;width: max-content;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:max-w-2xl{max-width: 42rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:shrink{flex-shrink: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:shrink-0{flex-shrink: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grow{flex-grow: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grow-0{flex-grow: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-flow-row{grid-auto-flow: row;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-flow-col{grid-auto-flow: column;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-flow-row-dense{grid-auto-flow: row dense;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-flow-col-dense{grid-auto-flow: column dense;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-1{grid-template-columns: repeat(1, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-10{grid-template-columns: repeat(10, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-11{grid-template-columns: repeat(11, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-12{grid-template-columns: repeat(12, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-2{grid-template-columns: repeat(2, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-3{grid-template-columns: repeat(3, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-4{grid-template-columns: repeat(4, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-5{grid-template-columns: repeat(5, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-6{grid-template-columns: repeat(6, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-7{grid-template-columns: repeat(7, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-8{grid-template-columns: repeat(8, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:grid-cols-9{grid-template-columns: repeat(9, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:flex-row{flex-direction: row;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:flex-row-reverse{flex-direction: row-reverse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:flex-col{flex-direction: column;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:flex-col-reverse{flex-direction: column-reverse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:flex-wrap{flex-wrap: wrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:flex-wrap-reverse{flex-wrap: wrap-reverse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:flex-nowrap{flex-wrap: nowrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:items-start{align-items: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:items-end{align-items: flex-end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:items-center{align-items: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:items-baseline{align-items: baseline;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:items-stretch{align-items: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-normal{justify-content: normal;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-start{justify-content: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-end{justify-content: flex-end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-center{justify-content: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-between{justify-content: space-between;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-around{justify-content: space-around;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-evenly{justify-content: space-evenly;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-stretch{justify-content: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-2{gap: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-4{gap: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-5{gap: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-6{gap: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-8{gap: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-x-2{-moz-column-gap: 0.5rem;column-gap: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-x-4{-moz-column-gap: 1rem;column-gap: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-x-5{-moz-column-gap: 1.25rem;column-gap: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-x-6{-moz-column-gap: 1.5rem;column-gap: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-x-8{-moz-column-gap: 2rem;column-gap: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-y-2{row-gap: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-y-4{row-gap: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-y-5{row-gap: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-y-6{row-gap: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:gap-y-8{row-gap: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.md\:space-x-1 > :not([hidden]) ~ :not([hidden])){--tw-space-x-reverse: 0;margin-left: calc(0.25rem * var(--tw-space-x-reverse));margin-right: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:self-start{align-self: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:self-end{align-self: flex-end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:self-center{align-self: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:self-stretch{align-self: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:self-baseline{align-self: baseline;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-self-auto{justify-self: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-self-start{justify-self: start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-self-end{justify-self: end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-self-center{justify-self: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:justify-self-stretch{justify-self: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:border-none{border-style: none;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:p-4{padding: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:p-8{padding: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:px-1{padding-right: 0.25rem;padding-left: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:px-2{padding-right: 0.5rem;padding-left: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:py-0{padding-top: 0px;padding-bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .md\:shadow-none{--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}}@media (min-width: 1024px){:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:sticky{position: sticky;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:inset-y-0{top: 0px;bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:top-8{top: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-1{order: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-10{order: 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-11{order: 11;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-12{order: 12;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-2{order: 2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-3{order: 3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-4{order: 4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-5{order: 5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-6{order: 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-7{order: 7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-8{order: 8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-9{order: 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-first{order: -9999;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-last{order: 9999;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:order-none{order: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-1{grid-column: span 1 / span 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-10{grid-column: span 10 / span 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-11{grid-column: span 11 / span 11;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-12{grid-column: span 12 / span 12;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-2{grid-column: span 2 / span 2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-3{grid-column: span 3 / span 3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-4{grid-column: span 4 / span 4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-5{grid-column: span 5 / span 5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-6{grid-column: span 6 / span 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-7{grid-column: span 7 / span 7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-8{grid-column: span 8 / span 8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-span-9{grid-column: span 9 / span 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-1{grid-column-start: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-10{grid-column-start: 10;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-11{grid-column-start: 11;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-12{grid-column-start: 12;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-2{grid-column-start: 2;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-3{grid-column-start: 3;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-4{grid-column-start: 4;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-5{grid-column-start: 5;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-6{grid-column-start: 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-7{grid-column-start: 7;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-8{grid-column-start: 8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:col-start-9{grid-column-start: 9;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:ml-40{margin-right: 10rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:ml-6{margin-right: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:block{display: block;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex{display: flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid{display: grid;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:h-16{height: 4rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:h-188{height: 47rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:h-\[34rem\]{height: 34rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/10{width: 10%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/11{width: 9.0909091%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/12{width: 8.3333333%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/2{width: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/3{width: 33.333333%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/4{width: 25%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/5{width: 20%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/6{width: 16.666667%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/7{width: 14.2857143%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/8{width: 12.5%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-1\/9{width: 11.1111111%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-2\/3{width: 66.666667%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-64{width: 16rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-\[47\.5rem\]{width: 47.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-\[60rem\]{width: 60rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:w-full{width: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:max-w-3xl{max-width: 48rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:max-w-\[77rem\]{max-width: 77rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:max-w-full{max-width: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex-1{flex: 1 1 0%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex-shrink-0{flex-shrink: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:shrink{flex-shrink: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:shrink-0{flex-shrink: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grow{flex-grow: 1;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grow-0{flex-grow: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-flow-row{grid-auto-flow: row;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-flow-col{grid-auto-flow: column;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-flow-row-dense{grid-auto-flow: row dense;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-flow-col-dense{grid-auto-flow: column dense;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-1{grid-template-columns: repeat(1, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-10{grid-template-columns: repeat(10, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-11{grid-template-columns: repeat(11, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-12{grid-template-columns: repeat(12, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-2{grid-template-columns: repeat(2, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-3{grid-template-columns: repeat(3, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-4{grid-template-columns: repeat(4, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-5{grid-template-columns: repeat(5, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-6{grid-template-columns: repeat(6, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-7{grid-template-columns: repeat(7, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-8{grid-template-columns: repeat(8, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-9{grid-template-columns: repeat(9, minmax(0, 1fr));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:grid-cols-\[16rem_1fr\]{grid-template-columns: 16rem 1fr;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex-row{flex-direction: row;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex-row-reverse{flex-direction: row-reverse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex-col{flex-direction: column;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex-col-reverse{flex-direction: column-reverse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex-wrap{flex-wrap: wrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex-wrap-reverse{flex-wrap: wrap-reverse;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:flex-nowrap{flex-wrap: nowrap;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:items-start{align-items: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:items-end{align-items: flex-end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:items-center{align-items: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:items-baseline{align-items: baseline;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:items-stretch{align-items: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-normal{justify-content: normal;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-start{justify-content: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-end{justify-content: flex-end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-center{justify-content: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-between{justify-content: space-between;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-around{justify-content: space-around;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-evenly{justify-content: space-evenly;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-stretch{justify-content: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-2{gap: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-4{gap: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-5{gap: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-6{gap: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-8{gap: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-x-2{-moz-column-gap: 0.5rem;column-gap: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-x-4{-moz-column-gap: 1rem;column-gap: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-x-5{-moz-column-gap: 1.25rem;column-gap: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-x-6{-moz-column-gap: 1.5rem;column-gap: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-x-8{-moz-column-gap: 2rem;column-gap: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-y-2{row-gap: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-y-4{row-gap: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-y-5{row-gap: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-y-6{row-gap: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:gap-y-8{row-gap: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:self-start{align-self: flex-start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:self-end{align-self: flex-end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:self-center{align-self: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:self-stretch{align-self: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:self-baseline{align-self: baseline;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-self-auto{justify-self: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-self-start{justify-self: start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-self-end{justify-self: end;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-self-center{justify-self: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:justify-self-stretch{justify-self: stretch;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:p-0{padding: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:p-12{padding: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:p-6{padding: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:p-8{padding: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:px-0{padding-right: 0px;padding-left: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:px-5{padding-right: 1.25rem;padding-left: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:py-0{padding-top: 0px;padding-bottom: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .lg\:py-6{padding-top: 1.5rem;padding-bottom: 1.5rem;}}@media (min-width: 1280px){:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .xl\:col-span-6{grid-column: span 6 / span 6;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .xl\:ml-4{margin-right: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .xl\:mr-0{margin-left: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .xl\:max-w-\[280px\]{max-width: 280px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .xl\:max-w-\[696px\]{max-width: 696px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .xl\:grid-cols-3{grid-template-columns: repeat(3, minmax(0, 1fr));}}@media (max-width: 782px){:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .tablet\:my-2{margin-top: 0.5rem;margin-bottom: 0.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .tablet\:justify-items-start{justify-items: start;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .tablet\:pr-2{padding-left: 0.5rem;}}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rtl\:right-1:where([dir="rtl"], [dir="rtl"] *){left: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rtl\:right-2\/4:where([dir="rtl"], [dir="rtl"] *){left: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .rtl\:before\:right-2\/4:where([dir="rtl"], [dir="rtl"] *)::before{content: var(--tw-content);left: 50%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:focus-within .rtl\:group-focus-within\/switch\:right-0\.5:where([dir="rtl"], [dir="rtl"] *)){left: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .rtl\:group-hover\/switch\:right-0:where([dir="rtl"], [dir="rtl"] *)){left: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.group\/switch:hover .rtl\:group-hover\/switch\:right-0\.5:where([dir="rtl"], [dir="rtl"] *)){left: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\:focus\+div\]\:flex:focus+div){display: flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\:hover\+div\]\:flex:hover+div){display: flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .\[\&\:hover\:has\(\:disabled\)\]\:outline-field-border-disabled:hover:has(:disabled){outline-color: #F3F3F8;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .\[\&\:hover\:not\(\:focus\)\:not\(\:disabled\)\]\:outline-border-strong:hover:not(:focus):not(:disabled){outline-color: #6B7280;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .\[\&\:is\(\[data-hover\=true\]\)\]\:rounded-none:is([data-hover=true]){border-radius: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) .\[\&\:is\(\[data-hover\=true\]\)\]\:bg-brand-background-50:is([data-hover=true]){--tw-bg-opacity: 1;background-color: rgb(231 224 250 / var(--tw-bg-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\:not\(svg\)\]\:m-1>*:not(svg)){margin: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\:not\(svg\)\]\:mx-1>*:not(svg)){margin-right: 0.25rem;margin-left: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\:not\(svg\)\]\:my-0\.5>*:not(svg)){margin-top: 0.125rem;margin-bottom: 0.125rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:box-border>*){box-sizing: border-box;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-2xl>*){font-size: 1.5rem;line-height: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-base>*){font-size: 1rem;line-height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-lg>*){font-size: 1.125rem;line-height: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-sm>*){font-size: 0.875rem;line-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-xl>*){font-size: 1.25rem;line-height: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-xs>*){font-size: 0.75rem;line-height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-field-color-disabled>*){--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-field-helper>*){--tw-text-opacity: 1;color: rgb(159 159 191 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-field-label>*){--tw-text-opacity: 1;color: rgb(79 78 124 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>\*\]\:text-support-error>*){--tw-text-opacity: 1;color: rgb(220 38 38 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>div\>\.ast-licensing-wrap\]\:max-w-full>div>.ast-licensing-wrap){max-width: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>div\]\:p-6>div){padding: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>label\]\:\!left-1>label){right: 0.25rem !important;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>label\]\:\!size-2\.5>label){width: 0.625rem !important;height: 0.625rem !important;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>li\]\:pointer-events-auto>li){pointer-events: auto;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>p\]\:m-0>p){margin: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>p\]\:w-full>p){width: 100%;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>span\:first-child\]\:shrink-0>span:first-child){flex-shrink: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>span\]\:flex>span){display: flex;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>span\]\:items-center>span){align-items: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:m-1>svg){margin: 0.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:m-1\.5>svg){margin: 0.375rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:mr-0>svg){margin-left: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:block>svg){display: block;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:size-12>svg){width: 3rem;height: 3rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:size-3>svg){width: 0.75rem;height: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:size-3\.5>svg){width: 0.875rem;height: 0.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:size-4>svg){width: 1rem;height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:size-5>svg){width: 1.25rem;height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:size-6>svg){width: 1.5rem;height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:size-8>svg){width: 2rem;height: 2rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:h-3>svg){height: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:h-4>svg){height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:h-5>svg){height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:w-3>svg){width: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:w-4>svg){width: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:w-5>svg){width: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:shrink-0>svg){flex-shrink: 0;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&\>svg\]\:text-icon-interactive>svg){--tw-text-opacity: 1;color: rgb(92 46 222 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\*\]\:box-border *){box-sizing: border-box;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\*\]\:text-sm *){font-size: 0.875rem;line-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\*\]\:leading-5 *){line-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.editor-content\>p\]\:min-h-5 .editor-content>p){min-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.editor-content\>p\]\:min-h-6 .editor-content>p){min-height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.editor-content\>p\]\:min-h-7 .editor-content>p){min-height: 1.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.editor-content\>p\]\:content-center .editor-content>p){align-content: center;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.editor-content\>p\]\:text-base .editor-content>p){font-size: 1rem;line-height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.editor-content\>p\]\:text-sm .editor-content>p){font-size: 0.875rem;line-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.editor-content\>p\]\:text-xs .editor-content>p){font-size: 0.75rem;line-height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.editor-content\>p\]\:font-normal .editor-content>p){font-weight: 400;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.pointer-events-none\]\:text-base .pointer-events-none){font-size: 1rem;line-height: 1.5rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.pointer-events-none\]\:text-sm .pointer-events-none){font-size: 0.875rem;line-height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.pointer-events-none\]\:text-xs .pointer-events-none){font-size: 0.75rem;line-height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_\.pointer-events-none\]\:font-normal .pointer-events-none){font-weight: 400;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_h2\]\:leading-\[1\.875rem\] h2){line-height: 1.875rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_h2\]\:text-text-primary h2){--tw-text-opacity: 1;color: rgb(20 19 56 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_p\]\:m-0 p){margin: 0px;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_p\]\:text-badge-color-disabled p){--tw-text-opacity: 1;color: rgb(189 193 199 / var(--tw-text-opacity, 1));}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_svg\]\:size-3 svg){width: 0.75rem;height: 0.75rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_svg\]\:size-4 svg){width: 1rem;height: 1rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_svg\]\:size-5 svg){width: 1.25rem;height: 1.25rem;}:is( #astra-dashboard-app,[data-floating-ui-portal],.ast-site-builder-table-view ) :is(.\[\&_svg\]\:size-6 svg){width: 1.5rem;height: 1.5rem;}
