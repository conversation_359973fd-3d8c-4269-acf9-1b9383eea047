(()=>{var o=document.getElementById("astra-mobile-cart-drawer"),e=document.getElementById("masthead"),a=astra_cart.responsive_cart_click,n=null;if(e){var c="",i=e.querySelector("#ast-mobile-header"),s="";void 0!==o&&""!==o&&null!==o&&(c=o.querySelector(".widget_shopping_cart.woocommerce"),s=o.querySelector(".widget_edd_cart_widget"));let r={moveToCloseButton:function(){var e=document.querySelector(".astra-cart-drawer-close");e&&e.focus()},returnToTrigger:function(){var e;n?n.focus():(e=(e=(e=document.querySelector(".ast-header-woo-cart .ast-site-header-cart .ast-site-header-cart-li a.cart-container"))||document.querySelector(".ast-site-header-cart-li a"))||document.querySelector(".ast-header-woo-cart a"))&&e.focus()},setupFocusTrap:function(){o&&(document.removeEventListener("keydown",this.trapTabKey),document.addEventListener("keydown",this.trapTabKey))},trapTabKey:function(e){var t,a;o.classList.contains("active")&&"Tab"===e.key&&(a=o.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),0!==(a=Array.prototype.filter.call(a,function(e){return 0<e.offsetWidth&&0<e.offsetHeight&&"hidden"!==window.getComputedStyle(e).visibility})).length)&&(t=a[0],a=a[a.length-1],e.shiftKey&&document.activeElement===t?(e.preventDefault(),a.focus()):e.shiftKey||document.activeElement!==a||(e.preventDefault(),t.focus()))}};cartFlyoutOpen=function(e){n=e.currentTarget.querySelector("a.cart-container")||e.currentTarget,"redirect"===a&&document.body.classList.contains("ast-header-break-point")||(e.preventDefault(),"woocommerce"===(e=e.currentTarget.cart_type)&&document.body.classList.contains("woocommerce-cart"))||(o.classList.remove("active"),o.classList.remove("woocommerce-active"),o.classList.remove("edd-active"),void 0!==o&&""!==o&&null!==o&&(o.classList.add("active"),document.documentElement.classList.add("ast-mobile-cart-active"),void 0!==s&&""!==s&&null!==s&&(s.style.display="block","woocommerce"===e)&&(s.style.display="none",o.classList.add("woocommerce-active")),void 0!==c)&&""!==c&&null!==c&&(c.style.display="block","edd"===e)&&(c.style.display="none",o.classList.add("edd-active")),document.dispatchEvent(new CustomEvent("astra_on_slide_In_cart_open",{detail:{}})),setTimeout(function(){r.moveToCloseButton(),r.setupFocusTrap()},100))},cartFlyoutClose=function(e){e.preventDefault(),void 0!==o&&""!==o&&null!==o&&(o.classList.remove("active"),document.documentElement.classList.remove("ast-mobile-cart-active")),setTimeout(function(){r.returnToTrigger()},100)},document.querySelector(".ast-slidein-cart")&&document.querySelector(".ast-slidein-cart").addEventListener("click",e=>{n=e.currentTarget,document.querySelector("#astra-mobile-cart-drawer").classList.add("active"),document.querySelector("html").classList.add("ast-mobile-cart-active"),e.preventDefault(),setTimeout(function(){r.moveToCloseButton(),r.setupFocusTrap()},100)});var d,l=window.innerWidth;window.addEventListener("resize",function(){var e,t=document.querySelector(".astra-cart-drawer-close");void 0!==t&&""!==t&&null!==t&&"INPUT"!==document.activeElement.tagName&&o.classList.contains("active")&&(e=window.innerWidth)!==l&&(l=e,t.click())}),window.addEventListener("load",function(){u()}),document.addEventListener("astLayoutWidthChanged",function(){u()}),document.addEventListener("astPartialContentRendered",function(){u()});let t=window.innerWidth;function u(){document.addEventListener("keyup",function(e){27===e.keyCode&&(e.preventDefault(),o.classList.remove("active"),document.documentElement.classList.remove("ast-mobile-cart-active"),updateTrigger(),setTimeout(function(){r.returnToTrigger()},100))}),document.addEventListener("click",function(e){e.target===document.querySelector(".ast-mobile-cart-active .astra-mobile-cart-overlay")&&(o.classList.remove("active"),document.documentElement.classList.remove("ast-mobile-cart-active"),setTimeout(function(){r.returnToTrigger()},100))}),void 0!==i&&""!==i&&null!==i&&(a="flyout"==astra_cart.desktop_layout?document.querySelectorAll(".ast-mobile-header-wrap .ast-header-woo-cart, #ast-desktop-header .ast-desktop-cart-flyout"):document.querySelectorAll(".ast-mobile-header-wrap .ast-header-woo-cart"),e=document.querySelector(".ast-mobile-header-wrap .ast-header-edd-cart"),t=document.querySelector(".astra-cart-drawer-close"),0<a.length&&a.forEach(function(e){void 0!==e&&""!==e&&null!==e&&o&&(e.addEventListener("click",cartFlyoutOpen,!1),e.cart_type="woocommerce")}),void 0!==e&&""!==e&&null!==e&&o&&(e.addEventListener("click",cartFlyoutOpen,!1),e.cart_type="edd"),void 0!==t)&&""!==t&&null!==t&&t.addEventListener("click",cartFlyoutClose,!1);var e,t,a=document.querySelectorAll(".ast-site-header-cart .ast-site-header-cart-li a");0<a.length&&a.forEach(function(t){t.addEventListener("keydown",function(e){"Enter"!==e.key&&13!==e.keyCode||(e.preventDefault(),(n=t).click())})})}window.addEventListener("resize",function(){let e=window.innerWidth;clearTimeout(d),d=setTimeout(function(){u(),t!==e&&document.dispatchEvent(new CustomEvent("astLayoutWidthChanged",{detail:{response:""}})),t=e},50)}),jQuery(document).ready(function(r){r(document.body).on("added_to_cart astra_refresh_cart_fragments",function(e,t,a){r.get(wc_add_to_cart_params.wc_ajax_url.toString().replace("%%endpoint%%","get_refreshed_fragments"),function(e){e&&e.fragments&&r.each(e.fragments,function(e,t){r(e).replaceWith(t)})})}),r(document.body).trigger("astra_refresh_cart_fragments")})}})();