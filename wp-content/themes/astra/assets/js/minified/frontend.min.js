var astraGetParents=function(e,t){Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),a=t.length;0<=--a&&t.item(a)!==this;);return-1<a});for(var a=[];e&&e!==document;e=e.parentNode)(!t||e.matches(t))&&a.push(e);return a},getParents=function(e,t){console.warn("getParents() function has been deprecated since version 2.5.0 or above of Astra Theme and will be removed in the future. Use astraGetParents() instead."),astraGetParents(e,t)},astraToggleClass=function(e,t){e.classList.contains(t)?e.classList.remove(t):e.classList.add(t)},toggleClass=function(e,t){console.warn("toggleClass() function has been deprecated since version 2.5.0 or above of Astra Theme and will be removed in the future. Use astraToggleClass() instead."),astraToggleClass(e,t)},astraTriggerEvent=((()=>{function e(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var a=document.createEvent("CustomEvent");return a.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),a}"function"!=typeof window.CustomEvent&&(e.prototype=window.Event.prototype,window.CustomEvent=e)})(),function(e,t){t=new CustomEvent(t,2<arguments.length&&void 0!==arguments[2]?arguments[2]:{});e.dispatchEvent(t)});astraSmoothScroll=function(e,t){e.preventDefault(),window.scrollTo({top:t,left:0,behavior:"smooth"})},astScrollToTopHandler=function(e,t){var a=getComputedStyle(t).content,n=t.dataset.onDevices,a=a.replace(/[^0-9]/g,"");"both"==n||"desktop"==n&&"769"==a||"mobile"==n&&""==a?(n=window.pageYOffset||document.body.scrollTop,e&&e.length?n>e.offsetHeight+100?t.style.display="block":t.style.display="none":300<window.pageYOffset?t.style.display="block":t.style.display="none"):t.style.display="none"},(()=>{var r=document.querySelectorAll("#masthead .main-header-menu-toggle"),d=document.getElementById("masthead"),i={},c="",u=document.body,m="";function e(e){c=e.detail.type;e=document.querySelectorAll(".menu-toggle");"dropdown"===c&&(document.getElementById("ast-mobile-popup").classList.remove("active","show"),g("updateHeader")),"off-canvas"===c&&e.forEach(function(e){e&&e.classList.contains("toggled")&&e.click()}),t(c)}function g(e){m=d.querySelector("#ast-mobile-header");if(null==m||"dropdown"!==m.dataset.type||"updateHeader"===e){(void 0!==e&&"updateHeader"!==e?e.closest(".ast-mobile-popup-inner"):document.querySelector("#ast-mobile-popup")).querySelectorAll(".menu-item-has-children").forEach(e=>{e.classList.remove("ast-submenu-expanded"),Array.from(e.querySelectorAll(".sub-menu")).forEach(e=>{e.hasAttribute("data-initial-display")||e.setAttribute("data-initial-display",window.getComputedStyle(e).display),"block"===e.getAttribute("data-initial-display")?e.style.display="block":e.style.display="none"})});var t=document.querySelectorAll(".menu-toggle");document.body.classList.remove("ast-main-header-nav-open","ast-popup-nav-open"),document.documentElement.classList.remove("ast-off-canvas-active");for(var a=0;a<t.length;a++)t[a].classList.remove("toggled"),t[a].style.display="flex"}}function t(e){var a=document.querySelectorAll("#ast-mobile-header .menu-toggle"),n=document.querySelectorAll("#ast-desktop-header .menu-toggle");if(void 0===e&&null!==d)if(m=d.querySelector("#ast-mobile-header"))e=m.dataset.type;else{var o=d.querySelector("#ast-desktop-header");if(!o)return;e=o.dataset.toggleType}if("off-canvas"===e){var o=document.getElementById("menu-toggle-close"),s=document.querySelector(".ast-mobile-popup-inner");if(null==s)return;popupLinks=s.getElementsByTagName("a");for(var r=0;r<a.length;r++)a[r].removeEventListener("click",astraNavMenuToggle,!1),a[r].removeEventListener("click",popupTriggerClick),a[r].addEventListener("click",popupTriggerClick,!1),a[r].trigger_type="mobile";for(r=0;r<n.length;r++)n[r].removeEventListener("click",astraNavMenuToggle,!1),n[r].removeEventListener("click",popupTriggerClick),n[r].addEventListener("click",popupTriggerClick,!1),n[r].trigger_type="desktop";let t=document.querySelector(".ast-button-wrap .menu-toggle");o.addEventListener("click",function(e){document.getElementById("ast-mobile-popup").classList.remove("active","show"),g(this),t?.focus()}),document.addEventListener("keyup",function(e){27===e.keyCode&&(e.preventDefault(),document.getElementById("ast-mobile-popup").classList.remove("active","show"),g(),t?.focus())}),document.addEventListener("click",function(e){e.target===document.querySelector(".ast-mobile-popup-drawer.active .ast-mobile-popup-overlay")&&(document.getElementById("ast-mobile-popup").classList.remove("active","show"),g(),t?.focus())});for(let e=0,t=popupLinks.length;e<t;e++)null!==popupLinks[e].getAttribute("href")&&(popupLinks[e].getAttribute("href").startsWith("#")||-1!==popupLinks[e].getAttribute("href").search("#"))&&(!popupLinks[e].parentElement.classList.contains("menu-item-has-children")||popupLinks[e].parentElement.classList.contains("menu-item-has-children")&&document.querySelector("header.site-header").classList.contains("ast-builder-menu-toggle-icon"))&&(popupLinks[e].addEventListener("click",p,!0),popupLinks[e].headerType="off-canvas");AstraToggleSetup()}else if("dropdown"===e){var t=document.querySelectorAll(".ast-mobile-header-content")||!1,s=document.querySelector(".ast-desktop-header-content")||!1;if(0<t.length)for(let e=0;e<t.length;e++){var l=t[e].getElementsByTagName("a");for(link=0,len=l.length;link<len;link++){var i=null===l[link].closest(".wp-block-uagb-tabs");null!==l[link].getAttribute("href")&&(l[link].getAttribute("href").startsWith("#")||-1!==l[link].getAttribute("href").search("#"))&&(!l[link].parentElement.classList.contains("menu-item-has-children")||l[link].parentElement.classList.contains("menu-item-has-children")&&document.querySelector("header.site-header").classList.contains("ast-builder-menu-toggle-icon"))&&i&&(l[link].addEventListener("click",p,!0),l[link].headerType="dropdown")}}if(s){var c=s.getElementsByTagName("a");for(link=0,len=c.length;link<len;link++)c[link].addEventListener("click",p,!0),c[link].headerType="dropdown"}for(r=0;r<a.length;r++)a[r].removeEventListener("click",popupTriggerClick,!1),a[r].removeEventListener("click",astraNavMenuToggle),a[r].addEventListener("click",astraNavMenuToggle,!1),a[r].trigger_type="mobile";for(r=0;r<n.length;r++)n[r].removeEventListener("click",popupTriggerClick,!1),n[r].removeEventListener("click",astraNavMenuToggle),n[r].addEventListener("click",astraNavMenuToggle,!1),n[r].trigger_type="desktop";AstraToggleSetup()}v()}function p(e){switch(e.currentTarget.headerType){case"dropdown":for(var t=document.querySelectorAll(".menu-toggle.toggled"),a=0;a<t.length;a++)t[a].click();break;case"off-canvas":document.getElementById("menu-toggle-close").click()}}""!==(m=null!=d?d.querySelector("#ast-mobile-header"):m)&&null!==m&&(c=m.dataset.type),document.addEventListener("astMobileHeaderTypeChange",e,!1),document.addEventListener("click",function(e){e=e.target.closest(".menu-toggle");if(e){e.classList.toggle("toggled");{e=document.querySelectorAll(".menu-toggle");let t=Array.from(e).every(e=>e.classList.contains("toggled"));e.forEach(e=>{t?e.classList.remove("toggled"):e.classList.add("toggled")})}}}),popupTriggerClick=function(e){var e=e.currentTarget.trigger_type,t=document.getElementById("ast-mobile-popup"),a=document.getElementById("menu-toggle-close");a&&a.focus(),u.classList.contains("ast-popup-nav-open")||u.classList.add("ast-popup-nav-open"),u.classList.contains("ast-main-header-nav-open")||"mobile"===e||u.classList.add("ast-main-header-nav-open"),document.documentElement.classList.contains("ast-off-canvas-active")||document.documentElement.classList.add("ast-off-canvas-active"),"desktop"===e&&(t.querySelector(".ast-mobile-popup-content").style.display="none",t.querySelector(".ast-desktop-popup-content").style.display="block"),"mobile"===e&&(t.querySelector(".ast-desktop-popup-content").style.display="none",t.querySelector(".ast-mobile-popup-content").style.display="block"),this.style.display="none",t.classList.add("active","show")},window.addEventListener("load",function(){t()}),document.addEventListener("astLayoutWidthChanged",function(){t()}),document.addEventListener("astPartialContentRendered",function(){r=document.querySelectorAll(".main-header-menu-toggle"),u.classList.remove("ast-main-header-nav-open"),document.addEventListener("astMobileHeaderTypeChange",e,!1),t(),v()});var o=null!==navigator.userAgent.match(/Android/i)&&"Android"===navigator.userAgent.match(/Android/i)[0]?window.visualViewport.width:window.innerWidth;window.addEventListener("resize",function(){var e,t,a,n;"INPUT"!==document.activeElement.tagName&&(e=document.getElementById("menu-toggle-close"),t=document.querySelector(".menu-toggle.toggled"),a=document.querySelector("#masthead > #ast-desktop-header .ast-desktop-header-content"),n=document.querySelector(".elementor-editor-active"),a&&(a.style.display="none"),(null!==navigator.userAgent.match(/Android/i)&&"Android"===navigator.userAgent.match(/Android/i)[0]?window.visualViewport.width:window.innerWidth)!==o&&(t&&null===n&&t.click(),document.body.classList.remove("ast-main-header-nav-open","ast-popup-nav-open"),e)&&null==n&&e.click(),h(),AstraToggleSetup())}),document.addEventListener("DOMContentLoaded",function(){if(AstraToggleSetup(),null!==(e=u.classList.contains("ast-header-break-point")?document.getElementById("ast-mobile-header"):document.getElementById("ast-desktop-header"))){var e,t=e.querySelector(".navigation-accessibility");if(t&&e){var a=e.getElementsByTagName("button")[0]||e.getElementsByTagName("a")[0];if(a&&!a.classList.contains("astra-search-icon")){var n=t.getElementsByTagName("ul")[0];if(n){if(n.className.includes("nav-menu")||(n.className+=" nav-menu"),document.addEventListener("DOMContentLoaded",function(){var e;"off-canvas"===c&&(e=document.getElementById("menu-toggle-close"))&&(e.onclick=function(){var e=t.className.includes("toggled");t.className=e?t.className.replace(" toggled",""):t.className+" toggled",a.setAttribute("aria-expanded",e?"false":"true"),n.setAttribute("aria-expanded",e?"false":"true")})}),a.onclick=function(){var e=t.className.includes("toggled");t.className=e?t.className.replace(" toggled",""):t.className+" toggled",a.setAttribute("aria-expanded",e?"false":"true"),n.setAttribute("aria-expanded",e?"false":"true")},!astra.is_header_footer_builder_active){for(var o=n.getElementsByTagName("a"),s=n.getElementsByTagName("ul"),r=0,l=s.length;r<l;r++)s[r].parentNode.setAttribute("aria-haspopup","true");for(r=0,l=o.length;r<l;r++)o[r].addEventListener("focus",E,!0),o[r].addEventListener("blur",E,!0),o[r].addEventListener("click",L,!0)}astra.is_header_footer_builder_active&&(()=>{let t=document.querySelectorAll("nav.site-navigation .menu-item-has-children > a .ast-header-navigation-arrow"),a=document.querySelectorAll("nav.site-navigation .sub-menu"),n=document.querySelectorAll("nav.site-navigation .menu-item-has-children"),o=document.querySelectorAll(".astra-full-megamenu-wrapper");t&&(t.forEach(e=>{e.addEventListener("keydown",function(o){if("Enter"===o.key){/Mobi|Android|iPad|iPhone/i.test(navigator.userAgent)&&(o.preventDefault(),o.stopPropagation());let t=o.target.closest("li"),a=t.querySelector(".sub-menu"),n=a&&a.classList.contains("astra-megamenu");setTimeout(()=>{n?(e=t.querySelector(".astra-full-megamenu-wrapper"),a&&a.classList.toggle("astra-megamenu-focus"),e&&e.classList.toggle("astra-megamenu-wrapper-focus")):a.classList.toggle("toggled-on"),t.classList.toggle("ast-menu-hover");var e=o.target.getAttribute("aria-expanded");o.target.setAttribute("aria-expanded","false"!==e&&e?"false":"true")},10)}})}),a||n)&&(document.addEventListener("click",function(e){b(a,t,n,o)},!1),document.addEventListener("keydown",function(e){"Escape"===e.key&&b(a,t,n,o)},!1));var e=document.querySelectorAll("nav.site-navigation .ast-nav-menu > .menu-item-has-children > a .ast-header-navigation-arrow");e&&e.forEach(e=>{e.addEventListener("keydown",function(e){e.target.closest("li").classList.contains("ast-menu-hover")||"Enter"!==e.key||b(a,t,n,o)},!1)})})()}else a.style.display="none"}}}});for(var a,n,s,l,h=function(){var e=u.style.overflow,t=(u.style.overflow="hidden",document.documentElement.clientWidth);if(u.style.overflow=e,astra.break_point<t||0===t){if(0<r.length)for(var a=0;a<r.length;a++)null!==r[a]&&r[a].classList.remove("toggled");u.classList.remove("ast-header-break-point"),u.classList.add("ast-desktop"),astraTriggerEvent(u,"astra-header-responsive-enabled")}else u.classList.add("ast-header-break-point"),u.classList.remove("ast-desktop"),astraTriggerEvent(u,"astra-header-responsive-disabled")},v=function(){var e,a=document.querySelectorAll(".ast-account-action-login");if(a.length){let t=document.querySelector("#ast-hb-account-login-wrap");t&&(e=document.querySelector("#ast-hb-login-close"),a.forEach(function(e){e.addEventListener("click",function(e){e.preventDefault(),t.classList.add("show")})}),e)&&e.addEventListener("click",function(e){e.preventDefault(),t.classList.remove("show")})}},f=(h(),AstraToggleSubMenu=function(e){e.preventDefault(),"false"!==e.target.getAttribute("aria-expanded")&&e.target.getAttribute("aria-expanded")?e.target.setAttribute("aria-expanded","false"):e.target.setAttribute("aria-expanded","true");for(var t=this.parentNode,a=(t.classList.contains("ast-submenu-expanded")&&document.querySelector("header.site-header").classList.contains("ast-builder-menu-toggle-link")&&(this.classList.contains("ast-menu-toggle")||""!==(e=t.querySelector("a").getAttribute("href"))&&"#"!==e&&(window.location=e)),t.querySelectorAll(".menu-item-has-children")),n=0;n<a.length;n++){a[n].classList.remove("ast-submenu-expanded");var o=a[n].querySelector(".sub-menu, .children");null!==o&&(o.style.display="none")}for(var s=t.parentNode.querySelectorAll(".menu-item-has-children"),n=0;n<s.length;n++)if(s[n]!=t){s[n].classList.remove("ast-submenu-expanded");for(var r=s[n].querySelectorAll(".sub-menu"),l=0;l<r.length;l++)r[l].style.display="none"}t.classList.contains("menu-item-has-children")&&(astraToggleClass(t,"ast-submenu-expanded"),t.classList.contains("ast-submenu-expanded")?t.querySelector(".sub-menu").style.display="block":t.querySelector(".sub-menu").style.display="none")},AstraToggleSetup=function(){if("undefined"!=typeof astraAddon&&"function"==typeof astraToggleSetupPro)astraToggleSetupPro(c,u,i);else{var e,t,a,n=!1;if(0<(e="off-canvas"===c||"full-width"===c?(t=document.querySelectorAll("#ast-mobile-popup, #ast-mobile-header"),(a=document.querySelectorAll("#ast-mobile-header .main-header-menu-toggle")).length):(t=document.querySelectorAll("#ast-mobile-header"),(n=!(0<(e=(a=document.querySelectorAll("#ast-mobile-header .main-header-menu-toggle")).length)))?1:e))||n)for(var o=0;o<e;o++)if(n||(a[o].setAttribute("data-index",o),i[o])||(i[o]=a[o],a[o].removeEventListener("click",astraNavMenuToggle),a[o].addEventListener("click",astraNavMenuToggle,!1)),void 0!==t[o])for(var s,r=0;r<t.length;r++)if(0<(s=document.querySelector("header.site-header").classList.contains("ast-builder-menu-toggle-link")?t[r].querySelectorAll("ul.main-header-menu .menu-item-has-children > .menu-link, ul.main-header-menu .ast-menu-toggle"):t[r].querySelectorAll("ul.main-header-menu .ast-menu-toggle")).length)for(var l=0;l<s.length;l++)s[l].removeEventListener("click",AstraToggleSubMenu),s[l].addEventListener("click",AstraToggleSubMenu,!1)}},astraNavMenuToggle=function(e){if("undefined"!=typeof astraAddon)astraNavMenuTogglePro(e,u,c,this);else{e.preventDefault();var e=document.querySelectorAll("#masthead > #ast-mobile-header .main-header-bar-navigation"),t=(r=document.querySelectorAll("#masthead > #ast-mobile-header .main-header-menu-toggle"),"0");if(null!==this.closest("#ast-fixed-header")&&(e=document.querySelectorAll("#ast-fixed-header > #ast-mobile-header .main-header-bar-navigation"),r=document.querySelectorAll("#ast-fixed-header .main-header-menu-toggle"),t="0"),void 0===e[t])return!1;for(var a=e[t].querySelectorAll(".menu-item-has-children"),n=0;n<a.length;n++){a[n].classList.remove("ast-submenu-expanded");for(var o=a[n].querySelectorAll(".sub-menu"),s=0;s<o.length;s++)o[s].style.display="none"}-1!==(this.getAttribute("class")||"").indexOf("main-header-menu-toggle")&&(astraToggleClass(e[t],"toggle-on"),astraToggleClass(r[t],"toggled"),e[t].classList.contains("toggle-on")?(e[t].style.display="block",u.classList.add("ast-main-header-nav-open")):(e[t].style.display="",u.classList.remove("ast-main-header-nav-open")))}},u.addEventListener("astra-header-responsive-enabled",function(){var e=document.querySelectorAll(".main-header-bar-navigation");if(0<e.length)for(var t=0;t<e.length;t++){null!=e[t]&&(e[t].classList.remove("toggle-on"),e[t].style.display="");for(var a=e[t].getElementsByClassName("sub-menu"),n=0;n<a.length;n++)a[n].style.display="";for(var o=e[t].getElementsByClassName("children"),s=0;s<o.length;s++)o[s].style.display="";for(var r=e[t].getElementsByClassName("ast-search-menu-icon"),l=0;l<r.length;l++)r[l].classList.remove("ast-dropdown-active"),r[l].style.display=""}},!1),k=navigator.userAgent,n=k.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i)||[],/trident/i.test(n[1])?a=/\brv[ :]+(\d+)/g.exec(k)||[]:"Chrome"===n[1]&&null!=(a=k.match(/\bOPR|Edge\/(\d+)/))||(n=n[2]?[n[1],n[2]]:[navigator.appName,navigator.appVersion,"-?"],null!=(a=k.match(/version\/(\d+)/i))&&n.splice(1,1,a[1]),"Safari"===n[0]&&n[1]<11&&document.body.classList.add("ast-safari-browser-less-than-11")),document.getElementsByClassName("astra-search-icon")),y=0;y<f.length;y++)f[y].onclick=function(e){var t;this.classList.contains("slide-search")&&(e.preventDefault(),(t=this.parentNode.parentNode.parentNode.querySelector(".ast-search-menu-icon")).classList.contains("ast-dropdown-active")?(""!==(t.querySelector(".search-field").value||"")&&t.querySelector(".search-form").submit(),t.classList.remove("ast-dropdown-active")):(t.classList.add("ast-dropdown-active"),t.querySelector(".search-field").setAttribute("autocomplete","off"),setTimeout(function(){t.querySelector(".search-field").focus()},200)))};function b(e,t,a,n){e&&e.forEach(e=>{e.classList.remove("astra-megamenu-focus"),e.classList.remove("toggled-on")}),a&&a.forEach(e=>{e.classList.remove("ast-menu-hover")}),n&&n.forEach(e=>{e.classList.remove("astra-megamenu-wrapper-focus")}),t&&t.forEach(e=>{e.setAttribute("aria-expanded","false")})}function L(){var e=this||"";if(e&&!e.classList.contains("astra-search-icon")&&null===e.closest(".ast-builder-menu")&&-1!==String(e).indexOf("#")){var t=e.parentNode;if(u.classList.contains("ast-header-break-point"))document.querySelector("header.site-header").classList.contains("ast-builder-menu-toggle-link")&&t.classList.contains("menu-item-has-children")||(document.querySelector(".main-header-menu-toggle").classList.remove("toggled"),(t=document.querySelector(".main-header-bar-navigation")).classList.remove("toggle-on"),t.style.display="none",astraTriggerEvent(document.querySelector("body"),"astraMenuHashLinkClicked"));else for(;-1===e.className.indexOf("nav-menu");)"li"===e.tagName.toLowerCase()&&-1!==e.className.indexOf("focus")&&(e.className=e.className.replace(" focus","")),e=e.parentElement}}function E(){for(var e=this;-1===e.className.indexOf("navigation-accessibility");)"li"===e.tagName.toLowerCase()&&e.classList.toggle("focus"),e=e.parentElement}if(document.querySelectorAll(".search-field").forEach(e=>{e.addEventListener("focus",function(e){var t=this.parentNode.parentNode.parentNode.querySelector(".ast-search-menu-icon");t&&astraToggleClass(t,"ast-dropdown-active")}),e.addEventListener("blur",function(e){var t=this.parentNode.parentNode.parentNode.querySelector(".ast-search-menu-icon");t&&(t.classList.remove("ast-dropdown-active"),astraToggleClass(t,"ast-dropdown-active"))})}),u.onclick=function(e){if(void 0!==e.target.classList&&!e.target.classList.contains("ast-search-menu-icon")&&0===astraGetParents(e.target,".ast-search-menu-icon").length&&0===astraGetParents(e.target,".ast-search-icon").length)for(var t=document.getElementsByClassName("ast-search-menu-icon"),a=0;a<t.length;a++)t[a].classList.remove("ast-dropdown-active")},astra.is_header_footer_builder_active||"querySelector"in document&&"addEventListener"in window&&(u.addEventListener("mousedown",function(){u.classList.add("ast-mouse-clicked")}),u.addEventListener("keydown",function(){u.classList.remove("ast-mouse-clicked")})),astra.is_scroll_to_id){let o=e=>{let t=0;for(;e;)t+=e.offsetTop,e=e.offsetParent;return t},t=(e,t=null)=>{let a=0;var n=document.querySelector(".site-header");n&&(0<(n=n.querySelectorAll("div[data-stick-support]")).length?n.forEach(e=>a+=e.clientHeight):"undefined"==typeof astraAddon||Number(astraAddon.sticky_hide_on_scroll)&&!document?.querySelector(".ast-header-sticked")||(n=document.querySelector("#ast-fixed-header"))&&(a=n?.clientHeight,Number(astraAddon?.header_main_shrink))&&(n?.querySelectorAll(".ast-above-header-wrap, .ast-below-header-wrap"))?.forEach(()=>a-=10),n=t||e.target?.closest("a").hash)&&(t=document.querySelector(n))&&(t=(n=o(t))-(a="undefined"!=typeof astraAddon&&Number(astraAddon.sticky_hide_on_scroll)&&window?.scrollY<n?0:a))&&astraSmoothScroll(e,t)},s=[];var k=document.querySelectorAll('a[href*="#"]:not([href="#"]):not([href="#0"]):not([href*="uagb-tab"]):not(.uagb-toc-link__trigger):not(.skip-link):not(.nav-links a):not([href*="tab-"])');if(k)for(let e of k)e.href.split("#")[0]!==location.href.split("#")[0]?s.push({hash:e.hash,url:e.href.split("#")[0]}):""!==e.hash&&e.addEventListener("click",t);window.addEventListener("DOMContentLoaded",e=>{for(var a of s)if(window.location.href.split("#")[0]===a.url){var n=document.querySelector(".site-header");let t=0;n=n.querySelectorAll("div[data-stick-support]"),n=(n&&n.forEach(e=>{t+=e.clientHeight}),document.querySelector(a.hash));n&&(a=o(n)-t)&&astraSmoothScroll(e,a)}location.hash&&setTimeout(()=>t(new Event("click"),location.hash),750)})}astra.is_scroll_to_top&&(s=document.querySelector("#page header"),l=document.getElementById("ast-scroll-top"),astScrollToTopHandler(s,l),window.addEventListener("scroll",function(){astScrollToTopHandler(s,l)}),l.onclick=function(e){astraSmoothScroll(e,0)},l.addEventListener("keydown",function(e){"Enter"===e.key&&astraSmoothScroll(e,0)})),astra?.is_dark_palette?document.documentElement.classList.add("astra-dark-mode-enable"):document.documentElement.classList.remove("astra-dark-mode-enable"),window.addEventListener("DOMContentLoaded",e=>{var t=document.querySelector(".ast-woocommerce-store-notice-hanged");let a=()=>{var e=document.querySelector('.woocommerce-store-notice[data-position="hang-over-top"]');document.body.style.paddingTop=`${e?.clientHeight||0}px`};t&&(window.addEventListener("resize",a),setTimeout(()=>a(),0)),document.querySelector(".woocommerce-store-notice__dismiss-link")?.addEventListener("click",()=>{"undefined"!=typeof wp&&wp?.customize||(document.body.classList.remove("ast-woocommerce-store-notice-hanged"),window.removeEventListener("resize",a),document.body.style.paddingTop=0)})})})(),document.addEventListener("DOMContentLoaded",function(){let t=document.querySelectorAll(".menu-link .dropdown-menu-toggle");function n(e){var e=e.closest(".menu-link"),t=e.nextElementSibling.classList.contains("toggled-on");e.setAttribute("aria-expanded",t?"true":"false")}document.querySelectorAll(".menu-item-has-children > a").forEach(t=>{t.addEventListener("keydown",function(e){"Enter"===e.key&&(e=t.nextElementSibling)&&e.classList.contains("sub-menu")&&(e.classList.toggle("ast-visible"),e="false"===t.getAttribute("aria-expanded")?"true":"false",t.setAttribute("aria-expanded",e))})}),t.forEach(a=>{a.addEventListener("focus",()=>n(a)),a.addEventListener("blur",()=>n(a)),a.addEventListener("keydown",e=>{var t;"Enter"===e.key&&(e.preventDefault(),e=(e=a).closest(".menu-link"),t=e.getAttribute("aria-expanded"),e.setAttribute("aria-expanded","true"===t?"false":"true"))})}),document.addEventListener("keydown",e=>{"Escape"===e.key&&t.forEach(e=>n(e))}),window.addEventListener("orientationchange",()=>{setTimeout(()=>window.dispatchEvent(new Event("resize")),50)})}),document.addEventListener("DOMContentLoaded",()=>{document.querySelectorAll(".astra-shop-thumbnail-wrap").forEach(t=>{let a=t.querySelectorAll("a, span");a.forEach(e=>{e.addEventListener("focus",()=>{t.querySelectorAll(".ast-on-card-button, .ast-quick-view-trigger").forEach(e=>{e.style.opacity="1",e.style.visibility="visible",e.style.borderStyle="none"})}),e.addEventListener("blur",()=>{Array.from(a).some(e=>e===document.activeElement)||t.querySelectorAll(".ast-on-card-button, .ast-quick-view-trigger").forEach(e=>{e.style.opacity="",e.style.visibility=""})})})})}),jQuery(document).on("click",".main-header-bar-navigation a",function(){jQuery("body").hasClass("ast-main-header-nav-open")&&(jQuery(".main-header-menu-toggle.toggled").removeClass("toggled"),jQuery(".main-header-bar-navigation.toggle-on").removeClass("toggle-on").css("display",""),jQuery("body").removeClass("ast-main-header-nav-open"))});