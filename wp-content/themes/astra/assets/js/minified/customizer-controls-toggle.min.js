(e=>{ASTControlTrigger={triggerHook:function(t,s){e("body").trigger("astra-control-trigger."+t,s)},addHook:function(t,s){e("body").on("astra-control-trigger."+t,s)},removeHook:function(t,s){e("body").off("astra-control-trigger."+t,s)}},ASTCustomizerToggles={"astra-settings[display-site-title-responsive]":[],"astra-settings[display-site-tagline-responsive]":[],"astra-settings[ast-header-retina-logo]":[],custom_logo:[],"astra-settings[header-main-rt-section]":[],"astra-settings[hide-custom-menu-mobile]":[],"astra-settings[blog-width]":[],"astra-settings[blog-post-structure]":[],"astra-settings[blog-single-post-structure]":[],"astra-settings[blog-single-width]":[],"astra-settings[blog-single-meta]":[],"astra-settings[footer-sml-layout]":[],"astra-settings[footer-sml-section-1]":[],"astra-settings[footer-sml-section-2]":[],"astra-settings[footer-sml-divider]":[],"astra-settings[header-main-sep]":[],"astra-settings[disable-primary-nav]":[],"astra-settings[footer-adv]":[],"astra-settings[shop-archive-width]":[],"astra-settings[mobile-header-logo]":[],"astra-settings[different-mobile-logo]":[]}})(jQuery);