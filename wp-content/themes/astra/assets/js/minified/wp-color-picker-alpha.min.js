!function(n){var l,o,e,a,i,s,p,c;n.wp.wpColorPicker.prototype._hasAlpha||(l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAAHnlligAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAHJJREFUeNpi+P///4EDBxiAGMgCCCAGFB5AADGCRBgYDh48CCRZIJS9vT2QBAggFBkmBiSAogxFBiCAoHogAKIKAlBUYTELAiAmEtABEECk20G6BOmuIl0CIMBQ/IEMkO0myiSSraaaBhZcbkUOs0HuBwDplz5uFJ3Z4gAAAABJRU5ErkJggg==",o='<div class="wp-picker-holder" />',e='<div class="wp-picker-container" />',a='<input type="button" class="button button-small" />',(i=void 0!==wpColorPickerL10n.current)?s='<a tabindex="0" class="wp-color-result" />':(s='<button type="button" class="button wp-color-result" aria-expanded="false"><span class="wp-color-result-text"></span></button>',p="<label></label>",c='<span class="screen-reader-text"></span>'),Color.fn.toString=function(){if(this._alpha<1)return this.toCSS("rgba",this._alpha).replace(/\s+/g,"");var t=parseInt(this._color,10).toString(16);return this.error?"":(t.length<6&&(t=("00000"+t).substr(-6)),"#"+t)},n.widget("wp.wpColorPicker",n.wp.wpColorPicker,{_hasAlpha:!0,_create:function(){if(n.support.iris){var r=this,t=r.element;if(n.extend(r.options,t.data()),"hue"===r.options.type)return r._createHueOnly();r.close=n.proxy(r.close,r),r.initialValue=t.val(),t.addClass("wp-color-picker"),i?(t.hide().wrap(e),r.wrap=t.parent(),r.toggler=n(s).insertBefore(t).css({backgroundColor:r.initialValue}).attr("title",wpColorPickerL10n.pick).attr("data-current",wpColorPickerL10n.current),r.pickerContainer=n(o).insertAfter(t),r.button=n(a).addClass("hidden")):(t.parent("label").length||(t.wrap(p),r.wrappingLabelText=n(c).insertBefore(t).text(wpColorPickerL10n.defaultLabel)),r.wrappingLabel=t.parent(),r.wrappingLabel.wrap(e),r.wrap=r.wrappingLabel.parent(),r.toggler=n(s).insertBefore(r.wrappingLabel).css({backgroundColor:r.initialValue}),r.toggler.find(".wp-color-result-text").text(wpColorPickerL10n.pick),r.pickerContainer=n(o).insertAfter(r.wrappingLabel),r.button=n(a)),r.options.defaultColor?(r.button.addClass("wp-picker-default").val(wpColorPickerL10n.defaultString),i||r.button.attr("aria-label",wpColorPickerL10n.defaultAriaLabel)):(r.button.addClass("wp-picker-clear").val(wpColorPickerL10n.clear),i||r.button.attr("aria-label",wpColorPickerL10n.clearAriaLabel)),i?t.wrap('<span class="wp-picker-input-wrap" />').after(r.button):(r.wrappingLabel.wrap('<span class="wp-picker-input-wrap hidden" />').after(r.button),r.inputWrapper=t.closest(".wp-picker-input-wrap")),t.iris({target:r.pickerContainer,hide:r.options.hide,width:r.options.width,mode:r.options.mode,palettes:r.options.palettes,change:function(t,o){r.options.alpha?(r.toggler.css({"background-image":"url("+l+")"}),i?r.toggler.html('<span class="color-alpha" />'):(r.toggler.css({position:"relative"}),0==r.toggler.find("span.color-alpha").length&&r.toggler.append('<span class="color-alpha" />')),r.toggler.find("span.color-alpha").css({width:"30px",height:"28px",position:"absolute",top:0,left:0,"border-top-left-radius":"2px","border-bottom-left-radius":"2px",background:o.color.toString()})):r.toggler.css({backgroundColor:o.color.toString()}),n.isFunction(r.options.change)&&r.options.change.call(this,t,o)}}),t.val(r.initialValue),r._addListeners(),r.options.hide||r.toggler.click()}},_addListeners:function(){var o=this;o.wrap.on("click.wpcolorpicker",function(t){t.stopPropagation()}),o.toggler.click(function(){o.toggler.hasClass("wp-picker-open")?o.close():o.open()}),o.element.on("change",function(t){""!==n(this).val()&&!o.element.hasClass("iris-error")||(o.options.alpha?(i&&o.toggler.removeAttr("style"),o.toggler.find("span.color-alpha").css("backgroundColor","")):o.toggler.css("backgroundColor",""),n.isFunction(o.options.clear)&&o.options.clear.call(this,t))}),o.button.on("click",function(t){n(this).hasClass("wp-picker-clear")?(o.element.val(""),o.options.alpha?(i&&o.toggler.removeAttr("style"),o.toggler.find("span.color-alpha").css("backgroundColor","")):o.toggler.css("backgroundColor",""),n.isFunction(o.options.clear)&&o.options.clear.call(this,t),o.element.trigger("change")):n(this).hasClass("wp-picker-default")&&o.element.val(o.options.defaultColor).change()})}}),n.widget("a8c.iris",n.a8c.iris,{_create:function(){var r,t,o,e,a,i;this._super(),this.options.alpha=this.element.data("alpha")||!1,this.element.is(":input")||(this.options.alpha=!1),void 0!==this.options.alpha&&this.options.alpha&&(t=(r=this).element,e=(o=n('<div class="iris-strip iris-slider iris-alpha-slider"><div class="iris-slider-offset iris-slider-offset-alpha"></div></div>').appendTo(r.picker.find(".iris-picker-inner"))).find(".iris-slider-offset-alpha"),e={aContainer:o,aSlider:e},void 0!==t.data("custom-width")?r.options.customWidth=parseInt(t.data("custom-width"))||0:r.options.customWidth=100,r.options.defaultWidth=t.width(),(r._color._alpha<1||-1!=r._color.toString().indexOf("rgb"))&&t.width(parseInt(88)),n.each(e,function(t,o){r.controls[t]=o}),r.controls.square.css({"margin-right":"0"}),e=r.picker.width()-r.controls.square.width()-20,i=e/2-(a=e/6),n.each(["aContainer","strip"],function(t,o){r.controls[o].width(i).css({"margin-left":a+"px"})}),r._initControls(),r._change())},_initControls:function(){var r;this._super(),this.options.alpha&&(r=this).controls.aSlider.slider({orientation:"vertical",min:0,max:100,step:1,value:parseInt(100*r._color._alpha),slide:function(t,o){r._color._alpha=parseFloat(o.value/100),r._change.apply(r,arguments)}})},_change:function(){this._super();var t,o,r,e,a,i=this,n=i.element;this.options.alpha&&(t=i.controls,o=parseInt(100*i._color._alpha),r=["rgb("+(a=i._color.toRgb()).r+","+a.g+","+a.b+") 0%","rgba("+a.r+","+a.g+","+a.b+", 0) 100%"],e=i.options.defaultWidth,i.options.customWidth,a=i.picker.closest(".wp-picker-container").find(".wp-color-result"),t.aContainer.css({background:"linear-gradient(to bottom, "+r.join(", ")+"), url("+l+")"}),a.hasClass("wp-picker-open")&&(t.aSlider.slider("value",o),i._color._alpha<1?(t.strip.attr("style",t.strip.attr("style").replace(/rgba\(([0-9]+,)(\s+)?([0-9]+,)(\s+)?([0-9]+)(,(\s+)?[0-9\.]+)\)/g,"rgb($1$3$5)")),n.width(parseInt(88))):n.width(e))),n.data("reset-alpha")&&i.picker.find(".iris-palette-container").on("click.palette",".iris-palette",function(){i._color._alpha=1,i.active="external",i._change()}),n.trigger("change")},_addInputListeners:function(e){function t(t){var o=new Color(e.val()),r=e.val();e.removeClass("iris-error"),o.error?""!==r&&e.addClass("iris-error"):o.toString()!==a._color.toString()&&("keyup"===t.type&&r.match(/^[0-9a-fA-F]{3}$/)||a._setOption("color",o.toString()))}var a=this;e.on("change",t).on("keyup",a._debounce(t,100)),a.options.hide&&e.on("focus",function(){a.show()})}}))}(jQuery),jQuery(document).ready(function(t){t(".color-picker").wpColorPicker()});