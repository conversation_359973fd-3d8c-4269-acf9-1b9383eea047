(i=>{var c=wp.customize;ASTCustomizer={controls:{},init:function(){ASTCustomizer._initToggles(),ASTCustomizer._initSubControlsToggle()},_initToggles:function(){ASTControlTrigger.triggerHook("astra-toggle-control",c),i.each(ASTCustomizerToggles,function(t,o){c(t,function(e){i.each(o,function(t,n){i.each(n.controls,function(t,o){c.control(o,function(o){function t(t){o.container.toggle(n.callback(t))}t(e.get()),e.bind(t)})})})})})},subControlsToggleCSS(e,t){i.each(t,function(n,t){i.each(t,function(t,o){jQuery("style#ast-sub-control-"+o).remove(),e!==n&&jQuery("head").append('<style id="ast-sub-control-'+o+'">#customize-control-'+o+"\t{ display: none; }</style>")})})},_initSubControlsToggle:function(){document.addEventListener("AstraToggleSubControls",function(t){t=t.detail;ASTCustomizer.subControlsToggleCSS(t.controlValue,t.dependents)})}},i(function(){ASTCustomizer.init()})})(jQuery),(t=>{t.sectionConstructor["astra-pro"]=t.Section.extend({attachEvents:function(){},isContextuallyActive:function(){return!0}})})(wp.customize);