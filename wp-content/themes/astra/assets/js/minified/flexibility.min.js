!function(e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).flexibility=e()}(function(){return function l(n,o,f){function i(r,e){if(!o[r]){if(!n[r]){var t="function"==typeof require&&require;if(!e&&t)return t(r,!0);if(s)return s(r,!0);e=new Error("Cannot find module '"+r+"'");throw e.code="MODULE_NOT_FOUND",e}t=o[r]={exports:{}};n[r][0].call(t.exports,function(e){var t=n[r][1][e];return i(t||e)},t,t.exports,l,n,o,f)}return o[r].exports}for(var s="function"==typeof require&&require,e=0;e<f.length;e++)i(f[e]);return i}({1:[function(e,t,r){t.exports=function(e){var t,r,l,n=-1;if(1<e.lines.length&&"flex-start"===e.style.alignContent)for(t=0;l=e.lines[++n];)l.crossStart=t,t+=l.cross;else if(1<e.lines.length&&"flex-end"===e.style.alignContent)for(t=e.flexStyle.crossSpace;l=e.lines[++n];)l.crossStart=t,t+=l.cross;else if(1<e.lines.length&&"center"===e.style.alignContent)for(t=e.flexStyle.crossSpace/2;l=e.lines[++n];)l.crossStart=t,t+=l.cross;else if(1<e.lines.length&&"space-between"===e.style.alignContent)for(r=e.flexStyle.crossSpace/(e.lines.length-1),t=0;l=e.lines[++n];)l.crossStart=t,t+=l.cross+r;else if(1<e.lines.length&&"space-around"===e.style.alignContent)for(t=(r=2*e.flexStyle.crossSpace/(2*e.lines.length))/2;l=e.lines[++n];)l.crossStart=t,t+=l.cross+r;else for(r=e.flexStyle.crossSpace/e.lines.length,t=e.flexStyle.crossInnerBefore;l=e.lines[++n];)l.crossStart=t,l.cross+=r,t+=l.cross}},{}],2:[function(e,t,r){t.exports=function(e){for(var t,r=-1;line=e.lines[++r];)for(t=-1;child=line.children[++t];){var l=child.style.alignSelf;"flex-start"===(l="auto"===l?e.style.alignItems:l)?child.flexStyle.crossStart=line.crossStart:"flex-end"===l?child.flexStyle.crossStart=line.crossStart+line.cross-child.flexStyle.crossOuter:"center"===l?child.flexStyle.crossStart=line.crossStart+(line.cross-child.flexStyle.crossOuter)/2:(child.flexStyle.crossStart=line.crossStart,child.flexStyle.crossOuter=line.cross,child.flexStyle.cross=child.flexStyle.crossOuter-child.flexStyle.crossBefore-child.flexStyle.crossAfter)}}},{}],3:[function(e,t,r){t.exports=function(e,t){var t="row"===t||"row-reverse"===t,r=e.mainAxis;r?t&&"inline"===r||!t&&"block"===r||(e.flexStyle={main:e.flexStyle.cross,cross:e.flexStyle.main,mainOffset:e.flexStyle.crossOffset,crossOffset:e.flexStyle.mainOffset,mainBefore:e.flexStyle.crossBefore,mainAfter:e.flexStyle.crossAfter,crossBefore:e.flexStyle.mainBefore,crossAfter:e.flexStyle.mainAfter,mainInnerBefore:e.flexStyle.crossInnerBefore,mainInnerAfter:e.flexStyle.crossInnerAfter,crossInnerBefore:e.flexStyle.mainInnerBefore,crossInnerAfter:e.flexStyle.mainInnerAfter,mainBorderBefore:e.flexStyle.crossBorderBefore,mainBorderAfter:e.flexStyle.crossBorderAfter,crossBorderBefore:e.flexStyle.mainBorderBefore,crossBorderAfter:e.flexStyle.mainBorderAfter}):(e.flexStyle=t?{main:e.style.width,cross:e.style.height,mainOffset:e.style.offsetWidth,crossOffset:e.style.offsetHeight,mainBefore:e.style.marginLeft,mainAfter:e.style.marginRight,crossBefore:e.style.marginTop,crossAfter:e.style.marginBottom,mainInnerBefore:e.style.paddingLeft,mainInnerAfter:e.style.paddingRight,crossInnerBefore:e.style.paddingTop,crossInnerAfter:e.style.paddingBottom,mainBorderBefore:e.style.borderLeftWidth,mainBorderAfter:e.style.borderRightWidth,crossBorderBefore:e.style.borderTopWidth,crossBorderAfter:e.style.borderBottomWidth}:{main:e.style.height,cross:e.style.width,mainOffset:e.style.offsetHeight,crossOffset:e.style.offsetWidth,mainBefore:e.style.marginTop,mainAfter:e.style.marginBottom,crossBefore:e.style.marginLeft,crossAfter:e.style.marginRight,mainInnerBefore:e.style.paddingTop,mainInnerAfter:e.style.paddingBottom,crossInnerBefore:e.style.paddingLeft,crossInnerAfter:e.style.paddingRight,mainBorderBefore:e.style.borderTopWidth,mainBorderAfter:e.style.borderBottomWidth,crossBorderBefore:e.style.borderLeftWidth,crossBorderAfter:e.style.borderRightWidth},"content-box"===e.style.boxSizing&&("number"==typeof e.flexStyle.main&&(e.flexStyle.main+=e.flexStyle.mainInnerBefore+e.flexStyle.mainInnerAfter+e.flexStyle.mainBorderBefore+e.flexStyle.mainBorderAfter),"number"==typeof e.flexStyle.cross)&&(e.flexStyle.cross+=e.flexStyle.crossInnerBefore+e.flexStyle.crossInnerAfter+e.flexStyle.crossBorderBefore+e.flexStyle.crossBorderAfter)),e.mainAxis=t?"inline":"block",e.crossAxis=t?"block":"inline","number"==typeof e.style.flexBasis&&(e.flexStyle.main=e.style.flexBasis+e.flexStyle.mainInnerBefore+e.flexStyle.mainInnerAfter+e.flexStyle.mainBorderBefore+e.flexStyle.mainBorderAfter),e.flexStyle.mainOuter=e.flexStyle.main,e.flexStyle.crossOuter=e.flexStyle.cross,"auto"===e.flexStyle.mainOuter&&(e.flexStyle.mainOuter=e.flexStyle.mainOffset),"auto"===e.flexStyle.crossOuter&&(e.flexStyle.crossOuter=e.flexStyle.crossOffset),"number"==typeof e.flexStyle.mainBefore&&(e.flexStyle.mainOuter+=e.flexStyle.mainBefore),"number"==typeof e.flexStyle.mainAfter&&(e.flexStyle.mainOuter+=e.flexStyle.mainAfter),"number"==typeof e.flexStyle.crossBefore&&(e.flexStyle.crossOuter+=e.flexStyle.crossBefore),"number"==typeof e.flexStyle.crossAfter&&(e.flexStyle.crossOuter+=e.flexStyle.crossAfter)}},{}],4:[function(e,t,r){var n=e("../reduce");t.exports=function(r){var l;0<r.mainSpace&&0<(l=n(r.children,function(e,t){return e+parseFloat(t.style.flexGrow)},0))&&(r.main=n(r.children,function(e,t){return"auto"===t.flexStyle.main?t.flexStyle.main=t.flexStyle.mainOffset+parseFloat(t.style.flexGrow)/l*r.mainSpace:t.flexStyle.main+=parseFloat(t.style.flexGrow)/l*r.mainSpace,t.flexStyle.mainOuter=t.flexStyle.main+t.flexStyle.mainBefore+t.flexStyle.mainAfter,e+t.flexStyle.mainOuter},0),r.mainSpace=0)}},{"../reduce":12}],5:[function(e,t,r){var n=e("../reduce");t.exports=function(r){var l;r.mainSpace<0&&0<(l=n(r.children,function(e,t){return e+parseFloat(t.style.flexShrink)},0))&&(r.main=n(r.children,function(e,t){return t.flexStyle.main+=parseFloat(t.style.flexShrink)/l*r.mainSpace,t.flexStyle.mainOuter=t.flexStyle.main+t.flexStyle.mainBefore+t.flexStyle.mainAfter,e+t.flexStyle.mainOuter},0),r.mainSpace=0)}},{"../reduce":12}],6:[function(e,t,r){var n=e("../reduce");t.exports=function(e){var t;e.lines=[t={main:0,cross:0,children:[]}];for(var r,l=-1;r=e.children[++l];)"nowrap"===e.style.flexWrap||0===t.children.length||"auto"===e.flexStyle.main||e.flexStyle.main-e.flexStyle.mainInnerBefore-e.flexStyle.mainInnerAfter-e.flexStyle.mainBorderBefore-e.flexStyle.mainBorderAfter>=t.main+r.flexStyle.mainOuter?(t.main+=r.flexStyle.mainOuter,t.cross=Math.max(t.cross,r.flexStyle.crossOuter)):e.lines.push(t={main:r.flexStyle.mainOuter,cross:r.flexStyle.crossOuter,children:[]}),t.children.push(r);e.flexStyle.mainLines=n(e.lines,function(e,t){return Math.max(e,t.main)},0),e.flexStyle.crossLines=n(e.lines,function(e,t){return e+t.cross},0),"auto"===e.flexStyle.main&&(e.flexStyle.main=Math.max(e.flexStyle.mainOffset,e.flexStyle.mainLines+e.flexStyle.mainInnerBefore+e.flexStyle.mainInnerAfter+e.flexStyle.mainBorderBefore+e.flexStyle.mainBorderAfter)),"auto"===e.flexStyle.cross&&(e.flexStyle.cross=Math.max(e.flexStyle.crossOffset,e.flexStyle.crossLines+e.flexStyle.crossInnerBefore+e.flexStyle.crossInnerAfter+e.flexStyle.crossBorderBefore+e.flexStyle.crossBorderAfter)),e.flexStyle.crossSpace=e.flexStyle.cross-e.flexStyle.crossInnerBefore-e.flexStyle.crossInnerAfter-e.flexStyle.crossBorderBefore-e.flexStyle.crossBorderAfter-e.flexStyle.crossLines,e.flexStyle.mainOuter=e.flexStyle.main+e.flexStyle.mainBefore+e.flexStyle.mainAfter,e.flexStyle.crossOuter=e.flexStyle.cross+e.flexStyle.crossBefore+e.flexStyle.crossAfter}},{"../reduce":12}],7:[function(n,e,t){e.exports=function(e){for(var t,r,l=-1;t=e.children[++l];)n("./flex-direction")(t,e.style.flexDirection);for(n("./flex-direction")(e,e.style.flexDirection),n("./order")(e),n("./flexbox-lines")(e),n("./align-content")(e),l=-1;r=e.lines[++l];)r.mainSpace=e.flexStyle.main-e.flexStyle.mainInnerBefore-e.flexStyle.mainInnerAfter-e.flexStyle.mainBorderBefore-e.flexStyle.mainBorderAfter-r.main,n("./flex-grow")(r),n("./flex-shrink")(r),n("./margin-main")(r),n("./margin-cross")(r),n("./justify-content")(r,e.style.justifyContent,e);n("./align-items")(e)}},{"./align-content":1,"./align-items":2,"./flex-direction":3,"./flex-grow":4,"./flex-shrink":5,"./flexbox-lines":6,"./justify-content":8,"./margin-cross":9,"./margin-main":10,"./order":11}],8:[function(e,t,r){t.exports=function(e,t,r){var l,n,o,r=r.flexStyle.mainInnerBefore,f=-1;if("flex-end"===t)for(l=e.mainSpace,l+=r;o=e.children[++f];)o.flexStyle.mainStart=l,l+=o.flexStyle.mainOuter;else if("center"===t)for(l=e.mainSpace/2,l+=r;o=e.children[++f];)o.flexStyle.mainStart=l,l+=o.flexStyle.mainOuter;else if("space-between"===t)for(n=e.mainSpace/(e.children.length-1),l=0,l+=r;o=e.children[++f];)o.flexStyle.mainStart=l,l+=o.flexStyle.mainOuter+n;else if("space-around"===t)for(l=(n=2*e.mainSpace/(2*e.children.length))/2,l+=r;o=e.children[++f];)o.flexStyle.mainStart=l,l+=o.flexStyle.mainOuter+n;else for(l=0,l+=r;o=e.children[++f];)o.flexStyle.mainStart=l,l+=o.flexStyle.mainOuter}},{}],9:[function(e,t,r){t.exports=function(e){for(var t,r=-1;t=e.children[++r];){var l=0,n=("auto"===t.flexStyle.crossBefore&&++l,"auto"===t.flexStyle.crossAfter&&++l,e.cross-t.flexStyle.crossOuter);"auto"===t.flexStyle.crossBefore&&(t.flexStyle.crossBefore=n/l),"auto"===t.flexStyle.crossAfter&&(t.flexStyle.crossAfter=n/l),"auto"===t.flexStyle.cross?t.flexStyle.crossOuter=t.flexStyle.crossOffset+t.flexStyle.crossBefore+t.flexStyle.crossAfter:t.flexStyle.crossOuter=t.flexStyle.cross+t.flexStyle.crossBefore+t.flexStyle.crossAfter}}},{}],10:[function(e,t,r){t.exports=function(e){for(var t,r=0,l=-1;t=e.children[++l];)"auto"===t.flexStyle.mainBefore&&++r,"auto"===t.flexStyle.mainAfter&&++r;if(0<r){for(l=-1;t=e.children[++l];)"auto"===t.flexStyle.mainBefore&&(t.flexStyle.mainBefore=e.mainSpace/r),"auto"===t.flexStyle.mainAfter&&(t.flexStyle.mainAfter=e.mainSpace/r),"auto"===t.flexStyle.main?t.flexStyle.mainOuter=t.flexStyle.mainOffset+t.flexStyle.mainBefore+t.flexStyle.mainAfter:t.flexStyle.mainOuter=t.flexStyle.main+t.flexStyle.mainBefore+t.flexStyle.mainAfter;e.mainSpace=0}}},{}],11:[function(e,t,r){var l=/^(column|row)-reverse$/;t.exports=function(e){e.children.sort(function(e,t){return e.style.order-t.style.order||e.index-t.index}),l.test(e.style.flexDirection)&&e.children.reverse()}},{}],12:[function(e,t,r){t.exports=function(e,t,r){for(var l=e.length,n=-1;++n<l;)n in e&&(r=t(r,e[n],n));return r}},{}],13:[function(e,t,r){var l=e("./read"),n=e("./write"),o=e("./readAll"),f=e("./writeAll");t.exports=function(e){f(o(e))},t.exports.read=l,t.exports.write=n,t.exports.readAll=o,t.exports.writeAll=f},{"./read":15,"./readAll":16,"./write":17,"./writeAll":18}],14:[function(e,t,r){t.exports=function(e,t,r){var l=e[t];if(o=String(l).match(f)){var n=o[1],o=o[2];if("px"===o)return+n;else if("cm"===o)return.3937*n*96;else if("in"===o)return 96*n;else if("mm"===o)return.3937*n*96/10;else if("pc"===o)return 12*n*96/72;else if("pt"===o)return 96*n/72;else if("rem"===o)return 16*n;else{o=l;n=r;return i.style.cssText="border:none!important;clip:rect(0 0 0 0)!important;display:block!important;font-size:1em!important;height:0!important;margin:0!important;padding:0!important;position:relative!important;width:"+o+"!important",n.parentNode.insertBefore(i,n.nextSibling),o=i.offsetWidth,n.parentNode.removeChild(i),o;return}}return(r=t.match(a))?"none"!==e["border"+r[1]+"Style"]&&s[l]||0:l};var f=/^([-+]?\d*\.?\d+)(%|[a-z]+)$/,i=document.createElement("div"),s={medium:4,none:0,thick:6,thin:2},a=/^border(Bottom|Left|Right|Top)Width$/},{}],15:[function(e,t,r){t.exports=function(e){var t={alignContent:"stretch",alignItems:"stretch",alignSelf:"auto",borderBottomStyle:"none",borderBottomWidth:0,borderLeftStyle:"none",borderLeftWidth:0,borderRightStyle:"none",borderRightWidth:0,borderTopStyle:"none",borderTopWidth:0,boxSizing:"content-box",display:"inline",flexBasis:"auto",flexDirection:"row",flexGrow:0,flexShrink:1,flexWrap:"nowrap",justifyContent:"flex-start",height:"auto",marginTop:0,marginRight:0,marginLeft:0,marginBottom:0,paddingTop:0,paddingRight:0,paddingLeft:0,paddingBottom:0,maxHeight:"none",maxWidth:"none",minHeight:0,minWidth:0,order:0,position:"static",width:"auto"};if(e instanceof Element){var r,l=e.hasAttribute("data-style"),n=l?e.getAttribute("data-style"):e.getAttribute("style")||"",l=(l||e.setAttribute("data-style",n),window.getComputedStyle&&getComputedStyle(e)||{}),o=t,f=l;for(r in o)r in f&&!p.test(r)&&(o[r]=f[r]);var i,s,a,y,l=e.currentStyle||{},c=t,x=l;for(y in c)y in x?c[y]=x[y]:(i=y.replace(/[A-Z]/g,"-$&").toLowerCase())in x&&(c[y]=x[i]);"-js-display"in x&&(c.display=x["-js-display"]);for(var S=t,m=n;s=u.exec(m);){var d=s[1].toLowerCase().replace(/-[a-z]/g,function(e){return e.slice(1).toUpperCase()});S[d]=s[2]}for(a in t)t[a]=h(t,a,e);l=e.getBoundingClientRect();t.offsetHeight=l.height||e.offsetHeight,t.offsetWidth=l.width||e.offsetWidth}return{element:e,style:t}};var u=/([^\s:;]+)\s*:\s*([^;]+?)\s*(;|$)/g,p=/^(alignSelf|height|width)$/,h=e("./getComputedLength")},{"./getComputedLength":14}],16:[function(e,t,r){function y(e){var t=e instanceof Element,r=t&&e.getAttribute("data-style"),t=t&&e.currentStyle&&e.currentStyle["-js-display"];return l.test(r)||n.test(t)}t.exports=function(e){var t=[];return function e(t,r){for(var l,n=y(t),o=[],f=-1;l=t.childNodes[++f];){var i,s=3===l.nodeType&&!/^\s*$/.test(l.nodeValue),s=(n&&s&&(s=l,(l=t.insertBefore(document.createElement("flex-item"),s)).appendChild(s)),l instanceof Element);s&&(s=e(l,r),n)&&((i=l.style).display="inline-block",i.position="absolute",s.style=c(l).style,o.push(s))}var a={element:t,children:o};return n&&(a.style=c(t).style,r.push(a)),a}(e,t),t};var c=e("../read"),l=/(^|;)\s*display\s*:\s*(inline-)?flex\s*(;|$)/i,n=/^(inline-)?flex$/i},{"../read":15}],17:[function(e,t,r){function i(e){return"string"==typeof e?e:Math.max(e,0)+"px"}t.exports=function(e){s(e);var t=e.element.style,r="inline"===e.mainAxis?["main","cross"]:["cross","main"];t.boxSizing="content-box",t.display="block",t.position="relative",t.width=i(e.flexStyle[r[0]]-e.flexStyle[r[0]+"InnerBefore"]-e.flexStyle[r[0]+"InnerAfter"]-e.flexStyle[r[0]+"BorderBefore"]-e.flexStyle[r[0]+"BorderAfter"]),t.height=i(e.flexStyle[r[1]]-e.flexStyle[r[1]+"InnerBefore"]-e.flexStyle[r[1]+"InnerAfter"]-e.flexStyle[r[1]+"BorderBefore"]-e.flexStyle[r[1]+"BorderAfter"]);for(var l,n=-1;l=e.children[++n];){var o=l.element.style,f="inline"===l.mainAxis?["main","cross"]:["cross","main"];o.boxSizing="content-box",o.display="block",o.position="absolute","auto"!==l.flexStyle[f[0]]&&(o.width=i(l.flexStyle[f[0]]-l.flexStyle[f[0]+"InnerBefore"]-l.flexStyle[f[0]+"InnerAfter"]-l.flexStyle[f[0]+"BorderBefore"]-l.flexStyle[f[0]+"BorderAfter"])),"auto"!==l.flexStyle[f[1]]&&(o.height=i(l.flexStyle[f[1]]-l.flexStyle[f[1]+"InnerBefore"]-l.flexStyle[f[1]+"InnerAfter"]-l.flexStyle[f[1]+"BorderBefore"]-l.flexStyle[f[1]+"BorderAfter"])),o.top=i(l.flexStyle[f[1]+"Start"]),o.left=i(l.flexStyle[f[0]+"Start"]),o.marginTop=i(l.flexStyle[f[1]+"Before"]),o.marginRight=i(l.flexStyle[f[0]+"After"]),o.marginBottom=i(l.flexStyle[f[1]+"After"]),o.marginLeft=i(l.flexStyle[f[0]+"Before"])}};var s=e("../flexbox")},{"../flexbox":7}],18:[function(e,t,r){t.exports=function(e){for(var t,r=-1;t=e[++r];)l(t)};var l=e("../write")},{"../write":17}]},{},[13])(13)});