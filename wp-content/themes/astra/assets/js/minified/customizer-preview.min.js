function astra_font_size_rem(t,e,o){var s="";return""!=t&&(o=void 0!==o?o:"desktop",s="font-size: "+t+"px;",e)&&((e=wp.customize("astra-settings[font-size-body]").get()).desktop=""!=e.desktop?e.desktop:15,e.tablet=""!=e.tablet?e.tablet:e.desktop,e.mobile=""!=e.mobile?e.mobile:e.tablet,"px"===e[o+"-unit"])&&(s+="font-size: "+t/e[o]+"rem;"),s}function astra_refresh_customizer(t){wp.customize(t,function(t){t.bind(function(t){wp.customize.preview.send("refresh")})})}function astra_color_responsive_css(a,n,r,i){wp.customize(n,function(t){t.bind(function(t){var e,o,s;t.desktop||t.mobile||t.tablet?(n=(n=n.replace("[","-")).replace("]",""),jQuery("style#"+n+"-"+a).remove(),(s=o=e="")!=t.desktop&&(e=r+": "+t.desktop),""!=t.tablet&&(o=r+": "+t.tablet),""!=t.mobile&&(s=r+": "+t.mobile),jQuery("head").append('<style id="'+n+"-"+a+'">'+i+"\t{ "+e+" }@media (max-width: 768px) {"+i+"\t{ "+o+" } }@media (max-width: 544px) {"+i+"\t{ "+s+" } }</style>")):jQuery("style#"+n+"-"+a).remove()})})}function astra_responsive_font_size(n,r,i=!1,l=""){wp.customize(n,function(t){t.bind(function(t){var e,o,s,a;t.desktop||t.mobile||t.tablet?(n=(n=n.replace("[","-")).replace("]",""),a="font-size",e="px"==t["desktop-unit"]?astra_font_size_rem(t.desktop,!0,"desktop"):"",s=o="",jQuery("style#"+n+"-"+a).remove(),""!=t.desktop&&(e=e||"font-size: "+t.desktop+(null==t["desktop-unit"]?"px":t["desktop-unit"])),""!=t.tablet&&(o="font-size: "+t.tablet+(null==t["tablet-unit"]?"px":t["tablet-unit"])),""!=t.mobile&&(s="font-size: "+t.mobile+(null==t["mobile-unit"]?"px":t["mobile-unit"])),jQuery("head").append('<style id="'+n+"-"+a+'">'+r+"\t{ "+e+" }@media (max-width: 768px) {"+r+"\t{ "+o+" } }@media (max-width: 544px) {"+r+"\t{ "+s+" } }</style>"),i&&(a=new CustomEvent("AstraStyleGuideElementUpdated",{detail:{value:{desktop:t.desktop+(null==t["desktop-unit"]?"px":t["desktop-unit"]),tablet:t.tablet+(null==t["tablet-unit"]?"px":t["tablet-unit"]),mobile:t.mobile+(null==t["mobile-unit"]?"px":t["mobile-unit"])},selector:l}}),document.dispatchEvent(a))):jQuery("style#"+n+"-font-size").remove()})})}function astra_font_extras_css(o,s){wp.customize("astra-settings["+o+"]",function(t){t.bind(function(t){var e;t?(jQuery("style#"+o).remove(),e=s+" { line-height : "+t["line-height"]+t["line-height-unit"]+";",e=(e+="letter-spacing : "+t["letter-spacing"]+t["letter-spacing-unit"]+";")+"text-decoration : "+t["text-decoration"]+";text-transform : "+t["text-transform"]+";}",jQuery("head").append('<style id="'+o+'">'+e+"</style>")):jQuery("style#"+o).remove()})})}function astra_responsive_spacing(i,l,u,c){wp.customize(i,function(t){t.bind(function(o){var s,a,n,t,e="",r="padding";o.desktop.top||o.desktop.right||o.desktop.bottom||o.desktop.left||o.tablet.top||o.tablet.right||o.tablet.bottom||o.tablet.left||o.mobile.top||o.mobile.right||o.mobile.bottom||o.mobile.left?(void 0!==c&&(e=(e=c+"").replace(/,/g,"-")),void 0!==u&&(r=u+""),i=(i=i.replace("[","-")).replace("]",""),jQuery("style#"+i+"-"+r+"-"+e).remove(),n=a=s="",t=void 0!==c?c:["top","bottom","right","left"],jQuery.each(t,function(t,e){""!=o.desktop[e]&&(s+=r+"-"+e+": "+o.desktop[e]+o["desktop-unit"]+";")}),jQuery.each(t,function(t,e){""!=o.tablet[e]&&(a+=r+"-"+e+": "+o.tablet[e]+o["tablet-unit"]+";")}),jQuery.each(t,function(t,e){""!=o.mobile[e]&&(n+=r+"-"+e+": "+o.mobile[e]+o["mobile-unit"]+";")}),jQuery("head").append('<style id="'+i+"-"+r+"-"+e+'">'+l+"\t{ "+s+" }@media (max-width: 768px) {"+l+"\t{ "+a+" } }@media (max-width: 544px) {"+l+"\t{ "+n+" } }</style>")):(wp.customize.preview.send("refresh"),jQuery("style#"+i+"-"+r+"-"+e).remove())})})}function astra_css_font_size(o,s){wp.customize(o,function(t){t.bind(function(t){var e;t?(o=(o=o.replace("[","-")).replace("]",""),jQuery("style#"+o).remove(),e="font-size: "+t,(!Number.isNaN(t)||0<=t.indexOf("px"))&&(e=astra_font_size_rem(t=t.replace("px",""),!0)),jQuery("head").append('<style id="'+o+'">'+s+"\t{ "+e+" }</style>")):jQuery("style#"+o).remove()})})}function get_hexdec(t){t=t.toString(16);return parseInt(t,16)}function astra_css(o,s,a,n,r=!1){wp.customize(o,function(t){t.bind(function(t){var e;o=(o=o.replace("[","-")).replace("]",""),(t||0===t)&&(void 0!==n&&("url"===n?t="url("+t+")":t+=n),e=r?" !important":"",jQuery("style#"+o+"-"+s).remove(),jQuery("head").append('<style id="'+o+"-"+s+'">'+a+"\t{ "+s+": "+t+e+" }</style>"),"unset"!==t)||jQuery("style#"+o+"-"+s).remove()})})}function astra_add_dynamic_css(t,e){t=(t=t.replace("[","-")).replace("]",""),jQuery("style#"+t).remove(),jQuery("head").append('<style id="'+t+'">'+e+"</style>")}function astra_background_obj_css(t,e,o,s){var a="",n=e["background-image"],r=e["background-color"];if(""===r&&""===n)jQuery("style#"+o).remove();else{if(void 0!==e["background-type"]&&""!==e["background-type"])if("color"===e["background-type"])""!==n&&""!==r&&void 0!==r&&"unset"!==r?a="background-image: linear-gradient(to right, "+r+", "+r+"), url("+n+");":void 0!==n&&""!==n&&"unset"!==n||(a="background-color: "+r+";");else if("image"===e["background-type"]){if(""!==n)if("overlay-type"in e&&"none"!==e["overlay-type"]){var i="overlay-color"in e?e["overlay-color"]:"",l="overlay-opacity"in e?e["overlay-opacity"]:"",u="overlay-gradient"in e?e["overlay-gradient"]:"";if("classic"===e["overlay-type"]&&""!==i){if(""!==l)return void wp.customize.preview.send("refresh");a="background-image: linear-gradient(to right, "+e["overlay-color"]+", "+e["overlay-color"]+"), url("+n+");"}else a="gradient"===e["overlay-type"]&&""!==u?"background-image: "+u+", url("+n+");":"background-image: url("+n+");"}else a="background-image: url("+n+");"}else"gradient"===e["background-type"]&&""!==r&&"unset"!==r&&(a="background-image: "+r+";");""!==n&&(a=(a=(a+="background-repeat: "+e["background-repeat"]+";")+"background-position: "+e["background-position"]+";")+"background-size: "+e["background-size"]+";background-attachment: "+e["background-attachment"]+";"),astra_add_dynamic_css(o,s.replace("{{css}}",a))}}function astra_generate_outside_font_family_css(n,r,i=!1,l=""){wp.customize(n,function(t){t.bind(function(t,e){var o="font-family",s="",a=t.split(",")[0];a=a.replace(/'/g,""),n=(n=n.replace("[","-")).replace("]",""),jQuery("style#"+n+"-"+o).remove(),a in astraCustomizer.googleFonts&&(a=a.split(" ").join("+"),jQuery("link#"+n).remove(),s='<link id="'+n+'" href="https://fonts.googleapis.com/css?family='+a+'"  rel="stylesheet">'),i&&(a=new CustomEvent("AstraStyleGuideElementUpdated",{detail:{value:t,selector:l}}),document.dispatchEvent(a)),jQuery("head").append('<style id="'+n+"-"+o+'">'+r+"\t{ "+o+": "+t+" }</style>"+s)})})}function astra_builder_advanced_css(o,s){wp.customize("astra-settings["+o+"-padding]",function(t){t.bind(function(t){var e;t.hasOwnProperty("desktop")&&(""!=t.desktop.bottom||""!=t.desktop.top||""!=t.desktop.left||""!=t.desktop.right||""!=t.tablet.bottom||""!=t.tablet.top||""!=t.tablet.left||""!=t.tablet.right||""!=t.mobile.bottom||""!=t.mobile.top||""!=t.mobile.left||""!=t.mobile.right?(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e="")+s+" {padding-left: "+t.desktop.left+t["desktop-unit"]+";")+"padding-right: "+t.desktop.right+t["desktop-unit"]+";")+"padding-top: "+t.desktop.top+t["desktop-unit"]+";")+"padding-bottom: "+t.desktop.bottom+t["desktop-unit"]+";} @media (max-width: 921px) {")+s+" {padding-left: "+t.tablet.left+t["tablet-unit"]+";")+"padding-right: "+t.tablet.right+t["tablet-unit"]+";")+"padding-top: "+t.tablet.top+t["tablet-unit"]+";")+"padding-bottom: "+t.tablet.bottom+t["tablet-unit"]+";} } @media (max-width: 544px) {")+s+" {padding-left: "+t.mobile.left+t["mobile-unit"]+";")+"padding-right: "+t.mobile.right+t["mobile-unit"]+";")+"padding-top: "+t.mobile.top+t["mobile-unit"]+";")+"padding-bottom: "+t.mobile.bottom+t["mobile-unit"]+";",astra_add_dynamic_css(o+"-padding-toggle-button",e=e+"} "+"} ")):astra_add_dynamic_css(o+"-padding-toggle-button",""))})}),wp.customize("astra-settings["+o+"-margin]",function(t){t.bind(function(t){var e;t.hasOwnProperty("desktop")&&(""!=t.desktop.bottom||""!=t.desktop.top||""!=t.desktop.left||""!=t.desktop.right||""!=t.tablet.bottom||""!=t.tablet.top||""!=t.tablet.left||""!=t.tablet.right||""!=t.mobile.bottom||""!=t.mobile.top||""!=t.mobile.left||""!=t.mobile.right?(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e="")+s+" {margin-left: "+t.desktop.left+t["desktop-unit"]+";")+"margin-right: "+t.desktop.right+t["desktop-unit"]+";")+"margin-top: "+t.desktop.top+t["desktop-unit"]+";")+"margin-bottom: "+t.desktop.bottom+t["desktop-unit"]+";} @media (max-width: 921px) {")+s+" {margin-left: "+t.tablet.left+t["tablet-unit"]+";")+"margin-right: "+t.tablet.right+t["tablet-unit"]+";")+"margin-top: "+t.tablet.top+t["tablet-unit"]+";")+"margin-bottom: "+t.tablet.bottom+t["tablet-unit"]+";} } @media (max-width: 544px) {")+s+" {margin-left: "+t.mobile.left+t["mobile-unit"]+";")+"margin-right: "+t.mobile.right+t["mobile-unit"]+";")+"margin-top: "+t.mobile.top+t["mobile-unit"]+";")+"margin-bottom: "+t.mobile.bottom+t["mobile-unit"]+";",astra_add_dynamic_css(o+"-margin-toggle-button",e=e+"} "+"} ")):astra_add_dynamic_css(o+"-margin-toggle-button",""))})})}function astra_border_spacing_advanced_css(o,s){wp.customize("astra-settings["+o+"-border-width]",function(t){t.bind(function(t){var e=s+" {";t.top&&(e=e+"border-top-style: solid;border-top-width: "+t.top+"px;"),t.right&&(e=e+"border-right-style: solid;border-right-width: "+t.right+"px;"),t.bottom&&(e=e+"border-bottom-style: solid;border-bottom-width: "+t.bottom+"px;"),t.left&&(e=e+"border-left-style: solid;border-left-width: "+t.left+"px;"),astra_add_dynamic_css("astra-settings["+o+"-border-width]",e+="} ")})}),wp.customize("astra-settings["+o+"-border-radius]",function(t){t.bind(function(t){var e;t.top||t.right||t.bottom||t.left?(e=s+" {",""!==t.top&&(e+="border-top-left-radius: "+t.top+"px;"),""!==t.right&&(e+="border-top-right-radius: "+t.right+"px;"),""!==t.left&&(e+="border-bottom-left-radius: "+t.left+"px;"),""!==t.bottom&&(e+="border-bottom-right-radius: "+t.bottom+"px;"),astra_add_dynamic_css("astra-settings["+o+"-border-radius]",e+="} ")):wp.customize.preview.send("refresh")})}),astra_css("astra-settings["+o+"-border-color]","border-color",s),astra_builder_advanced_css(o,s)}function astra_generate_font_weight_css(s,a,n,r){wp.customize(a,function(t){t.bind(function(t){a=(a=a.replace("[","-")).replace("]","");var e,o="";t?("undefined"!=typeof unit&&("url"===unit?t="url("+t+")":t+=unit),e=(e=(e=wp.customize._value[s]._value).split(","))[0].replace(/'/g,""),jQuery("style#"+a+"-"+n).remove(),e in astraCustomizer.googleFonts&&(jQuery("#"+s).remove(),o="inherit"===t?'<link id="'+s+'" href="https://fonts.googleapis.com/css?family='+e+'"  rel="stylesheet">':'<link id="'+s+'" href="https://fonts.googleapis.com/css?family='+e+"%3A"+t+'"  rel="stylesheet">'),jQuery("head").append('<style id="'+a+"-"+n+'">'+r+"\t{ "+n+": "+t+" }</style>"+o)):jQuery("style#"+a).remove()})})}function astra_apply_responsive_background_css(u,c,d,t,b){wp.customize(u,function(t){t.bind(function(t){if(b=(b=b||"")||"header",u=(u=u.replace("[","-")).replace("]",""),""!==t[d]&&void 0!==t[d]){var e,o="",s=t[d]["background-image"],a=t.tablet["background-image"],n=t.desktop["background-image"],r=t[d]["background-color"],i=!!t.tablet["background-image"],l=!!t.desktop["background-image"];if(void 0!==t[d]["background-type"]&&""!==t[d]["background-type"])if("color"===t[d]["background-type"])""!==s&&""!==r&&void 0!==r&&"unset"!==r?o="background-image: linear-gradient(to right, "+r+", "+r+"), url("+s+");":"mobile"===d?l?o="background-image: linear-gradient(to right, "+r+", "+r+"), url("+n+");":i?o="background-image: linear-gradient(to right, "+r+", "+r+"), url("+a+");":(o="background-color: "+r+";",o+="background-image: none;"):"tablet"===d?l?o="background-image: linear-gradient(to right, "+r+", "+r+"), url("+n+");":(o="background-color: "+r+";",o+="background-image: none;"):void 0!==s&&""!==s||(o="background-color: "+r+";",o+="background-image: none;");else if("image"===t[d]["background-type"]){if(""!==s)if(void 0!==t[d]["overlay-type"]&&"none"!==t[d]["overlay-type"]){i=void 0!==t[d]["overlay-color"]?t[d]["overlay-color"]:"",a=void 0!==t[d]["overlay-opacity"]?t[d]["overlay-opacity"]:"",l=void 0!==t[d]["overlay-gradient"]?t[d]["overlay-gradient"]:"";if("classic"===t[d]["overlay-type"]&&""!==i){if(""!==a)return void wp.customize.preview.send("refresh");o="background-image: linear-gradient(to right, "+i+", "+i+"), url("+s+");"}else o="gradient"===t[d]["overlay-type"]&&""!==l?"background-image: "+l+", url("+s+");":"background-image: url("+s+");"}else o="background-image: url("+s+");"}else"gradient"===t[d]["background-type"]&&""!==r&&"unset"!==r&&(o="background-image: "+r+";");""!==s&&(o=(o=(o=(o+="background-repeat: "+t[d]["background-repeat"]+";")+"background-position: "+t[d]["background-position"]+";")+"background-size: "+t[d]["background-size"]+";")+"background-attachment: "+t[d]["background-attachment"]+";"),jQuery("style#"+u+"-"+d+"-"+b).remove(),"desktop"==d&&(e='<style id="'+u+"-"+d+"-"+b+'">'+c+"\t{ "+o+" }</style>"),"tablet"==d&&(e='<style id="'+u+"-"+d+"-"+b+'">@media (max-width: 768px) {'+c+"\t{ "+o+" } }</style>"),"mobile"==d&&(e='<style id="'+u+"-"+d+"-"+b+'">@media (max-width: 544px) {'+c+"\t{ "+o+" } }</style>"),jQuery("head").append(e)}})})}function getChangedKey(t,e){t=isJsonString(t)?JSON.parse(t):t,e=isJsonString(e)?JSON.parse(e):e;for(var o in t){if(!e.hasOwnProperty(o)||!t.hasOwnProperty(o))return o;if(!1===((t,e)=>{var o=Object.prototype.toString.call(t);if(0<=["[object Array]","[object Object]"].indexOf(o)){if("string"==typeof getChangedKey(t,e))return!1}else{if(o!==Object.prototype.toString.call(e))return!1;if("[object Function]"===o){if(t.toString()!==e.toString())return!1}else if(t!==e)return!1}})(t[o],e[o]))return o}return!0}function isJsonString(t){try{JSON.parse(t)}catch(t){return!1}return!0}function hasWordPressWidgetBlockEditor(){return astraCustomizer.has_block_editor_support||!1}(n=>{wp.customize("astra-settings[logo-svg-icon]",function(t){t.bind(function(){astra_add_dynamic_css("ast-header-responsive-logo-svg-icon-width",".ast-logo-title-inline .ast-site-identity { gap: 5px; } #masthead .ast-logo-svg-icon svg, .ast-sg-logo-section .ast-logo-svg-icon svg { width: 30px; }")})}),wp.customize("astra-settings[ast-header-responsive-logo-width]",function(t){t.bind(function(t){var e=astraCustomizer.tablet_breakpoint||921,o=astraCustomizer.mobile_breakpoint||544;""!=t.desktop||""!=t.tablet||""!=t.mobile?(astra_add_dynamic_css("ast-header-responsive-logo-width",`#masthead .site-logo-img .custom-logo-link img, .ast-sg-logo-section .site-logo-img .custom-logo-link img {
					max-width: ${t.desktop}px;
					width: ${t.desktop}px;
				}
				@media( max-width: ${e}px ) {
					#masthead .site-logo-img .custom-logo-link img, .ast-sg-logo-section .site-logo-img .custom-logo-link img {
						max-width: ${t.tablet}px;
						width: ${t.tablet}px;
					}
					#masthead .site-logo-img img {
						max-width: ${t.tablet}px;
						width: ${t.tablet}px;
					}
				}
				@media( max-width: ${o}px ) {
					#masthead .site-logo-img .custom-logo-link img, .ast-sg-logo-section .site-logo-img .custom-logo-link img {
						max-width: ${t.mobile}px;
						width: ${t.mobile}px;
					}
					#masthead .site-logo-img img {
						max-width: ${t.mobile}px;
						max-height: ${t.mobile}px;
					}
				}`),astra_add_dynamic_css("mobile-header-logo-width",".ast-header-break-point #masthead .site-logo-img .custom-mobile-logo-link img, .ast-header-break-point .ast-sg-logo-section .site-logo-img .custom-mobile-logo-link img { max-width: "+t.tablet+"px; } @media( max-width: "+e+"px ) { .ast-header-break-point #masthead .site-logo-img .custom-mobile-logo-link img, .ast-header-break-point .ast-sg-logo-section .site-logo-img .custom-mobile-logo-link img { max-width: "+t.tablet+"px; }  @media( max-width: "+o+"px ) { .ast-header-break-point #masthead .site-logo-img .custom-mobile-logo-link img { max-width: "+t.mobile+"px; }"),astra_add_dynamic_css("ast-header-responsive-logo-svg-icon-width","#masthead .ast-logo-svg-icon svg, .ast-sg-logo-section .ast-logo-svg-icon svg { width: "+t.desktop+"px; } @media( max-width: "+e+"px ) { #masthead .ast-logo-svg-icon svg, .ast-sg-logo-section .ast-logo-svg-icon svg { width: "+t.tablet+"px; } #masthead .ast-logo-svg-icon svg, .ast-sg-logo-section .ast-logo-svg-icon svg { width: "+t.tablet+"px; } } @media( max-width: "+o+"px ) { #masthead .ast-logo-svg-icon svg, .ast-sg-logo-section .ast-logo-svg-icon svg { width: "+t.mobile+"px; }#masthead .ast-logo-svg-icon svg, .ast-sg-logo-section .ast-logo-svg-icon svg { width: "+t.mobile+"px; } }")):wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[logo-svg-site-title-gap]",function(t){t.bind(function(t){var e=astraCustomizer.tablet_breakpoint||921;""!=t.desktop||""!=t.tablet||""!=t.mobile?astra_add_dynamic_css("logo-svg-site-title-gap",`.ast-logo-title-inline .ast-site-identity {
					gap: ${t.desktop}px;
				}
				@media( max-width: ${e}px ) {
					.ast-logo-title-inline .ast-site-identity {
						gap: ${t.tablet}px;
					}
				}
				@media( max-width: ${astraCustomizer.mobile_breakpoint||544}px ) {
					.ast-logo-title-inline .ast-site-identity {
						gap: ${t.mobile}px;
					}
				}`):wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[display-site-title-responsive]",function(t){t.bind(function(t){astra_add_dynamic_css("display-site-title-responsive",".ast-site-title-wrap .site-title { display: "+(t.desktop?"block":"none")+";} @media( max-width: "+(astraBuilderPreview.tablet_break_point||921)+"px) { .ast-site-title-wrap .site-title { display: "+(t.tablet?"block":"none")+";} } @media( max-width: "+(astraBuilderPreview.mobile_break_point||544)+"px) { .ast-site-title-wrap .site-title { display: "+(t.mobile?"block":"none")+";} }")})}),wp.customize("astra-settings[display-site-tagline-responsive]",function(t){t.bind(function(t){astra_add_dynamic_css("display-site-tagline-responsive",".ast-site-title-wrap .site-description { display: "+(t.desktop?"block":"none")+";} @media( max-width: "+(astraBuilderPreview.tablet_break_point||768)+"px) { .ast-site-title-wrap .site-description { display: "+(t.tablet?"block":"none")+";} } @media( max-width: "+(astraBuilderPreview.mobile_break_point||544)+"px) { .ast-site-title-wrap .site-description { display: "+(t.mobile?"block":"none")+";} }")})}),wp.customize("astra-settings[site-content-width]",function(t){t.bind(function(t){var e=(e="@media (min-width: 554px) {")+(".ast-container, .fl-builder #content .entry-header { max-width: "+(40+parseInt(t))+"px } ")+"}";astra_add_dynamic_css("site-content-width",e=jQuery("body").hasClass("ast-page-builder-template")?(e+="@media (min-width: 554px) {")+".ast-page-builder-template .comments-area { max-width: "+(40+parseInt(t))+"px } }":e)})}),wp.customize("astra-settings[header-main-menu-label]",function(t){t.bind(function(t){var e;0<n("button.main-header-menu-toggle .mobile-menu-wrap .mobile-menu").length?""!=t?n("button.main-header-menu-toggle .mobile-menu-wrap .mobile-menu").text(t):n("button.main-header-menu-toggle .mobile-menu-wrap").remove():(e=n("button.main-header-menu-toggle").html(),""!=t&&(e+='<div class="mobile-menu-wrap"><span class="mobile-menu">'+t+"</span> </div>"),n("button.main-header-menu-toggle").html(e))})});var e,t=t=>{astra_apply_responsive_background_css("astra-settings[content-bg-obj-responsive]",t,"desktop"),astra_apply_responsive_background_css("astra-settings[content-bg-obj-responsive]",t,"tablet"),astra_apply_responsive_background_css("astra-settings[content-bg-obj-responsive]",t,"mobile")};astra_apply_responsive_background_css("astra-settings[site-layout-outside-bg-obj-responsive]","body, .ast-separate-container","desktop"),astra_apply_responsive_background_css("astra-settings[site-layout-outside-bg-obj-responsive]","body, .ast-separate-container","tablet"),astra_apply_responsive_background_css("astra-settings[site-layout-outside-bg-obj-responsive]","body, .ast-separate-container","mobile"),astraCustomizer.is_content_bg_option_to_load&&(e=astraCustomizer.content_layout,p=astraCustomizer.site_layout,o=void 0!==wp.customize._value["astra-settings[blog-grid]"]?wp.customize._value["astra-settings[blog-grid]"]._value:1,g=".ast-separate-container .ast-article-single:not(.ast-related-post), .ast-separate-container .comments-area .comment-respond,.ast-separate-container .comments-area .ast-comment-list li, .ast-separate-container .ast-woocommerce-container, .ast-separate-container .error-404, .ast-separate-container .no-results, .single.ast-separate-container .site-main .ast-author-meta, .ast-separate-container .related-posts, .ast-separate-container .comments-count-wrapper, .ast-separate-container .comments-area .comments-title, .ast-single-related-posts-container, .ast-plain-container, .site-content article .ast-article-inner","blog-layout-1"==(void 0!==wp.customize._value["astra-settings[blog-layout]"]?wp.customize._value["astra-settings[blog-layout]"]._value:"blog-layout-1")&&1!=o&&(g+=", .ast-separate-container .ast-article-inner"),"boxed-container"==e?t(g+=", .ast-separate-container.ast-two-container #secondary .widget"):"content-boxed-container"==e?t(g):!astraCustomizer.apply_content_bg_fullwidth_layouts||"ast-box-layout"!==p&&"ast-padded-layout"!==p||"plain-container"!==e?astraCustomizer.apply_content_bg_fullwidth_layouts&&"plain-container"===e?t(g+=", .ast-plain-container .site-content"):astraCustomizer.apply_content_bg_fullwidth_layouts&&"page-builder"==e&&"ast-box-layout"!==p&&"ast-padded-layout"!==p&&t(g+=", .ast-page-builder-template .site-content"):t(".ast-box-layout.ast-plain-container .site-content, .ast-padded-layout.ast-plain-container .site-content")),wp.customize("astra-settings[content-bg-obj-responsive]",function(t){t.bind(function(t){"narrow-container"==e&&wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[blog-max-width]",function(t){t.bind(function(t){var e="@media all and ( min-width: 921px ) {";jQuery("body").hasClass("ast-woo-shop-archive")||(e+=".blog .site-content > .ast-container,.archive .site-content > .ast-container{ max-width: "+parseInt(t)+"px } "),jQuery("body").hasClass("ast-fluid-width-layout")&&(e+=".blog .site-content > .ast-container,.archive .site-content > .ast-container{ padding-left:20px; padding-right:20px; } "),astra_add_dynamic_css("blog-max-width",e+="}")})}),wp.customize("astra-settings[blog-single-max-width]",function(t){t.bind(function(t){var e="@media all and ( min-width: 921px ) {";e+=".single-post .site-content > .ast-container{ max-width: "+(40+parseInt(t))+"px } ",jQuery("body").hasClass("ast-fluid-width-layout")&&(e+=".single-post .site-content > .ast-container{ padding-left:20px; padding-right:20px; } "),astra_add_dynamic_css("blog-single-max-width",e+="}")})}),wp.customize("astra-settings[page-single-max-width]",function(t){t.bind(function(t){var e="@media all and ( min-width: 921px ) {";astra_add_dynamic_css("page-single-max-width",e+(".page .site-content > .ast-container{ max-width: "+(40+parseInt(t))+"px } ")+"}")})}),wp.customize("astra-settings[edd-archive-max-width]",function(t){t.bind(function(t){astra_add_dynamic_css("edd-archive-max-width",".ast-edd-archive-page .site-content > .ast-container { max-width: "+parseInt(t)+"px } ")})}),wp.customize("astra-settings[site-sidebar-width]",function(t){t.bind(function(t){var e;!jQuery("body").hasClass("ast-no-sidebar")&&15<=t&&t<=50&&(e="@media (min-width: 769px) {",astra_add_dynamic_css("site-sidebar-width",e=(e+="#primary { width: "+(100-parseInt(t))+"% } ")+("#secondary { width: "+t+"% } ")+"}"))})}),wp.customize("astra-settings[header-main-sep]",function(t){t.bind(function(t){var e="body.ast-header-break-point .main-header-bar { border-bottom-width: "+t+"px }";astra_add_dynamic_css("header-main-sep",(e+=".ast-desktop .main-header-bar {")+("border-bottom-width: "+t+"px")+"}")})}),wp.customize("astra-settings[footer-sml-divider]",function(t){t.bind(function(t){jQuery(".ast-small-footer").css("border-top-width",t+"px")})}),wp.customize("astra-settings[footer-adv-border-width]",function(t){t.bind(function(t){jQuery(".footer-adv .footer-adv-overlay").css("border-top-width",t+"px")})}),wp.customize("astra-settings[footer-adv-border-color]",function(t){t.bind(function(t){jQuery(".footer-adv .footer-adv-overlay").css("border-top-color",t)})}),wp.customize("astra-settings[footer-sml-divider-color]",function(t){t.bind(function(t){jQuery(".ast-small-footer").css("border-top-color",t)})});let r=astraCustomizer.astra_woo_btn_global_compatibility?', .woocommerce a.button, .woocommerce .widget_price_filter .button, .woocommerce button.button, [CLASS*="wc-block"] button':"";var o=astraCustomizer.astra_woo_btn_global_compatibility?', .woocommerce a.button:hover, .woocommerce .widget_price_filter .button:hover, .woocommerce button.button:hover, [CLASS*="wc-block"] button:hover':"";let i=astraCustomizer.isLifterLMS?", .lifterlms a.llms-button-primary, .lifterlms a.llms-button-secondary, .lifterlms .llms-button-action, .lifterlms button.llms-field-button, .lifterlms a.llms-field-button":"";var s,a,l,u,c,d,b,p=astraCustomizer.isLifterLMS?", .lifterlms a.llms-button-primary:hover, .lifterlms a.llms-button-primary:focus, .lifterlms a.llms-button-secondary:hover, .lifterlms a.llms-button-secondary:focus, .lifterlms .llms-button-action:hover, .lifterlms .llms-button-action:focus, .lifterlms button.llms-field-button:hover, .lifterlms button.llms-field-button:focus, .lifterlms a.llms-field-button:hover, .lifterlms a.llms-field-button:focus":"",m=(wp.customize("astra-settings[button-radius-fields]",function(t){t.bind(function(t){var e=astraBuilderPreview.tablet_break_point||768,o=astraBuilderPreview.mobile_break_point||544,s=hasWordPressWidgetBlockEditor()?', form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button':"";let a='.menu-toggle, button, .ast-button, .ast-custom-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .wp-block-button:not(.is-style-outline) .wp-block-button__link'+astraCustomizer.v4_2_2_core_form_btns_styling+', body #ld_course_list .btn, body a.btn-blue, body a.btn-blue:visited, body a#quiz_continue_link, body .btn-join, body .learndash_checkout_buttons input.btn-join[type="button"], body #btn-join, body .learndash_checkout_buttons input.btn-join[type="submit"], body .wpProQuiz_content .wpProQuiz_button2, a.llms-button-primary, .llms-button-secondary, .llms-button-action, .llms-field-button, .llms-button-action.large'+s+r+i;document.body.classList.contains("edd-page")&&(a+=", .ast-edd-site-header-cart .widget_edd_cart_widget .edd_checkout a, .widget_edd_cart_widget .edd_checkout a ");s=a+"{ border-top-left-radius :"+t.desktop.top+t["desktop-unit"]+"; border-bottom-right-radius :"+t.desktop.bottom+t["desktop-unit"]+"; border-bottom-left-radius :"+t.desktop.left+t["desktop-unit"]+"; border-top-right-radius :"+t.desktop.right+t["desktop-unit"]+"; } ";astra_add_dynamic_css("button-radius",(s+="@media (max-width: "+e+"px) { "+a+"{ border-top-left-radius :"+t.tablet.top+t["tablet-unit"]+"; border-bottom-right-radius :"+t.tablet.bottom+t["tablet-unit"]+"; border-bottom-left-radius :"+t.tablet.left+t["tablet-unit"]+"; border-top-right-radius :"+t.tablet.right+t["tablet-unit"]+"; } } ")+("@media (max-width: "+o+"px) { "+a+"{ border-top-left-radius :"+t.mobile.top+t["mobile-unit"]+"; border-bottom-right-radius :"+t.mobile.bottom+t["mobile-unit"]+"; border-bottom-left-radius :"+t.mobile.left+t["mobile-unit"]+"; border-top-right-radius :"+t.mobile.right+t["mobile-unit"]+"; } } "))})}),wp.customize("astra-settings[secondary-button-radius-fields]",function(t){t.bind(function(t){var e,o,s=astraBuilderPreview.tablet_break_point||768;""!==t&&(o=(e=".wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button, .ast-outline-button, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button")+"{ border-top-left-radius :"+t.desktop.top+t["desktop-unit"]+"; border-bottom-right-radius :"+t.desktop.bottom+t["desktop-unit"]+"; border-bottom-left-radius :"+t.desktop.left+t["desktop-unit"]+"; border-top-right-radius :"+t.desktop.right+t["desktop-unit"]+"; } ",astra_add_dynamic_css("secondary-button-radius",o=(o+="@media (max-width: "+s+"px) { "+e+"{ border-top-left-radius :"+t.tablet.top+t["tablet-unit"]+"; border-bottom-right-radius :"+t.tablet.bottom+t["tablet-unit"]+"; border-bottom-left-radius :"+t.tablet.left+t["tablet-unit"]+"; border-top-right-radius :"+t.tablet.right+t["tablet-unit"]+"; } } ")+("@media (max-width: "+(astraBuilderPreview.mobile_break_point||544)+"px) { "+e+"{ border-top-left-radius :"+t.mobile.top+t["mobile-unit"]+"; border-bottom-right-radius :"+t.mobile.bottom+t["mobile-unit"]+"; border-bottom-left-radius :"+t.mobile.left+t["mobile-unit"]+"; border-top-right-radius :"+t.mobile.right+t["mobile-unit"]+"; } } ")))})}),wp.customize("astra-settings[header-main-sep]",function(t){t.bind(function(t){var e=" body.ast-header-break-point .main-header-bar { border-bottom-width: "+t+"px } ";astra_add_dynamic_css("header-main-sep",(e+=".ast-desktop .main-header-bar {")+("border-bottom-width: "+t+"px")+"}")})}),wp.customize("astra-settings[header-main-sep-color]",function(t){t.bind(function(t){var e;""==t&&wp.customize.preview.send("refresh"),t&&(e=" .ast-desktop .main-header-bar { border-bottom-color: "+t+"; } ",astra_add_dynamic_css("header-main-sep-color",e+=" body.ast-header-break-point .main-header-bar { border-bottom-color: "+t+"; } "))})}),wp.customize("astra-settings[mobile-header-toggle-btn-style-color]",function(t){t.bind(function(t){""!=t?astra_add_dynamic_css("primary-toggle-button-color",jQuery(".menu-toggle").hasClass("ast-mobile-menu-buttons-fill")?".ast-header-break-point .ast-mobile-menu-buttons-fill.menu-toggle { background: "+t+"}":jQuery(".menu-toggle").hasClass("ast-mobile-menu-buttons-outline")?".ast-header-break-point .ast-mobile-menu-buttons-outline.menu-toggle { border: 1px solid "+t+"; color: "+t+"}":".ast-header-break-point .ast-mobile-menu-buttons-minimal.menu-toggle { color: "+t+"}"):wp.customize.preview.send("refresh")})}),astra_responsive_font_size("astra-settings[font-size-site-tagline]",".ast-sg-logo-section .site-description, .site-header .site-description"),astra_responsive_font_size("astra-settings[font-size-site-title]",".site-title"),astra_responsive_font_size("astra-settings[font-size-page-title]","body:not(.ast-single-post) .entry-title"),astra_responsive_font_size("astra-settings[font-size-post-meta]",".entry-meta, .read-more"),astra_responsive_font_size("astra-settings[font-size-post-tax]",".ast-blog-single-element.ast-taxonomy-container a"),astra_refresh_customizer("astra-settings[blog-meta-category-style]"),astra_refresh_customizer("astra-settings[blog-category-style]"),astra_refresh_customizer("astra-settings[blog-tag-style]"),astra_refresh_customizer("astra-settings[blog-post-meta-divider-type]"),astra_refresh_customizer("astra-settings[blog-meta-tag-style]"),astra_refresh_customizer("astra-settings[blog-post-content]"),wp.customize("astra-settings[post-card-border-radius]",function(t){t.bind(function(t){var e=t.desktop.top||0,o=t.desktop.bottom||0,s=t.desktop.left||0,a=t.desktop.right||0,n=t.tablet.top||0,r=t.tablet.bottom||0,i=t.tablet.left||0,l=t.tablet.right||0,u=t.mobile.top||0,c=t.mobile.bottom||0,d=t.mobile.left||0,b=t.mobile.right||0,p=astraBuilderPreview.tablet_break_point||921,m="blog-layout-5"===(void 0!==wp.customize._value["astra-settings[blog-layout]"]?wp.customize._value["astra-settings[blog-layout]"]._value:"blog-layout-1")?".archive .ast-article-post, .blog .ast-article-post, .archive .ast-article-post:hover, .blog .ast-article-post:hover":".archive .ast-article-post .ast-article-inner, .blog .ast-article-post .ast-article-inner, .archive .ast-article-post .ast-article-inner:hover, .blog .ast-article-post .ast-article-inner:hover",e=m+"{ border-top-left-radius :"+e+t["desktop-unit"]+"; border-bottom-right-radius :"+o+t["desktop-unit"]+"; border-bottom-left-radius :"+s+t["desktop-unit"]+"; border-top-right-radius :"+a+t["desktop-unit"]+"; } ";astra_add_dynamic_css("post-card-border-radius",e+("@media (max-width: "+p+"px) { "+m+"{ border-top-left-radius :"+n+t["tablet-unit"]+"; border-bottom-right-radius :"+r+t["tablet-unit"]+"; border-bottom-left-radius :"+i+t["tablet-unit"]+"; border-top-right-radius :"+l+t["tablet-unit"]+"; } } ")+("@media (max-width: "+(astraBuilderPreview.mobile_break_point||544)+"px) { "+m+"{ border-top-left-radius :"+u+t["mobile-unit"]+"; border-bottom-right-radius :"+c+t["mobile-unit"]+"; border-bottom-left-radius :"+d+t["mobile-unit"]+"; border-top-right-radius :"+b+t["mobile-unit"]+"; } } "))})}),astra_css("astra-settings[post-card-featured-overlay]","background-color",".ast-blog-layout-6-grid .ast-article-inner .post-thumb::after"),1==astraCustomizer.includeAnchorsInHeadindsCss?(astra_responsive_font_size("astra-settings[font-size-h1]","h1, .entry-content h1, .entry-content h1 a",!0,'.ast-sg-typo-field[data-for="h1"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h2]","h2, .entry-content h2, .entry-content h2 a",!0,'.ast-sg-typo-field[data-for="h2"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h3]","h3, .entry-content h3, .entry-content h3 a",!0,'.ast-sg-typo-field[data-for="h3"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h4]","h4, .entry-content h4, .entry-content h4 a",!0,'.ast-sg-typo-field[data-for="h4"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h5]","h5, .entry-content h5, .entry-content h5 a",!0,'.ast-sg-typo-field[data-for="h5"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h6]","h6, .entry-content h6, .entry-content h6 a",!0,'.ast-sg-typo-field[data-for="h6"] .ast-sg-font-size')):(astra_responsive_font_size("astra-settings[font-size-h1]","h1, .entry-content h1",!0,'.ast-sg-typo-field[data-for="h1"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h2]","h2, .entry-content h2",!0,'.ast-sg-typo-field[data-for="h2"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h3]","h3, .entry-content h3",!0,'.ast-sg-typo-field[data-for="h3"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h4]","h4, .entry-content h4",!0,'.ast-sg-typo-field[data-for="h4"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h5]","h5, .entry-content h5",!0,'.ast-sg-typo-field[data-for="h5"] .ast-sg-font-size'),astra_responsive_font_size("astra-settings[font-size-h6]","h6, .entry-content h6",!0,'.ast-sg-typo-field[data-for="h6"] .ast-sg-font-size')),wp.customize("astra-settings[para-margin-bottom]",function(t){t.bind(function(t){""==t&&wp.customize.preview.send("refresh"),t&&astra_add_dynamic_css("para-margin-bottom"," p, .entry-content p { margin-bottom: "+t+"em; } ")})}),wp.customize("astra-settings[underline-content-links]",function(t){t.bind(function(t){astra_add_dynamic_css("underline-content-links",t?".ast-single-post .entry-content a, .ast-comment-content a:not(.ast-comment-edit-reply-wrap a), .woocommerce-js .woocommerce-product-details__short-description a { text-decoration: underline; } ":".ast-single-post .entry-content a, .ast-comment-content a:not(.ast-comment-edit-reply-wrap a), .woocommerce-js .woocommerce-product-details__short-description a { text-decoration: unset; } ")})}),astraCustomizer.page_builder_button_style_css?1==astraCustomizer.includeAnchorsInHeadindsCss?("color-typo"!=astraCustomizer.elementor_default_color_font_setting&&"typo"!=astraCustomizer.elementor_default_color_font_setting||astra_css("astra-settings[headings-line-height]","line-height",".elementor-widget-heading h1.elementor-heading-title, .elementor-widget-heading h2.elementor-heading-title, .elementor-widget-heading h3.elementor-heading-title, .elementor-widget-heading h4.elementor-heading-title, .elementor-widget-heading h5.elementor-heading-title, .elementor-widget-heading h6.elementor-heading-title"),astra_css("astra-settings[headings-line-height]","line-height","h1, .entry-content h1, .entry-content h1 a, h2, .entry-content h2, .entry-content h2 a, h3, .entry-content h3, .entry-content h3 a, h4, .entry-content h4, .entry-content h4 a, h5, .entry-content h5, .entry-content h5 a, h6, .entry-content h6, .entry-content h6 a, .site-title, .site-title a")):("color-typo"!=astraCustomizer.elementor_default_color_font_setting&&"typo"!=astraCustomizer.elementor_default_color_font_setting||astra_css("astra-settings[headings-line-height]","line-height",".elementor-widget-heading h1.elementor-heading-title, .elementor-widget-heading h2.elementor-heading-title, .elementor-widget-heading h3.elementor-heading-title, .elementor-widget-heading h4.elementor-heading-title, .elementor-widget-heading h5.elementor-heading-title, .elementor-widget-heading h6.elementor-heading-title"),astra_css("astra-settings[headings-line-height]","line-height","h1, .entry-content h1, h2, .entry-content h2, h3, .entry-content h3, h4, .entry-content h4, h5, .entry-content h5, h6, .entry-content h6, .site-title, .site-title a")):1==astraCustomizer.includeAnchorsInHeadindsCss?astra_css("astra-settings[headings-line-height]","line-height","h1, .entry-content h1, .entry-content h1 a, h2, .entry-content h2, .entry-content h2 a, h3, .entry-content h3, .entry-content h3 a, h4, .entry-content h4, .entry-content h4 a, h5, .entry-content h5, .entry-content h5 a, h6, .entry-content h6, .entry-content h6 a, .site-title, .site-title a"):astra_css("astra-settings[headings-line-height]","line-height","h1, .entry-content h1, h2, .entry-content h2, h3, .entry-content h3, h4, .entry-content h4, h5, .entry-content h5, h6, .entry-content h6, .site-title, .site-title a"),1==astraCustomizer.includeAnchorsInHeadindsCss?(astra_generate_outside_font_family_css("astra-settings[headings-font-family]","h1, .entry-content h1, .entry-content h1 a, h2, .entry-content h2, .entry-content h2 a, h3, .entry-content h3, .entry-content h3 a, h4, .entry-content h4, .entry-content h4 a, h5, .entry-content h5, .entry-content h5 a, h6, .entry-content h6, .entry-content h6 a, .site-title, .site-title a"),astra_generate_font_weight_css("astra-settings[headings-font-family]","astra-settings[headings-font-weight]","font-weight","h1, .entry-content h1, .entry-content h1 a, h2, .entry-content h2, .entry-content h2 a, h3, .entry-content h3, .entry-content h3 a, h4, .entry-content h4, .entry-content h4 a, h5, .entry-content h5, .entry-content h5 a, h6, .entry-content h6, .entry-content h6 a, .site-title, .site-title a"),astra_font_extras_css("headings-font-extras",".entry-content h1, .entry-content h1 a, h2, .entry-content h2, .entry-content h2 a, h3, .entry-content h3, .entry-content h3 a, h4, .entry-content h4, .entry-content h4 a, h5, .entry-content h5, .entry-content h5 a, h6, .entry-content h6, .entry-content h6 a, .site-title, .site-title a")):(astra_generate_outside_font_family_css("astra-settings[headings-font-family]","h1, .entry-content h1, h2, .entry-content h2, h3, .entry-content h3, h4, .entry-content h4, h5, .entry-content h5, h6, .entry-content h6, .site-title, .site-title a"),astra_generate_font_weight_css("astra-settings[headings-font-family]","astra-settings[headings-font-weight]","font-weight","h1, .entry-content h1, h2, .entry-content h2, h3, .entry-content h3, h4, .entry-content h4, h5, .entry-content h5, h6, .entry-content h6, .site-title, .site-title a"),astra_font_extras_css("headings-font-extras",".entry-content h1, h2, .entry-content h2, h3, .entry-content h3, h4, .entry-content h4, h5, .entry-content h5, h6, .entry-content h6, .site-title, .site-title a")),astra_css("astra-settings[link-color]","color",".entry-meta, .entry-meta *"),astra_css("astra-settings[link-h-color]","color",".read-more a:hover, .entry-meta a:hover, .entry-meta a:hover *"),astra_css("astra-settings[footer-color]","color",".ast-small-footer"),astra_css("astra-settings[footer-link-color]","color",".ast-small-footer a"),astra_css("astra-settings[footer-link-h-color]","color",".ast-small-footer a:hover"),wp.customize("astra-settings[footer-bg-obj]",function(t){t.bind(function(t){astra_background_obj_css(wp.customize,t,"footer-bg-obj"," .ast-small-footer > .ast-footer-overlay { {{css}} }")})}),astra_css("astra-settings[footer-adv-wgt-title-color]","color",".footer-adv .widget-title, .footer-adv .widget-title a"),astra_css("astra-settings[footer-adv-text-color]","color",".footer-adv"),astra_css("astra-settings[footer-adv-link-color]","color",".footer-adv a"),astra_css("astra-settings[footer-adv-link-h-color]","color",".footer-adv a:hover, .footer-adv .no-widget-text a:hover, .footer-adv a:focus, .footer-adv .no-widget-text a:focus"),wp.customize("astra-settings[footer-adv-bg-obj]",function(t){t.bind(function(t){astra_background_obj_css(wp.customize,t,"footer-adv-bg-obj"," .footer-adv-overlay { {{css}} }")})}),wp.customize("astra-settings[shop-archive-max-width]",function(t){t.bind(function(t){var e="@media all and ( min-width: 921px ) {";jQuery("body").hasClass("ast-page-builder-template")||(e+=".ast-woo-shop-archive .site-content > .ast-container{ max-width: "+parseInt(t)+"px } "),jQuery("body").hasClass("ast-fluid-width-layout")&&(e+=".ast-woo-shop-archive .site-content > .ast-container{ padding-left:20px; padding-right:20px; } "),astra_add_dynamic_css("shop-archive-max-width",e+="}")})}),wp.customize("astra-settings[mobile-header-toggle-btn-style]",function(t){t.bind(function(t){astra_add_dynamic_css("mobile-header-toggle-btn-style",""===wp.customize("astra-settings[mobile-header-toggle-btn-color]").get()&&"fill"===t?a=' [data-section="section-header-mobile-trigger"] .ast-button-wrap .mobile-menu-toggle-icon .ast-mobile-svg { fill: #ffffff; } ':"");var e=n(document).find(".ast-mobile-menu-buttons .menu-toggle"),e=(e.removeClass("ast-mobile-menu-buttons-default ast-mobile-menu-buttons-fill ast-mobile-menu-buttons-outline"),e.removeClass("ast-mobile-menu-buttons-default ast-mobile-menu-buttons-fill ast-mobile-menu-buttons-minimal"),e.addClass("ast-mobile-menu-buttons-"+t),wp.customize("astra-settings[theme-color]").get()),o="#ffffff",s=wp.customize("astra-settings[mobile-header-toggle-btn-color]").get(),t=o="fill"!==t?e:o,a='[data-section="section-header-mobile-trigger"] .ast-button-wrap .mobile-menu-toggle-icon .ast-mobile-svg {';astra_add_dynamic_css("mobile-header-toggle-btn-style",a=(a=(a=(a=(a=(a=a+("fill: "+(t=""!==s&&null!=s?s:t)+";")+"}")+'[data-section="section-header-mobile-trigger"] .ast-button-wrap .mobile-menu-wrap .mobile-menu {'+("color: "+t+";"))+"}"+'[data-section="section-header-mobile-trigger"] .ast-button-wrap .ast-mobile-menu-trigger-fill, [data-section="section-header-mobile-trigger"] .ast-button-wrap .ast-mobile-menu-trigger-minimal {')+("color: "+t+";")+"border: none;")+"}"+'[data-section="section-header-mobile-trigger"] .ast-button-wrap .ast-mobile-menu-trigger-outline {')+("color: "+t+";")+"}")})}),wp.customize("astra-settings[mobile-header-toggle-btn-border-radius]",function(t){t.bind(function(t){astra_add_dynamic_css("mobile-header-toggle-btn-border-radius",".ast-header-break-point .main-header-bar .ast-button-wrap .menu-toggle { border-radius: "+parseInt(t)+"px } ")})}),wp.customize("astra-settings[primary-submenu-border]",function(t){t.bind(function(t){var e=wp.customize("astra-settings[primary-submenu-b-color]").get();""!=t.top||""!=t.right||""!=t.bottom||""!=t.left?astra_add_dynamic_css("primary-submenu-border",".ast-desktop .main-header-menu.submenu-with-border .sub-menu"+"{"+("border-top-width:"+t.top+"px;")+("border-right-width:"+t.right+"px;")+("border-left-width:"+t.left+"px;")+("border-bottom-width:"+t.bottom+"px;")+("border-color:"+e+";")+"border-style: solid;"+"}"+".ast-desktop .main-header-menu.submenu-with-border .sub-menu .sub-menu"+"{"+("top:-"+t.top+"px;")+"}"+"@media (min-width: 769px){"+".main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu:hover > .sub-menu, .main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu.focus > .sub-menu"+"{"+("margin-left:-"+(+t.left+ +t.right)+"px;")+"}"+"}"):wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[primary-submenu-b-color]",function(t){t.bind(function(t){var e=wp.customize("astra-settings[primary-submenu-border]").get();""!=t?""==e.top&&""==e.right&&""==e.bottom&&""==e.left||astra_add_dynamic_css("primary-submenu-border-color",".ast-desktop .main-header-menu.submenu-with-border .sub-menu"+"{"+("border-top-width:"+e.top+"px;")+("border-right-width:"+e.right+"px;")+("border-left-width:"+e.left+"px;")+("border-bottom-width:"+e.bottom+"px;")+("border-color:"+t+";")+"border-style: solid;"+"}"+".ast-desktop .main-header-menu.submenu-with-border .sub-menu .sub-menu"+"{"+("top:-"+e.top+"px;")+"}"+"@media (min-width: 769px){"+".main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu:hover > .sub-menu, .main-header-menu .sub-menu .menu-item.ast-left-align-sub-menu.focus > .sub-menu"+"{"+("margin-left:-"+(+e.left+ +e.right)+"px;")+"}"+"}"):wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[primary-submenu-item-b-color]",function(t){t.bind(function(t){var e=wp.customize("astra-settings[primary-submenu-item-border]").get();""!=t?1==e&&astra_add_dynamic_css("primary-submenu-item-b-color",""+".ast-desktop .main-header-menu.submenu-with-border .sub-menu .menu-link"+"{"+("border-bottom-width:"+(!0===e?"1px;":"0px;"))+("border-color:"+t+";")+"border-style: solid;"+"}"):wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[primary-submenu-item-border]",function(t){t.bind(function(t){var e,o=wp.customize("astra-settings[primary-submenu-item-b-color]").get();!0===t?(e=".ast-desktop .main-header-menu.submenu-with-border .sub-menu .menu-link",astra_add_dynamic_css("primary-submenu-item-border",e=(e=(e+="{")+("border-bottom-width:"+(!0===t?"1px;":"0px;"))+("border-color:"+o+";"))+"border-style: solid;"+"}")):wp.customize.preview.send("refresh")})}),astra_css("astra-settings[header-main-rt-section-button-text-color]","color",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[header-main-rt-section-button-back-color]","background-color",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[header-main-rt-section-button-text-h-color]","color",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button:hover"),astra_css("astra-settings[header-main-rt-section-button-back-h-color]","background-color",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button:hover"),astra_css("astra-settings[header-main-rt-section-button-border-radius]","border-radius",".main-header-bar .ast-container .button-custom-menu-item .ast-custom-button-link .ast-custom-button","px"),astra_css("astra-settings[header-main-rt-section-button-border-color]","border-color",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[header-main-rt-section-button-border-h-color]","border-color",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button:hover"),astra_responsive_spacing("astra-settings[header-main-rt-section-button-padding]",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button","padding",["top","right","bottom","left"]),astra_generate_outside_font_family_css("astra-settings[primary-header-button-font-family]",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button, .ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_responsive_font_size("astra-settings[primary-header-button-font-size]",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button, .ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[primary-header-button-font-weight]","font-weight",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button, .ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[primary-header-button-line-height]","line-height",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button, .ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[primary-header-button-text-transform]","text-transform",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button, .ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[primary-header-button-letter-spacing]","letter-spacing",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button, .ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button","px"),wp.customize("astra-settings[header-main-rt-section-button-border-size]",function(t){t.bind(function(t){""==t.top&&""==t.right&&""==t.bottom&&""==t.left||astra_add_dynamic_css("header-main-rt-section-button-border-size",".main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"+"{"+("border-top-width:"+t.top+"px;")+("border-right-width:"+t.right+"px;")+("border-left-width:"+t.left+"px;")+("border-bottom-width:"+t.bottom+"px;")+"border-style: solid;"+"}")})}),""),g="",t="",h="",_=(astraCustomizer.v4_2_2_core_form_btns_styling&&""!=astraCustomizer.v4_2_2_core_form_btns_styling&&(t=", #comment .submit:hover, .search .search-submit:hover",h=", #comment .submit:focus, .search .search-submit:focus"),!astraCustomizer.page_builder_button_style_css||"color-typo"!=astraCustomizer.elementor_default_color_font_setting&&"color"!=astraCustomizer.elementor_default_color_font_setting&&"font"!=astraCustomizer.elementor_default_color_font_setting||(m=", .elementor-widget-button .elementor-button, .elementor-widget-button .elementor-button:visited",g=", .elementor-widget-button .elementor-button.elementor-size-sm, .elementor-widget-button .elementor-button.elementor-size-xs, .elementor-widget-button .elementor-button.elementor-size-md, .elementor-widget-button .elementor-button.elementor-size-lg, .elementor-widget-button .elementor-button.elementor-size-xl, .elementor-widget-button .elementor-button"),wp.customize("astra-settings[theme-button-border-group-border-size]",function(t){t.bind(function(t){var e=hasWordPressWidgetBlockEditor()?', form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button, .woocommerce a.button':"",e='.menu-toggle, button, .ast-button, .ast-custom-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .wp-block-button .wp-block-button__link'+astraCustomizer.v4_2_2_core_form_btns_styling+m+e+r+i;""!=t.top||""!=t.right||""!=t.bottom||""!=t.left?astraCustomizer.gb_outline_buttons_patterns_support&&!astraCustomizer.updated_gb_outline_button_patterns?wp.customize.preview.send("refresh"):astra_add_dynamic_css("theme-button-border-group-border-size",e=(e=(e=(e=e+"{"+("border-top-width:"+t.top+"px;"))+("border-right-width:"+t.right+"px;"))+("border-left-width:"+t.left+"px;")+("border-bottom-width:"+t.bottom+"px;"))+"border-style: solid;"+"}"):astraCustomizer.gb_outline_buttons_patterns_support&&!astraCustomizer.updated_gb_outline_button_patterns?wp.customize.preview.send("refresh"):astra_add_dynamic_css("theme-button-border-group-border-size",e=(e=(e=e+"{"+"border-top-width: 0;")+"border-right-width: 0;"+"border-left-width: 0;")+"border-bottom-width: 0;"+"}")})}),wp.customize("astra-settings[secondary-theme-button-border-group-border-size]",function(t){t.bind(function(t){var e=".wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button, .ast-outline-button, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button";""!=t.top||""!=t.right||""!=t.bottom||""!=t.left?astraCustomizer.gb_outline_buttons_patterns_support&&!astraCustomizer.updated_gb_outline_button_patterns?wp.customize.preview.send("refresh"):astra_add_dynamic_css("secondary-theme-button-border-group-border-size",e=(e=(e=(e=e+"{"+("border-top-width:"+t.top+"px;"))+("border-right-width:"+t.right+"px;"))+("border-left-width:"+t.left+"px;")+("border-bottom-width:"+t.bottom+"px;"))+"border-style: solid;"+"}"):astraCustomizer.gb_outline_buttons_patterns_support&&!astraCustomizer.updated_gb_outline_button_patterns?wp.customize.preview.send("refresh"):astra_add_dynamic_css("secondary-theme-button-border-group-border-size",e=(e=(e=e+"{"+"border-top-width: 0;")+"border-right-width: 0;"+"border-left-width: 0;")+"border-bottom-width: 0;"+"}")})}),hasWordPressWidgetBlockEditor()?', form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button':""),g=(astra_responsive_spacing("astra-settings[theme-button-padding]",'.menu-toggle, button, .ast-button, .ast-custom-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .woocommerce a.button, .woocommerce button.button, .woocommerce .product a.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled], .wp-block-button .wp-block-button__link'+astraCustomizer.v4_2_2_core_form_btns_styling+g+_+i,"padding",["top","bottom"]),astra_responsive_spacing("astra-settings[theme-button-padding]",'.menu-toggle, button, .ast-button, .ast-custom-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .woocommerce a.button, .woocommerce button.button, .woocommerce .product a.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled], .wp-block-button .wp-block-button__link'+astraCustomizer.v4_2_2_core_form_btns_styling+g+_+i,"padding",["left","right"]),astra_responsive_spacing("astra-settings[secondary-theme-button-padding]",".wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button, .ast-outline-button, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button","padding",["top","bottom"]),astra_responsive_spacing("astra-settings[secondary-theme-button-padding]",".wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button, .ast-outline-button, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button","padding",["left","right"]),wp.customize("astra-settings[transparent-header-button-border-group]",function(t){t.bind(function(t){t=JSON.parse(t)["header-main-rt-trans-section-button-border-size"];""==t.top&&""==t.right&&""==t.bottom&&""==t.left||astra_add_dynamic_css("header-main-rt-trans-section-button-border-size",".ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"+"{"+("border-top-width:"+t.top+"px;")+("border-right-width:"+t.right+"px;")+("border-left-width:"+t.left+"px;")+("border-bottom-width:"+t.bottom+"px;")+"border-style: solid;"+"}")})}),astra_generate_outside_font_family_css("astra-settings[font-family-site-title]",".site-title, .site-title a"),astra_generate_font_weight_css("astra-settings[font-family-site-title]","astra-settings[font-weight-site-title]","font-weight",".site-title, .site-title a"),astra_responsive_font_size("astra-settings[font-size-site-title]",".site-title, .site-title a"),astra_font_extras_css("font-extras-site-title",".site-title, .site-title a"),astra_generate_outside_font_family_css("astra-settings[font-family-site-tagline]",".ast-sg-logo-section .site-description, .site-header .site-description"),astra_generate_font_weight_css("astra-settings[font-family-site-tagline]","astra-settings[font-weight-site-tagline]","font-weight",".ast-sg-logo-section .site-description, .site-header .site-description"),astra_responsive_font_size("astra-settings[font-size-site-tagline]",".ast-sg-logo-section .site-description, .site-header .site-description"),astra_css("astra-settings[line-height-site-tagline]","line-height",".ast-sg-logo-section .site-description, .site-header .site-description"),astra_css("astra-settings[text-transform-site-tagline]","text-transform",".ast-sg-logo-section .site-description, .site-header .site-description"),_=hasWordPressWidgetBlockEditor()?', form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button, .woocommerce a.button':r,hasWordPressWidgetBlockEditor()?', form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button:hover, form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button:focus, .woocommerce a.button:hover':o),y=", .wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link:hover, .wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link:focus, .wp-block-buttons .wp-block-button.is-style-outline > .wp-block-button__link:not(.has-text-color):hover, .wp-block-buttons .wp-block-button.wp-block-button__link.is-style-outline:not(.has-text-color):hover";function f(a){wp.customize("astra-settings[button-preset-style]",function(t){t.bind(function(t){var e=wp.customize("astra-settings[button-bg-color]").get(),o=wp.customize("astra-settings[button-color]").get(),s=wp.customize("astra-settings[theme-color]").get();"button_04"===t||"button_05"===t||"button_06"===t?(""===wp.customize("astra-settings[theme-button-border-group-border-color]").get()&&(jQuery("style#astra-settings-theme-button-border-group-border-color").remove(),jQuery("head").append('<style id="astra-settings-theme-button-border-group-border-color">'+a+"\t{ border-color: "+e+" }</style>")),""===o&&""!==e&&(jQuery("style#astra-settings-button-outline-preset-color").remove(),jQuery("head").append('<style id="astra-settings-button-outline-preset-color">'+a+"\t{ color: "+e+" }</style>")),""===o&&""===e&&(jQuery("style#astra-settings-button-outline-preset-color").remove(),jQuery("head").append('<style id="astra-settings-button-outline-preset-color">'+a+"\t{ color: "+s+" }</style>")),jQuery("style#astra-settings-button-preset-style-background-color").remove(),jQuery("head").append('<style id="astra-settings-button-preset-style-background-color">'+a+"\t{ background: transparent }</style>")):(jQuery("style#astra-settings-button-bg-color-background-color").remove(),jQuery("style#astra-settings-button-outline-preset-color").remove(),""===o&&""===e?jQuery("head").append('<style id="astra-settings-button-bg-color-background-color">'+a+"\t{ background-color: "+s+" }</style>"):(e=""!=e?e:s,jQuery("head").append('<style id="astra-settings-button-bg-color-background-color">'+a+"\t{ background-color: "+e+" }</style>")),""===o&&(jQuery("head").append('<style id="astra-settings-button-outline-preset-color">'+a+"\t{ color: #FFFFFF; }</style>"),jQuery("style#astra-settings-button-color-color").remove()))})}),wp.customize("astra-settings[secondary-button-preset-style]",function(t){var a=".wp-block-buttons .wp-block-button .wp-block-button__link.is-style-outline:not(.has-background), .wp-block-buttons .wp-block-button.is-style-outline>.wp-block-button__link:not(.has-background), .ast-outline-button";t.bind(function(t){var e=wp.customize("astra-settings[secondary-button-bg-color]").get(),o=wp.customize("astra-settings[secondary-button-color]").get(),s=wp.customize("astra-settings[theme-color]").get();"button_04"===t||"button_05"===t||"button_06"===t?(""===wp.customize("astra-settings[secondary-theme-button-border-group-border-color]").get()&&(jQuery("style#astra-settings-secondary-theme-button-border-group-border-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-theme-button-border-group-border-color">'+c+"\t{ border-color: "+e+" }</style>")),""===o&&""!==e&&(jQuery("style#astra-settings-secondary-button-outline-preset-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-button-outline-preset-color">'+d+"\t{ color: "+e+" }</style>")),""===o&&""===e&&(jQuery("style#astra-settings-secondary-button-outline-preset-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-button-outline-preset-color">'+d+"\t{ color: "+s+" }</style>")),jQuery("style#astra-settings-secondary-button-preset-style-background-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-button-preset-style-background-color">'+a+"\t{ background: transparent }</style>")):(jQuery("style#astra-settings-secondary-button-bg-color-background-color").remove(),jQuery("style#astra-settings-secondary-button-outline-preset-color").remove(),""===o&&""===e?jQuery("head").append('<style id="astra-settings-secondary-button-bg-color-background-color">'+a+"\t{ background-color: "+s+" }</style>"):(e=""!=e?e:s,jQuery("head").append('<style id="astra-settings-secondary-button-bg-color-background-color">'+a+"\t{ background-color: "+e+" }</style>")),""===o&&(jQuery("head").append('<style id="astra-settings-secondary-button-outline-preset-color">'+d+"\t{ color: #FFFFFF; }</style>"),jQuery("style#astra-settings-secondary-button-color-color").remove()),""===t&&wp.customize.preview.send("refresh"))})}),wp.customize("astra-settings[button-color]",function(t){t.bind(function(t){var e;""===t?"button_04"===(e=wp.customize("astra-settings[button-preset-style]").get())||"button_05"===e||"button_06"===e?(e=wp.customize("astra-settings[button-bg-color]").get(),jQuery("style#astra-settings-button-outline-color").remove(),jQuery("head").append('<style id="astra-settings-button-color-color">'+a+"\t{ color: "+e+" }</style>")):(jQuery("style#astra-settings-button-color-color").remove(),jQuery("head").append('<style id="astra-settings-button-color-color">'+a+"\t{ color: #FFFFFF }</style>")):(jQuery("style#astra-settings-button-color-color").remove(),jQuery("head").append('<style id="astra-settings-button-color-color">'+a+"\t{ color: "+t+" }</style>"))})}),wp.customize("astra-settings[secondary-button-color]",function(t){t.bind(function(t){var e,o;""===t?(o=wp.customize("astra-settings[secondary-button-preset-style]").get(),e=wp.customize("astra-settings[theme-color]").get(),"button_04"===o||"button_05"===o||"button_06"===o?(o=wp.customize("astra-settings[secondary-button-bg-color]").get(),jQuery("style#astra-settings-secondary-button-color-color").remove(),jQuery("head").append('<style id="astra-settings-button-color-color">'+d+"\t{ color: "+o+" }</style>")):(jQuery("style#astra-settings-button-color-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-button-color-color">'+d+"\t{ color: "+e+" }</style>"))):(jQuery("style#astra-settings-secondary-button-color-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-button-color-color">'+d+"\t{ color: "+t+" }</style>"))})}),wp.customize("astra-settings[button-bg-color]",function(t){t.bind(function(t){var e=wp.customize("astra-settings[button-preset-style]").get(),o=wp.customize("astra-settings[theme-color]").get(),s=wp.customize("astra-settings[button-color]").get();"button_04"===e||"button_05"===e||"button_06"===e?(s=wp.customize("astra-settings[button-color]").get(),""===wp.customize("astra-settings[theme-button-border-group-border-color]").get()&&(jQuery("style#astra-settings-theme-button-border-group-border-color").remove(),jQuery("head").append('<style id="astra-settings-theme-button-border-group-border-color">'+a+"\t{ border-color: "+t+" }</style>")),""===s&&(jQuery("style#astra-settings-button-outline-preset-color").remove(),jQuery("head").append('<style id="astra-settings-button-outline-preset-color">'+a+"\t{ color: "+t+" }</style>"))):(jQuery("style#astra-settings-button-bg-color-background-color").remove(),jQuery("style#astra-settings-button-outline-preset-color").remove(),t=""!=t?t:o,""===s&&jQuery("head").append('<style id="astra-settings-button-outline-preset-color">'+a+"\t{ color: #FFFFFF; }</style>"),jQuery("head").append('<style id="stra-settings-button-bg-color-background-color">'+a+"\t{ background-color: "+t+" }</style>"))})}),wp.customize("astra-settings[secondary-button-bg-color]",function(t){t.bind(function(t){var e=wp.customize("astra-settings[secondary-button-preset-style]").get(),o=wp.customize("astra-settings[theme-color]").get(),s=wp.customize("astra-settings[secondary-button-color]").get(),a=wp.customize("astra-settings[secondary-theme-button-border-group-border-color]").get();"button_04"===e||"button_05"===e||"button_06"===e?(""===a&&(jQuery("style#astra-settings-secondary-theme-button-border-group-border-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-theme-button-border-group-border-color">'+c+"\t{ border-color: "+t+" }</style>")),""===s&&(jQuery("style#astra-settings-secondary-button-outline-preset-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-button-outline-preset-color">'+d+"\t{ color: "+t+" }</style>")),jQuery("style#astra-settings-secondary-button-bg-color-background-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-button-bg-color-background-color">'+c+"\t{ background-color: transparent }</style>")):"button_01"===e||"button_02"===e||"button_03"===e?(jQuery("style#astra-settings-secondary-button-outline-preset-color").remove(),jQuery("style#astra-settings-secondary-button-bg-color-background-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-button-bg-color-background-color">'+c+"\t{ background-color: "+t+" }</style>"),t=""!=t?t:o,""===s&&jQuery("head").append('<style id="astra-settings-secondary-button-outline-preset-color">'+d+"\t{ color: #FFFFFF; }</style>")):(""===a&&jQuery("head").append('<style id="astra-settings-secondary-theme-button-border-group-border-color">'+c+"\t{ border-color: "+t+" }</style>"),jQuery("style#astra-settings-secondary-button-bg-color-background-color").remove(),jQuery("head").append('<style id="astra-settings-secondary-button-bg-color-background-color">'+c+"\t{ background-color: "+t+" }</style>"))})}),wp.customize("astra-settings[woo-header-cart-product-count-color]",function(t){t.bind(function(t){t?astra_add_dynamic_css("woo-header-cart-product-count-color",".ast-menu-cart-outline .ast-cart-menu-wrap .count, .ast-site-header-cart .ast-addon-cart-wrap i.astra-icon:after { color: "+t+"; } "):wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[woo-header-cart-product-count-h-color]",function(t){t.bind(function(t){t?astra_add_dynamic_css("woo-header-cart-product-count-h-color",".ast-site-header-cart .ast-site-header-cart-li:hover .ast-cart-menu-wrap .count .ast-count-text, .ast-site-header-cart .ast-site-header-cart-li:hover .ast-addon-cart-wrap i.astra-icon:after { color: "+t+"; } "):wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[single-product-cart-button-width]",function(t){t.bind(function(t){var e,o=astraBuilderPreview.tablet_break_point||768;""!=t.desktop||""!=t.tablet||""!=t.mobile?(e="",astra_add_dynamic_css("header-woo-cart-icon-size",e=(e=(e=(e=(e=(e=(e+=".woocommerce div.product form.cart .button.single_add_to_cart_button {")+("width: "+t.desktop+"%;")+"} ")+("@media (max-width: "+o+"px) {")+".woocommerce div.product form.cart .button.single_add_to_cart_button {")+("width: "+t.tablet+"%;")+"} ")+"} "+("@media (max-width: "+(astraBuilderPreview.mobile_break_point||544)+"px) {"))+".woocommerce div.product form.cart .button.single_add_to_cart_button {"+("width: "+t.mobile+"%;"))+"} "+"} ")):wp.customize.preview.send("refresh")})});var t=".woocommerce .ast-sticky-add-to-cart .button.alt",e=".woocommerce .ast-sticky-add-to-cart .button.alt:hover";astra_css("astra-settings[single-product-sticky-add-to-cart-btn-n-color]","color",t),astra_css("astra-settings[single-product-sticky-add-to-cart-btn-h-color]","color",e),astra_css("astra-settings[single-product-sticky-add-to-cart-btn-bg-n-color]","background",t),astra_css("astra-settings[single-product-sticky-add-to-cart-btn-bg-h-color]","background",e),astra_css("astra-settings[single-product-sticky-add-to-cart-btn-bg-n-color]","border-color",t),astra_css("astra-settings[single-product-sticky-add-to-cart-btn-bg-h-color]","border-color",e),astra_css("astra-settings[single-product-sticky-add-to-cart-text-color]","color",".ast-sticky-add-to-cart .ast-container .ast-sticky-add-to-cart-content"),astra_css("astra-settings[single-product-sticky-add-to-cart-bg-color]","background-color",".ast-sticky-add-to-cart"),wp.customize("astra-settings[single-product-sticky-add-to-cart-position]",function(t){t.bind(function(t){var e="";astra_add_dynamic_css("sticky-add-to-cart-position",e+="top"===t?"div.ast-sticky-add-to-cart{top: 0;bottom: initial;transform: translate(0, -100%);box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.1), 0px 1px 9px rgba(0, 0, 0, 0.06);opacity: 0}":"div.ast-sticky-add-to-cart{bottom: 0;top: initial;transform: translate(0, 100%);box-shadow: 0px -1px 10px rgba(0, 0, 0, 0.1), 0px -1px 9px rgba(0, 0, 0, 0.06);opacity: 0}")})}),wp.customize("astra-settings[single-product-payment-icon-color]",function(t){t.bind(function(t){wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[shop-ratings-product-archive]",function(t){t.bind(function(t){wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[single-product-payment-text]",function(t){t.bind(function(t){var e=document.querySelector(".ast-single-product-payments legend");e&&(e.textContent=t)})}),wp.customize("astra-settings[single-product-payment-list]",function(t){t.bind(function(t){wp.customize.preview.send("refresh")})})}astraCustomizer.page_builder_button_style_css?(u=l=a=s=b="","color-typo"!=astraCustomizer.elementor_default_color_font_setting&&"color"!=astraCustomizer.elementor_default_color_font_setting||(b=",.elementor-widget-button .elementor-button, .elementor-widget-button .elementor-button:visited",l=", .elementor-widget-button .elementor-button, .elementor-widget-button .elementor-button:visited",u=a=s=",.elementor-widget-button .elementor-button:hover, .elementor-widget-button .elementor-button:focus"),b='.menu-toggle, button, .ast-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .wp-block-button:not(.is-style-outline) .wp-block-button__link, .ast-custom-button'+astraCustomizer.v4_2_2_core_form_btns_styling+b+_+r+i,c=".wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button, .ast-outline-button, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button",d=".wp-block-buttons .wp-block-button.is-style-outline > .wp-block-button__link.wp-element-button:not(.has-text-color), .wp-block-buttons .wp-block-button.wp-block-button__link.wp-element-button.is-style-outline:not(.has-text-color), .ast-outline-button, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button",f(b),astra_css("astra-settings[button-h-color]","color",'button:focus, .menu-toggle:hover, button:hover, .ast-button:hover, .button:hover, input[type=reset]:hover, input[type=reset]:focus, input#submit:hover, input#submit:focus, input[type="button"]:hover, input[type="button"]:focus, input[type="submit"]:hover, input[type="submit"]:focus, .wp-block-button .wp-block-button__link:hover, .wp-block-button .wp-block-button__link:focus, .ast-custom-button:hover, .ast-custom-button:focus, .wp-block-button .uagb-buttons-repeater.wp-block-button__link:hover'+t+s+g+y+p,"",!0),astra_css("astra-settings[secondary-button-h-color]","color",".wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link:hover, .wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link:focus, .wp-block-buttons .wp-block-button.is-style-outline > .wp-block-button__link:not(.has-text-color):hover, .wp-block-buttons .wp-block-button.wp-block-button__link.is-style-outline:not(.has-text-color):hover, .ast-outline:hover, .ast-outline:focus, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button:hover","",!0),astra_css("astra-settings[button-bg-h-color]","background-color",'button:focus, .menu-toggle:hover, button:hover, .ast-button:hover, .button:hover, input[type=reset]:hover, input[type=reset]:focus, input#submit:hover, input#submit:focus, input[type="button"]:hover, input[type="button"]:focus, input[type="submit"]:hover, input[type="submit"]:focus, .wp-block-button .wp-block-button__link:hover, .wp-block-button .wp-block-button__link:focus, .ast-custom-button:hover, .ast-custom-button:focus, .wp-block-button .uagb-buttons-repeater.wp-block-button__link:hover'+t+a+g+y+o+p),astra_css("astra-settings[secondary-button-bg-h-color]","background-color",".wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link:hover, .wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link:focus, .wp-block-buttons .wp-block-button.is-style-outline > .wp-block-button__link:not(.has-text-color):hover, .wp-block-buttons .wp-block-button.wp-block-button__link.is-style-outline:not(.has-text-color):hover, .ast-outline-button:hover, .ast-outline-button:focus, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button:hover"),astra_css("astra-settings[theme-button-border-group-border-color]","border-color",'.menu-toggle, button, .ast-button, .ast-custom-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .wp-block-button .wp-block-button__link'+astraCustomizer.v4_2_2_core_form_btns_styling+l+_+", .wp-block-buttons .wp-block-button.is-style-outline > .wp-block-button__link:not(.has-text-color), .wp-block-buttons .wp-block-button.wp-block-button__link.is-style-outline:not(.has-text-color)"+r+i),astra_css("astra-settings[secondary-theme-button-border-group-border-color]","border-color",".wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button, .ast-outline-button, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button"),astra_css("astra-settings[theme-button-border-group-border-h-color]","border-color",'button:focus, .menu-toggle:hover, button:hover, .ast-button:hover, .ast-custom-button:hover, .button:hover, input[type=reset]:hover, input[type=reset]:focus, input#submit:hover, input#submit:focus, input[type="button"]:hover, input[type="button"]:focus, input[type="submit"]:hover, input[type="submit"]:focus, .wp-block-button .wp-block-button__link:hover, .wp-block-button .wp-block-button__link:focus'+t+h+u+g+y+o+p),astra_css("astra-settings[secondary-theme-button-border-group-border-h-color]","border-color",".wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link:hover, .wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link:focus, .wp-block-buttons .wp-block-button.is-style-outline > .wp-block-button__link:not(.has-text-color):hover, .wp-block-buttons .wp-block-button.wp-block-button__link.is-style-outline:not(.has-text-color):hover, .ast-outline-button:hover, .ast-outline-button:focus, .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button:hover")):(f(b='.menu-toggle, button, .ast-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .ast-custom-button'+_+r+i),astra_css("astra-settings[button-bg-color]","border-color",'.menu-toggle, button, .ast-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .ast-custom-button'+_+i),astra_css("astra-settings[button-h-color]","color",'button:focus, .menu-toggle:hover, button:hover, .ast-button:hover, .button:hover, input[type=reset]:hover, input[type=reset]:focus, input#submit:hover, input#submit:focus, input[type="button"]:hover, input[type="button"]:focus, input[type="submit"]:hover, input[type="submit"]:focus, .ast-custom-button:hover, .ast-custom-button:focus'+g+p),astra_css("astra-settings[button-bg-h-color]","background-color",'button:focus, .menu-toggle:hover, button:hover, .ast-button:hover, .button:hover, input[type=reset]:hover, input[type=reset]:focus, input#submit:hover, input#submit:focus, input[type="button"]:hover, input[type="button"]:focus, input[type="submit"]:hover, input[type="submit"]:focus, .ast-custom-button:hover, .ast-custom-button:focus'+g+p),astra_responsive_spacing("astra-settings[theme-button-padding]",'.menu-toggle, button, .ast-button, .ast-custom-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .woocommerce a.button, .woocommerce button.button, .woocommerce .product a.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled]'+astraCustomizer.v4_2_2_core_form_btns_styling+_+i,"padding",["top","bottom"]),astra_responsive_spacing("astra-settings[theme-button-padding]",'.menu-toggle, button, .ast-button, .ast-custom-button, .button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .woocommerce a.button, .woocommerce button.button, .woocommerce .product a.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled]'+astraCustomizer.v4_2_2_core_form_btns_styling+_+i,"padding",["left","right"])),wp.customize.bind("preview-ready",function(){wp.customize.selectiveRefresh.bind("render-partials-response",function(t){if(t.contents.hasOwnProperty("astra-settings[footer-desktop-items]")||!t.contents.hasOwnProperty("astra-settings[header-desktop-items]")&&!t.contents.hasOwnProperty("astra-settings[header-mobile-items]"))return!1;setTimeout(function(){document.dispatchEvent(new CustomEvent("astLayoutWidthChanged",{detail:{response:t}}))},10)}),wp.customize.selectiveRefresh.bind("partial-content-rendered",function(t){if(t.partial.id.includes("footer"))return!1;sessionStorage.setItem("astPartialContentRendered",!0),document.dispatchEvent(new CustomEvent("astPartialContentRendered",{detail:{response:t}}))}),wp.customize.selectiveRefresh.bind("partial-content-rendered",function(t){wp.customize.preview.send("AstraBuilderPartialContentRendered",t)}),wp.customize.preview.bind("astPreviewDeviceChanged",function(t){document.dispatchEvent(new CustomEvent("astPreviewDeviceChanged",{detail:t}))})}),wp.customize("astra-settings[related-posts-based-on]",function(t){t.bind(function(){wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[related-posts-order-by]",function(t){t.bind(function(){wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[related-posts-order]",function(t){t.bind(function(){wp.customize.preview.send("refresh")})}),astra_css("astra-settings[related-posts-text-color]","color",".ast-related-post-content .entry-header .ast-related-post-title a, .ast-related-post-content .ast-related-post-excerpt"),astra_css("astra-settings[related-posts-meta-color]","color",".ast-related-post-content .entry-meta, .ast-related-post-content .entry-meta *"),astra_css("astra-settings[related-posts-title-color]","color",".ast-single-related-posts-container .ast-related-posts-title-section .ast-related-posts-title"),astra_css("astra-settings[related-posts-background-color]","background-color",".ast-single-related-posts-container"),astra_css("astra-settings[related-posts-link-color]","color",".ast-related-post-content .ast-related-post-cta a"),astra_css("astra-settings[related-posts-link-hover-color]","color",".ast-related-post-content .ast-related-post-cta a:hover"),astra_css("astra-settings[related-posts-meta-link-hover-color]","color",".ast-related-post-content .entry-meta a:hover, .ast-related-post-content .entry-meta span a span:hover"),astra_generate_outside_font_family_css("astra-settings[related-posts-title-font-family]",".ast-related-post-content .entry-header .ast-related-post-title, .ast-related-post-content .entry-header .ast-related-post-title a"),astra_css("astra-settings[related-posts-title-font-weight]","font-weight",".ast-related-post-content .entry-header .ast-related-post-title, .ast-related-post-content .entry-header .ast-related-post-title a"),astra_responsive_font_size("astra-settings[related-posts-title-font-size]",".ast-related-post-content .entry-header .ast-related-post-title, .ast-related-post-content .entry-header .ast-related-post-title a"),astra_font_extras_css("related-posts-title-font-extras",".ast-related-post-content .entry-header .ast-related-post-title, .ast-related-post-content .entry-header .ast-related-post-title a"),astra_generate_outside_font_family_css("astra-settings[related-posts-section-title-font-family]",".ast-single-related-posts-container .ast-related-posts-title-section .ast-related-posts-title"),astra_css("astra-settings[related-posts-section-title-font-weight]","font-weight",".ast-single-related-posts-container .ast-related-posts-title-section .ast-related-posts-title"),astra_responsive_font_size("astra-settings[related-posts-section-title-font-size]",".ast-single-related-posts-container .ast-related-posts-title-section .ast-related-posts-title"),astra_css("astra-settings[releted-posts-title-alignment]","text-align",".ast-single-related-posts-container .ast-related-posts-title-section .ast-related-posts-title"),astra_font_extras_css("related-posts-section-title-font-extras",".ast-single-related-posts-container .ast-related-posts-title-section .ast-related-posts-title"),astra_generate_outside_font_family_css("astra-settings[related-posts-meta-font-family]",".ast-related-post-content .entry-meta, .ast-related-post-content .entry-meta *"),astra_css("astra-settings[related-posts-meta-font-weight]","font-weight",".ast-related-post-content .entry-meta, .ast-related-post-content .entry-meta *"),astra_responsive_font_size("astra-settings[related-posts-meta-font-size]",".ast-related-post-content .entry-meta, .ast-related-post-content .entry-meta *"),astra_font_extras_css("related-posts-meta-font-extras",".ast-related-post-content .entry-meta, .ast-related-post-content .entry-meta *"),astra_generate_outside_font_family_css("astra-settings[related-posts-content-font-family]",".ast-related-post-content .ast-related-post-excerpt"),astra_css("astra-settings[related-posts-content-font-weight]","font-weight",".ast-related-post-content .ast-related-post-excerpt"),astra_responsive_font_size("astra-settings[related-posts-content-font-size]",".ast-related-post-content .ast-related-post-excerpt"),astra_font_extras_css("related-posts-content-font-extras",".ast-related-post-content .ast-related-post-excerpt"),astra_border_spacing_advanced_css("ast-sub-section-related-posts","body .site .ast-single-related-posts-container"),astra_border_spacing_advanced_css("ast-sub-section-comments","body .site .comments-area"),astra_css("astra-settings[header-color-site-title]","color",".ast-site-identity .site-title a, .ast-site-identity .site-title"),astra_css("astra-settings[header-color-h-site-title]","color",".ast-site-identity .site-title a:hover, .ast-site-identity .site-title:hover"),astra_css("astra-settings[logo-svg-icon-color]","fill",".site-branding .ast-logo-svg-icon svg"),astra_css("astra-settings[logo-svg-icon-hover-color]","fill",".site-branding .ast-logo-svg-icon:hover svg"),astra_css("astra-settings[header-color-site-tagline]","color",".ast-site-identity .site-description"),wp.customize("astra-settings[related-posts-author-avatar-size]",function(t){t.bind(function(t){var e="";astra_add_dynamic_css("related-posts-author-avatar-size",e+".ast-related-post-content .ast-author-avatar img {"+("width: "+t+"px;")+("height: "+t+"px;")+"} ")})}),astra_generate_outside_font_family_css("astra-settings[body-font-family]","body, button, input, select, textarea, .ast-button, .ast-custom-button"),astra_generate_font_weight_css("astra-settings[body-font-family]","astra-settings[body-font-weight]","font-weight","body, button, input, select, textarea, .ast-button, .ast-custom-button"),astra_font_extras_css("body-font-extras","body, button, input, select, textarea, .ast-button, .ast-custom-button"),wp.customize("astra-settings[font-size-body]",function(t){t.bind(function(t){var e,o,s,a,n,r;t.desktop||t.mobile||t.tablet?(o="body, button, input, select, textarea, .ast-button, .ast-custom-button",a=s=e="",n="font-size",r=(r=(r="astra-settings[font-size-body]").replace("[","-")).replace("]",""),jQuery("style#"+r+"-"+n).remove(),""!=t.desktop&&(e="font-size: "+t.desktop+(null==t["desktop-unit"]?"px":t["desktop-unit"])),""!=t.tablet&&(s="font-size: "+t.tablet+(null==t["tablet-unit"]?"px":t["tablet-unit"])),""!=t.mobile&&(a="font-size: "+t.mobile+(null==t["mobile-unit"]?"px":t["mobile-unit"])),jQuery("head").append('<style id="'+r+"-"+n+'">'+o+"\t{ "+e+" }@media (max-width: 768px) {.ast-header-break-point "+o+" { "+s+" } }@media (max-width: 544px) {.ast-header-break-point "+o+" { "+a+" } }</style>")):jQuery("style#"+r+"-font-size").remove()})}),astra_refresh_customizer("astra-settings[related-metadata-separator]"),astra_refresh_customizer("astra-settings[related-posts-image-ratio-type]"),astra_refresh_customizer("astra-settings[related-posts-image-ratio-pre-scale]"),astra_refresh_customizer("astra-settings[related-posts-image-custom-scale-width]"),astra_refresh_customizer("astra-settings[related-posts-image-custom-scale-height]"),astra_refresh_customizer("astra-settings[related-posts-image-size]"),astra_refresh_customizer("astra-settings[related-posts-author-prefix-label]"),astra_refresh_customizer("astra-settings[related-posts-author-avatar]"),astra_refresh_customizer("astra-settings[related-posts-meta-date-type]"),astra_refresh_customizer("astra-settings[related-posts-date-format]"),astra_refresh_customizer("astra-settings[related-posts-category-style]"),astra_refresh_customizer("astra-settings[related-posts-tag-style]")})(jQuery);