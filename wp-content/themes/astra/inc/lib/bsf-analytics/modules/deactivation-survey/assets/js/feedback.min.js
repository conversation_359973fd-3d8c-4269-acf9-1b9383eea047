(i=>{let t={slug:"",skipButton:"",formWrapper:"",radioButton:"",closeButton:"",buttonAction:"",feedbackForm:"",feedbackInput:"",deactivateUrl:"",buttonTrigger:"",deactivateButton:"",submitDeactivate:"",_cacheElements(){this.slug=udsVars?._plugin_slug||"",this.skipButton=i(".uds-feedback-skip"),this.submitDeactivate=i(".uds-feedback-submit"),this.deactivateButton=i("#the-list").find(".row-actions span.deactivate a"),this.feedbackForm=i(".uds-feedback-form"),this.feedbackInput=i(".uds-options-feedback"),this.formWrapper=i(".uds-feedback-form--wrapper"),this.closeButton=i(".uds-feedback-form--wrapper .uds-close"),this.radioButton=i(".uds-reason-input")},_showPopup(t){i("#deactivation-survey-"+t).addClass("show_popup")},_hidePopup(){this.formWrapper.removeClass("show_popup")},_redirectOrReload(){this.deactivateUrl?window.location.href=this.deactivateUrl:location.reload()},_hideShowFeedbackAndCTA(t){var e="true"===i(t.target).attr("data-accept_feedback"),a="true"===i(t.target).attr("data-show_cta");i(t.target).closest(this.formWrapper).find(".uds-options-feedback").removeClass("hide").addClass(e?"show":"hide"),i(t.target).closest(this.formWrapper).find(".uds-option-feedback-cta").removeClass("hide").addClass(a?"show":"hide")},_changePlaceholderText(t){var e=t.target.getAttribute("data-placeholder");i(t.target).closest(this.formWrapper).find(this.feedbackInput).attr("placeholder",e||""),this._hideShowFeedbackAndCTA(t)},_submitFeedback(t,s){t.preventDefault();var t=i(t.target),e=t.closest(this.feedbackForm),e={action:"uds_plugin_deactivate_feedback",security:udsVars?._ajax_nonce||"",reason:e.find(this.radioButton.filter(":checked")).val()||"",source:e.find('input[name="source"]').val()||"",referer:e.find('input[name="referer"]').val()||"",version:e.find('input[name="version"]').val()||"",feedback:e.find(this.feedbackInput).val()||""};t.find(".uds-feedback-"+this.buttonAction).text("Deactivating.").addClass("processing"),i.ajax({url:udsVars?.ajaxurl,type:"POST",data:e,success(t){t.success&&s._redirectOrReload(),s._hidePopup()},error(t,e,a){s._redirectOrReload()}})},_handleClick(t){t.target.classList.contains("show_popup")&&t.target.closest(".uds-feedback-form--wrapper")?this._hidePopup():t.target.classList.contains("activate")&&(this.deactivateUrl=t.target.href,-1===this.deactivateUrl.indexOf(`stylesheet=${udsVars?._current_theme}-child`))&&-1===this.deactivateUrl.indexOf(`stylesheet=${udsVars?._current_theme}&`)&&(t.preventDefault(),this._showPopup(udsVars?._current_theme))},_init(){this._cacheElements(),this._bind()},_bind(){let a=this;this.deactivateButton.on("click",function(t){var e=i(t.target).closest("tr").data("slug");a.slug.includes(e)&&(t.preventDefault(),a.deactivateUrl=i(t.target).attr("href"),a._showPopup(e))}),this.closeButton.on("click",function(t){t.preventDefault(),a._hidePopup()}),this.radioButton.on("click",function(t){a._changePlaceholderText(t)}),this.submitDeactivate.add(this.skipButton).on("click",function(t){t.preventDefault(),a.buttonAction=i(t.target).attr("data-action"),i(t.target).closest(a.feedbackForm).submit()}),this.feedbackForm.on("submit",function(t){a._submitFeedback(t,a)}),document.addEventListener("click",function(t){a._handleClick(t)})}};i(function(){t._init()})})(jQuery);