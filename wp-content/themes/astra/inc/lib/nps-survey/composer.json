{"name": "brainstormforce/nps-survey", "type": "wordpress-plugin", "description": "NPS Survey Plugin", "require-dev": {"squizlabs/php_codesniffer": "^3.5", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.1", "phpcompatibility/php-compatibility": "^9.3", "wp-coding-standards/wpcs": "^2.2", "phpstan/phpstan": "^1.9", "szepeviktor/phpstan-wordpress": "^1.1", "php-stubs/wordpress-stubs": "^6.1", "php-stubs/generator": "^0.8.2", "automattic/vipwpcs": "^2.3", "nunomaduro/phpinsights": "^2.10"}, "scripts": {"format": "vendor/bin/phpcbf", "lint": "vendor/bin/phpcs", "test": "vendor/bin/phpunit", "phpstan": "vendor/bin/phpstan --memory-limit=2048M analyse", "insights": "./vendor/bin/phpinsights analyse -v --no-interaction", "insights:fix": "./vendor/bin/phpinsights analyse --fix --no-interaction", "gen-stubs": "vendor/bin/generate-stubs artifact/phpstan/nps-survey/ --out=tests/php/stubs/nps-survey-stubs.php && rm -rf artifact/phpstan", "update-stubs": "rm -f tests/php/stubs/nps-survey-stubs.php && bash bin/build-folder-phpstan.sh && composer gen-stubs"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}}