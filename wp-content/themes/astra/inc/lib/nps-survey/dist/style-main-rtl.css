.nps-survey-root .invisible {visibility: hidden
}.nps-survey-root .fixed {position: fixed
}.nps-survey-root .absolute {position: absolute
}.nps-survey-root .relative {position: relative
}.nps-survey-root .inset-0 {inset: 0px
}.nps-survey-root .bottom-2 {bottom: 0.5rem
}.nps-survey-root .right-2 {left: 0.5rem
}.nps-survey-root .right-3 {left: 0.75rem
}.nps-survey-root .top-3 {top: 0.75rem
}.nps-survey-root .isolate {isolation: isolate
}.nps-survey-root .z-\[9999999999\] {z-index: 9999999999
}.nps-survey-root .mx-0 {margin-right: 0px;margin-left: 0px
}.nps-survey-root .my-0 {margin-top: 0px;margin-bottom: 0px
}.nps-survey-root .mb-0 {margin-bottom: 0px
}.nps-survey-root .mt-1 {margin-top: 0.25rem
}.nps-survey-root .mt-2 {margin-top: 0.5rem
}.nps-survey-root .mt-3 {margin-top: 0.75rem
}.nps-survey-root .mt-5 {margin-top: 1.25rem
}.nps-survey-root .block {display: block
}.nps-survey-root .flex {display: flex
}.nps-survey-root .inline-flex {display: inline-flex
}.nps-survey-root .size-5 {width: 1.25rem;height: 1.25rem
}.nps-survey-root .size-6 {width: 1.5rem;height: 1.5rem
}.nps-survey-root .h-11 {height: 2.75rem
}.nps-survey-root .h-5 {height: 1.25rem
}.nps-survey-root .h-\[2\.625rem\] {height: 2.625rem
}.nps-survey-root .w-4 {width: 1rem
}.nps-survey-root .w-5 {width: 1.25rem
}.nps-survey-root .w-\[calc\(100\%-8px\)\] {width: calc(100% - 8px)
}.nps-survey-root .w-full {width: 100%
}.nps-survey-root .max-w-\[30rem\] {max-width: 30rem
}.nps-survey-root .flex-1 {flex: 1 1 0%
}@keyframes spin {to {transform: rotate(-360deg)
    }
}.nps-survey-root .animate-spin {animation: spin 1s linear infinite
}.nps-survey-root .cursor-not-allowed {cursor: not-allowed
}.nps-survey-root .cursor-pointer {cursor: pointer
}.nps-survey-root .cursor-progress {cursor: progress
}.nps-survey-root .resize {resize: both
}.nps-survey-root .items-center {align-items: center
}.nps-survey-root .justify-start {justify-content: flex-start
}.nps-survey-root .justify-center {justify-content: center
}.nps-survey-root .justify-between {justify-content: space-between
}.nps-survey-root .gap-2 {gap: 0.5rem
}.nps-survey-root .rounded {border-radius: 0.25rem
}.nps-survey-root .rounded-lg {border-radius: 0.5rem
}.nps-survey-root .rounded-md {border-radius: 0.375rem
}.nps-survey-root .border {border-width: 1px
}.nps-survey-root .border-0 {border-width: 0px
}.nps-survey-root .border-solid {border-style: solid
}.nps-survey-root .border-none {border-style: none
}.nps-survey-root .border-border-tertiary {--tw-border-opacity: 1;border-color: rgb(216 223 233 / var(--tw-border-opacity))
}.nps-survey-root .border-button-disabled {--tw-border-opacity: 1;border-color: rgb(229 231 235 / var(--tw-border-opacity))
}.nps-survey-root .border-nps-button-background {--tw-border-opacity: 1;border-color: rgb(34 113 177 / var(--tw-border-opacity))
}.nps-survey-root .border-transparent {border-color: transparent
}.nps-survey-root .border-white {--tw-border-opacity: 1;border-color: rgb(255 255 255 / var(--tw-border-opacity))
}.nps-survey-root .border-zip-body-text {--tw-border-opacity: 1;border-color: rgb(var(--zip-body-text) / var(--tw-border-opacity))
}.nps-survey-root .bg-nps-button-background {--tw-bg-opacity: 1;background-color: rgb(34 113 177 / var(--tw-bg-opacity))
}.nps-survey-root .bg-transparent {background-color: transparent
}.nps-survey-root .bg-white {--tw-bg-opacity: 1;background-color: rgb(255 255 255 / var(--tw-bg-opacity))
}.nps-survey-root .p-4 {padding: 1rem
}.nps-survey-root .px-4 {padding-right: 1rem;padding-left: 1rem
}.nps-survey-root .px-5 {padding-right: 1.25rem;padding-left: 1.25rem
}.nps-survey-root .px-6 {padding-right: 1.5rem;padding-left: 1.5rem
}.nps-survey-root .py-1\.5 {padding-top: 0.375rem;padding-bottom: 0.375rem
}.nps-survey-root .py-2 {padding-top: 0.5rem;padding-bottom: 0.5rem
}.nps-survey-root .py-3 {padding-top: 0.75rem;padding-bottom: 0.75rem
}.nps-survey-root .pl-0 {padding-right: 0px
}.nps-survey-root .pl-3 {padding-right: 0.75rem
}.nps-survey-root .pl-4 {padding-right: 1rem
}.nps-survey-root .pl-5 {padding-right: 1.25rem
}.nps-survey-root .pl-6 {padding-right: 1.5rem
}.nps-survey-root .pr-3 {padding-left: 0.75rem
}.nps-survey-root .pr-4 {padding-left: 1rem
}.nps-survey-root .pr-5 {padding-left: 1.25rem
}.nps-survey-root .pr-6 {padding-left: 1.5rem
}.nps-survey-root .text-base {font-size: 1rem;line-height: 1.5rem
}.nps-survey-root .text-lg {font-size: 1.125rem;line-height: 1.75rem
}.nps-survey-root .text-sm {font-size: 0.875rem;line-height: 1.25rem
}.nps-survey-root .text-xs {font-size: 0.75rem;line-height: 1rem
}.nps-survey-root .font-bold {font-weight: 700
}.nps-survey-root .font-medium {font-weight: 500
}.nps-survey-root .font-normal {font-weight: 400
}.nps-survey-root .font-semibold {font-weight: 600
}.nps-survey-root .leading-5 {line-height: 1.25rem
}.nps-survey-root .leading-6 {line-height: 1.5rem
}.nps-survey-root .leading-7 {line-height: 1.75rem
}.nps-survey-root .text-border-secondary {--tw-text-opacity: 1;color: rgb(107 114 128 / var(--tw-text-opacity))
}.nps-survey-root .text-nps-button-background {--tw-text-opacity: 1;color: rgb(34 113 177 / var(--tw-text-opacity))
}.nps-survey-root .text-secondary-text {--tw-text-opacity: 1;color: rgb(156 163 175 / var(--tw-text-opacity))
}.nps-survey-root .text-white {--tw-text-opacity: 1;color: rgb(255 255 255 / var(--tw-text-opacity))
}.nps-survey-root .text-zip-app-heading {--tw-text-opacity: 1;color: rgb(var(--zip-app-heading) / var(--tw-text-opacity))
}.nps-survey-root .text-zip-app-inactive-icon {--tw-text-opacity: 1;color: rgb(var(--zip-app-inactive-icon) / var(--tw-text-opacity))
}.nps-survey-root .text-zip-body-text {--tw-text-opacity: 1;color: rgb(var(--zip-body-text) / var(--tw-text-opacity))
}.nps-survey-root .underline {text-decoration-line: underline
}.nps-survey-root .no-underline {text-decoration-line: none
}.nps-survey-root .opacity-25 {opacity: 0.25
}.nps-survey-root .opacity-50 {opacity: 0.5
}.nps-survey-root .opacity-70 {opacity: 0.7
}.nps-survey-root .opacity-75 {opacity: 0.75
}.nps-survey-root .shadow-lg {--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}.nps-survey-root .shadow-sm {--tw-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);--tw-shadow-colored: 0px 1px 2px 0px var(--tw-shadow-color);box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}.nps-survey-root .transition {transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms
}.nps-survey-root .transition-colors {transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);transition-duration: 150ms
}.nps-survey-root .duration-150 {transition-duration: 150ms
}.nps-survey-root .ease-in-out {transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1)
}.nps-survey-root {font-size: 1rem;line-height: 1.5rem
}.nps-survey-root * {box-sizing: border-box;font-family: Figtree, sans-serif
}.nps-survey-root .hover\:cursor-pointer:hover {cursor: pointer
}.nps-survey-root .hover\:bg-gray-50:hover {--tw-bg-opacity: 1;background-color: rgb(249 250 251 / var(--tw-bg-opacity))
}.nps-survey-root .focus\:z-10:focus {z-index: 10
}.nps-survey-root .focus\:ring-1:focus {--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}.nps-survey-root .focus\:ring-nps-button-background:focus {--tw-ring-opacity: 1;--tw-ring-color: rgb(34 113 177 / var(--tw-ring-opacity))
}.nps-survey-root .focus-visible\:outline:focus-visible {outline-style: solid
}.nps-survey-root .focus-visible\:outline-2:focus-visible {outline-width: 2px
}.nps-survey-root .focus-visible\:outline-offset-2:focus-visible {outline-offset: 2px
}@media (min-width: 512px) {.nps-survey-root .xs\:w-full {width: 100%
    }
}@media (min-width: 640px) {.nps-survey-root .sm\:p-5 {padding: 1.25rem
    }.nps-survey-root .sm\:text-sm {font-size: 0.875rem;line-height: 1.25rem
    }.nps-survey-root .sm\:leading-6 {line-height: 1.5rem
    }
}
