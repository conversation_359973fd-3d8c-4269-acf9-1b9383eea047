jQuery,astra_css("astra-settings[scroll-to-top-icon-size]","font-size","#ast-scroll-top","px"),astra_css("astra-settings[scroll-to-top-icon-color]","color","#ast-scroll-top"),astra_css("astra-settings[scroll-to-top-icon-bg-color]","background-color","#ast-scroll-top"),astra_css("astra-settings[scroll-to-top-icon-h-color]","color","#ast-scroll-top:hover"),astra_css("astra-settings[scroll-to-top-icon-h-bg-color]","background-color","#ast-scroll-top:hover"),wp.customize("astra-settings[scroll-to-top-icon-radius-fields]",function(t){t.bind(function(t){var o=astraBuilderPreview.tablet_break_point||768,s="",s=(s=(s+=" #ast-scroll-top { border-top-left-radius :"+t.desktop.top+t["desktop-unit"]+"; border-bottom-right-radius :"+t.desktop.bottom+t["desktop-unit"]+"; border-bottom-left-radius :"+t.desktop.left+t["desktop-unit"]+"; border-top-right-radius :"+t.desktop.right+t["desktop-unit"]+"; } ")+("@media (max-width: "+o+"px) { #ast-scroll-top { border-top-left-radius :"+t.tablet.top+t["tablet-unit"]+"; border-bottom-right-radius :"+t.tablet.bottom+t["tablet-unit"]+"; border-bottom-left-radius :"+t.tablet.left+t["tablet-unit"]+"; border-top-right-radius :"+t.tablet.right+t["tablet-unit"]+"; } }"))+("@media (max-width: "+(astraBuilderPreview.mobile_break_point||544)+"px) { #ast-scroll-top { border-top-left-radius :"+t.mobile.top+t["mobile-unit"]+"; border-bottom-right-radius :"+t.mobile.bottom+t["mobile-unit"]+"; border-bottom-left-radius :"+t.mobile.left+t["mobile-unit"]+"; border-top-right-radius :"+t.mobile.right+t["mobile-unit"]+"; } }");astra_add_dynamic_css("scroll-to-top-icon-radius-fields",s)})}),wp.customize("astra-settings[scroll-to-top-icon-position]",function(t){t.bind(function(t){jQuery("#ast-scroll-top").removeClass("ast-scroll-to-top-right ast-scroll-to-top-left"),jQuery("#ast-scroll-top").addClass("ast-scroll-to-top-"+t)})});