(()=>{AstraBuilderTransparentData.is_astra_hf_builder_active,wp.customize("astra-settings[transparent-header-logo-width]",function(e){e.bind(function(e){""!=e.desktop||""!=e.tablet||""!=e.mobile?(e=".ast-theme-transparent-header #masthead .site-logo-img .transparent-custom-logo img {max-width: "+e.desktop+"px;} .ast-theme-transparent-header #masthead .site-logo-img .transparent-custom-logo .astra-logo-svg { width: "+e.desktop+"px;} @media( max-width: 768px ) { .ast-theme-transparent-header #masthead .site-logo-img .transparent-custom-logo img {max-width: "+e.tablet+"px;} .ast-theme-transparent-header #masthead .site-logo-img .transparent-custom-logo .astra-logo-svg { width: "+e.tablet+"px;} } @media( max-width: 544px ) { .ast-theme-transparent-header #masthead .site-logo-img .transparent-custom-logo img {max-width: "+e.mobile+"px;} .ast-theme-transparent-header #masthead .site-logo-img .transparent-custom-logo .astra-logo-svg { width: "+e.mobile+"px;} }",astra_add_dynamic_css("transparent-header-logo-width",e)):wp.customize.preview.send("refresh")})});var e=AstraBuilderTransparentData.transparent_header_devices;let a="";switch(e){case"mobile":a=".ast-header-break-point";break;case"desktop":a=":not(.ast-header-break-point)"}astra_color_responsive_css("colors-background","astra-settings[primary-menu-a-bg-color-responsive]","background-color",".main-header-menu .current-menu-item > .menu-link, .main-header-menu .current-menu-ancestor > .menu-link,.ast-header-sections-navigation .menu-item.current-menu-item > .menu-link,.ast-header-sections-navigation .menu-item.current-menu-ancestor > .menu-link"),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-header-bg-color-responsive]","background-color",".ast-theme-transparent-header"+a+" .main-header-bar, .ast-theme-transparent-header.ast-header-break-point .main-header-bar-wrap .main-header-menu, .ast-theme-transparent-header"+a+" .main-header-bar-wrap .main-header-bar, .ast-theme-transparent-header"+("both"===e?".ast-header-break-point":a)+" .ast-mobile-header-wrap .main-header-bar"),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-header-color-site-title-responsive]","color",".ast-theme-transparent-header .site-title a, .ast-theme-transparent-header .site-title a:focus, .ast-theme-transparent-header .site-title a:hover, .ast-theme-transparent-header .site-title a:visited, .ast-theme-transparent-header .site-header .site-description"),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-header-color-h-site-title-responsive]","color",".ast-theme-transparent-header .site-header .site-title a:hover"),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-menu-bg-color-responsive]","background-color",".ast-theme-transparent-header .ast-builder-menu .main-header-menu, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu, .ast-theme-transparent-header .ast-builder-menu .main-header-menu, .ast-theme-transparent-header.ast-header-break-point .ast-builder-menu .main-header-bar-wrap .main-header-menu, .ast-flyout-menu-enable.ast-header-break-point.ast-theme-transparent-header .main-header-bar-navigation .site-navigation, .ast-fullscreen-menu-enable.ast-header-break-point.ast-theme-transparent-header .main-header-bar-navigation .site-navigation, .ast-flyout-above-menu-enable.ast-header-break-point.ast-theme-transparent-header .ast-above-header-navigation-wrap .ast-above-header-navigation, .ast-flyout-below-menu-enable.ast-header-break-point.ast-theme-transparent-header .ast-below-header-navigation-wrap .ast-below-header-actual-nav, .ast-fullscreen-above-menu-enable.ast-header-break-point.ast-theme-transparent-header .ast-above-header-navigation-wrap, .ast-fullscreen-below-menu-enable.ast-header-break-point.ast-theme-transparent-header .ast-below-header-navigation-wrap, .ast-theme-transparent-header .main-header-menu .menu-link"),astra_color_responsive_css("transparent-primary-header-menu-colors","astra-settings[transparent-menu-color-responsive]","color",'.ast-theme-transparent-header .ast-builder-menu .main-header-menu, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-link, .ast-theme-transparent-header [CLASS*="ast-builder-menu-"] .main-header-menu .menu-item > .menu-link, .ast-theme-transparent-header .ast-masthead-custom-menu-items, .ast-theme-transparent-header .ast-masthead-custom-menu-items a, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item > .ast-menu-toggle, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item > .ast-menu-toggle, .ast-theme-transparent-header .main-header-menu .menu-link'),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-menu-h-color-responsive]","color",'.ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item:hover > .menu-link, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item:hover > .ast-menu-toggle, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .ast-masthead-custom-menu-items a:hover, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .focus > .menu-link, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .focus > .ast-menu-toggle, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .current-menu-item > .menu-link, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .current-menu-ancestor > .menu-link, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .current-menu-item > .ast-menu-toggle, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .current-menu-ancestor > .ast-menu-toggle, .ast-theme-transparent-header [CLASS*="ast-builder-menu-"] .main-header-menu .current-menu-item > .menu-link, .ast-theme-transparent-header [CLASS*="ast-builder-menu-"] .main-header-menu .current-menu-ancestor > .menu-link, .ast-theme-transparent-header [CLASS*="ast-builder-menu-"] .main-header-menu .current-menu-item > .ast-menu-toggle, .ast-theme-transparent-header [CLASS*="ast-builder-menu-"] .main-header-menu .current-menu-ancestor > .ast-menu-toggle, .ast-theme-transparent-header .main-header-menu .menu-item:hover > .menu-link, .ast-theme-transparent-header .main-header-menu .current-menu-item > .menu-link, .ast-theme-transparent-header .main-header-menu .current-menu-ancestor > .menu-link'),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-submenu-bg-color-responsive]","background-color",'.ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu, .ast-header-break-point.ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu, .ast-theme-transparent-header .ast-builder-menu [CLASS*="ast-builder-menu-"] .main-header-menu .sub-menu, .ast-header-break-point.ast-theme-transparent-header .ast-builder-menu [CLASS*="ast-builder-menu-"] .main-header-menu .sub-menu, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-link, .ast-header-break-point.ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-link, .ast-theme-transparent-header .ast-builder-menu [CLASS*="ast-builder-menu-"] .main-header-menu .sub-menu .menu-link, .ast-header-break-point.ast-theme-transparent-header .ast-builder-menu [CLASS*="ast-builder-menu-"] .main-header-menu .sub-menu .menu-link, .ast-theme-transparent-header .main-header-menu .menu-item .sub-menu .menu-link, .ast-header-break-point.ast-theme-transparent-header .main-header-menu .menu-item .sub-menu .menu-link, .ast-theme-transparent-header .main-header-menu .menu-item .sub-menu, .ast-header-break-point.ast-theme-transparent-header .main-header-menu .menu-item .sub-menu'),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-submenu-color-responsive]","color",'.ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-item .menu-link,.ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-item > .ast-menu-toggle, .astra-hfb-header.ast-theme-transparent-header [CLASS*="ast-builder-menu-"]  .main-header-menu .sub-menu .menu-item .menu-link, .astra-hfb-header.ast-theme-transparent-header [CLASS*="ast-builder-menu-"]  .main-header-menu .sub-menu .menu-item > .ast-menu-toggle, .ast-theme-transparent-header .main-header-menu .menu-item .sub-menu .menu-link, .ast-header-break-point.ast-theme-transparent-header .main-header-menu .menu-item .sub-menu .menu-link'),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-submenu-h-color-responsive]","color",".ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu a:hover,.ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-item:hover > .menu-item, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-item.focus > .menu-item, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-item.current-menu-item > .menu-link,\t.ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-item.current-menu-item > .ast-menu-toggle,.ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-item:hover > .ast-menu-toggle, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-item.focus > .ast-menu-toggle, .ast-theme-transparent-header .ast-builder-menu .main-header-menu .menu-item .sub-menu .menu-item:hover > .menu-link, .ast-theme-transparent-header .main-header-menu .menu-item .sub-menu .menu-item:hover .menu-link");var t=document.querySelector(".ast-main-header-wrap.main-header-bar-wrap ");t&&t.querySelector(".site-logo-img")&&astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-header-bg-color-responsive]","background-color",".ast-theme-transparent-header"+a+" .main-header-bar, .ast-theme-transparent-header.ast-header-break-point .main-header-bar-wrap .main-header-menu, .ast-theme-transparent-header"+a+" .main-header-bar-wrap .main-header-bar, .ast-theme-transparent-header"+("both"===e?".ast-header-break-point":a)+" .ast-mobile-header-wrap .main-header-bar, .ast-theme-transparent-header .ast-sg-element-wrap.ast-sg-logo-section"),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-content-section-text-color-responsive]","color",'.ast-theme-transparent-header div.ast-masthead-custom-menu-items, .ast-theme-transparent-header div.ast-masthead-custom-menu-items .widget, .ast-theme-transparent-header div.ast-masthead-custom-menu-items .widget-title, .ast-theme-transparent-header .site-header-section [CLASS*="ast-header-html-"] .ast-builder-html-element'),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-content-section-link-color-responsive]","color",'.ast-theme-transparent-header div.ast-masthead-custom-menu-items a, .ast-theme-transparent-header div.ast-masthead-custom-menu-items .widget a, .ast-theme-transparent-header .site-header-section [CLASS*="ast-header-html-"] .ast-builder-html-element a'),astra_color_responsive_css("transparent-primary-header","astra-settings[transparent-content-section-link-h-color-responsive]","color",'.ast-theme-transparent-header div.ast-masthead-custom-menu-items a:hover, .ast-theme-transparent-header div.ast-masthead-custom-menu-items .widget a:hover, .ast-theme-transparent-header .site-header-section [CLASS*="ast-header-html-"] .ast-builder-html-element a:hover'),astra_color_responsive_css("transparent-above-header","astra-settings[hba-transparent-header-bg-color-responsive]","background-color",".ast-theme-transparent-header"+a+" .ast-above-header-wrap .ast-above-header, .ast-theme-transparent-header.ast-header-break-point .ast-above-header-wrap .main-header-menu"),astra_color_responsive_css("transparent-above-header","astra-settings[transparent-menu-bg-color-responsive]","background-color",".ast-theme-transparent-header .ast-above-header-menu, .ast-theme-transparent-header.ast-header-break-point .ast-above-header-section-separated .ast-above-header-navigation ul, .ast-flyout-above-menu-enable.ast-header-break-point.ast-theme-transparent-header .ast-above-header-navigation-wrap .ast-above-header-navigation, .ast-fullscreen-above-menu-enable.ast-header-break-point.ast-theme-transparent-header .ast-above-header-section-separated .ast-above-header-navigation-wrap"),astra_color_responsive_css("transparent-above-header","astra-settings[transparent-menu-color-responsive]","color",".ast-theme-transparent-header .ast-above-header-navigation a, .ast-header-break-point.ast-theme-transparent-header .ast-above-header-navigation a, .ast-header-break-point.ast-theme-transparent-header .ast-above-header-navigation > ul.ast-above-header-menu > .menu-item-has-children:not(.current-menu-item) > .ast-menu-toggle"),astra_color_responsive_css("transparent-above-header","astra-settings[transparent-menu-h-color-responsive]","color",".ast-theme-transparent-header .ast-above-header-navigation .menu-item.current-menu-item > .menu-link,.ast-theme-transparent-header .ast-above-header-navigation .menu-item.current-menu-ancestor > .menu-link, .ast-theme-transparent-header .ast-above-header-navigation .menu-item:hover > .menu-link"),astra_color_responsive_css("transparent-above-header","astra-settings[transparent-submenu-bg-color-responsive]","background-color",".ast-theme-transparent-header .ast-above-header-menu .sub-menu"),astra_color_responsive_css("transparent-above-header","astra-settings[transparent-submenu-color-responsive]","color",".ast-theme-transparent-header .ast-above-header-menu .sub-menu, .ast-theme-transparent-header .ast-above-header-navigation .ast-above-header-menu .sub-menu a"),astra_color_responsive_css("transparent-above-header","astra-settings[transparent-submenu-h-color-responsive]","color",".ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item:hover > .menu-item, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item:focus > .menu-item, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.focus > .menu-item,.ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item:hover > .ast-menu-toggle, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item:focus > .ast-menu-toggle, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.focus > .ast-menu-toggle,.ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-ancestor > .ast-menu-toggle, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-item > .ast-menu-toggle, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-ancestor:hover > .ast-menu-toggle, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-ancestor:focus > .ast-menu-toggle, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-ancestor.focus > .ast-menu-toggle, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-item:hover > .ast-menu-toggle, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-item:focus > .ast-menu-toggle, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-item.focus > .ast-menu-toggle,.ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-ancestor > .menu-link, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-item > .menu-link, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-ancestor:hover > .menu-link, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-ancestor:focus > .menu-link, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-ancestor.focus > .menu-link, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-item:hover > .menu-link, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-item:focus > .menu-link, .ast-theme-transparent-header .ast-above-header-menu .sub-menu .menu-item.current-menu-item.focus > .menu-link"),astra_color_responsive_css("transparent-above-header","astra-settings[transparent-content-section-text-color-responsive]","color",".ast-theme-transparent-header .ast-above-header-section .user-select, .ast-theme-transparent-header .ast-above-header-section .widget, .ast-theme-transparent-header .ast-above-header-section .widget-title"),astra_color_responsive_css("transparent-above-header","astra-settings[transparent-content-section-link-color-responsive]","color",".ast-theme-transparent-header .ast-above-header-section .user-select a, .ast-theme-transparent-header .ast-above-header-section .widget a"),astra_color_responsive_css("transparent-above-header","astra-settings[transparent-content-section-link-h-color-responsive]","color",".ast-theme-transparent-header .ast-above-header-section .user-select a:hover, .ast-theme-transparent-header .ast-above-header-section .widget a:hover"),(t=document.querySelector(".ast-above-header-wrap"))&&t.querySelector(".site-logo-img")&&astra_color_responsive_css("transparent-above-header","astra-settings[hba-transparent-header-bg-color-responsive]","background-color"," .ast-theme-transparent-header"+a+" .ast-above-header-wrap .ast-above-header, .ast-theme-transparent-header.ast-header-break-point .ast-above-header-wrap .main-header-menu, .ast-theme-transparent-header .ast-sg-element-wrap.ast-sg-logo-section"),astra_color_responsive_css("transparent-below-header","astra-settings[hbb-transparent-header-bg-color-responsive]","background-color",".ast-theme-transparent-header"+a+" .ast-below-header-wrap .ast-below-header, .ast-theme-transparent-header.ast-header-break-point .ast-below-header-wrap .main-header-menu"),astra_color_responsive_css("transparent-below-header","astra-settings[transparent-menu-bg-color-responsive]","background-color",".ast-theme-transparent-header.ast-no-toggle-below-menu-enable.ast-header-break-point .ast-below-header-navigation-wrap, .ast-theme-transparent-header .ast-below-header-actual-nav, .ast-theme-transparent-header.ast-header-break-point .ast-below-header-actual-nav, .ast-flyout-below-menu-enable.ast-header-break-point.ast-theme-transparent-header .ast-below-header-navigation-wrap .ast-below-header-actual-nav, .ast-fullscreen-below-menu-enable.ast-header-break-point.ast-theme-transparent-header .ast-below-header-section-separated .ast-below-header-navigation-wrap"),astra_color_responsive_css("transparent-below-header","astra-settings[transparent-menu-color-responsive]","color",".ast-theme-transparent-header .ast-below-header-menu, .ast-theme-transparent-header .ast-below-header-menu a, .ast-header-break-point.ast-theme-transparent-header .ast-below-header-menu a, .ast-header-break-point.ast-theme-transparent-header .ast-below-header-menu"),astra_color_responsive_css("transparent-below-header","astra-settings[transparent-menu-h-color-responsive]","color",".ast-theme-transparent-header .ast-below-header-menu .menu-item:hover > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .menu-item:focus > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .menu-item.focus > .menu-link,.ast-theme-transparent-header .ast-below-header-menu .menu-item.current-menu-ancestor > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .menu-item.current-menu-item > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .menu-item.current-menu-ancestor > .ast-menu-toggle, .ast-theme-transparent-header .ast-below-header-menu .menu-item.current-menu-item > .ast-menu-toggle, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor:hover > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor:focus > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor.focus > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item:hover > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item:focus > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item.focus > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor:hover > .ast-menu-toggle, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor:focus > .ast-menu-toggle, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor.focus > .ast-menu-toggle, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item:hover > .ast-menu-toggle, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item:focus > .ast-menu-toggle, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item.focus > .ast-menu-toggle"),astra_color_responsive_css("transparent-below-header","astra-settings[transparent-submenu-bg-color-responsive]","background-color",".ast-theme-transparent-header .ast-below-header-menu .sub-menu"),astra_color_responsive_css("transparent-below-header","astra-settings[transparent-submenu-color-responsive]","color",".ast-theme-transparent-header .ast-below-header-menu .sub-menu, .ast-theme-transparent-header .ast-below-header-menu .sub-menu a"),astra_color_responsive_css("transparent-below-header","astra-settings[transparent-submenu-h-color-responsive]","color",".ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item:hover > .menu-item, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item:focus > .menu-item, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.focus > .menu-item,.ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor:hover > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor:focus > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-ancestor.focus > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item:hover > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item:focus > .menu-link, .ast-theme-transparent-header .ast-below-header-menu .sub-menu .menu-item.current-menu-item.focus > .menu-link"),astra_color_responsive_css("transparent-below-header","astra-settings[transparent-content-section-text-color-responsive]","color","",".ast-theme-transparent-header .below-header-user-select, .ast-theme-transparent-header .below-header-user-select .widget,.ast-theme-transparent-header .below-header-user-select .widget-title"),astra_color_responsive_css("transparent-below-header","astra-settings[transparent-content-section-link-color-responsive]","color","",".ast-theme-transparent-header .below-header-user-select a, .ast-theme-transparent-header .below-header-user-select .widget a"),astra_color_responsive_css("below-transparent-header","astra-settings[transparent-content-section-link-h-color-responsive]","color",".ast-theme-transparent-header .below-header-user-select a:hover, .ast-theme-transparent-header .below-header-user-select .widget a:hover");(e=document.querySelector(".ast-below-header-wrap  "))&&e.querySelector(".site-logo-img")&&astra_color_responsive_css("transparent-below-header","astra-settings[hbb-transparent-header-bg-color-responsive]","background-color",".ast-theme-transparent-header"+a+" .ast-below-header-wrap .ast-below-header, .ast-theme-transparent-header.ast-header-break-point .ast-below-header-wrap .main-header-menu, .ast-theme-transparent-header .ast-sg-element-wrap.ast-sg-logo-section"),wp.customize("astra-settings[primary-header-button-border-group]",function(e){e.bind(function(e){var a,e=JSON.parse(e)["header-main-rt-section-button-border-size"];""==e.top&&""==e.right&&""==e.bottom&&""==e.left||(a=(a=(a=(a=(a=".main-header-bar .ast-container .button-custom-menu-item .ast-custom-button-link .ast-custom-button")+"{border-top-width:"+e.top+"px;")+"border-right-width:"+e.right+"px;")+"border-left-width:"+e.left+"px;")+"border-bottom-width:"+e.bottom+"px;border-style: solid;}",astra_add_dynamic_css("header-main-rt-section-button-border-size",a))})}),astra_css("astra-settings[header-main-rt-trans-section-button-text-color]","color",".ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[header-main-rt-trans-section-button-back-color]","background-color",".ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[header-main-rt-trans-section-button-text-h-color]","color",".ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button:hover"),astra_css("astra-settings[header-main-rt-trans-section-button-back-h-color]","background-color",".ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button:hover"),astra_css("astra-settings[header-main-rt-trans-section-button-border-radius]","border-radius",".ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button","px"),astra_css("astra-settings[header-main-rt-trans-section-button-border-color]","border-color",".ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button"),astra_css("astra-settings[header-main-rt-trans-section-button-border-h-color]","border-color",".ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button:hover"),astra_responsive_spacing("astra-settings[header-main-rt-trans-section-button-padding]",".ast-theme-transparent-header .main-header-bar .button-custom-menu-item .ast-custom-button-link .ast-custom-button","padding",["top","right","bottom","left"]),astra_css("astra-settings[transparent-header-divider-color]","border-color",".ast-theme-transparent-header .ast-header-divider-element .ast-divider-wrapper"),astra_css("astra-settings[transparent-header-html-text-color]","color",'.ast-theme-transparent-header [CLASS*="ast-header-html-"] .ast-builder-html-element'),astra_css("astra-settings[transparent-header-html-link-color]","color",'.ast-theme-transparent-header [CLASS*="ast-header-html-"] .ast-builder-html-element a'),astra_css("astra-settings[transparent-header-html-link-h-color]","color",'.ast-theme-transparent-header [CLASS*="ast-header-html-"] .ast-builder-html-element a:hover'),astra_css("astra-settings[transparent-header-search-icon-color]","color",".ast-theme-transparent-header .ast-header-search .astra-search-icon, .ast-theme-transparent-header .ast-header-search .ast-icon"),astra_css("astra-settings[transparent-header-search-box-placeholder-color]","color",".ast-theme-transparent-header .ast-header-search .ast-search-menu-icon .search-field, .ast-theme-transparent-header .ast-header-search .ast-search-menu-icon .search-field::placeholder"),astra_css("astra-settings[transparent-header-search-box-background-color]","background-color",".ast-theme-transparent-header .ast-header-search .ast-search-menu-icon .search-field, .ast-theme-transparent-header .ast-header-search .ast-search-menu-icon .search-form, .ast-theme-transparent-header .ast-header-search .ast-search-menu-icon .search-submit"),astra_color_responsive_css("transparent-header-social-color","astra-settings[transparent-header-social-icons-bg-color]","background",".ast-theme-transparent-header .ast-header-social-wrap .ast-social-color-type-custom .ast-builder-social-element"),astra_color_responsive_css("transparent-header-social-color","astra-settings[transparent-header-social-icons-color]","fill",".ast-theme-transparent-header .ast-header-social-wrap .ast-social-color-type-custom .ast-builder-social-element svg"),astra_color_responsive_css("transparent-header-social-color-label","astra-settings[transparent-header-social-icons-color]","color",".ast-theme-transparent-header .ast-header-social-wrap .ast-social-color-type-custom .ast-builder-social-element .social-item-label"),astra_color_responsive_css("transparent-header-social-color","astra-settings[transparent-header-social-icons-bg-h-color]","background",".ast-theme-transparent-header .ast-header-social-wrap .ast-social-color-type-custom .ast-builder-social-element:hover"),astra_color_responsive_css("transparent-header-social-color","astra-settings[transparent-header-social-icons-h-color]","fill",".ast-theme-transparent-header .ast-header-social-wrap .ast-social-color-type-custom .ast-builder-social-element:hover svg"),astra_color_responsive_css("transparent-header-social-color-label-h","astra-settings[transparent-header-social-icons-h-color]","color",".ast-theme-transparent-header .ast-header-social-wrap .ast-social-color-type-custom .ast-builder-social-element:hover .social-item-label"),astra_css("astra-settings[transparent-header-widget-title-color]","color",".ast-theme-transparent-header .widget-area.header-widget-area .widget-title"),t=AstraBuilderTransparentData.is_flex_based_css?".ast-theme-transparent-header .widget-area.header-widget-area.header-widget-area-inner":".ast-theme-transparent-header .widget-area.header-widget-area. header-widget-area-inner",astra_css("astra-settings[transparent-header-widget-content-color]","color",t),astra_css("astra-settings[transparent-header-widget-link-color]","color",t+" a"),astra_css("astra-settings[transparent-header-widget-link-h-color]","color",t+" a:hover"),astra_css("astra-settings[transparent-header-button-text-color]","color",'.ast-theme-transparent-header [CLASS*="ast-header-button-"] .ast-custom-button'),astra_css("astra-settings[transparent-header-button-bg-color]","background",'.ast-theme-transparent-header [CLASS*="ast-header-button-"] .ast-custom-button'),astra_css("astra-settings[transparent-header-button-text-h-color]","color",'.ast-theme-transparent-header [CLASS*="ast-header-button-"] ..ast-custom-button:hover'),astra_css("astra-settings[transparent-header-button-bg-h-color]","background",'.ast-theme-transparent-header [CLASS*="ast-header-button-"] .ast-custom-button:hover'),astra_css("astra-settings[transparent-header-button-border-color]","border-color",'.ast-theme-transparent-header [CLASS*="ast-header-button-"] .ast-custom-button'),astra_css("astra-settings[transparent-header-button-border-h-color]","border-color",'.ast-theme-transparent-header [CLASS*="ast-header-button-"] ..ast-custom-button:hover'),e='.ast-theme-transparent-header [data-section="section-header-mobile-trigger"]';astra_css("astra-settings[transparent-header-toggle-btn-color]","fill",e+" .ast-button-wrap .mobile-menu-toggle-icon .ast-mobile-svg"),astra_css("astra-settings[transparent-header-toggle-btn-color]","color",e+" .ast-button-wrap .mobile-menu-wrap .mobile-menu"),astra_css("astra-settings[transparent-header-toggle-btn-bg-color]","background",e+" .ast-button-wrap .menu-toggle.ast-mobile-menu-trigger-fill"),astra_css("astra-settings[transparent-header-toggle-border-color]","border-color",e+" .ast-button-wrap .menu-toggle.ast-mobile-menu-trigger-outline"),astra_css("astra-settings[transparent-account-icon-color]","fill",".ast-theme-transparent-header .ast-header-account-wrap .ast-header-account-type-icon .ahfb-svg-iconset svg path:not(.ast-hf-account-unfill), .ast-theme-transparent-header .ast-header-account-wrap .ast-header-account-type-icon .ahfb-svg-iconset svg circle"),astra_css("astra-settings[transparent-account-type-text-color]","color",".ast-theme-transparent-header .ast-header-account-wrap .ast-header-account-text"),astra_css("astra-settings[transparent-account-menu-color]","color",".ast-theme-transparent-header .ast-header-account-wrap .main-header-menu .menu-item > .menu-link"),astra_css("astra-settings[transparent-account-menu-h-color]","color",".ast-theme-transparent-header .ast-header-account-wrap .main-header-menu .menu-item:hover > .menu-link"),astra_css("astra-settings[transparent-account-menu-a-color]","color",".ast-theme-transparent-header .ast-header-account-wrap .main-header-menu .menu-item.current-menu-item > .menu-link"),astra_css("astra-settings[transparent-account-menu-bg-obj]","background",".ast-theme-transparent-header .ast-header-account-wrap .account-main-navigation ul"),astra_css("astra-settings[transparent-account-menu-h-bg-color]","background",".ast-theme-transparent-header .ast-header-account-wrap .account-main-navigation .menu-item:hover > .menu-link"),astra_css("astra-settings[transparent-account-menu-a-bg-color]","background",".ast-theme-transparent-header .ast-header-account-wrap .account-main-navigation .menu-item.current-menu-item > .menu-link")})(jQuery);