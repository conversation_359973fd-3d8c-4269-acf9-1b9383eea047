(()=>{var t,e,n,o,s,i="h1, .entry-content h1, h2, .entry-content h2, h3, .entry-content h3, h4, .entry-content h4, h5, .entry-content h5, h6, .entry-content h6";function r(s){let i="",t="";astraCustomizer.includeAnchorsInHeadindsCss&&(i=",.entry-content "+s+" a"),astraCustomizer.font_weights_widget_title_support&&(t=","+s+".widget-title"),astra_generate_outside_font_family_css("astra-settings[font-family-"+s+"]",s+", .entry-content "+s+i,!0,'.ast-sg-typo-field[data-for="'+s+'"] .ast-sg-font-family'),astra_generate_font_weight_css("astra-settings[font-family-"+s+"]","astra-settings[font-weight-"+s+"]","font-weight",s+", .entry-content "+s+i+t),wp.customize("astra-settings[font-extras-"+s+"]",function(t){t.bind(function(t){let e="",n="";astraCustomizer.page_builder_button_style_css&&(e=",.elementor-widget-heading "+s+".elementor-heading-title");var o=s+", .entry-content "+s+e+i,o=(t["line-height"]&&t["line-height-unit"]&&(n=(n+=o+"{")+"line-height : "+t["line-height"]+t["line-height-unit"]+";}",o=new CustomEvent("AstraStyleGuideElementUpdated",{detail:{value:t["line-height"]+t["line-height-unit"],selector:'.ast-sg-typo-field[data-for="'+s+'"] .ast-sg-line-height'}}),document.dispatchEvent(o)),s+", .entry-content "+s+i);(t["letter-spacing"]||t["text-decoration"]||t["text-transform"])&&(n+=o+"{",t["letter-spacing"]&&t["letter-spacing-unit"]&&(n+="letter-spacing : "+t["letter-spacing"]+t["letter-spacing-unit"]+";"),t["text-decoration"]&&(n+="text-decoration : "+t["text-decoration"]+";"),t["text-transform"]&&(n+="text-transform : "+t["text-transform"]+";"),n+="}"),astra_add_dynamic_css("font-extras-"+s,n)})})}astraHeadingColorOptions.maybeApplyHeadingColorForTitle&&(i+=",.ast-archive-title, .entry-title a"),astra_css("astra-settings[heading-base-color]","color",i),r("h1"),r("h2"),r("h3"),r("h4"),r("h5"),r("h6");let a="";astraCustomizer.astra_woo_btn_global_compatibility&&(a=", .woocommerce a.button, .woocommerce button.button, .woocommerce .woocommerce-message a.button, .woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt, .woocommerce input.button,.woocommerce-cart table.cart td.actions .button, .woocommerce form.checkout_coupon .button, .woocommerce #respond input#submit, .wc-block-grid__products .wc-block-grid__product .wp-block-button__link"),astraCustomizer.page_builder_button_style_css?(s=o=n=e=t=i="","color-typo"!=astraCustomizer.elementor_default_color_font_setting&&"typo"!=astraCustomizer.elementor_default_color_font_setting||(e=",.elementor-widget-button .elementor-button.elementor-size-sm, .elementor-widget-button .elementor-button.elementor-size-xs, .elementor-widget-button .elementor-button.elementor-size-md, .elementor-widget-button .elementor-button.elementor-size-lg, .elementor-widget-button .elementor-button.elementor-size-xl, .elementor-widget-button .elementor-button",s=o=n=t=i=",.elementor-widget-button .elementor-button, .elementor-widget-button .elementor-button:visited"),astra_generate_outside_font_family_css("astra-settings[font-family-button]",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .wp-block-button .wp-block-button__link, form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button'+astraCustomizer.v4_2_2_core_form_btns_styling+i+a),astra_generate_font_weight_css("astra-settings[font-family-button]","astra-settings[font-weight-button]","font-weight",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .wp-block-button .wp-block-button__link, form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button'+astraCustomizer.v4_2_2_core_form_btns_styling+t+a),astra_font_extras_css("font-extras-button",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], body .wp-block-button .wp-block-button__link, form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button'+astraCustomizer.v4_2_2_core_form_btns_styling+n+a+astraCustomizer.improved_button_selector),astra_responsive_font_size("astra-settings[font-size-button]",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .wp-block-button .wp-block-button__link, form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button'+astraCustomizer.v4_2_2_core_form_btns_styling+e+a),astra_css("astra-settings[theme-btn-line-height]","line-height",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .wp-block-button .wp-block-button__link, form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button'+astraCustomizer.v4_2_2_core_form_btns_styling+o+a),astra_css("astra-settings[theme-btn-letter-spacing]","letter-spacing",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"], .wp-block-button .wp-block-button__link, form[CLASS*="wp-block-search__"].wp-block-search .wp-block-search__inside-wrapper .wp-block-search__button'+astraCustomizer.v4_2_2_core_form_btns_styling+s+a,"px")):(astra_generate_outside_font_family_css("astra-settings[font-family-button]",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"]'+astraCustomizer.v4_2_2_core_form_btns_styling+a),astra_generate_font_weight_css("astra-settings[font-family-button]","astra-settings[font-weight-button]","font-weight",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"]'+astraCustomizer.v4_2_2_core_form_btns_styling+a),astra_font_extras_css("font-extras-button",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"]'+astraCustomizer.v4_2_2_core_form_btns_styling+a),astra_responsive_font_size("astra-settings[font-size-button]",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"]'+astraCustomizer.v4_2_2_core_form_btns_styling+a),astra_css("astra-settings[theme-btn-line-height]","line-height",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"]'+astraCustomizer.v4_2_2_core_form_btns_styling+a),astra_css("astra-settings[theme-btn-letter-spacing]","letter-spacing",'button, .ast-button, .ast-custom-button, input#submit, input[type="button"], input[type="submit"], input[type="reset"]'+astraCustomizer.v4_2_2_core_form_btns_styling+a,"px")),i="body .wp-block-buttons .wp-block-button.is-style-outline .wp-block-button__link.wp-element-button, body .ast-outline-button, body .wp-block-uagb-buttons-child .uagb-buttons-repeater.ast-outline-button",astra_generate_outside_font_family_css("astra-settings[secondary-font-family-button]",i),astra_generate_font_weight_css("astra-settings[secondary-font-family-button]","astra-settings[secondary-font-weight-button]","font-weight",i),astra_font_extras_css("secondary-font-extras-button",i),astra_responsive_font_size("astra-settings[secondary-font-size-button]",i),astra_css("astra-settings[secondary-theme-btn-line-height]","line-height",i),astra_css("astra-settings[secondary-theme-btn-letter-spacing]","letter-spacing",i,"px")})(jQuery);