jQuery,astra_responsive_font_size("astra-settings[breadcrumb-font-size]",".ast-breadcrumbs-wrapper .trail-items span, .ast-breadcrumbs-wrapper a, .ast-breadcrumbs-wrapper .breadcrumb_last, .ast-breadcrumbs-wrapper span,  .ast-breadcrumbs-wrapper .breadcrumbs, .ast-breadcrumbs-wrapper .current-item, .ast-breadcrumbs-wrapper .last, .ast-breadcrumbs-wrapper .separator, .ast-breadcrumbs-wrapper .breadcrumb-item, .ast-breadcrumbs-wrapper .breadcrumb-item.active, .ast-breadcrumbs-wrapper .breadcrumb-item:after, .ast-breadcrumbs-inner nav, .ast-breadcrumbs-inner nav .breadcrumb-item, .ast-breadcrumbs-inner nav .breadcrumb-item:after"),astra_generate_outside_font_family_css("astra-settings[breadcrumb-font-family]",".ast-breadcrumbs-wrapper .trail-items span, .ast-breadcrumbs-wrapper a, .ast-breadcrumbs-wrapper .breadcrumb_last, .ast-breadcrumbs-wrapper span,  .ast-breadcrumbs-wrapper .breadcrumbs, .ast-breadcrumbs-wrapper .current-item, .ast-breadcrumbs-wrapper .last, .ast-breadcrumbs-wrapper .separator, .ast-breadcrumbs-wrapper .breadcrumb-item, .ast-breadcrumbs-wrapper .breadcrumb-item.active, .ast-breadcrumbs-wrapper .breadcrumb-item:after, .ast-breadcrumbs-inner nav, .ast-breadcrumbs-inner nav .breadcrumb-item, .ast-breadcrumbs-inner nav .breadcrumb-item:after"),astra_generate_font_weight_css("astra-settings[breadcrumb-font-family]","astra-settings[breadcrumb-font-weight]","font-weight",".ast-breadcrumbs-wrapper .trail-items span, .ast-breadcrumbs-wrapper a, .ast-breadcrumbs-wrapper .breadcrumb_last, .ast-breadcrumbs-wrapper span,  .ast-breadcrumbs-wrapper .breadcrumbs, .ast-breadcrumbs-wrapper .current-item, .ast-breadcrumbs-wrapper .last, .ast-breadcrumbs-wrapper .separator, .ast-breadcrumbs-wrapper .breadcrumb-item, .ast-breadcrumbs-wrapper .breadcrumb-item.active, .ast-breadcrumbs-wrapper .breadcrumb-item:after, .ast-breadcrumbs-inner nav, .ast-breadcrumbs-inner nav .breadcrumb-item, .ast-breadcrumbs-inner nav .breadcrumb-item:after"),astra_font_extras_css("breadcrumb-font-extras",".ast-breadcrumbs-wrapper .ast-breadcrumbs-name, .ast-breadcrumbs-wrapper .ast-breadcrumbs-item, .ast-breadcrumbs-wrapper .ast-breadcrumbs .separator, .ast-breadcrumbs-wrapper a, .ast-breadcrumbs-wrapper .breadcrumb_last, .ast-breadcrumbs-wrapper span, .ast-breadcrumbs-wrapper a, .ast-breadcrumbs-wrapper .breadcrumbs, .ast-breadcrumbs-wrapper .current-item, .ast-breadcrumbs-wrapper a, .ast-breadcrumbs-wrapper .last, .ast-breadcrumbs-wrapper .separator, .ast-breadcrumbs-wrapper .breadcrumb-item, .ast-breadcrumbs-wrapper .breadcrumb-item.active, .ast-breadcrumbs-wrapper .breadcrumb-item:after, .ast-breadcrumbs-inner nav, .ast-breadcrumbs-inner nav .breadcrumb-item, .ast-breadcrumbs-inner nav .breadcrumb-item:after"),astra_color_responsive_css("breadcrumb","astra-settings[breadcrumb-active-color-responsive]","color",".ast-breadcrumbs-wrapper .trail-items .trail-end, .ast-breadcrumbs-wrapper #ast-breadcrumbs-yoast .breadcrumb_last, .ast-breadcrumbs-wrapper .current-item, .ast-breadcrumbs-wrapper .last, .ast-breadcrumbs-inner, .ast-breadcrumbs-wrapper .breadcrumb-item.active"),astra_color_responsive_css("breadcrumb","astra-settings[breadcrumb-text-color-responsive]","color",".ast-breadcrumbs-wrapper .trail-items a, .ast-breadcrumbs-wrapper #ast-breadcrumbs-yoast a, .ast-breadcrumbs-wrapper .breadcrumbs a, .ast-breadcrumbs-wrapper .rank-math-breadcrumb a, .ast-breadcrumbs-wrapper .breadcrumb-item a"),astra_color_responsive_css("breadcrumb","astra-settings[breadcrumb-hover-color-responsive]","color",".ast-breadcrumbs-wrapper .trail-items a:hover, .ast-breadcrumbs-wrapper #ast-breadcrumbs-yoast a:hover, .ast-breadcrumbs-wrapper .breadcrumbs a:hover, .ast-breadcrumbs-wrapper .rank-math-breadcrumb a:hover, .ast-breadcrumbs-wrapper .breadcrumb-item a:hover"),astra_color_responsive_css("breadcrumb","astra-settings[breadcrumb-separator-color]","color",".ast-breadcrumbs-wrapper .trail-items li::after, .ast-breadcrumbs-wrapper #ast-breadcrumbs-yoast, .ast-breadcrumbs-wrapper .breadcrumbs, .ast-breadcrumbs-wrapper .rank-math-breadcrumb .separator, .ast-breadcrumbs-wrapper .breadcrumb-item:after"),astra_color_responsive_css("breadcrumb","astra-settings[breadcrumb-bg-color]","background-color",".ast-breadcrumbs-wrapper, .main-header-bar.ast-header-breadcrumb, .ast-primary-sticky-header-active .main-header-bar.ast-header-breadcrumb"),astra_css("astra-settings[breadcrumb-alignment]","text-align",".ast-breadcrumbs-wrapper"),wp.customize("astra-settings[breadcrumb-spacing]",function(r){r.bind(function(r){var a=wp.customize("astra-settings[breadcrumb-position]").get();"astra_header_markup_after"==a||"astra_header_after"==a?astra_responsive_spacing("astra-settings[breadcrumb-spacing]",".main-header-bar.ast-header-breadcrumb","padding",["top","right","bottom","left"]):"astra_masthead_content"==a?astra_responsive_spacing("astra-settings[breadcrumb-spacing]",".ast-breadcrumbs-wrapper .ast-breadcrumbs-inner #ast-breadcrumbs-yoast, .ast-breadcrumbs-wrapper .ast-breadcrumbs-inner .breadcrumbs, .ast-breadcrumbs-wrapper .ast-breadcrumbs-inner .rank-math-breadcrumb, .ast-breadcrumbs-wrapper .ast-breadcrumbs-inner .ast-breadcrumbs, .ast-breadcrumbs-wrapper .ast-breadcrumbs-inner","padding",["top","right","bottom","left"]):astra_responsive_spacing("astra-settings[breadcrumb-spacing]",".ast-breadcrumbs-wrapper #ast-breadcrumbs-yoast, .ast-breadcrumbs-wrapper .breadcrumbs, .ast-breadcrumbs-wrapper .rank-math-breadcrumb, .ast-breadcrumbs-wrapper .ast-breadcrumbs, .ast-breadcrumbs-wrapper .ast-breadcrumbs-inner","padding",["top","right","bottom","left"])})}),wp.customize("astra-settings[breadcrumb-separator-selector]",function(r){r.bind(function(r){var a=wp.customize.value("astra-settings[breadcrumb-separator]")(),e="",e=(e+=".trail-items li::after {")+('content: "'+("unicode"!==r?r:a)+'";')+"} ";astra_add_dynamic_css("breadcrumb-separator-selector",e)})}),wp.customize("astra-settings[breadcrumb-separator]",function(r){r.bind(function(r){var a="",a=(a+=".trail-items li::after {")+('content: "'+r+'";')+"} ";astra_add_dynamic_css("breadcrumb-separator",a)})});