!function(){"use strict";var e=window.wp.element,t=window.wp.i18n,n=window.wp.components;const o=()=>{const{themeName:o,themeIconUrl:r}=astraElementorEditor,[c,a]=(0,e.useState)(!1);(0,e.useEffect)((()=>{const e=e=>{e.target.closest(".elementor-control-astra_section")&&setTimeout((()=>{const e=document.querySelector(".elementor-control-astra_section"),t=e?.classList.contains("e-open")||!1;a(t)}),0)};return document.addEventListener("click",e,!0),()=>{document.removeEventListener("click",e,!0),a(!1)}}),[]);
// translators: %s is the Astra theme name.
const s=(0,t.sprintf)((0,t.__)("%s Settings","astra"),o);return(0,e.createElement)(n.Tooltip,{text:s,placement:"bottom",delay:150},(0,e.createElement)("button",{value:"Astra Settings","aria-label":s,title:s,onClick:()=>{const e=document.querySelector('[value="document-settings"]');e?.click();const t=document.querySelector(".elementor-control-astra_section button");t?.click(),a(!c)},style:{background:c?"#ffffff29":"transparent",display:"inline-flex",cursor:"pointer",border:"none",borderRadius:"4px",padding:"7px"}},(0,e.createElement)("img",{width:20,height:20,src:r,alt:(0,t.sprintf)((0,t.__)("%s Theme Icon","astra"),o)})))};"undefined"!=typeof elementor&&elementor.once?elementor.once("preview:loaded",(()=>{try{const t=document.querySelector("#elementor-editor-wrapper-v2 button[value=Structure]"),n=t?.parentElement;if(t&&n){const t=document.createElement("span");n.parentElement.appendChild(t),(0,e.createRoot)(t)?.render((0,e.createElement)(o,null))}}catch(e){console.error("Error initializing Astra settings:",e)}elementor.channels.editor.on("namespace:editor:astraRefresh",(()=>{$e?.run&&$e.run("document/save/auto",{force:!0}).then((()=>{"function"==typeof elementor?.reloadPreview&&elementor.reloadPreview()})).catch((e=>{console.error("namespace:editor:astraRefresh - Auto-save failed",e)}))}))})):console.warn("Elementor is not available.")}();