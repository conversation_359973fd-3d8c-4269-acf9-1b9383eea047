(()=>{var i=AstraBuilderHTMLData.tablet_break_point||768,l=AstraBuilderHTMLData.mobile_break_point||544;astra_builder_html_css("footer",AstraBuilderHTMLData.component_limit);for(var t=1;t<=AstraBuilderHTMLData.component_limit;t++)(a=>{wp.customize("astra-settings[footer-html-"+a+"-alignment]",function(t){t.bind(function(t){var e;""==t.desktop&&""==t.tablet&&""==t.mobile||(e="",e=(e=(e=(e=(e=(e+='.footer-widget-area[data-section="section-fb-html-'+a+'"] .ast-builder-html-element {')+"text-align: "+t.desktop+";} ")+"@media (max-width: "+i+'px) {.footer-widget-area[data-section="section-fb-html-'+a+'"] .ast-builder-html-element {')+"text-align: "+t.tablet+";} ")+"} @media (max-width: "+l+"px) {")+'.footer-widget-area[data-section="section-fb-html-'+a+'"] .ast-builder-html-element {text-align: '+t.mobile+";} } ",astra_add_dynamic_css("footer-html-"+a+"-alignment",e))})})})(t)})(jQuery);