(()=>{var r=astraBuilderPreview.tablet_break_point||768,o=astraBuilderPreview.mobile_break_point||544,t="section-above-footer-builder",e='.site-above-footer-wrap[data-section="section-above-footer-builder"]';astra_css("astra-settings[hba-footer-vertical-alignment]","align-items",e+" .ast-builder-grid-row, "+e+" .site-footer-section"),wp.customize("astra-settings[hba-footer-separator]",function(t){t.bind(function(t){var a="";""!==t&&(a=(a=e+" {")+"border-top-width: "+t+"px;border-top-style: solid} "),astra_add_dynamic_css("hba-footer-separator",a)})}),wp.customize("astra-settings[hba-inner-spacing]",function(t){t.bind(function(t){var a="";""!=t.desktop&&(a=(a+=e+" .ast-builder-grid-row {")+"grid-column-gap: "+t.desktop+"px;} "),""!=t.tablet&&(a=(a=(a+="@media (max-width: "+r+"px) {")+e+" .ast-builder-grid-row {grid-column-gap: "+t.tablet+"px;")+"grid-row-gap: "+t.tablet+"px;} } "),""!=t.mobile&&(a=(a=(a+="@media (max-width: "+o+"px) {")+e+" .ast-builder-grid-row {grid-column-gap: "+t.mobile+"px;")+"grid-row-gap: "+t.mobile+"px;} } "),astra_add_dynamic_css("hba-inner-spacing-toggle-button",a)})}),wp.customize("astra-settings[hba-footer-top-border-color]",function(t){t.bind(function(t){var a="";""!==t&&(a=(a=e+" {")+"border-top-color: "+t+";border-top-style: solid} "),astra_add_dynamic_css("hba-footer-top-border-color",a)})}),wp.customize("astra-settings[hba-footer-layout-width]",function(t){t.bind(function(t){var a="";"content"==t&&(a=(a=e+" .ast-builder-grid-row {")+"max-width: "+AstraBuilderPrimaryFooterData.footer_content_width+"px;margin-left: auto;margin-right: auto;} "),"full"==t&&(a=e+" .ast-builder-grid-row {",a+="max-width: 100%;padding-right: 35px; padding-left: 35px;} "),astra_add_dynamic_css("hba-footer-layout-width",a)})}),astra_apply_responsive_background_css("astra-settings[hba-footer-bg-obj-responsive]",e,"desktop"),astra_apply_responsive_background_css("astra-settings[hba-footer-bg-obj-responsive]",e,"tablet"),astra_apply_responsive_background_css("astra-settings[hba-footer-bg-obj-responsive]",e,"mobile"),astra_builder_advanced_css(t,e),astra_builder_visibility_css(t,e,"grid")})(jQuery);