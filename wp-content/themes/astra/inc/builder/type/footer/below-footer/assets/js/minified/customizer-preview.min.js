(()=>{var o=astraBuilderPreview.tablet_break_point||768,a=astraBuilderPreview.mobile_break_point||544,t="section-below-footer-builder",e='.site-below-footer-wrap[data-section="section-below-footer-builder"]';astra_css("astra-settings[hbb-footer-vertical-alignment]","align-items",e+" .ast-builder-grid-row, "+e+" .site-footer-section"),wp.customize("astra-settings[hbb-inner-spacing]",function(t){t.bind(function(t){var r="";""!=t.desktop&&(r=(r+=e+" .ast-builder-grid-row {")+"grid-column-gap: "+t.desktop+"px;} "),""!=t.tablet&&(r=(r=(r+="@media (max-width: "+o+"px) {")+e+" .ast-builder-grid-row {grid-column-gap: "+t.tablet+"px;")+"grid-row-gap: "+t.tablet+"px;} } "),""!=t.mobile&&(r=(r=(r+="@media (max-width: "+a+"px) {")+e+" .ast-builder-grid-row {grid-column-gap: "+t.mobile+"px;")+"grid-row-gap: "+t.mobile+"px;} } "),astra_add_dynamic_css("hbb-inner-spacing-toggle-button",r)})}),wp.customize("astra-settings[hbb-footer-separator]",function(t){t.bind(function(t){var r="";""!==t&&(r=(r=e+" {")+"border-top-width: "+t+"px;border-top-style: solid} "),astra_add_dynamic_css("hbb-footer-separator",r)})}),wp.customize("astra-settings[hbb-footer-top-border-color]",function(t){t.bind(function(t){var r="";""!==t&&(r=(r=e+" {")+"border-top-color: "+t+";border-top-style: solid} "),astra_add_dynamic_css("hbb-footer-top-border-color",r)})}),wp.customize("astra-settings[hbb-footer-layout-width]",function(t){t.bind(function(t){var r="";"content"==t&&(r=(r=e+" .ast-builder-grid-row {")+"max-width: "+AstraBuilderPrimaryFooterData.footer_content_width+"px;margin-left: auto;margin-right: auto;} "),"full"==t&&(r=e+" .ast-builder-grid-row {",r+="max-width: 100%;padding-right: 35px; padding-left: 35px;} "),astra_add_dynamic_css("hbb-footer-layout-width",r)})}),astra_apply_responsive_background_css("astra-settings[hbb-footer-bg-obj-responsive]",e,"desktop"),astra_apply_responsive_background_css("astra-settings[hbb-footer-bg-obj-responsive]",e,"tablet"),astra_apply_responsive_background_css("astra-settings[hbb-footer-bg-obj-responsive]",e,"mobile"),astra_builder_advanced_css(t,e),astra_builder_visibility_css(t,e,"grid")})(jQuery);