<?php
/**
 * Copyright control - Dynamic CSS
 *
 * @package Astra Builder
 * @since 3.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Copyright CSS
 */
add_filter( 'astra_dynamic_theme_css', 'astra_fb_copyright_dynamic_css' );

/**
 * Dynamic CSS
 *
 * @param  string $dynamic_css          Astra Dynamic CSS.
 * @param  string $dynamic_css_filtered Astra Dynamic CSS Filters.
 * @return String Generated dynamic CSS for Heading Colors.
 *
 * @since 3.0.0
 */
function astra_fb_copyright_dynamic_css( $dynamic_css, $dynamic_css_filtered = '' ) {

	if ( ! Astra_Builder_Helper::is_component_loaded( 'copyright', 'footer' ) ) {
		return $dynamic_css;
	}

	$_section = 'section-footer-copyright';

	$selector = '.ast-footer-copyright ';

	$visibility_selector = '.ast-footer-copyright.ast-builder-layout-element';

	$alignment = astra_get_option( 'footer-copyright-alignment' );

	$desktop_alignment = isset( $alignment['desktop'] ) ? $alignment['desktop'] : '';
	$tablet_alignment  = isset( $alignment['tablet'] ) ? $alignment['tablet'] : '';
	$mobile_alignment  = isset( $alignment['mobile'] ) ? $alignment['mobile'] : '';

	$margin = astra_get_option( $_section . '-margin' );

	/**
	 * Copyright CSS.
	 */
	$css_output_desktop = array(
		'.ast-footer-copyright' => array(
			'text-align' => $desktop_alignment,
		),
		$selector               => array(
			'color'         => astra_get_option( 'footer-copyright-color', astra_get_option( 'text-color' ) ),
			// Margin CSS.
			'margin-top'    => astra_responsive_spacing( $margin, 'top', 'desktop' ),
			'margin-bottom' => astra_responsive_spacing( $margin, 'bottom', 'desktop' ),
			'margin-left'   => astra_responsive_spacing( $margin, 'left', 'desktop' ),
			'margin-right'  => astra_responsive_spacing( $margin, 'right', 'desktop' ),
		),
	);

	$css_output_tablet = array(
		'.ast-footer-copyright' => array(
			'text-align' => $tablet_alignment,
		),
		$selector               => array(
			// Margin CSS.
			'margin-top'    => astra_responsive_spacing( $margin, 'top', 'tablet' ),
			'margin-bottom' => astra_responsive_spacing( $margin, 'bottom', 'tablet' ),
			'margin-left'   => astra_responsive_spacing( $margin, 'left', 'tablet' ),
			'margin-right'  => astra_responsive_spacing( $margin, 'right', 'tablet' ),
		),
	);

	$css_output_mobile = array(
		'.ast-footer-copyright' => array(
			'text-align' => $mobile_alignment,
		),
		$selector               => array(
			// Margin CSS.
			'margin-top'    => astra_responsive_spacing( $margin, 'top', 'mobile' ),
			'margin-bottom' => astra_responsive_spacing( $margin, 'bottom', 'mobile' ),
			'margin-left'   => astra_responsive_spacing( $margin, 'left', 'mobile' ),
			'margin-right'  => astra_responsive_spacing( $margin, 'right', 'mobile' ),
		),
	);

	/* Parse CSS from array() */
	$css_output  = astra_parse_css( $css_output_desktop );
	$css_output .= astra_parse_css( $css_output_tablet, '', astra_get_tablet_breakpoint() );
	$css_output .= astra_parse_css( $css_output_mobile, '', astra_get_mobile_breakpoint() );

	$dynamic_css .= $css_output;

	$dynamic_css .= Astra_Builder_Base_Dynamic_CSS::prepare_advanced_typography_css( $_section, $selector );

	$dynamic_css .= Astra_Builder_Base_Dynamic_CSS::prepare_visibility_css( $_section, $visibility_selector );

	return $dynamic_css;
}
