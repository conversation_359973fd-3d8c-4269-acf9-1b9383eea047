(()=>{var o=astraBuilderPreview.tablet_break_point||768,i=astraBuilderPreview.mobile_break_point||544,e=".ast-header-account-wrap";wp.customize("astra-settings[header-account-icon-color]",function(t){t.bind(function(t){var a=(a=e+" .ast-header-account-type-icon .ahfb-svg-iconset svg path:not( .ast-hf-account-unfill ), "+e+" .ast-header-account-type-icon .ahfb-svg-iconset svg circle, .ast-mobile-popup-content"+e+" .ast-header-account-type-icon .ahfb-svg-iconset svg path:not( .ast-hf-account-unfill ), .ast-mobile-popup-content "+e+" .ast-header-account-type-icon .ahfb-svg-iconset svg circle {")+("fill: "+(t=t||"inherit")+";")+"} ";astra_add_dynamic_css("header-account-icon-color",a)})}),astra_responsive_font_size("astra-settings[font-size-section-header-account]",e+" .ast-header-account-text"),astra_css("astra-settings[header-account-type-text-color]","color",e+" .ast-header-account-text, .ast-mobile-popup-content "+e+" .ast-header-account-text"),wp.customize("astra-settings[header-account-icon-size]",function(t){t.bind(function(t){var a;""==t.desktop&&""==t.tablet&&""==t.mobile||(a=(a=(a=(a=(a=(a=(a="")+e+" .ast-header-account-type-icon .ahfb-svg-iconset svg {height: "+t.desktop+"px;")+"width: "+t.desktop+"px;} ")+"@media (max-width: "+o+"px) {"+e+" .ast-header-account-type-icon .ahfb-svg-iconset svg {")+"height: "+t.tablet+"px;width: "+t.tablet+"px;} } ")+"@media (max-width: "+i+"px) {"+e+" .ast-header-account-type-icon .ahfb-svg-iconset svg {")+"height: "+t.mobile+"px;width: "+t.mobile+"px;} } ",astra_add_dynamic_css("header-account-icon-size",a))})}),wp.customize("astra-settings[header-account-image-width]",function(t){t.bind(function(t){var a;""==t.desktop&&""==t.tablet&&""==t.mobile||(a="",a=(a=(a=(a=(a=(a+=e+" .ast-header-account-type-avatar .avatar {")+"width: "+t.desktop+"px;} ")+"@media (max-width: "+o+"px) {"+e+" .ast-header-account-type-avatar .avatar {")+"width: "+t.tablet+"px;} ")+"} @media (max-width: "+i+"px) {")+e+" .ast-header-account-type-avatar .avatar {width: "+t.mobile+"px;} } ",astra_add_dynamic_css("header-account-image-width",a))})}),wp.customize("astra-settings[header-account-margin]",function(t){t.bind(function(t){var a,e;""==t.desktop.bottom&&""==t.desktop.top&&""==t.desktop.left&&""==t.desktop.right&&""==t.tablet.bottom&&""==t.tablet.top&&""==t.tablet.left&&""==t.tablet.right&&""==t.mobile.bottom&&""==t.mobile.top&&""==t.mobile.left&&""==t.mobile.right||(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e="")+(a=".ast-header-account-wrap")+" {margin-left: "+t.desktop.left+t["desktop-unit"]+";")+"margin-right: "+t.desktop.right+t["desktop-unit"]+";")+"margin-top: "+t.desktop.top+t["desktop-unit"]+";")+"margin-bottom: "+t.desktop.bottom+t["desktop-unit"]+";")+"} @media (max-width: "+o+"px) {")+a+" {margin-left: "+t.tablet.left+t["tablet-unit"]+";")+"margin-right: "+t.tablet.right+t["tablet-unit"]+";")+"margin-top: "+t.tablet.top+t["desktop-unit"]+";")+"margin-bottom: "+t.tablet.bottom+t["desktop-unit"]+";} ")+"} @media (max-width: "+i+"px) {")+a+" {margin-left: "+t.mobile.left+t["mobile-unit"]+";")+"margin-right: "+t.mobile.right+t["mobile-unit"]+";")+"margin-top: "+t.mobile.top+t["desktop-unit"]+";")+"margin-bottom: "+t.mobile.bottom+t["desktop-unit"]+";} } ",astra_add_dynamic_css("header-account-margin",e))})}),astra_builder_visibility_css("section-header-account",'.ast-header-account[data-section="section-header-account"]')})(jQuery);