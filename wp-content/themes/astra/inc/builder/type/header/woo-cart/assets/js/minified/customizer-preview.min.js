(i=>{var s=".ast-site-header-cart",t=".astra-cart-drawer",a=".woocommerce-js ",n=astraBuilderCartPreview.tablet_break_point||768,d=astraBuilderCartPreview.mobile_break_point||544;astra_css("astra-settings[header-woo-cart-icon-color]","color",s+" .ast-cart-menu-wrap .count, "+s+" .ast-cart-menu-wrap .count:after,"+s+" .ast-woo-header-cart-info-wrap,"+s+" .ast-site-header-cart .ast-addon-cart-wrap"),astra_css("astra-settings[header-woo-cart-icon-color]","border-color",s+" .ast-cart-menu-wrap .count, "+s+" .ast-cart-menu-wrap .count:after"),astra_css("astra-settings[header-woo-cart-icon-color]","border-color",".ast-menu-cart-fill .ast-cart-menu-wrap .count, .ast-menu-cart-fill .ast-cart-menu-wrap"),astra_color_responsive_css("woo-cart-text-color","astra-settings[header-woo-cart-text-color]","color",".astra-cart-drawer-title, .ast-site-header-cart-data span, .ast-site-header-cart-data strong, .ast-site-header-cart-data .woocommerce-mini-cart__empty-message, .ast-site-header-cart-data .total .woocommerce-Price-amount, .ast-site-header-cart-data .total .woocommerce-Price-amount .woocommerce-Price-currencySymbol, .ast-header-woo-cart .ast-site-header-cart .ast-site-header-cart-data .widget_shopping_cart .mini_cart_item a.remove,"+t+" .widget_shopping_cart_content span, "+t+" .widget_shopping_cart_content strong,"+t+" .woocommerce-mini-cart__empty-message, .astra-cart-drawer .woocommerce-mini-cart *, "+t+" .astra-cart-drawer-title"),astra_color_responsive_css("woo-cart-border-color","astra-settings[header-woo-cart-text-color]","border-color",".ast-site-header-cart .ast-site-header-cart-data .widget_shopping_cart .mini_cart_item a.remove, "+t+" .widget_shopping_cart .mini_cart_item a.remove"),astra_color_responsive_css("woo-cart-link-color","astra-settings[header-woo-cart-link-color]","color",s+" .ast-site-header-cart-data .widget_shopping_cart_content a:not(.button),"+t+" .widget_shopping_cart_content a:not(.button)"),astra_color_responsive_css("woo-cart-background-color","astra-settings[header-woo-cart-background-color]","background-color",".ast-site-header-cart .widget_shopping_cart, .astra-cart-drawer"),astra_color_responsive_css("woo-cart-border-color","astra-settings[header-woo-cart-background-color]","border-color",".ast-site-header-cart .widget_shopping_cart, .astra-cart-drawer"),astra_color_responsive_css("woo-cart-border-bottom-color","astra-settings[header-woo-cart-background-color]","border-bottom-color",".ast-site-header-cart .widget_shopping_cart:before, .ast-site-header-cart .widget_shopping_cart:after, .open-preview-woocommerce-cart .ast-site-header-cart .widget_shopping_cart:before, #astra-mobile-cart-drawer, .astra-cart-drawer"),astra_color_responsive_css("woo-cart-background-hover-color","astra-settings[header-woo-cart-background-hover-color]","background-color",".ast-site-header-cart .widget_shopping_cart:hover, #astra-mobile-cart-drawer:hover"),astra_color_responsive_css("woo-cart-border-color","astra-settings[header-woo-cart-background-hover-color]","border-color",".ast-site-header-cart .widget_shopping_cart:hover, #astra-mobile-cart-drawer:hover"),astra_color_responsive_css("woo-cart-border-bottom-color","astra-settings[header-woo-cart-background-hover-color]","border-bottom-color",".ast-site-header-cart .widget_shopping_cart:hover,site-header-cart .widget_shopping_cart:hover:after, #astra-mobile-cart-drawer:hover,.ast-site-header-cart:hover .widget_shopping_cart:hover:before, .ast-site-header-cart:hover .widget_shopping_cart:hover:after, .open-preview-woocommerce-cart .ast-site-header-cart .widget_shopping_cart:hover:before"),astra_color_responsive_css("woo-cart-separator-colors","astra-settings[header-woo-cart-separator-color]","border-top-color",".ast-site-header-cart .widget_shopping_cart .woocommerce-mini-cart__total, #astra-mobile-cart-drawer .widget_shopping_cart .woocommerce-mini-cart__total, .astra-cart-drawer .astra-cart-drawer-header"),astra_color_responsive_css("woo-cart-border-bottom-colors","astra-settings[header-woo-cart-separator-color]","border-bottom-color",".ast-site-header-cart .widget_shopping_cart .woocommerce-mini-cart__total, #astra-mobile-cart-drawer .widget_shopping_cart .woocommerce-mini-cart__total, .astra-cart-drawer .astra-cart-drawer-header, .ast-site-header-cart .widget_shopping_cart .mini_cart_item, #astra-mobile-cart-drawer .widget_shopping_cart .mini_cart_item"),astra_color_responsive_css("woo-cart-link-hover-colors","astra-settings[header-woo-cart-link-hover-color]","color",".ast-site-header-cart .ast-site-header-cart-data .widget_shopping_cart_content a:not(.button):hover, .ast-site-header-cart .ast-site-header-cart-data .widget_shopping_cart .mini_cart_item a.remove:hover, .ast-site-header-cart .ast-site-header-cart-data .widget_shopping_cart .mini_cart_item:hover > a.remove,"+t+" .widget_shopping_cart_content a:not(.button):hover,"+t+" .widget_shopping_cart .mini_cart_item a.remove:hover,"+t+" .widget_shopping_cart .mini_cart_item:hover > a.remove"),astra_color_responsive_css("woo-cart-border-colors","astra-settings[header-woo-cart-link-hover-color]","border-color",s+" .ast-site-header-cart-data .widget_shopping_cart .mini_cart_item a.remove:hover,"+s+" .ast-site-header-cart-data .widget_shopping_cart .mini_cart_item:hover > a.remove,"+t+" .widget_shopping_cart .mini_cart_item a.remove:hover,"+t+" .widget_shopping_cart .mini_cart_item:hover > a.remove"),astra_color_responsive_css("woo-cart-btn-text-color","astra-settings[header-woo-cart-btn-text-color]","color",a+s+" .ast-site-header-cart-data .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.wc-forward:not(.checkout),"+a+t+" .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.wc-forward:not(.checkout)"),astra_color_responsive_css("woo-cart-btn-bg-color","astra-settings[header-woo-cart-btn-background-color]","background-color",s+" .ast-site-header-cart-data .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.wc-forward:not(.checkout),"+a+t+" .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.wc-forward:not(.checkout)"),astra_color_responsive_css("woo-cart-btn-hover-text-color","astra-settings[header-woo-cart-btn-text-hover-color]","color",s+" .ast-site-header-cart-data .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.wc-forward:not(.checkout):hover,"+a+t+" .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.wc-forward:not(.checkout):hover"),astra_color_responsive_css("woo-cart-btn-hover-bg-color","astra-settings[header-woo-cart-btn-bg-hover-color]","background-color",s+" .ast-site-header-cart-data .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.wc-forward:not(.checkout):hover,"+a+t+" .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.wc-forward:not(.checkout):hover"),astra_color_responsive_css("woo-cart-checkout-btn-text-color","astra-settings[header-woo-checkout-btn-text-color]","color",s+" .ast-site-header-cart-data .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward,"+t+" .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward"),astra_color_responsive_css("woo-cart-checkout-btn-border-color","astra-settings[header-woo-checkout-btn-background-color]","border-color",s+" .ast-site-header-cart-data .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward,"+t+" .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward"),astra_color_responsive_css("woo-cart-checkout-btn-background-color","astra-settings[header-woo-checkout-btn-background-color]","background-color",s+" .ast-site-header-cart-data .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward,"+t+" .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward"),astra_color_responsive_css("woo-cart-checkout-btn-hover-text-color","astra-settings[header-woo-checkout-btn-text-hover-color]","color",s+" .ast-site-header-cart-data .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward:hover,"+t+" .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward:hover"),astra_color_responsive_css("woo-cart-checkout-btn-bg-hover-background-color","astra-settings[header-woo-checkout-btn-bg-hover-color]","background-color",s+" .ast-site-header-cart-data .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward:hover,"+t+" .widget_shopping_cart_content .woocommerce-mini-cart__buttons a.button.checkout.wc-forward:hover"),wp.customize("astra-settings[woo-header-cart-icon-style]",function(t){t.bind(function(t){var a=i(document).find(".ast-site-header-cart");a.removeClass("ast-menu-cart-fill ast-menu-cart-outline ast-menu-cart-none"),a.addClass("ast-menu-cart-"+t);astra_add_dynamic_css("woo-header-cart-icon-style",".ast-site-header-cart a, .ast-site-header-cart a *{ transition: all 0s; } "),wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[woo-slide-in-cart-width]",function(t){t.bind(function(t){var a,{desktop:o,mobile:r,tablet:e}=t,s=t["desktop-unit"],c=t["tablet-unit"],t=t["mobile-unit"];"left"==wp.customize("astra-settings[woo-desktop-cart-flyout-direction]").get()?(a=".ast-desktop .astra-cart-drawer { width: "+o+s+"; left: -"+o+s+"; } ",a+=".ast-desktop .astra-cart-drawer.active { left: "+o+s+"; } "):a=".ast-desktop .astra-cart-drawer { width: "+o+s+"; left: 100%; } ",a=(a=(a=(a=a+"@media (max-width: "+n+"px ) and ( min-width: "+d+"px) {#astra-mobile-cart-drawer {")+"width: "+e+c+";}")+"}@media (max-width: "+d+"px) {")+"#astra-mobile-cart-drawer {width: "+r+t+";}}",500<o?i("#astra-mobile-cart-drawer .astra-cart-drawer-content").addClass("ast-large-view"):i("#astra-mobile-cart-drawer .astra-cart-drawer-content").removeClass("ast-large-view"),astra_add_dynamic_css("woo-slide-in-cart-width",a)})}),wp.customize("astra-settings[woo-header-cart-icon]",function(t){t.bind(function(t){i(document.body).trigger("wc_fragment_refresh")})}),wp.customize("astra-settings[woo-header-cart-click-action]",function(t){t.bind(function(t){i(document.body).trigger("wc_fragment_refresh"),wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[header-woo-cart-icon-hover-color]",function(t){t.bind(function(t){wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[header-woo-cart-icon-color]",function(t){t.bind(function(t){astra_add_dynamic_css("header-woo-cart-icon-color",".ast-menu-cart-fill .ast-cart-menu-wrap .count, .ast-menu-cart-fill .ast-cart-menu-wrap { background-color: "+t+"; } "),wp.customize.preview.send("refresh")})}),astra_css("astra-settings[woo-header-cart-border-width]","border-width",".ast-menu-cart-outline .ast-addon-cart-wrap, .ast-theme-transparent-header .ast-menu-cart-outline .ast-addon-cart-wrap","px"),wp.customize("astra-settings[woo-header-cart-icon-radius-fields]",function(t){t.bind(function(t){var a=".ast-site-header-cart.ast-menu-cart-outline .ast-cart-menu-wrap, .ast-site-header-cart.ast-menu-cart-fill .ast-cart-menu-wrap, .ast-site-header-cart.ast-menu-cart-outline .ast-cart-menu-wrap .count, .ast-site-header-cart.ast-menu-cart-fill .ast-cart-menu-wrap .count, .ast-site-header-cart.ast-menu-cart-outline .ast-addon-cart-wrap, .ast-site-header-cart.ast-menu-cart-fill .ast-addon-cart-wrap ",o=a+"{ border-top-left-radius :"+t.desktop.top+t["desktop-unit"]+"; border-bottom-right-radius :"+t.desktop.bottom+t["desktop-unit"]+"; border-bottom-left-radius :"+t.desktop.left+t["desktop-unit"]+"; border-top-right-radius :"+t.desktop.right+t["desktop-unit"]+"; } ",o=(o+="@media (max-width: "+n+"px) { "+a+"{ border-top-left-radius :"+t.tablet.top+t["tablet-unit"]+"; border-bottom-right-radius :"+t.tablet.bottom+t["tablet-unit"]+"; border-bottom-left-radius :"+t.tablet.left+t["tablet-unit"]+"; border-top-right-radius :"+t.tablet.right+t["tablet-unit"]+"; } } ")+("@media (max-width: "+d+"px) { "+a+"{ border-top-left-radius :"+t.mobile.top+t["mobile-unit"]+"; border-bottom-right-radius :"+t.mobile.bottom+t["mobile-unit"]+"; border-bottom-left-radius :"+t.mobile.left+t["mobile-unit"]+"; border-top-right-radius :"+t.mobile.right+t["mobile-unit"]+"; } } ");astra_add_dynamic_css("woo-header-cart-icon-radius-fields",o)})}),wp.customize("astra-settings[transparent-header-woo-cart-icon-color]",function(t){t.bind(function(t){wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[woo-header-cart-icon-total-label-position]",function(t){t.bind(function(t){var a=i(document).find(".cart-container"),o=(defaultPositionSelector="ast-cart-desktop-position-left ast-cart-desktop-position-right ast-cart-desktop-position-bottom ast-cart-mobile-position-left ast-cart-mobile-position-right ast-cart-mobile-position-bottom ast-cart-tablet-position-left ast-cart-tablet-position-right ast-cart-tablet-position-bottom",(i(s).find(".ast-addon-cart-wrap").length?((o=i(document).find(".ast-addon-cart-wrap")).removeClass(defaultPositionSelector),a.removeClass(defaultPositionSelector),o.addClass("ast-cart-desktop-position-"+t.desktop),o.addClass("ast-cart-mobile-position-"+t.mobile),o):(a.removeClass(defaultPositionSelector),a.addClass("ast-cart-desktop-position-"+t.desktop),a.addClass("ast-cart-mobile-position-"+t.mobile),a)).addClass("ast-cart-tablet-position-"+t.tablet),".cart-container, .ast-addon-cart-wrap {display : flex; align-items : center; padding-top: 7px; padding-bottom: 5px;} "),a=(o+=".astra-icon {line-height : 0.1;} ",astraBuilderCartPreview.tablet_break_point||768),r=astraBuilderCartPreview.mobile_break_point||544;function e(t,a){switch(t){case"bottom":var o=".ast-cart-"+a+"-position-bottom { flex-direction : column;} ";return o+=".ast-cart-"+a+"-position-bottom .ast-woo-header-cart-info-wrap { order : 2; line-height : 1; margin-top  : 0.5em; } ";case"right":return o=".ast-cart-"+a+"-position-right .ast-woo-header-cart-info-wrap { order :  2; margin-left : 0.7em;} ";case"left":return o=".ast-cart-"+a+"-position-left .ast-woo-header-cart-info-wrap { margin-right : 0.5em;} "}}t.desktop&&(o=(o+="@media (min-width: "+a+"px) {")+e(t.desktop,"desktop")+"} "),t.tablet&&(o=(o+="@media (max-width: "+a+"px ) and ( min-width: "+r+"px) {")+(dynamicMobileStyle=e(t.tablet,"tablet"))+"} "),t.mobile&&(o=(o+="@media (max-width: "+r+"px) {")+(dynamictabletStyle=e(t.mobile,"mobile"))+"} "),astra_add_dynamic_css("woo-header-cart-icon-total-label-position",o)})}),astra_builder_advanced_css("section-header-woo-cart",".woocommerce .ast-header-woo-cart .ast-site-header-cart .ast-addon-cart-wrap, .ast-header-woo-cart .ast-site-header-cart .ast-addon-cart-wrap"),astra_builder_visibility_css("section-header-woo-cart",".ast-header-woo-cart"),wp.customize("astra-settings[header-woo-cart-icon-size]",function(t){t.bind(function(t){var a;""==t.desktop&&""==t.tablet&&""==t.mobile||(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a="")+".ast-icon-shopping-bag .ast-icon svg, .ast-icon-shopping-cart .ast-icon svg, .ast-icon-shopping-basket .ast-icon svg {height: "+t.desktop+"px;")+"width: "+t.desktop+"px;")+"} @media (max-width: "+n+"px) {")+".ast-icon-shopping-bag .ast-icon svg, .ast-icon-shopping-cart .ast-icon svg, .ast-icon-shopping-basket .ast-icon svg {height: "+t.tablet+"px;")+"width: "+t.tablet+"px;} ")+"} @media (max-width: "+d+"px) {")+".ast-icon-shopping-bag .ast-icon svg, .ast-icon-shopping-cart .ast-icon svg, .ast-icon-shopping-basket .ast-icon svg {height: "+t.mobile+"px;")+"width: "+t.mobile+"px;} } .ast-cart-menu-wrap, .ast-site-header-cart i.astra-icon {")+"font-size: "+t.desktop+"px;} ")+"@media (max-width: "+n+"px) {.ast-header-break-point.ast-hfb-header .ast-cart-menu-wrap, .ast-site-header-cart i.astra-icon {")+"font-size: "+t.tablet+"px;} ")+"} @media (max-width: "+d+"px) {")+".ast-header-break-point.ast-hfb-header .ast-cart-menu-wrap, .ast-site-header-cart i.astra-icon {font-size:"+t.mobile+"px;} } ",astra_add_dynamic_css("header-woo-cart-icon-size",a))})}),wp.customize("astra-settings[woo-header-cart-badge-display]",function(t){t.bind(function(t){var a;t?(a=".astra-icon.astra-icon::after {  display:block; } ",a+=".ast-count-text {  display:block; } "):(a=".astra-icon.astra-icon::after {  display:none; } ",a+=".ast-count-text {  display:none; } "),astra_add_dynamic_css("woo-header-cart-badge-display",a)})}),wp.customize("astra-settings[woo-header-cart-total-label]",function(t){t.bind(function(t){i(document.body).trigger("wc_fragment_refresh")})})})(jQuery);