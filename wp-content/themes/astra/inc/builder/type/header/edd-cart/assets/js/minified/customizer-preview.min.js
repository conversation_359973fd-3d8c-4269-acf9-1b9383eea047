(t=>{var e=".ast-edd-site-header-cart",d=".astra-cart-drawer.edd-active";astra_css("astra-settings[edd-header-cart-icon-color]","color",e+" .ast-edd-cart-menu-wrap .count, "+e+" .ast-edd-cart-menu-wrap .count:after,"+e+" .ast-edd-header-cart-info-wrap"),astra_css("astra-settings[edd-header-cart-icon-color]","border-color",e+" .ast-edd-cart-menu-wrap .count, "+e+" .ast-edd-cart-menu-wrap .count:after"),astra_color_responsive_css("edd-cart-colors","astra-settings[header-edd-cart-text-color]","color",e+" .widget_edd_cart_widget span, "+e+" .widget_edd_cart_widget strong, "+e+" .widget_edd_cart_widget *, "+d+" .widget_edd_cart_widget span, .astra-cart-drawer .widget_edd_cart_widget *, "+d+" .astra-cart-drawer-title"),astra_color_responsive_css("edd-cart-colors","astra-settings[header-edd-cart-link-color]","color",e+" .widget_edd_cart_widget a, "+e+" .widget_edd_cart_widget a.edd-remove-from-cart, "+e+" .widget_edd_cart_widget .cart-total, "+e+" .widget_edd_cart_widget a.edd-remove-from-cart:after, "+d+" .widget_edd_cart_widget a, "+d+" .widget_edd_cart_widget a.edd-remove-from-cart, "+d+" .widget_edd_cart_widget .cart-total, "+d+" .widget_edd_cart_widget a.edd-remove-from-cart:after"),astra_color_responsive_css("edd-cart-colors","astra-settings[header-edd-cart-link-color]","border-color",e+" .widget_edd_cart_widget a.edd-remove-from-cart:after,"+d+" .widget_edd_cart_widget a.edd-remove-from-cart:after,"),astra_color_responsive_css("edd-cart-colors","astra-settings[header-edd-cart-background-color]","background-color",".ast-builder-layout-element "+e+" .widget_edd_cart_widget ,"+d+", "+d+"#astra-mobile-cart-drawer"),astra_color_responsive_css("edd-cart-border-color","astra-settings[header-edd-cart-background-color]","border-color",".ast-builder-layout-element "+e+" .widget_edd_cart_widget, "+d+" .widget_edd_cart_widget"),astra_color_responsive_css("edd-cart-border-bottom-color","astra-settings[header-edd-cart-background-color]","border-bottom-color",".ast-builder-layout-element "+e+" .widget_edd_cart_widget:before, .ast-builder-layout-element "+e+" .widget_edd_cart_widget:after, "+d+" .widget_edd_cart_widget:before, "+d+" .widget_edd_cart_widget:after"),astra_color_responsive_css("edd-cart-colors","astra-settings[header-edd-cart-separator-color]","border-bottom-color",".ast-builder-layout-element "+e+" .widget_edd_cart_widget .edd-cart-item, .ast-builder-layout-element "+e+" .widget_edd_cart_widget .edd-cart-number-of-items, .ast-builder-layout-element "+e+" .widget_edd_cart_widget .edd-cart-meta,"+d+" .widget_edd_cart_widget .edd-cart-item, "+d+" .widget_edd_cart_widget .edd-cart-number-of-items, "+d+" .widget_edd_cart_widget .edd-cart-meta, "+d+" .astra-cart-drawer-header"),astra_color_responsive_css("edd-cart-colors","astra-settings[header-edd-checkout-btn-text-color]","color",e+" .widget_edd_cart_widget .edd_checkout a, .widget_edd_cart_widget .edd_checkout a, "+d+" .widget_edd_cart_widget .edd_checkout a"),astra_color_responsive_css("edd-cart-colors","astra-settings[header-edd-checkout-btn-background-color]","background-color",e+" .widget_edd_cart_widget .edd_checkout a, .widget_edd_cart_widget .edd_checkout a,"+d+" .widget_edd_cart_widget .edd_checkout a, .widget_edd_cart_widget .edd_checkout a"),astra_color_responsive_css("edd-cart-border-color","astra-settings[header-edd-checkout-btn-background-color]","border-color",e+" .widget_edd_cart_widget .edd_checkout a, .widget_edd_cart_widget .edd_checkout a,"+d+" .widget_edd_cart_widget .edd_checkout a, .widget_edd_cart_widget .edd_checkout a"),astra_color_responsive_css("edd-cart-colors","astra-settings[header-edd-checkout-btn-text-hover-color]","color",e+" .widget_edd_cart_widget .edd_checkout a:hover, .widget_edd_cart_widget .edd_checkout a:hover,"+d+" .widget_edd_cart_widget .edd_checkout a:hover, .widget_edd_cart_widget .edd_checkout a:hover"),astra_color_responsive_css("edd-cart-colors","astra-settings[header-edd-checkout-btn-bg-hover-color]","background-color",e+" .widget_edd_cart_widget .edd_checkout a:hover, .widget_edd_cart_widget .edd_checkout a:hover, "+d+" .widget_edd_cart_widget .edd_checkout a:hover, .widget_edd_cart_widget .edd_checkout a:hover"),wp.customize("astra-settings[edd-header-cart-icon-style]",function(e){e.bind(function(e){var d=t(document).find(".ast-edd-site-header-cart");d.removeClass("ast-edd-menu-cart-fill ast-edd-menu-cart-outline"),d.addClass("ast-edd-menu-cart-"+e);astra_add_dynamic_css("edd-header-cart-icon-style",".ast-edd-site-header-cart a, .ast-edd-site-header-cart a *{ transition: all 0s; } "),wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[edd-header-cart-icon-color]",function(e){e.bind(function(e){wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[edd-header-cart-icon-radius]",function(e){e.bind(function(e){e=".ast-edd-site-header-cart.ast-edd-menu-cart-outline .ast-addon-cart-wrap, .ast-edd-site-header-cart.ast-edd-menu-cart-fill .ast-addon-cart-wrap, .ast-edd-site-header-cart.ast-edd-menu-cart-outline .count, .ast-edd-site-header-cart.ast-edd-menu-cart-fill .count{ border-radius: "+parseInt(e)+"px } ";astra_add_dynamic_css("edd-header-cart-icon-radius",e)})}),wp.customize("astra-settings[transparent-header-edd-cart-icon-color]",function(e){e.bind(function(e){wp.customize.preview.send("refresh")})}),astra_builder_visibility_css("section-header-edd-cart",".ast-header-edd-cart")})(jQuery);