(()=>{wp.customize("astra-settings[different-mobile-logo]",function(t){t.bind(function(t){"1"==t?jQuery(".site-header").addClass("ast-has-mobile-header-logo"):jQuery(".site-header").removeClass("ast-has-mobile-header-logo")})}),wp.customize("astra-settings[title_tagline-margin]",function(t){t.bind(function(t){var e,i,o;""==t.desktop.bottom&&""==t.desktop.top&&""==t.desktop.left&&""==t.desktop.right&&""==t.tablet.bottom&&""==t.tablet.top&&""==t.tablet.left&&""==t.tablet.right&&""==t.mobile.bottom&&""==t.mobile.top&&""==t.mobile.left&&""==t.mobile.right||(o="",e=".ast-builder-layout-element .ast-site-identity",i=astraBuilderPreview.tablet_break_point||768,o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=e+" {margin-left: "+t.desktop.left+t["desktop-unit"]+";")+"margin-right: "+t.desktop.right+t["desktop-unit"]+";")+"margin-top: "+t.desktop.top+t["desktop-unit"]+";")+"margin-bottom: "+t.desktop.bottom+t["desktop-unit"]+";")+"} @media (max-width: "+i+"px) {")+e+" {margin-left: "+t.tablet.left+t["tablet-unit"]+";")+"margin-right: "+t.tablet.right+t["tablet-unit"]+";")+"margin-top: "+t.tablet.top+t["desktop-unit"]+";")+"margin-bottom: "+t.tablet.bottom+t["desktop-unit"]+";} ")+"} @media (max-width: "+(astraBuilderPreview.mobile_break_point||544)+"px) {")+e+" {margin-left: "+t.mobile.left+t["mobile-unit"]+";")+"margin-right: "+t.mobile.right+t["mobile-unit"]+";")+"margin-top: "+t.mobile.top+t["desktop-unit"]+";")+"margin-bottom: "+t.mobile.bottom+t["desktop-unit"]+";} } ",astra_add_dynamic_css("title_tagline-margin",o))})}),astra_builder_visibility_css("title_tagline",'.ast-builder-layout-element[data-section="title_tagline"]')})(jQuery);