(()=>{var s=astraBuilderPreview.tablet_break_point||768,i=astraBuilderPreview.mobile_break_point||544,a=".ast-header-search";astra_color_responsive_css("header-search-icon-color","astra-settings[header-search-icon-color]","color",a+" .astra-search-icon, "+a+" .search-field::placeholder,"+a+" .ast-icon"),astra_css("astra-settings[header-search-icon-space]","font-size",a+" .astra-search-icon","px"),wp.customize("astra-settings[header-search-icon-space]",function(e){e.bind(function(e){var t;""==e.desktop&&""==e.tablet&&""==e.mobile||(t="",t=(t=(t=(t=(t=(t+=a+" .astra-search-icon {")+"font-size: "+e.desktop+"px;} ")+"@media (max-width: "+s+"px) {"+a+" .astra-search-icon {")+"font-size: "+e.tablet+"px;} ")+"} @media (max-width: "+i+"px) {")+a+" .astra-search-icon {font-size: "+e.mobile+"px;} } ",astra_add_dynamic_css("header-search-icon-space",t))})}),wp.customize("astra-settings[header-search-width]",function(e){e.bind(function(e){var t;""==e.desktop&&""==e.tablet&&""==e.mobile||(t=(t=(t=(t=(t=(t=".ast-header-search form.search-form .search-field, .ast-header-search .ast-dropdown-active.ast-search-menu-icon.slide-search input.search-field {")+"width:"+e.desktop+"px;} ")+"@media( max-width: "+astColors.tablet_break_point+"px ) {.ast-header-search form.search-form .search-field, .ast-header-search .ast-dropdown-active.ast-search-menu-icon.slide-search input.search-field, .ast-mobile-header-content .ast-search-menu-icon .search-form {")+"width:"+e.tablet+"px;} }")+"@media( max-width: "+astColors.mobile_break_point+"px ) {.ast-header-search form.search-form .search-field, .ast-header-search .ast-dropdown-active.ast-search-menu-icon.slide-search input.search-field, .ast-mobile-header-content .ast-search-menu-icon .search-form {")+"width:"+e.mobile+"px;} }",astra_add_dynamic_css("astra-settings[header-search-width]",t))})}),wp.customize("astra-settings[section-header-search-margin]",function(e){e.bind(function(e){var t,a;""==e.desktop.bottom&&""==e.desktop.top&&""==e.desktop.left&&""==e.desktop.right&&""==e.tablet.bottom&&""==e.tablet.top&&""==e.tablet.left&&""==e.tablet.right&&""==e.mobile.bottom&&""==e.mobile.top&&""==e.mobile.left&&""==e.mobile.right||(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a=(a="")+(t=".ast-hfb-header .site-header-section > .ast-header-search, .ast-hfb-header .ast-header-search")+" {margin-left: "+e.desktop.left+e["desktop-unit"]+";")+"margin-right: "+e.desktop.right+e["desktop-unit"]+";")+"margin-top: "+e.desktop.top+e["desktop-unit"]+";")+"margin-bottom: "+e.desktop.bottom+e["desktop-unit"]+";")+"} @media (max-width: "+s+"px) {")+t+" {margin-left: "+e.tablet.left+e["tablet-unit"]+";")+"margin-right: "+e.tablet.right+e["tablet-unit"]+";")+"margin-top: "+e.tablet.top+e["desktop-unit"]+";")+"margin-bottom: "+e.tablet.bottom+e["desktop-unit"]+";} ")+"} @media (max-width: "+i+"px) {")+t+" {margin-left: "+e.mobile.left+e["mobile-unit"]+";")+"margin-right: "+e.mobile.right+e["mobile-unit"]+";")+"margin-top: "+e.mobile.top+e["desktop-unit"]+";")+"margin-bottom: "+e.mobile.bottom+e["desktop-unit"]+";} } ",astra_add_dynamic_css("header-search-margin",a))})}),astra_builder_visibility_css("section-header-search",a)})(jQuery);