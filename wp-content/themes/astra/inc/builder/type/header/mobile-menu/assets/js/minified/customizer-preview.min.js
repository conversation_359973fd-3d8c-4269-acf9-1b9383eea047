(()=>{var n=AstraBuilderMenuData.tablet_break_point||768,a=AstraBuilderMenuData.mobile_break_point||544,e=".ast-builder-menu-mobile .main-navigation";astra_builder_visibility_css("section-header-mobile-menu",e,"block"),astra_generate_outside_font_family_css("astra-settings[header-mobile-menu-font-family]",e+" .menu-item > .menu-link"),astra_generate_font_weight_css("astra-settings[header-mobile-menu-font-family]","astra-settings[header-mobile-menu-font-weight]","font-weight",e+" .menu-item > .menu-link"),astra_responsive_font_size("astra-settings[header-mobile-menu-font-size]",e+" .menu-item > .menu-link"),astra_font_extras_css("font-extras-header-mobile-menu",".ast-builder-menu-mobile .main-navigation .menu-item > .menu-link"),astra_color_responsive_css("astra-menu-color-preview","astra-settings[header-mobile-menu-color-responsive]","color",e+" .main-header-menu .menu-item > .menu-link"),astra_color_responsive_css("astra-menu-h-color-preview","astra-settings[header-mobile-menu-h-color-responsive]","color",e+" .main-header-menu .menu-item:hover > .menu-link, "+e+" .inline-on-mobile .menu-item:hover > .ast-menu-toggle"),astra_color_responsive_css("astra-builder-toggle","astra-settings[header-mobile-menu-color-responsive]","color",e+" .main-header-menu .menu-item > .ast-menu-toggle"),astra_color_responsive_css("astra-menu-h-toogle-color-preview","astra-settings[header-mobile-menu-h-color-responsive]","color",e+" .menu-item:hover > .ast-menu-toggle"),astra_color_responsive_css("astra-menu-active-color-preview","astra-settings[header-mobile-menu-a-color-responsive]","color",e+" .menu-item.current-menu-item > .menu-link, "+e+" .inline-on-mobile .menu-item.current-menu-item > .ast-menu-toggle"),astra_apply_responsive_background_css("astra-settings[header-mobile-menu-bg-obj-responsive]",e+" .main-header-menu .menu-link, "+e+" .main-header-menu .sub-menu","desktop"),astra_apply_responsive_background_css("astra-settings[header-mobile-menu-bg-obj-responsive]",e+" .main-header-menu .menu-link, "+e+" .main-header-menu .sub-menu","tablet"),astra_apply_responsive_background_css("astra-settings[header-mobile-menu-bg-obj-responsive]",e+" .main-header-menu .menu-link, "+e+" .main-header-menu .sub-menu","mobile"),astra_color_responsive_css("astra-menu-bg-preview","astra-settings[header-mobile-menu-h-bg-color-responsive]","background",e+" .main-header-menu .menu-item:hover > .menu-link, "+e+" .inline-on-mobile .menu-item:hover > .ast-menu-toggle"),astra_color_responsive_css("astra-builder","astra-settings[header-mobile-menu-a-bg-color-responsive]","background",e+" .menu-item.current-menu-item > .menu-link, "+e+" .inline-on-mobile .menu-item.current-menu-item > .ast-menu-toggle"),wp.customize("astra-settings[header-mobile-menu-submenu-item-b-size]",function(e){e.bind(function(e){var t=".ast-hfb-header .ast-builder-menu-mobile .main-navigation",i=(i=(i=(i="")+(t+" .main-header-menu {")+("border-top-width: "+e+"px;"))+"} "+(t+" .menu-item .sub-menu .menu-link, "+t+" .menu-item .menu-link {"))+("border-bottom-width: "+e+"px;")+"} ";astra_add_dynamic_css("header-mobile-menu-submenu-item-b-size",i)})}),wp.customize("astra-settings[header-mobile-menu-submenu-border]",function(e){e.bind(function(e){var t=".ast-builder-menu-mobile  .sub-menu {",t=(t=(t=(t+="border-top-width:"+e.top+"px;")+("border-right-width:"+e.right+"px;"))+("border-left-width:"+e.left+"px;")+"border-style: solid;")+("border-bottom-width:"+e.bottom+"px;")+"}";astra_add_dynamic_css("header-mobile-menu-submenu-border",t)})}),wp.customize("astra-settings[header-mobile-menu-menu-spacing]",function(e){e.bind(function(e){var t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t="")+".ast-hfb-header .ast-builder-menu-mobile .main-header-menu .menu-item > .menu-link {"+("padding-left: "+e.desktop.left+e["desktop-unit"]+";"))+("padding-right: "+e.desktop.right+e["desktop-unit"]+";"))+("padding-top: "+e.desktop.top+e["desktop-unit"]+";"))+("padding-bottom: "+e.desktop.bottom+e["desktop-unit"]+";")+"} ")+".ast-hfb-header .ast-builder-menu-mobile .main-navigation ul .menu-item.menu-item-has-children > .ast-menu-toggle {"+("top: "+e.desktop.top+e["desktop-unit"]+";"))+("right: calc( "+e.desktop.right+e["desktop-unit"]+" - 0.907em );"))+"} "+("@media (max-width: "+n+"px) {"))+".ast-header-break-point .ast-builder-menu-mobile .main-header-menu .menu-item > .menu-link {"+("padding-left: "+e.tablet.left+e["tablet-unit"]+";"))+("padding-right: "+e.tablet.right+e["tablet-unit"]+";"))+("padding-top: "+e.tablet.top+e["tablet-unit"]+";"))+("padding-bottom: "+e.tablet.bottom+e["tablet-unit"]+";")+"} ")+".ast-hfb-header .ast-builder-menu-mobile .main-navigation ul .menu-item.menu-item-has-children > .ast-menu-toggle {"+("top: "+e.tablet.top+e["tablet-unit"]+";"))+("right: calc( "+e.tablet.right+e["tablet-unit"]+" - 0.907em );")+"} ")+"} "+("@media (max-width: "+a+"px) {"))+".ast-header-break-point .ast-builder-menu-mobile .main-header-menu .menu-item > .menu-link {"+("padding-left: "+e.mobile.left+e["mobile-unit"]+";"))+("padding-right: "+e.mobile.right+e["mobile-unit"]+";"))+("padding-top: "+e.mobile.top+e["mobile-unit"]+";"))+("padding-bottom: "+e.mobile.bottom+e["mobile-unit"]+";")+"} ")+".ast-hfb-header .ast-builder-menu-mobile .main-navigation ul .menu-item.menu-item-has-children > .ast-menu-toggle {"+("top: "+e.mobile.top+e["mobile-unit"]+";"))+("right: calc( "+e.mobile.right+e["mobile-unit"]+" - 0.907em );"))+"} "+"} ";astra_add_dynamic_css("header-mobile-menu-menu-spacing-toggle-button",t)})}),wp.customize("astra-settings[section-header-mobile-menu-margin]",function(e){e.bind(function(e){var t=".ast-builder-menu-mobile .main-header-menu, .ast-header-break-point .ast-builder-menu-mobile .main-header-menu",i=(i=(i=(i=(i=(i=(i=(i=(i=(i=(i=(i=(i=(i=(i=(i="")+(t+" {")+("margin-left: "+e.desktop.left+e["desktop-unit"]+";"))+("margin-right: "+e.desktop.right+e["desktop-unit"]+";"))+("margin-top: "+e.desktop.top+e["desktop-unit"]+";"))+("margin-bottom: "+e.desktop.bottom+e["desktop-unit"]+";"))+"} "+("@media (max-width: "+n+"px) {"))+(t+" {")+("margin-left: "+e.tablet.left+e["tablet-unit"]+";"))+("margin-right: "+e.tablet.right+e["tablet-unit"]+";"))+("margin-top: "+e.tablet.top+e["desktop-unit"]+";"))+("margin-bottom: "+e.tablet.bottom+e["desktop-unit"]+";")+"} ")+"} "+("@media (max-width: "+a+"px) {"))+(t+" {")+("margin-left: "+e.mobile.left+e["mobile-unit"]+";"))+("margin-right: "+e.mobile.right+e["mobile-unit"]+";"))+("margin-top: "+e.mobile.top+e["desktop-unit"]+";"))+("margin-bottom: "+e.mobile.bottom+e["desktop-unit"]+";"))+"} "+"} ";astra_add_dynamic_css("section-header-mobile-menu-margin",i)})}),wp.customize("astra-settings[header-mobile-menu-submenu-item-b-color]",function(e){e.bind(function(e){var t,i=wp.customize("astra-settings[header-mobile-menu-submenu-item-border]").get(),n=wp.customize("astra-settings[header-mobile-menu-submenu-item-b-size]").get();""!=e&&1==i?(t="",t=(t=(t=(t=(t+=".ast-hfb-header .ast-builder-menu-mobile .main-navigation .menu-item .sub-menu .menu-link, .ast-hfb-header .ast-builder-menu-mobile .main-navigation .menu-item .menu-link")+"{border-bottom-width:"+(!0===i?n+"px;":"0px;"))+"border-color:"+e+";border-style: solid;}.ast-hfb-header .ast-builder-menu-mobile .main-navigation .main-header-menu")+"{border-top-width:"+(!0===i?n+"px;":"0px;"))+"border-color:"+e+";}",astra_add_dynamic_css("header-mobile-menu-submenu-item-b-color",t)):wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[header-mobile-menu-submenu-item-border]",function(e){e.bind(function(e){var t,i=wp.customize("astra-settings[header-mobile-menu-submenu-item-b-color]").get(),n=wp.customize("astra-settings[header-mobile-menu-submenu-item-b-size]").get();!0===e?(t=".ast-builder-menu-mobile .main-navigation .main-header-menu .menu-item .sub-menu .menu-link, .ast-builder-menu-mobile .main-navigation .main-header-menu .menu-item .menu-link",t=(t=(t=(t+="{")+"border-bottom-width:"+(!0===e?n+"px;":"0px;")+"border-color:"+i+";border-style: solid;}.ast-builder-menu-mobile .main-navigation .main-header-menu{")+"border-top-width:"+(!0===e?n+"px;":"0px;")+"border-style: solid;")+"border-color:"+i+";}",astra_add_dynamic_css("header-mobile-menu-submenu-item-border",t)):wp.customize.preview.send("refresh")})}),astra_css("astra-settings[header-mobile-menu-submenu-b-color]","border-color",e+" li.menu-item .sub-menu, "+e+" .main-header-menu"),astra_color_responsive_css("astra-builder-transparent-submenu","astra-settings[transparent-submenu-h-color-responsive]","color",".ast-theme-transparent-header .main-header-menu .menu-item .sub-menu .menu-item:hover > .menu-link")})(jQuery);