(()=>{var a=astraBuilderPreview.tablet_break_point||768,o=astraBuilderPreview.mobile_break_point||544;astra_css("astra-settings[off-canvas-close-color]","color",".ast-mobile-popup-drawer.active .menu-toggle-close"),wp.customize("astra-settings[off-canvas-background]",function(e){e.bind(function(e){astra_background_obj_css(wp.customize,e,"off-canvas-background"," .ast-mobile-popup-drawer.active .ast-mobile-popup-inner, .ast-mobile-header-wrap .ast-mobile-header-content, .ast-desktop-header-content { {{css}} }")})}),wp.customize("astra-settings[off-canvas-inner-spacing]",function(e){e.bind(function(e){var t="";""!=e&&(t=(t=t+".ast-mobile-popup-content > *, .ast-mobile-header-content > *, .ast-desktop-popup-content > *, .ast-desktop-header-content > * {padding-top: "+e+"px;")+"padding-bottom: "+e+"px;} "),astra_add_dynamic_css("off-canvas-inner-spacing",t)})}),wp.customize("astra-settings[mobile-header-type]",function(e){e.bind(function(e){var t=document.querySelectorAll("#ast-mobile-header"),a=document.querySelectorAll("#ast-desktop-header"),o=e,e=void 0!==wp.customize._value["astra-settings[off-canvas-slide]"]?wp.customize._value["astra-settings[off-canvas-slide]"]._value:"right",s="";"off-canvas"===o?s="left"===e?"ast-mobile-popup-left":"ast-mobile-popup-right":"full-width"===o&&(s="ast-mobile-popup-full-width"),jQuery(".ast-mobile-popup-drawer").removeClass("ast-mobile-popup-left"),jQuery(".ast-mobile-popup-drawer").removeClass("ast-mobile-popup-right"),jQuery(".ast-mobile-popup-drawer").removeClass("ast-mobile-popup-full-width"),jQuery(".ast-mobile-popup-drawer").addClass(s),"full-width"===o&&(o="off-canvas");for(var n=0;n<t.length;n++)t[n].setAttribute("data-type",o);for(n=0;n<a.length;n++)a[n].setAttribute("data-type",o);e=new CustomEvent("astMobileHeaderTypeChange",{detail:{type:o}});document.dispatchEvent(e)})}),wp.customize("astra-settings[off-canvas-slide]",function(e){e.bind(function(e){var t="",t="left"===e?"ast-mobile-popup-left":"ast-mobile-popup-right";jQuery(".ast-mobile-popup-drawer").removeClass("ast-mobile-popup-left"),jQuery(".ast-mobile-popup-drawer").removeClass("ast-mobile-popup-right"),jQuery(".ast-mobile-popup-drawer").removeClass("ast-mobile-popup-full-width"),jQuery(".ast-mobile-popup-drawer").addClass(t)})}),wp.customize("astra-settings[off-canvas-padding]",function(e){e.bind(function(e){var t;""!=e.desktop.bottom||""!=e.desktop.top||""!=e.desktop.left||""!=e.desktop.right||""!=e.tablet.bottom||""!=e.tablet.top||""!=e.tablet.left||""!=e.tablet.right||""!=e.mobile.bottom||""!=e.mobile.top||""!=e.mobile.left||""!=e.mobile.right?(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t="")+".ast-mobile-popup-drawer.active .ast-desktop-popup-content, .ast-mobile-popup-drawer.active .ast-mobile-popup-content {padding-left: "+e.desktop.left+e["desktop-unit"]+";")+"padding-right: "+e.desktop.right+e["desktop-unit"]+";")+"padding-top: "+e.desktop.top+e["desktop-unit"]+";")+"padding-bottom: "+e.desktop.bottom+e["desktop-unit"]+";")+"} @media (max-width: "+a+"px) {")+".ast-mobile-popup-drawer.active .ast-desktop-popup-content, .ast-mobile-popup-drawer.active .ast-mobile-popup-content {padding-left: "+e.tablet.left+e["tablet-unit"]+";")+"padding-right: "+e.tablet.right+e["tablet-unit"]+";")+"padding-top: "+e.tablet.top+e["tablet-unit"]+";")+"padding-bottom: "+e.tablet.bottom+e["tablet-unit"]+";} ")+"} @media (max-width: "+o+"px) {")+".ast-mobile-popup-drawer.active .ast-desktop-popup-content, .ast-mobile-popup-drawer.active .ast-mobile-popup-content {padding-left: "+e.mobile.left+e["mobile-unit"]+";")+"padding-right: "+e.mobile.right+e["mobile-unit"]+";")+"padding-top: "+e.mobile.top+e["mobile-unit"]+";")+"padding-bottom: "+e.mobile.bottom+e["mobile-unit"]+";} } ",astra_add_dynamic_css("off-canvas-padding",t)):astra_add_dynamic_css("off-canvas-padding","")})}),wp.customize("astra-settings[header-builder-menu-toggle-target]",function(e){e.bind(function(e){e="ast-builder-menu-toggle-"+e+" ";jQuery(".site-header").removeClass("ast-builder-menu-toggle-icon"),jQuery(".site-header").removeClass("ast-builder-menu-toggle-link"),jQuery(".site-header").addClass(e)})}),wp.customize("astra-settings[header-offcanvas-content-alignment]",function(e){e.bind(function(e){var t="content-align-"+e+" ",a="center",t=(jQuery(".ast-mobile-header-content").removeClass("content-align-flex-start"),jQuery(".ast-mobile-header-content").removeClass("content-align-flex-end"),jQuery(".ast-mobile-header-content").removeClass("content-align-center"),jQuery(".ast-mobile-popup-drawer").removeClass("content-align-flex-end"),jQuery(".ast-mobile-popup-drawer").removeClass("content-align-flex-start"),jQuery(".ast-mobile-popup-drawer").removeClass("content-align-center"),jQuery(".ast-desktop-header-content").removeClass("content-align-flex-start"),jQuery(".ast-desktop-header-content").removeClass("content-align-flex-end"),jQuery(".ast-desktop-header-content").removeClass("content-align-center"),jQuery(".ast-desktop-header-content").addClass(t),jQuery(".ast-mobile-header-content").addClass(t),jQuery(".ast-mobile-popup-drawer").addClass(t),"flex-start"===e?a="left":"flex-end"===e&&(a="right"),".content-align-"+e+" .ast-builder-layout-element {"),t=(t=(t+="justify-content: "+e+";")+"} "+(".content-align-"+e+" .main-header-menu {"))+("text-align: "+a+";")+"} ";astra_add_dynamic_css("header-offcanvas-content-alignment",t)})})})(jQuery);