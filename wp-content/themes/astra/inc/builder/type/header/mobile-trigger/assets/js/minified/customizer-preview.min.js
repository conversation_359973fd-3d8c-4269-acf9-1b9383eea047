(()=>{var i=astraBuilderPreview.tablet_break_point||768,r=astraBuilderPreview.mobile_break_point||544;astra_css("astra-settings[mobile-header-toggle-btn-color]","fill",'[data-section="section-header-mobile-trigger"] .ast-button-wrap .mobile-menu-toggle-icon .ast-mobile-svg'),astra_css("astra-settings[mobile-header-toggle-btn-color]","color",'[data-section="section-header-mobile-trigger"] .ast-button-wrap .mobile-menu-wrap .mobile-menu'),astra_css("astra-settings[mobile-header-toggle-icon-size]","width",'[data-section="section-header-mobile-trigger"] .ast-button-wrap .mobile-menu-toggle-icon .ast-mobile-svg',"px"),astra_css("astra-settings[mobile-header-toggle-icon-size]","height",'[data-section="section-header-mobile-trigger"] .ast-button-wrap .mobile-menu-toggle-icon .ast-mobile-svg',"px"),astra_css("astra-settings[mobile-header-toggle-btn-bg-color]","background",'[data-section="section-header-mobile-trigger"] .ast-button-wrap .menu-toggle.ast-mobile-menu-trigger-fill'),wp.customize("astra-settings[mobile-header-toggle-btn-border-size]",function(t){t.bind(function(t){var e='[data-section="section-header-mobile-trigger"] .ast-button-wrap .menu-toggle.main-header-menu-toggle {',e=(e=(e+="border-top-width:"+t.top+"px;")+("border-right-width:"+t.right+"px;")+("border-left-width:"+t.left+"px;"))+("border-bottom-width:"+t.bottom+"px;")+"} ";astra_add_dynamic_css("astra-settings[mobile-header-toggle-btn-border-size]",e)})}),wp.customize("astra-settings[mobile-header-toggle-border-radius-fields]",function(t){t.bind(function(t){var e='[data-section="section-header-mobile-trigger"] .ast-button-wrap .menu-toggle.main-header-menu-toggle',o=e+"{ border-top-left-radius :"+t.desktop.top+t["desktop-unit"]+"; border-bottom-right-radius :"+t.desktop.bottom+t["desktop-unit"]+"; border-bottom-left-radius :"+t.desktop.left+t["desktop-unit"]+"; border-top-right-radius :"+t.desktop.right+t["desktop-unit"]+"; } ",o=(o+="@media (max-width: "+i+"px) { "+e+"{ border-top-left-radius :"+t.tablet.top+t["tablet-unit"]+"; border-bottom-right-radius :"+t.tablet.bottom+t["tablet-unit"]+"; border-bottom-left-radius :"+t.tablet.left+t["tablet-unit"]+"; border-top-right-radius :"+t.tablet.right+t["tablet-unit"]+"; } } ")+("@media (max-width: "+r+"px) { "+e+"{ border-top-left-radius :"+t.mobile.top+t["mobile-unit"]+"; border-bottom-right-radius :"+t.mobile.bottom+t["mobile-unit"]+"; border-bottom-left-radius :"+t.mobile.left+t["mobile-unit"]+"; border-top-right-radius :"+t.mobile.right+t["mobile-unit"]+"; } } ");astra_add_dynamic_css("astra-settings[mobile-header-toggle-border-radius-fields]",o)})}),astra_css("astra-settings[mobile-header-toggle-border-color]","border-color",'[data-section="section-header-mobile-trigger"] .ast-button-wrap .menu-toggle.ast-mobile-menu-trigger-outline, [data-section="section-header-mobile-trigger"] .ast-button-wrap .menu-toggle.ast-mobile-menu-trigger-fill'),wp.customize("astra-settings[section-header-mobile-trigger-margin]",function(t){t.bind(function(t){var e,o;""==t.desktop.bottom&&""==t.desktop.top&&""==t.desktop.left&&""==t.desktop.right&&""==t.tablet.bottom&&""==t.tablet.top&&""==t.tablet.left&&""==t.tablet.right&&""==t.mobile.bottom&&""==t.mobile.top&&""==t.mobile.left&&""==t.mobile.right||(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o=(o="")+(e='[data-section="section-header-mobile-trigger"] .ast-button-wrap .menu-toggle.main-header-menu-toggle')+" {margin-left: "+t.desktop.left+t["desktop-unit"]+";")+"margin-right: "+t.desktop.right+t["desktop-unit"]+";")+"margin-top: "+t.desktop.top+t["desktop-unit"]+";")+"margin-bottom: "+t.desktop.bottom+t["desktop-unit"]+";")+"} @media (max-width: "+i+"px) {")+e+" {margin-left: "+t.tablet.left+t["tablet-unit"]+";")+"margin-right: "+t.tablet.right+t["tablet-unit"]+";")+"margin-top: "+t.tablet.top+t["desktop-unit"]+";")+"margin-bottom: "+t.tablet.bottom+t["desktop-unit"]+";} ")+"} @media (max-width: "+r+"px) {")+e+" {margin-left: "+t.mobile.left+t["mobile-unit"]+";")+"margin-right: "+t.mobile.right+t["mobile-unit"]+";")+"margin-top: "+t.mobile.top+t["desktop-unit"]+";")+"margin-bottom: "+t.mobile.bottom+t["desktop-unit"]+";} } ",astra_add_dynamic_css("header-mobile-trigger-margin",o))})}),astra_css("astra-settings[mobile-header-label-font-size]","font-size",'[data-section="section-header-mobile-trigger"] .ast-button-wrap .mobile-menu-wrap .mobile-menu',"px")})(jQuery);