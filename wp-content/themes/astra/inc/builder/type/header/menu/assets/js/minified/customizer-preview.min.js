(()=>{for(var i=AstraBuilderMenuData.tablet_break_point||768,a=AstraBuilderMenuData.mobile_break_point||544,n=AstraBuilderMenuData.nav_menu_enabled||!1,e=1;e<=AstraBuilderMenuData.component_limit;e++){var t="menu"+e,s=".ast-builder-menu-"+e;astra_builder_visibility_css("section-hb-menu-"+e,s),astra_generate_outside_font_family_css("astra-settings[header-"+t+"-font-family]",s+" .menu-item > .menu-link"),astra_generate_font_weight_css("astra-settings[header-"+t+"-font-family]","astra-settings[header-"+t+"-font-weight]","font-weight",s+" .menu-item > .menu-link"),astra_css("astra-settings[header-"+t+"-text-transform]","text-transform",s+" .menu-item > .menu-link"),astra_responsive_font_size("astra-settings[header-"+t+"-font-size]",s+" .menu-item > .menu-link"),astra_css("astra-settings[header-"+t+"-line-height]","line-height",s+" .menu-item > .menu-link"),astra_css("astra-settings[header-"+t+"-letter-spacing]","letter-spacing",s+" .menu-item > .menu-link","px"),astra_color_responsive_css("astra-menu-color-preview","astra-settings[header-"+t+"-color-responsive]","color",s+" .main-header-menu .menu-item > .menu-link"),astra_color_responsive_css("astra-menu-h-color-preview","astra-settings[header-"+t+"-h-color-responsive]","color",s+" .menu-item:hover > .menu-link, "+s+" .inline-on-mobile .menu-item:hover > .ast-menu-toggle"),astra_color_responsive_css("astra-builder-toggle","astra-settings[header-"+t+"-color-responsive]","color",s+" .menu-item > .ast-menu-toggle"),astra_color_responsive_css("astra-menu-h-toogle-color-preview","astra-settings[header-"+t+"-h-color-responsive]","color",s+" .menu-item:hover > .ast-menu-toggle"),astra_color_responsive_css("astra-menu-active-color-preview","astra-settings[header-"+t+"-a-color-responsive]","color",s+" .menu-item.current-menu-item > .menu-link, "+s+" .inline-on-mobile .menu-item.current-menu-item > .ast-menu-toggle, "+s+" .current-menu-ancestor > .menu-link, "+s+"  .current-menu-ancestor > .ast-menu-toggle"),astra_apply_responsive_background_css("astra-settings[header-"+t+"-bg-obj-responsive]",s+" .main-header-menu, "+s+" .main-header-menu .sub-menu","desktop"),astra_apply_responsive_background_css("astra-settings[header-"+t+"-bg-obj-responsive]",s+" .main-header-menu, "+s+" .main-header-menu .sub-menu","tablet"),astra_apply_responsive_background_css("astra-settings[header-"+t+"-bg-obj-responsive]",s+" .main-header-menu, "+s+" .main-header-menu .sub-menu","mobile"),astra_color_responsive_css("astra-menu-bg-preview","astra-settings[header-"+t+"-h-bg-color-responsive]","background",s+" .menu-item:hover > .menu-link, "+s+" .inline-on-mobile .menu-item:hover > .ast-menu-toggle"),astra_color_responsive_css("astra-builder","astra-settings[header-"+t+"-a-bg-color-responsive]","background",s+" .menu-item.current-menu-item > .menu-link, "+s+" .inline-on-mobile .menu-item.current-menu-item > .ast-menu-toggle, "+s+" .current-menu-ancestor > .menu-link, "+s+"  .current-menu-ancestor > .ast-menu-toggle"),(s=>{wp.customize("astra-settings[header-menu"+s+"-submenu-border]",function(e){e.bind(function(e){var t=".ast-builder-menu-"+s+" .sub-menu, .ast-builder-menu-"+s+" .inline-on-mobile .sub-menu, .ast-builder-menu-"+s+" .main-header-menu.submenu-with-border .astra-megamenu, .ast-builder-menu-"+s+" .main-header-menu.submenu-with-border .astra-full-megamenu-wrapper {",n=(t=(t=(t=(t=(t+="border-top-width:"+e.top+"px;")+("border-right-width:"+e.right+"px;")+("border-left-width:"+e.left+"px;"))+"border-style: solid;"+("border-bottom-width:"+e.bottom+"px;"))+"}"+(".ast-builder-menu-"+s+" .sub-menu .sub-menu {"))+("top:"+Number(-1*e.top)+"px;")+"}",wp.customize("astra-settings[header-menu"+s+"-submenu-top-offset]").get()),t=(t+=".ast-desktop .ast-builder-menu-"+s+" .main-header-menu > .menu-item > .sub-menu:before, .ast-desktop .ast-builder-menu-"+s+" .main-header-menu > .menu-item > .astra-full-megamenu-wrapper:before {")+("height: calc( "+(e.top||0)+"px + "+(n||0)+"px + 5px );")+"}";astra_add_dynamic_css("header-menu"+s+"-submenu-border",t)})}),wp.customize("astra-settings[header-menu"+s+"-menu-spacing]",function(e){e.bind(function(e){var t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t="")+(".ast-builder-menu-"+s+" .main-header-menu .menu-item > .menu-link {")+("padding-left: "+e.desktop.left+e["desktop-unit"]+";"))+("padding-right: "+e.desktop.right+e["desktop-unit"]+";"))+("padding-top: "+e.desktop.top+e["desktop-unit"]+";"))+("padding-bottom: "+e.desktop.bottom+e["desktop-unit"]+";"))+"} "+("@media (max-width: "+i+"px) {"))+(".ast-header-break-point .ast-builder-menu-"+s+" .main-header-menu .menu-item > .menu-link {")+("padding-left: "+e.tablet.left+e["tablet-unit"]+";"))+("padding-right: "+e.tablet.right+e["tablet-unit"]+";"))+("padding-top: "+e.tablet.top+e["tablet-unit"]+";"))+("padding-bottom: "+e.tablet.bottom+e["tablet-unit"]+";")+"} ")+(".ast-hfb-header .ast-builder-menu-"+s+" .main-navigation ul .menu-item.menu-item-has-children > .ast-menu-toggle {")+("top: "+e.tablet.top+e["tablet-unit"]+";"))+("right: calc( "+e.tablet.right+e["tablet-unit"]+" - 0.907em );")+"} ")+"} "+("@media (max-width: "+a+"px) {"))+(".ast-header-break-point .ast-builder-menu-"+s+" .main-header-menu .menu-item > .menu-link {")+("padding-left: "+e.mobile.left+e["mobile-unit"]+";"))+("padding-right: "+e.mobile.right+e["mobile-unit"]+";"))+("padding-top: "+e.mobile.top+e["mobile-unit"]+";"))+("padding-bottom: "+e.mobile.bottom+e["mobile-unit"]+";")+"} ")+(".ast-hfb-header .ast-builder-menu-"+s+" .main-navigation ul .menu-item.menu-item-has-children > .ast-menu-toggle {")+("top: "+e.mobile.top+e["mobile-unit"]+";"))+("right: calc( "+e.mobile.right+e["mobile-unit"]+" - 0.907em );"))+"} "+"} ";astra_add_dynamic_css("header-menu"+s+"-menu-spacing-toggle-button",t)})}),wp.customize("astra-settings[section-hb-menu-"+s+"-margin]",function(e){e.bind(function(e){var t=".ast-builder-menu-"+s+" .main-header-menu, .ast-header-break-point .ast-builder-menu-"+s+" .main-header-menu",n=(n=(n=(n=(n=(n=(n=(n=(n=(n=(n=(n=(n=(n=(n=(n="")+(t+" {")+("margin-left: "+e.desktop.left+e["desktop-unit"]+";"))+("margin-right: "+e.desktop.right+e["desktop-unit"]+";"))+("margin-top: "+e.desktop.top+e["desktop-unit"]+";"))+("margin-bottom: "+e.desktop.bottom+e["desktop-unit"]+";"))+"} "+("@media (max-width: "+i+"px) {"))+(t+" {")+("margin-left: "+e.tablet.left+e["tablet-unit"]+";"))+("margin-right: "+e.tablet.right+e["tablet-unit"]+";"))+("margin-top: "+e.tablet.top+e["desktop-unit"]+";"))+("margin-bottom: "+e.tablet.bottom+e["desktop-unit"]+";")+"} ")+"} "+("@media (max-width: "+a+"px) {"))+(t+" {")+("margin-left: "+e.mobile.left+e["mobile-unit"]+";"))+("margin-right: "+e.mobile.right+e["mobile-unit"]+";"))+("margin-top: "+e.mobile.top+e["desktop-unit"]+";"))+("margin-bottom: "+e.mobile.bottom+e["desktop-unit"]+";"))+"} "+"} ";astra_add_dynamic_css("section-hb-menu-"+s+"-margin",n)})}),wp.customize("astra-settings[header-menu"+s+"-submenu-item-b-color]",function(e){e.bind(function(e){var t=wp.customize("astra-settings[header-menu"+s+"-submenu-item-border]").get(),n=wp.customize("astra-settings[header-menu"+s+"-submenu-item-b-size]").get();""!=e?1==t&&(t="",t=(t=(t=(t+=".ast-desktop .ast-builder-menu-"+s+" .main-header-menu .menu-item .sub-menu .menu-link, .ast-header-break-point .main-navigation ul .menu-item .menu-link")+"{border-bottom-width:"+(n?n+"px;":"0px;"))+"border-color:"+e+";border-style: solid;")+"}.ast-desktop .ast-builder-menu-"+s+" .main-header-menu .menu-item .sub-menu .menu-item:last-child .menu-link{ border-style: none; }",astra_add_dynamic_css("header-menu"+s+"-submenu-item-b-color",t)):wp.customize.preview.send("refresh")})}),wp.customize("astra-settings[header-menu"+s+"-submenu-item-b-size]",function(e){e.bind(function(e){var t=".ast-desktop .ast-builder-menu-"+s+" .main-header-menu",n=(n=(n=(n="")+(t+" .menu-item .sub-menu:last-child > .menu-item > .menu-link, .ast-header-break-point .main-navigation ul .menu-item .menu-link {")+("border-bottom-width: "+e+"px;"))+"} "+(t+" .menu-item .sub-menu .menu-item:last-child .menu-link {"))+"border-bottom-width: 0px"+"} ";astra_add_dynamic_css("header-menu"+s+"-submenu-item-b-size",n)})}),wp.customize("astra-settings[header-menu"+s+"-submenu-item-border]",function(e){e.bind(function(e){var t=wp.customize("astra-settings[header-menu"+s+"-submenu-item-b-color]").get(),n=wp.customize("astra-settings[header-menu"+s+"-submenu-item-b-size]").get(),i=".ast-desktop .ast-builder-menu-"+s+" .main-header-menu.submenu-with-border .sub-menu .menu-link";!0===e?i=(i+="{")+"border-bottom-width:"+(n?n+"px;":"0px;")+"border-color:"+t+";border-style: solid;}":i+="{border-style: none;}",i+=".ast-desktop .ast-builder-menu-"+s+" .menu-item .sub-menu .menu-item:last-child .menu-link{ border-style: none; }",astra_add_dynamic_css("header-menu"+s+"-submenu-item-border",i)})}),wp.customize("astra-settings[header-menu"+s+"-menu-hover-animation]",function(e){e.bind(function(e){var t=jQuery("#ast-desktop-header #ast-hf-menu-"+s);t.removeClass("ast-menu-hover-style-underline"),t.removeClass("ast-menu-hover-style-zoom"),t.removeClass("ast-menu-hover-style-overline"),n&&document.dispatchEvent(new CustomEvent("astMenuHoverStyleChanged",{detail:{}})),t.addClass("ast-menu-hover-style-"+e)})}),wp.customize("astra-settings[header-menu"+s+"-submenu-container-animation]",function(e){e.bind(function(e){var t=jQuery("#ast-desktop-header #ast-hf-menu-"+s);t.removeClass("astra-menu-animation-fade"),t.removeClass("astra-menu-animation-slide-down"),t.removeClass("astra-menu-animation-slide-up"),""!=e&&t.addClass("astra-menu-animation-"+e)})}),wp.customize("astra-settings[header-menu"+s+"-menu-stack-on-mobile]",function(e){e.bind(function(e){var t=jQuery("#ast-mobile-header #ast-hf-menu-"+s+".main-header-menu");t.removeClass("inline-on-mobile"),t.removeClass("stack-on-mobile"),!1===e?t.addClass("inline-on-mobile"):t.addClass("stack-on-mobile")})}),wp.customize("astra-settings[header-menu"+s+"-submenu-border-radius-fields]",function(e){e.bind(function(t){var e=astraBuilderPreview.tablet_break_point||768,n=astraBuilderPreview.mobile_break_point||544;let i=wp.customize.get()?.["astra-settings[header-menu"+s+"-submenu-border]"];var a=e=>".ast-builder-menu-"+s+" li.menu-item .sub-menu, .ast-builder-menu-"+s+" ul.inline-on-mobile li.menu-item .sub-menu {\t\t\t\t\t\t\t\t\tborder-top-left-radius: "+t[e].top+t[e+"-unit"]+";\t\t\t\t\t\t\t\t\tborder-bottom-right-radius: "+t[e].bottom+t[e+"-unit"]+";\t\t\t\t\t\t\t\t\tborder-bottom-left-radius: "+t[e].left+t[e+"-unit"]+";\t\t\t\t\t\t\t\t\tborder-top-right-radius:"+t[e].right+t[e+"-unit"]+";\t\t\t\t\t\t\t\t}\t\t\t\t\t\t\t\t.ast-builder-menu-"+s+" li.menu-item .sub-menu .menu-item:first-of-type > .menu-link, .ast-builder-menu-"+s+" ul.inline-on-mobile li.menu-item .sub-menu .menu-item:first-of-type > .menu-link {\t\t\t\t\t\t\t\t\tborder-top-left-radius: calc("+t[e].top+t[e+"-unit"]+" - "+i?.top+"px);\t\t\t\t\t\t\t\t\tborder-top-right-radius: calc("+t[e].right+t[e+"-unit"]+" - "+i?.top+"px);\t\t\t\t\t\t\t\t}\t\t\t\t\t\t\t\t.ast-builder-menu-"+s+" li.menu-item .sub-menu .menu-item:last-of-type > .menu-link, .ast-builder-menu-"+s+" ul.inline-on-mobile li.menu-item .sub-menu .menu-item:last-of-type > .menu-link {\t\t\t\t\t\t\t\t\tborder-bottom-left-radius: calc("+t[e].left+t[e+"-unit"]+" - "+i?.top+"px);\t\t\t\t\t\t\t\t\tborder-bottom-right-radius: calc("+t[e].bottom+t[e+"-unit"]+" - "+i?.top+"px);\t\t\t\t\t\t\t\t}";dynamicStyle=a("desktop")+"@media ( max-width: "+e+"px ) { "+a("tablet")+" }\n@media ( max-width: "+n+"px ) { "+a("mobile")+" }\n",astra_add_dynamic_css("header-menu"+s+"-submenu-border-radius-fields",dynamicStyle)})}),wp.customize("astra-settings[header-menu"+s+"-submenu-top-offset]",function(e){e.bind(function(e){n=(n=".ast-desktop .ast-builder-menu-"+s+" .main-header-menu > li.menu-item > .sub-menu, .ast-desktop .ast-builder-menu-"+s+" ul.inline-on-mobile > li.menu-item > .sub-menu, .ast-desktop .ast-builder-menu-"+s+" li.menu-item .astra-full-megamenu-wrapper {")+"margin-top: "+e+"px}";var t=wp.customize("astra-settings[header-menu"+s+"-submenu-border]").get(),n=(n+=".ast-desktop .ast-builder-menu-"+s+" .main-header-menu > .menu-item > .sub-menu:before, .ast-desktop .ast-builder-menu-"+s+" .main-header-menu > .menu-item > .astra-full-megamenu-wrapper:before {")+("height: calc( "+(t.top||0)+"px + "+(e||0)+"px + 5px );")+"}";astra_add_dynamic_css("header-menu"+s+"-submenu-top-offset",n)})}),wp.customize("astra-settings[header-menu"+s+"-submenu-width]",function(e){e.bind(function(e){var t=(t=".ast-builder-menu-"+s+" li.menu-item .sub-menu, .ast-builder-menu-"+s+" ul.inline-on-mobile li.menu-item .sub-menu {")+("width: "+e+"px")+"}";astra_add_dynamic_css("header-menu"+s+"-submenu-width",t)})}),astra_font_extras_css("header-menu"+s+"-font-extras",".ast-builder-menu-"+s+" .menu-item > .menu-link")})(e),astra_css("astra-settings[header-"+t+"-submenu-b-color]","border-color",s+" li.menu-item .sub-menu, "+s+" .inline-on-mobile li.menu-item .sub-menu ")}astra_color_responsive_css("astra-builder-transparent-submenu","astra-settings[transparent-submenu-h-color-responsive]","color",".ast-theme-transparent-header .main-header-menu .menu-item .sub-menu .menu-item:hover > .menu-link")})(jQuery);