.astra-review-notice-container {
	display: flex;
	align-items: center;
	padding-top: 10px;
}

.astra-review-notice-container .dashicons {
	font-size: 1.4em;
	padding-left: 10px;
}

.astra-review-notice-container a {
	padding-left: 5px;
	text-decoration: none;
}

.astra-review-notice-container .dashicons:first-child {
	padding-left: 0;
}

.notice-image img {
	max-width: 90px;
}

.notice-content .notice-heading {
	padding-bottom: 5px;
}

.notice-content {
	margin-left: 15px;
}

.notice-container {
    padding-top: 10px;
    padding-bottom: 10px;
	display: flex;
	justify-content: left;
	align-items: center;
}

#astra-sites-on-active .notice-image img,
#astra-upgrade-pro-wc .notice-image img {
	max-width: 50px;
	margin-left: 5px;
}

#astra-sites-on-active .notice-content .notice-heading,
#astra-upgrade-pro-wc .notice-content .notice-heading {
	margin: 0 0 10px;
	padding: 0;
	font-weight: 600;
	font-size: 1.3em;
	color: #1e293b;
}

#astra-sites-on-active .notice-content p,
#astra-upgrade-pro-wc .notice-content p {
	padding-top: 0;
	margin-top: 0;
	margin-bottom: 6px;
	color: #475569;
}

#astra-sites-on-active .notice-container,
#astra-upgrade-pro-wc .notice-container {
	padding: 18px 0 18px;
	align-items: start;
}

#astra-sites-on-active .button.button-hero {
	font-size: 13px;
	min-height: 30px;
	line-height: 26px;
	padding: 0 12px;
	height: 30px;
}

#astra-sites-on-active .astra-review-notice-container,
#astra-upgrade-pro-wc .astra-review-notice-container {
	padding-top: 5px;
}

#astra-sites-on-active .button-primary,
#astra-upgrade-pro-wc .button-primary {
	box-shadow: 0 1px 0 #006799;
}

#astra-sites-on-active .button.updating-message:before,
#astra-sites-on-active .button.updated-message:before,
#astra-sites-on-active .button.installed:before,
#astra-sites-on-active .button.installing:before {
	margin: 4px 5px 0px -1px;
}

.wp-core-ui .astra-notice-wrapper:has(.ast-welcome-banner) {
    padding-right: 0;
}
.ast-welcome-banner {
	width: 100%;
	display: flex;
	gap: 40px;
	justify-content: center;
	overflow: hidden;
}
.ast-col-left {
	width: 50%;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: center;
	padding: 40px 0px 40px 28px;
}
.ast-col-right {
	width: 50%;
	display: flex;
	position: relative;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.ast-col-right > img {
	width: 100%;
	position: relative;
}
.ast-welcome-banner .notice-title {
	color: #1e293b;
	font-size: 30px;
	font-weight: 600;
	line-height: 40px;
	letter-spacing: -0.2px;
	margin-top: 12px;
	margin-bottom: 12px;
	padding: 0;
}
.ast-welcome-banner .description {
	color: #475569;
	font-size: 16px;
	line-height: 28px;
	margin-top: 0px;
	margin-bottom: 32px;
	padding: 0px;
}
#astra-sites-on-active .astra-notice-container .notice-actions > button {
	border-radius: 6px;
	background: #046bd2;
	padding: 12px 24px;
	/* shadow/sm */
	color: white;
	box-shadow: none;
	border: none;
	font-size: 16px;
	font-weight: 500;
	line-height: 24px;
}
.ast-welcome-banner .sub-notice-title {
	display: flex;
	flex-direction: column;
	align-self: stretch;
	color: #646970;
	font-size: 14px;
	line-height: 22px;
	margin: 0;
	padding: 0;
}
.astra-notice-container:has(.ast-welcome-banner) {
    padding-top: 0;
    padding-bottom: 0;
}
.ast-welcome-banner .notice-actions button {
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
    padding: 12px 24px;
}
p.sub-notice-description {
    color: #475569;
    font-size: 13px;
    line-height: 20px;
    text-decoration: underline;
    cursor: pointer;
    margin: 0;
    padding: 16px 0 0;
}
.ast-st-sites-cta {
	border: 1px solid #fff;
    border-radius: 9999px;
    display: flex;
    gap: 12px;
    justify-content: center;
    align-items: center;
    align-content: center;
    box-shadow: 0px 16px 40px -16px #00000040;
    z-index: 9;
    padding: 12px;
    position: absolute;
    bottom: 40px;
    background: rgb(228 228 228 / 50%);
    backdrop-filter: blur(4px);
}

.ast-st-sites-cta .ast-page-builder-ico {
    padding: 8px;
    background: #fff;
    border-radius: 24px;
    width: 20px;
    height: 20px;
}
.ast-st-sites-cta span {
	font-size: 16px;
    line-height: 20px;
    color: #0F172A;
    font-weight: 700;
    width: 100px;
    text-align: center;
    padding-right: 6px;
    border-right: 1px solid #fff;
}
#astra-sites-on-active {
	padding: 0;
}
@media screen and (max-width: 1199px) {
	.ast-col-right {
		display: none;
	}
	.ast-col-left {
		width: 100%;
		padding: 20px 24px;
		align-items: unset;
	}
	#astra-sites-on-active .astra-notice-container .notice-actions > button {
		width: 100%;
	}
}
@media screen and (max-width: 782px) {
	.ast-col-left {
		width: 100%;
		font-size: 14px;
		align-items: unset;
		padding-left: 15px;
		padding-right: 15px;
	}
	#astra-sites-on-active .astra-notice-container .notice-actions > button {
		font-size: 14px;
		width: 100%;
	}
	.ast-welcome-banner .notice-title {
		font-size: 22px;
	}
	.ast-welcome-banner .sub-notice-title,
	.ast-welcome-banner .description {
		font-size: 14px;
		line-height: 24px;
	}
}

@media screen and (min-width: 1199px) and (max-width: 1700px) {
	.ast-welcome-banner {
		max-height: 350px;
	}
	.ast-col-left {
		width: 45%;
	}
	.ast-col-right {
		width: 65%;
	}
	#astra-sites-on-active .astra-notice-container .notice-actions > button {
		font-size: 14px;
	}
	.ast-welcome-banner .notice-title {
		font-size: 24px;
		line-height: 36px;
	}
	.ast-welcome-banner .sub-notice-title {
		font-size: 13px;
	}
}
