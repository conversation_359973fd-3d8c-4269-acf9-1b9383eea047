/**
 * CSS code for all Astra Extensions admin screens
 *
 * @package Astra
 * @since 1.0.0
 */
.ast-clear:before, .ast-clear:after {
	content: " ";
	display: table;
}
.ast-clear:after {
	clear: both;
}

/**
 * Error Message
 */
#message {
    display: block;
    margin-right: 0;
}

/**
 * Astra Welcome Page
 */
.ast-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
}
.ast-container .ast-normal-cursor.hndle{
    cursor: initial;
}
.ast-container .postbox .hndle{
    background: #ffffff;
    border-bottom: 1px solid #e6e6e6;
}
.ast-container div.notice,
.ast-container div.updated,
.ast-container div.error{
	margin-top: 0;
}
.ast-container #poststuff h2{
	padding: 12px 12px;
}
.ast-flex{
	-js-display: flex;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-align-content: center;
    -ms-flex-line-pack: center;
    align-content: center;
}

/**
 * Astra Welcome screen sidebar
 */
.ast-sidebar .hndle .dashicons{
	margin: 0 0 0 5px;
}

/**
 * Astra Custom Notice for static position
 */
.ast-notice{
    margin-bottom: 20px;
}
.ast-notice.ast-notice-error{
    border-right-color: #dc3232;
}
.ast-notice{
    background: #fff;
    border-right: 4px solid #fff;
    box-shadow: 0 1px 1px 0 rgba( 0, 0, 0, 0.1 );
    padding: 1px 12px;
}
.ast-notice p{
    margin: 0.5em 0;
    padding: 2px;
}
