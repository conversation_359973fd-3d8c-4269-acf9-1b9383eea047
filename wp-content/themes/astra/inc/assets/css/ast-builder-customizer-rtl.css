.ahfb-builder-is-active .preview-desktop #customize-preview, .ahfb-builder-is-active .preview-tablet #customize-preview {
    height: auto;
}

.ahfb-grid-disabled {
    pointer-events: none;
    opacity: 0.4;
}

.ahfb-dragging-dropzones .ahfb-builder-add-item {
    display: none;
}

.customize-control-ast-builder-header-control .ahfb-builder-tab-toggle, .customize-control-ast-builder-header-control .ahfb-builder-section-shortcut {
    font-size: 13px;
    font-weight: 600;
    z-index: 10;
    color: #555D65;
    background: #eeeeee;
    vertical-align: top;
    border: none;
    padding: 8px 12px;
    border-radius: 0;
}

#customize-control-astra-settings-builder-header, #customize-control-astra-settings-builder-footer {
    background: #eee;
    backface-visibility: hidden;
    z-index: 10;
    height: 45px;
    border-top: 1px solid #ddd;
}

.ahfb-builder-hide #customize-control-astra-settings-builder-header, .ahfb-builder-hide #customize-control-astra-settings-builder-footer {
    transform: translateY(-628%);
    overflow: visible;
}

#customize-control-astra-settings-builder-header .ast-customize-control-description, #customize-control-astra-settings-builder-footer .ast-customize-control-description {
    margin: 0px;
    text-align: left;
}

.button.ahfb-builder-hide-button.ahfb-builder-tab-toggle {
	width: auto;
	padding-left: 20px;
	text-align: center;
}

.customize-control-ast-builder-header-control .ahfb-builder-tab-toggle:hover, .customize-control-ast-builder-header-control .ahfb-builder-section-shortcut:hover {
    background: #eeeeee;
    color: #0073aa;
    border-color: #dddddd;
    border-bottom: unset;
}

.ahfb-header-builder-active.ahfb-builder-hide .ast-builder-show-action, .ahfb-footer-builder-active.ahfb-builder-hide .ast-builder-show-action {
    display: block;
}

.ahfb-header-builder-active .ast-builder-show-action, .ahfb-header-builder-active.ahfb-builder-hide .ast-builder-hide-action, .ahfb-footer-builder-active .ast-builder-show-action, .ahfb-footer-builder-active.ahfb-builder-hide .ast-builder-hide-action {
    display: none;
}

.customize-control-ast-builder-header-control .ahfb-builder-tab-toggle span.dashicons, .customize-control-ast-builder-header-control .ahfb-builder-section-shortcut span.dashicons {
    font-size: 13px;
    vertical-align: text-top;
    line-height: 20px;
    font-weight: bold;
}

.customize-control-ast-builder-header-control .ahfb-builder-show-button.ahfb-builder-tab-toggle {
    visibility: hidden;
    margin-bottom: 20px;
    opacity: 0;
    bottom: 100%;
    top: auto;
}

#customize-control-astra-settings-builder-header,
#customize-control-astra-settings-builder-footer {
    display: flex !important;
    justify-content: space-between;
}

#customize-control-astra-settings-builder-header .ast-customize-control-title,
#customize-control-astra-settings-builder-footer .ast-customize-control-title {
    padding: 0 25px;
    text-align: center;
    color: #888;
}

#customize-control-astra-settings-builder-header .ast-customize-control-title .dashicons,
#customize-control-astra-settings-builder-footer .ast-customize-control-title .dashicons {
    margin-left: 10px;
}

#customize-theme-controls #sub-accordion-section-section-header-builder .customize-control, #customize-theme-controls #sub-accordion-section-section-footer-builder .customize-control {
    margin: 0;
    padding: 0;
}

#customize-theme-controls #sub-accordion-section-section-header-builder .customize-control .description {
    padding: 0 20px;
}

.ahfb-compontent-tabs {
    display: -webkit-box;
    display: flex;
    margin-top: -16px;
    margin-right: -24px;
    margin-left: -24px;
    padding: 0;
    margin-bottom: 0;
    border: 1px solid var(--ast-customizer-color-7);
    background: var(--ast-customizer-color-9);
    margin-bottom: 0;
}

.ahfb-compontent-tabs .ahfb-compontent-tabs-button {
	-webkit-box-flex: 1;
    flex: 1 1 0;
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 2px;
    font-style: normal;
    text-transform: uppercase;
    margin: 0;
    box-sizing: content-box;
    padding: 0 10px;
    cursor: pointer;
    border: 0;
    background: transparent;
    border-bottom: 3px solid transparent;
    border-radius: 0;
	color: var(--ast-customizer-color-6);
}

.ahfb-compontent-tabs .ahfb-compontent-tabs-button:not(:first-child) {
    margin-right: 0px;
}

.ahfb-compontent-tabs .ahfb-compontent-tabs-button:hover {
    box-shadow: none !important;
}

.ahfb-compontent-tabs .ahfb-compontent-tabs-button:not(.nav-tab-active):hover {
	background: var(--ast-customizer-color-9);
    color: var(--ast-customizer-color-8);
}

.ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active {
	border-bottom-color: var(--ast-customizer-color-1);
    color: var(--ast-customizer-color-8);
}

.ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active:focus {
    outline: 0;
    box-shadow: none;
}

/*Preview*/

.primary-menu-container .customize-partial-edit-shortcut, .secondary-menu-container .customize-partial-edit-shortcut, .footer-menu-container .customize-partial-edit-shortcut, span.customize-partial-edit-shortcut.customize-partial-edit-shortcut-header-desktop-items, .site-branding .site-title .customize-partial-edit-shortcut, .site-branding .site-description .customize-partial-edit-shortcut, .menu-toggle-open .customize-partial-edit-shortcut, .mobile-menu-container .customize-partial-edit-shortcut {
    display: none;
}

.site-header-focus-item {
    outline: 2px solid transparent;
    position: relative;
    transition: all 0.3s;
    box-shadow: 0 2px 1px rgba(46, 68, 83, 0);
}

.site-header-focus-item .customize-partial-edit-shortcut {
    opacity: 0;
    right: 0;
    transition: all 0.3s;
}

.site-header-focus-item .customize-partial-edit-shortcut button {
    border-radius: 0;
    border: 0;
    box-shadow: none;
}

.site-header-focus-item:hover {
    outline: 2px solid #0085ba !important;
    box-shadow: 0 2px 1px rgba(46, 68, 83, 0.15);
}

.site-header-focus-item:hover > * > .customize-partial-edit-shortcut {
    opacity: 1;
}

.site-footer-focus-item {
    outline: 2px solid transparent;
    position: relative;
    transition: all 0.3s;
    box-shadow: 0 2px 1px rgba(46, 68, 83, 0);
}

.site-footer-focus-item .customize-partial-edit-shortcut {
    opacity: 0;
    right: 0;
    transition: all 0.3s;
}

.site-footer-focus-item .customize-partial-edit-shortcut button {
    border-radius: 0;
    border: 0;
    box-shadow: none;
}

.site-footer-focus-item:hover {
    outline: 2px solid #0085ba !important;
    box-shadow: 0 2px 1px rgba(46, 68, 83, 0.15);
}

.site-footer-focus-item:hover > * > .customize-partial-edit-shortcut, .site-footer-focus-item:hover > * > *:first-child > .customize-partial-edit-shortcut {
    opacity: 1;
}

.customize-partial-edit-shortcut button {
    right: 0;
}

.ast-builder-grid-row-layout-contained > .customize-partial-edit-shortcut button {
    right: calc(-30px + -1.5rem);
}

/*------------------------------------------------------------------------------------------------------------------*/

#customize-control-astra-settings-header-sticky-link + #customize-control-astra-settings-header-transparant-link,
#customize-control-astra-settings-header-transparant-link + #customize-control-astra-settings-header-sticky-link {
    margin-top: 0;
}

/*controls.css*/
.customize-control-astra_builder_control {
    border: 0 !important;
}

.ahfb-builder-items {
    padding: 25px;
}

.ahfb-builder-sortable-panel {
    min-height: 46px;
    display: flex;
    flex: 1;
    padding: 0 10px;
    align-items: center;
    background-color: #ffffff;
}

.active-builder-row .ahfb-builder-sortable-panel, .ahfb-builder-group-horizontal:hover .ahfb-builder-sortable-panel {
    background-color: #ffffff;
}

.ahfb-builder-item {
    line-height: 32px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    height: auto;
    min-width: 90px;
    background: var(--ast-customizer-color-9);
    color: var(--ast-customizer-color-5);
    position: relative;
    white-space: nowrap;
    cursor: grab;
    margin: 0 4px;
    padding: 0 14px;
	border: 1px solid var(--ast-customizer-color-7);
    border-radius: 4px;
}

.ahfb-builder-item.item-has-controls:hover {
    border-top-right-radius: 0;
}

.ahfb-builder-item:hover, .ahfb-builder-item.active-builder-item {
    background: var(--ast-customizer-color-9);
    color: var(--ast-customizer-color-4);
    border: 1px solid var(--ast-customizer-color-1);
}

.ahfb-builder-area .ahfb-builder-drop:nth-last-child(2) .ahfb-builder-item:last-child {
    margin-left: 0;
}

.ahfb-builder-area .ahfb-builder-drop:first-child .ahfb-builder-item:first-child {
    margin-right: 0;
}

#customize-control-astra-settings-header-desktop-items .popup-vertical-group .ahfb-row-actions,
#customize-control-astra-settings-header-mobile-items .popup-vertical-group .ahfb-row-actions {
    position: inherit;
    height: 30px;
    width: 100%;
    border-bottom: none;
    border-radius: 2px 2px 0 0;
}

.customize-control-ast-builder .popup-vertical-group .ahfb-builder-item {
    margin: 4px;
}

#customize-control-astra-settings-header-desktop-items .popup-vertical-group .ahfb-builder-item:first-child,
#customize-control-astra-settings-header-mobile-items .popup-vertical-group .ahfb-builder-item:first-child {
    margin-top: 8px;
}
#customize-control-astra-settings-header-desktop-items .popup-vertical-group .ahfb-builder-item:last-child,
#customize-control-astra-settings-header-mobile-items .popup-vertical-group .ahfb-builder-item:last-child {
    margin-bottom: 8px;
}
#customize-control-astra-settings-header-mobile-items .popup-vertical-group .ahfb-builder-item,
#customize-control-astra-settings-header-desktop-items .popup-vertical-group .ahfb-builder-item {
	margin: 4px;
}

.ahfb-builder-item > .ahfb-builder-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    left: 0;
    cursor: pointer;
    margin-left: -10px;
    width: 28px;
    height: 28px;
    color: var( --ast-customizer-icon-color );
    background: transparent;
    border: 0;
    padding: 0;
    margin-right: 8px;
}

.ahfb-builder-item-icon svg {
    width: 16px;
    height: 16px;
}

.ahfb-builder-item-icon .dashicons-no-alt {
    width: 17px;
    height: 17px;
    line-height: 16px;
    align-self: center;
    color: #777777;
}

.active-builder-item .ahfb-builder-item-icon .dashicons-no-alt, .ahfb-builder-item-icon:hover .dashicons-no-alt, .ahfb-builder-item:hover .ahfb-builder-item-icon .dashicons-no-alt, .ahfb-builder-item:hover .ahfb-builder-item-icon .dashicons-admin-settings,.ahfb-builder-item:hover .ahfb-builder-item-icon .dashicons-admin-generic  {
    color: #111111;
}

.ahfb-builder-item.sortable-ghost {
    opacity: 0.4;
    box-shadow: none;
    opacity: 0.6;
    font-size: 0;
    background: #e8e8e8;
}

.ahfb-builder-item.sortable-ghost .ahfb-builder-item-icon {
    display: none;
}

.ahfb-builder-item.sortable-drag {
    z-index: 999999 !important;
}

.ahfb-builder-item.sortable-drag .ahfb-builder-item-icon:not(.ahfb-move-icon) {
    display: none;
}

.ahfb-builder-item-start {
    margin-bottom: 16px;
    min-height: 34px;
    display: flex;
}

.ahfb-builder-item-start .ahfb-builder-item {
    flex: 1;
    display: flex;
    width: 100%;
    box-sizing: border-box;
}

.ahfb-builder-item-start .ahfb-builder-item.sortable-drag {
    width: auto;
}

#accordion-section-section-header-builder, #accordion-section-section-footer-builder {
    display: none !important;
}

.ahfb-build-tabs {
    border-top: 1px solid #dddddd;
    padding-top: 0;
    border-bottom: 0;
}

.ahfb-build-tabs .nav-tab {
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-style: normal;
    height: 37px;
    transition: 0.5s;
    margin: 0;
    border-top: unset;
    border-right: unset;
    background: transparent;
    border-bottom: 4px solid transparent;
    border-left: 1px solid #dddddd;
    padding: 4px 20px 0;
    cursor: pointer;
    box-sizing: content-box;
}

.ahfb-build-tabs-button span {
    padding-bottom: 4px;
}

.ahfb-build-tabs .nav-tab .dashicons.dashicons-desktop {
    font-size: 14px;
    height: auto;
}

.ahfb-build-tabs .nav-tab:hover {
    box-shadow: none !important;
}

.ahfb-build-tabs .nav-tab.nav-tab-active {
    color: #191e23;
    box-shadow: none;
    border-bottom: 4px solid #191e23;
}

.ahfb-build-tabs .nav-tab:hover, .ahfb-build-tabs .nav-tab:focus {
    background: #ffffff;
    color: #0073aa;
}

.ahfb-build-tabs-button.nav-tab-active:hover {
    border-bottom-color: #0073aa;
}

#customize-theme-controls #sub-accordion-section-section-header-builder, #customize-theme-controls #sub-accordion-section-section-footer-builder {
    position: fixed !important;
    top: auto;
    right: 300px;
    left: 0;
    background: unset;
    border-top: unset;
    bottom: 0;
    visibility: visible;
    height: auto;
    width: auto;
    padding: 0;
    max-height: 60%;
    overflow: auto;
    transition: all 0.2s;
    transform: translateY(100%);
    backface-visibility: hidden;
}
@media (min-width: 1660px) {
    #customize-theme-controls #sub-accordion-section-section-header-builder, #customize-theme-controls #sub-accordion-section-section-footer-builder {
        right: 18%;
    }
}

.ahfb-header-builder-is-active #customize-theme-controls #sub-accordion-section-section-header-builder.ahfb-header-builder-active,
.ahfb-footer-builder-is-active #customize-theme-controls #sub-accordion-section-section-footer-builder.ahfb-footer-builder-active {
    transform: translateY(0%);
    visibility: visible;
    overflow: hidden;
}

.ahfb-header-builder-active > li.customize-section-description-container, .ahfb-footer-builder-active > li.customize-section-description-container {
    display: none !important;
}

.ahfb-builder-areas .ahfb-builder-group-horizontal {
    display: flex;
    height: 52px;
    margin-bottom: 0;
    background: #eeeeee;
    border: 1px dashed var(--ast-customizer-color-7);
    border-right: none;
    border-radius: 4px 0 0 4px;
    transition: all 0.15s;
    transform: translate(0, -2px);
}

.ahfb-builder-area .ahfb-builder-drop-left, .ahfb-builder-area .ahfb-builder-drop-left + .ahfb-builder-add-item .ahfb-builder-item-add-icon {
    border-radius: 2px 0 0 2px;
}

.ahfb-builder-area .ahfb-builder-drop-right, .ahfb-builder-area .ahfb-builder-drop-right + .ahfb-builder-add-item .ahfb-builder-item-add-icon {
    border-radius: 4px 0 0 4px;
}

.ahfb-builder-areas .ahfb-builder-group-horizontal:hover, .active-builder-row .ahfb-builder-group-horizontal, .ahfb-builder-areas:hover .ahfb-row-actions {
    border: 1px solid #dddddd;
    border-right: none;
    box-shadow: -5px 3px 10px rgb(0 39 36 / 7%);
}

.ahfb-builder-areas.popup-vertical-group .ahfb-builder-group-horizontal:hover,
.active-builder-row.popup-vertical-group .ahfb-builder-group-horizontal {
    box-shadow: 3px 3px 10px rgb(0 39 36 / 7%);
}

.ahfb-builder-areas:hover .ahfb-row-actions, .active-builder-row .ahfb-row-actions:hover {
    border: 1px solid #ddd;
    box-shadow: 3px 3px 10px rgb(0 39 36 / 7%);
}

.ahfb-builder-group-horizontal:hover .components-button.ahfb-row-actions {
    color: #007cba;
}

.ahfb-builder-row-items .ahfb-builder-areas:nth-child(2) .ahfb-builder-group-horizontal {
    margin-top: 15px;
    margin-bottom: 15px;
}

.ahfb-builder-row-items .ahfb-builder-areas:nth-child(3) .ahfb-builder-group-horizontal {
    margin-top: 9px;
}

.customize-control-ast-builder .ahfb-builder-items {
    padding-right: 30px;
}

.ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-area {
    display: flex;
}

.ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-area-left, .ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-area-right {
    flex: 1 1 0%;
}

.ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-area-right .ahfb-builder-drop-right, .ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-drop-left_center {
    justify-content: flex-end;
}

.ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-drop-left_center, .ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-drop-right_center {
    width: 0px;
    flex: 0;
    overflow: hidden;
}

.ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-area-center {
    min-width: 134px;
    border-right: 1px dashed var(--ast-customizer-color-7);
    border-left: 1px dashed var(--ast-customizer-color-7);
}

.ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-area-center.ahfb-dragging-dropzones, .ahfb-builder-areas.has-center-items .ahfb-builder-area-center {
    min-width: 120px;
}

.ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-area-center .ahfb-builder-sortable-panel {
    justify-content: center;
}

.ahfb-builder-areas.has-center-items .ahfb-builder-drop-left_center, .ahfb-builder-areas.has-center-items .ahfb-builder-drop-right_center {
    width: auto;
    flex: 1;
    overflow: visible;
}

.ahfb-builder-areas.has-center-items .ahfb-dragging-dropzones .ahfb-builder-drop-left_center {
    min-width: 100px;
}

.ahfb-builder-areas.has-center-items .ahfb-dragging-dropzones .ahfb-builder-drop-right_center {
    min-width: 100px;
}

.ahfb-builder-areas.popup-vertical-group {
    width: 200px;
    padding-left: 20px;
    padding-right: 0;
}

.ahfb-builder-areas.popup-vertical-group .ahfb-builder-group {
    height: auto;
    min-height: 160px;
    margin-bottom: 0;
}

.ahfb-builder-areas.popup-vertical-group .ahfb-builder-area {
    flex: auto;
    flex-direction: column;
}

.ahfb-builder-areas.popup-vertical-group .ahfb-builder-area .ahfb-builder-sortable-panel {
    min-height: 160px;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    padding: 0;
}

.ahfb-builder-areas.popup-vertical-group .ahfb-builder-area .ahfb-builder-sortable-panel .ahfb-builder-item {
    width: 90%;
    margin-top: 4px;
    margin-bottom: 4px;
    box-sizing: border-box;
}

.ahfb-builder-mode-footer .ahfb-builder-group.ahfb-builder-group-horizontal {
    display: grid;
}

.ahfb-builder-mode-footer .ahfb-builder-area:not(:first-child) {
    border-right: 1px solid #e5e5e5;
}

.ahfb-builder-mode-footer .ahfb-builder-area:first-child {
    border-right: 0;
}

.ahfb-builder-item-start button.ahfb-builder-item {
    border: 1px solid var(--ast-customizer-color-7);
    background: var(--ast-customizer-color-9);
    cursor: pointer;
    box-shadow: none !important;
	line-height: 36px;
	font-size: 13px;
	margin: 0;
}

.ast-builder-elements-section .ast-builder-elements-notice  {
    border: 1px dashed #b4b9be;
    background: transparent;
    padding: 10px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
}

.ahfb-footer-builder-is-active .in-sub-panel:not(.section-open) ul#sub-accordion-section-section-footer-builder-layout, .ahfb-header-builder-is-active .in-sub-panel:not(.section-open) ul#sub-accordion-section-section-header-builder-layout {
    transform: none;
    height: 100%;
    visibility: visible;
    overflow: visible;
    top: 75px;
    padding: 24px;
}

.ahfb-footer-builder-is-active .in-sub-panel:not(.section-open) ul#sub-accordion-section-section-footer-builder-layout .customize-section-description-container.section-meta, .ahfb-header-builder-is-active .in-sub-panel:not(.section-open) ul#sub-accordion-section-section-header-builder-layout .customize-section-description-container.section-meta, .ahfb-footer-builder-is-active .in-sub-panel:not(.section-open) #sub-accordion-section-section-footer-builder-layout .customize-section-description-container, .ahfb-header-builder-is-active .in-sub-panel:not(.section-open) ul#sub-accordion-section-section-header-builder-layout .customize-section-description-container, .ahfb-footer-builder-is-active .in-sub-panel:not(.section-open) #sub-accordion-panel-panel-footer-builder-group .accordion-section.control-section, .ahfb-header-builder-is-active .in-sub-panel:not(.section-open) #sub-accordion-panel-panel-header-builder-group .accordion-section.control-section {
    display: none !important;
}

.ahfb-footer-builder-is-active .preview-desktop #customize-preview, .ahfb-footer-builder-is-active .preview-tablet #customize-preview, .ahfb-header-builder-is-active .preview-desktop #customize-preview, .ahfb-header-builder-is-active .preview-tablet #customize-preview {
    height: auto;
    margin-bottom: -1px;
    bottom: 46px;
}

.customize-control-ast-builder .ahfb-builder-items {
    display: flex;
}

.customize-control-ast-builder .ahfb-builder-row-items {
    flex: 1;
    z-index: 9;
}

.ahfb-builder-areas .components-button.ahfb-row-actions {
    background: var(--ast-customizer-color-13);
    color: var(--ast-customizer-color-6);
    text-transform: uppercase;
    font-size: 8px;
    font-weight: 500;
    text-align: center;
    position: absolute;
    top: -2px;
    right: 0;
    width: 30px;
    transition: all 0.2s;
    border-radius: 0 4px 4px 0;
    border: 1px dashed var(--ast-customizer-color-7);
    height: 100%;
    padding: 0;
    overflow: visible;
}

.ahfb-builder-areas.ahfb-builder-mode-header.popup-vertical-group .ahfb-row-actions {
    height: 50px;
    align-self: center;
}

.customize-control-ast-builder .popup-vertical-group .ahfb-row-actions {
    font-size: 10px;
}

#customize-control-astra-settings-header-desktop-items .popup-vertical-group .ahfb-row-actions .dashicon,
#customize-control-astra-settings-header-mobile-items .popup-vertical-group .ahfb-row-actions .dashicon {
    margin-right: 10px;
    margin-left: 5px;
}

.ahfb-builder-mode-header[data-row="above"] .ahfb-row-actions, .ahfb-builder-mode-header[data-row="below"] .ahfb-row-actions,
.ahfb-builder-mode-footer[data-row="above"] .ahfb-row-actions, .ahfb-builder-mode-footer[data-row="primary"] .ahfb-row-actions {
    cursor: default;
}

.active-builder-row .components-button.ahfb-row-actions {
    background: #007cba;
    border-color: #007cba;
    color: white;
    box-shadow: 9px 3px 10px rgb(0 39 36 / 7%);
}

.active-builder-row .ahfb-builder-group.ahfb-builder-group-horizontal .ahfb-row-actions {
    color: #ffffff;
}

.ahfb-builder-areas .components-button.ahfb-row-actions .dashicon {
    font-size: 13px;
    line-height: 20px;
    transition: 0s;
    margin: 0 auto;
}

.ahfb-row-actions svg {
    width: 12px;
    height: 12px;
    margin: 0 auto;
}

.ahfb-builder-areas .components-button.ahfb-row-actions:hover, .ahfb-builder-areas .components-button.ahfb-row-actions:focus {
    background: var(--ast-customizer-color-1) !important;
    color: white !important;
    border-color: var(--ast-customizer-color-1);
}

.ahfb-builder-areas .components-button.ahfb-row-actions:hover {
	background: white;
    color: var(--ast-customizer-color-1);
	border: 1px solid var(--ast-customizer-color-1);
	border-left: 1px dashed var(--ast-customizer-color-7);
}

.ahfb-builder-areas:hover .components-button.ahfb-row-actions {
	background: white;
}
.ahfb-builder-areas.active-builder-row:hover .components-button.ahfb-row-actions {
    color: var(--ast-customizer-color-6);
}

.ahfb-builder-areas .components-button.ahfb-row-actions:hover ~ .ahfb-builder-group-horizontal {
	border: 1px solid var(--ast-customizer-color-1);
	border-right: none;
    box-shadow: -5px 3px 10px rgb(0 39 36 / 7%);
	background-color: white;
}

.ahfb-builder-areas .components-button.ahfb-row-actions:hover ~ .ahfb-builder-group-horizontal .ahfb-builder-item-add-icon {
	background-color: white;
}


.components-button:not(:disabled):not([aria-disabled="true"]):not(.is-secondary):not(.is-primary):not(.is-tertiary):not(.is-link):not(.components-color-picker__saturation-pointer):hover {
    box-shadow: none;
}

.ahfb-builder-item-add-icon.dashicons-plus-alt2:before {
    content: "\f543";
    display: inline-block;
    opacity: 0;
    transition: 0.2s;
    -webkit-font-smoothing: antialiased;
    font: normal 20px/1 'dashicons';
    font-size: 16px;
}

.ahfb-builder-item-add-icon:hover:before {
    opacity: 1;
    color: #777777;
}

.ahfb-builder-areas {
    position: relative;
    padding-right: 30px;
    z-index: 10;
}

.ahfb-builder-group.ahfb-builder-group-horizontal[data-setting="bottom"] {
    margin-bottom: 0
}

.footer-row-columns-2.footer-row-layout-left-golden .ahfb-builder-area-1 {
    flex: 0 1 66.67%;
}

.footer-row-columns-2.footer-row-layout-left-golden .ahfb-builder-area-2 {
    flex: 0 1 33.33%;
}

.footer-row-columns-2.footer-row-layout-right-golden .ahfb-builder-area-1 {
    flex: 0 1 33.33%;
}

.footer-row-columns-2.footer-row-layout-right-golden .ahfb-builder-area-2 {
    flex: 0 1 66.67%;
}

.footer-row-columns-3.footer-row-layout-left-half .ahfb-builder-area, .footer-row-columns-3.footer-row-layout-right-half .ahfb-builder-area, .footer-row-columns-3.footer-row-layout-center-half .ahfb-builder-area {
    flex: 0 1 25%;
}

.footer-row-columns-3.footer-row-layout-left-half .ahfb-builder-area-1, .footer-row-columns-3.footer-row-layout-right-half .ahfb-builder-area-3,.footer-row-columns-3.footer-row-layout-center-half .ahfb-builder-area-2 {
    flex: 0 1 50%;
}

.footer-row-columns-3.footer-row-layout-center-wide .ahfb-builder-area {
    flex: 0 1 20%;
}

.footer-row-columns-3.footer-row-layout-center-wide .ahfb-builder-area-2 {
    flex: 0 1 60%;
}

.footer-row-columns-3.footer-row-layout-center-exwide .ahfb-builder-area {
    flex: 0 1 15%;
}

.footer-row-columns-3.footer-row-layout-center-exwide .ahfb-builder-area-2 {
    flex: 0 1 70%;
}

.footer-row-columns-4.footer-row-layout-left-forty .ahfb-builder-area, .footer-row-columns-4.footer-row-layout-right-forty .ahfb-builder-area {
    flex: 1;
}

.footer-row-columns-4.footer-row-layout-left-forty .ahfb-builder-area-1, .footer-row-columns-4.footer-row-layout-right-forty .ahfb-builder-area-4 {
    flex: 2;
}

.ahfb-builder-areas.footer-row-direction-column .ahfb-builder-group-horizontal .ahfb-builder-area .ahfb-builder-drop {
    flex-direction: column;
    align-items: normal;
}

.ahfb-builder-areas.footer-row-direction-column .ahfb-builder-group-horizontal .ahfb-builder-area .ahfb-builder-drop .ahfb-builder-item {
    margin: 4px;
}

.ahfb-builder-item-start button.ahfb-builder-item:hover {
    background: var(--ast-customizer-color-13);
	border-style: solid;
    color: var(--ast-customizer-color-5);
}

.ahfb-builder-item > .ahfb-builder-item-icon.ahfb-move-icon {
    margin-right: -10px;
    transform: rotate(-90deg);
    margin-left: 0;
    cursor: grab;
    width: 18px;
    opacity: 0.7;
}

.ahfb-builder-item-text {
    flex-grow: 1;
}

.ahfb-builder-item-start.ahfb-move-item .ahfb-builder-item {
    justify-content: flex-start;
}

.ahfb-availbile-items-title {
    padding: 10px 0;
}

.ahfb-builder-item > .ahfb-builder-item-icon.ahfb-builder-item-focus-icon svg {
    width: 14px;
}

.ahfb-builder-area .ahfb-builder-add-item {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

.ahfb-popover-builder-list .ahfb-all-coponents-used {
    width: inherit;
    text-align: center;
    font-weight: 400;
}

.ahfb-builder-area {
    position: relative;
}

.ahfb-builder-area .ahfb-builder-item {
    z-index: 10;
}

.ahfb-builder-mode-footer .ahfb-builder-drop {
    justify-content: center;
}

.ahfb-builder-area .ahfb-builder-item-add-icon {
    display: block;
    position: absolute;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    border-radius: 0;
    background: var(--ast-customizer-color-13);
    border: 0;
    height: auto;
    width: auto;
    padding: 0;
    min-width: 100%;
    z-index: 1;
    transition: all .2s ease-in-out;
    color: #808285;
}

.ahfb-builder-area .ahfb-builder-item-add-icon:hover, .ahfb-builder-area .ahfb-builder-item-add-icon:focus {
    color: #191e23;
    background: #ffffff;
    box-shadow: unset;
    outline: none;
}

.ahfb-builder-group:hover .ahfb-builder-item-add-icon, .ahfb-builder-group:focus .ahfb-builder-item-add-icon, .active-builder-row .ahfb-builder-area .ahfb-builder-item-add-icon {
    background: #ffffff;
}

.active-builder-section.ahfb-builder-section-shortcut {
    color: #0073aa;
}

.ast-customize-control-title span.dashicons.dashicons-external {
    margin-right: 10px;
}

.ahfb-header-builder-is-active #customize-theme-controls #sub-accordion-section-section-header-builder.ahfb-header-builder-active.ahfb-builder-hide,
.ahfb-header-builder-is-active .preview-only.collapsed #customize-theme-controls #sub-accordion-section-section-header-builder.ahfb-header-builder-active.ahfb-builder-hide,  .ahfb-footer-builder-is-active .in-sub-panel #customize-theme-controls #sub-accordion-section-section-footer-builder.ahfb-footer-builder-active.ahfb-builder-hide {
    transform: translateY(100%);
    overflow: visible;
}

li#customize-control-astra-settings-header-desktop-items, li#customize-control-astra-settings-header-mobile-items, li#customize-control-astra-settings-footer-desktop-items {
    transition: all 0.3s;
    background: #eeeeee;
    border-top: 1px solid #dddddd;
}

.components-popover__content .ahfb-popover-builder-list .ahfb-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-gap: 4px;
    width: 420px;
    max-height: 340px;
    overflow: auto;
}

.components-popover__content {
    box-shadow: rgba(50, 50, 93, 0.25) 0px 0px 0px -2px, rgba(50, 50, 93, 0.25) 0px 3px 7px -3px;
}

.ahfb-popover-builder-list .ahfb-radio-container-control .components-button.is-tertiary {
    font-size: 12px;
    margin: 0;
    display: grid;
    text-transform: capitalize;
    border: 1px solid transparent;
}

.ahfb-builder-areas.has-center-items .ahfb-builder-add-item.center-on-right {
    left: 50%;
}

.ahfb-builder-areas.has-center-items .ahfb-builder-add-item.center-on-left {
    right: 50%;
}

.ahfb-builder-area .ahfb-builder-add-item.left-center-on-left, .ahfb-builder-area .ahfb-builder-add-item.right-center-on-right {
    display: none;
}

.ahfb-builder-areas.has-center-items .ahfb-builder-add-item.left-center-on-left {
    display: block;
    left: 50%;
}

.ahfb-builder-areas.has-center-items .ahfb-builder-add-item.right-center-on-right {
    display: block;
    right: 50%;
}

.ahfb-builder-area-right .left-center-on-left, .ahfb-builder-area-left .right-center-on-right, .ahfb-builder-area-center .ahfb-builder-add-item, .ahfb-builder-area-right .ahfb-builder-add-item {
    border-right: unset;
}

.preview-only.collapsed #customize-theme-controls #sub-accordion-section-section-header-builder.ahfb-header-builder-active, .preview-only.collapsed #customize-theme-controls #sub-accordion-section-section-footer-builder.ahfb-footer-builder-active {
    transform: translateY(0%);
    right: 0;
}

.preview-only.collapsed #customize-theme-controls #sub-accordion-section-section-header-builder.ahfb-header-builder-active .ahfb-build-tabs {
    padding-right: 40px;
}

.ahfb-range-control .ahfb-responsive-controls-content {
    display: flex;
}

.ahfb-range-control .components-button {
    height: 28px;
}

.ahfb-control-field.radio-btn-width-50 .ahfb-radio-container-control {
    flex-wrap: wrap;
}

.ahfb-control-field.radio-btn-width-50 .ahfb-radio-container-control .components-button.is-tertiary {
    min-width: 45%;
    margin: 4px;
}

.ahfb-responsive-control-bar {
    display: flex;
    position: relative;
    margin-bottom: 10px;
}

.ahfb-responsive-control-bar .floating-controls {
	padding-right: 5px;
}

.ahfb-responsive-control-bar .floating-controls .components-button.is-tertiary:not(.active-device) {
    color: #A0AEC0;
}

.ahfb-responsive-control-bar .floating-controls .components-button.is-tertiary:not(.active-device):hover, .ahfb-responsive-control-bar .floating-controls .components-button.is-tertiary:hover:not(:disabled) {
    color: #718096;
    box-shadow: none;
}

.ahfb-responsive-control-bar .floating-controls .components-button.active-device.is-tertiary:not(.active-device):hover {
    box-shadow: none;
}

.ahfb-responsive-control-bar .floating-controls .components-button {
    height: 18px;
    padding-top: 0;
    padding-bottom: 0;
    box-shadow: none;
}

.ahfb-responsive-control-bar .floating-controls .components-button svg {
    height: 13px;
    width: 16px;
    fill: none;
}

.ahfb-responsive-control-bar .floating-controls .components-button:focus:not(:disabled) {
    color: #007cba;
    box-shadow: none
}

.ahfb-responsive-control-bar .floating-controls .components-button-group {
    display: flex;
    border: 0;
}

.ahfb-sorter-drop-social_item_group .components-button-group {
    border: 1px solid #dddddd;
}

.ahfb-control-field {
    position: relative;
}

.ahfb-control-field .customize-control-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    letter-spacing: 0.1px;
    line-height: 18px;
}

.ahfb-sorter-drop-social_item_group .ahfb-radio-container-control {
    display: block;
    padding: 10px;
    background: #f9f9f9;
    background-image: linear-gradient(-45deg, #ddd 25%, transparent 25%, transparent 75%, #ddd 75%, #ddd), linear-gradient(-45deg, #ddd 25%, transparent 25%, transparent 75%, #ddd 75%, #ddd);
    background-size: 16px 16px;
    background-position: 100% 0, 8px 8px;
    background-color: #f6f6f6;
    border: 0;
}
.ahfb-sorter-drop-social_item_group .ahfb-sorter-item-panel-content .components-base-control {
	margin-bottom: 15px;
}
.ahfb-sorter-drop-social_item_group .ahfb-sorter-item-panel-content .components-base-control  .components-base-control__label {
	margin-bottom: 4px;
}

.ahfb-radio-container-control .components-button.is-tertiary {
    align-items: stretch;
    font-size: 13px;
    font-weight: 400;
    font-style: normal;
    text-transform: uppercase;
    height: auto;
    line-height: normal;
    margin: 0;
    padding: 13px 20px;
    border-radius: 4px;
    background: transparent;
    color: #32373c;
    white-space: normal;
    box-shadow: none;
}

.components-button.builder-add-btn svg {
    min-width: 20px;
    min-height: 20px;
    max-width: 20px;
    max-height: 24px;
}

.components-button.builder-add-btn .add-btn-icon {
    padding-bottom: 7px;
}

.ahfb-radio-container-control .components-button.is-tertiary:not(:disabled):not([aria-disabled=true]):hover, .ahfb-radio-container-control .components-button.is-tertiary:not(:disabled):not([aria-disabled=true]):active {
    color: #191e23;
    box-shadow: 0px 8px 48px -8px #00000029;
    border-color: #E5E7EB;    
    background: transparent;
    border-radius: 6px;
}

.ahfb-radio-container-control .components-button.pro-feature.is-tertiary:not(:disabled):not([aria-disabled=true]):hover, .ahfb-radio-container-control .components-button.is-tertiary:not(:disabled):not([aria-disabled=true]):active {
    color: #191e23;
    box-shadow: none;
    border: 1px solid #E5E7EB;
    background: transparent;
    border-radius: 6px;
}

.ahfb-radio-container-control .components-button.is-tertiary.active-radio {
    background: #007cba;
    color: #ffffff;
}

.ahfb-radio-container-control .components-button.is-tertiary.active-radio:not(:disabled):not([aria-disabled=true]):hover {
    color: #32373c;
    border-color: #007cba;
}

.ahfb-social-type .ahfb-radio-container-control button:not(:first-child) {
    margin-right: 15px;
}

.ahfb-radio-container-control .components-button.is-tertiary .ahfb-icon-set {
    display: flex;
}

.ahfb-radio-container-control.ahfb-icon-set-container-control {
    margin-top: 10px;
}

.ahfb-radio-container-control.ahfb-icon-set-container-control .components-button.is-tertiary {
    padding: 5px;
    height: 50px;
}

.ahfb-radio-container-control.ahfb-icon-set-container-control .components-button.is-tertiary svg {
    width: 100%;
    height: auto;
    max-height: 100%;
}

.ahfb-control-field.ahfb-color-control {
    display: flex;
}

.ahfb-control-field.ahfb-color-control .customize-control-title {
    flex-grow: 2;
}

.components-popover.ahfb-popover-color .components-popover__content {
    padding: 15px 15px 0px;
    box-sizing: initial;
    background: #ffffff;
    border-radius: 4px;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 8px 16px;
}

.ahfb-popover-tabs.ahfb-background-tabs .components-tab-panel__tabs {
    display: flex;
    border-bottom: 1px solid #dadada;
    margin-top: -5px;
    margin-bottom: 15px;
}

.ahfb-popover-tabs.ahfb-background-tabs .components-tab-panel__tabs .components-button {
    display: flex;
    flex: 1;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    font-style: normal;
    height: 36px;
    text-transform: uppercase;
    border: 0;
    border-bottom: 4px solid transparent;
    border-radius: 0;
    margin-bottom: -1px;
    opacity: 0.6;
}

.ahfb-popover-tabs.ahfb-background-tabs .components-tab-panel__tabs .components-button:focus {
    outline: 0;
    box-shadow: none;
}

.ahfb-popover-tabs.ahfb-background-tabs .components-tab-panel__tabs .components-button:hover {
    box-shadow: none !important;
    opacity: 1;
    border-bottom: 4px solid #dadada;
}

.ahfb-popover-tabs.ahfb-background-tabs .components-tab-panel__tabs .components-button.active-tab {
    border-bottom-color: #007cba;
    opacity: 1;
}

.components-popover__content .ahfb-radio-container-control {
    padding: 10px 5px;
    background: white;
}

.ahfb-control-field .ahfb-background-tabs .customize-control-title {
    padding-top: 10px;
    font-size: 12px;
    display: block;
}

.ahfb-control-field.ahfb-background-control .ahfb-responsive-control-bar .floating-controls {
    display: flex;
    align-items: center;
    margin-right: 0px;
}

.ahfb-control-field.ahfb-background-control .ahfb-responsive-control-bar .customize-control-title {
    flex-grow: 1;
}

.ahfb-control-field.ahfb-background-control .ahfb-responsive-controls-content {
    display: flex;
    justify-content: flex-end;
}

.ahfb-control-field.ahfb-palette-control.ahfb-color-control {
    display: block;
}

.ahfb-palette-header {
    display: flex;
    align-items: center;
}

.ahfb-palette-colors {
    display: flex;
    padding: 20px 0 0;
    justify-content: space-between;
}

.ahfb-palette-header .components-button-group .components-button.is-tertiary {
    color: #A0AEC0;
    border: 1px solid #A0AEC0;
    height: 30px;
    font-size: 12px;
    padding: 0 4px;
    box-shadow: none;
}

@media (max-width: 1800px) {
    .ahfb-palette-header .components-button-group .components-button.is-tertiary {
        font-size: 10px;
        padding: 0 2px;
    }
}

@media (max-width: 1400px) {
    .ahfb-control-field .customize-control-title {
        font-size: 13px;
    }
}

.ahfb-palette-header .components-button-group .components-button.is-tertiary.active-palette {
    color: #ffffff;
    border: 1px solid #007cba;
    background: #007cba;
}

.ahfb-border-control .ahfb-responsive-controls-content {
    display: flex;
    justify-content: flex-end;
}

.ahfb-border-control .ahfb-responsive-controls-content input.components-text-control__input {
    border: 1px solid #e2e4e7;
    width: 60px;
}

.ahfb-border-control .ahfb-responsive-controls-content .color-button-wrap {
    display: inline-flex;
}

.ahfb-select-units select.components-select-control__input {
    width: 100%;
    margin: 0 0 2px 0;
    border: 1px solid #e2e4e7;
}

.ahfb-control-field.ahfb-title-control {
    background: #f9f9f9;
    margin-bottom: -13px;
    margin-top: -17px;
    margin-right: -24px;
    margin-left: -24px;
    border-top: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;
    padding: 12px 20px;
}

.ahfb-control-field.ahfb-title-control .customize-control-title {
    font-size: 16px;
    font-weight: 700;
    letter-spacing: .3px;
}

.ahfb-locked .components-button.is-single {
    border: 1px solid #e2e4e7;
    background-color: #ffffff;
    display: flex;
    height: 30px;
}

.ahfb-locked .components-button svg {
    width: 16px;
}

.ahfb-radio-container-control .components-button.is-tertiary svg {
    width: 100%;
    height: 100%;
    max-height: 100%;
}

/* .ahfb-row-layout-control .ahfb-responsive-controls-content .components-button svg, .ahfb-icon-set-control .ahfb-radio-container-control .components-button svg {
    width: 75px;
    height: 50px;
} */

#customize-control-astra-settings-header-trigger-icon .components-button.is-tertiary.active-radio svg, #customize-control-astra-settings-header-trigger-icon .components-button.is-tertiary svg,
#customize-control-astra-settings-header-trigger-icon svg {
	width: 52px;
	height: 50px;
	margin-left: 10px;
}

#customize-control-astra-settings-header-trigger-icon input:checked + label .ahfb-svg-iconset.ast-inline-flex::after {
    display: none;
}

#customize-control-astra-settings-header-trigger-icon input:checked + label .ahfb-svg-iconset.ast-inline-flex svg path {
    fill: red;
}

#customize-control-astra-settings-header-trigger-icon .components-button-group {
    display: inline-flex;
}

.ahfb-radio-container-control .components-button .ahfb-icon-set {
    width: 100%;
    height: 100%;
}

#customize-control-page_layout .components-button-group.ahfb-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}

#customize-control-page_layout .components-button-group.ahfb-radio-container-control .components-button.is-tertiary {
    min-width: 25%;
    padding: 10px;
    margin: 0;
    min-height: 80px;
}

#customize-control-page_title_layout .components-button-group.ahfb-radio-container-control .components-button.is-tertiary {
    min-height: 90px;
    padding: 10px;
}

#customize-control-page_title_layout .ahfb-radio-container-control .components-button.is-tertiary:not(:first-child) {
    margin-right: 10px;
}

.ahfb-radio-dashicon {
    max-width: 20px;
}

.ahfb-sorter-item-panel-header {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
}

.ahfb-sorter-item-panel-header .ahfb-sorter-title {
    flex-grow: 2;
    font-size: 13px;
    line-height: 18px;
}

.ahfb-sorter-item-panel-header .ahfb-sorter-visiblity {
    border-radius: 0;
    border-left: 1px solid #A0AEC0;
}

.ahfb-sorter-item-panel-header .ahfb-sorter-item-expand, .ahfb-sorter-item-panel-header .ahfb-sorter-item-remove {
    border-radius: 2px;
    position: relative;
    height: 18px;
    padding: 0;
}

.ahfb-sorter-item-panel-header .ahfb-sorter-item-expand:focus, .ahfb-sorter-item-panel-header .ahfb-sorter-item-remove:focus {
    box-shadow: none;
    outline: none;
}

.ahfb-sorter-item-panel-header .ahfb-sorter-item-expand svg, .ahfb-sorter-item-panel-header .ahfb-sorter-item-remove svg {
    fill: revert-layer;
    vertical-align: middle;
}

.ahfb-sorter-item-panel-header .ahfb-sorter-item-remove:hover {
    background-color: var( --ast-customizer-color-7 );
}

.ahfb-sorter-drop .ahfb-sorter-item {
    height: auto;
    background: var( --ast-customizer-color-10 );;
    position: relative;
    border: 1px solid var( --ast-customizer-color-7 );
    white-space: nowrap;
    margin: 0 0 16px;
    border-radius: 3px;
    line-height: 28px;
}

.ahfb-sorter-row .ahfb-sorter-item {
    padding: 8px;
}

.ahfb-sorter-drop .ahfb-sorter-item:hover, .ahfb-sorter-drop .ahfb-sorter-item:focus, .ahfb-sorter-item.open {
    border-color: var( --ast-customizer-color-1 );
}

.customize-control-ast-social-icons .ahfb-sorter-drop-social_item_group .ahfb-sorter-item-panel-content .components-text-control__input{
    border-color: var(--ast-customizer-border-color);
}

.customize-control-ast-social-icons .ahfb-sorter-drop-social_item_group .ahfb-sorter-item-panel-content .components-text-control__input:focus, .customize-control-ast-social-icons .ahfb-sorter-drop-social_item_group .ahfb-sorter-item-panel-content .components-text-control__input:hover  {
    border-color: var(--ast-customizer-color-1);
}

.customize-control-ast-social-icons .ahfb-social-add-area .ahfb-sorter-add-item:hover {
    background: var(--ast-customizer-color-1);
}

.customize-control-ast-social-icons .ahfb-social-add-area .ahfb-sorter-add-item {
    box-shadow: none !important;
 }

.ahfb-sorter-drop .ahfb-sorter-item.sortable-chosen {
    border-color: var( --ast-customizer-color-1 );
}

.customize-control-ast-social-icons .ahfb-sorter-item-panel-content, .ahfb-sorter-item-panel-content.expanded-below {
    padding: 12px;
    box-shadow: 0px 6px 24px 0px #0000003D;
    margin-bottom: 20px;
    margin-top: -8px;
    border-radius: 3px;
    gap: 14px;
    background: var( --ast-customizer-color-9 );
    height: 275px;
}

.ahfb-sorter-item-panel-content.expanded-below {
    height: auto;
}

.ahfb-sorter-item-panel-content {
    padding: 12px;
}

.ahfb-sorter-drop, .ahfb-sorter-item-panel-content .components-base-control__field {
    display: flex;
    flex-direction: column;
}

.ahfb-sorter-item-panel-content .components-button.ahfb-sorter-item-remove {
    color: #b52727;
}

.sortable-style-tabs .components-tab-panel__tabs {
    display: flex;
    border-bottom: 1px solid #dadada;
    margin-bottom: 15px;
}

.sortable-style-tabs .components-tab-panel__tabs .components-button {
    display: flex;
    -webkit-box-flex: 1;
    flex: 1;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    font-style: normal;
    height: 36px;
    text-transform: uppercase;
    border: 0;
    border-bottom: 2px solid transparent;
    border-radius: 0;
    margin-bottom: -1px;
    opacity: 0.6;
    box-shadow: none;
}

.sortable-style-tabs .components-tab-panel__tabs .components-button.active-tab {
    opacity: 1;
    border-bottom-color: #0071a1;
    color: #0073aa;
    background-image: linear-gradient(-180deg, #f3f5f6, #ffffff);
}

.ahfb-social-add-area, .ahfb-language-selector-add-area {
    display: flex;
}

.ahfb-social-add-area .components-base-control, .ahfb-language-selector-add-area .components-base-control {
    flex-grow: 1;
    padding-left: 10px;
}

.ahfb-social-add-area .components-select-control {
    border-radius: 3px;
}

.ahfb-social-add-area .ahfb-sorter-add-item, .ahfb-language-selector-add-area .ahfb-sorter-add-item {
    height: 31px;
    line-height: normal;
    padding: 0 12px;
    background: #fff;
    color: var(--ast-customizer-color-1);
    border: 1px solid var(--ast-customizer-color-1);
    display: flex;
    gap: 2px;
}

.ahfb-language-selector-add-area .ahfb-sorter-add-item {
    color: var(--ast-customizer-color-1);
    border: 1px solid var(--ast-customizer-color-1);
    padding: 7px;
    width: 31px;
}

.ahfb-language-selector-add-area .ahfb-sorter-add-item:hover {
    border: 1px solid var(--ast-customizer-color-1);
    background: var(--ast-customizer-color-1) !important;
}

.ahfb-sorter-item-panel-content .components-button.button-add-media {
    display: block;
    margin-bottom: 20px;
    height: auto;
}

.ahfb-sorter-item-panel-content .ahfb-radio-container-control .components-button.is-tertiary {
    padding: 12px;
}

.components-button.ahfb-sorter-item-expand.item-is-hidden:before {
    opacity: 1;
}

.ahfb-sorter-item-panel-content > * {
    margin: 10px 0;
}
.ahfb-sorter-row .customize-control-description {
	white-space: pre-wrap;
}

button.components-button.ahfb-sorter-item-expand {
    transition: 0.5s;
    position: relative;
}

@media (max-width: 1660px) {
    .ahfb-locked .components-button.is-single, .ahfb-range-control .components-button {
        padding: 0 2px;
    }

    .components-button.has-icon.has-text svg {
        margin-left: 3px;
        max-width: 20px;
    }

    .ahfb-locked .components-button.is-single svg {
        width: 14px;
    }
}

.ahfb-meta-sorter .ahfb-radio-container-control .components-button.is-tertiary svg {
    max-width: 12px;
    margin: 0 auto;
}

.ahfb-sorter-item-panel-content .components-range-control .components-base-control__field {
    flex-direction: row;
}

.ahfb-sorter-item-panel-content .components-range-control .components-base-control__field input.components-range-control__number {
    width: auto;
}

.ahfb-sorter-item-panel-content .ahfb-radio-container-control {
    margin-bottom: 10px;
    text-align: center;
}

.sorter-sub-option {
    padding: 12px 12px 0px;
    border: 1px solid #bbb;
    margin-bottom: 12px;
}

.meta-label-input-control {
    display: flex;
    margin-bottom: 6px;
}

.ahfb-label-visiblity svg {
    width: 14px;
}

.components-button.ahfb-label-visiblity {
    height: 30px;
}

.label-is-hidden .components-text-control__input {
    opacity: 0.2;
    pointer-events: none;
}

.ahfb-icon-set-control.ahfb-three-col .components-button-group.ahfb-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}

.ahfb-icon-set-control.ahfb-three-col .components-button-group.ahfb-radio-container-control .components-button.is-tertiary {
    min-width: 25%;
    padding: 10px;
    margin: 0;
    min-height: 90px;
}

.ahfb-icon-set-control.ahfb-three-col .components-button-group.ahfb-radio-container-control .components-button.is-tertiary svg {
    max-width: 70px;
}

.ahfb-icon-set-control .components-button-group.ahfb-radio-container-control .components-button.btn-flex-col.is-tertiary {
    flex-direction: column;
    font-size: 10px;
}

.ahfb-icon-set-control .components-button-group.ahfb-radio-container-control .components-button.btn-flex-col.is-tertiary .ahfb-icon-set {
    margin-bottom: 3px;
    display: block;
}

.ahfb-radio-icon-control .components-button-group.ahfb-radio-container-control .components-button.is-tertiary, #customize-control-astra-settings-header-trigger-icon .components-button-group.ahfb-radio-container-control .components-button.is-tertiary {
    min-width: 25%;
    padding: 0;
    margin-left: 12px;
    min-height: 40px;
}

.components-button-group.ahfb-radio-container-control .components-button.is-tertiary.active-radio {
    background: unset;
}

#customize-control-astra-settings-hba-footer-layout .components-button .ahfb-radio-icon svg,
#customize-control-astra-settings-hb-footer-layout .components-button .ahfb-radio-icon svg,
#customize-control-astra-settings-hbb-footer-layout .components-button .ahfb-radio-icon svg {
    fill: none;
}

#customize-control-astra-settings-hba-footer-layout .components-button-group.ahfb-radio-container-control .active-radio.ast-radio-img-svg svg rect, 
#customize-control-astra-settings-hb-footer-layout  .components-button-group.ahfb-radio-container-control .active-radio.ast-radio-img-svg svg rect,
#customize-control-astra-settings-hbb-footer-layout  .components-button-group.ahfb-radio-container-control .active-radio.ast-radio-img-svg svg rect {
    stroke: var(--ast-customizer-color-1);
}

.components-button-group.ahfb-radio-container-control .ast-radio-img-svg:not(.active-radio):hover svg {
    background-color: var(--ast-customizer-color-13);
}

.active-radio .ahfb-icon-set svg {
    background: #ffffff;
    border-radius: 3px;
    box-shadow: 0 0 3px 0px rgba(0, 133, 186, 0.67);
}

#customize-control-astra-settings-header-trigger-icon .ahfb-radio-container-control .components-button.is-tertiary:not(:disabled):not([aria-disabled=true]):hover, #customize-control-astra-settings-header-trigger-icon .ahfb-radio-container-control .components-button.is-tertiary:not(:disabled):not([aria-disabled=true]):active,
.ahfb-radio-icon-control .components-button-group.ahfb-radio-container-control .components-button.is-tertiary:not(:disabled):not([aria-disabled=true]):hover,
.ahfb-radio-icon-control .ahfb-radio-container-control .components-button.is-tertiary:not(:disabled):not([aria-disabled=true]):active {
    border: none;
    box-shadow: unset;
}

.ahfb-icon-set svg,
#customize-control-astra-settings-header-trigger-icon span.ahfb-svg-iconset.svg-baseline svg path, span.ahfb-svg-iconset.svg-baseline svg circle {
    fill: var(--ast-customizer-sub-text-color);
}

.active-radio .ahfb-icon-set svg, .active-radio .ahfb-icon-set svg, #customize-control-astra-settings-header-trigger-icon input:checked + .ast-radio-img-svg svg path, #customize-control-astra-settings-header-trigger-icon input:checked + .ast-radio-img-svg svg circle {
    fill: var(--ast-customizer-secondary-text-color);
}

#customize-control-astra-settings-header-trigger-icon input:checked + .ast-radio-img-svg svg > path {
	fill: var(--ast-customizer-color-1);
}

.ahfb-icon-set-control.ahfb-three-col-short .components-button-group.ahfb-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}

.ahfb-icon-set-control.ahfb-three-col-short .components-button-group.ahfb-radio-container-control .components-button.is-tertiary {
    min-width: 25%;
    padding: 0px;
    margin: 0;
    height: 30px;
}

.ahfb-sorter-no-sorting .ahfb-sorter-item {
    margin-bottom: 12px;
}

.ahfb-sorter-no-sorting .ahfb-sorter-item-panel-header {
    cursor: default;
}

.components-button-group.ahfb-featured-image-ratio {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}

.ahfb-sorter-item-panel-content .ahfb-featured-image-ratio .components-button.is-tertiary {
    padding: 0;
    height: 30px;
    margin: 0;
}

.ahfb-sorter-item-panel-content .ahfb-radio-container-control .components-button.is-tertiary svg {
    max-width: 22px;
    margin: 0 auto;
}

#customize-theme-controls .accordion-section-content {
    color: var(--ast-customizer-color-5);
}

.ahfb-popover-social-list .components-button-group.ahfb-radio-container-control {
    flex-wrap: wrap;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 5px;
    padding-bottom: 15px;
    padding-top: 5px;
}

.ahfb-popover-social-list .components-button-group.ahfb-radio-container-control .components-button.social-radio-btn.is-tertiary {
    min-width: 80px;
    margin: 0;
    padding: 0;
    font-size: 10px;
}

.radio-icon-padding .ahfb-radio-container-control .components-button.is-tertiary {
    padding: 10px 0;
}

.ahfb-sorter-drop-social_item_group .ahfb-sorter-item-panel-header .ahfb-sorter-visiblity {
    border: 0;
    height: auto;
    padding: 0;
    max-height: 18px;
}

.ahfb-sorter-drop-social_item_group .ahfb-sorter-item-panel-header .ahfb-sorter-visiblity svg {
    max-width: 16px;
    max-height: 16px;
    fill: #000000;
    vertical-align: middle;
    padding-left: 4px;
    width: 28px;
    height: 28px;
}

.ahfb-sorter-drop-social_item_group .ahfb-sorter-item-panel-header .ahfb-sorter-visiblity.item-is-hidden {
    opacity: 0.2;
}
.ahfb-tiny-text .ahfb-radio-container-control .components-button.is-tertiary, #customize-control-logo_layout .ahfb-radio-container-control .components-button.is-tertiary {
    font-size: 9px;
}

.ahfb-builder-is-active .wp-full-overlay.collapsed #customize-preview, .ahfb-footer-builder-is-active .wp-full-overlay.collapsed #customize-preview {
    bottom: 0 !important;
}

.ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-drop-left_center,
.ahfb-builder-areas .ahfb-builder-group-horizontal .ahfb-builder-drop-right_center {
    display: none;
}

.ahfb-builder-areas.ahfb-builder-mode-header.popup-vertical-group .ahfb-builder-group-horizontal {
    border-right: 1px solid #e5e5e5;
}

.ahfb-builder-areas.ahfb-builder-mode-header.popup-vertical-group .ahfb-builder-group-horizontal:hover, .active-builder-row.ahfb-builder-areas.ahfb-builder-mode-header.popup-vertical-group .ahfb-builder-group-horizontal {
    border-right: 1px solid #ddd;
    box-shadow: 3px 7px 10px rgb(0 39 36 / 7%);
}

.ahfb-builder-areas.has-center-items .ahfb-builder-drop-left_center, .ahfb-builder-areas.has-center-items .ahfb-builder-drop-right_center {
    display: flex;
}

.ahfb-icon-set-control.ahfb-two-forced .components-button-group.ahfb-radio-container-control .components-button.is-tertiary {
    margin: 0;
}

.ahfb-icon-set-control.ahfb-two-forced .ahfb-radio-container-control {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 10px;
    row-gap: 10px;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-6-equal {
    grid-template-columns: repeat( 6, 1fr );
}

.ahfb-builder-mode-footer .ast-grid-row-layout-5-equal {
    grid-template-columns: repeat( 5, 1fr );
}

.ahfb-builder-mode-footer .ast-grid-row-layout-4-equal {
    grid-template-columns: repeat( 4, 1fr );
}

.ahfb-builder-mode-footer .ast-grid-row-layout-4-lheavy {
    grid-template-columns: 2fr 1fr 1fr 1fr;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-4-rheavy {
    grid-template-columns: 1fr 1fr 1fr 2fr;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-3-equal {
    grid-template-columns: repeat( 3, 1fr );
}

.ahfb-builder-mode-footer .ast-grid-row-layout-3-lheavy {
    grid-template-columns: 2fr 1fr 1fr;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-3-rheavy {
    grid-template-columns: 1fr 1fr 2fr;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-3-cheavy {
    grid-template-columns: 1fr 2fr 1fr;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-3-cwide {
    grid-template-columns: 1fr 3fr 1fr;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-2-equal {
    grid-template-columns: repeat( 2, 1fr );
}

.ahfb-builder-mode-footer .ast-grid-row-layout-2-lheavy {
    grid-template-columns: 2fr 1fr;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-2-rheavy {
    grid-template-columns: 1fr 2fr;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-2-full {
    grid-template-columns: 2fr;
}

.ahfb-builder-mode-footer .ast-grid-row-layout-full {
    grid-template-columns: 1fr;
}

@media (max-width: 1700px) {
    .ahfb-tiny-text .ahfb-radio-container-control .components-button.is-tertiary {
        font-size: 7px;
    }
}

/**
 * Adding new UI CSS for launching new HF Builder as requirement for test drive marketing.
 */
#accordion-panel-panel-header-builder-group h3 .ahfb-highlight, #accordion-panel-panel-footer-builder-group h3 .ahfb-highlight {
    color: #fff;
    background: linear-gradient(-90deg, rgba(73,44,221,1) 0%, rgba(173,56,226,1) 100%);
    font-size: 0.7em;
    font-weight: 600;
    position: relative;
    padding: 0.3em 0.6em 0.4em;
    top: -2px;
    right: 5px;
    letter-spacing: 0.5px;
    line-height: 1em;
    text-transform: uppercase;
    border-radius: 2px;
}


.ahfb-slide-up {
	display: flex;
	align-items: center;
	position: absolute;
	cursor: pointer;
	transform: translateY(-100%);
	opacity: 0;
	visibility: hidden;
	transition: color 0.15s ease, opacity 0.1s ease, visibility 0.1s ease;
	top: 0;
	right: -1px;
	background-color: var(--ast-customizer-color-9);
	border: 1px solid var(--ast-customizer-color-1);
	border-radius: 4px 4px 0 0;
	border-bottom: 0;
}

.ahfb-slide-up span {
	font-size: 14px;
	line-height: 20px;
	padding-right: 2px;
}

.ahfb-builder-item:hover .ahfb-slide-up {
	opacity: 1;
	visibility: visible;
	color: #444444;
}

[data-tooltip] {
	position: relative;
}

[data-tooltip]:hover::before {
	box-sizing: border-box;
	position: absolute;
	top: -11px;
	right: -6px;
	width: 4px;
	height: 4px;
	padding-top: 5px;
	border: 6px solid transparent;
	border-top-color: #1f1f1f;
}

.ahfb-slide-up [data-tooltip]:hover::before {
	right: -1px;
}

[data-tooltip]::after {
 content: attr(data-tooltip);
 pointer-events: none;
 display: inline-table;
 position: absolute;
 right: -20px;
 font-size: 12px;
 opacity: 0;
 visibility: hidden;
 line-height: 1;
 background: #1f1f1f;
 color: #fff;
 border-radius: 3px;
 font-family: system-ui;
 padding: 5px 10px;
 top: -30px;
}

[data-tooltip]:hover::after {
	opacity: 1;
	visibility: visible;
}
.rtl #astra-reset.button {
	margin-right: 45px;
}
.rtl .customize-control-ast-builder .components-popover[data-x-axis="right"] .components-popover__content {
	left: unset;
}

#customize-control-astra-settings-header-desktop-items .popup-vertical-group .ahfb-builder-area-content .sortable-ghost[data-id="mobile-trigger"],
#customize-control-astra-settings-header-desktop-items .popup-vertical-group .ahfb-builder-area-content .sortable-ghost[data-id^="menu-"],
#customize-control-astra-settings-header-mobile-items .popup-vertical-group .ahfb-builder-area-content .sortable-ghost[data-id="mobile-trigger"],
#customize-control-astra-settings-header-mobile-items .popup-vertical-group .ahfb-builder-area-content .sortable-ghost[data-id^="menu-"],
#customize-control-astra-settings-header-desktop-items .ahfb-builder-row-items .sortable-ghost[data-id="mobile-menu"],
#customize-control-astra-settings-header-mobile-items .ahfb-builder-row-items .sortable-ghost[data-id="mobile-menu"] {
    background: #fff;
}
/* Box Shadow Control Style */
.customize-control-ast-box-shadow .ast-link-box-shadow-inset-wrapper {
    margin: 10px 0;
}

.customize-control-ast-box-shadow .customize-control-title {
    display: inline-block;
}

.customize-control-ast-box-shadow .ast-box-shadow-outer-wrapper {
    display: flex;
    position: relative;
}

.customize-control-ast-box-shadow .ast-box-shadow-input-item {
    margin-bottom: 0;
}

.customize-control-ast-box-shadow .ast-box-shadow-btns {
    display: block;
    text-align: center;
    line-height: 2;
    border: 1px solid #dddddd;
    background-color: #ffffff;
    border-radius: 3px 0 0 3px;
}

.ast-box-shadow-wrapper .ast-box-shadow-input-item:last-child .ast-box-shadow-input {
    border-radius: 3px 0px 0px 3px;
}
.ast-box-shadow-wrapper .ast-box-shadow-input-item:not(:last-child) .ast-box-shadow-input {
    border-radius: 0px;
}
.ast-box-shadow-wrapper .ast-box-shadow-input-item:first-child .ast-box-shadow-input {
    border-radius: 0px 3px 3px 0px;
}

.customize-control-ast-box-shadow .ast-box-shadow-btns > li {
    margin-bottom: 0;
    display: none;
    width: 35px;
    height: 26px;
}

.customize-control-ast-box-shadow .ast-box-shadow-btns > li.active {
    display: inline-block;
}

.customize-control-ast-box-shadow .ast-box-shadow-btns button[type="button"] {
    padding: 0;
    cursor: pointer;
    background: none;
    border: none;
    opacity: .75;
    outline: none;
    width: 100%;
    height: 100%;
}

.customize-control-ast-box-shadow .ast-box-shadow-btns button[type="button"] > i {
    font-size: 15px;
    margin-top: 1px;
    vertical-align: middle;
}

.customize-control-ast-box-shadow .ast-box-shadow.new {
    display: flex;
    gap: 20px;
}

.customize-control-ast-box-shadow .input-wrapper.ast-box-shadow-wrapper {
    display: inline-flex;
    gap: 8px;
}

.customize-control-ast-box-shadow .ast-box-shadow-wrapper {
    display: flex;
    gap: 8px;
  }  
  
  .ast-active-design-tab .customize-control-header-button1-box-shadow-position.customize-control-ast-select, .ast-active-design-tab .customize-control-header-button2-box-shadow-position.customize-control-ast-select {
    display: flex;
    gap: 20px;
    align-items: baseline;
  }

.customize-control-ast-box-shadow .input-wrapper .ast-box-shadow-wrapper {
    display: none;
}
.customize-control-ast-box-shadow .input-wrapper .ast-box-shadow-wrapper li {
    text-align: center;
    -webkit-box-flex: 1;
    -ms-flex: auto;
    flex: auto;
}
.customize-control-ast-box-shadow .input-wrapper .ast-box-shadow-wrapper li input.ast-box-shadow-input {
    text-align: center;
    display: block;
    font-size: 12px;
    padding: 4px 0;
    width: 100%;
    height: 28px;
    border: 1px solid var(--ast-customizer-border-color);
    box-shadow: none;
    padding-right: 11px;
    border-radius: 3px;
}

.customize-control-ast-box-shadow .input-wrapper .ast-box-shadow-wrapper li input.ast-box-shadow-input:hover {
    border-color: var(--ast-customizer-color-1);
}
.customize-control-ast-box-shadow .input-wrapper .ast-box-shadow-wrapper li input.ast-box-shadow-input:focus {
    border-color: var(--ast-customizer-color-1);
}

.customize-control-ast-box-shadow .input-wrapper .ast-box-shadow-wrapper li:last-child input.ast-box-shadow-input {
    border-left-width: 1px;
}
.customize-control-ast-box-shadow .input-wrapper .ast-box-shadow-wrapper.active {
    display: flex;
}
.customize-control-ast-box-shadow .input-wrapper .ast-box-shadow-wrapper span.ast-box-shadow-title {
    text-transform: uppercase;
    font-size: 8px;
    line-height: 8px;
    letter-spacing: 0.4px;
    opacity: .75;
}
.customize-control-ast-box-shadow .input-wrapper .ast-box-shadow-wrapper .ast-box-shadow-input-item-link span {
    width: 35px;
    height: 26px;
    line-height: 26px;
    font-size: 14px;
    border: 1px solid #dddddd;
    background-color: #ffffff;
    border-radius: 0 3px 3px 0;
    border-left-width: 0;
}
.customize-control-ast-box-shadow .ast-box-shadow-inset-wrapper {
    margin-top: 15px;
}
.customize-control-ast-box-shadow .ast-box-shadow-inset-wrapper .components-flex__item,
.customize-control-ast-box-shadow .ast-box-shadow-inset-wrapper .components-select-control {
    display: inline-block;
}
.customize-control-ast-box-shadow .ast-box-shadow-inset-wrapper .components-select-control__input,
.customize-control-ast-box-shadow .ast-box-shadow-inset-wrapper .components-input-control__container {
    display: inline-flex;
    flex-direction: column;
    width: 60%;
    float: left;
}
.customize-control-ast-box-shadow .ast-box-shadow-inset-wrapper Select.components-select-control__input,
.customize-control-ast-box-shadow .ast-box-shadow-inset-wrapper Select.components-input-control__container  {
    line-height: inherit;
}
.ast-box-shadow-inset-wrapper .components-base-control__label,
.ast-box-shadow-inset-wrapper .components-input-control__label {
    font-weight: 600;
}

.customize-control.customize-control-ast-box-shadow > label span {
	margin-top: 6px;
}

.customize-control.customize-control-ast-select:has([data-name*="-box-shadow-position"]) {
	display: flex;
	align-items: center;
	gap: 6px;
}

.customize-control.customize-control-ast-select:has([data-name*="-box-shadow-position"]) .customize-control-title {
	margin-bottom: 0;
}

.ahfb-builder-item-icon .dashicons-admin-settings, .ahfb-builder-item-icon .dashicons-admin-generic{
	width: 14px;
	height: 14px;
	font-size: 14px;
	line-height: 14px;
	color: #777777;
}

.ahfb-builder-item > .ahfb-builder-item-icon.ahfb-setting-icon {
    margin-left: -12px;
}

/**
 * Added CSS for hiding section from WP-Customizer panel.
 */
li#accordion-section-ast-single-post, li#accordion-section-ast-single-product,
li#accordion-section-ast-archive-post, li#accordion-section-ast-archive-product {
    display: none !important;
}


#customize-control-astra-settings-shop-skin-section-link .ahfb-builder-item-start,
#customize-control-astra-settings-footer-create-menu-link .ahfb-builder-item-start,
#customize-control-astra-settings-header-mobile-menu-create-menu-link .ahfb-builder-item-start {
	margin-bottom: 0;
}

.ahfb-sorter-drop-social_item_group .ahfb-sorter-item .ahfb-sorter-item-panel-header .dashicons,
.ahfb-sorter-drop-language_selector_group .ahfb-sorter-item .ahfb-sorter-item-panel-header .dashicons {
	width: 16px;
	height: 16px;
	font-size: 16px;
}

/** Site icon control specific CSS as divider attr can't accessible to core control. */
li#customize-control-site_icon {
    margin-bottom: 16px;
}
/*
 * Hide customizer scrollbar during navigation.
 */
 #customize-controls .wp-full-overlay-sidebar-content:has(.busy) {
    overflow-y: hidden;
}



.ahfb-popover-builder-list .hfb-widgets {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 10px;
    margin-right: -4px;
    margin-left: -4px;
}

.components-button-group .builder-add-btn {
    position: relative;
}

.components-button-group .pro-feature .pro-icon {
    position: absolute;
    top: 2px;
    left: 13px;
    width: 10px;
    height: 12px;
    opacity: 1;
}

/* For WebKit browsers (Chrome, Safari, Edge) */
.components-button-group.ahfb-radio-container-control::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
}

.components-button-group.ahfb-radio-container-control::-webkit-scrollbar-thumb {
    background-color: #1615151a;
}

/* For Firefox */
.components-button-group.ahfb-radio-container-control {
    scrollbar-width: thin;
    scrollbar-color: #1212121A transparent;
}

/* For IE/Edge legacy */
.components-button-group.ahfb-radio-container-control {
    -ms-overflow-style: -ms-autohiding-scrollbar;
}

.ahfb-popover-builder-list .hfb-widgets .elements-title {
    font-family: "Inter", sans-serif;
    font-weight: 500;
    font-size: 10px;
    text-align: center;
    color: #64748B;
}

.ahfb-popover-builder-list .hfb-widgets .elements-utm {
    color: #0084C7;
    text-decoration: none;
}

.react-tooltip-arrow {
    top: 10px !important;
}

.custom-tooltip {
    opacity: 1 !important;
}

.pro-feature .add-btn-icon .dashicon {
    opacity: 0.2;
}

.components-popover__content {
    padding: 4px; 
    gap: 4px;
    border-radius: 8px;
}

.tooltip-button{
    color: #7DD3FC;
}

.tooltip-button:hover {
    color: #0EA5E9;
}

.custom-tooltip::before {
    content: '';
    position: absolute;
    top: 0;
    right: -8px;
    width: 8px;
    height: 100%;
    pointer-events: auto; 
  }

  .ahfb-popover-builder-list .bottom-shadow {
    box-shadow: rgba(18, 18, 0, 0.2) 0px 18px 22px -10px;
	position: relative;
	z-index:9999;
    margin-left: -4px;
    margin-right: -4px;
}
