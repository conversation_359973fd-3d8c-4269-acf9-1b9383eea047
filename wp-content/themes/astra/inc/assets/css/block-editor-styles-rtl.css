html {
  font-size: 93.75%;
}

/* Variables */
/*----------  Media Query min-width Structure   ----------*/
/*----------  Media Query max-width Structure   ----------*/
/*----------  Break-point min-width Structure   ----------*/
/*----------  Break-point max-width Structure   ----------*/
/*----------  Font Size  ----------*/
/*----------  Line Height  ----------*/
/*----------  Site Basic Structure  ----------*/
/*----------  z-index Structure   ----------*/
/*
 * Button mixin- creates a button effect with correct
 * highlights/shadows, based on a base color.
 */
.ast-highlight-wpblock-onhover .block-editor-block-list__layout .block-editor-block-list__block {
  transition: all 0.2s;
}

.ast-highlight-wpblock-onhover .block-editor-block-list__layout .block-editor-block-list__block.is-highlighted, .ast-highlight-wpblock-onhover .block-editor-block-list__layout .block-editor-block-list__block:hover {
  box-shadow: 0 0 0 var(--wp-admin-border-width-focus) var(--wp-admin-theme-color);
}

.ast-highlight-wpblock-onhover .block-editor-block-list__layout .block-editor-block-list__block.is-highlighted:after {
  box-shadow: none;
}

.edit-post-visual-editor {
  /* must have higher specificity than alternative color schemes inline styles */
}

.edit-post-visual-editor ::selection {
  color: #fff;
  background: royalblue;
}

.edit-post-visual-editor body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.edit-post-visual-editor body:not(.logged-in) {
  position: relative;
}

.edit-post-visual-editor #page {
  position: relative;
}

.edit-post-visual-editor a,
.edit-post-visual-editor a:focus {
  text-decoration: none;
}

.edit-post-visual-editor a,
.edit-post-visual-editor .site-header a *,
.edit-post-visual-editor .site-footer a *,
.edit-post-visual-editor .secondary a * {
  transition: all 0.2s linear;
}

.edit-post-visual-editor .capitalize {
  text-transform: uppercase;
}

.edit-post-visual-editor img {
  vertical-align: middle;
}

.edit-post-visual-editor .entry-content h1,
.edit-post-visual-editor .entry-content h2,
.edit-post-visual-editor .entry-content h3,
.edit-post-visual-editor .entry-content h4,
.edit-post-visual-editor .entry-content h5,
.edit-post-visual-editor .entry-content h6 {
  margin-bottom: 20px;
}

.edit-post-visual-editor p {
  margin-bottom: 1.75em;
}

.edit-post-visual-editor blockquote {
  margin: 1.5em 3em 1.5em 1em;
  font-size: 1.1em;
  line-height: inherit;
  position: relative;
}

.edit-post-visual-editor .ast-button,
.edit-post-visual-editor .button,
.edit-post-visual-editor input[type="button"],
.edit-post-visual-editor input[type="submit"] {
  border-radius: 0;
  padding: 18px 30px;
  border: 0;
  box-shadow: none;
  text-shadow: none;
}

.edit-post-visual-editor .ast-button:hover,
.edit-post-visual-editor .button:hover,
.edit-post-visual-editor input[type="button"]:hover,
.edit-post-visual-editor input[type="submit"]:hover {
  box-shadow: none;
}

.edit-post-visual-editor .ast-button:active, .edit-post-visual-editor .ast-button:focus,
.edit-post-visual-editor .button:active,
.edit-post-visual-editor .button:focus,
.edit-post-visual-editor input[type="button"]:active,
.edit-post-visual-editor input[type="button"]:focus,
.edit-post-visual-editor input[type="submit"]:active,
.edit-post-visual-editor input[type="submit"]:focus {
  box-shadow: none;
}

.edit-post-visual-editor .site-title {
  font-weight: normal;
}

.edit-post-visual-editor .site-title,
.edit-post-visual-editor .site-description {
  margin-bottom: 0;
}

.edit-post-visual-editor .site-title a,
.edit-post-visual-editor .site-title:hover a,
.edit-post-visual-editor .site-title:focus a,
.edit-post-visual-editor .site-description a,
.edit-post-visual-editor .site-description:hover a,
.edit-post-visual-editor .site-description:focus a {
  transition: all 0.2s linear;
}

.edit-post-visual-editor .site-title a,
.edit-post-visual-editor .site-title a:focus,
.edit-post-visual-editor .site-title a:hover,
.edit-post-visual-editor .site-title a:visited {
  color: #222;
}

.edit-post-visual-editor .site-description a,
.edit-post-visual-editor .site-description a:focus,
.edit-post-visual-editor .site-description a:hover,
.edit-post-visual-editor .site-description a:visited {
  color: #999;
}

.edit-post-visual-editor .search-form .search-field {
  outline: none;
}

.edit-post-visual-editor .ast-search-menu-icon {
  position: relative;
  z-index: 3;
}

.edit-post-visual-editor .site .skip-link {
  background-color: #f1f1f1;
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.2);
  color: #21759b;
  display: block;
  font-family: Montserrat, "Helvetica Neue", sans-serif;
  font-size: 14px;
  font-weight: 700;
  right: -9999em;
  outline: none;
  padding: 15px 23px 14px;
  text-decoration: none;
  text-transform: none;
  top: -9999em;
}

.edit-post-visual-editor .site .skip-link:focus {
  clip: auto;
  height: auto;
  right: 6px;
  top: 7px;
  width: auto;
  z-index: 100000;
  outline: thin dotted;
}

.logged-in .edit-post-visual-editor .site .skip-link {
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.2);
  font-family: "Open Sans", sans-serif;
}

.edit-post-visual-editor h1, .edit-post-visual-editor h2, .edit-post-visual-editor h3, .edit-post-visual-editor h4, .edit-post-visual-editor h5, .edit-post-visual-editor h6 {
  clear: both;
}

.edit-post-visual-editor h1,
.edit-post-visual-editor .entry-content h1 {
  color: #808285;
  font-size: 2em;
  line-height: 1.2;
}

.edit-post-visual-editor h2,
.edit-post-visual-editor .entry-content h2 {
  color: #808285;
  font-size: 1.7em;
  line-height: 1.3;
}

.edit-post-visual-editor h3,
.edit-post-visual-editor .entry-content h3 {
  color: #808285;
  font-size: 1.5em;
  line-height: 1.4;
}

.edit-post-visual-editor h4,
.edit-post-visual-editor .entry-content h4 {
  color: #808285;
  line-height: 1.5;
  font-size: 1.3em;
}

.edit-post-visual-editor h5,
.edit-post-visual-editor .entry-content h5 {
  color: #808285;
  line-height: 1.6;
  font-size: 1.2em;
}

.edit-post-visual-editor h6,
.edit-post-visual-editor .entry-content h6 {
  color: #808285;
  line-height: 1.7;
  font-size: 1.1em;
}

.edit-post-visual-editor .wp-block-heading h1 {
  line-height: 1.2;
}

.edit-post-visual-editor .wp-block-heading h2 {
  line-height: 1.3;
}

.edit-post-visual-editor .wp-block-heading h3 {
  line-height: 1.4;
}

.edit-post-visual-editor .wp-block-heading h4 {
  line-height: 1.5;
}

.edit-post-visual-editor .wp-block-heading h5 {
  line-height: 1.6;
}

.edit-post-visual-editor .wp-block-heading h6 {
  line-height: 1.7;
}

.edit-post-visual-editor button.components-button {
  color: inherit;
}

#editor .edit-post-visual-editor.responsive-enabled {
  background-color: #2f2f2f;
}

.editor-styles-wrapper .block-editor-block-list__block h1 {
  line-height: 1.2;
}

.editor-styles-wrapper .block-editor-block-list__block h2 {
  line-height: 1.3;
}

.editor-styles-wrapper .block-editor-block-list__block h3 {
  line-height: 1.4;
}

.editor-styles-wrapper .block-editor-block-list__block h4 {
  line-height: 1.5;
}

.editor-styles-wrapper .block-editor-block-list__block h5 {
  line-height: 1.6;
}

.editor-styles-wrapper .block-editor-block-list__block h6 {
  line-height: 1.7;
}

.edit-post-visual-editor p,
.block-editor-block-list__block p,
.editor-default-block-appender textarea.editor-default-block-appender__content {
  font-size: 15px;
  font-size: 1rem;
}

.editor-post-title__block .editor-post-title__input {
  font-size: 30px;
  font-size: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif;
  font-weight: normal;
}

@media (min-width: 600px) {
  .edit-post-visual-editor .block-editor-block-list__block {
    padding-right: 0;
    padding-left: 0;
  }
}

.edit-post-visual-editor .block-editor-block-list__block .editor-block-list__block-edit {
  margin-right: 0;
  margin-left: 0;
}

.edit-post-visual-editor .block-editor-block-list__block > .editor-block-mover {
  right: -50px;
  top: -5px;
}

@media (min-width: 600px) {
  .edit-post-visual-editor .block-editor-block-list__block.wp-block-quote {
    padding: 1.2em;
  }
}

.block-editor-block-list__block[data-align=full] {
  max-width: none;
}

@media (min-width: 600px) {
  .block-editor-block-list__block[data-align=full] .editor-block-list__block-edit {
    padding-right: 0;
    padding-left: 0;
  }
}

.block-editor-block-list__block[data-align=wide] {
  max-width: 1400px;
}

.editor-default-block-appender__content {
  margin-top: 32px;
}

.wp-block-latest-posts.is-grid {
  list-style: none;
}

.blocks-gallery-grid {
  margin: 0;
}

.wp-block-gallery {
  margin: 0;
}

.wp-block-gallery.is-cropped .blocks-gallery-item img {
  height: 100%;
}

.edit-post-visual-editor .blocks-gallery-grid {
  margin: 0;
}

.wp-block-latest-posts {
  margin-right: 0;
}

.wp-block-latest-posts li {
  list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: inherit;
}

.mce-widget i {
  font-style: normal;
}

#elementor-editor-button {
  background: #0073aa;
  border-color: #0073aa;
  color: #fff;
  font-size: 14px;
  height: 46px;
  line-height: 44px;
  padding: 0 36px;
  display: inline-block;
  border-width: 1px;
  border-style: solid;
  -webkit-appearance: none;
  border-radius: 3px;
  white-space: nowrap;
  box-sizing: border-box;
  box-shadow: 0 2px 0 #006799;
}

#elementor-editor-button:hover, #elementor-editor-button:focus {
  background: #007db9;
  border-color: #00699b;
  color: #fff;
}

#elementor-editor-button:focus {
  box-shadow: 0 0 0 1px #fff, 0 0 0 3px #0073aa;
}

#elementor-editor-button:active {
  background: #006291;
  border-color: #006291;
  color: #fff;
}

#elementor-editor-button.active, #elementor-editor-button.active:focus, #elementor-editor-button.active:hover {
  background: #0073aa;
  color: #fff;
  border-color: #003f5e;
  box-shadow: inset 0 2px 5px -3px black;
}

#elementor-editor-button i {
  font-style: normal;
  color: white;
}

.editor-media-placeholder button,
.fl-builder-layout-launch-view button {
  margin: 2px;
}

.fl-builder-layout-launch-view .is-primary.is-primary {
  color: white;
}

.ast-separate-container #wpwrap .edit-post-visual-editor {
  background-color: #f5f5f5;
}

/**
 * Woocommerce Gutenberg Blocks Product Grid CSS Compatibility.
 */
.wc-block-grid .wc-block-grid__products .wc-block-grid__product {
  text-align: right;
  margin-bottom: 2.5em;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product.product-category > a {
  display: inline-block;
  position: relative;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product a img {
  width: 100%;
  height: auto;
  display: block;
  margin: 0 0 .8em 0;
  box-shadow: none;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .wc-block-grid__product-title {
  margin-bottom: .5em;
  padding: 0;
  font-size: 1em;
  line-height: 1.2;
  font-weight: inherit;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .star-rating {
  margin: 0 0 .5em auto;
  backface-visibility: hidden;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .woocommerce-loop-product__link {
  position: relative;
  display: block;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .woocommerce-loop-product__link:hover .ast-shop-product-out-of-stock {
  background-color: white;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product.product-category > a:hover .woocommerce-loop-category__title {
  background-color: white;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .woocommerce-loop-category__title {
  bottom: 1.8em;
  font-size: 0.9em;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .woocommerce-loop-category__title .count {
  font-size: .7em;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .price {
  line-height: 1.3;
  margin-bottom: 0;
  font-weight: 700;
  margin-bottom: .5em;
  font-size: .9em;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .price del {
  display: initial;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .wp-block-button__link {
  margin-top: .5em;
  margin-bottom: .5em;
  white-space: normal;
  line-height: 1.3;
  font-size: 100%;
  font-weight: 700;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .wp-block-button__link.added {
  margin-bottom: 0;
  transition: margin 0s;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .woocommerce-loop-category__title {
  text-transform: uppercase;
  font-weight: bold;
  line-height: 1.5;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .woocommerce-loop-category__title .count {
  display: block;
  background: none;
  opacity: .5;
  font-size: .75em;
}

.wc-block-grid .wc-block-grid__products .wc-block-grid__product .wc-block-grid__product-onsale {
  min-width: 3em;
  min-height: 3em;
  line-height: 3em;
  padding: 0;
  font-size: 1em;
  font-weight: normal;
  text-transform: capitalize;
  position: absolute;
  text-align: center;
  border-radius: 100%;
  z-index: 9;
  top: 0;
  left: 15px;
  right: auto;
  margin: -0.5em 0 0 -0.5em;
}

.edit-post-visual-editor__post-title-wrapper {
  position: relative;
  max-width: var(--wp--custom--ast-content-width-size);
  margin-right: auto !important;
  margin-left: auto !important;
  margin-bottom: 1.5em;
}

.edit-post-visual-editor__post-title-wrapper.invisible {
  opacity: 0.5;
}

.edit-post-visual-editor__post-title-wrapper .title-visibility {
  cursor: pointer;
  font-size: 18px;
  width: 36px;
  height: 100%;
  text-align: center;
  color: var(--wp-admin-theme-color);
  vertical-align: middle;
  position: absolute;
  right: -37px;
  bottom: 0;
  opacity: 0;
  margin: 0 !important;
  padding: 0 !important;
  box-shadow: 0 0 0 1px var(--wp-admin-theme-color);
  transition: all 0.2s;
}

.edit-post-visual-editor__post-title-wrapper .title-visibility:before {
  width: 100%;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.edit-post-visual-editor__post-title-wrapper .title-visibility:after {
  content: attr(data-tooltip);
  white-space: nowrap;
  border-radius: 2px;
  pointer-events: none;
  color: #fff;
  background: #1e1e1e;
  opacity: 0;
  visibility: hidden;
  width: max-content;
  line-height: 26px;
  margin-right: -1px;
  display: flex;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  align-items: flex-start;
  padding: 0 6px;
  font-size: 12px;
  transition: all 0.2s;
}

.edit-post-visual-editor__post-title-wrapper h1.editor-post-title__input {
  padding: 10px 6px;
  transition: all 0.2s;
}

.edit-post-visual-editor__post-title-wrapper h1.editor-post-title__input.is-selected {
  box-shadow: 0 0 0 1px var(--wp-admin-theme-color);
}

.edit-post-visual-editor__post-title-wrapper h1.editor-post-title__input.is-selected + .title-visibility {
  opacity: 1;
  color: var(--wp-admin-theme-color);
}

.edit-post-visual-editor__post-title-wrapper .ast-show-visibility-icon {
  opacity: 1;
  color: var(--wp-admin-theme-color);
}

.edit-post-visual-editor__post-title-wrapper .ast-show-editor-title-outline {
  box-shadow: 0 0 0 1px var(--wp-admin-theme-color);
}

.ast-page-builder-template .edit-post-visual-editor__post-title-wrapper, .ast-stacked-title-visibility .edit-post-visual-editor__post-title-wrapper {
  max-width: none;
  padding: 0 20px;
}

.ast-page-builder-template .edit-post-visual-editor__post-title-wrapper .title-visibility, .ast-stacked-title-visibility .edit-post-visual-editor__post-title-wrapper .title-visibility {
  right: unset;
  top: -41px;
  height: 40px;
}

/**
 * Gutenberg editor improving UI/UX.
 */
.edit-post-visual-editor ul.block-editor-block-list__block,
.edit-post-visual-editor ol.block-editor-block-list__block {
  margin-right: 20px;
}

.block-editor-block-list__block.wp-block.wp-social-link {
  font-size: inherit;
}

.edit-post-visual-editor .block-editor-block-list__block.wp-social-link {
  padding: 0;
}

.wp-block ol, .wp-block ul {
  margin-right: 20px;
  margin-left: 20px;
}

.edit-post-visual-editor .wp-block.wp-block-shortcode {
  padding: 20px;
}

.wp-block-table td, .wp-block-table th {
  padding: 0.5em;
  border: 1px solid;
  word-break: normal;
}

.edit-post-visual-editor pre.wp-block, .edit-post-visual-editor .wp-block.wp-block-query-title {
  padding-right: 20px;
}

.block-editor-block-list__layout .block-editor-block-list__block.wp-block-button:not(:first-child) {
  padding-right: 0;
  padding-left: 0;
}

.block-editor-block-list__empty-block-inserter .block-editor-inserter__toggle.components-button.has-icon {
  margin: 6px 0 0 -2px;
  border-radius: 0;
}

.components-dropdown__content .components-popover__content {
  min-width: 290px;
}

.components-color-palette__custom-color-dropdown-content .components-popover__content {
  min-width: auto;
}

.wp-block-buttons > .wp-block.wp-block-button {
  padding-left: 0;
}

.wp-block .wp-block-categories__list {
  padding-right: 0;
}

.wp-block-latest-comments .wp-block-latest-comments {
  margin-right: 0;
}

.wp-block-quote.is-style-large cite {
  text-align: right;
}

.editor-styles-wrapper p {
  line-height: 1.85714285714286;
}

.wp-block-paragraph.has-background {
  padding: 1.25em 2.375em;
}

.ast-theme-block-color-name {
  mix-blend-mode: difference;
}

.components-color-palette__custom-color {
  text-align: right;
  padding-right: 5px;
}

h1.wp-block.has-background,
h2.wp-block.has-background,
h3.wp-block.has-background,
h4.wp-block.has-background,
h5.wp-block.has-background,
h6.wp-block.has-background {
  padding: 1.25em 2.375em;
}

/**
 * 6.3 Compatibility fixes in block editor.
 */
span.title-visibility svg {
  position: absolute;
  top: 50%;
  right: 50%;
  transform: translate(50%, -50%);
  width: 16px;
  height: 40px;
  fill: var(--wp-admin-theme-color);
}

#astra_settings_meta_box .components-button.is-primary:hover:not(:disabled),
.ast-meta-settings-content .components-button.is-primary:hover:not(:disabled) {
  background: var(--wp-components-color-accent-darker-10, var(--wp-admin-theme-color-darker-10, #2145e6)) !important;
  color: var(--wp-components-color-accent-inverted, #fff) !important;
}
