.customize-control-ast-selector .ast-alignment-wrapper {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.customize-control-ast-selector .ast-alignment-inner-wrap,
.customize-control-ast-selector .ast-selector-responsive-wrap {
	display: none;
}

.customize-control-ast-selector .ast-alignment-wrapper .ast-alignment-inner-wrap.active {
	flex: 1 1 auto;
	display: flex;
}

.customize-control-ast-selector .components-button.has-icon {
	padding: 6px;
	min-width: 36px;
	justify-content: center;
}

.ast-alignment-inner-wrap .components-button {
	white-space: normal;
	color: var( --ast-customizer-color-5 );
	text-decoration: none;
	text-shadow: none;
	outline: unset;
	border: 1px solid var( --ast-customizer-color-7 );
	width: 100%;
	height: 100%;
	text-align: center;
	justify-content: center;
	border-radius: unset;
	transition: none;
}

.ast-alignment-inner-wrap .components-button.is-pressed {
	font-weight: 600;
}

.ast-alignment-inner-wrap.active .components-button.is-primary {
	color: var( --ast-customizer-color-10 );
	background: var( --ast-customizer-color-1 );
}

.customize-control-ast-selector .ast-alignment-wrapper .ast-alignment-inner-wrap.active .components-button:not( .is-primary ):hover {
	color: var( --ast-customizer-color-5 ) !important;
}

.customize-control-ast-selector .components-button .ahfb-icon-set svg {
	width: 16px;
	height: 16px;
	fill: var( --ast-customizer-color-8 );
}

.customize-control-ast-selector .components-button.is-primary .ahfb-icon-set svg,
.customize-control-ast-selector .components-button.is-primary .ahfb-icon-set svg circle,
.customize-control-ast-selector .components-button.is-primary .ahfb-icon-set svg path:not( .ast-hf-account-unfill ) {
	fill: var( --ast-customizer-color-10 );
}

.customize-control-ast-selector .ast-alignment-inner-wrap:first-child .components-button {
	border-radius: 3px 0px 0px 3px;
}

.customize-control-ast-selector .ast-alignment-inner-wrap:last-child .components-button {
	border-radius: 0px 3px 3px 0px;
}

.ast-alignment-wrapper .ast-alignment-inner-wrap:not( :last-child ) .components-button {
	border-right: none;
}

.customize-control-ast-selector .ast-alignment-inner-wrap .components-button {
	border-radius: unset;
	font-size: 12px;
	padding: 6px 4px;
}

.customize-control-ast-selector .ast-selector-responsive-wrap.active {
	display: flex;
	flex: 1 1 0;
}

.customize-control-ast-selector .ast-responsive-btns {
	position: relative;
	left: 6px;
	top: 1px;
}

.customize-control-ast-selector .ast-alignment-inner-wrap .components-button:focus:not( :disabled ) {
	box-shadow: none;
}

.customize-control-ast-selector .components-button .ahfb-icon-set {
	display: flex;
}

.customize-control-ast-selector {
	margin-bottom: 1px;
}

.ast-alignment-inner-wrap.active button.components-button.is-primary {
	border-color: var( --ast-customizer-color-3 );
}

#customize-controls .customize-control-notifications-container {
	margin: 0;
}

.customize-control-ast-background .screen-reader-text {
	top: initial;
}

.customize-control-ast-background .background-container h4 {
	font-weight: normal;
}

.customize-control-ast-background .background-attachment h4,
.customize-control-ast-background .background-color h4,
.customize-control-ast-background .background-position h4,
.customize-control-ast-background .background-repeat h4,
.customize-control-ast-background .background-size h4 {
	margin-bottom: 5px;
	margin-top: 10px;
}

.customize-control-ast-background .background-color {
	margin-bottom: 12px;
}

.customize-control-ast-background .background-repeat {
	margin: 15px 0 8px 0;
}

.customize-control-ast-background .background-attachment .buttonset,
.customize-control-ast-background .background-size .buttonset {
	display: flex;
	flex-wrap: wrap;
}

.customize-control-ast-background .background-attachment .buttonset .switch-label,
.customize-control-ast-background .background-size .buttonset .switch-label {
	background: #ffffff;
	border: 1px solid rgba( 0, 0, 0, 0.1 );
	color: #555;
	padding: 2px 4px;
	margin-right: 15px;
	text-align: center;
	flex-grow: 1;
	transition: background-color 140ms linear;
}

.customize-control-ast-background .background-attachment .buttonset .switch-label:last-child,
.customize-control-ast-background .background-size .buttonset .switch-label:last-child {
	margin-right: 0;
}

.customize-control-ast-background .background-attachment .buttonset .switch-input:checked + .switch-label,
.customize-control-ast-background .background-size .buttonset .switch-input:checked + .switch-label,
.customize-control-ast-background .background-attachment .buttonset .switch-input[checked="checked"] + .switch-label,
.customize-control-ast-background .background-size .buttonset .switch-input[checked="checked"] + .switch-label {
	background-color: #f5f5f5;
	color: #565e67;
}

.customize-control-ast-background .ast-bg-img-remove.components-button.is-link {
	width: 100%;
	border: 1px dashed #b4b9be;
	box-sizing: border-box;
	box-shadow: unset;
	padding: 9px 0;
	line-height: 1.6;
	margin-top: 10px;
	text-decoration: none;
}

.customize-control-ast-background .ast-bg-img-remove.components-button.is-destructive:hover:not( :disabled ) {
	color: #a02222;
	box-shadow: unset;
	border-color: #a02222;
}

.customize-control-ast-background .more-settings {
	margin-top: 12px;
	display: flex;
	justify-content: flex-end;
	padding: 5px 0 5px 0;
	cursor: pointer;
	float: none;
	text-decoration: none;
}

.customize-control-ast-responsive-background .more-settings .message,
.customize-control-ast-background .more-settings .message {
	margin-right: 5px;
}

.customize-control-ast-background .more-settings:focus {
	outline: 0;
	box-shadow: none;
}

.customize-control-ast-background .arrow-icon {
	margin-left: 5px;
}

.customize-control-ast-background .customize-control-title {
	display: block;
}

.customize-control-ast-background .astra-color-picker-wrap {
	margin-top: 18px;
}

.ast-field-settings-modal .customize-control-ast-background.customize-control:first-child,
.ast-field-settings-modal .customize-control-ast-background.customize-control {
	margin-top: 28px;
}

.ast-field-settings-modal .customize-control-ast-background .more-settings {
	margin-top: 6px;
}

.ast-field-settings-modal .customize-control-ast-background .customize-control-content .ast-color-btn-reset-wrap {
	right: 59px;
}

.ast-field-settings-modal .customize-control-ast-background .customize-control-content .color-button-wrap {
	right: 16px;
}

.ast-field-settings-modal .customize-control-ast-background .astra-popover-tabs .ast-clear-btn-inside-picker.components-button {
	margin: 5px 20px 20px 10px;
}

.customize-control-ast-background .ast-color-btn-reset-wrap {
	top: 4px;
}

.customize-control-ast-border {
	/* Unit Screen wrap */
}

.customize-control-ast-border .customize-control-title {
	display: inline-block;
}

.customize-control-ast-border .ast-border-outer-wrapper {
	display: flex;
	position: relative;
}

.customize-control-ast-border .ast-border-wrapper {
	display: flex;
}

.customize-control-ast-border .ast-border-btns {
	display: block;
	text-align: center;
	line-height: 2;
	border: 1px solid #dddddd;
	background-color: #ffffff;
	border-radius: 0 3px 3px 0;
}

.customize-control-ast-border .ast-border-btns > li {
	margin-bottom: 0;
	display: none;
	width: 35px;
	height: 26px;
}

.customize-control-ast-border .ast-border-btns > li.active {
	display: inline-block;
}

.customize-control-ast-border .ast-border-btns button[type="button"] {
	padding: 0;
	cursor: pointer;
	background: none;
	border: none;
	opacity: 0.75;
	outline: none;
	width: 100%;
	height: 100%;
}

.customize-control-ast-border .ast-border-btns button[type="button"] > i {
	width: 15px;
	height: 15px;
	font-size: 15px;
	margin-top: 1px;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper {
	display: none;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li {
	text-align: center;
	-webkit-box-flex: 1;
	-ms-flex: auto;
	flex: auto;
	margin: 0 2px;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li:nth-child( 1 ) {
	margin-left: 0px;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li input.ast-border-input {
	text-align: center;
	display: block;
	font-size: 12px;
	padding: 15px 0;
	width: 100%;
	height: 28px;
	border: 1px solid var( --ast-customizer-color-7 );
	box-shadow: none;
	transition: all 0.3s;
	-moz-appearance: textfield;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li .ast-border-connected {
	color: #ffffff;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li:last-child input.ast-border-input {
	border-right-width: 1px;
	border-radius: 0 3px 3px 0;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper.active {
	display: flex;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper span.ast-border-title {
	text-transform: uppercase;
	font-size: 10px;
	color: var( --ast-customizer-color-6 );
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper .ast-border-input-item-link .ast-border-connected {
	display: none;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper .ast-border-input-item-link.disconnected .ast-border-disconnected {
	display: none;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper .ast-border-input-item-link.disconnected .ast-border-connected {
	display: block;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper .ast-border-input-item-link span {
	width: 100%;
	height: 26px;
	line-height: 26px;
	font-size: 14px;
	background-color: transparent;
	border-radius: 3px;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper .ast-border-input-item-link span svg {
	pointer-events: none;
	margin-top: 4px;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper .ast-border-input-item-link {
	background: #e5e7eb;
	height: 32px;
	padding-right: 5px;
	padding-left: 5px;
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li {
	margin: 0px;
}

.ast-border-wrapper.desktop .ast-border-input-item:nth-child( n + 2 ) .ast-border-input {
	border-left: 0;
}

.ast-border-wrapper.desktop .ast-border-input-item:nth-child( n + 2 ) .ast-border-input:hover,
.ast-border-wrapper.desktop .ast-border-input-item:nth-child( n + 2 ) .ast-border-input:focus {
	border-left: 1px solid var( --ast-customizer-color-1 );
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li:has( input.ast-border-input:is( :focus, :hover, :active ) ) + li > input.ast-border-input {
    border-left: none;
}

.ast-border-wrapper.desktop .ast-border-input-item:first-child .ast-border-input {
	border-top-left-radius: 2px;
	border-bottom-left-radius: 2px;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li input.ast-border-input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li input.ast-border-input:hover,
.customize-control-ast-border .input-wrapper .ast-border-wrapper li input.ast-border-input:focus {
	border-color: var( --ast-customizer-color-1 );
}

.customize-control-ast-border .input-wrapper .ast-border-wrapper li input.ast-border-input:focus {
	outline: none;
}


.customize-control-ast-button-link .ast-button-link,
.ast-upgrade-pro-wrap .ast-button-link {
	display: block;
	padding: 8px 18px;
	border-radius: 30px;
	border: 1px solid var( --ast-customizer-color-1 );
	background-color: #5c2ede;
	color: var( --ast-customizer-color-9 );
	font-size: 13px !important;
	font-weight: 400;
	line-height: 13px !important;
	text-align: center;
	cursor: pointer;
	gap: 12px;
}

.customize-control-ast-button-link .ast-button-link:hover,
.ast-upgrade-pro-wrap .ast-button-link:hover {
	background-color: var( --ast-customizer-color-9 );
	color: #5c2ede;
	border: 1px solid #5c2ede;
}

.attachment-media-view.attachment-media-view-image .actions {
	display: flex;
	justify-content: space-between;
}

.attachment-media-view.attachment-media-view-image button.button.remove-button,
.attachment-media-view.attachment-media-view-image button.upload-button.control-focus {
	border: 1px solid var( --ast-customizer-color-1 );
	border-radius: 3px;
	background: var( --ast-customizer-color-9 );
	font-weight: 400;
	font-size: 12px;
	line-height: 16px;
}

.attachment-media-view.attachment-media-view-image button.button.remove-button:hover,
.attachment-media-view.attachment-media-view-image button.upload-button.control-focus:hover {
	background: var( --ast-customizer-color-1 );
	color: var( --ast-customizer-color-10 );
}

.ast-btn-preset-wrap {
	display: grid;
	grid-template-columns: repeat( 3, 1fr );
	gap: 12px;
}

.ast-btn-preset-wrap .ast-btn-style-item {
	cursor: pointer;
}

.ast-btn-preset-wrap .ast-btn-style-item svg {
	fill: var( --ast-customizer-color-8 );
}

.ast-btn-preset-wrap .ast-btn-style-item svg:hover {
	fill: var( --ast-customizer-color-15 );
}

.ast-btn-preset-wrap .ast-btn-style-item.active svg,
.ast-btn-preset-wrap .ast-btn-style-item.active svg:hover {
	fill: #2271b1;
}

.ast-reset-btn-preset-wrap {
	position: absolute;
	display: inline-block;
	line-height: 1;
	right: 0;
	top: -4px;
}

#customize-control-astra-settings-theme-color-divider-reset .ast-reset-btn-preset-wrap {
	top: 4px;
}

.ast-reset-btn-preset-wrap .components-button.is-secondary,
.ast-reset-btn-preset-wrap .components-button.is-secondary:hover {
	box-shadow: none !important;
	background: transparent;
	color: var( --ast-customizer-color-1 );
	height: auto;
	padding: 3.5px;
}

.reset-active .ast-reset-btn svg path {
	fill: var(--ast-customizer-color-1);
}

.color-group-item .components-tooltip .components-popover__content {
	border-radius: 3px;
}

.color-group-item .components-tooltip .components-popover__content > div {
	padding: 5px 10px;
	line-height: 1;
}

.customize-control.customize-control-ast-color-group {
	display: flex;
	flex-wrap: wrap;
}

.customize-control.customize-control-ast-color-group .ast-toggle-desc-wrap {
	flex: 1 1 auto;
	align-self: center;
}

.customize-control.customize-control-ast-color-group .customize-control-title {
	margin-bottom: 5px;
}

.customize-control.customize-control-ast-color-group .ast-field-color-group-wrap {
	justify-content: flex-end;
	display: flex;
	max-height: 28px;
}

.customize-control.customize-control-ast-color-group .color-button-wrap {
	position: inherit;
}

.customize-control.customize-control-ast-color-group .color-group-item:not( :last-child ) {
	margin-right: 8px;
}

.customize-control.customize-control-ast-color-group .ast-color-group-reset {
	right: 72px;
	cursor: pointer;
}

.customize-control.customize-control-ast-color-group .ast-color-group-responsive-wrap {
	display: none;
}

.customize-control.customize-control-ast-color-group .ast-color-group-responsive-wrap.active {
	justify-content: flex-end;
	display: flex;
}

.customize-control.customize-control-ast-color-group .ast-responsive-btns {
	position: relative;
	right: -5px;
}

.customize-control.customize-control-ast-color-group .ast-bg-img-remove.components-button.is-link {
	width: 100%;
	border: 1px dashed #b4b9be;
	box-sizing: border-box;
	box-shadow: unset;
	padding: 9px 0;
	line-height: 1.6;
	margin-top: 10px;
	text-decoration: none;
}

.customize-control.customize-control-ast-color-group .more-settings {
	margin-top: 12px;
	display: flex;
	justify-content: flex-end;
	padding: 5px 0 5px 0;
	cursor: pointer;
	float: none;
	text-decoration: none;
}

.customize-control-ast-color-group .ast-color-multiple-group-reset.ast-color-group-reset {
	right: 110px;
}

.customize-control-ast-color-group .ast-color-multiple-group-reset.ast-color-group-reset.ast-single-color-group-set {
	right: 112px;
}

#customize-control-astra-settings-woo-single-page-color-group .ast-color-group-reset {
	right: 145px;
}

.ast-top-dotted-divider.customize-control-ast-color-group .ast-control-wrap .ast-color-btn-reset-wrap {
	top: 30px;
}

.ast-top-dotted-divider.customize-control-ast-color-group .ast-divider-title + .ast-control-wrap .ast-color-btn-reset-wrap {
	top: 75px;
}

.customize-control-ast-color-group .ast-control-wrap,
.customize-control-ast-color-group .ast-control-wrap .ast-toggle-desc-wrap {
	display: flex;
	align-items: center;
	width: 100%;
}

.customize-control-ast-color-group .ast-divider-title + .ast-control-wrap .astra-color-picker-wrap {
	top: 73px;
}

.astra-color-picker-wrap.picker-open + span {
	display: none;
}

#customize-control-astra-color-palettes {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

#customize-control-astra-color-palettes .ast-field-settings-wrap {
	position: absolute;
	top: 24px;
}

#customize-control-astra-color-palettes .color-button-wrap {
	display: unset;
	position: relative;
	right: unset;
	top: unset;
	vertical-align: sub;
	z-index: 10;
}

#customize-control-astra-color-palettes .astra-color-picker-wrap {
	position: absolute;
	z-index: 999;
	right: 0;
	width: 100%;
	display: none;
	margin-top: 10px;
}

#customize-control-astra-color-palettes .astra-color-picker-wrap.picker-open {
	display: block;
	z-index: 99;
}

#customize-control-astra-settings-theme-button-border-group-border-size .ast-units-wrapper.ast-range-unit, #customize-control-astra-settings-secondary-theme-button-border-group-border-size .ast-units-wrapper.ast-range-unit {
	top: 10px
}

#customize-control-astra-color-palettes .ast-color-palette-label {
	width: 65%;
	display: inline-block;
	margin-right: 10px;
	vertical-align: top;
}

#customize-control-astra-color-palettes .ast-color-palette-label input {
	background: none;
	border: none;
}

#customize-control-astra-color-palettes .ast-color-palette-label input:focus {
	border: 1px solid black;
	outline: none;
	box-shadow: none;
	background: white;
}

.ast-color-palette-wrapper .ast-single-palette-wrap {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.ast-single-palette-wrap .components-button.astra-color-icon-indicate .component-color-indicator.astra-advanced-color-indicate {
	width: 25px;
	height: 25px;
}

#customize-control-astra-color-palettes .ast-color-palette-wrapper .ast-color-picker-wrap:first-child {
	margin-left: 0;
}

#customize-control-astra-color-palettes .ast-color-palette-wrapper .ast-color-picker-wrap {
	cursor: pointer;
}

.ast-reset-btn-preset-wrap .ast-reset-btn svg {
	height: 13px;
	width: 13px;
}

#customize-control-astra-color-palettes .ast-reset-btn:disabled {
	opacity: 0.6;
}

#customize-control-astra-color-palettes .ast-palette-presets-inner-wrap .ast-preset-label-wrap {
	font-size: 13px;
	line-height: 18px;
	color: var( --ast-customizer-text-color );
	text-align: left;
	width: 64px;
}

.ast-color-preset-container {
	max-height: 304px;
	overflow: scroll;
	overflow-x: hidden;
	-ms-overflow-style: none;
	scrollbar-width: none;
	padding: 16px;
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.ast-color-preset-container::-webkit-scrollbar {
	width: 5px;
}

.ast-color-preset-container::-webkit-scrollbar-track {
	background: #f1f1f1;
}

.ast-color-preset-container::-webkit-scrollbar-thumb {
	background: #d8d8d8;
}

.ast-color-palette-container {
	width: 100%;
	display: inline-grid;
	grid-template-columns: repeat( 2, 1fr );
	column-gap: 10px;
	row-gap: 10px;
}

.ast-color-palette-wrap {
	background: transparent;
	border-radius: 2px;
	overflow: hidden;
	border: 1px solid var( --ast-customizer-border-color );
}

.ast-color-palette-wrap.active {
	border-color: var( --ast-customizer-primary-color );
}

#customize-control-astra-color-palettes .ast-color-palette-wrap > section {
	display: flex;
	text-align: center;
	outline: none;
	cursor: pointer;
	transition: all 0.2s;
	box-shadow: 0 0 0 1px #ddd;
}

#customize-control-astra-color-palettes .ast-single-color-container {
	width: 25%;
	height: 22px;
	display: inline-block;
}

.ast-palette-label-wrap {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 2px;
	color: var( --ast-customizer-input-text-color );
	overflow: hidden;
	width: 100%;
	box-sizing: border-box;
	transition: 0.15s color ease-in-out, 0.15s background-color ease-in-out, 0.15s border-color ease-in-out;
	font-style: normal;
	font-weight: 600;
	font-size: 11px;
	line-height: 16px;
	padding: 3px 0;
}

.ast-color-palette-container :last-child .ast-palette-label-wrap,
.ast-color-palette-container :last-child .ast-palette-label-wrap .palette-name-input:focus {
	color: #fff;
	background: #000;
}

.ast-palette-label-wrap.dark{
	background: #000000;
	color: #fff
}

#customize-control-astra-color-palettes .ast-color-palette-wrap.active > section {
	box-shadow: 0 0 0 1px var( --ast-customizer-color-1 );
}

.ast-color-palette-wrap .ast-palette-label-wrap .editable-palette-name {
	opacity: 0.7;
}

.ast-color-palette-wrap.active .ast-palette-label-wrap .editable-palette-name {
	opacity: 1;
}

.ast-palette-individual-item {
	height: 17px;
	width: 17px;
	display: block;
	box-shadow: 0px 0px 0px 15px inset;
	border-image: initial;
	border-radius: 50%;
	transition: box-shadow 100ms ease 0s;
}

.ast-preset-palette-item .ast-palette-individual-item-wrap:nth-of-type( 5 ) .ast-palette-individual-item,
.ast-preset-palette-item .ast-palette-individual-item-wrap:nth-of-type( 6 ) .ast-palette-individual-item,
.ast-preset-palette-item .ast-palette-individual-item-wrap:nth-of-type( 7 ) .ast-palette-individual-item,
.ast-preset-palette-item .ast-palette-individual-item-wrap:nth-of-type( 8 ) .ast-palette-individual-item {
	border: 0.5px solid #e2e8f0;
}

.components-button.ast-preset-palette-item {
	width: -webkit-fill-available;
	padding: 0;
	height: unset;
	display: flex;
	justify-content: space-between;
}

.ast-palette-presets-inner-wrap:last-child .ast-preset-palette-item {
	margin-bottom: 0;
}

.components-button.ast-preset-palette-item:hover {
	border: none;
	box-shadow: none;
	outline: none;
}

.customize-control-ast-color-palette .ast-field-settings-modal::before {
	right: 18px;
}

section.ast-palette-presets-inner-wrap {
	display: flex;
	padding: 7px;
	transition: all 0.2s;
	border: 1px solid var( --ast-customizer-alternate-background );
	border-radius: 3px;
}

.ast-palette-presets-inner-wrap:hover {
	background: var( --ast-customizer-alternate-background );
	border-radius: 3px;
}

.ast-color-preset-container .components-button.ast-preset-palette-item:focus {
	outline: none;
	box-shadow: none;
}

#customize-control-astra-color-palettes .ast-color-palette-wrapper {
	clear: both;
}

#customize-controls .customize-section-title.is-in-view.is-sticky {
	z-index: 99;
}

#customize-control-astra-color-palettes .ast-palette-selection-wrapper {
	overflow: hidden;
	padding: 2px;
}

@media ( min-width: 1800px ) {
	#customize-control-astra-color-palettes .astra-color-picker-wrap {
		width: auto;
	}
}

#customize-control-astra-color-palettes .ast-field-settings-modal {
	margin-left: -12px;
	margin-right: -12px;
	display: none;
	padding: 0;
	margin-top: 16px;
	border-radius: 4px;
}

.astra-advanced-color-indicate .global-color {
	display: none;
}

.color-button-wrap.has-global-palette-color .astra-advanced-color-indicate .global-color {
	color: var(--ast-customizer-secondary-text-color);
	position: absolute;
	left: 50%;
	top: 50%;
	display: flex;
	transform: translate( -50%, -50% );
}

.color-button-wrap.has-global-palette-color .astra-advanced-color-indicate .global-color.darkish {
	color: var(--ast-customizer-icon-color);
}

.color-button-wrap.has-global-palette-color .astra-advanced-color-indicate .global-color svg {
    fill: transparent;
}

.ast-color-picker-custom-tooltip {
	position: absolute;
	top: -30px;
	bottom: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100%;
}

.ast-color-picker-custom-tooltip[data-title]::after {
	content: attr( data-title );
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: 1ch 1.5ch;
	border-radius: 0.3ch;
	box-shadow: 0 1em 2em -0.5em rgb( 0 0 0 / 35% );
	background: var( --ast-customizer-primary-color );
	color: var( --ast-customizer-secondary-text-color );
	z-index: 1000;
	bottom: calc( 100% + 5px );
	text-align: center;
	animation: tooltips-vert 0.3s ease-out forwards;
	text-transform: none;
	font-size: 0.9em;
	line-height: 1;
	user-select: none;
	pointer-events: none;
	position: absolute;
	display: none;
	opacity: 0;
	left: 50%;
	transform: translate( -50%, -0.5em );
}

.ast-single-palette-wrap > :last-child .ast-color-picker-custom-tooltip[data-title]::after {
	left: -16px;
}

.ast-color-picker-custom-tooltip-wrapper {
	position: relative;
}

.ast-color-picker-wrap:hover + .ast-color-picker-custom-tooltip-wrapper .ast-color-picker-custom-tooltip[data-title]::after {
	display: flex;
	white-space: break-spaces;
	inline-size: max-content;
}

.ast-color-palette .components-popover.components-tooltip {
	position: relative !important;
	left: 5px !important;
	top: -35px !important;
	transform: unset !important;
}

.customize-control .components-tooltip {
	z-index: 8 !important;
}

.ast-color-palette .components-popover .components-popover__content {
	bottom: calc( 100% + 5px );
	animation: tooltips-vert 0.3s ease-out forwards;
	left: 50%;
	transform: translate( -50%, -0.5em );
}

/* Palette Rename CSS */
#customize-control-astra-color-palettes .ast-color-palette-wrap.active > section,
#customize-control-astra-color-palettes .ast-color-palette-wrap > section {
	box-shadow: none;
}

.ast-palette-label-wrap .edit-icon {
	display: flex;
	padding: 1.5px;
}

.ast-palette-label-wrap .edit-icon svg {
	width: 13px;
	height: 13px;
}

.customize-control .ast-palette-label-wrap .palette-name-input {
	min-height: auto;
	box-shadow: none;
	border: none;
	background: transparent;
	text-align: center;
	border-color: transparent;
	padding: 0;
	font-size: 11px;
	line-height: 16px;
}

.ast-color-palette-wrap:hover {
	cursor: pointer;
}

.ast-color-palette-wrap:hover {
    border-color: var(--ast-customizer-primary-color);
}

/* .ast-preset-palette-item.bigword {
    justify-content: flex-end;
    padding: 0px;
} */

/* .bigword .ast-palette-individual-item-wrap {
    margin-right: -5px;
} */

/* Button Icons SVG */
.astra-control-field.astra-color-control .components-button svg {
	fill: transparent;
}

/** Remove Button Color */

.astra-control-field.astra-color-control {
	display: flex;
}

.astra-control-field {
	position: relative;
	margin-top: 10px;
	margin-bottom: 10px;
}

.astra-control-field.astra-color-control .customize-control-title {
	flex-grow: 2;
}

.astra-control-field .customize-control-title {
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 0;
	display: flex;
	align-items: center;
	letter-spacing: 0.1px;
	line-height: 18px;
}

.astra-globe-dashicon {
	color: #ffffff;
	background: rgba( 0, 0, 0, 0.4 );
	border-radius: 100%;
	border: 0.1px solid rgba( 0, 0, 0, 0.1 );
	opacity: 0.78;
}

.components-button.astra-color-icon-indicate .component-color-indicator.astra-advanced-color-indicate {
	width: 28px;
	height: 28px;
	border-radius: 50%;
	margin: 0;
	background: inherit;
}

.component-color-indicator {
	width: 25px;
	height: 16px;
	display: inline-block;
}

.astra-advanced-color-indicate.component-color-indicator {
	border: none;
}

.astra-color-icon-indicate.components-button {
	height: fit-content;
	position: relative;
	transform: scale( 1 );
	transition: none;
	border-radius: 50%;
	padding: 0;
	background-image: linear-gradient( 45deg, #ddd 25%, transparent 0 ), linear-gradient( -45deg, #ddd 25%, transparent 0 ), linear-gradient( 45deg, transparent 75%, #ddd 0 ), linear-gradient( -45deg, transparent 75%, #ddd 0 );
	background-size: 10px 10px;
	background-position: 0 0, 0 5px, 5px -5px, -5px 0;
}

/** Color */
.astra-popover-color .components-popover__content .components-focal-point-picker-wrapper {
	box-sizing: border-box;
}

.astra-popover-color .components-popover__content .components-focal-point-picker_position-display-container input[type="number"].components-text-control__input {
	min-height: 16px;
	line-height: 16px;
	font-size: 12px;
	width: 50px;
	font-weight: normal;
}

.astra-popover-color .components-popover__content .components-focal-point-picker_position-display-container .components-base-control {
	flex: 1;
	margin-bottom: 0;
}

.astra-popover-color .components-popover__content .components-focal-point-picker_position-display-container .components-base-control .components-base-control__label {
	margin-bottom: 0;
	margin-right: 0.2em;
}

.astra-popover-color .components-popover__content .components-focal-point-picker_position-display-container .components-base-control__field {
	display: flex;
	align-items: center;
	font-size: 8px;
	font-weight: 600;
	font-style: normal;
	text-transform: uppercase;
}

.astra-popover-color .components-popover__content .components-focal-point-picker_position-display-container .components-base-control:last-child .components-base-control__field {
	justify-content: flex-end;
}

.astra-popover-color .components-popover__content .actions {
	display: flex;
	justify-content: center;
	margin-bottom: 10px;
}

.astra-popover-color .components-popover__content .actions .button {
	flex: 1;
	margin-top: 10px;
}

.astra-background-picker-wrap .astra-popover-color .components-popover__content {
	min-width: 300px;
	min-height: 340px;
	max-height: 60vh;
}

.color-button-wrap {
	display: inline-block;
	position: absolute;
	right: 0;
	top: 0;
	transform: scale( 1 );
	transition: transform 0.1s ease;
	height: 27px;
	width: 27px;
}

.components-button.astra-background-icon-indicate {
	width: 50px;
	height: 50px;
	border-radius: 50%;
	position: relative;
	transform: scale( 1 );
	transition: transform 0.1s ease;
	border-radius: 50%;
	padding: 0;
	background-image: linear-gradient( 45deg, #ddd 25%, transparent 0 ), linear-gradient( -45deg, #ddd 25%, transparent 0 ), linear-gradient( 45deg, transparent 75%, #ddd 0 ), linear-gradient( -45deg, transparent 75%, #ddd 0 );
	border: 1px solid #dadada;
	background-size: 10px 10px;
	background-position: 0 0, 0 5px, 5px -5px, -5px 0;
}

.components-button.astra-background-icon-indicate .component-color-indicator.astra-advanced-color-indicate {
	width: 100%;
	height: 100%;
	border-radius: 4px;
	margin: 0;
	display: block;
	position: absolute;
	border: 0;
	top: 0;
}

.components-button.astra-background-icon-indicate > svg.dashicon {
	position: absolute;
	transform: translate( -50%, -50% );
	left: 50%;
	top: 50%;
	color: white;
	background: rgba( 0, 0, 0, 0.6 );
	border-radius: 100%;
	width: 16px;
	height: 16px;
	border: 1px solid rgba( 0, 0, 0, 0.1 );
}

.components-button.astra-background-icon-indicate > svg.dashicon path {
	transform: translate( 1px, 1px );
}

.components-button.astra-background-icon-indicate img.astra-background-image-preview {
	display: flex;
	object-fit: cover;
	min-width: 100%;
	min-height: 100%;
	position: absolute;
	top: 0;
}

.components-button.astra-background-icon-indicate:hover {
	box-shadow: none !important;
}

.astra-control-field.astra-color-control {
	display: flex;
}

.astra-control-field.astra-color-control .customize-control-title {
	flex-grow: 2;
}

.components-popover.astra-popover-color .components-popover__content {
	padding: 15px 15px 0px;
	box-sizing: initial;
	background: #fff;
	border: unset;
	border-radius: 4px;
	-webkit-box-shadow: 0 2px 15px rgba( 0, 0, 0, 0.3 );
	box-shadow: 0 2px 15px rgba( 0, 0, 0, 0.3 );
}

.customize-control-ast-color.customize-control > * {
	position: relative;
	display: inline-flex;
	align-items: center;
	width: 100%;
	min-height: 28px;
}

.components-popover.astra-popover-color .components-popover__content .sketch-picker {
	padding: 0 0 5px !important;
	box-shadow: none !important;
	border-radius: 0px !important;
}

.components-popover.astra-popover-color .components-popover__content .attachment-media-view {
	margin-top: 10px;
	margin-bottom: 10px;
}

.astra-swatches-wrap .astra-swatche-item-wrap:hover {
	transform: scale( 1.1 ) !important;
}

.astra-swatches-wrap .astra-swatche-item-wrap .astra-swatch-item {
	background-image: linear-gradient( 45deg, #ddd 25%, transparent 0 ), linear-gradient( -45deg, #ddd 25%, transparent 0 ), linear-gradient( 45deg, transparent 75%, #ddd 0 ), linear-gradient( -45deg, transparent 75%, #ddd 0 );
	background-size: 10px 10px;
	background-position: 0 0, 0 5px, 5px -5px, -5px 0;
	padding: 0;
	display: flex;
	justify-content: center;
}

.astra-swatches-wrap .astra-swatche-item-wrap .astra-swatch-item .dashicon {
	display: none;
}

.astra-swatches-wrap .astra-swatche-item-wrap .astra-swatch-item.swatch-active {
	box-shadow: 0 0 0 8px inset !important;
}

.astra-swatches-wrap .astra-swatche-item-wrap .astra-swatch-item.swatch-active .dashicon {
	display: block;
	color: white;
	background: rgba( 0, 0, 0, 0.6 );
	width: 16px;
	height: 16px;
	border: 1px solid rgba( 0, 0, 0, 0.1 );
	border-radius: 100%;
}

.astra-swatches-wrap .astra-swatche-item-wrap .astra-swatch-item.swatch-active .dashicon path {
	transform: translate( 1px, 1px );
}

.components-button.astra-color-icon-indicate > .dashicon {
	position: absolute;
	transform: translate( -50%, -50% );
	left: 50%;
	top: 50%;
	color: white;
	background: rgb( 85 93 101 );
	border-radius: 4px;
	width: 17px;
	height: 17px;
	font-size: 17px;
	border: 1px solid rgba( 0, 0, 0, 0.1 );
}

.astra-popover-color {
	margin: 5px -5px 0;
	background-color: #ffffff;
	border-radius: 4px;
	box-shadow: 0 6px 24px 0 #0000003d;
}

.astra-popover-color .components-h-stack.ast-color-palette {
	padding: 14px 5px;
}

.customize-control-ast-responsive-background .astra-popover-color .components-h-stack.ast-color-palette,
.customize-control-ast-color-group .astra-popover-color .astra-background-tabs .components-h-stack.ast-color-palette {
	padding: 0 5px 14px;
}

.astra-popover-color .components-h-stack .components-button {
	flex: inherit;
}

.astra-popover-color .components-circular-option-picker__option:focus::after {
	width: auto;
	height: auto;
}

.components-circular-option-picker__option:after {
    box-shadow: none;
}

.astra-popover-color .components-h-stack .components-circular-option-picker__option-wrapper {
	margin-right: 0;
}

.astra-popover-color .components-color-picker .react-colorful__hue,
.astra-popover-color .components-color-picker .react-colorful__alpha {
	width: 90%;
}

.ast-field-settings-modal .astra-popover-color {
	margin-left: 0px;
	border: 1px solid #dedede;
}

.ast-field-settings-modal .astra-popover-color .components-tab-panel__tabs {
	padding: 8px;
}

.components-color-picker__saturation-black {
	border-radius: 4px 4px 0 0;
}

.components-color-picker__saturation-color {
	border-radius: 3px;
}

.components-color-picker__saturation-white {
	border-radius: 3px;
}

.astra-color-picker-wrap {
	position: absolute;
	z-index: 2;
	top: 30px;
	left: 0;
	right: 0;
	bottom: 0;
	display: none;
}

.astra-color-picker-wrap .astra-popover-color .components-color-picker,
.astra-color-picker-wrap .astra-popover-color .react-colorful {
	width: 100%;
}

.astra-popover-color .components-custom-gradient-picker {
	z-index: 1;
}

.astra-popover-color .components-custom-gradient-picker__ui-line {
	z-index: -1;
}

.astra-color-picker-wrap.picker-open {
	display: block;
}

.ast-field-settings-modal .astra-color-picker-wrap {
	position: relative;
	top: 10px;
}

.ast-color-palette .components-circular-option-picker__option-wrapper {
	margin-right: 6px;
	position: relative;
	height: 25px;
	width: 25px;
}

/* Clear Button CSS */
.ast-color-btn-clear-wrap {
	position: absolute;
	top: 1.5px;
	right: 65px;
}

.ast-color-btn-clear-wrap .astra-color-clear-button.components-button {
	padding: 10px 3px;
	width: 20px;
	height: 20px;
}

.ast-clear-btn-inside-picker.components-button,
.ast-reset-btn-inside-picker.components-button,
.astra-popover-tabs .ast-clear-btn-inside-picker.components-button {
	margin: 5px 20px 20px 15px;
	padding: 0 8px;
	border: 1px solid #ccc;
	box-shadow: none;
}

.ast-clear-btn-inside-picker.components-button:hover,
.astra-popover-tabs .ast-clear-btn-inside-picker.components-button:hover,
.ast-clear-btn-inside-picker.components-button:focus,
.astra-popover-tabs .ast-clear-btn-inside-picker.components-button:focus,
.ast-clear-btn-inside-picker.components-button:active,
.astra-popover-tabs .ast-clear-btn-inside-picker.components-button:active,
.astra-popover-tabs [ID*="-gradient-view"] .components-circular-option-picker__clear:active {
	box-shadow: none !important;
	background: transparent !important;
}

.ast-clear-btn-inside-picker {
	padding: 0 8px;
}

/* Reset Button CSS */
.ast-color-btn-reset-wrap {
	position: absolute;
	right: 36px;
}

.customize-control.customize-control-ast-color .ast-color-btn-reset-wrap {
	top: 50%;
	translate: 0 -50%;
}

.ast-color-btn-reset-wrap .ast-reset-btn.components-button,
.ast-resp-slider-reset-wrap .ast-reset-btn.components-button {
	color: #006ba1;
	height: 20px;
	width: 20px;
	padding: 4px;
	background: transparent;
	box-shadow: none !important;
	cursor: pointer;
}

.ast-reset-btn.components-button.is-secondary:not(:disabled) svg path {
	fill: var(--ast-customizer-color-1);
  }

.astra-color-clear-button.components-button:focus:not( :disabled ) {
	outline: unset;
	border: none;
}

.ast-color-btn-reset-wrap .ast-reset-btn.components-button:hover,
.ast-color-btn-reset-wrap .ast-reset-btn.components-button:focus,
.ast-resp-slider-reset-wrap .ast-reset-btn.components-button:hover,
.ast-resp-slider-reset-wrap .ast-reset-btn.components-button:focus {
	box-shadow: none;
	border: none;
	background: transparent;
	/* stroke: var( --ast-customizer-color-1); */
}

.ast-reset-btn:hover svg path {
	fill: var( --ast-customizer-color-1 );
}

.astra-color-clear-button.components-button.is-secondary:hover:not( :disabled ),
.astra-color-clear-button.components-button:focus:not( :disabled ) {
	box-shadow: 0 0 black;
	padding: 1.5px;
	border: none;
}

.astra-color-clear-button.components-button.is-secondary:disabled,
.ast-reset-btn.components-button.is-secondary:disabled span {
	color: var( --ast-customizer-color-6 );
}

/** Inside Setting group popup - Colors */
.ast-field-settings-modal .color-button-wrap {
	right: 16px;
}

.ast-field-settings-modal .ast-color-btn-reset-wrap {
	right: 59px;
	top: 6px;
}

.ast-field-settings-modal .ast-color-btn-clear-wrap {
	right: 78px;
}

.astra-color-picker-wrap .components-color-picker__inputs-wrapper {
	min-width: unset;
}

/** Gradient Color Picker */
.customize-control .ast-gradient-color-picker input[type="number"] {
	width: 100%;
}


#customize-control-astra-settings-site-layout-outside-bg-obj-responsive .ast-reset-btn.components-button.is-secondary:not(:disabled) svg path {
	fill: var(--ast-customizer-color-6);
  }
  
  #customize-control-astra-settings-site-layout-outside-bg-obj-responsive .ast-reset-btn.components-button.is-secondary:not(:disabled) svg:hover  path{
	fill: var(--ast-customizer-color-1);
  }
  
  #customize-control-astra-settings-content-bg-obj-responsive .ast-reset-btn.components-button.is-secondary:not(:disabled) svg path {
	fill: var(--ast-customizer-color-6);
  }
  #customize-control-astra-settings-content-bg-obj-responsive .ast-reset-btn.components-button.is-secondary:not(:disabled) svg:hover  path{
	fill: var(--ast-customizer-color-1);
  }

.ast-gradient-color-picker .components-select-control.components-custom-gradient-picker__type-picker {
	align-items: center;
	flex-direction: inherit;
}

.astra-color-picker-wrap .components-popover__content {
	width: max-content;
}

.astra-color-picker-wrap .components-custom-gradient-picker__inserter .components-color-picker,
.astra-color-picker-wrap .astra-popover-color .ast-gradient-ui .components-color-picker,
.astra-color-picker-wrap .astra-popover-color .ast-gradient-ui .react-colorful {
	width: 216px;
}

.astra-color-picker-wrap .ast-gradient-ui .components-select-control.components-custom-gradient-picker__type-picker {
	flex-direction: column;
	align-items: start;
}

.astra-color-picker-wrap .ast-gradient-ui .components-angle-picker-control {
	margin-bottom: 0;
}

.astra-color-picker-wrap .ast-gradient-ui .components-custom-gradient-picker__remove-control-point {
	padding: 0 16px 16px 16px;
	border: 0;
}

.astra-color-picker-wrap .ast-gradient-ui .components-custom-gradient-picker__remove-control-point:focus {
	box-shadow: none;
	outline: none;
}

/** Gradient Color Picker -> RGBA & HSLA range fields */
.customize-control .ast-gradient-color-picker .is-alpha-enabled .components-base-control__field [type="number"] {
	width: 100%;
}

/** Modal Color Picker - Group Param */
.ast-field-settings-modal .components-circular-option-picker__option-wrapper .components-circular-option-picker__option:focus::after {
	width: 24px;
	height: 24px;
}

.ast-field-settings-modal .ast-color-palette .components-circular-option-picker__option-wrapper {
	height: 25px;
	width: 25px;
}

.ast-field-settings-modal .ast-color-palette .components-circular-option-picker__option.is-pressed + svg {
	left: 2.5px;
	top: 0px;
	width: 20px;
}

.ast-color-palette .components-circular-option-picker__option.is-pressed + svg {
	left: 0;
	top: 0;
	mix-blend-mode: difference;
}

.astra-popover-color .components-circular-option-picker .components-circular-option-picker__swatches {
	gap: 4px;
	justify-content: center;
}

.astra-popover-color .components-circular-option-picker__option-wrapper:hover {
	transform: scale( 1.1 );
}

.components-circular-option-picker__option-wrapper .components-button:hover {
	box-shadow: inset 0px 0px 0px 1px #0003 !important;
}

.customize-control-ast-color + .customize-control-ast-color {
	margin-top: 30px;
}

.astra-popover-tabs .color-tab [data-wp-component="Flex"],
.astra-popover-color .color-tab [data-wp-component="Flex"] {
	padding-left: 8px;
}

.astra-popover-tabs .ast-clear-btn-inside-picker.components-button,
.astra-popover-color .ast-clear-btn-inside-picker.components-button {
	margin-left: 8px;
	margin-top: 0px;
}

.astra-popover-color div[data-wp-component="Flex"] {
	padding-left: 10px;
}
@media ( max-width: 1845px ) {
	/** Responsive Devices - Compatibility CSS */
	.ast-color-palette .components-circular-option-picker__option-wrapper {
		height: 22px;
		width: 22px;
	}

	.astra-background-tabs .ast-color-palette .components-circular-option-picker__option-wrapper {
		height: 20px;
		width: 20px;
	}

	.ast-color-palette .components-circular-option-picker__option.is-pressed + svg {
		width: 20px;
		top: -2px;
	}

	.astra-popover-color .ast-color-palette.components-circular-option-picker {
		width: auto;
		padding: 16px 0px 0px 8px;
	}

	.astra-typography-control .typography-button-wrap > button.components-button.astra-typography-preview-indicate {
		padding: 0 2px;
	}

	.astra-typography-control .typography-button-wrap > button.components-button {
		padding: 0 4px;
	}

	/** Modal Popup */
	.ast-fields-wrap .components-circular-option-picker__option-wrapper .components-circular-option-picker__option:focus::after {
		width: auto;
		height: auto;
	}

	.ast-fields-wrap .ast-color-palette .components-circular-option-picker__option-wrapper {
		height: 20px;
		width: 20px;
	}

	.ast-fields-wrap .ast-color-palette .components-circular-option-picker__option.is-pressed + svg {
		left: 1px;
		top: -2px;
		width: 18px;
	}

	.astra-popover-color .components-circular-option-picker__option[aria-pressed="true"] + svg,
	.components-circular-option-picker__option[aria-selected="true"] + svg {
		width: 22px;
		height: 22px;
		left: 0;
		top: 0;
		mix-blend-mode: difference;
	}

	.ast-color-palette .components-circular-option-picker__option-wrapper .components-circular-option-picker__option:focus::after {
		width: 24px;
		height: 24px;
	}

	.astra-background-tabs .components-circular-option-picker__option[aria-selected="true"] + svg {
		width: 20px;
		height: 20px;
	}
}

@supports ( -moz-appearance: none ) {
	@media ( max-width: 1845px ) {
		.ast-color-palette .components-circular-option-picker__option-wrapper {
			margin-right: 5px;
		}

		.ast-field-settings-modal .components-circular-option-picker__option-wrapper .components-circular-option-picker__option:focus::after {
			width: 21px;
			height: 21px;
		}

		.ast-field-settings-modal .ast-color-palette .components-circular-option-picker__option-wrapper {
			height: 20px;
			width: 20px;
		}

		.ast-field-settings-modal .ast-color-palette .components-circular-option-picker__option.is-pressed + svg {
			left: 3px;
			top: -2px;
			width: 15px;
		}
	}
}

.astra-color-icon-indicate.components-button:focus:not( :disabled ) {
	box-shadow: unset;
	outline: unset;
}

.astra-color-icon-indicate.components-button:hover {
	transform: scale( 1.05 );
}

.ast-top-divider.customize-control-ast-color .ast-divider-title + .ast-control-wrap,
.ast-top-divider.customize-control-ast-color-group .ast-divider-title + .ast-control-wrap {
	position: relative;
	/* margin-top: 16px; */
	display: inline-flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	min-height: 28px;
}

.ast-top-divider.customize-control-ast-toggle-control .ast-divider-title + .ast-togglecontrol-wrapper,
.ast-bottom-divider.customize-control-ast-toggle-control .ast-divider-title + .ast-togglecontrol-wrapper,
.ast-top-divider.customize-control-ast-responsive-toggle-control .ast-divider-title + .ast-responsive-toggle-control-wrapper,
.ast-top-divider.customize-control-ast-responsive-color .ast-divider-title + .ast-control-wrap,
.ast-top-divider.customize-control-ast-slider .ast-divider-title + .ast-slider-wrap,
.customize-control-ast-color-group .ast-divider-title + .ast-control-wrap {
	margin-top: 16px;
}

.ast-top-divider.customize-control-ast-color-group .ast-control-wrap {
	display: inline-flex;
	width: 100%;
}

.ast-color-palette.components-circular-option-picker .components-circular-option-picker__swatches {
	width: 100%;
}

/* Instead of updating above CSS, handled this control's edge case separately. */
li#customize-control-astra-settings-transparent-content-section-text-color-responsive .ast-control-wrap .ast-responsive-btns {
	top: 2px;
}

.customize-control.customize-control-ast-color + .customize-control.customize-control-ast-color,
.customize-control.customize-control-ast-color + .customize-control.customize-control-ast-responsive-color,
.customize-control.customize-control-ast-color + .customize-control.customize-control-ast-color-group,
.customize-control.customize-control-ast-responsive-color + .customize-control.customize-control-ast-color,
.customize-control.customize-control-ast-responsive-color + .customize-control.customize-control-ast-responsive-color,
.customize-control.customize-control-ast-responsive-color + .customize-control.customize-control-ast-color-group,
.customize-control.customize-control-ast-color-group + .customize-control.customize-control-ast-color,
.customize-control.customize-control-ast-color-group + .customize-control.customize-control-ast-responsive-color,
.customize-control.customize-control-ast-color-group + .customize-control.customize-control-ast-color-group:not( :has( .ast-divider-title ) ) {
	margin-top: 8px;
}

/* Global customizer color palette */

:root {
	--ast-customizer-color-1: #0284c7; /* active color */
	--ast-customizer-color-2: #0ea5e9; /* active focus color */
	--ast-customizer-color-3: #2271b1; /* active color border */
	--ast-customizer-color-4: #1e293b; /* title / heading */
	--ast-customizer-color-5: #475569; /* input text color */
	--ast-customizer-color-6: #9CA3AF; /* Sub text color */
	--ast-customizer-color-7: #D8DBDF; /* border color / outline / grey background */
	--ast-customizer-color-8: #4B5563; /* icon color / Toggle color */
	--ast-customizer-color-9: #ffffff; /* background color */
	--ast-customizer-color-10: #ffffff; /* text invert / Icon invert */
	--ast-customizer-color-12: #1e293b; /* alt background 2*/
	--ast-customizer-color-11: #F5F5F5; /* alt background */
	--ast-customizer-color-13: #F3F4F6; /* alt background 3 */
	--ast-customizer-color-14: #cc1818; /* danger color */

	/* New Customizer Design - Variables */
	--ast-customizer-global-background: #ffffff;
	--ast-customizer-primary-color: #0284c7;
	--ast-customizer-alternate-primary-color: #0ea5e9;
	--ast-customizer-secondary-color: #f3f4f6;
	--ast-customizer-text-color: #1e293b;
	--ast-customizer-sub-text-color: #9ca3af;
	--ast-customizer-secondary-text-color: #ffffff;
	--ast-customizer-input-text-color: #475569;
	--ast-customizer-border-color: #d8dbdf;
	--ast-customizer-active-border-color: #0ea5e9;
	--ast-customizer-icon-color: #4b5563;
	--ast-customizer-icon-background: #e5e7eb;
	--ast-customizer-alternate-background: #f0f5ff;
	--ast-customizer-divider-color: #ededed;

	/* Other variables */
	--ast-customizer-notice-background: #f9fafb;
	--ast-customizer-tab-radius: 3px;
}

input[type="text"],
input[type="password"],
input[type="color"],
input[type="date"],
input[type="datetime"],
input[type="datetime-local"],
input[type="email"],
input[type="month"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="time"],
input[type="url"],
input[type="week"],
select,
textarea {
	color: var( --ast-customizer-color-5 );
}

.customize-control-ast-customizer-link .customizer-link {
	text-decoration: underline;
	font-size: 11px;
	line-height: 16px;
	display: inline-flex;
	margin-left: 16%;
}

.customize-control-ast-customizer-link:empty {
	display: none;
}

/* Customizer Scroll CSS */
.accordion-section-content::-webkit-scrollbar,
.wp-full-overlay-sidebar-content::-webkit-scrollbar {
	width: 6px;
	background-color: #f1f1f1;
}

.accordion-section-content::-webkit-scrollbar-thumb,
.wp-full-overlay-sidebar-content::-webkit-scrollbar-thumb {
	border-radius: 10px;
	background-color: #d8d8d8;
}

.accordion-section-content::-webkit-scrollbar-track,
.wp-full-overlay-sidebar-content::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 0 rgba( 0, 0, 0, 0.3 );
	width: 6px;
}

.wp-full-overlay-sidebar:after {
	display: none;
}

.customize-control:not( .customize-control-nav_menu_item ) {
	position: relative;
	margin-bottom: 0;
	margin-top: 16px;
}

#customize-control-astra-settings-scroll-to-top-on-devices {
	margin-top: 24px;
}

.customize-control.customize-control-ast-color,
.customize-control-ast-responsive-color,
.customize-control.customize-control-ast-color-group,
.customize-control-ast-responsive-background,
.customize-control-ast-background,
.customize-control-ast-group-title {
	display: flex;
	align-items: center;
	min-height: 28px;
	flex-wrap: wrap;
}

.customize-control-ast-responsive-background label {
	white-space: nowrap;
}

.customize-control .media-position-setting label.components-text {
	text-transform: capitalize;
	font-size: 12px;
	font-weight: 400;
}

.media-position-setting .astra-popover-tabs .components-tab-panel__tabs {
	padding-left: 0;
	padding-right: 0;
}

.customize-control[ID*="-builder-layout-ast-context-tabs"] {
	margin-top: -12px;
	margin-left: -12px;
	margin-right: -12px;
	width: calc( 100% + 24px );
}

[ID*="-builder-layout-ast-context-tabs"] {
	margin-top: 10px;
}

.customize-section-title {
	margin: -12px -24px 0 -24px;
}

.customize-section-description-container,
#customize-controls .customize-info.section-meta,
#customize-controls .customize-info {
	margin-bottom: 0;
}

#customize-controls #sub-accordion-panel-nav_menus .customize-info {
	margin-bottom: 16px;
}

#customize-controls .components-base-control .components-base-control__field {
	margin-bottom: 0;
}

#customize-controls .components-base-control .components-base-control__field .components-input-control__backdrop {
	border-color: #d1d5db !important;
}

.customize-control .customize-inside-control-row {
	margin-left: 0;
	padding-top: 0;
	padding-bottom: 0;
}

.customize-control-title {
	display: inline-block;
	margin-bottom: 16px;
	vertical-align: middle;
	color: var( --ast-customizer-text-color );
	font-size: 13px;
	line-height: 18px;
	font-weight: 400;
}

.customize-control-ast-color .customize-control-title,
.customize-control-ast-responsive-background .customize-control-title,
.customize-control-ast-background .customize-control-title,
.customize-control-ast-responsive-color .customize-control-title {
	display: contents;
}

.customize-control .ast-control-tooltip {
	position: absolute;
	top: 0;
	right: 0;
	color: var( --ast-customizer-sub-text-color );
	cursor: help;
}

.customize-control .ast-control-tooltip:hover {
	color: var( --ast-customizer-primary-color );
}

.customize-control.customize-control-ast-responsive-background .ast-control-tooltip {
	top: 50%;
	right: 36px;
	transform: translateY( -50% );
}

.customize-control.customize-control-ast-responsive-background:has( .ast-color-btn-reset-wrap ) .ast-control-tooltip {
	right: 60px;
}

.customize-control.customize-control-ast-toggle-control .ast-control-tooltip {
	position: absolute;
	top: 50%;
	right: 40px;
	transform: translateY( -50% );
	z-index: 1;
}

.customize-control.customize-control-ast-toggle-control.ast-top-dotted-divider .ast-control-tooltip {
	position: absolute;
	top: 55%;
	right: 40px;
	transform: translateY( 0 );
}

/* Specific fix: align tooltip for Force Reload toggle */
#customize-control-astra-settings-color-switcher-force-reload.ast-top-dotted-divider .ast-control-tooltip {
	transform: translateY( -50% );
}

.customize-control-ast-selector.customize-control .ast-control-tooltip {
	top: -3px;
}

.customize-control.customize-control-ast-toggle-control.ast-bottom-divider .ast-control-tooltip {
	top: calc( 50% - 11px );
}

.customize-control.customize-control-ast-toggle-control .ast-description-enabled .components-form-toggle {
	margin-left: 25px;
}

.customize-control.customize-control-ast-divider .ast-control-tooltip {
	position: absolute;
	top: auto;
	bottom: 3px;
	right: 0;
}

.ast-fields-wrap .customize-control .ast-control-tooltip {
	right: 15px;
	top: 6px;
}

.customize-control .ast-sortable-subfields-wrap .ast-control-tooltip {
	position: absolute !important;
	top: 40%;
	right: 0px;
	transform: translateY( -50% );
	z-index: 1;
}

.customize-control .ast-sortable-subfields-wrap .customize-control-ast-toggle .ast-control-tooltip {
	right: 30px;
	top: 50%;
}

#customize-control-astra-settings-header-logo-color .dashicons-editor-help[data-title]+.ast-tooltip[data-title]::before {
	right: 58px;
}



#customize-control-ast-dynamic-single-page-article-featured-image-position-layout-1 .ast-tooltip {
    top: 9px;
}

#customize-control-ast-dynamic-single-post-article-featured-image-position-layout-1 .ast-tooltip {
    top: 83px;
}

#customize-control-ast-dynamic-single-post-article-featured-image-position-layout-1 .dashicons-editor-help[data-title]+.ast-tooltip[data-title]::before, #customize-control-ast-dynamic-single-post-article-featured-image-position-layout-1 .dashicons-editor-help[data-title]+.ast-tooltip[data-title]::before  {
	right: 4px;
	transform: rotate(180deg);
	top: -66px;
}

#customize-control-ast-dynamic-single-product-remove-featured-padding .ast-control-tooltip, #customize-control-ast-dynamic-single-page-remove-featured-padding .ast-control-tooltip, #customize-control-ast-dynamic-single-post-remove-featured-padding .ast-control-tooltip {
    right: 38px;
}

#customize-control-ast-dynamic-single-product-article-featured-image-position-layout-1 .ast-tooltip {
    top: 100px;
}


#customize-control-ast-dynamic-single-product-article-featured-image-position-layout-1 .dashicons-editor-help[data-title]+.ast-tooltip[data-title]::before{
	right: 4px;
	transform: rotate(180deg);
	top: -83px;
}

#customize-control-astra-settings-single-post-sidebar-layout .ast-control-tooltip,
#customize-control-astra-settings-single-page-sidebar-layout .ast-control-tooltip {
	top: 15px;
}

/* Alignment fix for WooCommerce sidebar layout control */
#customize-control-astra-settings-woocommerce-sidebar-layout .ast-control-tooltip,
#customize-control-astra-settings-single-product-sidebar-layout .ast-control-tooltip,
#customize-control-astra-settings-archive-product-sidebar-layout .ast-control-tooltip {
        top: 15px;
}

#customize-control-astra-settings-product-sale-notification .ast-control-tooltip {
	top: -35px !important;
}

#customize-control-astra-settings-product-sale-notification .ast-tooltip {
	margin: -34px 0 0 20px;
}

#customize-control-astra-settings-product-sale-notification .dashicons-editor-help[data-title]+.ast-tooltip[data-title]::before {
	right: 20px;
}

.ast-top-dotted-divider .dashicons-editor-help[data-title]:hover ~ .ast-tooltip,
.ast-top-section-divider .dashicons-editor-help[data-title]:hover ~ .ast-tooltip {
	top: 24px;
}

/* Select2 WP 5.3 compatibility */
.customize-control .select2-search input[type="text"].select2-search__field {
	min-height: 17px;
}

input[type="text"].select2-search__field {
	line-height: 1;
	border-radius: 0;
}

#customize-control-astra-settings-ast-callback-notice-header-transparent-header-meta-link,
#customize-control-astra-settings-ast-callback-notice-header-transparent-header-logo-link {
	margin-top: -5px;
	background-color: var( --ast-customizer-notice-background );
	border-radius: 0 0 4px 4px;
	border: 1px solid var( --ast-customizer-border-color );
	border-top: none;
	width: calc( 100% - 2px );
	padding-bottom: 14px;
}

/* CSS to show popups without scroll */
#customize-theme-controls .control-section.open {
	height: 100%;
	padding: 12px 24px 25px 24px;
}

/* Safari Browser specific CSS */
@media not all and ( min-resolution: 0.001dpcm ) {
	@supports ( -webkit-appearance: none ) {
		#customize-control-astra-settings-ast-header-responsive-logo-width .wrapper {
			display: inline-block;
			width: 100%;
		}

		.dashicons-desktop:before,
		.dashicons-tablet:before,
		.dashicons-smartphone:before {
			color: #000000;
			opacity: 0.75;
		}

		.ast-reset-btn.components-button svg {
			padding: 0px;
			height: 13px;
		}
	}
}

/* Firefox Browser specific CSS */
@-moz-document url-prefix() {
	.control-section.open .customize-control:last-child {
		margin-bottom: 20px;
	}
}

/* IE Browser specific CSS */
@media screen and ( -ms-high-contrast: active ), ( -ms-high-contrast: none ) {
	.control-section.open .customize-control:last-child {
		padding-bottom: 20px;
	}

	#customize-control-astra-settings-blog-archive-image-width .customize-control-title,
	#customize-control-astra-settings-blog-archive-image-height .customize-control-title,
	#customize-control-astra-settings-blog-single-post-image-width .customize-control-title,
	#customize-control-astra-settings-blog-single-post-image-height .customize-control-title {
		margin-left: 0px;
		margin-right: 20px;
	}
}

.wp-customizer li:not( .customize-control-sidebar_block_editor ) h1,
.wp-customizer li:not( .customize-control-sidebar_block_editor ) h2,
.wp-customizer li:not( .customize-control-sidebar_block_editor ) h3,
.wp-customizer li:not( .customize-control-sidebar_block_editor ) h4,
.wp-customizer li:not( .customize-control-sidebar_block_editor ) h5,
.wp-customizer li:not( .customize-control-sidebar_block_editor ) h6 {
	font-weight: 600;
}

/* Customizer - widget appender bottom space CSS */
.block-list-appender {
	margin-bottom: 20px;
}

.block-editor-block-inspector .block-editor-block-card {
	margin-top: 12px;
}

/* Customizer - builder widget area's block gets round shapes in more-settings tab */
.components-circular-option-picker__option-wrapper::before {
	background: unset;
}

.block-editor-panel-color-gradient-settings .components-circular-option-picker__option-wrapper {
	position: relative;
}

/* Hiding this specific control on responsive devices.  */
.preview-tablet #customize-control-astra-settings-woo-desktop-cart-flyout-width {
	display: none;
}

.preview-tablet #customize-control-astra-settings-woo-desktop-cart-flyout-direction {
	display: none;
}

.preview-mobile #customize-control-astra-settings-woo-desktop-cart-flyout-width {
	display: none;
}

.preview-mobile #customize-control-astra-settings-woo-desktop-cart-flyout-direction {
	display: none;
}

.wp-core-ui #customize-controls .control-section:hover > .accordion-section-title,
.wp-core-ui #customize-controls .control-section .accordion-section-title:hover,
.wp-core-ui #customize-controls .control-section .accordion-section-title:focus,
.wp-core-ui .customize-panel-back:hover,
.wp-core-ui .customize-panel-back:focus,
.wp-core-ui .customize-section-back:hover,
.wp-core-ui .customize-section-back:focus {
	border-left-color: var( --ast-customizer-primary-color );
}

/* Toggle Section Control */
.ast-section-toggle {
	display: grid;
	grid-template-columns: 70% 20% 10%;
	align-items: center;
	padding: 0 0 0 12px;
	height: 100%;
	align-items: center;
	align-content: center;
	max-height: 46px;
}

.ast-section-toggle label {
	font-size: 13px;
	line-height: 1;
	font-weight: 400;
	height: 100%;
	display: inline-flex;
	align-items: center;
}

.ast-section-toggle > label {
	display: block;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	height: auto;
	line-height: 34px;
}

.ast-section-toggle:not( .active ) > label {
	cursor: default;
}

.customize-control.customize-control-ast-section-toggle {
	box-shadow: none !important;
	line-height: 42px;
	font-size: 13px;
	display: inline-grid;
	align-items: center;
	height: auto;
	position: relative;
	white-space: nowrap;
	border-radius: 3px;
	transition: all 0.2s;
	padding-top: 0;
	padding-bottom: 0;
	margin-top: 24px;
	margin-top: 16px;
}

.customize-control.customize-control-ast-section-toggle .ast-section-toggle {
	border: 1px solid var( --ast-customizer-border-color );
	border-radius: 3px;
}

.customize-control.customize-control-ast-section-toggle .ast-section-toggle:hover {
	background: var( --ast-customizer-primary-color3 );
}

.customize-control.customize-control-ast-section-toggle .ast-section-toggle.active {
	border-color: var( --ast-customizer-primary-color );
	color: var( --ast-customizer-primary-color );
	background: var( ---ast-customizer-global-background9 );
}

.ast-form-toggle {
	display: inline-flex;
	align-items: center;
	cursor: pointer;
	position: relative;
}

.ast-switch ~ label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 2px;
	display: block;
	width: 16px;
	height: 16px;
	border-radius: 50px;
	background: var( --ast-customizer-global-background );
	transition: all 0.2 ease-in;
}

.ast-section-toggle-icon {
	color: var( --ast-customizer-icon-color );
	cursor: pointer;
	line-height: 20px;
	height: 100%;
	display: inline-flex;
	align-items: center;
}

.ast-section-toggle-icon.active {
	color: var( --ast-customizer-primary-color );
}

.ast-section-toggle-icon span {
	font-size: 16px;
	line-height: 16px;
	width: 16px;
	height: 16px;
}

.customize-control-ast-section-toggle {
	padding-top: 25px;
	padding-bottom: 16px;
}

li[ID*="accordion-section-ast-dynamic-single-"].control-section-ast_section,
li[ID*="accordion-section-ast-dynamic-archive-"].control-section-ast_section,
li#accordion-section-section-search-page-title.control-section-ast_section,
li[ID*="accordion-section-ast-sub-section-"].control-section-ast_section {
	display: none !important;
}

.item-customizer-focus svg {
	min-width: 12px;
	min-height: 12px;
	width: 12px;
	height: 12px;
	fill: #fff;
	margin-left: -1px;
}

/* Customizer - Adjusting Style Guide Trigger Button */
.wp-full-overlay-sidebar #customize-header-actions {
	padding-left: 6px;
	padding-right: 6px;
}

#customize-header-actions button#astra-tour {
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 48px;
	width: 45px;
	margin-top: 0 !important;
	padding: 0;
	background: #f0f0f1;
	border: none;
	border-radius: 0;
	border-top: 4px solid #f0f0f1;
	border-right: 1px solid #dcdcde;
	color: #3c434a;
	fill: #3c434a;
	stroke: #3c434a;
}

#customize-header-actions button#astra-tour:hover,
#customize-header-actions button#astra-tour:focus {
	background: #fff;
	color: #2271b1;
	fill: #2271b1;
	stroke: #2271b1;
	border-top-color: #2271b1;
	box-shadow: none;
	outline: 1px solid transparent;
}

#customize-header-actions button#astra-tour svg {
	margin-top: 7px;
	margin-left: 3px;
	width: 16px;
	height: 16px;
}

@media screen and ( max-width: 640px ) {
	#customize-header-actions button#astra-tour {
		left: 153px;
	}
}

/* To highlight style guide option */
.indicator-dot {
	z-index: 1000;
	position: relative;
	height: 5px;
	width: 5px;
	background-color: red;
	border-radius: 50%;
	display: inline-block;
	margin-left: 80px;
	margin-bottom: 15px;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0% {
		transform: scale( 1 );
		opacity: 1;
	}

	50% {
		transform: scale( 1.5 );
		opacity: 0.7;
	}

	100% {
		transform: scale( 1 );
		opacity: 1;
	}
}

/* Styling for the tooltip */
.ast-style-guide-tooltip {
	display: none;
	position: absolute;
	left: 50%;
	transform: translateX( -50% );
	margin-bottom: 5px;
	background-color: #e5e5e5;
	color: #494948;
	border-radius: 3px;
	white-space: nowrap;
	font-size: 12px;
	z-index: 1000;
	opacity: 0;
	transition: opacity 0.3s ease;
	padding: 0 8px;
	top: 45px;
	box-shadow: rgba( 0, 0, 0, 0.02 ) 0px 1px 3px 0px, rgba( 27, 31, 35, 0.15 ) 0px 0px 0px 1px;
}

/* Show the ast-style-guide-tooltip on hover */
#astra-tour:hover .ast-style-guide-tooltip {
	display: block;
	opacity: 1;
}

/* =================== New Customizer Design =================== */

/* General Layout */
.wp-ui-text-highlight {
	color: var( --ast-customizer-primary-color );
}

.wp-full-overlay-sidebar,
.expanded .wp-full-overlay-footer,
.wp-full-overlay-footer .devices {
	background-color: var( --ast-customizer-global-background );
}

.wp-full-overlay-footer .devices {
	box-shadow: none;
}

#customize-theme-controls .control-panel-themes > .accordion-section-title,
#customize-theme-controls .control-panel-themes > .accordion-section-title:hover {
	margin-bottom: 0;
}

/* Panels */
#customize-controls h3 {
	font-size: 13px;
}

#customize-controls .accordion-section-title,
#customize-outer-theme-controls .accordion-section-title {
	border-bottom: 1px solid var( --ast-customizer-border-color );
}

#customize-theme-controls .accordion-section-title button.accordion-trigger {
	padding: 14px 24px;
	line-height: 17px;
}

#customize-controls .accordion-section.control-section .accordion-section-title,
#customize-outer-theme-controls .accordion-section-title {
	border-left: none;
	border-color: var( --ast-customizer-border-color );
}

#customize-controls .control-section > .accordion-section-title,
#customize-controls .control-section .accordion-section-title button {
	color: var( --ast-customizer-input-text-color);
}

#customize-controls .control-section:hover > .accordion-section-title,
#customize-controls .control-section.open > .accordion-section-title,
#customize-controls .control-section .accordion-section-title button:hover,
#customize-controls .control-section .accordion-section-title button:focus {
	color: var( --ast-customizer-primary-color );
	background: var( --ast-customizer-secondary-color );
}

#customize-controls .control-section .accordion-section-title button:focus {
	box-shadow: 0 0 0 1px var( --ast-customizer-primary-color );
}

#customize-controls .accordion-section.control-section .accordion-section-title:after,
#customize-outer-theme-controls .accordion-section-title:after {
	font-size: 16px;
	color: var( --ast-customizer-icon-color );
	margin-right: 10px;
}

#customize-controls .accordion-section.control-section .accordion-section-title:hover:after,
#customize-outer-theme-controls .accordion-section-title:hover:after {
	color: var( --ast-customizer-primary-color );
}

#customize-controls .control-section .accordion-section-title:after,
#customize-controls .customize-pane-child .accordion-section-title:after {
	top: calc( 50% - 7px );
}

/* Tabs */
#customize-controls .ahfb-compontent-tabs {
	background: var( --ast-customizer-secondary-color );
	margin-left: -12px;
	margin-right: -12px;
	padding-left: 5px;
	padding-right: 5px;
	border: none;
}

#customize-controls .control-section.open .ahfb-compontent-tabs {
	margin-left: -24px;
	margin-right: -24px;
}

#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button {
	margin: 10px 5px 0;
	padding: 19px 16px 16px;
	border-radius: var( --ast-customizer-tab-radius ) var( --ast-customizer-tab-radius ) 0 0;
	line-height: 1;
}

#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active {
	position: relative;
	background-color: var( --ast-customizer-global-background );
	border-bottom-color: var( --ast-customizer-global-background );
}

#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button:not( .nav-tab-active ):hover {
	background-color: transparent;
}

/* Active Tab Styling */
#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active::before,
#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active::after,
#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active span::before,
#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active span::after {
	content: "";
	position: absolute;
	width: var( --ast-customizer-tab-radius );
	height: var( --ast-customizer-tab-radius );
	bottom: -3px;
}

#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active::before,
#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active::after {
	background: var( --ast-customizer-global-background );
}

#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active span::before,
#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active span::after {
	background: var( --ast-customizer-secondary-color );
	z-index: 1;
}

#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active::before,
#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active span::before {
	left: calc( -1 * var( --ast-customizer-tab-radius ) );
}

#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active::after,
#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active span::after {
	right: calc( -1 * var( --ast-customizer-tab-radius ) );
}

#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active span::before {
	border-radius: 0 0 var( --ast-customizer-tab-radius ) 0;
}

#customize-controls .ahfb-compontent-tabs .ahfb-compontent-tabs-button.nav-tab-active span::after {
	border-radius: 0 0 0 var( --ast-customizer-tab-radius );
}

#customize-control-site_icon .site-icon-preview {
	width: auto;
}

#customize-control-astra-settings-breadcrumb-separator-selector svg {
	border-radius: 4px;
}

#accordion-section-astra-pro .wp-ui-highlight {
	background-color: var(--ast-customizer-border-color);
}

#customize-control-astra-settings-secondary-theme-button-bg-color-group, #customize-control-astra-settings-secondary-theme-button-border-color-group {
	margin-top: 8px;
}

.customize-control-ast-description .ast-description {
	color: var( --ast-customizer-input-text-color );
	font-size: 11px;
	line-height: 16px;
	font-weight: 400;
	text-wrap: wrap;
	display: inline-block;
}

.customize-control-ast-description .ast-description:not( .ast-customizer-notice ) {
	font-style: italic;
}

.customize-control-ast-description a.button {
	margin-top: 10px;
	font-style: normal;
}

.customize-control-ast-description p {
	margin-left: 12px;
	margin-top: 0;
	cursor: initial;
	font-size: 11px;
	line-height: 16px;
	font-weight: 400;
}

.customize-control .customize-control-description {
	display: none;
}

/* CSS for a Description as a Notice. */
.customize-control-ast-description .ast-description.ast-customizer-notice {
	display: flex;
	align-items: flex-start;
	gap: 5px;
	color: var( --ast-customizer-input-text-color );
	background: var( --ast-customizer-notice-background );
	border: 1px solid var( --ast-customizer-border-color );
	padding: 14px;
	border-radius: 4px;
}

.customize-control-ast-description .ast-description.ast-customizer-notice svg {
	color: var( --ast-customizer-primary-color );
}

.customize-control-ast-description .ast-description.ast-customizer-notice p {
	padding: 0;
	margin: 0;
}

.customize-control-ast-divider hr {
	margin-top: 0;
	margin-bottom: 0;
	border-bottom: none;
}

.customize-control-ast-divider .customize-control-title {
	font-size: 15px;
	margin: 1em 0 0.2em;
}

.customize-control-ast-divider .customize-control-suffix {
	color: var(--ast-customizer-primary-color);
	font-size: 12px;
	margin: 1em 0 0.2em;
}

.customize-control-ast-divider .customizer-text {
	margin-top: -10px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.ast-field-settings-modal .customize-control-ast-divider {
	background-color: #f5f5f5;
	display: block;
	padding: 6px 14px;
	margin: 0;
	border-width: 0.5px 0;
	border-style: solid;
	border-color: #dddddd;
	line-height: 1;
	margin-top: 16px;
}

.customize-control-ast-settings-group .ast-field-settings-modal .customize-control-ast-divider .customizer-text {
	margin-top: 0;
}

.customize-control-ast-settings-group .ast-field-settings-modal .customize-control-ast-divider .customize-control-title {
	font-weight: 500;
	letter-spacing: 1px;
	font-size: 11px;
	margin: 0;
	text-transform: uppercase;
}

/*
 * CSS for ast-divider attribute started.
 */
.ast-bottom-divider {
	border-bottom: 0.5px solid var(--ast-customizer-divider-color);
	padding-bottom: 16px;
}

.ast-top-divider {
	border-top: 0.5px solid var(--ast-customizer-divider-color);
	padding-top: 16px;
}

.ast-top-divider>label.ast-divider-title,
.ast-bottom-divider>label.ast-divider-title,
.customize-control-ast-color-group>.ast-divider-title {
	display: block;
	width: 100%;
	font-size: 13px;
	line-height: 16px;
	font-weight: 600;
}

.ast-top-divider .ast-control-wrap .astra-color-picker-wrap {
	top: 55px;
	background-color: transparent;
}

.ast-top-divider .ast-divider-title+.ast-control-wrap .astra-color-picker-wrap {
	top: 100px;
	background-color: transparent;
}

.ast-top-divider.customize-control-ast-select .ast-divider-title {
	margin-bottom: 24px;
}

/*
* CSS for ast-spacing attribute started.
*/
.customize-control.ast-no-spacing {
	margin: 0;
}

.customize-control.ast-bottom-spacing,
.ast-field-settings-modal .customize-control.ast-bottom-spacing {
	margin-bottom: 16px;
}

.customize-control.ast-top-spacing,
.ast-field-settings-modal .customize-control.ast-top-spacing {
	margin-top: 16px;
}

li.customize-control.ast-bottom-section-spacing {
	margin-bottom: 24px;
}

li.customize-control.ast-top-section-spacing {
	margin-top: 16px;
}

/*
* CSS for ast-divider attribute started.
*/

.customize-control.ast-top-dotted-divider,
#customize-control-astra-settings-edd-archive-variable-button-text,
#customize-control-astra-settings-header-account-logged-out-text,
#customize-control-astra-settings-shop-load-more-text,
#customize-control-astra-settings-single-product-shipping-text,
#customize-control-astra-settings-checkout-back-to-cart-button-text,
#customize-control-astra-settings-ast-header-retina-logo,
#customize-control-astra-settings-product-sale-percent-value,
#customize-control-astra-settings-header-account-create-menu-link,
#customize-control-astra-settings-woo-cart-button-text,
#customize-control-astra-settings-single-product-recently-viewed-text,
#customize-control-astra-settings-sticky-header-retina-logo {
	margin-top: 24px;
	padding-top: 24px;
	border-top: 0.5px dashed var(--ast-customizer-divider-color);
}

#customize-control-blogname,
#customize-control-blogdescription {
	margin-top: 16px;
}

#customize-control-astra-settings-product-sale-percent-value .ast-control-tooltip {
	top: 24px;
}

.customize-control.ast-bottom-dotted-divider,
#customize-control-astra-settings-header-account-logged-in-text {
	margin-bottom: 8px;
	padding-bottom: 24px;
	border-bottom: 0.5px dashed var(--ast-customizer-divider-color);
}

.customize-control.ast-bottom-dotted-divider .ast-adv-toggle-icon {
	bottom: 24px;
}

/*
* CSS for ast-section-divider attribute started.
*/

.customize-control.ast-top-section-divider,
.customize-control.ast-bottom-section-divider {
	position: relative;
}

.customize-control.ast-top-section-divider {
	padding-top: 16px;
	margin-top: 16px;
}

.customize-control.ast-bottom-section-divider {
	padding-bottom: 16px;
	margin-bottom: 16px;
}

.customize-control.ast-sub-top-divider,
.customize-control.ast-sub-top-dotted-divider {
	padding-top: 16px;
}

.customize-control.ast-sub-bottom-divider,
.customize-control.ast-sub-bottom-dotted-divider {
	padding-bottom: 16px;
}

.customize-control.ast-top-section-divider::before,
.customize-control.ast-bottom-section-divider::after {
	content: "";
	position: absolute;
	left: -100%;
	background-color: var(--ast-customizer-divider-color);
	width: 500%;
	height: 0.5px;
}

.customize-control.ast-sub-top-divider::before,
.customize-control.ast-sub-top-dotted-divider::before,
.customize-control.ast-top-section-divider::before {
	top: 0;
}

.customize-control.ast-sub-bottom-divider::after,
.customize-control.ast-sub-bottom-dotted-divider::after,
.customize-control.ast-bottom-section-divider::after {
	bottom: 0;
}

.customize-control.ast-sub-top-divider::before,
.customize-control.ast-sub-bottom-divider::after,
.customize-control.ast-sub-top-dotted-divider::before,
.customize-control.ast-sub-bottom-dotted-divider::after {
	content: "";
	border-top: 0.5px dashed var(--ast-customizer-divider-color);
	width: calc(100% - 32px);
	height: 0.5px;
	position: absolute;
	left: 16px;
}

.customize-control.ast-sub-top-divider::before,
.customize-control.ast-sub-bottom-divider::after {
	border-top-style: solid;
}

/*
* CSS for ast-first-control-spacing attribute started.
*/
[ID*="-create-menu-link"] .ahfb-builder-item-start {
	margin-bottom: 0;
}

.customize-control.ast-section-spacing {
	margin-top: 16px;
}

#customize-control-astra-settings-footer-create-menu-link,
#customize-control-astra-settings-footer-language-switcher-options,
#customize-control-astra-settings-header-mobile-menu-create-menu-link,
#customize-control-astra-settings-header-language-switcher-options,
#customize-control-astra-settings-site-icon-link,
[ID*="sub-accordion-section-section-hb-menu-"] [ID*="-create-menu-link"],
[ID*="customize-control-astra-settings-footer-button"][ID*="-text"] {
	margin-top: 24px;
}

#customize-control-body-font-variant,
#customize-control-headings-font-variant {
	margin-top: 0;
}

.new-divider {
	margin-bottom: 12px;
}

/* Sub controls divider support */
.ast-fields-wrap .customize-control.ast-bottom-section-divider::after,
.ast-fields-wrap .customize-control.ast-top-section-divider::before {
	width: 100%;
	left: 0px;
}

.ast-top-dotted-divider .ast-control-tooltip {
	top: 22px;
}

.ast-top-section-divider .ast-control-tooltip {
	top: 30px;
}

#customize-control-astra-settings-archive-post-content-style .ast-control-tooltip {
	top: 0;
}

#customize-control-astra-settings-archive-post-sidebar-layout .ast-control-tooltip,
#customize-control-astra-settings-single-post-social-sharing-icon-position .ast-control-tooltip {
	top: 15px;
}

.ast-field-settings-modal .customize-control.ast-top-dotted-divider {
	margin-top: 0;
}

#customize-control-astra-typography-presets {
	padding-bottom: 20px;
}

.ast-top-section-divider.header-builder-item {
	margin-top: 10px;
}

#customize-control-astra-settings-blog-filter-taxonomy-text-colors {
	margin-top: 0;
}
.ast-typo-presets {
	width: 100%;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	row-gap: 8px;
	column-gap: 8px;
	justify-content: space-between;
}

.ast-typo-presets .ast-typo-preset-item {
	padding: 0;
	border-radius: 4px;
	cursor: pointer;
	border: 1px solid var( --ast-customizer-border-color );
	height: 80px;
	text-align: center;
	width: 30%;
	position: relative;
	margin-bottom: 4px;
}

.ast-typo-presets .ast-typo-preset-item:hover {
	background: var( --ast-customizer-secondary-color );
}

.ast-typo-presets .ast-typo-preset-item svg {
	transform: scale( 1 );
	width: 100%;
	height: 100%;
	padding: 0;
	margin: 0px;
}

.ast-typo-presets .ast-typo-preset-item.active {
	border: 1px solid var( --ast-customizer-color-1 );
	background: var( --ast-customizer-color-9 );
}

@media screen and (max-width: 1600px) {
	.ast-typo-presets .ast-typo-preset-item {
		height: 75px;
	}
}

.customize-control-ast-typography-presets .customize-control-title {
	margin-bottom: 16px;
}

.customize-control-ast-typography-presets .ast-reset-btn-preset-wrap {
	top: -4px;
}

.ast-typo-preset-item .components-tooltip {
	margin-top: -8px;
}

.ast-typo-preset-item .components-tooltip::before {
	border-bottom: none !important;
	border-left-color: transparent !important;
	border-right-color: transparent !important;
	border-top-style: solid !important;
	border-top-color: #1e1e1e !important;
	margin-left: -10px;
	bottom: -8px;
	content: "";
	position: absolute;
	height: 0;
	width: 0;
	line-height: 0;
	border: 8px solid #ccc;
}

.ast-typo-preset-item .components-tooltip::after {
	border-bottom: none !important;
	border-left-color: transparent !important;
	border-right-color: transparent !important;
	border-top-style: solid !important;
	border-top-color: #1e1e1e !important;
	margin-left: -10px;
	bottom: -6px;
	content: "";
	position: absolute;
	height: 0;
	width: 0;
	line-height: 0;
	border: 8px solid #fff;
}

.ast-font-styling {
	display: flex;
	justify-content: space-between;
	gap: 15px;
	align-items: center;
	padding-bottom: 16px;
	border-bottom: 0.5px solid var( --ast-customizer-divider-color );
}

.ast-font-extras-wrapper,
.ast-font-spacing-wrapper {
	display: flex;
	align-items: center;
	gap: 8px;
}

.ast-font-extras-wrapper .ast-font-extras-input,
.ast-font-spacing-wrapper .ast-font-spacing-input {
	position: relative;
	display: flex;
	align-items: center;
	background: #fff;
	max-width: 72px;
	width: 100%;
	border: 1px solid #d1d5db;
	border-radius: 3px;
	padding: 0 3px;
}

.ast-font-extras-wrapper .ast-font-extras-input input,
.ast-font-spacing-wrapper .ast-font-spacing-input input {
	padding: 0;
	border: none;
	min-height: 32px;
	border-radius: 3px;
	border: none !important;
	box-shadow: none !important;
	text-align: center;
	padding: 0 5px;
}

.ast-font-extras-wrapper .ast-font-extras-input input[type="number"],
.ast-font-spacing-wrapper .ast-font-spacing-input input[type="number"] {
	-moz-appearance: textfield;
}

.ast-font-extras-input:hover,
.ast-font-spacing-input:hover {
	border-color: var( --ast-customizer-color-2 );
}

.ast-font-extras-wrapper .ast-font-extras-input svg,
.ast-font-spacing-wrapper .ast-font-spacing-input svg {
	width: 25px;
	height: 28px;
}

.ast-font-extras-input .plus-minus-control .dashicons,
.ast-font-spacing-input .plus-minus-control .dashicons {
	font-size: 9px;
	color: #9ca3af;
	width: 10px;
	height: 10px;
	display: inherit;
	cursor: pointer;
}

div .ast-field-settings-modal .customize-control-ast-font-extras {
	margin-top: 15px;
}

.ast-font-extras-input input[type="number"]::-webkit-inner-spin-button,
.ast-font-extras-input input[type="number"]::-webkit-outer-spin-button,
.ast-font-spacing-input input[type="number"]::-webkit-inner-spin-button,
.ast-font-spacing-input input[type="number"]::-webkit-outer-spin-button {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

.ast-font-unit-wrapper {
	display: flex;
	gap: 4px;
	font-size: 10px;
	color: var( --ast-customizer-sub-text-color );
}

.ast-font-unit-wrapper span {
	cursor: pointer;
}

.ast-font-unit-wrapper span:hover,
.ast-font-unit-wrapper .active {
	color: var( --ast-customizer-primary-color );
}

.ast-font-styling-second {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 10px;
	padding-top: 16px;
}

.ast-font-transform-wrapper,
.ast-font-decoration-wrapper {
	display: flex;
	align-items: center;
	flex: 1;
}

.ast-font-transform-wrapper div,
.ast-font-decoration-wrapper div {
	padding: 8px 0;
	width: 33%;
	text-align: center;
	font-weight: 600;
	font-size: 12px;
	line-height: 16px;
	color: #334155;
}

.ast-font-transform-wrapper div.active,
.ast-font-decoration-wrapper div.active {
	background-color: #0284c7;
	color: #fff;
	cursor: pointer;
}

.ast-font-item-type {
	border: 1px solid #d1d5db;
}

.ast-font-item-type:hover {
	background-color: #0284c7;
	color: #fff;
	cursor: pointer;
}

.ast-font-item-type:first-child {
	border: 1px solid #d1d5db;
	border-right: none;
	border-radius: 3px 0 0 3px;
}

.ast-font-item-type:last-child {
	border: 1px solid #d1d5db;
	border-left: none;
	border-radius: 0 3px 3px 0;
}

.ast-font-item-type.ast-font-deco-underline {
	text-decoration: underline;
}

.ast-font-item-type.ast-font-deco-linethrough {
	text-decoration: line-through;
}

.customize-control-ast-radio-image .modern-layout label:hover svg,
.components-button-group.ahfb-radio-container-control label:hover svg, .input-wrapper.ast-alignment-wrapper .ast-alignment-inner-wrap:hover {
	background-color: var( --ast-customizer-color-13);
}

.ast-customizer-font-varient-wrap {
	margin-top: 12px;
}

.ast-customizer-font-varient-wrap .ast-multi-select__value-container {
	overflow-x: hidden;
	flex-wrap: nowrap;
	padding: 2px 5px;
}

.ast-customizer-font-varient-wrap .ast-multi-select__multi-value {
	margin-top: 0;
	margin-bottom: 0;
	font-size: 12px;
	padding: 2px 2px 2px 2px;
	min-width: auto;
	color: #4b5563;
	background-color: #f5f5f5;
	border: 1px solid #d1d5db;
	border-radius: 2px;
	padding-left: 5px;
}

.ast-customizer-font-varient-wrap .ast-multi-select__multi-value .ast-multi-select__multi-value__label {
	line-height: normal;
}

.ast-customizer-font-varient-wrap .ast-multi-select__multi-value .ast-multi-select__multi-value__remove {
	color: #4b5563;
	padding-right: 0;
}

.ast-customizer-font-varient-wrap .ast-multi-select__multi-value .ast-multi-select__multi-value__remove:hover {
	background-color: transparent;
	color: #4b5563;
}

.ast-customizer-font-varient-wrap .ast-multi-select__input-container input:focus {
	box-shadow: none;
}

.ast-customizer-font-varient-wrap .ast-multi-select__control {
	position: relative;
	max-height: 32px;
	display: inline-flex;
	width: 100%;
	align-content: center;
}

.ast-customizer-font-varient-wrap .ast-multi-select__control:after {
	content: "\f11c";
	font-family: dashicons;
	font-size: 16px;
	margin-right: 7px;
	pointer-events: none;
	z-index: 1;
}

.ast-customizer-font-varient-wrap .ast-multi-select__control:before {
	content: "";
	position: absolute;
	top: 3px;
	right: 3px;
	width: 50px;
	height: 25px;
	background: rgb( 247, 247, 247 );
	background: linear-gradient( 90deg, rgba( 247, 247, 247, 0 ) 0%, rgba( 255, 255, 255, 1 ) 50%, rgba( 255, 255, 255, 1 ) 100% );
	pointer-events: none;
	z-index: 1;
}

#customize-control-astra-settings-ast-headings-font-settings .ast-fields-wrap,
#customize-control-astra-settings-ast-body-font-settings .ast-fields-wrap {
	overflow: visible;
}

/**
 * Hide normal font weight from font weight dropdown and display only if its set as font weight.
 */
.customize-control-ast-font select option[value="normal"],
.customize-control-ast-font select[data-value="normal"] option[value="400"] {
	display: none;
}

.customize-control-ast-font select[data-value="normal"] option[value="normal"] {
	display: block;
}

span.customize-control-title.ast-group-section-title {
	margin-bottom: 0;
	padding: 5px 0;
	font-weight: 600;
}

.ast-top-divider.customize-control-ast-group-title .ast-reset-btn-preset-wrap {
	top: 24px;
}

.customize-control-ast-group-title .ast-responsive-btns {
	margin-left: 5px;
}

li#customize-control-astra-settings-header-preset-style .customizer-text {
	font-weight: 600;
}

.ast-header-preset-container {
	max-width: 100%;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	padding: 16px;
	gap: 12px;
}

.ast-header-preset-item svg {
	display: block;
	width: 100%;
	border-radius: 5px;
}

.ast-header-preset-item svg,
.ast-header-preset-item svg * {
	color: var( --ast-customizer-border-color );
	transition: color 0.3s ease;
}

.ast-header-preset-item:hover svg,
.ast-header-preset-item:hover svg * {
	color: var( --ast-customizer-primary-color );
}

.ast-header-preset-item:hover svg rect {
}

.ast-header-preset-item {
	cursor: pointer;
}

#customize-control-astra-settings-header-preset-style .ast-field-settings-modal {
	display: none;
	padding: 0;
	width: calc( 100% + 16px );
	margin-left: -8px;
}

#customize-control-astra-settings-header-preset-style .ast-adv-toggle-icon {
	top: -5px;
}

.ast-field-settings-wrap .customize-control-ast-heading {
	margin: 0px 0 15px 0;
}

.customize-control-ast-heading {
	margin-top: 24px;
	margin-bottom: 0;
}

.customize-control-ast-heading.ast-top-section-divider,
.customize-control:has( .ast-divider-title ).ast-top-section-divider
 {
	margin-top: 16px;
	padding-top: 16px;
}

.customize-control-ast-heading .customize-control-title {
	display: block;
	margin: 0;
	background-color: var( --ast-customizer-global-background );
	color: var( --ast-customizer-text-color );
	font-size: 13px;
	line-height: 18px;
	font-weight: 600;
}

.customize-control-ast-heading .customize-control-caption {
	top: 50%;
	transform: translateY( -50% );
	position: absolute;
	right: 0;
	font-size: 11px;
	font-weight: 500;
	letter-spacing: 1px;
}

.customize-control-ast-heading .customize-control-description {
	margin-top: 10px;
}

.ast-heading-wrapper label {
	cursor: default;
}

.customize-control > label {
	cursor: default;
}

.customize-control .ast-spacing-input-item-link,
.customize-control .ast-border-input-item-link {
	cursor: pointer;
}

.customize-control.customize-control-ast-slider label {
	cursor: pointer;
}

.customize-control.customize-control-ast-slider label .wrapper input[type="range"] {
	cursor: pointer;
}

.customize-control .ast-control-reduce-top-space {
	margin-top: -15px;
}

.customize-control-ast-input-with-dropdown .ast-input-with-dropdown-wrapper {
	position: relative;
}

.customize-control-ast-input-with-dropdown .components-dropdown-menu {
	position: absolute;
	right: 8px;
	top: 50%;
	translate: 0 -50%;
}

.customize-control-ast-input-with-dropdown .components-dropdown-menu .components-dropdown-menu__toggle {
	width: 17px;
	height: 17px;
	min-width: 17px;
	padding: 0;
}

.customize-control-ast-input-with-dropdown .components-button:focus:not( :disabled ) {
	box-shadow: none;
	outline: none;
}

.customize-control-ast-input-with-dropdown .components-popover__content {
	min-width: 170px;
}

.customize-control-ast-input-with-dropdown .components-popover__content .components-button:not( :disabled ):not( [aria-disabled="true"] ):not( .is-secondary ):not( .is-primary ):not( .is-tertiary ):not( .is-link ):not( .components-color-picker__saturation-pointer ):hover {
	color: var( --wp-admin-theme-color );
}

.customize-control-ast-input-with-dropdown .ast-input-with-dropdown-wrapper input.components-text-control__input {
	padding-right: 37px;
	min-height: 30px;
}

.components-popover__fallback-container 
.components-popover__content 
.components-dropdown-menu__menu-item {
	height: 30px;
}

.ast-link-open-in-new-tab-wrapper {
	margin: 16px 0 0;
	border-top: 0.5px solid var( --ast-customizer-color-7 );
}

.ast-link-label-wrapper {
	margin: 16px 0;
	padding-top: 16px;
	border-top: 0.5px solid var( --ast-customizer-color-7 );
}

.ast-logo-svg-icon-btn-wrapper {
	display: flex;
}

.ast-logo-svg-icon-btn-wrapper .ast-logo-svg-icon-btn {
	flex: 1 1 auto;
	display: flex;
}

.ast-logo-svg-icon-btn-wrapper .ast-logo-svg-icon-btn:first-child .components-button {
	border-radius: 3px 0 0 3px;
}

.ast-logo-svg-icon-btn-wrapper .ast-logo-svg-icon-btn .components-button {
	border-radius: unset;
	font-size: 12px;
	padding: 9px 0 10px;
	white-space: nowrap;
	color: var( --ast-customizer-color-5 );
	text-decoration: none;
	text-shadow: none;
	outline: unset;
	border: 1px solid var( --ast-customizer-color-7 );
	width: 100%;
	height: 100%;
	text-align: center;
	justify-content: center;
	border-radius: unset;
	transition: none;
	font-weight: normal;
}

.ast-logo-svg-icon-btn-wrapper .ast-logo-svg-icon-btn button.components-button.is-primary {
	border-color: var( --ast-customizer-color-3 );
}

.ast-logo-svg-icon-btn-wrapper .ast-logo-svg-icon-btn:last-child .components-button {
	border-radius: 0 3px 3px 0;
}

.ast-logo-svg-icon-btn-wrapper .ast-logo-svg-icon-btn .components-button.is-primary {
	color: var( --ast-customizer-color-10 );
	background: var( --ast-customizer-color-1 );
}

.ast-logo-svg-icon-btn-wrapper .ast-logo-svg-icon-btn .components-button:focus:not( :disabled ) {
	box-shadow: none;
}

.ast-logo-svg-icon-btn-wrapper .ast-logo-svg-icon-btn .components-button:not( .is-primary ):hover {
	color: var( --ast-customizer-color-5 ) !important;
}

/* Modal stylings starts */

.ast-logo-svg-icon-icon-library-default .ast-logo-svg-icon-icon-library-default-icon-selected p {
	text-align: center;
	border: 1px dashed #c3c4c7;
	padding: 10px;
}

.ast-logo-svg-icon-icon-library-default .ast-logo-svg-icon-icon-library-default-icon-selected svg {
	width: 50px;
}

.ast-logo-svg-icon-icon-library-default .ast-logo-svg-icon-icon-library-default-icon-selected .btn-group-icon-selected {
	display: flex;
	gap: 10px;
}

.ast-logo-svg-icon-icon-library-default .btn-select-icon {
	margin-top: 10px;
	cursor: pointer;
	background-color: var( --ast-customizer-color-10 );
	color: var( --ast-customizer-primary-color );
	width: 100%;
	position: relative;
	text-align: center;
	border: 1px dashed #c3c4c7;
	box-sizing: border-box;
	padding: 9px 0;
	line-height: 1.6;
	border-radius: 4px;
}

.ast-logo-svg-icon-icon-library-default .btn-select-icon:hover {
	background-color: var( --ast-customizer-color-13 );
}

.ast-logo-svg-icon-icon-library-modal {
	width: 65%;
	max-height: 85%;
}

.ast-logo-svg-icon-icon-library-header {
	display: flex;
	align-items: baseline;
	height: 65px;
}

.ast-logo-svg-icon-icon-library-header h2 {
	width: 150px;
	margin: 0;
	font-size: 20px;
	font-weight: 400;
	color: #1e1e1e;
}

.ast-logo-svg-icon-search-container {
	flex: 1;
	padding-left: 24px;
}

.ast-logo-svg-icon-search-bar {
	position: relative;
}

.ast-logo-svg-icon-search-bar svg {
	width: 14px;
	height: 14px;
	position: absolute;
	top: calc( 18px - 7px );
	color: #555d66;
	fill: #555d66;
	transform: rotateZ( 90deg );
	left: 12px;
}

.ast-logo-svg-icon-search-bar input[type="text"] {
	height: 36px;
	width: 100%;
	padding: 7px 12px 7px calc( 12px * 2 + 14px );
	border: 1px solid #e6e7e9;
	margin: 0;
	border-radius: 2px;
	color: #1d2327;
}

.ast-logo-svg-icon-icon-library-container .components-flex .svg-icons-list-flexbox .svg-icons-list {
	height: 35em;
	overflow: hidden scroll;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-categories button {
	border: none;
	cursor: pointer;
	padding: 15px 0px 15px 10px;
	background: none;
	width: 100%;
	text-align: left;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-categories button.selected,
.ast-logo-svg-icon-icon-library-container .svg-icons-categories button:hover {
	background: #dcf2ff;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-list {
	display: grid;
	padding: 5px 15px;
	grid-template-columns: repeat( auto-fill, minmax( 100px, 1fr ) );
	grid-gap: 10px;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-list .icon-not-available {
	display: flex;
	gap: 10px;
	align-items: center;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-list .icon-not-available svg {
	width: 18px;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-list .icon-not-available span {
	font-size: 18px;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-list .svg-icon {
	padding: 15px;
	background-color: #ffffff;
	border: 1px solid #e6e7e9;
	border-radius: 3px;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	gap: 10px;
	height: 7em;
	align-items: center;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-list .svg-icon.selected {
	outline: 2px solid;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-list .svg-icon:hover:not( .selected ) svg {
	transform: scale( 1.55 );
	transition: transform 0.2s;
}

.ast-logo-svg-icon-icon-library-container .svg-icons-list .svg-icon svg {
	width: 20px;
	height: 20px;
}

.components-modal__frame.ast-logo-svg-icon-icon-library-modal .components-modal__content .ast-logo-svg-icon-icon-library-footer {
	text-align: right;
	display: flex;
	align-items: flex-end;
	justify-content: flex-end;
	gap: 10px;
}

.components-modal__frame.ast-logo-svg-icon-icon-library-modal .components-modal__content .ast-logo-svg-icon-icon-library-footer > button {
	font-size: 13px;
	border: none;
	border-radius: 2px;
	width: 89px;
	height: 36px;
	cursor: pointer;
	display: flex;
	justify-content: center;
}

.components-modal__frame.ast-logo-svg-icon-icon-library-modal .components-modal__content {
	padding: 30px 24px;
	margin: 0;
}

.components-modal__frame.ast-logo-svg-icon-icon-library-modal .components-modal__content .components-modal__header {
	display: none;
}

/* Modal stylings ends */

.ast-logo-svg-icon-element-custom {
	margin-top: 10px;
}

.ast-logo-svg-icon-element-custom textarea {
	width: 100%;
	height: 10em;
}

.customize-control-ast-multi-selector .input-wrapper {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.customize-control-ast-multi-selector .ast-multiselector-inner-wrap {
	display: flex;
	justify-content: center;
	flex: 1 1 0;
	background: var( --ast-customizer-color-9 );
	border: 1px solid var( --ast-customizer-color-7 );
	padding: 4px;
	cursor: pointer;
	--ast-customizer-multiselect-icon-color: var( --ast-customizer-sub-text-color );
	margin-left: -1px;
}

.customize-control-ast-multi-selector .ast-multiselector-inner-wrap.active {
	--ast-customizer-multiselect-icon-color: var( --ast-customizer-secondary-text-color );
	background: var( --ast-customizer-color-1 );
	border-color: var( --ast-customizer-color-3 );
	z-index: 1;
}

.customize-control-ast-multi-selector .ast-multiselector-inner-wrap.active svg path {
	stroke: var( --ast-customizer-color-10 );
}

.customize-control-ast-multi-selector .ast-multiselector-inner-wrap.active svg rect {
	fill: var( --ast-customizer-color-10 );
}

.customize-control-ast-multi-selector .ast-multiselector-inner-wrap svg {
	fill: transparent;
}

.ast-multiselector-inner-wrap .ahfb-icon-set {
	height: 22px;
}

.customize-control-ast-multi-selector .ast-multiselector-inner-wrap:last-child {
	border-radius: 0px 3px 3px 0px;
}

.customize-control-ast-multi-selector .ast-multiselector-inner-wrap:first-child {
	border-radius: 3px 0px 0px 3px;
}

.ast-multiselector-inner-wrap:hover {
    background: var(--ast-customizer-color-11);
}

.customize-control-ast-multi-selector .ast-multiselector-inner-wrap span[label] {
	color: var( --ast-customizer-sub-text-color );
	text-align: center;
}

.customize-control-ast-multi-selector .ast-multiselector-inner-wrap.active span[label] {
	color: var( --ast-customizer-multiselect-icon-color );
}

.ast-alignment-wrapper.stack-after-2 .ast-multiselector-inner-wrap {
	flex: 0 0 calc( 50% - 10px );
	margin: 5px;
}

.ast-alignment-wrapper.stack-after-2 .ast-multiselector-inner-wrap:nth-child( 2n ) {
	margin-right: 0;
}

.ast-alignment-wrapper.stack-after-3 .ast-multiselector-inner-wrap {
	flex: 0 0 calc( 33.33% - 10px );
	margin: 5px;
}

.ast-alignment-wrapper.stack-after-3 .ast-multiselector-inner-wrap:nth-child( 3n ) {
	margin-right: 0;
}

[CLASS*="stack-after-"].ast-alignment-wrapper {
	margin-left: -5px;
	margin-right: -5px;
	display: flex;
	flex-wrap: wrap;
}

[CLASS*="stack-after-"].ast-alignment-wrapper .ast-multiselector-inner-wrap {
	margin: 5px;
	align-items: center;
	padding: 5px;
	height: 30px;
	border-radius: 0;
}

.ast-alignment-wrapper.stack-after-2 .ast-multiselector-inner-wrap {
	flex: 0 0 calc( 45% - 10px );
}

.ast-alignment-wrapper.stack-after-3 .ast-multiselector-inner-wrap {
	flex: 0 0 calc( 28% - 10px );
}

.ast-multiselect-group-wrapper .ast-field-settings-wrap .ast-field-settings-modal {
	margin-top: 10px;
}

.ast-multiselect-group-wrapper .ast-multiselect {
	display: flex;
	justify-content: space-between;
	align-items: center;
	min-height: 32px;
	color: var( --ast-customizer-color-5 );
	padding: 0 8px;
	margin: 0;
	border: 1px solid var( --ast-customizer-color-7 );
	border-radius: 3px;
	background-size: 16px 16px;
	font-size: 13px;
	line-height: 16px;
	vertical-align: middle;
	cursor: pointer;
	transition: all 0.25s ease-in-out;
}

.ast-multiselect-group-wrapper .ast-multiselect.open {
	border-color: var( --ast-customizer-color-1 );
}

.ast-multiselect-group-wrapper .ast-multiselect .ast-msb-right {
	display: inline-flex;
	align-items: center;
	gap: 4px;
}

.ast-multiselect-group-wrapper .ast-multiselect button.icon {
	width: auto;
	height: auto;
	padding: 0;
	transition: all 0.25s ease-in-out;
	outline: none;
}

.ast-multiselect-group-wrapper .ast-multiselect button.icon:focus {
	box-shadow: none;
}

.ast-multiselect-group-wrapper .ast-multiselect button.icon svg {
	fill: revert-layer;
	width: auto;
	height: auto;
}

.ast-multiselect-group-wrapper .ast-multiselect.open button.icon.expand,
.ast-multiselect-group-wrapper .ast-multiselect:not( .open ) button.icon.close {
	display: none;
}

.ast-multiselect-group-wrapper .ast-multiselect.open button.icon.close,
.ast-multiselect-group-wrapper .ast-multiselect:not( .open ) button.icon.expand {
	display: inline-flex;
}

.ast-multiselect-group-wrapper .ast-multiselect button.icon.close:hover {
	background-color: var( --ast-customizer-color-7 );
}

.ast-multiselect-group-wrapper .ast-multiselect .ast-msb-selected {
	display: inline-flex;
	background: var( --ast-customizer-color-1 );
	border-radius: 4px;
}

.ast-multiselect-group-wrapper .ast-multiselect .ast-msb-selected > * {
	display: inline-flex;
	min-width: 21px;
	height: 21px;
	justify-content: center;
	align-items: center;
	color: var( --ast-customizer-color-9 );
}

.ast-multiselect-group-wrapper .ast-multiselect .ast-msb-selected .icon.remove rect {
	fill: var( --ast-customizer-color-9 );
}

.ast-multiselect-group-wrapper .ast-field-settings-modal .ast-fields-wrap {
	overflow: visible;
}

.ast-multiselect-group-wrapper .ast-field-settings-modal .customize-control.customize-control-ast-toggle-control {
	padding: 0;
	margin: 0;
}

.ast-multiselect-group-wrapper .ast-field-settings-modal .customize-control-ast-toggle-control .ast-form-toggle {
	position: absolute;
	left: 12px;
	margin-left: 0;
}

.ast-multiselect-group-wrapper .ast-field-settings-modal .customize-control-ast-toggle-control .components-toggle-control__label {
	padding: 12px 12px 12px 36px;
	transition: all 0.25s ease-in-out;
}

.ast-multiselect-group-wrapper .ast-field-settings-modal .customize-control-ast-toggle-control .components-toggle-control__label:hover {
	background-color: var( --ast-customizer-color-13 );
}

.ast-multiselect-group-wrapper .ast-field-settings-modal .customize-control:first-child .components-toggle-control__label {
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
}

.ast-multiselect-group-wrapper .ast-field-settings-modal .customize-control:last-child .components-toggle-control__label {
	border-end-start-radius: 3px;
	border-end-end-radius: 3px;
}

.ast-multiselect-group-wrapper .customize-control-ast-toggle-control .ast-control-tooltip {
	top: 50%;
	right: 8px;
}

.ast-multiselect-group-wrapper .customize-control-ast-toggle-control .dashicons-editor-help[data-title] + .ast-tooltip[data-title]::before {
	right: 6px;
}

.customize-control-ast-number .ast-number-single {
	display: flex;
	border: 1px solid var( --ast-customizer-color-7 );
	border-radius: 3px;
	background-color: var( --ast-customizer-color-9 );
	width: 96px;
}

.customize-control-ast-number .ast-number-single .plus {
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
	border-top-left-radius: 0px;
	border-bottom-left-radius: 0px;
}

.customize-control-ast-number .ast-number-single .minus {
	border-top-right-radius: 0px;
	border-bottom-right-radius: 0px;
	border-top-left-radius: 2px;
	border-bottom-left-radius: 2px;
}

.customize-control-ast-number .ast-number-single .minus:hover{
    background-color: var(--ast-customizer-color-1);
}

.customize-control-ast-number .ast-number-single .plus:hover{
    background-color: var(--ast-customizer-color-1);
}

.customize-control-ast-number .ast-number-single .minus:hover .dashicon.dashicons {
    color: #fff;
}

.customize-control-ast-number .ast-number-single .plus:hover .dashicon.dashicons {
    color: #fff;
}

.customize-control-ast-number .ast-number-single .components-input-control {
	flex-grow: 1;
}

.customize-control-ast-number .components-input-control__container,
.customize-control-ast-number .components-input-control__input {
	text-align: center;
}

.customize-control-ast-number .components-input-control__container input.components-input-control__input {
	height: 26px;
	min-height: 26px;
}

.customize-control-ast-number .ast-number-single button {
	all: initial;
}

.customize-control-ast-number .ast-number-single button:active .dashicons,
.customize-control-ast-number .ast-number-single button:focus .dashicons {
	color: var( --ast-customizer-color-10 );
	background-color: var( --ast-customizer-color-1 );
}

.customize-control-ast-number .ast-number-single:has( :focus ) {
	border-color: var( --ast-customizer-color-1 );
}

.customize-control-ast-number .ast-number-single .dashicon.dashicons {
	display: flex;
	align-items: center;
	justify-content: center;
	color: var( --ast-customizer-color-8 );
	font-size: 10px;
	padding: 0.3em 0.35em;
	cursor: pointer;
}

.customize-control-ast-number .components-input-control__backdrop {
	border: none !important;
	box-shadow: none !important;
}

.customize-control-ast-number .ast-number-single input[type="number"]::-webkit-inner-spin-button,
.customize-control-ast-number .ast-number-single input[type="number"]::-webkit-outer-spin-button {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	margin: 0;
}

.customize-control-ast-number .ast-control-wrapper {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.customize-control-ast-number .ast-title-wrapper {
	display: flex;
	align-items: center;
	width: 50%;
}

.customize-control-ast-number .ast-number-single {
	display: none;
}

.customize-control-ast-number .ast-number-single.active {
	display: flex;
}

.ast-number-single.active:hover {
    border-color: var(--ast-customizer-color-1);
}

.customize-control-ast-number .ast-responsive-btns {
	margin-left: 0.5em;
}

/* Hide spin buttons for WebKit (Chrome, Safari) */
.customize-control-ast-number input[type="number"]::-webkit-inner-spin-button,
.customize-control-ast-number input[type="number"]::-webkit-outer-spin-button {
	-webkit-appearance: none;
	appearance: none;
	margin: 0;
	/* Adjust as needed */
}

/* Hide spin buttons for Firefox */
.customize-control-ast-number input[type="number"] {
	-moz-appearance: textfield;
	text-align: center;
}

/* Hide spin buttons for Edge & IE */
.customize-control-ast-number input[type="number"]::-webkit-outer-spin-button,
.customize-control-ast-number input[type="number"]::-webkit-inner-spin-button,
.customize-control-ast-number input[type="number"]::-ms-clear,
.customize-control-ast-number input[type="number"]::-ms-reveal {
	display: none;
}

.customize-control-ast-radio-icon .ast-control-wrap {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: flex-start;
}

.customize-control-ast-radio-icon .customize-control-title {
	flex: 1;
	padding-right: 0.5em;
}

.customize-control-ast-radio-icon .customize-control-title {
	margin-top: 8px;
	margin-bottom: 8px;
}

.customize-control-ast-radio-icon .ast-options {
	background-color: transparent;
	margin: 0;
	line-height: 0;
}

.customize-control-ast-radio-icon .ast-options li {
	display: inline-block;
	position: relative;
	background-color: transparent;
	width: 42px;
	height: 28px;
	line-height: 28px;
	margin-bottom: 0;
	border: 1px solid var( --ast-customizer-color-7 );
	color: var( --ast-customizer-color-8 );
	cursor: pointer;
}

.customize-control-ast-radio-icon .ast-options li .dashicon {
	text-align: center;
	line-height: 28px;
	width: 100%;
	font-size: 16px;
}

.customize-control-ast-radio-icon .ast-options li {
	margin-left: -1px;
}

.customize-control-ast-radio-icon .ast-options li:nth-child( 1 ) {
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
	margin-left: 0;
}

.customize-control-ast-radio-icon .ast-options li:nth-last-child( 1 ) {
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
}

.ast-single-option:hover {
    background: var(--ast-customizer-color-13);
}

.customize-control-ast-radio-icon .ast-options li.active {
	background-color: var( --ast-customizer-color-1 );
	color: var( --ast-customizer-color-10 );
	border-color: var( --ast-customizer-color-1 );
	margin-right: 1px;
}

.customize-control-ast-row-layout .ast-responsive-btns {
	position: relative;
	top: 1px;
}

.customize-control-ast-radio-image label {
	position: relative;
	display: inline-block;
}

.ast-divide-four label {
	margin-right: 0;
}

.customize-control-ast-radio-image input {
	display: none;
}

#customize-control-astra-settings-header-trigger-icon input[type="radio"]:checked + label .ahfb-svg-iconset svg rect {
	fill: var( --ast-customizer-primary-color );
}

#customize-control-astra-settings-header-trigger-icon input[type="radio"]:checked + label .ahfb-svg-iconset svg rect:first-child {
	stroke: var( --ast-customizer-primary-color );
}

#customize-control-astra-settings-header-trigger-icon input[type="radio"]:checked + label .ahfb-svg-iconset svg rect:not( :first-child ) {
	fill: var( --ast-customizer-secondary-text-color );
}

.customize-control-ast-radio-image input:checked + label svg path {
	color: var( --ast-customizer-color-10 );
}

.customize-control-ast-radio-image .modern-layout input:checked + label svg,
.components-button-group.ahfb-radio-container-control .active-radio.ast-radio-img-svg svg {
	background-color: var( --ast-customizer-color-1 );
	border: 1px;
	border-color: transparent;
	border-bottom-color: #f0f0f1;
}

.ast-divide-four input:checked + label svg {
	box-shadow: none;
	background: var( --ast-customizer-primary-color );
}

.customize-control-ast-radio-image input + label .image-clickable {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100%;
}

.customize-control-ast-radio-image:not( #customize-control-astra-settings-header-trigger-icon ) > .image {
	display: grid;
	grid-template-columns: repeat( 3, 1fr );
	grid-column-gap: 5px;
	grid-row-gap: 5px;
}

.customize-control-ast-radio-image:not( #customize-control-astra-settings-header-trigger-icon ) > .modern-layout,
.components-button-group.ahfb-radio-container-control {
	display: grid;
	grid-template-columns: repeat( 2, 1fr );
	grid-column-gap: 23px;
	grid-row-gap: 20px;
}

.customize-control-ast-radio-image:not( #customize-control-astra-settings-header-trigger-icon ) > .ast-divide-four {
	grid-template-columns: repeat( 4, 1fr );
	grid-column-gap: 14px;
	grid-row-gap: 10px;
}

.customize-control-ast-radio-image .ast-radio-img-svg svg {
	width: 75px;
	height: 50px;
}

.customize-control-ast-radio-image .modern-layout .ast-radio-img-svg svg,
.components-button-group.ahfb-radio-container-control .ast-radio-img-svg svg {
	margin: 0;
	width: 100%;
	height: auto;
	border-radius: 2px;
	cursor: pointer;
	transition: background 0.15s ease, box-shadow 0.15s ease;
}

.ast-divide-four .ast-radio-img-svg svg {
	color: var( --ast-customizer-icon-color );
	width: 90%;
	height: 93%;
}

.ast-divide-four .ast-radio-img-svg svg path {
	color: var( --ast-customizer-color-8 );
}

.ast-divide-four input:checked + label:hover svg {
    background-color: var(--ast-customizer-color-1);
}

.ast-divide-four input + label:hover svg {
    background-color: var( --ast-customizer-color-11 );
}

.ast-divide-four input:checked + label svg rect {
    stroke: var(--ast-customizer-color-1);
}

/* don't show empty tooltips */
.image-clickable[data-title=""]::after {
	display: none !important;
}

.ast-upgrade-trigger:hover .image-clickable[data-title]::after {
	display: block;
}

.customize-control-ast-radio-image.ast-inline {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.customize-control-ast-radio-image.ast-inline .customize-control-title {
	margin-bottom: 0;
}

.customize-control-ast-radio-image.ast-inline .ast-divide-four {
	display: inline-flex;
	gap: 8px;
	height: 32px;
}

.customize-control-ast-radio-image.ast-inline .ast-divide-four svg {
	margin: 0 !important;
	width: 32px !important;
	height: 32px !important;
}

.customize-control-ast-radio-image.ast-inline .ast-divide-four input:checked + label svg {
	background: transparent;
}

.customize-control-ast-radio-image .modern-layout input:checked + label svg rect:first-of-type {
	stroke: var(--ast-customizer-color-1);
}

.customize-control-ast-radio-image .modern-layout input:checked + label svg :nth-child(3) {
	stroke: var(--ast-customizer-color-1);
}

#customize-control-astra-settings-shop-style.customize-control-ast-radio-image .modern-layout input:checked + label svg g rect:first-of-type, #customize-control-astra-settings-shop-style.customize-control-ast-radio-image  .modern-layout  input:checked + label svg g rect:nth-child(3) {
    stroke: #f8dbdf08;
}

.ahfb-svg-iconset.ast-inline-flex {
    position: relative;
}

#customize-control-astra-settings-header-trigger-icon label:hover .ahfb-svg-iconset svg {
    background: var( --ast-customizer-color-13);
}

#customize-control-astra-settings-header-trigger-icon label:nth-of-type(3):hover .ahfb-svg-iconset svg rect {
    fill: var( --ast-customizer-color-13);
}
.customize-control-ast-responsive-background {
	position: relative;
	margin-bottom: -5px;
}

.customize-control-ast-responsive-background .background-container h4 {
	font-size: 14px;
	font-weight: 600;
	color: #555d66;
}

.customize-control-ast-responsive-background .background-attachment h4,
.customize-control-ast-responsive-background .background-color h4,
.customize-control-ast-responsive-background .background-position h4,
.customize-control-ast-responsive-background .background-repeat h4,
.customize-control-ast-responsive-background .background-size h4 {
	margin-bottom: 5px;
	margin-top: 10px;
}

.customize-control-ast-responsive-background .background-color {
	margin-bottom: 12px;
}

.customize-control-ast-responsive-background .background-repeat {
	margin: 15px 0 15px 0;
}

.customize-control-ast-responsive-background .background-attachment .buttonset,
.customize-control-ast-responsive-background .background-size .buttonset {
	display: flex;
	flex-wrap: wrap;
}

.customize-control-ast-responsive-background .background-attachment .buttonset .switch-label,
.customize-control-ast-responsive-background .background-size .buttonset .switch-label {
	background: #ffffff;
	border: 1px solid rgba( 0, 0, 0, 0.1 );
	color: #555;
	padding: 2px 4px;
	margin-right: 15px;
	text-align: center;
	flex-grow: 1;
	transition: background-color 140ms linear;
}

.customize-control-ast-responsive-background .background-attachment .buttonset .switch-label:last-child,
.customize-control-ast-responsive-background .background-size .buttonset .switch-label:last-child {
	margin-right: 0;
}

.customize-control-ast-responsive-background .background-attachment .buttonset .switch-input:checked + .switch-label,
.customize-control-ast-responsive-background .background-size .buttonset .switch-input:checked + .switch-label {
	background-color: #f5f5f5;
	color: #565e67;
}

.customize-control-ast-responsive-background .background-attachment .buttonset .switch-input[checked="checked"] + .switch-label,
.customize-control-ast-responsive-background .background-size .buttonset .switch-input[checked="checked"] + .switch-label {
	background-color: #f5f5f5;
	color: #565e67;
}

.customize-control-ast-responsive-background .ast-bg-img-remove.components-button.is-link {
	width: 100%;
	border: 1px dashed #b4b9be;
	box-sizing: border-box;
	box-shadow: unset;
	padding: 9px 0;
	line-height: 1.6;
	margin-top: 10px;
	text-decoration: none;
}

.customize-control-ast-responsive-background .ast-bg-img-remove.components-button.is-destructive:hover:not( :disabled ) {
	color: #a02222;
	box-shadow: unset;
	border-color: #a02222;
}

.customize-control-ast-responsive-background .more-settings {
	margin-top: 12px;
	display: flex;
	justify-content: flex-end;
	padding: 5px 0 5px 0;
	cursor: pointer;
	float: none;
	text-decoration: none;
}

.customize-control-ast-responsive-background .more-settings:focus {
	outline: 0;
	box-shadow: none;
}

.customize-control-ast-responsive-background .arrow-icon {
	margin-left: 5px;
}

.customize-control-ast-responsive-background .background-container {
	display: none;
}

.customize-control-ast-responsive-background .background-container.active {
	display: block;
}

.customize-control-ast-responsive-background .wp-picker-container {
	display: block;
}

.customize-control-ast-responsive-background .wp-picker-container .wp-picker-clear {
	margin-left: 4px;
	min-height: 30px;
}

.customize-control-ast-responsive-background .customize-control-content .astra-color-picker-wrap {
	width: 100%;
}

.customize-control-ast-responsive-background .customize-control-content .components-color-picker__saturation-color,
.customize-control-ast-responsive-background .customize-control-content .components-color-picker__saturation-white {
	border-radius: unset;
}

.customize-control-ast-responsive-background .ast-color-btn-clear-wrap {
	right: 84px;
}

/**
 * BG Media Button.
 */
/**
* Gradient field
*/
.components-color-picker__inputs-toggle-wrapper .components-color-picker__inputs-toggle {
	vertical-align: middle;
	height: 32px;
}

.astra-popover-tabs .components-button.upload-button.button-add-media {
	width: 100%;
	position: relative;
	text-align: center;
	color: #555d66;
	border: 1px dashed #b4b9be;
	box-sizing: border-box;
	box-shadow: unset;
	padding: 9px 0;
	line-height: 1.6;
}

.astra-popover-tabs .components-button.upload-button.button-add-media:hover {
	color: #555d66;
	box-sizing: border-box;
	box-shadow: unset;
	border-color: #0185ba;
}

.astra-popover-tabs [ID*="-gradient-view"] {
	padding: 0 15px 15px 15px;
}

.astra-popover-tabs [ID*="-gradient-view"] .components-circular-option-picker__clear {
	flex: unset;
	box-shadow: unset;
	width: auto;
	padding: 0 5px;
}

.astra-popover-tabs [ID*="-gradient-view"] .components-circular-option-picker__clear:hover {
	box-shadow: unset;
}

.astra-popover-tabs [ID*="-gradient-view"] .components-circular-option-picker__clear:active {
	box-shadow: unset;
}

.astra-popover-tabs [ID*="-gradient-view"] .components-circular-option-picker__clear:focus {
	box-shadow: unset;
}

.astra-popover-tabs #tab-panel-0-image-view > div {
	text-align: center;
}

.astra-popover-tabs #tab-panel-0-image-view > div img {
	width: 200px;
	margin-bottom: 18px;
}

.astra-popover-tabs #tab-panel-0-image-view > div .media-position-setting {
	text-align: left;
}

.media-position-setting > .components-base-control {
	margin-bottom: 15px;
}

.media-position-setting [CLASS*="-Item-LabelWrapper"] {
	margin-bottom: 5px;
}

.astra-popover-color .components-circular-option-picker {
	position: relative;
	margin-top: 0;
}

.astra-popover-color .components-circular-option-picker .components-circular-option-picker__custom-clear-wrapper {
	margin-top: 10px;
	justify-content: flex-start;
}

@media screen and ( -ms-high-contrast: active ), ( -ms-high-contrast: none ) {
	.astra-popover-color .components-circular-option-picker .components-circular-option-picker__custom-clear-wrapper {
		width: 50px;
	}
}

.astra-popover-color [ID*="-color-view"] .components-circular-option-picker {
	width: auto;
}

.astra-popover-color .ast-color-palette {
	width: auto;
	padding: 16px 0px 0px 14px;
}

.astra-popover-color .ast-color-palette .components-circular-option-picker__option {
	width: 100%;
	height: 100%;
	border-radius: 100%;
	padding: 0;
	box-shadow: inset 0px 0px 0px 1px #0003;
}

.astra-popover-color .ast-color-palette .components-button.is-pressed:focus:not( :disabled ) {
	box-shadow: none;
	border: none;
}

.astra-popover-color .components-custom-gradient-picker {
	margin-top: 0;
}

.astra-popover-color .components-custom-gradient-picker__gradient-bar {
	box-sizing: border-box;
	opacity: 1;
}

.astra-popover-color .components-custom-gradient-picker__type-picker .components-base-control__label {
	display: block;
}

.components-custom-gradient-picker .components-base-control__label {
	padding-top: 10px;
	font-size: 12px;
	display: block;
	letter-spacing: 0.1px;
	line-height: 18px;
}

[ID*="-gradient-view"] .components-toolbar.components-custom-gradient-picker__toolbar .components-button.has-icon {
	border: none;
}

.media-position-setting.hide-settings {
	display: none;
}

.media-position-setting .astra-popover-tabs [ID*="-gradient-view"] {
	padding: 0 0 15px 0;
}

.ast-field-settings-modal .customize-control-content .color-button-wrap {
	right: 30px;
	top: 0;
}

.ast-field-settings-modal .ast-responsive-btns {
	position: absolute;
	right: 14px;
	top: 4px;
}

.ast-field-settings-modal .customize-control-content .astra-color-picker-wrap {
	margin-left: 0;
	margin-top: 0;
}

.ast-field-settings-modal .customize-control-content .ast-color-btn-reset-wrap {
	right: 65px;
	top: 7px;
}

.ast-field-settings-modal .customize-control-content .ast-color-btn-clear-wrap {
	right: 91px;
}

.ast-field-settings-modal .customize-control-ast-responsive-background.customize-control:first-child {
	margin-top: 28px;
}

.customize-control-ast-responsive-background .ast-responsive-btns,
.customize-control-ast-responsive-color .ast-responsive-btns {
	position: relative;
	right: -5px;
}

.ast-field-settings-modal .customize-control-ast-responsive-background .ast-responsive-btns,
.ast-field-settings-modal .customize-control-ast-responsive-color .ast-responsive-btns {
	right: 8px;
}

.customize-control-ast-responsive-color .ast-control-wrap {
	position: relative;
	display: flex;
	align-items: center;
	width: 100%;
	min-height: 28px;
}

.customize-control-ast-responsive-color .ast-control-wrap label {
	white-space: nowrap;
}

.customize-control-ast-responsive-color .customize-control-content .ast-color-picker-alpha {
	display: none;
}

.customize-control-ast-responsive-color .customize-control-content {
	display: block;
	margin-top: 5px;
}

.customize-control-ast-responsive-color .customize-control-content .ast-color-picker-alpha .astra-color-picker-wrap .components-color-picker__saturation-black {
	border-radius: 4px 4px 0 0;
}

.customize-control-ast-responsive-color .customize-control-content .ast-color-picker-alpha .astra-color-picker-wrap .components-color-picker__saturation-color,
.customize-control-ast-responsive-color .customize-control-content .ast-color-picker-alpha .astra-color-picker-wrap .components-color-picker__saturation-white {
	border-radius: 3px;
}

.customize-control-ast-responsive-color .customize-control-content .ast-color-picker-alpha.active {
	display: block;
}

.customize-control-ast-responsive-color .customize-control-content .components-color-picker__saturation-color,
.customize-control-ast-responsive-color .customize-control-content .components-color-picker__saturation-white {
	border-radius: unset;
}

.ast-field-settings-modal .customize-control-ast-responsive-color .customize-control-content .color-button-wrap {
	right: 30px;
	top: -3px;
}

.ast-field-settings-modal .customize-control-ast-responsive-color .customize-control-content .astra-color-picker-wrap {
	margin-left: 0px;
	margin-bottom: 10px;
	margin-top: 10px;
}

.ast-field-settings-modal .customize-control-ast-responsive-color .customize-control-content .customize-control-title {
	display: block;
	margin-bottom: 0px;
}

.ast-field-settings-modal .customize-control-ast-responsive-color .ast-responsive-btns {
	right: 7px;
	top: 1px;
}

.ast-field-settings-modal .customize-control-ast-responsive-color.customize-control:first-child {
	margin-top: 28px;
}

.ast-field-settings-modal .customize-control-ast-responsive-color .ast-clear-btn-inside-picker.components-button {
	margin: 5px 20px 20px 10px;
}

.ast-field-settings-modal .customize-control-ast-responsive-color .ast-color-btn-reset-wrap {
	top: 3.5px;
}

@supports ( -moz-appearance: none ) {
	.ast-field-settings-modal .customize-control-ast-responsive-color .ast-color-btn-reset-wrap {
		top: 5.5px;
		right: 65px;
	}

	.ast-field-settings-modal .customize-control-ast-responsive-color .customize-control-content .color-button-wrap {
		right: 27px;
		top: -2px;
	}
}

.customize-control-ast-responsive-slider .wrapper,
.customize-control-ast-slider .wrapper {
	position: relative;
	display: flex;
}

.customize-control-ast-responsive-slider .input-field-wrapper {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.customize-control-ast-responsive-slider input[type="range"] {
	position: inherit;
}

.customize-control-ast-responsive-slider select,
.customize-control.customize-control-select select {
	color: #32373c;
}

.customize-control-ast-responsive-slider .input-field-wrapper {
	display: none;
}

.customize-control-ast-responsive-slider .input-field-wrapper.active {
	display: flex;
}

.customize-control-ast-responsive-slider .components-base-control {
	width: 100%;
}

/**
 * Responsive Icons
 */
.ast-responsive-slider-btns {
	position: relative;
	display: inline-block;
	top: 3px;
}

.ast-responsive-slider-btns > li {
	margin-bottom: 0;
	display: none;
}

.ast-responsive-slider-btns > li.active {
	display: flex;
}

.ast-responsive-slider-btns button[type="button"] {
	padding: 0;
	cursor: pointer;
	background: none;
	border: none;
	outline: none;
	line-height: 1;
}

.ast-responsive-slider-btns button[type="button"] > i {
	cursor: pointer;
	width: 15px;
	height: 15px;
	font-size: 15px;
}

.customize-control-ast-responsive-slider .wrapper .components-base-control,
.customize-control-ast-responsive-slider .wrapper .input-field-wrapper.active,
.customize-control-ast-slider .components-base-control.components-range-control {
	width: 100%;
	display: inline-block;
}

.customize-control-ast-slider .wrapper .ast-resp-slider-reset-wrap,
.customize-control-ast-responsive-slider .wrapper .ast-resp-slider-reset-wrap {
	margin-left: 5px;
	margin-top: 4px;
}

.customize-control-ast-responsive-slider .wrapper .ast-resp-slider-reset-wrap {
	top: 4px;
	right: 0;
}

.customize-control-ast-slider .components-input-control input.components-input-control__input,
.customize-control-ast-responsive-slider .components-input-control input.components-input-control__input {
	-moz-appearance: textfield;
}

.customize-control-ast-slider input.components-input-control__input::-webkit-inner-spin-button,
.customize-control-ast-slider input.components-input-control__input::-webkit-outer-spin-button,
.customize-control-ast-responsive-slider input.components-input-control__input::-webkit-inner-spin-button,
.customize-control-ast-responsive-slider input.components-input-control__input::-webkit-outer-spin-button {
	-webkit-appearance: none;
}

.customize-control-ast-slider .components-input-control__container input.components-input-control__input,
.customize-control-ast-responsive-slider .components-input-control__container input.components-input-control__input {
	display: block;
	box-shadow: none;
	transition: all 0.3s;
	border: 1px solid var( --ast-customizer-color-7 );
}

.customize-control-ast-slider input.components-input-control__input:hover,
.customize-control-ast-slider input.components-input-control__input:focus,
.customize-control-ast-slider input.components-input-control__input:active,
.customize-control-ast-responsive-slider input.components-input-control__input:hover,
.customize-control-ast-responsive-slider input.components-input-control__input:focus,
.customize-control-ast-responsive-slider input.components-input-control__input:active {
	border-color: var( --ast-customizer-color-1 );
	border-radius: 3px;
	outline: none;
}

.customize-control-ast-slider .ast-slider-wrap,
.customize-control-ast-responsive-slider .ast-slider-wrap {
	position: relative;
}

.components-range-control__wrapper .components-range-control__track {
	color: var( --ast-customizer-color-1 );
}

.components-range-control__wrapper span span {
	background-color: var( --ast-customizer-color-1 );
}

.customize-control-ast-slider .ast-slider-wrap .ast-slider-top,
.customize-control-ast-responsive-slider .ast-slider-wrap .ast-slider-top {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 16px;
}

.customize-control-ast-slider .ast-slider-wrap .ast-slider-top .customize-control-title,
.customize-control-ast-responsive-slider .ast-slider-wrap .ast-slider-top .customize-control-title {
	margin-bottom: 0;
}

.customize-control-ast-slider .components-input-control__container div.components-input-control__backdrop,
.customize-control-ast-responsive-slider .components-input-control__container div.components-input-control__backdrop {
	border: none;
	border-radius: 3px;
	box-shadow: none;
}

.customize-control-ast-slider .ast-resp-slider-reset-wrap,
.customize-control-ast-responsive-slider .ast-resp-slider-reset-wrap {
	position: absolute;
	display: inline-block;
	line-height: 1;
	right: 0;
}

.customize-control-ast-slider:has( .ast-units-wrapper ) .ast-resp-slider-reset-wrap,
.customize-control-ast-responsive-slider:has( .ast-units-wrapper ) .ast-resp-slider-reset-wrap {
	right: 40px;
}

.customize-control-ast-responsive-slider .ast-slider-wrap .components-range-control__root {
	gap: 12px;
}

.customize-control-ast-responsive-slider .ast-slider-wrap .components-range-control__number {
	width: 64px;
	margin-left: 0 !important;
}

.customize-control-ast-responsive-slider .ast-slider-wrap .components-range-control__number input {
	text-align: center;
	min-width: 64px;
}


.customize-control-ast-responsive-spacing .ast-spacing-responsive-outer-wrapper {
	display: flex;
	position: relative;
}

.customize-control-ast-responsive-spacing .ast-spacing-responsive {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.customize-control-ast-responsive-spacing .ast-spacing-input-item {
	/* margin: 0 2px; */
	-moz-appearance: textfield;
}

.ast-spacing-input-item {
	margin-bottom: 0;
}

/* Target the second input item and apply 2px border-radius on left top and bottom corners */
.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper .ast-spacing-input-item:nth-child( 2 ) input.ast-spacing-input {
	border-top-left-radius: 2px;
	border-bottom-left-radius: 2px;
}

.customize-control-ast-responsive-spacing .ast-spacing-input-item input[type="number"] {
	-moz-appearance: textfield;
}

.customize-control-ast-responsive-spacing .ast-spacing-input-item input::-webkit-inner-spin-button,
.customize-control-ast-responsive-spacing .ast-spacing-input-item input::-webkit-outer-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

.customize-control-ast-responsive-spacing .ast-spacing-responsive-units-screen-wrap {
	position: relative;
	top: -5px;
	right: -10px;
	display: inline-block;
}

.customize-control-ast-responsive-spacing .ast-spacing-responsive-btns {
	position: relative;
}

.customize-control-ast-responsive-spacing .ast-spacing-responsive-btns > li {
	display: none;
}

.customize-control-ast-responsive-spacing .ast-spacing-responsive-btns > li.active {
	display: inline-block;
	margin-bottom: 0;
}

.customize-control-ast-responsive-spacing .ast-spacing-responsive-btns button[type="button"] {
	display: inline-flex;
	padding: 0;
	cursor: pointer;
	background: none;
	border: none;
	outline: none;
	width: 100%;
}

.customize-control-ast-responsive-spacing .ast-spacing-responsive-btns button[type="button"] > i {
	font-size: 15px;
	vertical-align: middle;
}

.customize-control-ast-responsive-spacing .input-wrapper.ast-spacing-responsive-wrapper {
	display: inline-flex;
}

.customize-control-ast-responsive-spacing .ast-spacing-responsive-units li.single-unit.active {
	opacity: 1;
	color: var( --ast-customizer-primary-color );
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper {
	display: none;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper li {
	text-align: center;
	-webkit-box-flex: 1;
	-ms-flex: auto;
	flex: auto;
	cursor: pointer;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper li input.ast-spacing-input {
	text-align: center;
	display: block;
	font-size: 12px;
	padding: 15px 0;
	width: 100%;
	height: 28px;
	box-shadow: none;
	transition: all -0.5s;
	border-right: 0;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper li input.ast-spacing-input:focus,
.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper li input.ast-spacing-input:hover,
.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper li input.ast-spacing-input:active {
	border-color: var(--ast-customizer-color-1);
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper li:has( input.ast-spacing-input:is( :focus, :hover, :active ) ) + li > input.ast-spacing-input,
.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper:has( li:nth-child( 5 ) input.ast-spacing-input:is( :focus, :hover, :active ) ) li.ast-spacing-input-item-link {
	border-left-color: var(--ast-customizer-color-1);
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper li .ast-spacing-connected {
	color: var(--ast-customizer-color-1);
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper .ast-units-wrapper {
	top: -36px;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper.active {
	display: flex;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper span.ast-spacing-title {
	text-transform: uppercase;
	font-size: 10px;
	color: var( --ast-customizer-color-6 );
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper .ast-spacing-input-item-link {
	order: 2;
	margin-bottom: 16px;
	background: #e5e7eb;
	height: 30px;
	padding-right: 5px;
	padding-left: 5px;
	border-top-right-radius: 2px;
	border-bottom-right-radius: 2px;
	border: 1px solid var( --ast-customizer-color-7 );
}

.ast-spacing-input-item-link {
	position: relative;
}

.ast-spacing-input-item-link.disconnected::before {
	display: none;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper .ast-spacing-input-item-link .dashicons {
	padding: 2px 0;
	font-size: 12px;
	line-height: 28px;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper .ast-spacing-input-item-link .ast-spacing-connected {
	display: none;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper .ast-spacing-input-item-link.disconnected .ast-spacing-disconnected {
	display: none;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper .ast-spacing-input-item-link.disconnected .ast-spacing-connected {
	display: block;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper .ast-spacing-input-item-link span {
	width: 100%;
	height: 32px;
	line-height: 28px;
	font-size: 14px;
	border-radius: 3px 0 0 3px;
	background-color: transparent;
	padding-left: 0;
	padding-right: 0;
}

.customize-control-ast-responsive-spacing .input-wrapper .ast-spacing-wrapper .ast-spacing-input-item-link svg {
	pointer-events: none;
	margin-top: 3px;
}

.customize-control-ast-responsive-spacing .ast-spacing-wrapper > li:nth-child( 2 ) {
	margin-left: 0;
}

.customize-control-ast-responsive-toggle-control .components-toggle-control .components-base-control__field,
.ast-responsive-toggle-control .components-base-control__field {
	margin: 0;
}

.customize-control-ast-responsive-toggle-control .components-toggle-control .components-form-toggle,
.customize-control-ast-link .ast-responsive-toggle-control .components-form-toggle {
	margin: 0;
	order: 2;
}

.customize-control-ast-link .ast-responsive-toggle-control .components-base-control__field {
	margin: 20px 0;
}

.customize-control-ast-responsive-toggle-control .components-toggle-control .components-toggle-control__label,
.ast-responsive-toggle-control label {
	display: flex;
	flex: 1 1 auto;
	font-size: 14px;
	font-weight: 600;
}

.customize-control-ast-responsive-toggle-control .ast-responsive-toggle-control-wrapper .ast-responsive-toggle-control {
	display: none;
}

.customize-control-ast-responsive-toggle-control .ast-responsive-toggle-control-wrapper .ast-responsive-toggle-control.active {
	position: relative;
	top: 2px;
	width: 91%;
	display: inline-block;
	vertical-align: middle;
}

.customize-control-ast-responsive-toggle-control .ast-responsive-toggle-control-wrapper .ast-responsive-toggle-btns {
	position: relative;
	float: right;
	top: 4px;
}

.customize-control-ast-responsive-toggle-control .ast-responsive-toggle-control-wrapper .ast-responsive-toggle-btns > li.active {
	vertical-align: middle;
}

.ast-responsive-toggle-btns > li {
	margin-bottom: 0;
	display: none;
}

.ast-responsive-toggle-btns > li.active {
	display: inline-block;
}

.ast-responsive-toggle-btns button[type="button"] {
	padding: 0;
	cursor: pointer;
	background: none;
	border: none;
	outline: none;
}

.ast-responsive-toggle-btns button[type="button"] > i {
	cursor: pointer;
	width: 15px;
	height: 15px;
	font-size: 15px;
}

.ast-responsive-btns {
	display: inline-flex;
}

.ast-responsive-btns > li {
	margin-bottom: 0;
	display: none;
}

.ast-responsive-btns > li.active {
	display: inline-block;
}

.ast-responsive-btns button[type="button"] {
	display: flex;
	padding: 0;
	cursor: pointer;
	background: none;
	border: none;
	outline: none;
}

.ast-responsive-btns button[type="button"] > i {
	width: 15px;
	height: 15px;
	font-size: 15px;
}

.input-wrapper.ast-responsive-wrapper {
	display: inline-flex;
	justify-content: space-between;
	margin-right: 0;
	align-items: flex-start;
	flex: 0 1;
	width: 100%;
}

.ast-customizer-select-multi .ast-multi-select__input-container {
	padding: 0;
	margin: 0;
}

.ast-customizer-select-multi .ast-multi-select__input-container input {
	min-height: min-content;
}

.ast-customizer-select-multi .ast-multi-select__input-container input:focus {
	box-shadow: none;
}

.ast-customizer-select-multi {
	position: relative;
}

.ast-customizer-select-multi .ast-multi-select__value-container {
	padding-top: 4px;
	padding-bottom: 4px;
	overflow-x: hidden;
	padding-left: 4px;
	padding-right: 30px;
	gap: 4px;
}

.ast-customizer-select-multi:after {
	content: "\f11c";
	position: absolute;
	font-family: dashicons;
	font-size: 16px;
	margin-right: 15px;
	pointer-events: none;
	z-index: 1;
	top: 8px;
	right: 0;
}

.ast-customizer-select-multi .ast-multi-select__multi-value__label {
	margin-right: 0.2em;
}

.ast-customizer-select-multi .ast-multi-select__multi-value {
	margin: 0;
	font-size: 14px;
	padding: 2px 5px 2px 5px;
	min-width: auto;
	color: #4b5563;
	background-color: #f5f5f5;
	border: 1px solid #d1d5db;
	border-radius: 2px;
}

.ast-customizer-select-multi .ast-multi-select__multi-value__remove {
	color: #4b5563;
	padding-right: 0;
}

.ast-customizer-select-multi .ast-multi-select__multi-value__remove:hover {
	background-color: transparent;
	color: #4b5563;
}

.ast-customizer-select-multi .ast-multi-select__menu *,
.ast-customizer-select-multi .ast-multi-select__multi-value__remove {
	cursor: pointer;
}

.ast-customizer-select-multi .ast-multi-select__menu {
	z-index: 2;
}

.customize-control select,
.ast-variant-select>div,
.customize-control input,
.customize-control textarea,
.customize-control-content .components-text-control__input {
	border-color: var(--ast-customizer-color-7);
	color: var(--ast-customizer-color-5);
	font-size: 13px;
	line-height: 16px;
	border-radius: 3px;
	margin: 0;
}

.customize-control-radio input {
	border-radius: 50%;
}

.ast-variant-select>div:first-of-type {
	padding: 0 3px;
	min-height: 32px;
	box-shadow: none;
}

.ast-customizer-select-multi .ast-multi-select__control--is-focused,
.ast-customizer-select-multi .ast-multi-select__control:hover {
	border-color: var(--ast-customizer-primary-color);
}

.customize-control select:focus,
.ast-variant-select>div:focus,
.customize-control input:focus {
	border-color: var(--ast-customizer-color-1);
	color: #0a4b78;
	box-shadow: 0 0 0 0px var(--ast-customizer-color-1) !important;
}

.customize-control select:hover,
.ast-variant-select>div:hover,
.customize-control input:hover {
	border-color: var(--ast-customizer-color-1);
}

.customize-control-ast-font select:hover {
	color: var(--ast-customizer-color-4);
}

.customize-control-ast-font span.select2-selection.select2-selection--single:hover,
.ast-font-weight:hover {
	border: 1px solid var(--ast-customizer-active-border-color);
}

/* Textarea */
.customize-control-textarea textarea {
	min-height: 80px;
}
.ast-adv-toggle-icon {
	right: 2px;
	position: absolute;
	width: 20px;
	height: 20px;
	font-size: 18px;
	border-radius: 2px;
	padding: 4px;
	color: var( --ast-customizer-color-8 );
	background: var( --ast-customizer-color-9 );
	cursor: pointer;
}

.ast-adv-toggle-icon:hover {
	color: var( --ast-customizer-color-1 );
}

.ast-toggle-desc-wrap:not( .ast-typography-control ) .ast-adv-toggle-icon.open {
	background: var( --ast-customizer-icon-background );
}

#customize-control-astra-color-palettes .ast-adv-toggle-icon:before {
	display: none;
}

.customize-control.customize-control-ast-settings-group .ast-toggle-desc-wrap:not( .ast-typography-control ) label {
	display: flex;
	align-items: center;
}

.ast-toggle-desc-wrap .ast-adv-toggle-icon svg {
	width: 100%;
	height: 100%;
}

.ast-field-settings-modal {
	position: absolute;
	box-shadow: 0px 6px 24px 0px rgba( 0, 0, 0, 0.24 );
	-webkit-border-radius: 3px;
	border-radius: 3px;
	left: 0;
	right: 0;
	z-index: 8;
	background-color: #fff;
}

.customize-control-ast-color-palette .ast-field-settings-modal {
	z-index: 11;
}

.ast-field-settings-modal .customize-control-ast-divider:first-child {
	margin-top: 15px;
}

.ast-field-settings-wrap {
	width: 100%;
}

.ast-group-tabs,
.ast-tab-content {
	position: relative;
}

.ast-group-list {
	overflow: hidden;
	border-bottom: 1px solid #ddd;
}

.ast-group-list:before,
.ast-group-list:after {
	content: "";
	display: table;
	border-collapse: collapse;
}

/* Tab anchor color */
.ui-tabs-anchor {
	float: left;
	padding: 0.5em 0.5em;
	color: #555d66;
	text-decoration: none;
}

.ui-state-active .ui-tabs-anchor {
	color: #ffffff;
}

.ui-tabs-anchor:hover {
	color: #555d66;
}

.ui-state-active .ui-tabs-anchor:hover {
	color: #ffffff;
}

.ast-group-tabs .ui-widget-content {
	overflow: hidden;
	/*padding-top: 15px;*/
}

.ast-group-tabs .ui-widget-content.iris-slider-offset,
.ast-group-tabs .ui-widget-content.iris-slider-offset-alpha {
	overflow: inherit;
}

.ast-fields-wrap {
	overflow: hidden;
}

.ast-field-settings-wrap {
	-webkit-box-shadow: 0 2px 15px rgba( 0, 0, 0, 0.3 );
	box-shadow: 0 2px 15px rgba( 0, 0, 0, 0.3 );
	-webkit-border-radius: 3px;
	background-color: #fff;
}

.ast-field-settings-wrap .ast-field-settings-modal {
	border-radius: 4px;
	margin-top: 24px;
	margin-bottom: 20px;
}

.ast-field-settings-modal::before {
	content: "";
	position: absolute;
	top: -17px;
	right: 13px;
	border: 9px solid transparent;
	border-bottom-color: #fff;
	pointer-events: none;
}

.ast-group-tabs .ui-tabs-nav {
	display: flex;
	padding: 16px 16px 0 16px;
}

.ast-group-tabs .ui-tabs-nav .ui-corner-top {
	align-items: center;
	flex: 1 1 auto;
	justify-content: center;
	margin: 0 0;
	padding: 0;
	border: 1px solid #ccc;
	transition: background-color 140ms linear;
}

.ast-group-tabs .ui-tabs-nav .ui-corner-top:first-child {
	border-radius: 3px 0px 0px 3px;
}

.ast-group-tabs .ui-tabs-nav .ui-corner-top:last-child {
	border-radius: 0px 3px 3px 0px;
}

.ast-group-tabs .ui-tabs-nav .ui-corner-top .ui-tabs-anchor {
	width: 100%;
	text-align: center;
	padding: 2px 4px;
	padding-left: 0;
	padding-right: 0;
	outline: none;
}

.ast-group-tabs ul.ast-group-list .ui-corner-top .ui-tabs-anchor:focus {
	box-shadow: none;
}

.ast-group-tabs .ui-tabs-nav {
	border: none;
}

.ast-group-tabs ul.ast-group-list .ui-corner-top.ui-state-active {
	background-color: #0185ba;
	border: 1px solid rgba( 0, 0, 0, 0.1 );
}

.ast-group-tabs .ui-tabs-nav {
	overflow: visible;
}

.ast-group-tabs ul.ast-group-list .ui-corner-top:not( :first-child ) {
	border-left-width: 0;
}

/* Buttons */
.wp-core-ui .ast-field-settings-modal .background-image-upload .button {
	font-size: 11px;
}

/* Popup params padding */
.ast-field-settings-modal .customize-control {
	padding-left: 16px;
	padding-right: 16px;
	box-sizing: border-box;
}

.ast-control-wrap .ast-color-picker-alpha {
	display: inline-flex;
}

.ast-field-settings-modal .customize-control:first-child {
	margin-top: 16px;
}

.ast-field-settings-modal .ui-tabs-nav .customize-control:first-child {
	margin-top: 0;
}

.ast-field-settings-modal .customize-control:last-child {
	padding-bottom: 16px;
}

.customize-control-ast-settings-group {
	line-height: 18px;
}

.customize-control-ast-settings-group .customize-control-title {
	margin-bottom: 0;
}

.customize-control-ast-settings-group .ast-field-settings-modal .customize-control-title {
	margin-bottom: 8px;
	font-size: 13px;
	font-weight: 400;
}

.customize-control-ast-settings-group .ast-field-settings-modal .customize-control-ast-responsive-slider .ast-range-unit,
.customize-control-ast-settings-group .ast-field-settings-modal .customize-control-ast-slider .ast-range-unit,
.customize-control-ast-settings-group .ast-field-settings-modal .customize-control-ast-slider .ast-range-unit {
	top: 6px;
}

.customize-control-ast-settings-group .ast-field-settings-modal .customize-control-ast-slider .ast-responsive-slider-btns,
.customize-control-ast-settings-group .ast-field-settings-modal .customize-control-ast-responsive-slider .ast-responsive-slider-btns {
	top: 2px;
}

#customize-control-headings-font-extras.ast-sub-top-divider {
	margin-top: 12px;
}

/**
 * Popover Tab Structure.
 */
.astra-popover-tabs {
	position: relative;
}

.astra-popover-tabs .components-tab-panel__tabs {
	display: flex;
	padding: 15px;
	border: none;
}

.astra-popover-tabs .components-tab-panel__tabs .components-tab-panel__tabs-item {
	color: #555d66;
	font-size: 13px;
	font-weight: 400;
	height: 35px;
	transition: unset;
	padding: 2px 4px;
	text-decoration: none;
	border-radius: 0;
	border: 1px solid rgba( 0, 0, 0, 0.1 );
}

.astra-popover-tabs .components-tab-panel__tabs .components-tab-panel__tabs-item:active {
	box-shadow: unset;
}

.astra-popover-tabs .components-tab-panel__tabs .components-tab-panel__tabs-item:focus {
	box-shadow: unset;
}

.astra-popover-tabs [ID*="-image-view"] {
	padding: 15px;
	border: none;
}

.astra-popover-tabs .components-button {
	align-items: center;
	flex: 1 1 auto;
	justify-content: center;
	margin: 0 0;
	padding: 0;
	border: 1px solid #ccc;
	transition: background-color 140ms linear;
}

.astra-popover-tabs .components-tab-panel__tabs-item:first-child {
	border-radius: 3px 0px 0px 3px;
}

.astra-popover-tabs .components-tab-panel__tabs-item:last-child {
	border-radius: 0px 3px 3px 0px;
}

.astra-popover-tabs .components-tab-panel__tabs-item:not( :first-child ) {
	border-left-width: 0;
}

.astra-popover-tabs .components-tab-panel__tabs-item.active-tab {
	background-color: #0185ba;
	color: #ffffff;
}
.astra-popover-tabs .components-tab-panel__tabs .components-tab-panel__tabs-item {
	height: 32px !important;
}
.astra-popover-tabs.astra-media-tab .components-tab-panel__tabs,
.astra-popover-tabs.astra-media-tab .components-tab-panel__tab-content {
	padding: 0;
}

.astra-popover-tabs.astra-media-tab .components-tab-panel__tabs-item.active-tab {
	border-color: var( --ast-customizer-color-3 );
	background: var( --ast-customizer-color-1 );
}

.astra-popover-tabs.astra-media-tab .components-tab-panel__tab-content .ast-social-icon-picker-label {
	text-align: left;
}

.ahfb-sorter-drop-social_item_group .ahfb-sorter-item-panel-content .ast-social-icon-picker-label{
	text-transform: uppercase;
	font-weight: 500;
    line-height: 1.4;
    font-size:11px;
}

.astra-media-tab .astra-media-image {
	width: 100%;
	margin-bottom: 0;
}

.astra-media-tab .ast-media-btn {
	display: block;
	width: 100%;
	border: 1px dashed var( --ast-customizer-color-7 );
	box-sizing: border-box;
	box-shadow: unset;
	padding: 9px 0;
	line-height: 1.6;
	text-decoration: none;
}

.astra-media-tab .ast-danger-btn {
	color: var( --ast-customizer-color-14 );
	margin-top: 10px;
}

.astra-media-tab .ast-media-btn:hover {
	border-color: var( --ast-customizer-color-3 );
}

.astra-media-tab .ast-danger-btn:hover {
	border-color: var( --ast-customizer-color-14 );
}

.components-base-control.ast-text-control-input {
	margin-bottom: 16px;
}

.ast-payments-text-control-input .components-base-control__label {
	text-transform: none;
	color: #334155;
	font-size: 13px;
}

.components-base-control.ast-payments-text-control-input {
	margin-bottom: 20px;
}

.components-base-control.ast-text-control-input input {
	border-color: var( --ast-customizer-color-7 );
	color: var( --ast-customizer-color-5 );
}

.astra-popover-color .astra-popover-tabs .components-tab-panel__tabs button.components-tab-panel__tabs-item.active-tab:not( .is-primary ):not( .is-tertiary ):not( .is-link ):hover {
	color: #fff;
}

.components-popover.astra-popover-color .components-popover__content {
	min-width: 310px;
	max-height: 60vh;
	padding: 0;
	-webkit-box-shadow: 0 2px 15px rgba( 0, 0, 0, 0.3 );
	box-shadow: 0 2px 15px rgba( 0, 0, 0, 0.3 );
}

.ast-field-settings-modal .components-popover.astra-popover-color .components-popover__content {
	min-width: 320px;
	min-height: 325px;
}

.ast-field-settings-modal .background-wrapper .components-popover.astra-popover-color .components-popover__content {
	min-height: 385px;
}

.ast-field-settings-modal .components-angle-picker-control .components-flex__block {
	min-width: auto;
}

.customize-control-ast-settings-group .customize-control-ast-slider .wrapper .ast-resp-slider-reset-wrap,
.customize-control-ast-settings-group .customize-control-ast-responsive-slider .wrapper .ast-resp-slider-reset-wrap {
	margin-top: 0;
}

/**
 * Responsive slider control inside settings-group, case: body-font, headings-font.
 */
.ast-fields-wrap .customize-control-ast-responsive-slider .ast-slider-wrap > label,
.ast-fields-wrap .customize-control-ast-selector .ast-slider-wrap > label {
	display: inline-block;
}

/* Typography Group Control styling */
.ast-typography-control + .ast-field-settings-wrap .ast-field-settings-modal {
	margin-top: 8px;
}

.ast-typography-control + .ast-field-settings-wrap .ast-field-settings-modal::before {
	right: 8px;
}

.ast-typography-selector {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 8px;
	border: 1px solid var( --ast-customizer-border-color );
	border-radius: 3px;
	padding: 7px;
	cursor: pointer;
	margin-top: 10px;
	color: var( --ast-customizer-color-5 );
	font-size: 12px;
	line-height: 16px;
	color: var( --ast-customizer-input-text-color );
}

.ast-typography-slash {
	color: var(--ast-customizer-color-7);
	font-weight: 300;
	padding: 0 2px;
  }
  

.ast-typography-selector .ast-typography-label {
	text-transform: capitalize;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.ast-typography-selector.open {
	border: 1px solid var(--ast-customizer-color-1);
}

.ast-typography-selector.open .ast-adv-toggle-icon {
	color: var( --ast-customizer-primary-color );
}

.ast-typography-selector .ast-adv-toggle-icon:before {
	content: "\f347";
	font-size: 16px;
}

.ast-typography-selector:hover {
    border: 1px solid var(--ast-customizer-color-1);
}

.ast-typography-details {
	margin-right: 28px;
	white-space: nowrap;
}

#customize-control-astra-settings-secondary-theme-button-bg-color-group {
	margin-top: 8px;
}
.customize-control-ast-slider .wrapper {
	display: flex;
}

.customize-control-ast-slider .components-range-control {
	width: 100%;
}

.components-range-control__slider[type="range"] {
	align-items: center;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
}

.components-range-control__slider[type="range"]::-webkit-slider-runnable-track,
.components-range-control__slider[type="range"]::-moz-range-track {
	content: "";
	height: 2px;
	pointer-events: none;
}

.components-range-control__slider[type="range"]::-webkit-slider-thumb,
.components-range-control__slider[type="range"]::-moz-range-thumb {
	cursor: pointer;
	-webkit-appearance: none;
	margin-top: 5px;
}

.customize-control-ast-slider .components-range-control__wrapper,
.customize-control-ast-responsive-slider .components-range-control__wrapper {
  margin-left: 0px;
  z-index: 0;
}

.customize-control-ast-slider .components-range-control__wrapper > span:first-of-type,
.customize-control-ast-responsive-slider .components-range-control__wrapper > span:first-of-type,
.customize-control-ast-slider .components-range-control__track,
.customize-control-ast-responsive-slider .components-range-control__track {
	height: 2px;
}

.customize-control-ast-slider .components-range-control__thumb-wrapper,
.customize-control-ast-responsive-slider .components-range-control__thumb-wrapper {
	height: 14px;
	width: 14px;
	margin-top: 7px;
}

.customize-control-ast-slider .components-range-control__thumb-wrapper > span::before,
.customize-control-ast-responsive-slider .components-range-control__thumb-wrapper > span::before {
	background-color: var( --ast-customizer-alternate-primary-color );
	top: -3px;
	left: -3px;
}

.ast-top-divider.customize-control-ast-slider .ast-divider-title + .ast-slider-wrap .ast-resp-slider-reset-wrap {
	top: 68px;
}

.customize-control-ast-responsive-slider .ast-responsive-units {
	position: absolute;
	top: 0;
	right: 0;
	font-size: 11px;
	line-height: normal;
	text-transform: uppercase;
	width: auto;
	display: none;
}

.ast-field-settings-modal .ast-fields-wrap > .customize-control-ast-responsive-slider + .customize-control-ast-font {
	margin-top: 8px;
}

/* This section styles the spin buttons for number inputs in the slider controls to ensure they appear as intended */
.customize-control-ast-slider input[type="number"]::-webkit-inner-spin-button,
.customize-control-ast-responsive-slider input[type="number"]::-webkit-inner-spin-button,
.customize-control-ast-slider input[type="number"]::-webkit-outer-spin-button,
.customize-control-ast-responsive-slider input[type="number"]::-webkit-outer-spin-button {
	appearance: auto !important;
	-webkit-appearance: auto !important;
}

.customize-control-ast-sortable .sortable {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.customize-control-ast-sortable .sortable .ast-sortable-item {
	padding: 6px 0;
	border: 1px solid var( --ast-customizer-color-7 );
	background-color: var( --ast-customizer-color-9 );
	border-radius: 3px;
	transition: box-shadow 0.5s ease;
}

.customize-control-ast-sortable .sortable .ast-sortable-item:hover {
	border-color: var( --ast-customizer-color-1 );
}

.ast-sortable-item.ui-sortable-handle.show:hover{
	border-color: #fff;
}

.customize-control-ast-sortable .sortable .ast-sortable-item.show:not( .invisible ) {
	box-shadow: 0 6px 24px 0 rgba( 0, 0, 0, 0.24 );
	padding-bottom: 0;
}

.customize-control-ast-sortable .sortable .ast-sortable-item > span {
	margin-left: 12px;
}

.customize-control-ast-sortable .sortable .ast-sortable-item > i.icon {
	float: right;
	position: relative;
	margin-left: 4px;
	max-height: 18px;
	cursor: pointer;
}

.customize-control-ast-sortable .sortable .ast-sortable-item > i.icon:first-of-type {
	margin-right: 8px;
}

.customize-control-ast-sortable .sortable .ast-sortable-item.ui-sortable-helper {
	border-color: var( --ast-customizer-color-1 );
}

.customize-control-ast-sortable .sortable .ast-sortable-item,
.customize-control-ast-sortable .sortable .ast-sortable-subcontrols {
	cursor: move;
}

.customize-control-ast-sortable .sortable .ast-sortable-subcontrols {
	margin-top: 6px;
}

.customize-control-ast-sortable .sortable .ast-sortable-item.invisible {
	opacity: 0.6;
	visibility: visible;
}

.customize-control-ast-sortable .sortable .ast-sortable-item > i.icon .invisible,
.customize-control-ast-sortable .sortable .ast-sortable-item.invisible > i.icon .visible {
	display: none;
}

.customize-control-ast-sortable .sortable .ast-sortable-item > i.icon .visible,
.customize-control-ast-sortable .sortable .ast-sortable-item.invisible > i.icon .invisible {
	display: block;
}

/**
 * Expansion specific new raw CSS.
 */

.ast-sortable-item.show:hover {
	opacity: 1;
}

.ast-sortable-item .dashicons-arrow-down-alt2,
.ast-sortable-item .dashicons-remove {
	margin: 0;
}

.ast-sortable-item .dashicons-admin-page,
.ast-sortable-item .dashicons-arrow-down-alt2,
.ast-sortable-item .dashicons-remove {
	cursor: pointer;
}

.ast-sortable-subcontrols {
	display: none;
	padding: 0px 12px 12px;
	border-top: 1px solid var( --ast-customizer-color-7 );
}

.show .ast-sortable-subcontrols {
	display: block;
}

.ast-sortable-subfields-wrap .customize-control {
	box-sizing: border-box;
	position: relative;
}

/**
 * ast-list-icons inside sortable compatibility CSS.
 */
.ast-sortable-item.show i.ast-accordion svg {
	transform: rotate( 180deg );
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .components-button {
	padding: 6px 8px;
	height: 40px;
	background: white;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .components-button:focus {
	box-shadow: none;
}

.customize-control-ast-list-icons .ahfb-sorter-item-panel-header .ahfb-sorter-title {
	padding-right: 20px;
	max-width: 100%;
	text-overflow: ellipsis;
	overflow: hidden;
}

.customize-control-ast-list-icons span.feature-label {
	max-width: 100px;
	text-overflow: ellipsis;
	font-size: 14px;
	line-height: 18px;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .dashicons-admin-page {
	color: #556068;
}

.customize-control-ast-sortable .ast-sortable-subfields-wrap .customize-control-ast-list-icons .ahfb-sorter-item-panel-header {
	border: 1px solid #ddd;
	border-left: 0;
	border-bottom: 0;
	overflow: hidden;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .ahfb-sorter-visiblity svg,
.ast-sortable-subfields-wrap .customize-control-ast-list-icons .ahfb-sorter-visiblity .ast-media-image-preview {
	height: 14px;
	width: 14px;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .ahfb-sorter-visiblity span {
	display: flex;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .ahfb-sorter-visiblity svg {
	height: 16px;
	width: 16px;
	fill: #556068;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .ast-sorter-item-expand {
	border-left: none;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .ahfb-sorter-visiblity {
	border-right: none;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .ahfb-sorter-item {
	border: 1px solid #ddd;
	border-top: 0;
}

.ahfb-sorter-row .ahfb-sorter-item {
    padding: 8px;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .rfipdropdown {
	width: 225px !important;
	left: -10px;
	box-shadow: 0 5px 15px rgb( 0 0 0 / 22% ), 0 0px 5px rgb( 0 0 0 / 30% );
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .rfipdropdown--open {
	height: 285px;
	overflow: hidden;
	position: absolute;
}

.ast-sortable-item .ahfb-sorter-item-panel-content {
	border-top: 1px solid #ddd;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .rfipdropdown--open .rfipicons__ibox:hover > * {
	transform: scale( 1.3 ) !important;
}

.ast-sortable-subfields-wrap .customize-control-ast-list-icons .rfipdropdown--open .rfipicons__ibox {
	height: 32px !important;
}

.ast-sortable-subfields-wrap .ast-color-btn-reset-wrap {
	top: 0;
}

.customize-control-ast-sortable .ast-sortable-subfields-wrap .customize-control-title {
	margin-bottom: 10px;
}

.customize-control-ast-sortable .ast-sortable-subfields-wrap .customize-control-title,
.customize-control-ast-sortable .ast-sortable-subfields-wrap .ast-title-wrapper .ast-control-label,
.customize-control-ast-sortable .ast-sortable-subfields-wrap .toggle-control-label {
	font-size: 10px;
	line-height: 10px;
	letter-spacing: 1.5px;
	font-weight: 600;
	text-transform: uppercase;
}

.ast-sortable-subfields-wrap .customize-control-ast-color .ast-control-wrap .astra-popover-color {
	margin-left: -10px;
	margin-right: 10px;
	width: calc( 100% + 15px );
}

.customize-control-ast-sortable .ast-sortable-subfields-wrap .ast-responsive-btns {
	top: 7px;
}

.customize-control-content {
	width: 100%;
	display: flex;
	flex-direction: column;
}

.ast-sortable-subfields-wrap .customize-control {
	float: none;
}

/**
 * Hide clone & accordion dashicons for invisible items.
 */
.ast-sortable-item.invisible > .dashicons-admin-page,
.ast-sortable-item.invisible > .ast-accordion,
.ast-sortable-item.invisible > .dashicons-remove {
	display: none;
}

.ast-sortable-item.invisible .ast-sortable-subcontrols {
	display: none;
}

.ast-sortable-item .ahfb-sorter-item .dashicons {
	font-size: 16px;
	width: 16px;
	height: 16px;
}

.ast-sortable-item .ahfb-sorter-item .ahfb-sorter-item-expand:before {
	left: 7px;
	width: 17px;
}

.ast-sortable-item .components-button.item-is-hidden:not( .visibility-item ),
.ahfb-sorter-item:first-child .ahfb-sorter-item-remove {
	display: none;
}

.customize-control-ast-sortable .sortable .rfip .rfipbtn__button {
	margin-left: auto;
}

.ast-sortable-subfields-wrap .customize-control-ast-responsive-slider .ast-responsive-slider-btns {
	top: -2px;
}

/**
 * Dynamic featured-image section compat.
 */
.ast-sortable-item .customize-control-ast-color .ast-color-btn-reset-wrap {
	top: 12px;
}

.ast-sortable-item .customize-control-ast-color .color-button-wrap {
	top: 8px;
}

#customize-control-ast-dynamic-single-post-banner-featured-overlay .color-button-wrap {
	top: 0;
	right: 5px;
}

#customize-control-ast-dynamic-single-post-banner-featured-overlay .ast-control-wrap {
	display: block;
}

.ast-sortable-item .customize-control-ast-color .astra-color-picker-wrap {
	position: relative;
	top: 8px;
}

.ast-sortable-subfields-wrap .customize-control-ast-description {
	line-height: 1;
}

.customize-control-ast-list-icons .ahfb-sorter-item-panel-header .dashicons-admin-page {
	margin-right: 0;
}

.customize-control-ast-text-input textarea {
	display: block;
	width: 100%;
}

.customize-control-ast-toggle-control .components-toggle-control .components-base-control__field,
.ast-togglecontrol-wrapper .components-base-control__field {
	margin: 0;
}

.customize-control-ast-toggle-control .components-toggle-control .components-form-toggle,
.customize-control-ast-link .ast-togglecontrol-wrapper .components-form-toggle,
.customize-control-ast-toggle .components-toggle-control .components-form-toggle {
	margin: 0;
	order: 2;
}

.customize-control-ast-link .ast-togglecontrol-wrapper .components-base-control__field {
	margin: 24px 0 0;
}

.ast-togglecontrol-wrapper {
	position: relative;
}

.ast-togglecontrol-wrapper .toggle-control-label,
.ast-togglecontrol-wrapper .components-toggle-control__label {
	font-size: 13px;
	line-height: 18px;
}

.customize-control-ast-toggle-control .components-toggle-control .components-toggle-control__label,
.ast-togglecontrol-wrapper label {
	display: flex;
	flex: 1 1 auto;
	font-size: 13px;
	line-height: 18px;
}

.ast-switch ~ label {
	position: relative;
	display: block;
	width: 36px;
	height: 16px;
	color: transparent;
	border-radius: 50px;
	cursor: pointer;
	transition: all 0.2s ease-in;
	border: 1px solid var( --ast-customizer-color-8 );
	box-sizing: border-box;
}

.ast-switch ~ label:after {
	content: "";
	position: absolute;
	top: 2px;
	left: 3px;
	display: block;
	width: 10px;
	height: 10px;
	border-radius: 50px;
	background: var( --ast-customizer-color-8 );
	transition: all 0.2s ease-in;
}

.ast-switch:checked ~ label {
	background: var( --ast-customizer-color-1 );
	border-color: var( --ast-customizer-color-1 );
}

.ast-switch:checked ~ label:after {
	transform: translateX( 19px );
	background: var( --ast-customizer-color-9 );
}

.is-checked .ast-switch ~ .ast-label {
	border-color: var( --ast-customizer-color-1 );
}

.ast-switch:disabled ~ label {
	background: #f0f0f0;
	pointer-events: none;
}

.ast-switch:disabled ~ label:after {
	background: #d1d1d1;
	box-shadow: 0 1px 2px 0 rgba( 72, 72, 72, 0.5 );
}

.ast-switch:not( :checked ),
.ast-switch:checked {
	opacity: 0;
}

.customize-control-ast-section-toggle .components-form-toggle {
	margin-left: 5px;
	text-align: right;
}

/* Wordpress 6.1 Compatibility CSS for toggle UI */
.components-toggle-control .components-base-control__field {
	display: flex;
	margin-bottom: 12px;
	line-height: initial;
	align-items: center;
}

.customize-control-ast-toggle-control .components-toggle-control input.ast-checkbox {
	appearance: none;
	-webkit-appearance: none;
	position: relative;
	opacity: 1;
	width: 16px;
	height: 16px;
	background-color: transparent;
	border: 1px solid var( --ast-customizer-color-7 );
	box-shadow: none;
}

.customize-control-ast-toggle-control .components-toggle-control input.ast-checkbox:checked {
	background-color: var( --ast-customizer-color-1 );
	border-color: var( --ast-customizer-color-3 );
}

.customize-control-ast-toggle-control .components-toggle-control input.ast-checkbox:checked::before {
	content: "";
	background: url( "data:image/svg+xml,%3Csvg width='12' height='9' viewBox='0 0 12 9' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 5.2L5.2 8L10.8 1' stroke='white'/%3E%3C/svg%3E" ) no-repeat center;
	width: 100%;
	height: 100%;
	margin: 0;
	transition: background-image 0.25s ease-in-out;
}

.customize-control-ast-toggle-control .components-toggle-control input.ast-checkbox:hover:checked::before {
	background-image: url( "data:image/svg+xml,%3Csvg width='8' height='2' viewBox='0 0 8 2' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect y='0.5' width='8' height='1' fill='white'/%3E%3C/svg%3E" );
}

@keyframes tooltips-vert {
	to {
		opacity: 1;
		transform: translate( -50%, 0 );
	}
}

@keyframes tooltips-horz {
	to {
		opacity: 1;
		transform: translate( 0, -50% );
	}
}

.components-tooltip,
.ast-tooltip[data-title]::before,
.ast-tooltip[data-title]::after {
	color: var( --ast-customizer-secondary-text-color );
	background: var( --ast-customizer-primary-color );
}

#customize-control-astra-typography-presets .ast-tooltip[data-title]::after {
	min-width: 10em;
}

.ast-tooltip {
	position: absolute;
	top: 9px;
	bottom: 0;
	left: 0;
	right: 0;
	width: 100%;
	height: 100%;
}

.ast-radio-img-svg .ast-tooltip {
	z-index: 99999999;
}
.dashicons-editor-help[data-title] {
	z-index: 1;
}

.dashicons-editor-help[data-title] + .ast-tooltip {
	display: none;
	opacity: 0;
}

.ast-tooltip[data-title]::before,
.ast-tooltip[data-title]::after {
	display: none;
	position: absolute;
	left: 50%;
	transform: translate( -50%, -0.5em );
	user-select: none;
	pointer-events: none;
	opacity: 1 !important;
	z-index: 1000;
}

.ast-tooltip[data-title]::before {
	content: "";
	bottom: calc( 100% + 6px );
	width: 12px;
	height: 5px;
	-webkit-mask: url( "data:image/svg+xml,%3Csvg width='12' height='5' viewBox='0 0 12 5' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6 5.00012L12 0.00012207H0L6 5.00012Z' fill='%23000000'/%3E%3C/svg%3E" );
	mask: url( "data:image/svg+xml,%3Csvg width='12' height='5' viewBox='0 0 12 5' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6 5.00012L12 0.00012207H0L6 5.00012Z' fill='%23000000'/%3E%3C/svg%3E" );
}

.dashicons-editor-help[data-title] + .ast-tooltip[data-title]::before {
	right: -2px;
	left: initial;
}

.customize-control-ast-toggle-control .dashicons-editor-help[data-title] + .ast-tooltip[data-title]::before {
	right: 38px;
}

.ast-tooltip[data-title]::after {
	content: attr( data-title );
	bottom: calc( 100% + 10px );
	min-width: 3em;
	max-width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: 8px;
	border-radius: 4px;
	text-align: center;
	text-transform: none;
	font-size: 10px;
	line-height: 16px;
	inline-size: max-content;
}

.ast-tooltip[data-title]:hover::before,
.ast-tooltip[data-title]:hover::after,
.dashicons-editor-help[data-title]:hover ~ .ast-tooltip,
.dashicons-editor-help[data-title]:hover ~ .ast-tooltip[data-title]::before,
.dashicons-editor-help[data-title]:hover ~ .ast-tooltip[data-title]::after {
	display: block;
	white-space: break-spaces;
	opacity: 1;
}

.ast-top-dotted-divider .ast-control-tooltip.dashicons-editor-help {
	top: 22px;
}

.customize-control.ast-bottom-dotted-divider.customize-control-ast-toggle-control .ast-control-tooltip {
	top: 8px;
}

.customize-control.customize-control-ast-select.ast-top-dotted-divider .ast-control-tooltip {
	top: 36px;
}

.customize-control-ast-sortable .customize-control-ast-selector .dashicons.ast-control-tooltip {
	top: 18%;
	padding: 0;
	font-size: 20px;
}

.customize-control.customize-control-ast-color i.ast-control-tooltip {
	top: 4px;
	right: 60px;
}

#customize-control-astra-settings-header-logo-color .dashicons-editor-help[data-title]+.ast-tooltip[data-title]::before {
	right: 58px;
}
#customize-control-ast-dynamic-single-post-article-featured-image-position-layout-2 .dashicons-editor-help[data-title]+.ast-tooltip[data-title]::before, #customize-control-ast-dynamic-single-page-article-featured-image-position-layout-1 .dashicons-editor-help[data-title]+.ast-tooltip[data-title]::before {
	right: 4px;
	transform: rotate(180deg);
	top: 7px;
}

#customize-control-ast-dynamic-single-post-article-featured-image-position-layout-2 .ast-tooltip[data-title]::after, #customize-control-ast-dynamic-single-page-article-featured-image-position-layout-1 .ast-tooltip[data-title]::after {
	bottom: calc(-55% + 10px);
}
span.select2-container {
	width: 100% !important;
	font-size: 13px;
	line-height: 16px;
}

span.select2-container.select2-container--default.select2-container--open {
	z-index: 999999;
}

span.select2-container.select2-container--default.select2-container--open li.select2-results__option {
	margin: 0;
}

.select2-container .select2-selection.select2-selection--single {
	display: flex;
	align-items: center;
}

.select2-selection__rendered li {
	margin-bottom: 0;
}

.select2-container--default .select2-selection--single,
.select2-container--default.select2-container .select2-selection--multiple {
	border-color: #ddd;
	border-radius: 0;
}

.select2-dropdown,
.select2-container--default .select2-selection--multiple .select2-selection__choice {
	border-color: #ddd;
	border-radius: 0;
}

.customize-control.customize-control-ast-hidden {
	margin-top: 0;
}

/* Vetically align customizer footer device icons */
.wp-full-overlay-footer .devices-wrapper .preview-desktop,
.wp-full-overlay-footer .devices-wrapper .preview-tablet,
.wp-full-overlay-footer .devices-wrapper .preview-mobile {
	vertical-align: middle;
}

.customize-control-ast-font-variant .ast-variant-select input#react-select-2-input:focus {
	box-shadow: none;
}

.ast-variant-select [CLASS*="-indicatorContainer"] {
	padding: 2px;
	display: contents;
}

.ast-variant-select [CLASS*="-multiValue"] > div:first-child {
	padding: 0;
}

.ast-units-wrapper {
	position: absolute;
	top: 0;
	right: 0;
	margin: 0;
}

.ast-units-wrapper .ast-unit-dropdown {
	all: unset;
	display: flex;
	align-items: center;
	gap: 2px;
	font-size: 12px;
	line-height: 16px;
	font-weight: 400;
	text-transform: lowercase;
	padding: 2px 5px;
	border-radius: 3px;
	transition: background .25s ease-in-out;
	cursor: pointer;
}

.ast-units-wrapper .ast-unit-dropdown .unit-text {
	margin-bottom: 3px;
}

.ast-units-wrapper .ast-unit-dropdown:hover,
.ast-units-wrapper.dropdown-active .ast-unit-dropdown {
	background: var( --ast-customizer-icon-background );
}

.ast-units-wrapper .ast-unit-dropdown-menu {
	position: absolute;
	border-radius: 3px;
	background: var( --ast-customizer-global-background );
	box-shadow: 0 0 4px 0 rgba( 0, 0, 0, 0.25 );
	z-index: 10;
	top: -12px;
	right: 5px;
}

.ast-units-wrapper .ast-unit-dropdown-menu .single-unit {
	text-align: center;
	font-size: 10px;
	line-height: 16px;
	letter-spacing: 0.5px;
	text-transform: lowercase;
	padding: 4px 9px;
	margin: 0;
	cursor: pointer;
}

.ast-units-wrapper .ast-unit-dropdown-menu .single-unit.active {
	color: var( --ast-customizer-color-1 );
}

.ast-upgrade-pro-wrap .ast-upgrade-trigger,
.ast-upgrade-pro-wrap .ast-upgrade-pro-innerwrap {
	width: 100%;
	-js-display: inline-flex;
	display: inline-flex;
	align-items: center;
	flex-wrap: wrap;
	align-content: center;
	position: relative;
	overflow: hidden;
	justify-content: center;
}

.ast-upgrade-pro-wrap .ast-upgrade-cta {
	position: absolute;
	top: auto;
	left: auto;
	width: auto;
	height: 28px;
	border-radius: 4px;
	padding: 0 8px 0 5px;
	font-size: 1em;
	line-height: 1em;
	display: inline-flex;
	color: var( --ast-customizer-color-1 );
	background: var( --ast-customizer-color-9 );
	border: 1px solid var( --ast-customizer-color-1 );
	align-items: center;
	align-content: center;
	white-space: nowrap;
}

a.ast-upgrade-trigger:focus,
a.ast-upgrade-trigger:active {
	box-shadow: none;
	border: none;
	outline: none;
}

.ast-upgrade-pro-wrap .ast-upgrade-section-title {
	margin-left: 8px;
}

.ast-upgrade-pro-wrap .ast-upgrade-pro-innerwrap > svg {
	width: 100%;
	height: 100%;
	filter: blur( 4px );
}

.ast-upgrade-pro-wrap .ast-upgrade-cta svg {
	width: 1.2em;
	height: 1.2em;
}

/** Radio image upgrade CSS */
.customize-control-ast-radio-image .ast-pro-option > *:not( .image-clickable ) {
	opacity: 0.4;
}

.ast-radio-img-svg.ast-pro-option .image-clickable::before {
	display: inline-flex;
	position: absolute;
	top: 36%;
	left: 41%;
	content: "";
	background-size: 1.5em 1.5em;
	height: 1.5em;
	width: 1.5em;
	border: 1px solid var( --ast-customizer-color-1 );
	background-image: url( "data:image/svg+xml,%3Csvg width='17' height='16' viewBox='0 0 17 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12.5002 7.2001H11.7002V4.8001C11.7002 3.0401 10.2602 1.6001 8.5002 1.6001C6.7402 1.6001 5.3002 3.0401 5.3002 4.8001V7.2001H4.5002C4.1002 7.2001 3.7002 7.6001 3.7002 8.0001V13.6001C3.7002 14.0001 4.1002 14.4001 4.5002 14.4001H12.5002C12.9002 14.4001 13.3002 14.0001 13.3002 13.6001V8.0001C13.3002 7.6001 12.9002 7.2001 12.5002 7.2001ZM9.3002 12.8001H7.7002L8.0202 11.0401C7.6202 10.8801 7.3002 10.4001 7.3002 10.0001C7.3002 9.3601 7.8602 8.8001 8.5002 8.8001C9.1402 8.8001 9.7002 9.3601 9.7002 10.0001C9.7002 10.4801 9.4602 10.8801 8.9802 11.0401L9.3002 12.8001ZM10.1002 7.2001H6.9002V4.8001C6.9002 3.9201 7.6202 3.2001 8.5002 3.2001C9.3802 3.2001 10.1002 3.9201 10.1002 4.8001V7.2001Z' fill='%230284C7'/%3E%3C/svg%3E" );
}

.ast-upgrade-list-wrapper {
	text-align: center;
}

.ast-upgrade-list-wrapper .ast-brand-logo {
	margin: 0;
}

.ast-upgrade-list-wrapper svg {
	width: 174px;
	height: 90px;
}

.ast-upgrade-list-items {
	margin-bottom: 1.8em;
}

.ast-upgrade-list-wrapper .ast-upgrade-list-section-title {
	margin: 0.5em auto 1.8em;
	max-width: 240px;
}

.ast-pro-upgrade-item {
	display: flex;
	align-items: center;
	gap: 5px;
}

.ast-pro-upgrade-item svg {
	vertical-align: middle;
}

.ast-pro-upgrade-item span {
	display: inline;
}

.ast-pro-upgrade-item > span:first-child {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
}

.ast-upgrade-list-description {
	margin: 5px 0 0 20px;
}

.ast-upgrade-list-title {
	font-weight: 600;
}

.ahfb-header-builder-active li#customize-control-astra-settings-builder-header:not( .ast-pro-available ),
.ahfb-header-builder-active li#customize-control-astra-settings-builder-header:not( .ast-pro-available ) *,
.ahfb-footer-builder-active li#customize-control-astra-settings-builder-footer:not( .ast-pro-available ),
.ahfb-footer-builder-active li#customize-control-astra-settings-builder-footer:not( .ast-pro-available ) * {
	color: var( --ast-customizer-color-9 );
	background: var( --ast-customizer-color-1 );
}

.customize-control .button.ahfb-builder-section-shortcut {
	border-left: 1px solid #ddd;
	border-right: 1px solid #ddd;
}

.ast-customize-control-title a.ast-builder-upgrade-link {
	background: var( --ast-customizer-color-9 ) !important;
	color: var( --ast-customizer-color-1 ) !important;
	box-shadow: 0px 4px 8px -4px rgba( 0, 0, 0, 0.16 );
	margin-left: 10px;
	border-radius: 4px;
	padding: 3px 10px 5px;
	text-decoration: none;
	vertical-align: top;
}

.ast-pro-upgrade-item p {
	margin-top: 13px;
	margin-bottom: 8px;
	line-height: 1;
}

.ast-upgrade-list-items .ast-pro-upgrade-item:not( :last-child ) {
	border-bottom: 1px dashed #d4d4d4;
}

li#customize-control-astra-settings-header-builder-pro-items .ast-upgrade-pro-wrap:not( :has( .ast-upgrade-modal-view-wrapper ) ),
li#customize-control-astra-settings-footer-builder-pro-items .ast-upgrade-pro-wrap:not( :has( .ast-upgrade-modal-view-wrapper ) ) {
	padding-left: 12px;
	padding-right: 12px;
}

/* Styling for SVG Icon Control's icon lbrary picker */
.customize-control-ast-svg-icon-selector .icon-library-picker input[type="tel"] {
	max-width: 40px;
}

/* Styling for SVG Icon Control's textarea */
.customize-control-ast-svg-icon-selector .custom-svg-icon-wrapper textarea {
	width: 100%;
	margin: 8px 0;
	height: 100px;
}

/* Modal View CSS */
.ast-upgrade-modal-view-wrapper {
	display: flex;
	gap: 8px;
	padding: 16px;
	border: 1px solid #E7E0FA;
	border-radius: 4px;
	font-size: 13px;
	line-height: 20px;
	cursor: pointer;
	background: #FAF9FE;
}

.ast-upgrade-pro-wrap .ast-lock-icon {
	margin-top: 2px;
}

.ast-upgrade-text svg {
	position: relative;
	top: 3px;
}

.ast-upgrade-modal-view-wrapper:hover {
	background: #F4F0FD;
	border-color: #BEABF2;
}

.customize-control.customize-control-ast-upgrade:has( .ast-upgrade-modal-view-wrapper ) {
	bottom: 0;
	position: relative;
}

.customize-control.customize-control-ast-upgrade:has( .ast-upgrade-modal-view-wrapper )::before {
	background: transparent;
}

.ast-upgrade-modal {
	--ast-modal-action-color: #5c2ede;
}

.ast-upgrade-modal.components-modal__screen-overlay {
	background: #00000080;
}

.ast-upgrade-modal .components-modal__content {
	width: 400px;
	padding: 24px;
	border-radius: 8px;
}

.ast-upgrade-modal .components-modal__content > div {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.ast-upgrade-modal .components-modal__content .ast-modal-svg {
	margin: auto;
}

.ast-upgrade-modal .components-modal__content .ast-modal-content-heading {
	font-weight: 600;
	font-size: 16px;
	line-height: 26px;
	letter-spacing: 0.2px;
	margin: 0 0 8px;
}

.ast-upgrade-modal .components-modal__content .ast-modal-content-description {
	font-weight: 400;
	font-size: 13px;
	line-height: 22px;
	margin: 0;
	margin-bottom: 10px;
}

.ast-upgrade-modal .components-modal__content .ast-modal-feature-items {
	display: grid;
	grid-template-columns: repeat( 2, 1fr );
	gap: 12px;
	margin: 0;
}

.ast-upgrade-modal .components-modal__content .ast-modal-feature-items .ast-pro-upgrade-item {
	margin-bottom: 0;
}

.ast-upgrade-modal .components-modal__content .ast-modal-spacer {
	height: 0px;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions {
	display: flex;
	align-items: center;
	gap: 12px;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions .ast-modal-plan-wrapper {
	flex-grow: 1;
	border: 1px solid var(--ast-customizer-icon-background);
	border-radius: 6px;
	overflow: hidden;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions .ast-modal-plan-wrapper:hover {
	border-color: #d1d5db;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions .ast-modal-plan-wrapper div {
	margin-bottom: 0;
	border: none;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions .ast-modal-plan-wrapper .components-input-control__container,
.ast-upgrade-modal .components-modal__content .ast-modal-actions .ast-modal-price-wrapper,
.ast-upgrade-modal .components-modal__content .ast-modal-actions .ast-modal-buy-now-button {
	border-radius: 6px;
	font-size: 13px;
	line-height: 18px;
	min-height: 40px;
	min-width: 83px;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions .components-input-control__container {
	padding-top: 4px;
	padding-bottom: 4px;
	color: var( --ast-customizer-text-color );
	background: var( --ast-customizer-global-background );
	border-color: var( --ast-customizer-border-color );
	font-weight: 600;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions select {
	padding: 0 10px;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions:has( select:focus ) .components-input-control__backdrop {
	border-color: var( --ast-customizer-primary-color ) !important;
	box-shadow: none;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions .ast-modal-price-wrapper {
	background: var( --ast-customizer-alternate-background );
	padding: 11px;
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions .ast-modal-buy-now-button {
	background: var( --ast-modal-action-color );
}

.ast-upgrade-modal .components-modal__content .ast-modal-actions .ast-modal-buy-now-button:hover {
	background: #4E26BF;
}

#customize-control-astra-settings-site-layout-outside-bg-obj-responsive,#customize-control-astra-settings-content-bg-obj-responsive, #customize-control-astra-settings-theme-color-divider-reset {
    margin-top: 8px;
}

/* container spacing adjustmenet  */
#customize-control-astra-settings-site-layout {
	margin-bottom: 0;
}

/* container spacing adjustmenet  */
#customize-control-astra-settings-site-layout {
	margin-bottom: 0;
}

#customize-control-astra-settings-ast-dynamic-single-page-layout, #customize-control-astra-settings-archive-post-ast-content-layout, #customize-control-astra-settings-archive-post-content-structure-divider, #customize-control-astra-settings-blog-pagination, #customize-control-astra-settings-ast-search-content-layout, #customize-control-astra-settings-woocommerce-ast-content-layout, #customize-control-astra-settings-shop-grids {
    margin-bottom: 0;
}

#customize-control-astra-settings-section-single-page-padding, #customize-control-astra-settings-section-blog-single-spacing-divider, #customize-control-astra-settings-header-menu1-text-colors {
  margin-top: 16px;
}

#customize-control-astra-settings-section-hb-html-1-typography, #customize-control-astra-settings-mobile-header-toggle-btn-style, #customize-control-astra-settings-shop-hover-style, #customize-control-astra-settings-breadcrumb-bg-color, #customize-control-astra-settings-breadcrumb-spacing, #customize-control-astra-settings-site-sidebar-width, #customize-control-astra-settings-font-size-sidebar-content, #customize-control-astra-settings-shop-grids, #customize-control-woocommerce_shop_page_display {
margin-top: 0px;
}

#customize-control-astra-settings-section-header-mobile-menu-margin, #customize-control-astra-settings-font-size-section-footer-copyright {
	margin-top: 0;
}

#customize-control-astra-settings-mobile-header-toggle-btn-style, #customize-control-astra-settings-woo-header-cart-icon-radius-fields{
	margin-bottom: 0;
}

#customize-control-astra-settings-header-menu2-submenu-border, #customize-control-astra-settings-header-menu1-submenu-border, #customize-control-astra-settings-font-size-sidebar-title {
padding-bottom: 16px;
}

#customize-control-astra-settings-shop-product-structure, #customize-control-astra-settings-sidebar-spacing-divider {
margin-top: 16px;
}

#customize-control-astra-settings-header-search-width, #customize-control-astra-settings-enable-related-posts-excerpt,#customize-control-astra-settings-ast-search-live-search-post-types, #customize-control-astra-settings-single-product-sticky-product-image {
margin-top: 24px;
}

 #customize-control-astra-settings-archive-product-content-style {
  padding-top:16px;
}

#customize-control-astra-settings-header-social-1-bg-space, #customize-control-astra-settings-header-social-1-space, #customize-control-astra-settings-header-social-1-size, #customize-control-astra-settings-header-menu2-submenu-border-radius-fields,  #customize-control-astra-settings-shop-grids {
padding-bottom: 16px;
}

#customize-control-astra-settings-section-single-page-padding {
	margin-bottom: 0;
}


