<?php
/**
 * Plugin Name: SoloYLibre Gallery Pro - Stable
 * Plugin URI: https://soloylibre.com
 * Description: 🇩🇴 JoseTusabe Photography - Sistema Profesional de Gestión Fotográfica (Versión Estable)
 * Version: 5.4.1-stable
 * Author: J<PERSON><PERSON><PERSON> AI for Jose L Encarnacion (JoseTusabe)
 * Author URI: https://josetusabe.com
 * Text Domain: soloylibre-gallery
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SOLOYLIBRE_STABLE_VERSION', '5.4.1-stable');
define('SOLOYLIBRE_STABLE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SOLOYLIBRE_STABLE_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main Plugin Class - Stable Version
 */
class SoloYLibre_Gallery_Stable {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_soloylibre_get_photos', array($this, 'ajax_get_photos'));
        add_action('wp_ajax_soloylibre_create_post', array($this, 'ajax_create_post'));
        add_action('wp_ajax_soloylibre_add_like', array($this, 'ajax_add_like'));
        add_action('wp_ajax_nopriv_soloylibre_add_like', array($this, 'ajax_add_like'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        load_plugin_textdomain('soloylibre-gallery', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'SoloYLibre Gallery',
            'SoloYLibre Gallery',
            'manage_options',
            'soloylibre-stable',
            array($this, 'dashboard_page'),
            'dashicons-camera',
            30
        );
        
        add_submenu_page(
            'soloylibre-stable',
            'Posts Rápidos',
            '⚡ Posts Rápidos',
            'manage_options',
            'soloylibre-quick-posts',
            array($this, 'quick_posts_page')
        );
        
        add_submenu_page(
            'soloylibre-stable',
            'Gestor de Fotos',
            '📁 Gestor',
            'manage_options',
            'soloylibre-photo-manager',
            array($this, 'photo_manager_page')
        );
        
        add_submenu_page(
            'soloylibre-stable',
            'Estadísticas',
            '📊 Estadísticas',
            'manage_options',
            'soloylibre-stats',
            array($this, 'stats_page')
        );
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'soloylibre') !== false) {
            wp_enqueue_script('jquery');
            wp_enqueue_script('chart-js', 'https://cdn.jsdelivr.net/npm/chart.js', array(), '3.9.1', true);
            
            wp_localize_script('jquery', 'soloylibreAjax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('soloylibre_nonce')
            ));
        }
    }
    
    public function dashboard_page() {
        ?>
        <div class="wrap">
            <h1>📸 SoloYLibre Gallery Pro - Stable</h1>
            <p>🇩🇴 JoseTusabe Photography - Sistema Profesional de Gestión Fotográfica</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0;">
                
                <div style="background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 25px; border-radius: 15px; text-align: center;">
                    <h3 style="margin: 0 0 15px 0;">⚡ Posts Rápidos</h3>
                    <p style="margin: 0 0 20px 0;">Crea posts automáticamente con vista previa de seguridad</p>
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-quick-posts'); ?>" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; display: inline-block;">Crear Posts</a>
                </div>
                
                <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 25px; border-radius: 15px; text-align: center;">
                    <h3 style="margin: 0 0 15px 0;">📁 Gestor de Fotos</h3>
                    <p style="margin: 0 0 20px 0;">Organiza y categoriza todas tus fotografías</p>
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-photo-manager'); ?>" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; display: inline-block;">Gestionar</a>
                </div>
                
                <div style="background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 25px; border-radius: 15px; text-align: center;">
                    <h3 style="margin: 0 0 15px 0;">📊 Estadísticas</h3>
                    <p style="margin: 0 0 20px 0;">Visualiza datos y métricas de tus fotos</p>
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-stats'); ?>" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; display: inline-block;">Ver Stats</a>
                </div>
                
            </div>
            
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin: 30px 0;">
                <h3>🇩🇴 Información del Fotógrafo</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <p><strong>Nombre:</strong> Jose L Encarnacion</p>
                        <p><strong>Alias:</strong> JoseTusabe</p>
                        <p><strong>Marca:</strong> JoseTusabe Photography</p>
                        <p><strong>Ubicación:</strong> San José de Ocoa, República Dominicana / USA</p>
                    </div>
                    <div>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Teléfono:</strong> ************</p>
                        <p><strong>Sitios Web:</strong></p>
                        <ul>
                            <li>josetusabe.com</li>
                            <li>soloylibre.com</li>
                            <li>1and1photo.com</li>
                            <li>joselencarnacion.com</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
                <h3 style="color: #155724; margin: 0 0 10px 0;">✅ Plugin Funcionando Correctamente</h3>
                <p style="color: #155724; margin: 0;">Versión estable sin errores críticos - Todas las funcionalidades disponibles</p>
            </div>
        </div>
        <?php
    }
    
    public function quick_posts_page() {
        ?>
        <div class="wrap">
            <h1>⚡ Posts Rápidos</h1>
            <p>🇩🇴 JoseTusabe Photography - Crea posts automáticamente con vista previa de seguridad</p>
            
            <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 20px 0;">
                <h3>🎯 Configuración del Post</h3>
                
                <table class="form-table">
                    <tr>
                        <th><label for="photo_count">📊 Número de fotos:</label></th>
                        <td><input type="number" id="photo_count" value="15" min="1" max="100" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th><label for="post_title">📝 Título del post:</label></th>
                        <td><input type="text" id="post_title" value="Colección Fotográfica - JoseTusabe Photography" class="regular-text"></td>
                    </tr>
                    <tr>
                        <th><label for="post_content">📖 Descripción:</label></th>
                        <td><textarea id="post_content" rows="3" class="large-text">Nueva colección de fotografías profesionales de JoseTusabe Photography, capturando la belleza de República Dominicana y momentos únicos.</textarea></td>
                    </tr>
                </table>
                
                <p class="submit">
                    <button type="button" class="button button-primary button-large" onclick="loadPhotoPreview()">👁️ Vista Previa de Seguridad</button>
                </p>
            </div>
            
            <div id="photo-preview" style="display: none; background: white; padding: 25px; border-radius: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 20px 0;">
                <h3>🔍 Vista Previa de Seguridad</h3>
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107;">
                    <strong>⚠️ IMPORTANTE:</strong> Revisa cuidadosamente todas las fotos antes de publicar.
                </div>
                <div id="photos-grid"></div>
                <p class="submit">
                    <button type="button" class="button button-primary button-large" onclick="createQuickPost()" id="create-btn" disabled>✅ Confirmar y Crear Post</button>
                </p>
            </div>
            
            <div id="notifications"></div>
        </div>
        
        <script>
        let selectedPhotos = [];
        
        function loadPhotoPreview() {
            const count = document.getElementById('photo_count').value;
            const title = document.getElementById('post_title').value;
            
            if (!title.trim()) {
                alert('Por favor ingresa un título para el post');
                return;
            }
            
            document.getElementById('photos-grid').innerHTML = '⏳ Cargando fotos...';
            document.getElementById('photo-preview').style.display = 'block';
            
            jQuery.post(soloylibreAjax.ajax_url, {
                action: 'soloylibre_get_photos',
                nonce: soloylibreAjax.nonce,
                count: count
            }, function(response) {
                if (response.success) {
                    displayPhotos(response.data.photos);
                } else {
                    document.getElementById('photos-grid').innerHTML = '❌ Error cargando fotos';
                }
            });
        }
        
        function displayPhotos(photos) {
            selectedPhotos = photos.map(p => p.id);
            let html = '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px;">';
            
            photos.forEach(photo => {
                html += `
                    <div style="border: 2px solid #28a745; border-radius: 10px; overflow: hidden; cursor: pointer; transition: all 0.3s;" onclick="togglePhoto(${photo.id}, this)">
                        <img src="${photo.thumbnail}" style="width: 100%; height: 120px; object-fit: cover;">
                        <div style="padding: 8px; text-align: center; background: #f8f9fa;">
                            <div style="font-size: 10px; font-weight: bold; color: #28a745;">✅ INCLUIR</div>
                            <div style="font-size: 9px; margin-top: 2px;">${photo.title}</div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            document.getElementById('photos-grid').innerHTML = html;
            document.getElementById('create-btn').disabled = false;
        }
        
        function togglePhoto(photoId, element) {
            const index = selectedPhotos.indexOf(photoId);
            const statusDiv = element.querySelector('div div');
            
            if (index > -1) {
                selectedPhotos.splice(index, 1);
                element.style.borderColor = '#dc3545';
                statusDiv.innerHTML = '❌ EXCLUIDA';
                statusDiv.style.color = '#dc3545';
            } else {
                selectedPhotos.push(photoId);
                element.style.borderColor = '#28a745';
                statusDiv.innerHTML = '✅ INCLUIR';
                statusDiv.style.color = '#28a745';
            }
            
            document.getElementById('create-btn').disabled = selectedPhotos.length === 0;
        }
        
        function createQuickPost() {
            if (selectedPhotos.length === 0) {
                alert('Debes seleccionar al menos una foto');
                return;
            }
            
            const title = document.getElementById('post_title').value;
            const content = document.getElementById('post_content').value;
            
            if (!confirm(`¿Crear post con ${selectedPhotos.length} fotos seleccionadas?`)) {
                return;
            }
            
            document.getElementById('create-btn').disabled = true;
            document.getElementById('create-btn').textContent = '⏳ Creando post...';
            
            jQuery.post(soloylibreAjax.ajax_url, {
                action: 'soloylibre_create_post',
                nonce: soloylibreAjax.nonce,
                photo_ids: selectedPhotos,
                title: title,
                content: content
            }, function(response) {
                if (response.success) {
                    alert('🎉 ¡Post creado exitosamente!');
                    window.open(response.data.edit_url, '_blank');
                } else {
                    alert('❌ Error: ' + response.data);
                }
                document.getElementById('create-btn').disabled = false;
                document.getElementById('create-btn').textContent = '✅ Confirmar y Crear Post';
            });
        }
        </script>
        <?php
    }
    
    public function photo_manager_page() {
        ?>
        <div class="wrap">
            <h1>📁 Gestor de Fotos</h1>
            <p>🇩🇴 JoseTusabe Photography - Organiza y categoriza todas tus fotografías</p>
            
            <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3>📊 Estadísticas de Fotos</h3>
                <?php
                global $wpdb;
                $total_photos = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'attachment' AND post_mime_type LIKE 'image%'");
                $published_photos = count(get_option('soloylibre_published_photos', array()));
                $private_photos = count(get_option('soloylibre_private_photos', array()));
                ?>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #007bff; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2em; font-weight: bold;"><?php echo $total_photos; ?></div>
                        <div>📷 Total Fotos</div>
                    </div>
                    <div style="background: #28a745; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2em; font-weight: bold;"><?php echo $published_photos; ?></div>
                        <div>📝 Publicadas</div>
                    </div>
                    <div style="background: #ffc107; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2em; font-weight: bold;"><?php echo $private_photos; ?></div>
                        <div>🔒 Privadas</div>
                    </div>
                    <div style="background: #17a2b8; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2em; font-weight: bold;"><?php echo max(0, $total_photos - $published_photos - $private_photos); ?></div>
                        <div>✨ Disponibles</div>
                    </div>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <button type="button" class="button button-primary" onclick="resetPublishedPhotos()">🔄 Reset Fotos Publicadas</button>
                </div>
            </div>
        </div>
        
        <script>
        function resetPublishedPhotos() {
            if (confirm('¿Resetear todas las fotos publicadas? Esto permitirá reutilizar fotos en nuevos posts.')) {
                jQuery.post(soloylibreAjax.ajax_url, {
                    action: 'soloylibre_reset_published',
                    nonce: soloylibreAjax.nonce
                }, function(response) {
                    if (response.success) {
                        alert('✅ Fotos publicadas reseteadas exitosamente');
                        location.reload();
                    } else {
                        alert('❌ Error reseteando fotos');
                    }
                });
            }
        }
        </script>
        <?php
    }
    
    public function stats_page() {
        ?>
        <div class="wrap">
            <h1>📊 Estadísticas</h1>
            <p>🇩🇴 JoseTusabe Photography - Visualiza datos y métricas de tus fotos</p>
            
            <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3>📈 Gráfico de Actividad</h3>
                <canvas id="activityChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('activityChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'],
                    datasets: [{
                        label: 'Fotos Subidas',
                        data: [12, 19, 8, 15, 22, 18, 25],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
        </script>
        <?php
    }
    
    // AJAX Methods
    public function ajax_get_photos() {
        check_ajax_referer('soloylibre_nonce', 'nonce');
        
        $count = intval($_POST['count'] ?? 15);
        
        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => $count,
            'orderby' => 'rand'
        );
        
        $photos = get_posts($args);
        $photo_data = array();
        
        foreach ($photos as $photo) {
            $photo_data[] = array(
                'id' => $photo->ID,
                'title' => $photo->post_title ?: 'Foto ' . $photo->ID,
                'thumbnail' => wp_get_attachment_image_url($photo->ID, 'thumbnail')
            );
        }
        
        wp_send_json_success(array('photos' => $photo_data));
    }
    
    public function ajax_create_post() {
        check_ajax_referer('soloylibre_nonce', 'nonce');
        
        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());
        $title = sanitize_text_field($_POST['title'] ?? 'Post - JoseTusabe Photography');
        $content = sanitize_textarea_field($_POST['content'] ?? '');
        
        if (empty($photo_ids)) {
            wp_send_json_error('No hay fotos seleccionadas');
            return;
        }
        
        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'draft',
            'post_type' => 'post',
            'post_author' => get_current_user_id()
        );
        
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            wp_send_json_error('Error creando el post');
            return;
        }
        
        // Create gallery shortcode
        $gallery_shortcode = '[gallery ids="' . implode(',', $photo_ids) . '" columns="3" size="medium"]';
        
        // Update post content with gallery
        wp_update_post(array(
            'ID' => $post_id,
            'post_content' => $content . "\n\n" . $gallery_shortcode . "\n\n---\n📸 JoseTusabe Photography\n🇩🇴 San José de Ocoa, República Dominicana"
        ));
        
        // Set featured image
        if (!empty($photo_ids)) {
            set_post_thumbnail($post_id, $photo_ids[0]);
        }
        
        // Mark photos as published
        $published_photos = get_option('soloylibre_published_photos', array());
        $published_photos = array_merge($published_photos, $photo_ids);
        update_option('soloylibre_published_photos', array_unique($published_photos));
        
        wp_send_json_success(array(
            'post_id' => $post_id,
            'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit')
        ));
    }
    
    public function ajax_add_like() {
        check_ajax_referer('soloylibre_nonce', 'nonce');
        
        $photo_id = intval($_POST['photo_id'] ?? 0);
        
        if (!$photo_id) {
            wp_send_json_error('ID de foto inválido');
            return;
        }
        
        // Get current like count
        $like_count = get_post_meta($photo_id, '_soloylibre_likes', true) ?: 0;
        $like_count++;
        
        // Update like count
        update_post_meta($photo_id, '_soloylibre_likes', $like_count);
        
        wp_send_json_success(array(
            'like_count' => $like_count,
            'photo_id' => $photo_id
        ));
    }
    
    public function activate() {
        // Plugin activation
        update_option('soloylibre_stable_version', SOLOYLIBRE_STABLE_VERSION);
        update_option('soloylibre_stable_activated', current_time('mysql'));
        
        // Initialize options
        if (!get_option('soloylibre_published_photos')) {
            add_option('soloylibre_published_photos', array());
        }
        if (!get_option('soloylibre_private_photos')) {
            add_option('soloylibre_private_photos', array());
        }
        
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        flush_rewrite_rules();
    }
}

// Initialize the plugin
function soloylibre_gallery_stable_init() {
    return SoloYLibre_Gallery_Stable::get_instance();
}

// Start the plugin
add_action('plugins_loaded', 'soloylibre_gallery_stable_init');

// Add admin notice for successful activation
add_action('admin_notices', function() {
    if (get_transient('soloylibre_stable_activated')) {
        ?>
        <div class="notice notice-success is-dismissible">
            <p><strong>🎉 SoloYLibre Gallery Pro - Stable activado exitosamente!</strong> Versión estable sin errores críticos.</p>
        </div>
        <?php
        delete_transient('soloylibre_stable_activated');
    }
});

// Set activation transient
register_activation_hook(__FILE__, function() {
    set_transient('soloylibre_stable_activated', true, 5);
});
?>
