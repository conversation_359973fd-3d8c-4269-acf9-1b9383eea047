<?php
/**
 * Specific Error Fix - SoloYLibre Gallery Plugin
 * Target and fix specific errors identified in the audit
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Enable error reporting but suppress output
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

echo "<h1>🔧 Reparación Específica de Errores</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Solucionando errores específicos identificados</p>";

// Load WordPress safely
try {
    if (!defined('ABSPATH')) {
        require_once('../../../wp-config.php');
    }
} catch (Exception $e) {
    die("❌ Error crítico: No se puede cargar WordPress");
}

$fixes_applied = array();
$errors_remaining = array();

echo "<h2>🎯 1. Reparando Errores de Plugin</h2>";

// Fix 1: Ensure plugin directory structure is correct
$plugin_dir = dirname(__FILE__);
$required_files = array(
    'soloylibre-gallery-stable.php' => 'Plugin principal'
);

foreach ($required_files as $file => $description) {
    $file_path = $plugin_dir . '/' . $file;
    if (!file_exists($file_path)) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Archivo faltante: $file";
        echo "</div>";
        $errors_remaining[] = "Archivo faltante: $file";
    } else {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Archivo encontrado: $file";
        echo "</div>";
    }
}

echo "<h2>⚙️ 2. Reparando Configuración PHP</h2>";

// Fix 2: Increase PHP limits temporarily
$original_memory = ini_get('memory_limit');
$original_time = ini_get('max_execution_time');

if (wp_convert_hr_to_bytes($original_memory) < wp_convert_hr_to_bytes('512M')) {
    ini_set('memory_limit', '512M');
    $fixes_applied[] = "Memory limit aumentado de $original_memory a 512M";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Memory limit aumentado de $original_memory a 512M";
    echo "</div>";
}

if ($original_time < 300 && $original_time != 0) {
    ini_set('max_execution_time', 300);
    $fixes_applied[] = "Max execution time aumentado de $original_time a 300s";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Max execution time aumentado de $original_time a 300s";
    echo "</div>";
}

echo "<h2>🗄️ 3. Reparando Base de Datos</h2>";

global $wpdb;

// Fix 3: Repair and optimize WordPress tables
$wp_tables = array(
    $wpdb->posts,
    $wpdb->postmeta,
    $wpdb->options,
    $wpdb->users,
    $wpdb->usermeta
);

foreach ($wp_tables as $table) {
    try {
        $result = $wpdb->query("REPAIR TABLE $table");
        if ($result !== false) {
            $fixes_applied[] = "Tabla reparada: $table";
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ Tabla reparada: $table";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "⚠️ No se pudo reparar tabla: $table";
        echo "</div>";
    }
}

echo "<h2>🔌 4. Reparando Conflictos de Plugins</h2>";

// Fix 4: Deactivate known problematic plugins temporarily
$active_plugins = get_option('active_plugins', array());
$problematic_plugins = array();

foreach ($active_plugins as $plugin) {
    // Check for old versions of our plugin
    if (strpos($plugin, 'Archive/soloylibre-gallery-plugin.php') !== false) {
        $problematic_plugins[] = $plugin;
    }
    // Check for other known problematic plugins
    if (strpos($plugin, 'jetpack') !== false && strpos($plugin, 'jetpack.php') !== false) {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "⚠️ Jetpack detectado - puede causar conflictos";
        echo "</div>";
    }
}

if (!empty($problematic_plugins)) {
    foreach ($problematic_plugins as $plugin) {
        $key = array_search($plugin, $active_plugins);
        if ($key !== false) {
            unset($active_plugins[$key]);
            $fixes_applied[] = "Plugin problemático desactivado: $plugin";
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ Plugin problemático desactivado: $plugin";
            echo "</div>";
        }
    }
    
    update_option('active_plugins', array_values($active_plugins));
}

echo "<h2>📁 5. Reparando Permisos de Archivos</h2>";

// Fix 5: Check and fix file permissions
$directories_to_check = array(
    WP_CONTENT_DIR => '755',
    WP_CONTENT_DIR . '/plugins' => '755',
    WP_CONTENT_DIR . '/uploads' => '755',
    dirname(__FILE__) => '755'
);

foreach ($directories_to_check as $dir => $required_perm) {
    if (is_dir($dir)) {
        $current_perm = substr(sprintf('%o', fileperms($dir)), -3);
        if ($current_perm !== $required_perm) {
            if (chmod($dir, octdec($required_perm))) {
                $fixes_applied[] = "Permisos corregidos: $dir ($current_perm -> $required_perm)";
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "✅ Permisos corregidos: " . basename($dir) . " ($current_perm -> $required_perm)";
                echo "</div>";
            } else {
                echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "⚠️ No se pudieron corregir permisos: " . basename($dir);
                echo "</div>";
            }
        } else {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ Permisos correctos: " . basename($dir) . " ($current_perm)";
            echo "</div>";
        }
    }
}

echo "<h2>🧹 6. Limpieza Profunda del Sistema</h2>";

// Fix 6: Clean up WordPress transients and expired data
$transients_deleted = 0;
$expired_transients = $wpdb->get_results("
    SELECT option_name 
    FROM {$wpdb->options} 
    WHERE option_name LIKE '_transient_timeout_%' 
    AND option_value < UNIX_TIMESTAMP()
");

foreach ($expired_transients as $transient) {
    $transient_name = str_replace('_transient_timeout_', '', $transient->option_name);
    delete_transient($transient_name);
    $transients_deleted++;
}

if ($transients_deleted > 0) {
    $fixes_applied[] = "Transients expirados eliminados: $transients_deleted";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Transients expirados eliminados: $transients_deleted";
    echo "</div>";
}

// Clean up autoload options
$autoload_options = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->options} 
    WHERE autoload = 'yes'
");

echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "ℹ️ Opciones autoload: $autoload_options";
echo "</div>";

if ($autoload_options > 1000) {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "⚠️ Muchas opciones autoload pueden afectar el rendimiento";
    echo "</div>";
}

echo "<h2>🔄 7. Reinicializando Plugin</h2>";

// Fix 7: Reinitialize plugin options with safe defaults
$plugin_options = array(
    'soloylibre_published_photos' => array(),
    'soloylibre_private_photos' => array(),
    'soloylibre_unwanted_photos' => array(),
    'soloylibre_stable_version' => '5.4.1-stable',
    'soloylibre_stable_settings' => array(
        'auto_feature_image' => true,
        'default_post_status' => 'draft',
        'photographer_name' => 'Jose L Encarnacion',
        'photographer_alias' => 'JoseTusabe',
        'photographer_brand' => 'JoseTusabe Photography',
        'photographer_email' => '<EMAIL>',
        'photographer_phone' => '************',
        'photographer_location' => 'San José de Ocoa, República Dominicana / USA'
    )
);

foreach ($plugin_options as $option => $default_value) {
    $current_value = get_option($option);
    if ($current_value === false) {
        add_option($option, $default_value);
        $fixes_applied[] = "Opción inicializada: $option";
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Opción inicializada: $option";
        echo "</div>";
    } else {
        echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "ℹ️ Opción ya existe: $option";
        echo "</div>";
    }
}

echo "<h2>🔍 8. Verificación Post-Reparación</h2>";

// Verification: Test basic WordPress functions
$verification_tests = array(
    'WordPress loaded' => defined('ABSPATH'),
    'Database connection' => $wpdb->get_var("SELECT 1") == 1,
    'Options table accessible' => get_option('blogname') !== false,
    'Plugin directory writable' => is_writable(dirname(__FILE__)),
    'Memory sufficient' => wp_convert_hr_to_bytes(ini_get('memory_limit')) >= wp_convert_hr_to_bytes('256M')
);

$tests_passed = 0;
foreach ($verification_tests as $test => $result) {
    if ($result) {
        $tests_passed++;
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ $test";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ $test";
        echo "</div>";
        $errors_remaining[] = $test;
    }
}

echo "<h2>📊 9. Resumen de Reparaciones</h2>";

$total_fixes = count($fixes_applied);
$total_errors = count($errors_remaining);
$success_rate = $tests_passed / count($verification_tests) * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_fixes</div>";
echo "<div>🔧 Reparaciones</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_errors</div>";
echo "<div>❌ Errores Restantes</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Éxito</div>";
echo "</div>";

echo "</div>";

if ($total_fixes > 0) {
    echo "<h3 style='color: #155724;'>✅ Reparaciones Aplicadas:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if ($total_errors > 0) {
    echo "<h3 style='color: #721c24;'>❌ Errores Que Requieren Atención Manual:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($errors_remaining as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// Final status
if ($success_rate >= 90) {
    echo "<div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #155724;'>🎉 ¡Reparación Exitosa!</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #155724;'>";
    echo "La mayoría de los errores han sido corregidos. El sistema debería funcionar correctamente ahora.";
    echo "</p>";
    echo "</div>";
} else {
    echo "<div style='background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #856404;'>⚠️ Reparación Parcial</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #856404;'>";
    echo "Algunos errores requieren atención manual. Revisa los errores listados arriba.";
    echo "</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='comprehensive-audit.php' style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔍 Ejecutar Audit Completo</a>";
echo "<a href='../../../wp-admin/plugins.php' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔧 Gestionar Plugins</a>";
echo "<a href='../../../wp-admin/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🏠 Dashboard</a>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Reparación específica completada</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";

// Save repair log
update_option('soloylibre_repair_log', array(
    'timestamp' => current_time('mysql'),
    'fixes_applied' => $fixes_applied,
    'errors_remaining' => $errors_remaining,
    'success_rate' => $success_rate
));
?>
