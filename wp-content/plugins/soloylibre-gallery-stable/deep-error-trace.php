<?php
/**
 * Deep Error Trace - SoloYLibre Gallery Plugin
 * Trace the exact source of the critical error
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Capture all output and errors
ob_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🕵️ Rastreo Profundo de Errores</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Identificando la fuente exacta del error</p>";

// Load WordPress with error tracking
try {
    if (!defined('ABSPATH')) {
        echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "🔄 Cargando WordPress con rastreo de errores...";
        echo "</div>";
        
        require_once('../../../wp-config.php');
        
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ WordPress cargado exitosamente";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Error crítico cargando WordPress: " . $e->getMessage();
    echo "<br><strong>Archivo:</strong> " . $e->getFile();
    echo "<br><strong>Línea:</strong> " . $e->getLine();
    echo "</div>";
    ob_end_flush();
    die();
}

echo "<h2>🔍 1. Verificación de Estado del Sistema</h2>";

// Check if we can access WordPress functions
$wp_functions_test = array(
    'get_bloginfo' => function_exists('get_bloginfo'),
    'get_option' => function_exists('get_option'),
    'wp_get_theme' => function_exists('wp_get_theme'),
    'get_plugins' => function_exists('get_plugins'),
    'is_admin' => function_exists('is_admin')
);

foreach ($wp_functions_test as $function => $exists) {
    if ($exists) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Función disponible: $function";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Función no disponible: $function";
        echo "</div>";
    }
}

echo "<h2>🔌 2. Análisis Detallado de Plugins</h2>";

// Get all plugins (not just active ones)
if (function_exists('get_plugins')) {
    $all_plugins = get_plugins();
    $active_plugins = get_option('active_plugins', array());
    
    echo "<h3>📋 Todos los Plugins Instalados:</h3>";
    
    foreach ($all_plugins as $plugin_file => $plugin_data) {
        $is_active = in_array($plugin_file, $active_plugins);
        $status_color = $is_active ? '#d4edda' : '#f8f9fa';
        $status_icon = $is_active ? '🟢' : '⚪';
        
        echo "<div style='background: $status_color; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "$status_icon <strong>" . $plugin_data['Name'] . "</strong> v" . $plugin_data['Version'];
        echo "<br><em>$plugin_file</em>";
        
        if ($is_active) {
            echo "<br><span style='color: #155724;'>ACTIVO</span>";
            
            // Check if this plugin might be causing issues
            $plugin_path = WP_PLUGIN_DIR . '/' . $plugin_file;
            if (file_exists($plugin_path)) {
                $plugin_content = file_get_contents($plugin_path);
                
                // Check for potential issues
                $issues = array();
                
                if (strpos($plugin_content, 'echo') !== false && strpos($plugin_content, 'add_action') !== false) {
                    $issues[] = "Contiene echo y add_action";
                }
                
                if (strpos($plugin_content, 'header(') !== false) {
                    $issues[] = "Modifica headers";
                }
                
                if (strpos($plugin_content, 'ob_start') !== false) {
                    $issues[] = "Usa output buffering";
                }
                
                if (strpos($plugin_content, 'wp_die') !== false) {
                    $issues[] = "Usa wp_die";
                }
                
                if (!empty($issues)) {
                    echo "<br><span style='color: #856404;'>⚠️ Posibles problemas: " . implode(', ', $issues) . "</span>";
                }
            }
        }
        echo "</div>";
    }
} else {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ No se puede acceder a la lista de plugins";
    echo "</div>";
}

echo "<h2>🎨 3. Análisis del Tema Activo</h2>";

if (function_exists('wp_get_theme')) {
    $current_theme = wp_get_theme();
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🎨 Tema Activo:</h3>";
    echo "<strong>Nombre:</strong> " . $current_theme->get('Name') . "<br>";
    echo "<strong>Versión:</strong> " . $current_theme->get('Version') . "<br>";
    echo "<strong>Directorio:</strong> " . $current_theme->get_stylesheet_directory() . "<br>";
    echo "</div>";
    
    // Check theme files for issues
    $theme_files = array(
        'functions.php',
        'header.php',
        'footer.php',
        'index.php'
    );
    
    $theme_dir = $current_theme->get_stylesheet_directory();
    
    foreach ($theme_files as $file) {
        $file_path = $theme_dir . '/' . $file;
        if (file_exists($file_path)) {
            $file_content = file_get_contents($file_path);
            
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "<strong>📄 $file:</strong> " . size_format(strlen($file_content));
            
            // Check for potential issues
            $issues = array();
            
            if (substr($file_content, 0, 3) === "\xEF\xBB\xBF") {
                $issues[] = "Contiene BOM";
            }
            
            if (preg_match('/^\s+<\?php/', $file_content)) {
                $issues[] = "Espacios antes de <?php";
            }
            
            if (preg_match('/\?>\s*\S/', $file_content)) {
                $issues[] = "Contenido después de ?>";
            }
            
            if (strpos($file_content, 'wp_die') !== false) {
                $issues[] = "Usa wp_die";
            }
            
            if (!empty($issues)) {
                echo "<br><span style='color: #dc3545;'>❌ Problemas: " . implode(', ', $issues) . "</span>";
            } else {
                echo " <span style='color: #28a745;'>✅</span>";
            }
            
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "⚠️ Archivo no encontrado: $file";
            echo "</div>";
        }
    }
}

echo "<h2>🗄️ 4. Verificación de Base de Datos</h2>";

global $wpdb;

try {
    // Test database connection with detailed info
    $db_version = $wpdb->get_var("SELECT VERSION()");
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Base de datos conectada - MySQL/MariaDB: $db_version";
    echo "</div>";
    
    // Check for database errors
    if (!empty($wpdb->last_error)) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Último error de DB: " . $wpdb->last_error;
        echo "</div>";
    }
    
    // Check WordPress tables
    $wp_tables = array('posts', 'options', 'users', 'postmeta', 'usermeta');
    foreach ($wp_tables as $table) {
        $full_table = $wpdb->prefix . $table;
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $full_table");
        
        if ($count !== null) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ Tabla $full_table: $count registros";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ Error accediendo a tabla: $full_table";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Error de base de datos: " . $e->getMessage();
    echo "</div>";
}

echo "<h2>🔧 5. Verificación de Configuración WordPress</h2>";

// Check WordPress constants
$wp_constants = array(
    'WP_DEBUG' => defined('WP_DEBUG') ? (WP_DEBUG ? 'true' : 'false') : 'no definido',
    'WP_DEBUG_LOG' => defined('WP_DEBUG_LOG') ? (WP_DEBUG_LOG ? 'true' : 'false') : 'no definido',
    'WP_DEBUG_DISPLAY' => defined('WP_DEBUG_DISPLAY') ? (WP_DEBUG_DISPLAY ? 'true' : 'false') : 'no definido',
    'SCRIPT_DEBUG' => defined('SCRIPT_DEBUG') ? (SCRIPT_DEBUG ? 'true' : 'false') : 'no definido',
    'WP_MEMORY_LIMIT' => defined('WP_MEMORY_LIMIT') ? WP_MEMORY_LIMIT : 'no definido',
    'WP_MAX_MEMORY_LIMIT' => defined('WP_MAX_MEMORY_LIMIT') ? WP_MAX_MEMORY_LIMIT : 'no definido'
);

foreach ($wp_constants as $constant => $value) {
    $color = ($value === 'no definido') ? '#fff3cd' : '#e3f2fd';
    echo "<div style='background: $color; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "<strong>$constant:</strong> $value";
    echo "</div>";
}

echo "<h2>🚨 6. Simulación del Error</h2>";

// Try to reproduce the error by testing different scenarios
echo "<h3>🧪 Test 1: Acceso directo al frontend</h3>";

try {
    $home_url = home_url();
    echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "🔗 URL del sitio: <a href='$home_url' target='_blank'>$home_url</a>";
    echo "</div>";
    
    // Test if we can make a request to the frontend
    $response = wp_remote_get($home_url, array('timeout' => 10));
    
    if (is_wp_error($response)) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Error accediendo al frontend: " . $response->get_error_message();
        echo "</div>";
    } else {
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code === 200) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ Frontend accesible - Código: $response_code";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ Frontend error - Código: $response_code";
            echo "</div>";
            
            // Check if response contains error message
            if (strpos($response_body, 'critical error') !== false) {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "🎯 <strong>ERROR CRÍTICO CONFIRMADO en el frontend</strong>";
                echo "</div>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ Excepción en test de frontend: " . $e->getMessage();
    echo "</div>";
}

echo "<h3>🧪 Test 2: Verificación de logs de error</h3>";

// Check WordPress debug log
$debug_log_path = WP_CONTENT_DIR . '/debug.log';
if (file_exists($debug_log_path)) {
    $log_size = filesize($debug_log_path);
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "📋 Debug log encontrado: " . size_format($log_size);
    echo "</div>";
    
    // Read last few lines of the log
    if ($log_size > 0) {
        $log_content = file_get_contents($debug_log_path);
        $log_lines = explode("\n", $log_content);
        $recent_lines = array_slice($log_lines, -10); // Last 10 lines
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📋 Últimas entradas del log:</h4>";
        echo "<pre style='background: #fff; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;'>";
        foreach ($recent_lines as $line) {
            if (!empty(trim($line))) {
                echo htmlspecialchars($line) . "\n";
            }
        }
        echo "</pre>";
        echo "</div>";
    }
} else {
    echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "ℹ️ No se encontró debug.log";
    echo "</div>";
}

echo "<h2>💡 7. Diagnóstico Final</h2>";

// Determine the most likely cause
$likely_causes = array();

// Check if any plugins are causing issues
$active_plugins = get_option('active_plugins', array());
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'Archive') !== false) {
        $likely_causes[] = "Plugin en directorio Archive puede estar causando conflictos";
    }
}

// Check PHP configuration
if (wp_convert_hr_to_bytes(ini_get('memory_limit')) < wp_convert_hr_to_bytes('256M')) {
    $likely_causes[] = "Memory limit PHP insuficiente: " . ini_get('memory_limit');
}

// Check WordPress debug settings
if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY) {
    $likely_causes[] = "WP_DEBUG_DISPLAY activado puede mostrar errores en frontend";
}

if (!empty($likely_causes)) {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎯 Causas Más Probables del Error:</h3>";
    echo "<ul>";
    foreach ($likely_causes as $cause) {
        echo "<li>$cause</li>";
    }
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>✅ No se identificaron causas obvias</h3>";
    echo "<p>El sistema parece estar funcionando correctamente desde el backend.</p>";
    echo "</div>";
}

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔧 Próximos Pasos Recomendados:</h3>";
echo "<ol>";
echo "<li><strong>Desactivar todos los plugins:</strong> Para aislar el problema</li>";
echo "<li><strong>Cambiar a tema por defecto:</strong> Twenty Twenty-Three o similar</li>";
echo "<li><strong>Verificar wp-config.php:</strong> Revisar configuración de debug</li>";
echo "<li><strong>Aumentar memory_limit:</strong> A 512M en php.ini</li>";
echo "<li><strong>Revisar logs del servidor:</strong> Apache/Nginx error logs</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🏠 Dashboard</a>";
echo "<a href='header-compatibility-check.php' style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔍 Check Headers</a>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Rastreo profundo completado</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";

// Save detailed error trace
update_option('soloylibre_error_trace', array(
    'timestamp' => current_time('mysql'),
    'wp_functions_available' => $wp_functions_test,
    'likely_causes' => $likely_causes,
    'active_plugins' => $active_plugins,
    'theme_info' => function_exists('wp_get_theme') ? wp_get_theme()->get('Name') : 'unknown'
));

ob_end_flush();
?>
