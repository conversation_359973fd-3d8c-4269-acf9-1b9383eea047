<?php
/**
 * Comprehensive Audit - SoloYLibre Gallery Plugin
 * Complete system audit to identify and fix all remaining errors
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Enable comprehensive error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start output buffering to catch any errors
ob_start();

echo "<h1>🔍 Auditoría Completa del Sistema</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Identificando y resolviendo todos los errores</p>";

// Try to load WordPress safely
try {
    if (!defined('ABSPATH')) {
        require_once('../../../wp-config.php');
    }
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ WordPress cargado correctamente";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Error crítico cargando WordPress: " . $e->getMessage();
    echo "</div>";
    die();
}

$errors_found = array();
$warnings_found = array();
$fixes_applied = array();

echo "<h2>🔧 1. Auditoría de Archivos PHP</h2>";

// Check all PHP files for syntax errors
$plugin_files = array(
    'soloylibre-gallery-stable.php' => 'Plugin principal estable',
    'final-verification.php' => 'Script de verificación',
    'comprehensive-audit.php' => 'Este script de auditoría'
);

foreach ($plugin_files as $file => $description) {
    $file_path = dirname(__FILE__) . '/' . $file;
    
    echo "<h3>📄 Auditando: $description</h3>";
    
    if (!file_exists($file_path)) {
        $errors_found[] = "Archivo no encontrado: $file";
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Archivo no encontrado: $file";
        echo "</div>";
        continue;
    }
    
    // Check file permissions
    if (!is_readable($file_path)) {
        $errors_found[] = "Archivo no legible: $file";
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Archivo no legible: $file";
        echo "</div>";
        continue;
    }
    
    // Check PHP syntax
    $output = array();
    $return_var = 0;
    exec("php -l " . escapeshellarg($file_path) . " 2>&1", $output, $return_var);
    
    if ($return_var !== 0) {
        $errors_found[] = "Error de sintaxis en $file: " . implode(' ', $output);
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ <strong>Error de sintaxis en $file:</strong><br>";
        echo "<pre style='background: #fff; padding: 10px; border-radius: 3px; margin: 5px 0;'>" . implode("\n", $output) . "</pre>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ $file - Sintaxis correcta";
        echo "</div>";
    }
    
    // Check file size
    $file_size = filesize($file_path);
    if ($file_size > 1024 * 1024) { // 1MB
        $warnings_found[] = "Archivo muy grande: $file (" . size_format($file_size) . ")";
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "⚠️ Archivo grande: $file (" . size_format($file_size) . ")";
        echo "</div>";
    }
}

echo "<h2>🗄️ 2. Auditoría de Base de Datos</h2>";

global $wpdb;

try {
    // Test basic database connection
    $db_test = $wpdb->get_var("SELECT 1");
    if ($db_test == 1) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Conexión a base de datos funcionando";
        echo "</div>";
    }
    
    // Check database charset
    $charset = $wpdb->get_var("SELECT @@character_set_database");
    if ($charset !== 'utf8mb4' && $charset !== 'utf8') {
        $warnings_found[] = "Charset de base de datos no óptimo: $charset";
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "⚠️ Charset de base de datos: $charset (recomendado: utf8mb4)";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Charset de base de datos: $charset";
        echo "</div>";
    }
    
    // Check WordPress tables
    $wp_tables = array('posts', 'options', 'users', 'usermeta', 'postmeta');
    foreach ($wp_tables as $table) {
        $full_table = $wpdb->prefix . $table;
        $exists = $wpdb->get_var("SHOW TABLES LIKE '$full_table'") == $full_table;
        if (!$exists) {
            $errors_found[] = "Tabla WordPress faltante: $full_table";
        }
    }
    
    // Check plugin options
    $plugin_options = array(
        'soloylibre_published_photos',
        'soloylibre_private_photos',
        'soloylibre_stable_version'
    );
    
    foreach ($plugin_options as $option) {
        $value = get_option($option);
        if ($value !== false) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ Opción encontrada: $option";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "⚠️ Opción no encontrada: $option (se creará automáticamente)";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    $errors_found[] = "Error de base de datos: " . $e->getMessage();
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ Error de base de datos: " . $e->getMessage();
    echo "</div>";
}

echo "<h2>⚙️ 3. Auditoría de Configuración PHP</h2>";

// Check PHP configuration
$php_config = array(
    'memory_limit' => array('current' => ini_get('memory_limit'), 'recommended' => '512M'),
    'max_execution_time' => array('current' => ini_get('max_execution_time'), 'recommended' => '300'),
    'upload_max_filesize' => array('current' => ini_get('upload_max_filesize'), 'recommended' => '64M'),
    'post_max_size' => array('current' => ini_get('post_max_size'), 'recommended' => '64M'),
    'max_input_vars' => array('current' => ini_get('max_input_vars'), 'recommended' => '3000')
);

foreach ($php_config as $setting => $values) {
    $current = $values['current'];
    $recommended = $values['recommended'];
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "<strong>$setting:</strong> $current";
    
    if ($setting === 'memory_limit') {
        $current_bytes = wp_convert_hr_to_bytes($current);
        $recommended_bytes = wp_convert_hr_to_bytes($recommended);
        if ($current_bytes < $recommended_bytes) {
            echo " <span style='color: #dc3545;'>⚠️ (Recomendado: $recommended)</span>";
            $warnings_found[] = "$setting muy bajo: $current (recomendado: $recommended)";
        } else {
            echo " <span style='color: #28a745;'>✅</span>";
        }
    } elseif ($setting === 'max_execution_time') {
        if (intval($current) < intval($recommended) && $current != 0) {
            echo " <span style='color: #dc3545;'>⚠️ (Recomendado: $recommended)</span>";
            $warnings_found[] = "$setting muy bajo: $current (recomendado: $recommended)";
        } else {
            echo " <span style='color: #28a745;'>✅</span>";
        }
    }
    
    echo "</div>";
}

echo "<h2>🔌 4. Auditoría de Plugins</h2>";

$active_plugins = get_option('active_plugins', array());
$all_plugins = get_plugins();

echo "<h3>📋 Plugins Activos:</h3>";
foreach ($active_plugins as $plugin_file) {
    if (isset($all_plugins[$plugin_file])) {
        $plugin_data = $all_plugins[$plugin_file];
        echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "🔌 <strong>" . $plugin_data['Name'] . "</strong> v" . $plugin_data['Version'];
        echo "<br><em>" . $plugin_file . "</em>";
        echo "</div>";
        
        // Check for known problematic plugins
        $problematic_plugins = array(
            'jetpack' => 'Jetpack',
            'wordfence' => 'Wordfence',
            'wp-super-cache' => 'WP Super Cache',
            'w3-total-cache' => 'W3 Total Cache',
            'wp-rocket' => 'WP Rocket'
        );
        
        foreach ($problematic_plugins as $problem_slug => $problem_name) {
            if (strpos($plugin_file, $problem_slug) !== false) {
                $warnings_found[] = "Plugin potencialmente problemático activo: $problem_name";
                echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "⚠️ Plugin potencialmente problemático: $problem_name";
                echo "</div>";
            }
        }
    }
}

// Check if our stable plugin is active
$our_plugin_active = false;
foreach ($active_plugins as $plugin_file) {
    if (strpos($plugin_file, 'soloylibre-gallery-stable') !== false) {
        $our_plugin_active = true;
        break;
    }
}

if ($our_plugin_active) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Plugin SoloYLibre Gallery Stable está activo";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "⚠️ Plugin SoloYLibre Gallery Stable NO está activo";
    echo "</div>";
}

echo "<h2>📁 5. Auditoría de Archivos y Permisos</h2>";

// Check WordPress directories and permissions
$wp_directories = array(
    ABSPATH => 'WordPress Root',
    WP_CONTENT_DIR => 'wp-content',
    WP_CONTENT_DIR . '/plugins' => 'Plugins',
    WP_CONTENT_DIR . '/uploads' => 'Uploads',
    WP_CONTENT_DIR . '/themes' => 'Themes'
);

foreach ($wp_directories as $dir => $name) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "📁 <strong>$name:</strong> Permisos $perms";
        
        if (is_writable($dir)) {
            echo " <span style='color: #28a745;'>✅ Escribible</span>";
        } else {
            echo " <span style='color: #dc3545;'>❌ No escribible</span>";
            $errors_found[] = "Directorio no escribible: $name";
        }
        echo "</div>";
    } else {
        $errors_found[] = "Directorio no encontrado: $name";
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Directorio no encontrado: $name";
        echo "</div>";
    }
}

echo "<h2>🔧 6. Aplicando Reparaciones Automáticas</h2>";

// Fix 1: Create missing plugin options
echo "<h3>⚙️ Creando opciones faltantes...</h3>";
$default_options = array(
    'soloylibre_published_photos' => array(),
    'soloylibre_private_photos' => array(),
    'soloylibre_unwanted_photos' => array(),
    'soloylibre_stable_version' => '5.4.1-stable',
    'soloylibre_stable_activated' => current_time('mysql')
);

foreach ($default_options as $option => $default_value) {
    if (get_option($option) === false) {
        add_option($option, $default_value);
        $fixes_applied[] = "Opción creada: $option";
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Opción creada: $option";
        echo "</div>";
    }
}

// Fix 2: Clean up any corrupted data
echo "<h3>🧹 Limpiando datos corruptos...</h3>";
$cleanup_options = array(
    'soloylibre_temp_data',
    'soloylibre_cache_data',
    'soloylibre_error_data',
    'soloylibre_debug_data'
);

foreach ($cleanup_options as $option) {
    if (delete_option($option)) {
        $fixes_applied[] = "Datos corruptos eliminados: $option";
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Datos corruptos eliminados: $option";
        echo "</div>";
    }
}

// Fix 3: Optimize database if needed
echo "<h3>🗄️ Optimizando base de datos...</h3>";
try {
    $wpdb->query("OPTIMIZE TABLE {$wpdb->options}");
    $fixes_applied[] = "Tabla options optimizada";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Tabla options optimizada";
    echo "</div>";
} catch (Exception $e) {
    $warnings_found[] = "No se pudo optimizar la base de datos: " . $e->getMessage();
}

// Fix 4: Clear any problematic caches
echo "<h3>🔄 Limpiando cachés...</h3>";
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    $fixes_applied[] = "Object cache limpiado";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Object cache limpiado";
    echo "</div>";
}

flush_rewrite_rules();
$fixes_applied[] = "Rewrite rules actualizadas";
echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "✅ Rewrite rules actualizadas";
echo "</div>";

echo "<h2>📊 7. Resumen de Auditoría</h2>";

$total_issues = count($errors_found) + count($warnings_found);
$fixes_count = count($fixes_applied);

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($errors_found) . "</div>";
echo "<div>❌ Errores Críticos</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($warnings_found) . "</div>";
echo "<div>⚠️ Advertencias</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$fixes_count</div>";
echo "<div>🔧 Reparaciones</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . ($total_issues > 0 ? round((1 - $total_issues / ($total_issues + $fixes_count)) * 100, 1) : 100) . "%</div>";
echo "<div>📈 Salud del Sistema</div>";
echo "</div>";

echo "</div>";

// Display detailed results
if (!empty($errors_found)) {
    echo "<h3 style='color: #dc3545;'>❌ Errores Críticos Encontrados:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($errors_found as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($warnings_found)) {
    echo "<h3 style='color: #856404;'>⚠️ Advertencias:</h3>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($warnings_found as $warning) {
        echo "<li>$warning</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($fixes_applied)) {
    echo "<h3 style='color: #155724;'>✅ Reparaciones Aplicadas:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// Final recommendations
echo "<h2>💡 8. Recomendaciones Finales</h2>";

if (count($errors_found) > 0) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24;'>🚨 Acción Requerida</h3>";
    echo "<p>Se encontraron errores críticos que requieren atención inmediata:</p>";
    echo "<ol>";
    echo "<li>Revisa los errores listados arriba</li>";
    echo "<li>Corrige los problemas de permisos de archivos</li>";
    echo "<li>Verifica la configuración de PHP</li>";
    echo "<li>Ejecuta este audit nuevamente después de las correcciones</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724;'>🎉 Sistema Saludable</h3>";
    echo "<p>No se encontraron errores críticos. El sistema está funcionando correctamente.</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/plugins.php' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔧 Gestionar Plugins</a>";
echo "<a href='../../../wp-admin/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🏠 Dashboard</a>";
echo "<a href='final-verification.php' style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔍 Verificación</a>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Auditoría completa del sistema</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";

// Capture any output buffer errors
$output_errors = ob_get_contents();
if (!empty($output_errors)) {
    echo "<h2>🐛 Errores de Output Buffer</h2>";
    echo "<pre style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo htmlspecialchars($output_errors);
    echo "</pre>";
}

ob_end_flush();

// Additional error checking and logging
if (count($errors_found) > 0 || count($warnings_found) > 0) {
    // Log errors to WordPress debug log
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('SoloYLibre Gallery Audit - Errors: ' . implode(', ', $errors_found));
        error_log('SoloYLibre Gallery Audit - Warnings: ' . implode(', ', $warnings_found));
    }

    // Create a detailed error report
    $error_report = array(
        'timestamp' => current_time('mysql'),
        'errors' => $errors_found,
        'warnings' => $warnings_found,
        'fixes_applied' => $fixes_applied,
        'php_version' => PHP_VERSION,
        'wp_version' => get_bloginfo('version'),
        'memory_usage' => memory_get_usage(true),
        'memory_limit' => ini_get('memory_limit')
    );

    update_option('soloylibre_audit_report', $error_report);
}
?>
