<?php
/**
 * Header & Compatibility Check - SoloYLibre Gallery Plugin
 * Deep analysis of headers, output buffering, and compatibility issues
 * Developed by JEYKO AI for Jose <PERSON> (JoseTusabe)
 */

// Start output buffering to catch any premature output
ob_start();

// Capture any errors that might be sent to output
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in output
ini_set('log_errors', 1);

echo "<h1>🔍 Diagnóstico de Headers y Compatibilidad</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Análisis profundo de compatibilidad</p>";

// Try to load WordPress safely
try {
    if (!defined('ABSPATH')) {
        require_once('../../../wp-config.php');
    }
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ WordPress cargado correctamente";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Error cargando WordPress: " . $e->getMessage();
    echo "</div>";
    ob_end_flush();
    die();
}

$compatibility_issues = array();
$header_issues = array();
$fixes_applied = array();

echo "<h2>📡 1. Análisis de Headers HTTP</h2>";

// Check if headers have been sent
if (headers_sent($file, $line)) {
    $header_issues[] = "Headers ya enviados desde $file en línea $line";
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ <strong>Headers ya enviados</strong><br>";
    echo "Archivo: $file<br>";
    echo "Línea: $line";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Headers no han sido enviados aún";
    echo "</div>";
}

// Check output buffering
$ob_level = ob_get_level();
echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "ℹ️ Nivel de output buffering: $ob_level";
echo "</div>";

if ($ob_level > 0) {
    $ob_contents = ob_get_contents();
    if (!empty($ob_contents)) {
        $header_issues[] = "Output buffer contiene datos: " . strlen($ob_contents) . " bytes";
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "⚠️ Output buffer contiene " . strlen($ob_contents) . " bytes de datos";
        echo "</div>";
    }
}

echo "<h2>🔧 2. Verificación de Configuración PHP</h2>";

// Check critical PHP settings
$php_settings = array(
    'output_buffering' => ini_get('output_buffering'),
    'implicit_flush' => ini_get('implicit_flush'),
    'zlib.output_compression' => ini_get('zlib.output_compression'),
    'auto_prepend_file' => ini_get('auto_prepend_file'),
    'auto_append_file' => ini_get('auto_append_file'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'error_reporting' => error_reporting(),
    'display_errors' => ini_get('display_errors'),
    'log_errors' => ini_get('log_errors')
);

foreach ($php_settings as $setting => $value) {
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "<strong>$setting:</strong> ";
    
    if ($value === '') {
        echo "<em>vacío</em>";
    } elseif ($value === '0' || $value === 0) {
        echo "0 (desactivado)";
    } elseif ($value === '1' || $value === 1) {
        echo "1 (activado)";
    } else {
        echo $value;
    }
    
    // Check for problematic settings
    if ($setting === 'auto_prepend_file' && !empty($value)) {
        $compatibility_issues[] = "auto_prepend_file está configurado: $value";
        echo " <span style='color: #dc3545;'>⚠️ Puede causar problemas</span>";
    }
    
    if ($setting === 'auto_append_file' && !empty($value)) {
        $compatibility_issues[] = "auto_append_file está configurado: $value";
        echo " <span style='color: #dc3545;'>⚠️ Puede causar problemas</span>";
    }
    
    if ($setting === 'implicit_flush' && $value == 1) {
        $compatibility_issues[] = "implicit_flush está activado";
        echo " <span style='color: #dc3545;'>⚠️ Puede causar problemas con headers</span>";
    }
    
    echo "</div>";
}

echo "<h2>📁 3. Verificación de Archivos WordPress</h2>";

// Check WordPress core files for BOM or extra whitespace
$wp_files_to_check = array(
    ABSPATH . 'wp-config.php' => 'wp-config.php',
    ABSPATH . 'wp-settings.php' => 'wp-settings.php',
    ABSPATH . 'wp-load.php' => 'wp-load.php',
    ABSPATH . 'wp-blog-header.php' => 'wp-blog-header.php'
);

foreach ($wp_files_to_check as $file_path => $file_name) {
    if (file_exists($file_path)) {
        $file_contents = file_get_contents($file_path);
        
        // Check for BOM
        if (substr($file_contents, 0, 3) === "\xEF\xBB\xBF") {
            $header_issues[] = "BOM encontrado en $file_name";
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ BOM (Byte Order Mark) encontrado en $file_name";
            echo "</div>";
        }
        
        // Check for whitespace before <?php
        if (preg_match('/^\s+<\?php/', $file_contents)) {
            $header_issues[] = "Espacios antes de <?php en $file_name";
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ Espacios en blanco antes de &lt;?php en $file_name";
            echo "</div>";
        }
        
        // Check for output after ?>
        if (preg_match('/\?>\s*\S/', $file_contents)) {
            $header_issues[] = "Output después de ?> en $file_name";
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ Contenido después de ?&gt; en $file_name";
            echo "</div>";
        }
        
        if (empty($header_issues)) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ $file_name - Sin problemas de formato";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "⚠️ Archivo no encontrado: $file_name";
        echo "</div>";
    }
}

echo "<h2>🔌 4. Verificación de Plugins</h2>";

// Check all active plugins for header issues
$active_plugins = get_option('active_plugins', array());
$plugin_issues = array();

foreach ($active_plugins as $plugin_file) {
    $plugin_path = WP_PLUGIN_DIR . '/' . $plugin_file;
    
    if (file_exists($plugin_path)) {
        $plugin_contents = file_get_contents($plugin_path);
        
        // Check for BOM in plugin files
        if (substr($plugin_contents, 0, 3) === "\xEF\xBB\xBF") {
            $plugin_issues[] = "BOM en plugin: $plugin_file";
        }
        
        // Check for premature output
        if (preg_match('/^\s+<\?php/', $plugin_contents)) {
            $plugin_issues[] = "Espacios antes de <?php en plugin: $plugin_file";
        }
        
        // Check for echo/print before headers
        if (preg_match('/^<\?php.*?(echo|print|printf|\?>).*?add_action.*?init/s', $plugin_contents)) {
            $plugin_issues[] = "Posible output prematuro en plugin: $plugin_file";
        }
    }
}

if (!empty($plugin_issues)) {
    echo "<h3>❌ Problemas en Plugins:</h3>";
    foreach ($plugin_issues as $issue) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ $issue";
        echo "</div>";
    }
} else {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ No se encontraron problemas en plugins activos";
    echo "</div>";
}

echo "<h2>🎨 5. Verificación de Tema</h2>";

// Check active theme for issues
$current_theme = wp_get_theme();
$theme_dir = $current_theme->get_stylesheet_directory();

$theme_files_to_check = array(
    $theme_dir . '/functions.php' => 'functions.php',
    $theme_dir . '/header.php' => 'header.php',
    $theme_dir . '/index.php' => 'index.php'
);

$theme_issues = array();

foreach ($theme_files_to_check as $file_path => $file_name) {
    if (file_exists($file_path)) {
        $file_contents = file_get_contents($file_path);
        
        // Check for BOM
        if (substr($file_contents, 0, 3) === "\xEF\xBB\xBF") {
            $theme_issues[] = "BOM en $file_name del tema";
        }
        
        // Check for whitespace issues
        if (preg_match('/^\s+<\?php/', $file_contents)) {
            $theme_issues[] = "Espacios antes de <?php en $file_name del tema";
        }
    }
}

if (!empty($theme_issues)) {
    echo "<h3>❌ Problemas en Tema:</h3>";
    foreach ($theme_issues as $issue) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ $issue";
        echo "</div>";
    }
} else {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ No se encontraron problemas en el tema activo";
    echo "</div>";
}

echo "<h2>🔧 6. Aplicando Reparaciones</h2>";

// Fix 1: Clean output buffer if it has content
if ($ob_level > 0) {
    $ob_contents = ob_get_contents();
    if (!empty($ob_contents)) {
        ob_clean();
        $fixes_applied[] = "Output buffer limpiado (" . strlen($ob_contents) . " bytes)";
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Output buffer limpiado";
        echo "</div>";
    }
}

// Fix 2: Set proper PHP settings
if (ini_get('implicit_flush')) {
    ini_set('implicit_flush', 0);
    $fixes_applied[] = "implicit_flush desactivado";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ implicit_flush desactivado";
    echo "</div>";
}

// Fix 3: Ensure proper error handling
ini_set('display_errors', 0);
ini_set('log_errors', 1);
$fixes_applied[] = "Error display desactivado, logging activado";
echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "✅ Error display desactivado, logging activado";
echo "</div>";

// Fix 4: Clean any WordPress hooks that might cause premature output
remove_all_actions('wp_head', 1);
remove_all_actions('wp_footer', 1);
$fixes_applied[] = "Hooks de output limpiados";
echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "✅ Hooks de output limpiados";
echo "</div>";

echo "<h2>🧪 7. Test de Compatibilidad</h2>";

// Test 1: Try to set a header
if (!headers_sent()) {
    header('X-SoloYLibre-Test: OK');
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Test de header exitoso";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ No se puede establecer header - ya fueron enviados";
    echo "</div>";
}

// Test 2: Check WordPress constants
$wp_constants = array(
    'WP_DEBUG' => defined('WP_DEBUG') ? WP_DEBUG : 'no definido',
    'WP_DEBUG_LOG' => defined('WP_DEBUG_LOG') ? WP_DEBUG_LOG : 'no definido',
    'WP_DEBUG_DISPLAY' => defined('WP_DEBUG_DISPLAY') ? WP_DEBUG_DISPLAY : 'no definido',
    'SCRIPT_DEBUG' => defined('SCRIPT_DEBUG') ? SCRIPT_DEBUG : 'no definido'
);

echo "<h3>🔍 Constantes de WordPress:</h3>";
foreach ($wp_constants as $constant => $value) {
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "<strong>$constant:</strong> ";
    if ($value === true) {
        echo "true";
    } elseif ($value === false) {
        echo "false";
    } else {
        echo $value;
    }
    echo "</div>";
}

echo "<h2>📊 8. Resumen de Compatibilidad</h2>";

$total_issues = count($compatibility_issues) + count($header_issues) + count($plugin_issues) + count($theme_issues);
$total_fixes = count($fixes_applied);

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_issues</div>";
echo "<div>❌ Problemas Encontrados</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_fixes</div>";
echo "<div>🔧 Reparaciones</div>";
echo "</div>";

$compatibility_score = $total_issues > 0 ? max(0, 100 - ($total_issues * 10)) : 100;
echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$compatibility_score%</div>";
echo "<div>📈 Compatibilidad</div>";
echo "</div>";

echo "</div>";

// Display all issues found
$all_issues = array_merge($compatibility_issues, $header_issues, $plugin_issues, $theme_issues);

if (!empty($all_issues)) {
    echo "<h3 style='color: #721c24;'>❌ Problemas de Compatibilidad:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($all_issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($fixes_applied)) {
    echo "<h3 style='color: #155724;'>✅ Reparaciones Aplicadas:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h2>💡 9. Recomendaciones Específicas</h2>";

if ($total_issues > 0) {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🔧 Acciones Recomendadas:</h3>";
    echo "<ol>";
    echo "<li><strong>Revisar archivos con BOM:</strong> Usar editor que soporte UTF-8 sin BOM</li>";
    echo "<li><strong>Eliminar espacios en blanco:</strong> Antes de &lt;?php y después de ?&gt;</li>";
    echo "<li><strong>Verificar plugins:</strong> Desactivar plugins problemáticos temporalmente</li>";
    echo "<li><strong>Configurar PHP:</strong> Ajustar output_buffering y error_reporting</li>";
    echo "<li><strong>Limpiar tema:</strong> Verificar functions.php y header.php</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 Sistema Compatible</h3>";
    echo "<p>No se encontraron problemas de compatibilidad significativos.</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🏠 Dashboard</a>";
echo "<a href='comprehensive-audit.php' style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔍 Audit Completo</a>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Análisis de compatibilidad completado</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";

// Save compatibility report
update_option('soloylibre_compatibility_report', array(
    'timestamp' => current_time('mysql'),
    'compatibility_issues' => $compatibility_issues,
    'header_issues' => $header_issues,
    'plugin_issues' => $plugin_issues,
    'theme_issues' => $theme_issues,
    'fixes_applied' => $fixes_applied,
    'compatibility_score' => $compatibility_score
));

// End output buffering
ob_end_flush();
?>
