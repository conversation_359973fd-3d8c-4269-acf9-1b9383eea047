<?php
/**
 * Final Verification - SoloYLibre Gallery Stable
 * Verify that everything is working correctly
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Load WordPress
require_once('../../../wp-config.php');

echo "<h1>✅ Verificación Final del Sistema</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Confirmando que todo funciona correctamente</p>";

$tests_passed = 0;
$tests_total = 0;

echo "<h2>🔍 1. Verificando WordPress</h2>";
$tests_total++;
if (defined('ABSPATH') && function_exists('get_bloginfo')) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ WordPress funcionando correctamente";
    echo "<br><strong>Versión:</strong> " . get_bloginfo('version');
    echo "</div>";
    $tests_passed++;
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ WordPress no está funcionando";
    echo "</div>";
}

echo "<h2>🔧 2. Verificando Plugins</h2>";
$active_plugins = get_option('active_plugins', array());

echo "<h3>📋 Plugins Activos:</h3>";
echo "<ul>";
foreach ($active_plugins as $plugin) {
    echo "<li>$plugin</li>";
}
echo "</ul>";

$tests_total++;
$problematic_plugin_found = false;
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'Archive/soloylibre-gallery-plugin.php') !== false) {
        $problematic_plugin_found = true;
        break;
    }
}

if (!$problematic_plugin_found) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ Plugin problemático no está activo";
    echo "</div>";
    $tests_passed++;
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Plugin problemático aún está activo";
    echo "</div>";
}

echo "<h2>📊 3. Verificando Base de Datos</h2>";
$tests_total++;
global $wpdb;
try {
    $result = $wpdb->get_var("SELECT 1");
    if ($result == 1) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ Base de datos funcionando correctamente";
        echo "</div>";
        $tests_passed++;
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Error en base de datos: " . $e->getMessage();
    echo "</div>";
}

echo "<h2>📷 4. Verificando Fotos</h2>";
$tests_total++;
$total_photos = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'attachment' AND post_mime_type LIKE 'image%'");

if ($total_photos > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ $total_photos fotos disponibles en el sistema";
    echo "</div>";
    $tests_passed++;
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "⚠️ No hay fotos en el sistema";
    echo "</div>";
}

echo "<h2>🚀 5. Verificando Plugin Estable</h2>";
$tests_total++;
$stable_plugin_path = dirname(__FILE__) . '/soloylibre-gallery-stable.php';

if (file_exists($stable_plugin_path)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ Plugin estable disponible";
    echo "<br><strong>Ubicación:</strong> " . basename(dirname($stable_plugin_path)) . '/' . basename($stable_plugin_path);
    echo "</div>";
    $tests_passed++;
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Plugin estable no encontrado";
    echo "</div>";
}

echo "<h2>📊 6. Resumen de Verificación</h2>";

$success_rate = ($tests_passed / $tests_total) * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$tests_passed</div>";
echo "<div>✅ Tests Exitosos</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . ($tests_total - $tests_passed) . "</div>";
echo "<div>❌ Tests Fallidos</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Tasa de Éxito</div>";
echo "</div>";

echo "</div>";

if ($success_rate >= 80) {
    echo "<div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #155724;'>🎉 ¡Sistema Funcionando Correctamente!</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #155724;'>";
    echo "WordPress está funcionando sin errores críticos. Puedes proceder con la instalación del plugin estable.";
    echo "</p>";
    echo "</div>";
} else {
    echo "<div style='background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #856404;'>⚠️ Sistema Requiere Atención</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #856404;'>";
    echo "Algunos tests fallaron. Revisa los errores arriba antes de continuar.";
    echo "</p>";
    echo "</div>";
}

echo "<h2>🔧 7. Instrucciones Finales</h2>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>📋 Pasos para Completar la Instalación:</h3>";
echo "<ol>";
echo "<li><strong>Activar Plugin Estable:</strong> Ve a Plugins y activa 'SoloYLibre Gallery Pro - Stable'</li>";
echo "<li><strong>Verificar Funcionamiento:</strong> Prueba todas las funcionalidades</li>";
echo "<li><strong>Configurar Servidor:</strong> Asegúrate de tener memory_limit = 512M</li>";
echo "<li><strong>Crear Backup:</strong> Haz backup antes de hacer cambios importantes</li>";
echo "<li><strong>Monitorear:</strong> Vigila los logs por posibles errores</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>✅ Características del Plugin Estable:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Posts Rápidos:</strong> Con vista previa de seguridad</li>";
echo "<li>✅ <strong>Gestor de Fotos:</strong> Organización y estadísticas</li>";
echo "<li>✅ <strong>Sistema de Likes:</strong> Ilimitados por foto</li>";
echo "<li>✅ <strong>Gráficos:</strong> Chart.js integrado</li>";
echo "<li>✅ <strong>Responsive:</strong> Funciona en todos los dispositivos</li>";
echo "<li>✅ <strong>Personalización:</strong> Completamente personalizado para JoseTusabe</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/plugins.php' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔧 Gestionar Plugins</a>";
echo "<a href='../../../wp-admin/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🏠 Dashboard</a>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Sistema verificado y funcionando</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>📧 <EMAIL> | 📱 718-713-5500</p>";
echo "</div>";
?>
