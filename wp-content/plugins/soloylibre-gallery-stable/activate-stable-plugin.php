<?php
/**
 * Activate Stable Plugin - SoloYLibre Gallery
 * Safely activate the stable version of the plugin
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Load WordPress
require_once('../../../wp-config.php');

echo "<h1>🚀 Activación del Plugin Estable</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Activando la versión estable del plugin</p>";

// Check if user has admin privileges
if (!current_user_can('activate_plugins')) {
    die('<div style="background: #f8d7da; padding: 20px; border-radius: 10px;">❌ No tienes permisos para activar plugins</div>');
}

$activation_steps = array();
$errors_found = array();

echo "<h2>🔍 1. Verificación Pre-Activación</h2>";

// Check if WordPress is working
if (defined('ABSPATH') && function_exists('get_bloginfo')) {
    $activation_steps[] = "WordPress funcionando correctamente";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ WordPress funcionando - Versión: " . get_bloginfo('version');
    echo "</div>";
} else {
    $errors_found[] = "WordPress no está funcionando correctamente";
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ WordPress no está funcionando correctamente";
    echo "</div>";
}

// Check if stable plugin file exists
$stable_plugin_file = dirname(__FILE__) . '/soloylibre-gallery-stable.php';
if (file_exists($stable_plugin_file)) {
    $activation_steps[] = "Plugin estable encontrado";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Plugin estable encontrado";
    echo "</div>";
} else {
    $errors_found[] = "Plugin estable no encontrado";
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ Plugin estable no encontrado";
    echo "</div>";
}

// Check current active plugins
$active_plugins = get_option('active_plugins', array());
$stable_plugin_path = 'soloylibre-gallery-stable/soloylibre-gallery-stable.php';
$old_plugin_active = false;
$stable_plugin_active = false;

foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'Archive/soloylibre-gallery-plugin.php') !== false) {
        $old_plugin_active = true;
    }
    if (strpos($plugin, 'soloylibre-gallery-stable.php') !== false) {
        $stable_plugin_active = true;
    }
}

if ($old_plugin_active) {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "⚠️ Plugin antiguo aún activo - se desactivará automáticamente";
    echo "</div>";
} else {
    $activation_steps[] = "Plugin antiguo no está activo";
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Plugin antiguo no está activo";
    echo "</div>";
}

if ($stable_plugin_active) {
    echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "ℹ️ Plugin estable ya está activo";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "⚠️ Plugin estable no está activo - se activará";
    echo "</div>";
}

echo "<h2>🔧 2. Proceso de Activación</h2>";

if (!empty($errors_found)) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ Errores que impiden la activación:</h3>";
    echo "<ul>";
    foreach ($errors_found as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "<p><strong>Solución:</strong> Corrige estos errores antes de continuar.</p>";
    echo "</div>";
} else {
    // Proceed with activation
    
    // Step 1: Deactivate old plugin if active
    if ($old_plugin_active) {
        echo "<h3>🔄 Desactivando plugin antiguo...</h3>";
        $new_active_plugins = array();
        foreach ($active_plugins as $plugin) {
            if (strpos($plugin, 'Archive/soloylibre-gallery-plugin.php') === false) {
                $new_active_plugins[] = $plugin;
            }
        }
        update_option('active_plugins', $new_active_plugins);
        $activation_steps[] = "Plugin antiguo desactivado";
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Plugin antiguo desactivado";
        echo "</div>";
    }
    
    // Step 2: Activate stable plugin if not active
    if (!$stable_plugin_active) {
        echo "<h3>🚀 Activando plugin estable...</h3>";
        
        // Get current active plugins
        $current_active = get_option('active_plugins', array());
        
        // Add stable plugin to active list
        if (!in_array($stable_plugin_path, $current_active)) {
            $current_active[] = $stable_plugin_path;
            update_option('active_plugins', $current_active);
            
            $activation_steps[] = "Plugin estable activado";
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ Plugin estable activado";
            echo "</div>";
            
            // Trigger activation hook manually
            if (file_exists($stable_plugin_file)) {
                include_once($stable_plugin_file);
                if (class_exists('SoloYLibre_Gallery_Stable')) {
                    $plugin_instance = SoloYLibre_Gallery_Stable::get_instance();
                    if (method_exists($plugin_instance, 'activate')) {
                        $plugin_instance->activate();
                        $activation_steps[] = "Hooks de activación ejecutados";
                        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                        echo "✅ Hooks de activación ejecutados";
                        echo "</div>";
                    }
                }
            }
        }
    }
    
    // Step 3: Verify activation
    echo "<h3>🔍 Verificando activación...</h3>";
    
    $final_active_plugins = get_option('active_plugins', array());
    $is_now_active = false;
    
    foreach ($final_active_plugins as $plugin) {
        if (strpos($plugin, 'soloylibre-gallery-stable.php') !== false) {
            $is_now_active = true;
            break;
        }
    }
    
    if ($is_now_active) {
        $activation_steps[] = "Activación verificada exitosamente";
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Plugin estable activado y verificado";
        echo "</div>";
        
        // Test if plugin menu is available
        if (function_exists('add_menu_page')) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ Funciones de WordPress disponibles";
            echo "</div>";
        }
        
    } else {
        $errors_found[] = "Plugin no se activó correctamente";
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Plugin no se activó correctamente";
        echo "</div>";
    }
}

echo "<h2>📊 3. Resumen de Activación</h2>";

$total_steps = count($activation_steps);
$total_errors = count($errors_found);
$success_rate = $total_errors == 0 ? 100 : ($total_steps / ($total_steps + $total_errors)) * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_steps</div>";
echo "<div>✅ Pasos Completados</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_errors</div>";
echo "<div>❌ Errores</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Éxito</div>";
echo "</div>";

echo "</div>";

if ($total_steps > 0) {
    echo "<h3 style='color: #155724;'>✅ Pasos Completados:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($activation_steps as $step) {
        echo "<li>$step</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if ($total_errors > 0) {
    echo "<h3 style='color: #721c24;'>❌ Errores Encontrados:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($errors_found as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// Final status
if ($success_rate >= 90) {
    echo "<div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #155724;'>🎉 ¡Plugin Activado Exitosamente!</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #155724;'>";
    echo "El plugin SoloYLibre Gallery Pro - Stable está ahora activo y funcionando.";
    echo "</p>";
    echo "</div>";
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎯 Próximos Pasos:</h3>";
    echo "<ol>";
    echo "<li>Ve al Dashboard de WordPress</li>";
    echo "<li>Busca el menú 'SoloYLibre Gallery' en la barra lateral</li>";
    echo "<li>Explora las funcionalidades: Posts Rápidos, Gestor, Estadísticas</li>";
    echo "<li>Prueba crear un post con vista previa de seguridad</li>";
    echo "<li>Verifica que todas las funcionalidades trabajen correctamente</li>";
    echo "</ol>";
    echo "</div>";
    
} else {
    echo "<div style='background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #856404;'>⚠️ Activación Incompleta</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #856404;'>";
    echo "Hubo problemas durante la activación. Revisa los errores y vuelve a intentar.";
    echo "</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🏠 Ir al Dashboard</a>";
echo "<a href='../../../wp-admin/plugins.php' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔧 Gestionar Plugins</a>";
if ($success_rate >= 90) {
    echo "<a href='../../../wp-admin/admin.php?page=soloylibre-stable' style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>📸 SoloYLibre Gallery</a>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Plugin activado exitosamente</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";

// Log activation attempt
update_option('soloylibre_activation_log', array(
    'timestamp' => current_time('mysql'),
    'steps_completed' => $activation_steps,
    'errors_found' => $errors_found,
    'success_rate' => $success_rate,
    'final_status' => $success_rate >= 90 ? 'success' : 'failed'
));
?>
