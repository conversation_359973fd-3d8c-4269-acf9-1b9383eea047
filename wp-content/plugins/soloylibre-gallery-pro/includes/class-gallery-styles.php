<?php
/**
 * Gallery Styles Class
 * Handles different gallery display styles
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Gallery_Styles {
    
    private $available_styles = array(
        'tiktok' => 'TikTok Style (Infinite Scroll)',
        'grid' => 'Grid Portfolio',
        'masonry' => 'Masonry Layout',
        'carousel' => 'Carousel Slider',
        'lightbox' => 'Lightbox Gallery',
        'dominican' => 'Dominican Style (Bandera RD)',
        'professional' => 'Professional Portfolio',
        'video_gallery' => 'Video Gallery',
        'mixed_media' => 'Photos & Videos Mixed'
    );

    private $photographer_info = array(
        'name' => 'Jose L Encarnacion',
        'alias' => 'JoseTusabe',
        'brand' => 'SoloYLibre Photography',
        'location' => 'San José de Ocoa, Dom. Rep. / USA',
        'phone' => '************',
        'email' => '<EMAIL>',
        'websites' => array(
            'josetusabe.com',
            'soloylibre.com',
            '1and1photo.com',
            'joselencarnacion.com'
        )
    );
    
    public function __construct() {
        add_action('wp_footer', array($this, 'add_gallery_scripts'));
    }
    
    /**
     * Get available styles
     */
    public function get_available_styles() {
        return $this->available_styles;
    }
    
    /**
     * Render gallery based on style
     */
    public function render_gallery($atts) {
        $style = $atts['style'];
        $photos = $this->get_gallery_photos($atts);
        
        if (empty($photos)) {
            return '<p class="soloylibre-no-photos">' . __('No hay fotos disponibles.', 'soloylibre-gallery') . '</p>';
        }
        
        ob_start();
        
        switch ($style) {
            case 'tiktok':
                $this->render_tiktok_style($photos, $atts);
                break;
            case 'grid':
                $this->render_grid_style($photos, $atts);
                break;
            case 'masonry':
                $this->render_masonry_style($photos, $atts);
                break;
            case 'carousel':
                $this->render_carousel_style($photos, $atts);
                break;
            case 'lightbox':
                $this->render_lightbox_style($photos, $atts);
                break;
            default:
                $this->render_grid_style($photos, $atts);
        }
        
        return ob_get_clean();
    }
    
    /**
     * TikTok Style Gallery
     */
    private function render_tiktok_style($photos, $atts) {
        $gallery_id = 'soloylibre-tiktok-' . uniqid();
        ?>
        <div class="soloylibre-gallery soloylibre-tiktok-style" id="<?php echo esc_attr($gallery_id); ?>" 
             data-style="tiktok" data-album="<?php echo esc_attr($atts['album']); ?>" 
             data-category="<?php echo esc_attr($atts['category']); ?>" data-limit="<?php echo esc_attr($atts['limit']); ?>">
            
            <!-- Header -->
            <div class="soloylibre-header">
                <div class="soloylibre-brand">
                    <span class="brand-icon">📸</span>
                    <span class="brand-text"><?php echo esc_html(get_option('soloylibre_gallery_photographer_brand', 'SoloYLibre')); ?></span>
                </div>
                <div class="soloylibre-membership-badge">
                    <?php echo $this->get_user_membership_badge(); ?>
                </div>
            </div>
            
            <!-- Gallery Container -->
            <div class="soloylibre-tiktok-container">
                <?php foreach ($photos as $photo): ?>
                    <?php $this->render_tiktok_photo_item($photo); ?>
                <?php endforeach; ?>
            </div>
            
            <!-- Loading Indicator -->
            <div class="soloylibre-loading-indicator">
                <span><?php _e('Cargando más fotos...', 'soloylibre-gallery'); ?></span>
            </div>
        </div>
        <?php
    }
    
    /**
     * Grid Style Gallery
     */
    private function render_grid_style($photos, $atts) {
        $gallery_id = 'soloylibre-grid-' . uniqid();
        ?>
        <div class="soloylibre-gallery soloylibre-grid-style" id="<?php echo esc_attr($gallery_id); ?>" 
             data-style="grid" data-album="<?php echo esc_attr($atts['album']); ?>" 
             data-category="<?php echo esc_attr($atts['category']); ?>" data-limit="<?php echo esc_attr($atts['limit']); ?>">
            
            <!-- Header -->
            <div class="soloylibre-grid-header">
                <div class="soloylibre-brand-section">
                    <div class="brand-avatar">
                        <?php echo substr(get_option('soloylibre_gallery_photographer_name', 'JL'), 0, 2); ?>
                    </div>
                    <div class="brand-info">
                        <h2><?php echo esc_html(get_option('soloylibre_gallery_photographer_brand', 'SoloYLibre')); ?> Photography</h2>
                        <p><?php echo esc_html(get_option('soloylibre_gallery_photographer_name', 'Jose L Encarnacion')); ?> • Professional Photographer</p>
                    </div>
                </div>
                <div class="soloylibre-user-info">
                    <?php echo $this->get_user_membership_badge(); ?>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="soloylibre-filters">
                <?php $this->render_category_filters(); ?>
                <?php $this->render_album_selector(); ?>
            </div>
            
            <!-- Grid Container -->
            <div class="soloylibre-grid-container">
                <?php foreach ($photos as $photo): ?>
                    <?php $this->render_grid_photo_item($photo); ?>
                <?php endforeach; ?>
            </div>
            
            <!-- Load More Button -->
            <div class="soloylibre-load-more">
                <button class="soloylibre-load-more-btn" type="button">
                    <?php _e('Cargar Más Fotos', 'soloylibre-gallery'); ?>
                </button>
            </div>
        </div>
        <?php
    }
    
    /**
     * Masonry Style Gallery
     */
    private function render_masonry_style($photos, $atts) {
        $gallery_id = 'soloylibre-masonry-' . uniqid();
        ?>
        <div class="soloylibre-gallery soloylibre-masonry-style" id="<?php echo esc_attr($gallery_id); ?>" 
             data-style="masonry" data-album="<?php echo esc_attr($atts['album']); ?>" 
             data-category="<?php echo esc_attr($atts['category']); ?>" data-limit="<?php echo esc_attr($atts['limit']); ?>">
            
            <!-- Navigation -->
            <nav class="soloylibre-navbar">
                <div class="nav-content">
                    <div class="brand-section">
                        <div class="brand-avatar">
                            <?php echo substr(get_option('soloylibre_gallery_photographer_name', 'JL'), 0, 2); ?>
                        </div>
                        <div class="brand-info">
                            <h1><?php echo esc_html(get_option('soloylibre_gallery_photographer_brand', 'SoloYLibre')); ?> Photography</h1>
                            <p><?php echo esc_html(get_option('soloylibre_gallery_photographer_name', 'Jose L Encarnacion')); ?> • Professional Photographer</p>
                        </div>
                    </div>
                    <div class="nav-actions">
                        <?php echo $this->get_user_membership_badge(); ?>
                    </div>
                </div>
            </nav>
            
            <!-- Controls -->
            <div class="soloylibre-controls-bar">
                <div class="controls-content">
                    <div class="category-filters">
                        <?php $this->render_category_chips(); ?>
                    </div>
                    <div class="view-controls">
                        <?php $this->render_album_dropdown(); ?>
                    </div>
                </div>
            </div>
            
            <!-- Masonry Container -->
            <div class="soloylibre-masonry-container">
                <?php foreach ($photos as $photo): ?>
                    <?php $this->render_masonry_photo_item($photo); ?>
                <?php endforeach; ?>
            </div>
            
            <!-- Load More -->
            <div class="soloylibre-load-more-section">
                <button class="soloylibre-load-more-btn" type="button">
                    <?php _e('Cargar Más Fotografías', 'soloylibre-gallery'); ?>
                </button>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render TikTok photo item
     */
    private function render_tiktok_photo_item($photo) {
        $is_premium = $this->is_premium_content($photo);
        $can_view = $this->can_user_view_content($photo);
        ?>
        <div class="soloylibre-photo-item" data-photo-id="<?php echo esc_attr($photo->ID); ?>">
            <?php if ($can_view): ?>
                <img src="<?php echo esc_url(wp_get_attachment_image_url($photo->ID, 'large')); ?>" 
                     alt="<?php echo esc_attr($photo->post_title); ?>" class="photo-content">
                
                <div class="photo-overlay">
                    <div class="photo-title"><?php echo esc_html($photo->post_title); ?></div>
                    <div class="photo-description"><?php echo esc_html($photo->post_excerpt); ?></div>
                    <div class="photo-actions">
                        <button class="action-btn like-btn" data-photo-id="<?php echo esc_attr($photo->ID); ?>">❤️</button>
                        <button class="action-btn comment-btn">💬</button>
                        <button class="action-btn share-btn">📤</button>
                        <span class="photographer-credit">Por <?php echo esc_html(get_option('soloylibre_gallery_photographer_name', 'Jose L Encarnacion')); ?></span>
                    </div>
                </div>
                
                <div class="side-actions">
                    <button class="side-btn like-btn">❤️</button>
                    <button class="side-btn comment-btn">💬</button>
                    <button class="side-btn share-btn">🔗</button>
                    <button class="side-btn download-btn">⬇️</button>
                </div>
            <?php else: ?>
                <div class="membership-lock">
                    <div class="lock-icon">🔒</div>
                    <h3><?php _e('Contenido Premium', 'soloylibre-gallery'); ?></h3>
                    <p><?php _e('Esta fotografía está disponible solo para miembros Premium', 'soloylibre-gallery'); ?></p>
                    <button class="upgrade-btn" onclick="window.location.href='<?php echo esc_url($this->get_upgrade_url()); ?>'">
                        <?php _e('Actualizar Membresía', 'soloylibre-gallery'); ?>
                    </button>
                </div>
            <?php endif; ?>
            
            <div class="photographer-credit">
                📍 <?php echo esc_html($this->get_photo_location($photo->ID)); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render grid photo item
     */
    private function render_grid_photo_item($photo) {
        $is_premium = $this->is_premium_content($photo);
        $can_view = $this->can_user_view_content($photo);
        ?>
        <div class="soloylibre-photo-card" data-photo-id="<?php echo esc_attr($photo->ID); ?>">
            <div class="photo-wrapper">
                <?php if ($can_view): ?>
                    <img src="<?php echo esc_url(wp_get_attachment_image_url($photo->ID, 'medium_large')); ?>" 
                         alt="<?php echo esc_attr($photo->post_title); ?>" class="photo-image">
                    
                    <div class="photo-overlay">
                        <div class="photo-actions">
                            <button class="action-btn">❤️ <?php echo $this->get_photo_likes($photo->ID); ?></button>
                            <button class="action-btn">💬 <?php echo $this->get_photo_comments($photo->ID); ?></button>
                            <button class="action-btn">📤</button>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="premium-lock">
                        <div class="lock-icon">🔒</div>
                        <div class="upgrade-text">
                            <strong><?php _e('Contenido Premium', 'soloylibre-gallery'); ?></strong><br>
                            <?php _e('Solo para miembros Plus', 'soloylibre-gallery'); ?>
                        </div>
                        <button class="upgrade-btn" onclick="window.location.href='<?php echo esc_url($this->get_upgrade_url()); ?>'">
                            <?php _e('Actualizar', 'soloylibre-gallery'); ?>
                        </button>
                    </div>
                <?php endif; ?>
                
                <div class="photographer-tag">
                    📍 <?php echo esc_html($this->get_photo_location($photo->ID)); ?>
                </div>
            </div>
            
            <div class="photo-info">
                <div class="photo-title"><?php echo esc_html($photo->post_title); ?></div>
                <div class="photo-description"><?php echo esc_html(wp_trim_words($photo->post_excerpt, 15)); ?></div>
                <div class="photo-meta">
                    <span><?php echo esc_html($this->get_photographer_brand()); ?></span>
                    <span><?php echo human_time_diff(strtotime($photo->post_date), current_time('timestamp')) . ' ' . __('ago', 'soloylibre-gallery'); ?></span>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render masonry photo item
     */
    private function render_masonry_photo_item($photo) {
        $is_premium = $this->is_premium_content($photo);
        $can_view = $this->can_user_view_content($photo);
        $image_meta = wp_get_attachment_metadata($photo->ID);
        $aspect_ratio = isset($image_meta['height']) && $image_meta['height'] > 0 ? 
                       $image_meta['width'] / $image_meta['height'] : 1;
        ?>
        <div class="soloylibre-photo-item" data-photo-id="<?php echo esc_attr($photo->ID); ?>">
            <div class="photo-wrapper">
                <?php if ($can_view): ?>
                    <img src="<?php echo esc_url(wp_get_attachment_image_url($photo->ID, 'large')); ?>" 
                         alt="<?php echo esc_attr($photo->post_title); ?>" class="photo-image">
                    
                    <div class="photo-overlay">
                        <div class="overlay-actions">
                            <button class="overlay-btn">❤️ <?php echo $this->get_photo_likes($photo->ID); ?></button>
                            <button class="overlay-btn">💬 <?php echo $this->get_photo_comments($photo->ID); ?></button>
                            <button class="overlay-btn">📤 <?php _e('Compartir', 'soloylibre-gallery'); ?></button>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="premium-overlay">
                        <div class="premium-icon">🔒</div>
                        <div class="premium-text">
                            <h4><?php _e('Contenido Premium', 'soloylibre-gallery'); ?></h4>
                            <p><?php _e('Solo para miembros Plus', 'soloylibre-gallery'); ?></p>
                        </div>
                        <button class="premium-upgrade" onclick="window.location.href='<?php echo esc_url($this->get_upgrade_url()); ?>'">
                            <?php _e('Actualizar Membresía', 'soloylibre-gallery'); ?>
                        </button>
                    </div>
                <?php endif; ?>
                
                <div class="location-tag">
                    📍 <?php echo esc_html($this->get_photo_location($photo->ID)); ?>
                </div>
            </div>
            
            <div class="photo-content">
                <div class="photo-title"><?php echo esc_html($photo->post_title); ?></div>
                <div class="photo-description"><?php echo esc_html($photo->post_excerpt); ?></div>
                <div class="photo-meta">
                    <span><?php echo esc_html($this->get_photographer_brand()); ?></span>
                    <div class="photo-stats">
                        <div class="stat-item">❤️ <?php echo $this->get_photo_likes($photo->ID); ?></div>
                        <div class="stat-item">👁️ <?php echo $this->get_photo_views($photo->ID); ?></div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Get gallery photos
     */
    private function get_gallery_photos($atts) {
        $args = array(
            'post_type' => 'soloylibre_photo',
            'post_status' => 'publish',
            'posts_per_page' => intval($atts['limit']),
            'meta_query' => array(
                array(
                    'key' => '_soloylibre_is_publishable',
                    'value' => '1',
                    'compare' => '='
                )
            )
        );
        
        // Add album filter
        if (!empty($atts['album'])) {
            $args['meta_query'][] = array(
                'key' => '_soloylibre_album_id',
                'value' => intval($atts['album']),
                'compare' => '='
            );
        }
        
        // Add category filter
        if (!empty($atts['category'])) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'photo_category',
                    'field' => 'slug',
                    'terms' => $atts['category']
                )
            );
        }
        
        return get_posts($args);
    }
    
    /**
     * Helper methods
     */
    private function is_premium_content($photo) {
        return get_post_meta($photo->ID, '_soloylibre_membership_level', true) !== '';
    }
    
    private function can_user_view_content($photo) {
        if (!$this->is_premium_content($photo)) {
            return true;
        }
        
        // Check if user has required membership level
        $required_level = get_post_meta($photo->ID, '_soloylibre_membership_level', true);
        return $this->user_has_membership_level($required_level);
    }
    
    private function user_has_membership_level($level) {
        // Integration with Paid Memberships Pro
        if (function_exists('pmpro_hasMembershipLevel')) {
            return pmpro_hasMembershipLevel($level);
        }
        return true; // Default to true if PMP Pro not active
    }
    
    private function get_user_membership_badge() {
        if (function_exists('pmpro_getMembershipLevelForUser')) {
            $level = pmpro_getMembershipLevelForUser();
            if ($level) {
                return '<div class="membership-badge">' . esc_html($level->name) . '</div>';
            }
        }
        return '<div class="membership-badge">JEYKO AI Premium</div>';
    }
    
    private function get_upgrade_url() {
        if (function_exists('pmpro_url')) {
            return pmpro_url('levels');
        }
        return '#';
    }
    
    private function get_photo_location($photo_id) {
        return get_post_meta($photo_id, '_soloylibre_location', true) ?: 'SoloYLibre';
    }
    
    private function get_photo_likes($photo_id) {
        return get_post_meta($photo_id, '_soloylibre_likes_count', true) ?: 0;
    }
    
    private function get_photo_comments($photo_id) {
        return get_comments_number($photo_id);
    }
    
    private function get_photo_views($photo_id) {
        return get_post_meta($photo_id, '_soloylibre_views_count', true) ?: 0;
    }
    
    private function get_photographer_brand() {
        return get_option('soloylibre_gallery_photographer_brand', 'SoloYLibre');
    }
    
    /**
     * Render filter components
     */
    private function render_category_filters() {
        $categories = get_terms(array(
            'taxonomy' => 'photo_category',
            'hide_empty' => true
        ));
        
        echo '<div class="filter-tabs">';
        echo '<button class="filter-tab active" data-category="">' . __('Todas', 'soloylibre-gallery') . '</button>';
        
        foreach ($categories as $category) {
            echo '<button class="filter-tab" data-category="' . esc_attr($category->slug) . '">' . esc_html($category->name) . '</button>';
        }
        echo '</div>';
    }
    
    private function render_album_selector() {
        global $wpdb;
        $albums = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}soloylibre_albums WHERE is_published = 1 ORDER BY sort_order ASC");
        
        echo '<div class="album-selector">';
        echo '<span>' . __('Álbum:', 'soloylibre-gallery') . '</span>';
        echo '<select class="album-dropdown">';
        echo '<option value="">' . __('Todos los Álbumes', 'soloylibre-gallery') . '</option>';
        
        foreach ($albums as $album) {
            echo '<option value="' . esc_attr($album->id) . '">' . esc_html($album->name) . '</option>';
        }
        echo '</select>';
        echo '</div>';
    }
    
    private function render_category_chips() {
        $categories = get_terms(array(
            'taxonomy' => 'photo_category',
            'hide_empty' => true
        ));
        
        echo '<div class="category-filters">';
        echo '<div class="filter-chip active" data-category="">' . __('Todas las Fotos', 'soloylibre-gallery') . '</div>';
        
        foreach ($categories as $category) {
            echo '<div class="filter-chip" data-category="' . esc_attr($category->slug) . '">' . esc_html($category->name) . '</div>';
        }
        echo '</div>';
    }
    
    private function render_album_dropdown() {
        global $wpdb;
        $albums = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}soloylibre_albums WHERE is_published = 1 ORDER BY sort_order ASC");
        
        echo '<div class="view-controls">';
        echo '<span style="font-size: 14px; color: #718096;">' . __('Álbum:', 'soloylibre-gallery') . '</span>';
        echo '<select class="album-select">';
        echo '<option value="">' . __('Todos los Álbumes', 'soloylibre-gallery') . '</option>';
        
        foreach ($albums as $album) {
            echo '<option value="' . esc_attr($album->id) . '">' . esc_html($album->name) . '</option>';
        }
        echo '</select>';
        echo '</div>';
    }
    
    /**
     * Add gallery scripts to footer
     */
    public function add_gallery_scripts() {
        if (is_singular() && has_shortcode(get_post()->post_content, 'soloylibre_gallery')) {
            ?>
            <script>
            jQuery(document).ready(function($) {
                // Initialize gallery functionality
                $('.soloylibre-gallery').each(function() {
                    var $gallery = $(this);
                    var style = $gallery.data('style');
                    
                    // Initialize based on style
                    switch(style) {
                        case 'tiktok':
                            initTikTokGallery($gallery);
                            break;
                        case 'grid':
                            initGridGallery($gallery);
                            break;
                        case 'masonry':
                            initMasonryGallery($gallery);
                            break;
                    }
                });
                
                function initTikTokGallery($gallery) {
                    // TikTok-style infinite scroll
                    var $container = $gallery.find('.soloylibre-tiktok-container');
                    
                    $container.on('scroll', function() {
                        if ($container.scrollTop() + $container.height() >= $container[0].scrollHeight - 100) {
                            loadMorePhotos($gallery);
                        }
                    });
                }
                
                function initGridGallery($gallery) {
                    // Grid gallery with load more button
                    $gallery.find('.soloylibre-load-more-btn').on('click', function() {
                        loadMorePhotos($gallery);
                    });
                }
                
                function initMasonryGallery($gallery) {
                    // Masonry layout with load more
                    $gallery.find('.soloylibre-load-more-btn').on('click', function() {
                        loadMorePhotos($gallery);
                    });
                }
                
                function loadMorePhotos($gallery) {
                    var page = $gallery.data('page') || 1;
                    page++;
                    
                    $.ajax({
                        url: soloylibre_ajax.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'load_more_photos',
                            nonce: soloylibre_ajax.nonce,
                            page: page,
                            style: $gallery.data('style'),
                            album: $gallery.data('album'),
                            category: $gallery.data('category'),
                            limit: $gallery.data('limit')
                        },
                        success: function(response) {
                            if (response.success) {
                                $gallery.find('.soloylibre-grid-container, .soloylibre-tiktok-container, .soloylibre-masonry-container').append(response.data.html);
                                $gallery.data('page', page);
                                
                                if (!response.data.has_more) {
                                    $gallery.find('.soloylibre-load-more-btn').text(soloylibre_ajax.no_more_text).prop('disabled', true);
                                }
                            }
                        }
                    });
                }
            });
            </script>
            <?php
        }
    }
}
