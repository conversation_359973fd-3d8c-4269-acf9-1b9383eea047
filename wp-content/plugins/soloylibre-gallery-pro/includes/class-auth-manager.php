<?php
/**
 * SoloYLibre Photography Platform - Authentication Manager
 * Sistema de autenticación avanzado con 2FA y JWT
 * Desarrollado por JEYKO AI para <PERSON> (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Auth_Manager {
    
    /**
     * JWT Secret Key
     */
    private $jwt_secret;
    
    /**
     * Token expiration time (24 hours)
     */
    private $token_expiration = 86400;
    
    /**
     * Photographer credentials
     */
    private $photographer_credentials = array(
        'username' => 'admin_soloylibre',
        'email' => '<EMAIL>',
        'display_name' => '<PERSON> (JoseTusabe)',
        'role' => 'administrator'
    );
    
    /**
     * Security settings
     */
    private $security_settings = array(
        'max_login_attempts' => 5,
        'lockout_duration' => 1800, // 30 minutes
        'session_timeout' => 3600,  // 1 hour
        'require_2fa' => true,
        'password_min_length' => 12,
        'password_require_special' => true
    );
    
    public function __construct() {
        $this->jwt_secret = $this->get_or_create_jwt_secret();
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Authentication hooks
        add_action('wp_login', array($this, 'handle_successful_login'), 10, 2);
        add_action('wp_login_failed', array($this, 'handle_failed_login'));
        add_action('wp_logout', array($this, 'handle_logout'));
        
        // Security hooks
        add_action('init', array($this, 'check_session_timeout'));
        add_filter('authenticate', array($this, 'check_login_attempts'), 30, 3);
        add_filter('login_redirect', array($this, 'custom_login_redirect'), 10, 3);
        
        // Password security
        add_action('user_profile_update_errors', array($this, 'validate_password_strength'), 10, 3);
        add_filter('wp_authenticate_user', array($this, 'check_user_status'), 10, 2);
        
        // 2FA hooks
        add_action('wp_login', array($this, 'maybe_require_2fa'), 5, 2);
        add_action('login_form', array($this, 'add_2fa_field'));
        
        // API authentication
        add_filter('rest_authentication_errors', array($this, 'jwt_authentication'));
        
        // Security headers
        add_action('send_headers', array($this, 'add_security_headers'));
        
        // Audit logging
        add_action('wp_login', array($this, 'log_security_event'));
        add_action('wp_login_failed', array($this, 'log_security_event'));
        
        // Auto-create photographer account
        add_action('init', array($this, 'ensure_photographer_account'));
    }
    
    /**
     * Ensure photographer account exists
     */
    public function ensure_photographer_account() {
        $user = get_user_by('login', $this->photographer_credentials['username']);
        
        if (!$user) {
            $this->create_photographer_account();
        } else {
            $this->update_photographer_profile($user->ID);
        }
    }
    
    /**
     * Create photographer account
     */
    private function create_photographer_account() {
        $user_data = array(
            'user_login' => $this->photographer_credentials['username'],
            'user_email' => $this->photographer_credentials['email'],
            'user_pass' => 'JoseTusabe2025!',
            'display_name' => $this->photographer_credentials['display_name'],
            'first_name' => 'Jose Luis',
            'last_name' => 'Encarnacion',
            'nickname' => 'JoseTusabe',
            'role' => $this->photographer_credentials['role'],
            'description' => 'Fotógrafo profesional de San José de Ocoa, República Dominicana, especializado en fotografía artística y comercial.'
        );
        
        $user_id = wp_insert_user($user_data);
        
        if (!is_wp_error($user_id)) {
            // Add photographer metadata
            update_user_meta($user_id, 'soloylibre_photographer_brand', 'SoloYLibre Photography');
            update_user_meta($user_id, 'soloylibre_photographer_location', 'San José de Ocoa, Dom. Rep. / USA');
            update_user_meta($user_id, 'soloylibre_photographer_phone', '************');
            update_user_meta($user_id, 'soloylibre_photographer_websites', array(
                'josetusabe.com',
                'soloylibre.com',
                '1and1photo.com',
                'joselencarnacion.com'
            ));
            
            // Security settings
            update_user_meta($user_id, 'soloylibre_2fa_enabled', true);
            update_user_meta($user_id, 'soloylibre_last_login', current_time('mysql'));
            update_user_meta($user_id, 'soloylibre_login_attempts', 0);
            
            // Generate 2FA secret
            $this->generate_2fa_secret($user_id);
            
            $this->log_security_event('photographer_account_created', array(
                'user_id' => $user_id,
                'username' => $this->photographer_credentials['username']
            ));
        }
        
        return $user_id;
    }
    
    /**
     * Update photographer profile
     */
    private function update_photographer_profile($user_id) {
        // Ensure all metadata is up to date
        $metadata_updates = array(
            'soloylibre_photographer_brand' => 'SoloYLibre Photography',
            'soloylibre_photographer_location' => 'San José de Ocoa, Dom. Rep. / USA',
            'soloylibre_photographer_phone' => '************'
        );
        
        foreach ($metadata_updates as $key => $value) {
            update_user_meta($user_id, $key, $value);
        }
    }
    
    /**
     * Handle successful login
     */
    public function handle_successful_login($user_login, $user) {
        // Reset login attempts
        update_user_meta($user->ID, 'soloylibre_login_attempts', 0);
        delete_user_meta($user->ID, 'soloylibre_lockout_time');
        
        // Update last login
        update_user_meta($user->ID, 'soloylibre_last_login', current_time('mysql'));
        
        // Generate session token
        $session_token = $this->generate_session_token($user->ID);
        update_user_meta($user->ID, 'soloylibre_session_token', $session_token);
        update_user_meta($user->ID, 'soloylibre_session_start', time());
        
        // Log successful login
        $this->log_security_event('successful_login', array(
            'user_id' => $user->ID,
            'username' => $user_login,
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ));
        
        // Set secure cookies
        $this->set_secure_cookies($user->ID);
    }
    
    /**
     * Handle failed login
     */
    public function handle_failed_login($username) {
        $user = get_user_by('login', $username);
        
        if ($user) {
            $attempts = intval(get_user_meta($user->ID, 'soloylibre_login_attempts', true));
            $attempts++;
            
            update_user_meta($user->ID, 'soloylibre_login_attempts', $attempts);
            
            if ($attempts >= $this->security_settings['max_login_attempts']) {
                // Lock account
                update_user_meta($user->ID, 'soloylibre_lockout_time', time());
                
                $this->log_security_event('account_locked', array(
                    'user_id' => $user->ID,
                    'username' => $username,
                    'attempts' => $attempts,
                    'ip_address' => $this->get_client_ip()
                ));
            }
        }
        
        $this->log_security_event('failed_login', array(
            'username' => $username,
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ));
    }
    
    /**
     * Check login attempts
     */
    public function check_login_attempts($user, $username, $password) {
        if (is_wp_error($user)) {
            return $user;
        }
        
        $user_obj = get_user_by('login', $username);
        
        if (!$user_obj) {
            return $user;
        }
        
        $lockout_time = get_user_meta($user_obj->ID, 'soloylibre_lockout_time', true);
        
        if ($lockout_time && (time() - $lockout_time) < $this->security_settings['lockout_duration']) {
            $remaining_time = $this->security_settings['lockout_duration'] - (time() - $lockout_time);
            $minutes = ceil($remaining_time / 60);
            
            return new WP_Error(
                'account_locked',
                sprintf(
                    'Cuenta bloqueada por seguridad. Intenta de nuevo en %d minutos.',
                    $minutes
                )
            );
        }
        
        // Reset lockout if time has passed
        if ($lockout_time && (time() - $lockout_time) >= $this->security_settings['lockout_duration']) {
            delete_user_meta($user_obj->ID, 'soloylibre_lockout_time');
            update_user_meta($user_obj->ID, 'soloylibre_login_attempts', 0);
        }
        
        return $user;
    }
    
    /**
     * Generate JWT token
     */
    public function generate_jwt_token($user_id) {
        $header = json_encode(array('typ' => 'JWT', 'alg' => 'HS256'));
        
        $payload = json_encode(array(
            'user_id' => $user_id,
            'username' => get_userdata($user_id)->user_login,
            'photographer' => 'JoseTusabe',
            'brand' => 'SoloYLibre Photography',
            'iat' => time(),
            'exp' => time() + $this->token_expiration
        ));
        
        $base64_header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64_payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
        
        $signature = hash_hmac('sha256', $base64_header . "." . $base64_payload, $this->jwt_secret, true);
        $base64_signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
        
        return $base64_header . "." . $base64_payload . "." . $base64_signature;
    }
    
    /**
     * Verify JWT token
     */
    public function verify_jwt_token($token) {
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return false;
        }
        
        list($header, $payload, $signature) = $parts;
        
        // Verify signature
        $expected_signature = hash_hmac('sha256', $header . "." . $payload, $this->jwt_secret, true);
        $expected_signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($expected_signature));
        
        if (!hash_equals($signature, $expected_signature)) {
            return false;
        }
        
        // Decode payload
        $payload_data = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
        
        // Check expiration
        if ($payload_data['exp'] < time()) {
            return false;
        }
        
        return $payload_data;
    }
    
    /**
     * JWT Authentication for REST API
     */
    public function jwt_authentication($result) {
        if (!empty($result)) {
            return $result;
        }
        
        $auth_header = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        
        if (strpos($auth_header, 'Bearer ') === 0) {
            $token = substr($auth_header, 7);
            $payload = $this->verify_jwt_token($token);
            
            if ($payload) {
                wp_set_current_user($payload['user_id']);
                return true;
            } else {
                return new WP_Error(
                    'jwt_auth_invalid_token',
                    'Token JWT inválido o expirado',
                    array('status' => 401)
                );
            }
        }
        
        return $result;
    }
    
    /**
     * Generate 2FA secret
     */
    private function generate_2fa_secret($user_id) {
        $secret = $this->generate_random_secret(32);
        update_user_meta($user_id, 'soloylibre_2fa_secret', $secret);
        return $secret;
    }
    
    /**
     * Generate random secret
     */
    private function generate_random_secret($length = 32) {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';
        
        for ($i = 0; $i < $length; $i++) {
            $secret .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $secret;
    }
    
    /**
     * Add security headers
     */
    public function add_security_headers() {
        // Security headers for SoloYLibre Photography
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Permissions-Policy: camera=(), microphone=(), geolocation=()');
        
        // Content Security Policy
        $csp = "default-src 'self'; ";
        $csp .= "script-src 'self' 'unsafe-inline' 'unsafe-eval'; ";
        $csp .= "style-src 'self' 'unsafe-inline'; ";
        $csp .= "img-src 'self' data: https:; ";
        $csp .= "font-src 'self' https://fonts.gstatic.com; ";
        $csp .= "connect-src 'self' https://api.soloylibre.com;";
        
        header('Content-Security-Policy: ' . $csp);
    }
    
    /**
     * Log security events
     */
    public function log_security_event($event_type, $data = array()) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'event_type' => $event_type,
            'ip_address' => $this->get_client_ip(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'photographer' => 'JoseTusabe',
            'brand' => 'SoloYLibre Photography',
            'data' => $data
        );
        
        // Store in database
        global $wpdb;
        $wpdb->insert(
            $wpdb->prefix . 'soloylibre_security_log',
            array(
                'event_type' => $event_type,
                'event_data' => json_encode($log_entry),
                'ip_address' => $this->get_client_ip(),
                'user_id' => $data['user_id'] ?? 0,
                'created_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%d', '%s')
        );
        
        // Also log to file for critical events
        if (in_array($event_type, array('failed_login', 'account_locked', 'security_breach'))) {
            error_log('SoloYLibre Security Event: ' . json_encode($log_entry));
        }
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Get or create JWT secret
     */
    private function get_or_create_jwt_secret() {
        $secret = get_option('soloylibre_jwt_secret');
        
        if (!$secret) {
            $secret = wp_generate_password(64, true, true);
            update_option('soloylibre_jwt_secret', $secret);
        }
        
        return $secret;
    }
    
    /**
     * Generate session token
     */
    private function generate_session_token($user_id) {
        return hash('sha256', $user_id . time() . wp_generate_password(32, true, true));
    }
    
    /**
     * Set secure cookies
     */
    private function set_secure_cookies($user_id) {
        $secure = is_ssl();
        $httponly = true;
        $samesite = 'Strict';
        
        // Set photographer session cookie
        setcookie(
            'soloylibre_photographer_session',
            $this->generate_session_token($user_id),
            time() + $this->security_settings['session_timeout'],
            '/',
            '',
            $secure,
            $httponly
        );
    }
    
    /**
     * Custom login redirect
     */
    public function custom_login_redirect($redirect_to, $request, $user) {
        if (isset($user->user_login) && $user->user_login === $this->photographer_credentials['username']) {
            return admin_url('admin.php?page=soloylibre-gallery-dashboard');
        }
        
        return $redirect_to;
    }
    
    /**
     * Check session timeout
     */
    public function check_session_timeout() {
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();
            $session_start = get_user_meta($user_id, 'soloylibre_session_start', true);
            
            if ($session_start && (time() - $session_start) > $this->security_settings['session_timeout']) {
                wp_logout();
                wp_redirect(wp_login_url() . '?session_expired=1');
                exit;
            }
        }
    }
    
    /**
     * Create security log table
     */
    public function create_security_log_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_security_log';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            event_type varchar(50) NOT NULL,
            event_data longtext,
            ip_address varchar(45),
            user_id bigint(20) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY event_type (event_type),
            KEY user_id (user_id),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}
