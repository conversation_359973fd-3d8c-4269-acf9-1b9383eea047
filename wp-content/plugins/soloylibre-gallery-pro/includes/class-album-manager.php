<?php
/**
 * Album Manager Class
 * Handles photo album creation and management
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Album_Manager {
    
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add album meta box to photos
        add_action('add_meta_boxes', array($this, 'add_album_meta_box'));
        add_action('save_post', array($this, 'save_album_meta'));
        
        // AJAX handlers
        add_action('wp_ajax_create_album', array($this, 'ajax_create_album'));
        add_action('wp_ajax_update_album', array($this, 'ajax_update_album'));
        add_action('wp_ajax_delete_album', array($this, 'ajax_delete_album'));
        add_action('wp_ajax_get_album_photos', array($this, 'ajax_get_album_photos'));
        add_action('wp_ajax_add_photos_to_album', array($this, 'ajax_add_photos_to_album'));
        add_action('wp_ajax_remove_photos_from_album', array($this, 'ajax_remove_photos_from_album'));
        add_action('wp_ajax_reorder_album_photos', array($this, 'ajax_reorder_album_photos'));
        
        // Admin columns
        add_filter('manage_soloylibre_photo_posts_columns', array($this, 'add_album_column'));
        add_action('manage_soloylibre_photo_posts_custom_column', array($this, 'display_album_column'), 10, 2);
        
        // Bulk actions
        add_filter('bulk_actions-edit-soloylibre_photo', array($this, 'add_album_bulk_actions'));
        add_filter('handle_bulk_actions-edit-soloylibre_photo', array($this, 'handle_album_bulk_actions'), 10, 3);
    }
    
    /**
     * Create new album
     */
    public function create_album($name, $description = '', $cover_image_id = null, $membership_level = '') {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_albums';
        
        $result = $wpdb->insert(
            $table_name,
            array(
                'name' => sanitize_text_field($name),
                'description' => sanitize_textarea_field($description),
                'cover_image_id' => $cover_image_id ? intval($cover_image_id) : null,
                'membership_level' => sanitize_text_field($membership_level),
                'is_published' => 1,
                'sort_order' => $this->get_next_sort_order(),
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            array('%s', '%s', '%d', '%s', '%d', '%d', '%s', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('album_creation_failed', __('Error al crear el álbum.', 'soloylibre-gallery'));
        }
        
        $album_id = $wpdb->insert_id;
        
        // Log album creation
        $this->log_album_action($album_id, 'created');
        
        return $album_id;
    }
    
    /**
     * Update album
     */
    public function update_album($album_id, $data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_albums';
        
        $update_data = array();
        $format = array();
        
        if (isset($data['name'])) {
            $update_data['name'] = sanitize_text_field($data['name']);
            $format[] = '%s';
        }
        
        if (isset($data['description'])) {
            $update_data['description'] = sanitize_textarea_field($data['description']);
            $format[] = '%s';
        }
        
        if (isset($data['cover_image_id'])) {
            $update_data['cover_image_id'] = $data['cover_image_id'] ? intval($data['cover_image_id']) : null;
            $format[] = '%d';
        }
        
        if (isset($data['membership_level'])) {
            $update_data['membership_level'] = sanitize_text_field($data['membership_level']);
            $format[] = '%s';
        }
        
        if (isset($data['is_published'])) {
            $update_data['is_published'] = intval($data['is_published']);
            $format[] = '%d';
        }
        
        if (isset($data['sort_order'])) {
            $update_data['sort_order'] = intval($data['sort_order']);
            $format[] = '%d';
        }
        
        $update_data['updated_at'] = current_time('mysql');
        $format[] = '%s';
        
        $result = $wpdb->update(
            $table_name,
            $update_data,
            array('id' => intval($album_id)),
            $format,
            array('%d')
        );
        
        if ($result === false) {
            return new WP_Error('album_update_failed', __('Error al actualizar el álbum.', 'soloylibre-gallery'));
        }
        
        // Log album update
        $this->log_album_action($album_id, 'updated', $data);
        
        return true;
    }
    
    /**
     * Delete album
     */
    public function delete_album($album_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_albums';
        
        // Remove photos from album first
        $this->remove_all_photos_from_album($album_id);
        
        $result = $wpdb->delete(
            $table_name,
            array('id' => intval($album_id)),
            array('%d')
        );
        
        if ($result === false) {
            return new WP_Error('album_deletion_failed', __('Error al eliminar el álbum.', 'soloylibre-gallery'));
        }
        
        // Log album deletion
        $this->log_album_action($album_id, 'deleted');
        
        return true;
    }
    
    /**
     * Get album by ID
     */
    public function get_album($album_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_albums';
        
        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $album_id)
        );
    }
    
    /**
     * Get all albums
     */
    public function get_albums($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'published_only' => true,
            'membership_level' => '',
            'orderby' => 'sort_order',
            'order' => 'ASC',
            'limit' => -1,
            'offset' => 0
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $table_name = $wpdb->prefix . 'soloylibre_albums';
        $where_clauses = array();
        $where_values = array();
        
        if ($args['published_only']) {
            $where_clauses[] = 'is_published = %d';
            $where_values[] = 1;
        }
        
        if (!empty($args['membership_level'])) {
            $where_clauses[] = 'membership_level = %s';
            $where_values[] = $args['membership_level'];
        }
        
        $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';
        
        $order_sql = sprintf(
            'ORDER BY %s %s',
            sanitize_sql_orderby($args['orderby']),
            $args['order'] === 'DESC' ? 'DESC' : 'ASC'
        );
        
        $limit_sql = '';
        if ($args['limit'] > 0) {
            $limit_sql = $wpdb->prepare('LIMIT %d', $args['limit']);
            if ($args['offset'] > 0) {
                $limit_sql = $wpdb->prepare('LIMIT %d, %d', $args['offset'], $args['limit']);
            }
        }
        
        $sql = "SELECT * FROM $table_name $where_sql $order_sql $limit_sql";
        
        if (!empty($where_values)) {
            $sql = $wpdb->prepare($sql, $where_values);
        }
        
        return $wpdb->get_results($sql);
    }
    
    /**
     * Get album photos
     */
    public function get_album_photos($album_id, $args = array()) {
        $defaults = array(
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'orderby' => 'meta_value_num',
            'meta_key' => '_soloylibre_sort_order',
            'order' => 'ASC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $args['post_type'] = 'soloylibre_photo';
        $args['meta_query'] = array(
            array(
                'key' => '_soloylibre_album_id',
                'value' => intval($album_id),
                'compare' => '='
            )
        );
        
        return get_posts($args);
    }
    
    /**
     * Add photos to album
     */
    public function add_photos_to_album($album_id, $photo_ids) {
        $album_id = intval($album_id);
        $added_count = 0;
        
        foreach ($photo_ids as $photo_id) {
            $photo_id = intval($photo_id);
            
            if (get_post_type($photo_id) !== 'soloylibre_photo') {
                continue;
            }
            
            // Check if photo is already in album
            $current_album = get_post_meta($photo_id, '_soloylibre_album_id', true);
            if ($current_album == $album_id) {
                continue;
            }
            
            // Add photo to album
            update_post_meta($photo_id, '_soloylibre_album_id', $album_id);
            
            // Set sort order
            $sort_order = $this->get_next_photo_sort_order($album_id);
            update_post_meta($photo_id, '_soloylibre_sort_order', $sort_order);
            
            $added_count++;
        }
        
        // Update album's updated_at timestamp
        $this->update_album($album_id, array());
        
        // Log action
        $this->log_album_action($album_id, 'photos_added', array('count' => $added_count));
        
        return $added_count;
    }
    
    /**
     * Remove photos from album
     */
    public function remove_photos_from_album($album_id, $photo_ids) {
        $album_id = intval($album_id);
        $removed_count = 0;
        
        foreach ($photo_ids as $photo_id) {
            $photo_id = intval($photo_id);
            
            $current_album = get_post_meta($photo_id, '_soloylibre_album_id', true);
            if ($current_album != $album_id) {
                continue;
            }
            
            // Remove from album
            delete_post_meta($photo_id, '_soloylibre_album_id');
            delete_post_meta($photo_id, '_soloylibre_sort_order');
            
            $removed_count++;
        }
        
        // Update album's updated_at timestamp
        $this->update_album($album_id, array());
        
        // Log action
        $this->log_album_action($album_id, 'photos_removed', array('count' => $removed_count));
        
        return $removed_count;
    }
    
    /**
     * Remove all photos from album
     */
    public function remove_all_photos_from_album($album_id) {
        $photos = $this->get_album_photos($album_id);
        $photo_ids = wp_list_pluck($photos, 'ID');
        
        return $this->remove_photos_from_album($album_id, $photo_ids);
    }
    
    /**
     * Reorder album photos
     */
    public function reorder_album_photos($album_id, $photo_order) {
        $album_id = intval($album_id);
        
        foreach ($photo_order as $index => $photo_id) {
            $photo_id = intval($photo_id);
            $sort_order = ($index + 1) * 10; // Leave gaps for future insertions
            
            update_post_meta($photo_id, '_soloylibre_sort_order', $sort_order);
        }
        
        // Update album's updated_at timestamp
        $this->update_album($album_id, array());
        
        // Log action
        $this->log_album_action($album_id, 'photos_reordered');
        
        return true;
    }
    
    /**
     * Get next sort order for albums
     */
    private function get_next_sort_order() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_albums';
        $max_order = $wpdb->get_var("SELECT MAX(sort_order) FROM $table_name");
        
        return ($max_order ? intval($max_order) : 0) + 10;
    }
    
    /**
     * Get next photo sort order in album
     */
    private function get_next_photo_sort_order($album_id) {
        global $wpdb;
        
        $max_order = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT MAX(CAST(meta_value AS UNSIGNED)) 
                 FROM $wpdb->postmeta 
                 WHERE meta_key = '_soloylibre_sort_order' 
                 AND post_id IN (
                     SELECT post_id FROM $wpdb->postmeta 
                     WHERE meta_key = '_soloylibre_album_id' 
                     AND meta_value = %d
                 )",
                $album_id
            )
        );
        
        return ($max_order ? intval($max_order) : 0) + 10;
    }
    
    /**
     * Add album meta box
     */
    public function add_album_meta_box() {
        add_meta_box(
            'soloylibre_album_meta',
            __('📁 Álbum', 'soloylibre-gallery'),
            array($this, 'render_album_meta_box'),
            'soloylibre_photo',
            'side',
            'default'
        );
    }
    
    /**
     * Render album meta box
     */
    public function render_album_meta_box($post) {
        wp_nonce_field('soloylibre_album_meta', 'soloylibre_album_nonce');
        
        $current_album_id = get_post_meta($post->ID, '_soloylibre_album_id', true);
        $albums = $this->get_albums();
        
        ?>
        <div class="soloylibre-album-settings">
            <p>
                <label for="soloylibre_album_id">
                    <strong><?php _e('Asignar a Álbum:', 'soloylibre-gallery'); ?></strong>
                </label>
            </p>
            <select name="soloylibre_album_id" id="soloylibre_album_id" style="width: 100%;">
                <option value=""><?php _e('Sin álbum', 'soloylibre-gallery'); ?></option>
                <?php foreach ($albums as $album): ?>
                    <option value="<?php echo esc_attr($album->id); ?>" <?php selected($current_album_id, $album->id); ?>>
                        <?php echo esc_html($album->name); ?>
                        <?php if (!empty($album->membership_level)): ?>
                            (<?php _e('Premium', 'soloylibre-gallery'); ?>)
                        <?php endif; ?>
                    </option>
                <?php endforeach; ?>
            </select>
            
            <p class="description">
                <?php _e('Selecciona el álbum al que pertenece esta foto.', 'soloylibre-gallery'); ?>
            </p>
            
            <div class="album-actions" style="margin-top: 10px;">
                <button type="button" class="button button-small" id="create-new-album">
                    <?php _e('+ Crear Nuevo Álbum', 'soloylibre-gallery'); ?>
                </button>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#create-new-album').on('click', function() {
                var albumName = prompt('<?php _e("Nombre del nuevo álbum:", "soloylibre-gallery"); ?>');
                if (albumName) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'create_album',
                            nonce: '<?php echo wp_create_nonce("soloylibre_album_nonce"); ?>',
                            name: albumName
                        },
                        success: function(response) {
                            if (response.success) {
                                var $select = $('#soloylibre_album_id');
                                $select.append('<option value="' + response.data.album_id + '">' + albumName + '</option>');
                                $select.val(response.data.album_id);
                                alert('<?php _e("Álbum creado exitosamente", "soloylibre-gallery"); ?>');
                            } else {
                                alert('<?php _e("Error al crear el álbum", "soloylibre-gallery"); ?>');
                            }
                        }
                    });
                }
            });
        });
        </script>
        <?php
    }
    
    /**
     * Save album meta
     */
    public function save_album_meta($post_id) {
        // Check nonce
        if (!isset($_POST['soloylibre_album_nonce']) || 
            !wp_verify_nonce($_POST['soloylibre_album_nonce'], 'soloylibre_album_meta')) {
            return;
        }
        
        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Save album assignment
        if (isset($_POST['soloylibre_album_id'])) {
            $album_id = intval($_POST['soloylibre_album_id']);
            
            if ($album_id > 0) {
                update_post_meta($post_id, '_soloylibre_album_id', $album_id);
                
                // Set sort order if not exists
                $sort_order = get_post_meta($post_id, '_soloylibre_sort_order', true);
                if (empty($sort_order)) {
                    $new_sort_order = $this->get_next_photo_sort_order($album_id);
                    update_post_meta($post_id, '_soloylibre_sort_order', $new_sort_order);
                }
            } else {
                delete_post_meta($post_id, '_soloylibre_album_id');
                delete_post_meta($post_id, '_soloylibre_sort_order');
            }
        }
    }
    
    /**
     * Add album column
     */
    public function add_album_column($columns) {
        $columns['album'] = __('📁 Álbum', 'soloylibre-gallery');
        return $columns;
    }
    
    /**
     * Display album column
     */
    public function display_album_column($column, $post_id) {
        if ($column === 'album') {
            $album_id = get_post_meta($post_id, '_soloylibre_album_id', true);
            
            if ($album_id) {
                $album = $this->get_album($album_id);
                if ($album) {
                    echo esc_html($album->name);
                    if (!empty($album->membership_level)) {
                        echo '<br><small style="color: orange;">🔒 Premium</small>';
                    }
                } else {
                    echo '<span style="color: red;">' . __('Álbum no encontrado', 'soloylibre-gallery') . '</span>';
                }
            } else {
                echo '<span style="color: #666;">' . __('Sin álbum', 'soloylibre-gallery') . '</span>';
            }
        }
    }
    
    /**
     * Add album bulk actions
     */
    public function add_album_bulk_actions($actions) {
        $albums = $this->get_albums();
        
        if (!empty($albums)) {
            $actions['assign_to_album'] = __('Asignar a Álbum', 'soloylibre-gallery');
            $actions['remove_from_album'] = __('Quitar de Álbum', 'soloylibre-gallery');
        }
        
        return $actions;
    }
    
    /**
     * Handle album bulk actions
     */
    public function handle_album_bulk_actions($redirect_to, $action, $post_ids) {
        if (!in_array($action, array('assign_to_album', 'remove_from_album'))) {
            return $redirect_to;
        }
        
        // This would typically show a form to select the album
        // For now, we'll just add a query parameter to handle it in JavaScript
        $redirect_to = add_query_arg('bulk_album_action', $action, $redirect_to);
        $redirect_to = add_query_arg('post_ids', implode(',', $post_ids), $redirect_to);
        
        return $redirect_to;
    }
    
    /**
     * Log album action
     */
    private function log_album_action($album_id, $action, $data = array()) {
        $log_entry = array(
            'album_id' => $album_id,
            'user_id' => get_current_user_id(),
            'timestamp' => current_time('mysql'),
            'action' => $action,
            'data' => $data
        );
        
        $logs = get_option('soloylibre_album_logs', array());
        $logs[] = $log_entry;
        
        // Keep only last 500 entries
        if (count($logs) > 500) {
            $logs = array_slice($logs, -500);
        }
        
        update_option('soloylibre_album_logs', $logs);
    }
    
    /**
     * AJAX create album
     */
    public function ajax_create_album() {
        check_ajax_referer('soloylibre_album_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('No tienes permisos para crear álbumes.', 'soloylibre-gallery'));
        }
        
        $name = sanitize_text_field($_POST['name']);
        $description = isset($_POST['description']) ? sanitize_textarea_field($_POST['description']) : '';
        $membership_level = isset($_POST['membership_level']) ? sanitize_text_field($_POST['membership_level']) : '';
        
        if (empty($name)) {
            wp_send_json_error(__('El nombre del álbum es requerido.', 'soloylibre-gallery'));
        }
        
        $album_id = $this->create_album($name, $description, null, $membership_level);
        
        if (is_wp_error($album_id)) {
            wp_send_json_error($album_id->get_error_message());
        }
        
        wp_send_json_success(array(
            'album_id' => $album_id,
            'message' => __('Álbum creado exitosamente.', 'soloylibre-gallery')
        ));
    }
    
    /**
     * AJAX get album photos
     */
    public function ajax_get_album_photos() {
        check_ajax_referer('soloylibre_gallery_nonce', 'nonce');
        
        $album_id = intval($_POST['album_id']);
        $photos = $this->get_album_photos($album_id);
        
        $photo_data = array();
        foreach ($photos as $photo) {
            $photo_data[] = array(
                'id' => $photo->ID,
                'title' => $photo->post_title,
                'thumbnail' => wp_get_attachment_image_url(get_post_thumbnail_id($photo->ID), 'thumbnail'),
                'edit_url' => get_edit_post_link($photo->ID)
            );
        }
        
        wp_send_json_success($photo_data);
    }
}
