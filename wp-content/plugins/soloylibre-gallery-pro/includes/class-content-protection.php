<?php
/**
 * Content Protection Class
 * Handles content protection and prevents accidental publishing of sensitive photos
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Content_Protection {
    
    private $protection_levels = array(
        'public' => 'Público',
        'members_only' => 'Solo Miembros',
        'premium' => 'Premium',
        'private' => 'Privado',
        'not_publishable' => 'No Publicable'
    );
    
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add protection meta box
        add_action('add_meta_boxes', array($this, 'add_protection_meta_box'));
        add_action('save_post', array($this, 'save_protection_meta'));
        
        // Filter content based on protection level
        add_filter('soloylibre_gallery_photos', array($this, 'filter_protected_content'), 5, 2);
        
        // Add admin notices for protection warnings
        add_action('admin_notices', array($this, 'show_protection_notices'));
        
        // Prevent accidental publishing
        add_action('transition_post_status', array($this, 'prevent_accidental_publishing'), 10, 3);
        
        // Add bulk actions for protection
        add_filter('bulk_actions-edit-soloylibre_photo', array($this, 'add_bulk_protection_actions'));
        add_filter('handle_bulk_actions-edit-soloylibre_photo', array($this, 'handle_bulk_protection_actions'), 10, 3);
        
        // Add protection column to admin list
        add_filter('manage_soloylibre_photo_posts_columns', array($this, 'add_protection_column'));
        add_action('manage_soloylibre_photo_posts_custom_column', array($this, 'display_protection_column'), 10, 2);
        
        // AJAX handlers
        add_action('wp_ajax_toggle_photo_protection', array($this, 'ajax_toggle_protection'));
        add_action('wp_ajax_bulk_update_protection', array($this, 'ajax_bulk_update_protection'));
    }
    
    /**
     * Get protection levels
     */
    public function get_protection_levels() {
        return apply_filters('soloylibre_protection_levels', $this->protection_levels);
    }
    
    /**
     * Check if photo is publishable
     */
    public function is_photo_publishable($photo_id) {
        $protection_level = get_post_meta($photo_id, '_soloylibre_protection_level', true);
        $is_publishable = get_post_meta($photo_id, '_soloylibre_is_publishable', true);
        
        // If explicitly marked as not publishable
        if ($protection_level === 'not_publishable' || $is_publishable === '0') {
            return false;
        }
        
        // Check if photo has been manually reviewed
        $is_reviewed = get_post_meta($photo_id, '_soloylibre_is_reviewed', true);
        
        // Require manual review for safety
        if (get_option('soloylibre_gallery_require_review', true) && !$is_reviewed) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get photo protection status
     */
    public function get_photo_protection_status($photo_id) {
        $protection_level = get_post_meta($photo_id, '_soloylibre_protection_level', true);
        $is_publishable = get_post_meta($photo_id, '_soloylibre_is_publishable', true);
        $is_reviewed = get_post_meta($photo_id, '_soloylibre_is_reviewed', true);
        
        return array(
            'protection_level' => $protection_level ?: 'public',
            'is_publishable' => $is_publishable === '1',
            'is_reviewed' => $is_reviewed === '1',
            'can_publish' => $this->is_photo_publishable($photo_id)
        );
    }
    
    /**
     * Add protection meta box
     */
    public function add_protection_meta_box() {
        add_meta_box(
            'soloylibre_protection_meta',
            __('🔒 Protección de Contenido', 'soloylibre-gallery'),
            array($this, 'render_protection_meta_box'),
            'soloylibre_photo',
            'side',
            'high'
        );
    }
    
    /**
     * Render protection meta box
     */
    public function render_protection_meta_box($post) {
        wp_nonce_field('soloylibre_protection_meta', 'soloylibre_protection_nonce');
        
        $status = $this->get_photo_protection_status($post->ID);
        $protection_levels = $this->get_protection_levels();
        
        ?>
        <div class="soloylibre-protection-settings">
            <!-- Protection Level -->
            <div class="protection-field">
                <label for="soloylibre_protection_level">
                    <strong><?php _e('Nivel de Protección:', 'soloylibre-gallery'); ?></strong>
                </label>
                <select name="soloylibre_protection_level" id="soloylibre_protection_level" style="width: 100%;">
                    <?php foreach ($protection_levels as $level => $label): ?>
                        <option value="<?php echo esc_attr($level); ?>" <?php selected($status['protection_level'], $level); ?>>
                            <?php echo esc_html($label); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <!-- Publishable Checkbox -->
            <div class="protection-field">
                <label>
                    <input type="checkbox" name="soloylibre_is_publishable" value="1" 
                           <?php checked($status['is_publishable']); ?>>
                    <strong><?php _e('✅ Esta foto es segura para publicar', 'soloylibre-gallery'); ?></strong>
                </label>
                <p class="description">
                    <?php _e('Marca esta casilla solo si has revisado cuidadosamente que esta foto no contiene contenido sensible o privado.', 'soloylibre-gallery'); ?>
                </p>
            </div>
            
            <!-- Reviewed Checkbox -->
            <div class="protection-field">
                <label>
                    <input type="checkbox" name="soloylibre_is_reviewed" value="1" 
                           <?php checked($status['is_reviewed']); ?>>
                    <strong><?php _e('👁️ He revisado esta foto manualmente', 'soloylibre-gallery'); ?></strong>
                </label>
                <p class="description">
                    <?php _e('Confirma que has revisado personalmente esta foto antes de permitir su publicación.', 'soloylibre-gallery'); ?>
                </p>
            </div>
            
            <!-- Status Display -->
            <div class="protection-status">
                <h4><?php _e('Estado Actual:', 'soloylibre-gallery'); ?></h4>
                <div id="protection-status-display">
                    <?php echo $this->get_protection_status_html($status); ?>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="protection-actions">
                <h4><?php _e('Acciones Rápidas:', 'soloylibre-gallery'); ?></h4>
                <button type="button" class="button" id="mark-safe-to-publish">
                    <?php _e('✅ Marcar como Segura', 'soloylibre-gallery'); ?>
                </button>
                <button type="button" class="button" id="mark-not-publishable">
                    <?php _e('🚫 Marcar como No Publicable', 'soloylibre-gallery'); ?>
                </button>
            </div>
            
            <!-- Protection Notes -->
            <div class="protection-field">
                <label for="soloylibre_protection_notes">
                    <strong><?php _e('Notas de Protección:', 'soloylibre-gallery'); ?></strong>
                </label>
                <textarea name="soloylibre_protection_notes" id="soloylibre_protection_notes" 
                          rows="3" style="width: 100%;" placeholder="<?php _e('Añade notas sobre por qué esta foto tiene este nivel de protección...', 'soloylibre-gallery'); ?>"><?php echo esc_textarea(get_post_meta($post->ID, '_soloylibre_protection_notes', true)); ?></textarea>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            function updateProtectionStatus() {
                var protectionLevel = $('#soloylibre_protection_level').val();
                var isPublishable = $('#soloylibre_is_publishable').is(':checked');
                var isReviewed = $('#soloylibre_is_reviewed').is(':checked');
                
                var status = {
                    protection_level: protectionLevel,
                    is_publishable: isPublishable,
                    is_reviewed: isReviewed,
                    can_publish: isPublishable && isReviewed && protectionLevel !== 'not_publishable'
                };
                
                $('#protection-status-display').html(getProtectionStatusHtml(status));
            }
            
            function getProtectionStatusHtml(status) {
                var html = '';
                
                if (status.can_publish) {
                    html += '<div class="status-item status-safe"><span class="dashicons dashicons-yes-alt"></span> Esta foto puede ser publicada en la galería</div>';
                } else {
                    html += '<div class="status-item status-warning"><span class="dashicons dashicons-warning"></span> Esta foto NO será mostrada en la galería</div>';
                    
                    if (!status.is_publishable) {
                        html += '<div class="status-reason">• No está marcada como segura para publicar</div>';
                    }
                    if (!status.is_reviewed) {
                        html += '<div class="status-reason">• No ha sido revisada manualmente</div>';
                    }
                    if (status.protection_level === 'not_publishable') {
                        html += '<div class="status-reason">• Marcada como "No Publicable"</div>';
                    }
                }
                
                return html;
            }
            
            // Update status when fields change
            $('#soloylibre_protection_level, #soloylibre_is_publishable, #soloylibre_is_reviewed').on('change', updateProtectionStatus);
            
            // Quick action buttons
            $('#mark-safe-to-publish').on('click', function() {
                $('#soloylibre_protection_level').val('public');
                $('#soloylibre_is_publishable').prop('checked', true);
                $('#soloylibre_is_reviewed').prop('checked', true);
                updateProtectionStatus();
            });
            
            $('#mark-not-publishable').on('click', function() {
                $('#soloylibre_protection_level').val('not_publishable');
                $('#soloylibre_is_publishable').prop('checked', false);
                updateProtectionStatus();
            });
            
            // Initial status update
            updateProtectionStatus();
        });
        </script>
        
        <style>
        .soloylibre-protection-settings {
            padding: 10px 0;
        }
        
        .protection-field {
            margin-bottom: 15px;
        }
        
        .protection-field label {
            display: block;
            margin-bottom: 5px;
        }
        
        .protection-status {
            margin: 15px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        
        .protection-status h4 {
            margin: 0 0 8px 0;
            font-size: 13px;
        }
        
        .status-item {
            padding: 5px 0;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .status-item .dashicons {
            font-size: 16px;
            width: 16px;
            height: 16px;
            vertical-align: middle;
            margin-right: 5px;
        }
        
        .status-safe {
            color: #46b450;
        }
        
        .status-warning {
            color: #dc3232;
        }
        
        .status-reason {
            font-size: 11px;
            color: #666;
            margin-left: 20px;
            margin-top: 2px;
        }
        
        .protection-actions {
            margin: 15px 0;
        }
        
        .protection-actions h4 {
            margin: 0 0 8px 0;
            font-size: 13px;
        }
        
        .protection-actions .button {
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 11px;
            padding: 2px 8px;
            height: auto;
        }
        </style>
        <?php
    }
    
    /**
     * Get protection status HTML
     */
    private function get_protection_status_html($status) {
        $html = '';
        
        if ($status['can_publish']) {
            $html .= '<div class="status-item status-safe"><span class="dashicons dashicons-yes-alt"></span> Esta foto puede ser publicada en la galería</div>';
        } else {
            $html .= '<div class="status-item status-warning"><span class="dashicons dashicons-warning"></span> Esta foto NO será mostrada en la galería</div>';
            
            if (!$status['is_publishable']) {
                $html .= '<div class="status-reason">• No está marcada como segura para publicar</div>';
            }
            if (!$status['is_reviewed']) {
                $html .= '<div class="status-reason">• No ha sido revisada manualmente</div>';
            }
            if ($status['protection_level'] === 'not_publishable') {
                $html .= '<div class="status-reason">• Marcada como "No Publicable"</div>';
            }
        }
        
        return $html;
    }
    
    /**
     * Save protection meta
     */
    public function save_protection_meta($post_id) {
        // Check nonce
        if (!isset($_POST['soloylibre_protection_nonce']) || 
            !wp_verify_nonce($_POST['soloylibre_protection_nonce'], 'soloylibre_protection_meta')) {
            return;
        }
        
        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Save protection level
        if (isset($_POST['soloylibre_protection_level'])) {
            $protection_level = sanitize_text_field($_POST['soloylibre_protection_level']);
            update_post_meta($post_id, '_soloylibre_protection_level', $protection_level);
        }
        
        // Save publishable status
        $is_publishable = isset($_POST['soloylibre_is_publishable']) ? '1' : '0';
        update_post_meta($post_id, '_soloylibre_is_publishable', $is_publishable);
        
        // Save reviewed status
        $is_reviewed = isset($_POST['soloylibre_is_reviewed']) ? '1' : '0';
        update_post_meta($post_id, '_soloylibre_is_reviewed', $is_reviewed);
        
        // Save protection notes
        if (isset($_POST['soloylibre_protection_notes'])) {
            $notes = sanitize_textarea_field($_POST['soloylibre_protection_notes']);
            update_post_meta($post_id, '_soloylibre_protection_notes', $notes);
        }
        
        // Log protection change
        $this->log_protection_change($post_id, get_current_user_id());
    }
    
    /**
     * Filter protected content
     */
    public function filter_protected_content($photos, $args) {
        $filtered_photos = array();
        
        foreach ($photos as $photo) {
            // Only include publishable photos
            if ($this->is_photo_publishable($photo->ID)) {
                $filtered_photos[] = $photo;
            }
        }
        
        return $filtered_photos;
    }
    
    /**
     * Prevent accidental publishing
     */
    public function prevent_accidental_publishing($new_status, $old_status, $post) {
        if ($post->post_type !== 'soloylibre_photo') {
            return;
        }
        
        if ($new_status === 'publish' && !$this->is_photo_publishable($post->ID)) {
            // Prevent publishing and show error
            wp_die(
                __('Esta foto no puede ser publicada porque no cumple con los requisitos de seguridad. Por favor, revisa la configuración de protección.', 'soloylibre-gallery'),
                __('Error de Protección de Contenido', 'soloylibre-gallery'),
                array('back_link' => true)
            );
        }
    }
    
    /**
     * Add protection column to admin list
     */
    public function add_protection_column($columns) {
        $columns['protection_status'] = __('🔒 Protección', 'soloylibre-gallery');
        return $columns;
    }
    
    /**
     * Display protection column
     */
    public function display_protection_column($column, $post_id) {
        if ($column === 'protection_status') {
            $status = $this->get_photo_protection_status($post_id);
            
            if ($status['can_publish']) {
                echo '<span style="color: green;">✅ Publicable</span>';
            } else {
                echo '<span style="color: red;">🚫 Protegida</span>';
            }
            
            echo '<br><small>' . esc_html($this->protection_levels[$status['protection_level']]) . '</small>';
        }
    }
    
    /**
     * Add bulk protection actions
     */
    public function add_bulk_protection_actions($actions) {
        $actions['mark_publishable'] = __('Marcar como Publicables', 'soloylibre-gallery');
        $actions['mark_not_publishable'] = __('Marcar como No Publicables', 'soloylibre-gallery');
        $actions['mark_reviewed'] = __('Marcar como Revisadas', 'soloylibre-gallery');
        
        return $actions;
    }
    
    /**
     * Handle bulk protection actions
     */
    public function handle_bulk_protection_actions($redirect_to, $action, $post_ids) {
        if (!in_array($action, array('mark_publishable', 'mark_not_publishable', 'mark_reviewed'))) {
            return $redirect_to;
        }
        
        $count = 0;
        
        foreach ($post_ids as $post_id) {
            if (get_post_type($post_id) !== 'soloylibre_photo') {
                continue;
            }
            
            switch ($action) {
                case 'mark_publishable':
                    update_post_meta($post_id, '_soloylibre_is_publishable', '1');
                    update_post_meta($post_id, '_soloylibre_is_reviewed', '1');
                    update_post_meta($post_id, '_soloylibre_protection_level', 'public');
                    break;
                    
                case 'mark_not_publishable':
                    update_post_meta($post_id, '_soloylibre_is_publishable', '0');
                    update_post_meta($post_id, '_soloylibre_protection_level', 'not_publishable');
                    break;
                    
                case 'mark_reviewed':
                    update_post_meta($post_id, '_soloylibre_is_reviewed', '1');
                    break;
            }
            
            $count++;
        }
        
        $redirect_to = add_query_arg('bulk_protection_updated', $count, $redirect_to);
        return $redirect_to;
    }
    
    /**
     * Show protection notices
     */
    public function show_protection_notices() {
        if (isset($_GET['bulk_protection_updated'])) {
            $count = intval($_GET['bulk_protection_updated']);
            printf(
                '<div class="notice notice-success is-dismissible"><p>%s</p></div>',
                sprintf(
                    _n(
                        'Se actualizó la protección de %d foto.',
                        'Se actualizó la protección de %d fotos.',
                        $count,
                        'soloylibre-gallery'
                    ),
                    $count
                )
            );
        }
        
        // Show warning if there are unreviewed photos
        $unreviewed_count = $this->get_unreviewed_photos_count();
        if ($unreviewed_count > 0 && get_current_screen()->id === 'edit-soloylibre_photo') {
            printf(
                '<div class="notice notice-warning"><p><strong>%s:</strong> %s <a href="%s">%s</a></p></div>',
                __('Atención', 'soloylibre-gallery'),
                sprintf(
                    _n(
                        'Tienes %d foto sin revisar.',
                        'Tienes %d fotos sin revisar.',
                        $unreviewed_count,
                        'soloylibre-gallery'
                    ),
                    $unreviewed_count
                ),
                admin_url('edit.php?post_type=soloylibre_photo&meta_key=_soloylibre_is_reviewed&meta_value=0'),
                __('Ver fotos sin revisar', 'soloylibre-gallery')
            );
        }
    }
    
    /**
     * Get count of unreviewed photos
     */
    private function get_unreviewed_photos_count() {
        $query = new WP_Query(array(
            'post_type' => 'soloylibre_photo',
            'post_status' => 'any',
            'meta_query' => array(
                'relation' => 'OR',
                array(
                    'key' => '_soloylibre_is_reviewed',
                    'value' => '0',
                    'compare' => '='
                ),
                array(
                    'key' => '_soloylibre_is_reviewed',
                    'compare' => 'NOT EXISTS'
                )
            ),
            'fields' => 'ids'
        ));
        
        return $query->found_posts;
    }
    
    /**
     * Log protection change
     */
    private function log_protection_change($photo_id, $user_id) {
        $log_entry = array(
            'photo_id' => $photo_id,
            'user_id' => $user_id,
            'timestamp' => current_time('mysql'),
            'action' => 'protection_updated',
            'status' => $this->get_photo_protection_status($photo_id)
        );
        
        // Store in option or custom table
        $logs = get_option('soloylibre_protection_logs', array());
        $logs[] = $log_entry;
        
        // Keep only last 1000 entries
        if (count($logs) > 1000) {
            $logs = array_slice($logs, -1000);
        }
        
        update_option('soloylibre_protection_logs', $logs);
    }
    
    /**
     * AJAX toggle protection
     */
    public function ajax_toggle_protection() {
        check_ajax_referer('soloylibre_gallery_nonce', 'nonce');
        
        $photo_id = intval($_POST['photo_id']);
        $protection_type = sanitize_text_field($_POST['protection_type']);
        
        if (!current_user_can('edit_post', $photo_id)) {
            wp_send_json_error(__('No tienes permisos para editar esta foto.', 'soloylibre-gallery'));
        }
        
        switch ($protection_type) {
            case 'publishable':
                $current = get_post_meta($photo_id, '_soloylibre_is_publishable', true);
                $new_value = $current === '1' ? '0' : '1';
                update_post_meta($photo_id, '_soloylibre_is_publishable', $new_value);
                break;
                
            case 'reviewed':
                $current = get_post_meta($photo_id, '_soloylibre_is_reviewed', true);
                $new_value = $current === '1' ? '0' : '1';
                update_post_meta($photo_id, '_soloylibre_is_reviewed', $new_value);
                break;
        }
        
        $status = $this->get_photo_protection_status($photo_id);
        wp_send_json_success($status);
    }
}
