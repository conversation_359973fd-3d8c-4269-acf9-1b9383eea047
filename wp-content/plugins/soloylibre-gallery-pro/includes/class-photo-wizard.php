<?php
/**
 * Photo Management Wizard Class
 * Asistente paso a paso para gestión de fotos
 * Desarrollado por JEYKO AI para Jose L Encarnacion (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Photo_Wizard {
    
    /**
     * Pasos del wizard
     */
    private $wizard_steps = array(
        'welcome' => array(
            'title' => '🎉 Bienvenido al Asistente',
            'description' => 'Te guiaré paso a paso para gestionar tus fotos profesionalmente',
            'icon' => '👋'
        ),
        'load_photos' => array(
            'title' => '📸 Cargar Fotos',
            'description' => 'Selecciona y carga hasta 500 fotos automáticamente',
            'icon' => '📁'
        ),
        'organize_photos' => array(
            'title' => '📂 Organizar Fotos',
            'description' => 'Clasifica tus fotos en los 4 estados disponibles',
            'icon' => '🗂️'
        ),
        'set_interactions' => array(
            'title' => '💝 Configurar Interacciones',
            'description' => 'Genera engagement automático y configura reacciones',
            'icon' => '⚡'
        ),
        'create_albums' => array(
            'title' => '📁 Crear Álbumes',
            'description' => 'Organiza tus fotos en álbumes temáticos',
            'icon' => '🎨'
        ),
        'publish_settings' => array(
            'title' => '🌍 Configurar Publicación',
            'description' => 'Define qué fotos se publican y cómo se muestran',
            'icon' => '🚀'
        ),
        'complete' => array(
            'title' => '✅ ¡Completado!',
            'description' => 'Tu galería está lista para mostrar al mundo',
            'icon' => '🎊'
        )
    );
    
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX handlers
        add_action('wp_ajax_wizard_get_step', array($this, 'ajax_get_step'));
        add_action('wp_ajax_wizard_save_step', array($this, 'ajax_save_step'));
        add_action('wp_ajax_wizard_load_photos', array($this, 'ajax_load_photos'));
        add_action('wp_ajax_wizard_organize_photos', array($this, 'ajax_organize_photos'));
        add_action('wp_ajax_wizard_bulk_process', array($this, 'ajax_bulk_process'));
        add_action('wp_ajax_wizard_reset_progress', array($this, 'ajax_reset_progress'));
        
        // Admin menu
        add_action('admin_menu', array($this, 'add_wizard_menu'));
        
        // Enqueue scripts
        add_action('admin_enqueue_scripts', array($this, 'enqueue_wizard_scripts'));
    }
    
    /**
     * Add wizard menu
     */
    public function add_wizard_menu() {
        add_submenu_page(
            'soloylibre-gallery',
            __('🧙‍♂️ Asistente de Fotos', 'soloylibre-gallery'),
            __('🧙‍♂️ Asistente', 'soloylibre-gallery'),
            'manage_options',
            'soloylibre-gallery-wizard',
            array($this, 'render_wizard_page')
        );
    }
    
    /**
     * Enqueue wizard scripts
     */
    public function enqueue_wizard_scripts($hook) {
        if (strpos($hook, 'soloylibre-gallery-wizard') !== false) {
            wp_enqueue_script(
                'soloylibre-wizard',
                SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/wizard.js',
                array('jquery'),
                SOLOYLIBRE_GALLERY_VERSION,
                true
            );
            
            wp_localize_script('soloylibre-wizard', 'soloylibre_wizard', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('soloylibre_wizard_nonce'),
                'steps' => $this->wizard_steps,
                'strings' => array(
                    'loading' => __('Cargando...', 'soloylibre-gallery'),
                    'error' => __('Error al procesar', 'soloylibre-gallery'),
                    'success' => __('¡Éxito!', 'soloylibre-gallery'),
                    'confirm_reset' => __('¿Estás seguro de reiniciar el progreso?', 'soloylibre-gallery'),
                    'processing' => __('Procesando fotos...', 'soloylibre-gallery'),
                    'complete' => __('¡Proceso completado!', 'soloylibre-gallery')
                )
            ));
            
            wp_enqueue_style(
                'soloylibre-wizard',
                SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/css/wizard.css',
                array(),
                SOLOYLIBRE_GALLERY_VERSION
            );
        }
    }
    
    /**
     * Render wizard page
     */
    public function render_wizard_page() {
        $current_step = get_option('soloylibre_wizard_current_step', 'welcome');
        $wizard_progress = get_option('soloylibre_wizard_progress', array());
        
        ?>
        <div class="wrap soloylibre-wizard-container">
            <div class="wizard-header">
                <h1>
                    <span class="wizard-icon">🧙‍♂️</span>
                    Asistente de Gestión de Fotos SoloYLibre
                </h1>
                <p class="wizard-subtitle">
                    Sistema profesional paso a paso para Jose L Encarnacion (JoseTusabe)
                </p>
            </div>
            
            <!-- Progress Bar -->
            <div class="wizard-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="wizard-progress-fill"></div>
                </div>
                <div class="progress-steps">
                    <?php foreach ($this->wizard_steps as $step_key => $step_info): ?>
                        <div class="progress-step <?php echo $step_key === $current_step ? 'active' : ''; ?>" 
                             data-step="<?php echo esc_attr($step_key); ?>">
                            <div class="step-icon"><?php echo $step_info['icon']; ?></div>
                            <div class="step-title"><?php echo esc_html($step_info['title']); ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Wizard Content -->
            <div class="wizard-content" id="wizard-content">
                <!-- Content will be loaded via AJAX -->
            </div>
            
            <!-- Wizard Navigation -->
            <div class="wizard-navigation">
                <button type="button" class="button button-secondary" id="wizard-prev" style="display: none;">
                    ← Anterior
                </button>
                
                <div class="wizard-actions">
                    <button type="button" class="button" id="wizard-reset">
                        🔄 Reiniciar
                    </button>
                    <button type="button" class="button" id="wizard-save">
                        💾 Guardar Progreso
                    </button>
                </div>
                
                <button type="button" class="button button-primary" id="wizard-next">
                    Siguiente →
                </button>
            </div>
        </div>
        
        <!-- Loading Overlay -->
        <div class="wizard-loading" id="wizard-loading" style="display: none;">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">Procesando...</div>
                <div class="loading-progress">
                    <div class="loading-bar">
                        <div class="loading-fill" id="loading-progress-fill"></div>
                    </div>
                    <div class="loading-percentage" id="loading-percentage">0%</div>
                </div>
            </div>
        </div>
        
        <style>
        .soloylibre-wizard-container {
            max-width: 1200px;
            margin: 20px auto;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .wizard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .wizard-header h1 {
            margin: 0;
            font-size: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .wizard-icon {
            font-size: 36px;
        }
        
        .wizard-subtitle {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .wizard-progress {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .progress-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.5s ease;
            width: 14.28%; /* 1/7 steps */
        }
        
        .progress-steps {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .progress-step {
            flex: 1;
            min-width: 120px;
            text-align: center;
            padding: 15px 10px;
            background: white;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .progress-step.active {
            border-color: #667eea;
            background: #f0f8ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }
        
        .progress-step.completed {
            border-color: #28a745;
            background: #f0fff4;
        }
        
        .step-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .step-title {
            font-size: 12px;
            font-weight: 600;
            color: #333;
        }
        
        .wizard-content {
            padding: 40px;
            min-height: 400px;
        }
        
        .wizard-navigation {
            padding: 20px 40px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .wizard-actions {
            display: flex;
            gap: 10px;
        }
        
        .wizard-loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            text-align: center;
            min-width: 300px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        
        .loading-progress {
            margin-top: 20px;
        }
        
        .loading-bar {
            height: 6px;
            background: #f3f3f3;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .loading-fill {
            height: 100%;
            background: #667eea;
            transition: width 0.3s ease;
            width: 0%;
        }
        
        .loading-percentage {
            font-size: 14px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .soloylibre-wizard-container {
                margin: 10px;
            }
            
            .wizard-header {
                padding: 20px;
            }
            
            .wizard-header h1 {
                font-size: 22px;
                flex-direction: column;
                gap: 10px;
            }
            
            .wizard-progress {
                padding: 20px;
            }
            
            .progress-steps {
                flex-direction: column;
            }
            
            .wizard-content {
                padding: 20px;
            }
            
            .wizard-navigation {
                padding: 15px 20px;
                flex-direction: column;
                gap: 15px;
            }
            
            .wizard-actions {
                order: 2;
            }
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // Initialize wizard
            SoloYLibreWizard.init();
            
            // Load current step
            SoloYLibreWizard.loadStep('<?php echo esc_js($current_step); ?>');
        });
        </script>
        <?php
    }
    
    /**
     * Get wizard step content
     */
    public function get_step_content($step) {
        switch ($step) {
            case 'welcome':
                return $this->render_welcome_step();
            case 'load_photos':
                return $this->render_load_photos_step();
            case 'organize_photos':
                return $this->render_organize_photos_step();
            case 'set_interactions':
                return $this->render_interactions_step();
            case 'create_albums':
                return $this->render_albums_step();
            case 'publish_settings':
                return $this->render_publish_step();
            case 'complete':
                return $this->render_complete_step();
            default:
                return $this->render_welcome_step();
        }
    }
    
    /**
     * Render welcome step
     */
    private function render_welcome_step() {
        ob_start();
        ?>
        <div class="wizard-step-content welcome-step">
            <div class="welcome-hero">
                <div class="hero-icon">🎉</div>
                <h2>¡Bienvenido al Asistente de SoloYLibre!</h2>
                <p class="hero-description">
                    Hola <strong>Jose L Encarnacion (JoseTusabe)</strong>, este asistente te guiará paso a paso 
                    para gestionar tus fotografías de manera profesional.
                </p>
            </div>
            
            <div class="welcome-features">
                <h3>🚀 Lo que haremos juntos:</h3>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">📸</div>
                        <h4>Cargar hasta 500 fotos</h4>
                        <p>Selección automática con opciones de ordenamiento</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📂</div>
                        <h4>Organizar en 4 estados</h4>
                        <p>Público, Privado, Solo Para Mis Ojos, Basura</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">💝</div>
                        <h4>Generar engagement</h4>
                        <p>Interacciones automáticas y reacciones</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📁</div>
                        <h4>Crear álbumes</h4>
                        <p>Organización temática profesional</p>
                    </div>
                </div>
            </div>
            
            <div class="welcome-info">
                <div class="info-card">
                    <h4>📍 Tu Información</h4>
                    <div class="photographer-info">
                        <p><strong>Nombre:</strong> Jose L Encarnacion</p>
                        <p><strong>Alias:</strong> JoseTusabe</p>
                        <p><strong>Marca:</strong> SoloYLibre Photography</p>
                        <p><strong>Ubicación:</strong> San José de Ocoa, Dom. Rep. / USA</p>
                        <p><strong>Teléfono:</strong> ************</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                    </div>
                </div>
                
                <div class="info-card">
                    <h4>⏱️ Tiempo Estimado</h4>
                    <p>Este proceso tomará aproximadamente <strong>10-15 minutos</strong> dependiendo de la cantidad de fotos que tengas.</p>
                    <p>Puedes pausar y continuar en cualquier momento.</p>
                </div>
            </div>
            
            <div class="welcome-actions">
                <div class="action-buttons">
                    <button type="button" class="button button-primary button-large" id="start-wizard">
                        🚀 Comenzar Asistente
                    </button>
                    <button type="button" class="button button-secondary" id="skip-wizard">
                        ⏭️ Ir Directamente al Dashboard
                    </button>
                </div>
            </div>
        </div>
        
        <style>
        .welcome-step {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .welcome-hero {
            margin-bottom: 40px;
        }
        
        .hero-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        .welcome-hero h2 {
            font-size: 32px;
            color: #333;
            margin-bottom: 15px;
        }
        
        .hero-description {
            font-size: 18px;
            color: #666;
            line-height: 1.6;
        }
        
        .welcome-features {
            margin-bottom: 40px;
            text-align: left;
        }
        
        .welcome-features h3 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            color: #333;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }
        
        .feature-card h4 {
            margin-bottom: 10px;
            color: #333;
            font-size: 16px;
        }
        
        .feature-card p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
        
        .welcome-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .info-card {
            background: #f0f8ff;
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            text-align: left;
        }
        
        .info-card h4 {
            margin-bottom: 15px;
            color: #333;
            font-size: 18px;
        }
        
        .photographer-info p {
            margin: 8px 0;
            color: #555;
            font-size: 14px;
        }
        
        .welcome-actions {
            margin-top: 40px;
        }
        
        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .button-large {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .welcome-hero h2 {
                font-size: 24px;
            }
            
            .hero-description {
                font-size: 16px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-info {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
        </style>
        <?php
        return ob_get_clean();
    }
    
    /**
     * AJAX get step
     */
    public function ajax_get_step() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $step = sanitize_text_field($_POST['step']);
        $content = $this->get_step_content($step);
        
        wp_send_json_success(array(
            'content' => $content,
            'step' => $step
        ));
    }
    
    /**
     * AJAX save step
     */
    public function ajax_save_step() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $step = sanitize_text_field($_POST['step']);
        $data = $_POST['data'];
        
        // Save step data
        $wizard_data = get_option('soloylibre_wizard_data', array());
        $wizard_data[$step] = $data;
        update_option('soloylibre_wizard_data', $wizard_data);
        
        // Update current step
        update_option('soloylibre_wizard_current_step', $step);
        
        wp_send_json_success(array(
            'message' => __('Progreso guardado', 'soloylibre-gallery')
        ));
    }
}
