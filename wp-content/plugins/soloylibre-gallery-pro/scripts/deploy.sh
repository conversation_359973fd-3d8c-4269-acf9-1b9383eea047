#!/bin/bash

# SoloYLibre Photography Platform - Deployment Script
# Optimizado para Synology RS3618xs
# Desarrollado por JEYKO AI para <PERSON> <PERSON> Encarnacion (JoseTusabe)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Photographer Information
PHOTOGRAPHER_NAME="Jose L Encarnacion"
PHOTOGRAPHER_ALIAS="JoseTusabe"
PHOTOGRAPHER_BRAND="SoloYLibre Photography"
PHOTOGRAPHER_LOCATION="San José de Ocoa, Dom. Rep. / USA"
PHOTOGRAPHER_PHONE="************"
PHOTOGRAPHER_EMAIL="<EMAIL>"

# Configuration
PROJECT_NAME="soloylibre-photography"
DOCKER_COMPOSE_FILE="docker-compose.yml"
BACKUP_DIR="/volume1/docker/soloylibre/backups"
LOG_DIR="/volume1/docker/soloylibre/logs"

# Functions
print_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                SoloYLibre Photography Platform              ║"
    echo "║                    Deployment Script                        ║"
    echo "║                                                              ║"
    echo "║  Photographer: ${PHOTOGRAPHER_NAME} (${PHOTOGRAPHER_ALIAS})                    ║"
    echo "║  Location: ${PHOTOGRAPHER_LOCATION}                ║"
    echo "║  Phone: ${PHOTOGRAPHER_PHONE}                              ║"
    echo "║  Email: ${PHOTOGRAPHER_EMAIL}                      ║"
    echo "║                                                              ║"
    echo "║  Server: Synology RS3618xs (56GB RAM, 36TB Storage)         ║"
    echo "║  Developed by: JEYKO AI                                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running on Synology
check_synology() {
    print_step "Checking if running on Synology NAS..."
    
    if [ -f /etc/synoinfo.conf ]; then
        SYNO_MODEL=$(grep "upnpmodelname" /etc/synoinfo.conf | cut -d'"' -f2)
        print_success "Detected Synology model: $SYNO_MODEL"
        
        if [ "$SYNO_MODEL" = "RS3618xs" ]; then
            print_success "Perfect! Running on target server: Synology RS3618xs"
        else
            print_warning "Running on $SYNO_MODEL instead of RS3618xs"
        fi
    else
        print_warning "Not running on Synology NAS"
    fi
}

# Check system requirements
check_requirements() {
    print_step "Checking system requirements..."
    
    # Check Docker
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        print_success "Docker found: $DOCKER_VERSION"
    else
        print_error "Docker not found. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
        print_success "Docker Compose found: $COMPOSE_VERSION"
    else
        print_error "Docker Compose not found. Please install Docker Compose first."
        exit 1
    fi
    
    # Check available memory
    TOTAL_MEM=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$TOTAL_MEM" -ge 50 ]; then
        print_success "Memory check passed: ${TOTAL_MEM}GB available"
    else
        print_warning "Low memory detected: ${TOTAL_MEM}GB (recommended: 56GB+)"
    fi
    
    # Check available disk space
    AVAILABLE_SPACE=$(df -h /volume1 2>/dev/null | awk 'NR==2{print $4}' || echo "Unknown")
    print_info "Available storage: $AVAILABLE_SPACE"
}

# Create directory structure
create_directories() {
    print_step "Creating directory structure..."
    
    DIRECTORIES=(
        "/volume1/docker/soloylibre"
        "/volume1/docker/soloylibre/wordpress"
        "/volume1/docker/soloylibre/mysql"
        "/volume1/docker/soloylibre/redis"
        "/volume1/docker/soloylibre/prometheus"
        "/volume1/docker/soloylibre/grafana"
        "/volume1/docker/soloylibre/backups"
        "/volume1/docker/soloylibre/logs"
        "/volume1/docker/soloylibre/ssl"
        "/volume1/docker/soloylibre/nginx"
        "/volume1/docker/soloylibre/monitoring"
        "./wp-content/plugins"
        "./wp-content/themes"
        "./wp-content/uploads"
        "./nginx/conf.d"
        "./mysql/conf.d"
        "./mysql/init"
        "./monitoring"
        "./ssl"
        "./logs"
        "./backups"
    )
    
    for dir in "${DIRECTORIES[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        else
            print_info "Directory already exists: $dir"
        fi
    done
    
    # Set proper permissions
    chmod -R 755 /volume1/docker/soloylibre
    chown -R 1000:1000 ./wp-content 2>/dev/null || true
}

# Copy plugin files
copy_plugin_files() {
    print_step "Copying SoloYLibre Gallery Pro plugin files..."
    
    PLUGIN_DIR="./wp-content/plugins/soloylibre-gallery-pro"
    
    if [ ! -d "$PLUGIN_DIR" ]; then
        mkdir -p "$PLUGIN_DIR"
    fi
    
    # Copy all plugin files
    cp -r includes/ "$PLUGIN_DIR/" 2>/dev/null || true
    cp -r assets/ "$PLUGIN_DIR/" 2>/dev/null || true
    cp -r docs/ "$PLUGIN_DIR/" 2>/dev/null || true
    cp soloylibre-gallery-plugin.php "$PLUGIN_DIR/" 2>/dev/null || true
    cp readme.txt "$PLUGIN_DIR/" 2>/dev/null || true
    
    print_success "Plugin files copied to $PLUGIN_DIR"
}

# Generate SSL certificates
generate_ssl() {
    print_step "Generating SSL certificates..."
    
    SSL_DIR="./ssl"
    
    if [ ! -f "$SSL_DIR/soloylibre.crt" ]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$SSL_DIR/soloylibre.key" \
            -out "$SSL_DIR/soloylibre.crt" \
            -subj "/C=DO/ST=San Jose de Ocoa/L=San Jose de Ocoa/O=SoloYLibre Photography/OU=IT Department/CN=soloylibre.local/emailAddress=$PHOTOGRAPHER_EMAIL"
        
        print_success "SSL certificates generated"
    else
        print_info "SSL certificates already exist"
    fi
}

# Create configuration files
create_configs() {
    print_step "Creating configuration files..."
    
    # Nginx configuration
    cat > ./nginx/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    include /etc/nginx/conf.d/*.conf;
}
EOF

    # Nginx site configuration
    cat > ./nginx/conf.d/soloylibre.conf << 'EOF'
server {
    listen 80;
    server_name soloylibre.local josetusabe.com soloylibre.com 1and1photo.com joselencarnacion.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name soloylibre.local josetusabe.com soloylibre.com 1and1photo.com joselencarnacion.com;
    
    root /var/www/html;
    index index.php index.html index.htm;
    
    # SSL Configuration
    ssl_certificate /etc/nginx/ssl/soloylibre.crt;
    ssl_certificate_key /etc/nginx/ssl/soloylibre.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Photography-specific optimizations
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Photographer "Jose L Encarnacion (JoseTusabe)";
        add_header X-Brand "SoloYLibre Photography";
    }
    
    # WordPress configuration
    location / {
        try_files $uri $uri/ /index.php?$args;
    }
    
    location ~ \.php$ {
        fastcgi_pass wordpress:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_read_timeout 300;
    }
    
    # Security
    location ~ /\.ht {
        deny all;
    }
    
    location ~ /wp-config.php {
        deny all;
    }
}
EOF

    # MySQL configuration
    cat > ./mysql/conf.d/soloylibre.cnf << 'EOF'
[mysqld]
# SoloYLibre Photography Platform MySQL Configuration
# Optimized for Synology RS3618xs (56GB RAM)

# General
default-storage-engine = InnoDB
sql-mode = "STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"

# Connection
max_connections = 200
max_connect_errors = 1000000

# Memory
innodb_buffer_pool_size = 4G
innodb_log_buffer_size = 32M
innodb_buffer_pool_instances = 4

# Logging
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# Performance
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1
innodb_open_files = 400

# Photography-specific optimizations
max_allowed_packet = 100M
innodb_lock_wait_timeout = 120
EOF

    print_success "Configuration files created"
}

# Deploy the application
deploy_application() {
    print_step "Deploying SoloYLibre Photography Platform..."
    
    # Pull latest images
    docker-compose pull
    
    # Build and start services
    docker-compose up -d
    
    # Wait for services to be ready
    print_info "Waiting for services to start..."
    sleep 30
    
    # Check service status
    docker-compose ps
    
    print_success "Application deployed successfully!"
}

# Create initial WordPress configuration
setup_wordpress() {
    print_step "Setting up WordPress for SoloYLibre Photography..."
    
    # Wait for WordPress to be ready
    print_info "Waiting for WordPress to be ready..."
    
    until curl -s http://localhost:8080 > /dev/null; do
        sleep 5
        print_info "Still waiting for WordPress..."
    done
    
    print_success "WordPress is ready!"
    
    # Install WordPress CLI
    docker-compose exec wordpress bash -c "
        curl -O https://raw.githubusercontent.com/wp-cli/wp-cli/v2.8.1/phar/wp-cli.phar
        chmod +x wp-cli.phar
        mv wp-cli.phar /usr/local/bin/wp
    "
    
    # Configure WordPress
    docker-compose exec wordpress wp core install \
        --url="http://localhost:8080" \
        --title="$PHOTOGRAPHER_BRAND" \
        --admin_user="admin_soloylibre" \
        --admin_password="JoseTusabe2025!" \
        --admin_email="$PHOTOGRAPHER_EMAIL" \
        --allow-root
    
    # Activate SoloYLibre Gallery Pro plugin
    docker-compose exec wordpress wp plugin activate soloylibre-gallery-pro --allow-root
    
    print_success "WordPress configured for $PHOTOGRAPHER_NAME ($PHOTOGRAPHER_ALIAS)"
}

# Create backup script
create_backup_script() {
    print_step "Creating backup script..."
    
    cat > ./scripts/backup.sh << 'EOF'
#!/bin/bash

# SoloYLibre Photography Platform - Backup Script
# Automated backup for Jose L Encarnacion (JoseTusabe)

BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
PHOTOGRAPHER="JoseTusabe"

echo "Starting backup for $PHOTOGRAPHER at $DATE"

# Create backup directory
mkdir -p "$BACKUP_DIR/$DATE"

# Backup WordPress files
tar -czf "$BACKUP_DIR/$DATE/wordpress_$DATE.tar.gz" -C /backup/wordpress .

# Backup MySQL database
mysqldump -h db -u soloylibre_user -pJoseTusabe2025!SecureDB soloylibre_photography > "$BACKUP_DIR/$DATE/database_$DATE.sql"

# Compress database backup
gzip "$BACKUP_DIR/$DATE/database_$DATE.sql"

# Create backup manifest
cat > "$BACKUP_DIR/$DATE/manifest.txt" << EOL
SoloYLibre Photography Platform Backup
Photographer: Jose L Encarnacion (JoseTusabe)
Date: $DATE
Files:
- wordpress_$DATE.tar.gz
- database_$DATE.sql.gz
EOL

# Cleanup old backups (keep last 30 days)
find "$BACKUP_DIR" -type d -mtime +30 -exec rm -rf {} \;

echo "Backup completed for $PHOTOGRAPHER"
EOF

    chmod +x ./scripts/backup.sh
    print_success "Backup script created"
}

# Show deployment summary
show_summary() {
    print_step "Deployment Summary"
    
    echo -e "${GREEN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              🎉 DEPLOYMENT SUCCESSFUL! 🎉                   ║"
    echo "╠══════════════════════════════════════════════════════════════╣"
    echo "║                                                              ║"
    echo "║  SoloYLibre Photography Platform is now running!            ║"
    echo "║                                                              ║"
    echo "║  👤 Photographer: ${PHOTOGRAPHER_NAME} (${PHOTOGRAPHER_ALIAS})                    ║"
    echo "║  🌍 Location: ${PHOTOGRAPHER_LOCATION}                ║"
    echo "║  📞 Phone: ${PHOTOGRAPHER_PHONE}                              ║"
    echo "║  📧 Email: ${PHOTOGRAPHER_EMAIL}                      ║"
    echo "║                                                              ║"
    echo "║  🔗 Access URLs:                                             ║"
    echo "║     • WordPress: http://localhost:8080                      ║"
    echo "║     • Admin: http://localhost:8080/wp-admin                  ║"
    echo "║     • PhpMyAdmin: http://localhost:8081                      ║"
    echo "║     • Grafana: http://localhost:3000                        ║"
    echo "║     • Prometheus: http://localhost:9090                      ║"
    echo "║                                                              ║"
    echo "║  🔐 Credentials:                                             ║"
    echo "║     • Username: admin_soloylibre                            ║"
    echo "║     • Password: JoseTusabe2025!                             ║"
    echo "║                                                              ║"
    echo "║  🚀 Server: Synology RS3618xs (56GB RAM, 36TB Storage)      ║"
    echo "║  💻 Developed by: JEYKO AI                                   ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Main execution
main() {
    print_header
    
    check_synology
    check_requirements
    create_directories
    copy_plugin_files
    generate_ssl
    create_configs
    create_backup_script
    deploy_application
    setup_wordpress
    
    show_summary
    
    print_success "SoloYLibre Photography Platform deployment completed!"
    print_info "Visit http://localhost:8080 to access your photography platform"
    print_info "Use credentials: admin_soloylibre / JoseTusabe2025!"
}

# Run main function
main "$@"
