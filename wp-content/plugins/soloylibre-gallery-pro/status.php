<?php
/**
 * Status Check para SoloYLibre Gallery Pro
 * Verificación del estado del sistema
 */

echo "<h1>🔍 SoloYLibre Gallery Pro - Estado del Sistema</h1>";

// Verificar PHP
echo "<h2>📋 Información del Sistema</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>SQLite Support:</strong> " . (class_exists('PDO') && in_array('sqlite', PDO::getAvailableDrivers()) ? '✅ Disponible' : '❌ No disponible') . "</p>";

// Verificar archivos
echo "<h2>📁 Archivos del Sistema</h2>";
$files_to_check = array(
    'wp-config.php' => 'Configuración de WordPress',
    'wp-content/db.php' => 'Manejador de Base de Datos SQLite',
    'wp-content/plugins/soloylibre-gallery-pro/soloylibre-gallery-plugin.php' => 'Plugin Principal',
    'wp-content/database/' => 'Directorio de Base de Datos'
);

foreach ($files_to_check as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? '✅' : '❌';
    echo "<p><strong>{$description}:</strong> {$status} {$file}</p>";
}

// Verificar base de datos
echo "<h2>🗄️ Base de Datos</h2>";
try {
    $db_file = 'wp-content/database/soloylibre_gallery.sqlite';
    if (file_exists($db_file)) {
        $pdo = new PDO('sqlite:' . $db_file);
        $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
        echo "<p><strong>Base de Datos:</strong> ✅ Conectada</p>";
        echo "<p><strong>Tablas:</strong> " . count($tables) . " tablas encontradas</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>{$table}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p><strong>Base de Datos:</strong> ❌ No encontrada</p>";
    }
} catch (Exception $e) {
    echo "<p><strong>Error de Base de Datos:</strong> " . $e->getMessage() . "</p>";
}

// Verificar WordPress
echo "<h2>🌐 WordPress</h2>";
try {
    require_once('wp-config.php');
    
    if (file_exists('wp-load.php')) {
        require_once('wp-load.php');
        echo "<p><strong>WordPress:</strong> ✅ Cargado correctamente</p>";
        
        // Verificar usuario
        global $wpdb;
        if ($wpdb && $wpdb->ready) {
            $user_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}");
            echo "<p><strong>Usuarios:</strong> {$user_count} usuarios en la base de datos</p>";
            
            $admin_user = $wpdb->get_row("SELECT * FROM {$wpdb->users} WHERE user_login = 'admin_soloylibre'");
            if ($admin_user) {
                echo "<p><strong>Usuario Admin:</strong> ✅ admin_soloylibre existe</p>";
            } else {
                echo "<p><strong>Usuario Admin:</strong> ❌ No encontrado</p>";
            }
        }
        
        // Verificar plugin
        if (function_exists('is_plugin_active')) {
            $plugin_active = is_plugin_active('soloylibre-gallery-pro/soloylibre-gallery-plugin.php');
            echo "<p><strong>Plugin SoloYLibre:</strong> " . ($plugin_active ? '✅ Activo' : '❌ Inactivo') . "</p>";
        }
        
    } else {
        echo "<p><strong>WordPress:</strong> ❌ wp-load.php no encontrado</p>";
    }
} catch (Exception $e) {
    echo "<p><strong>Error de WordPress:</strong> " . $e->getMessage() . "</p>";
}

// Enlaces de acceso
echo "<h2>🔗 Enlaces de Acceso</h2>";
echo "<p><a href='/' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Sitio Principal</a></p>";
echo "<p><a href='/bypass-login.php' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔓 Bypass Login</a></p>";
echo "<p><a href='/wp-admin/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 WordPress Admin</a></p>";

echo "<h2>📞 Información de Contacto</h2>";
echo "<p><strong>Desarrollado por:</strong> JEYKO AI</p>";
echo "<p><strong>Para:</strong> Jose L Encarnacion (JoseTusabe)</p>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Teléfono:</strong> ************</p>";
echo "<p><strong>Ubicación:</strong> San José de Ocoa, Dom. Rep. / USA</p>";

// CSS básico
echo "<style>
body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
h1 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
h2 { color: #555; margin-top: 30px; }
p { margin: 10px 0; }
ul { margin: 10px 0 10px 20px; }
a { margin-right: 10px; display: inline-block; margin-bottom: 10px; }
</style>";
?>
