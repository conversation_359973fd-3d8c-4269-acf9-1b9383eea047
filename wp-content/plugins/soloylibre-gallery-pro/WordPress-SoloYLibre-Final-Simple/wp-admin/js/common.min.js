/*! This file is auto-generated */
!function(B,W){var $=B(document),H=B(W),q=B(document.body),Q=wp.i18n.__,i=wp.i18n.sprintf;function r(e,t,n){n=void 0!==n?i(Q("%1$s is deprecated since version %2$s! Use %3$s instead."),e,t,n):i(Q("%1$s is deprecated since version %2$s with no alternative available."),e,t);W.console.warn(n)}function e(i,o,a){var s={};return Object.keys(o).forEach(function(e){var t=o[e],n=i+"."+e;"object"==typeof t?Object.defineProperty(s,e,{get:function(){return r(n,a,t.alternative),t.func()}}):Object.defineProperty(s,e,{get:function(){return r(n,a,"wp.i18n"),t}})}),s}W.wp.deprecateL10nObject=e,W.commonL10n=W.commonL10n||{warnDelete:"",dismiss:"",collapseMenu:"",expandMenu:""},W.commonL10n=e("commonL10n",W.commonL10n,"5.5.0"),W.wpPointerL10n=W.wpPointerL10n||{dismiss:""},W.wpPointerL10n=e("wpPointerL10n",W.wpPointerL10n,"5.5.0"),W.userProfileL10n=W.userProfileL10n||{warn:"",warnWeak:"",show:"",hide:"",cancel:"",ariaShow:"",ariaHide:""},W.userProfileL10n=e("userProfileL10n",W.userProfileL10n,"5.5.0"),W.privacyToolsL10n=W.privacyToolsL10n||{noDataFound:"",foundAndRemoved:"",noneRemoved:"",someNotRemoved:"",removalError:"",emailSent:"",noExportFile:"",exportError:""},W.privacyToolsL10n=e("privacyToolsL10n",W.privacyToolsL10n,"5.5.0"),W.authcheckL10n={beforeunload:""},W.authcheckL10n=W.authcheckL10n||e("authcheckL10n",W.authcheckL10n,"5.5.0"),W.tagsl10n={noPerm:"",broken:""},W.tagsl10n=W.tagsl10n||e("tagsl10n",W.tagsl10n,"5.5.0"),W.adminCommentsL10n=W.adminCommentsL10n||{hotkeys_highlight_first:{alternative:"window.adminCommentsSettings.hotkeys_highlight_first",func:function(){return W.adminCommentsSettings.hotkeys_highlight_first}},hotkeys_highlight_last:{alternative:"window.adminCommentsSettings.hotkeys_highlight_last",func:function(){return W.adminCommentsSettings.hotkeys_highlight_last}},replyApprove:"",reply:"",warnQuickEdit:"",warnCommentChanges:"",docTitleComments:"",docTitleCommentsCount:""},W.adminCommentsL10n=e("adminCommentsL10n",W.adminCommentsL10n,"5.5.0"),W.tagsSuggestL10n=W.tagsSuggestL10n||{tagDelimiter:"",removeTerm:"",termSelected:"",termAdded:"",termRemoved:""},W.tagsSuggestL10n=e("tagsSuggestL10n",W.tagsSuggestL10n,"5.5.0"),W.wpColorPickerL10n=W.wpColorPickerL10n||{clear:"",clearAriaLabel:"",defaultString:"",defaultAriaLabel:"",pick:"",defaultLabel:""},W.wpColorPickerL10n=e("wpColorPickerL10n",W.wpColorPickerL10n,"5.5.0"),W.attachMediaBoxL10n=W.attachMediaBoxL10n||{error:""},W.attachMediaBoxL10n=e("attachMediaBoxL10n",W.attachMediaBoxL10n,"5.5.0"),W.postL10n=W.postL10n||{ok:"",cancel:"",publishOn:"",publishOnFuture:"",publishOnPast:"",dateFormat:"",showcomm:"",endcomm:"",publish:"",schedule:"",update:"",savePending:"",saveDraft:"",private:"",public:"",publicSticky:"",password:"",privatelyPublished:"",published:"",saveAlert:"",savingText:"",permalinkSaved:""},W.postL10n=e("postL10n",W.postL10n,"5.5.0"),W.inlineEditL10n=W.inlineEditL10n||{error:"",ntdeltitle:"",notitle:"",comma:"",saved:""},W.inlineEditL10n=e("inlineEditL10n",W.inlineEditL10n,"5.5.0"),W.plugininstallL10n=W.plugininstallL10n||{plugin_information:"",plugin_modal_label:"",ays:""},W.plugininstallL10n=e("plugininstallL10n",W.plugininstallL10n,"5.5.0"),W.navMenuL10n=W.navMenuL10n||{noResultsFound:"",warnDeleteMenu:"",saveAlert:"",untitled:""},W.navMenuL10n=e("navMenuL10n",W.navMenuL10n,"5.5.0"),W.commentL10n=W.commentL10n||{submittedOn:"",dateFormat:""},W.commentL10n=e("commentL10n",W.commentL10n,"5.5.0"),W.setPostThumbnailL10n=W.setPostThumbnailL10n||{setThumbnail:"",saving:"",error:"",done:""},W.setPostThumbnailL10n=e("setPostThumbnailL10n",W.setPostThumbnailL10n,"5.5.0"),W.uiAutocompleteL10n=W.uiAutocompleteL10n||{noResults:"",oneResult:"",manyResults:"",itemSelected:""},W.uiAutocompleteL10n=e("uiAutocompleteL10n",W.uiAutocompleteL10n,"6.5.0"),W.adminMenu={init:function(){},fold:function(){},restoreMenuState:function(){},toggle:function(){},favorites:function(){}},W.columns={init:function(){var n=this;B(".hide-column-tog","#adv-settings").on("click",function(){var e=B(this),t=e.val();e.prop("checked")?n.checked(t):n.unchecked(t),columns.saveManageColumnsState()})},saveManageColumnsState:function(){var e=this.hidden();B.post(ajaxurl,{action:"hidden-columns",hidden:e,screenoptionnonce:B("#screenoptionnonce").val(),page:pagenow},function(){wp.a11y.speak(Q("Screen Options updated."))})},checked:function(e){B(".column-"+e).removeClass("hidden"),this.colSpanChange(1)},unchecked:function(e){B(".column-"+e).addClass("hidden"),this.colSpanChange(-1)},hidden:function(){return B(".manage-column[id]").filter(".hidden").map(function(){return this.id}).get().join(",")},useCheckboxesForHidden:function(){this.hidden=function(){return B(".hide-column-tog").not(":checked").map(function(){var e=this.id;return e.substring(e,e.length-5)}).get().join(",")}},colSpanChange:function(e){var t=B("table").find(".colspanchange");t.length&&(e=parseInt(t.attr("colspan"),10)+e,t.attr("colspan",e.toString()))}},B(function(){columns.init()}),W.validateForm=function(e){return!B(e).find(".form-required").filter(function(){return""===B(":input:visible",this).val()}).addClass("form-invalid").find(":input:visible").on("change",function(){B(this).closest(".form-invalid").removeClass("form-invalid")}).length},W.showNotice={warn:function(){return!!confirm(Q("You are about to permanently delete these items from your site.\nThis action cannot be undone.\n'Cancel' to stop, 'OK' to delete."))},note:function(e){alert(e)}},W.screenMeta={element:null,toggles:null,page:null,init:function(){this.element=B("#screen-meta"),this.toggles=B("#screen-meta-links").find(".show-settings"),this.page=B("#wpcontent"),this.toggles.on("click",this.toggleEvent)},toggleEvent:function(){var e=B("#"+B(this).attr("aria-controls"));e.length&&(e.is(":visible")?screenMeta.close(e,B(this)):screenMeta.open(e,B(this)))},open:function(e,t){B("#screen-meta-links").find(".screen-meta-toggle").not(t.parent()).css("visibility","hidden"),e.parent().show(),e.slideDown("fast",function(){e.removeClass("hidden").trigger("focus"),t.addClass("screen-meta-active").attr("aria-expanded",!0)}),$.trigger("screen:options:open")},close:function(e,t){e.slideUp("fast",function(){t.removeClass("screen-meta-active").attr("aria-expanded",!1),B(".screen-meta-toggle").css("visibility",""),e.parent().hide(),e.addClass("hidden")}),$.trigger("screen:options:close")}},B(".contextual-help-tabs").on("click","a",function(e){var t=B(this);if(e.preventDefault(),t.is(".active a"))return!1;B(".contextual-help-tabs .active").removeClass("active"),t.parent("li").addClass("active"),e=B(t.attr("href")),B(".help-tab-content").not(e).removeClass("active").hide(),e.addClass("active").show()});var t,a=!1,s=B("#permalink_structure"),n=B(".permalink-structure input:radio"),l=B("#custom_selection"),o=B(".form-table.permalink-structure .available-structure-tags button");function c(e){-1!==s.val().indexOf(e.text().trim())?(e.attr("data-label",e.attr("aria-label")),e.attr("aria-label",e.attr("data-used")),e.attr("aria-pressed",!0),e.addClass("active")):e.attr("data-label")&&(e.attr("aria-label",e.attr("data-label")),e.attr("aria-pressed",!1),e.removeClass("active"))}function d(){$.trigger("wp-window-resized")}n.on("change",function(){"custom"!==this.value&&(s.val(this.value),o.each(function(){c(B(this))}))}),s.on("click input",function(){l.prop("checked",!0)}),s.on("focus",function(e){a=!0,B(this).off(e)}),o.each(function(){c(B(this))}),s.on("change",function(){o.each(function(){c(B(this))})}),o.on("click",function(){var e=s.val(),t=s[0].selectionStart,n=s[0].selectionEnd,i=B(this).text().trim(),o=B(this).hasClass("active")?B(this).attr("data-removed"):B(this).attr("data-added");-1!==e.indexOf(i)?(e=e.replace(i+"/",""),s.val("/"===e?"":e),B("#custom_selection_updated").text(o),c(B(this))):(a||0!==t||0!==n||(t=n=e.length),l.prop("checked",!0),"/"!==e.substr(0,t).substr(-1)&&(i="/"+i),"/"!==e.substr(n,1)&&(i+="/"),s.val(e.substr(0,t)+i+e.substr(n)),B("#custom_selection_updated").text(o),c(B(this)),a&&s[0].setSelectionRange&&(n=(e.substr(0,t)+i).length,s[0].setSelectionRange(n,n),s.trigger("focus")))}),B(function(){var n,i,o,a,e,t,s,r=!1,l=B("input.current-page"),z=l.val(),c=/iPhone|iPad|iPod/.test(navigator.userAgent),R=-1!==navigator.userAgent.indexOf("Android"),d=B("#adminmenuwrap"),u=B("#wpwrap"),p=B("#adminmenu"),m=B("#wp-responsive-overlay"),h=B("#wp-toolbar"),f=h.find('a[aria-haspopup="true"]'),g=B(".meta-box-sortables"),v=!1,b=B("#wpadminbar"),w=0,k=!1,y=!1,C=0,L=!1,x={window:H.height(),wpwrap:u.height(),adminbar:b.height(),menu:d.height()},S=B(".wp-header-end");function A(e){var t=e.find(".wp-submenu"),e=e.offset().top,n=H.scrollTop(),i=e-n-30,e=e+t.height()+1,o=60+e-u.height(),n=H.height()+n-50;1<(o=i<(o=n<e-o?e-n:o)?i:o)&&B("#wp-admin-bar-menu-toggle").is(":hidden")?t.css("margin-top","-"+o+"px"):t.css("margin-top","")}function P(){B(".notice.is-dismissible").each(function(){var t=B(this),e=B('<button type="button" class="notice-dismiss"><span class="screen-reader-text"></span></button>');t.find(".notice-dismiss").length||(e.find(".screen-reader-text").text(Q("Dismiss this notice.")),e.on("click.wp-dismiss-notice",function(e){e.preventDefault(),t.fadeTo(100,0,function(){t.slideUp(100,function(){t.remove()})})}),t.append(e))})}function T(e,t,n,i){n.on("change",function(){e.val(B(this).val())}),e.on("change",function(){n.val(B(this).val())}),i.on("click",function(e){e.preventDefault(),e.stopPropagation(),t.trigger("click")})}p.on("click.wp-submenu-head",".wp-submenu-head",function(e){B(e.target).parent().siblings("a").get(0).click()}),B("#collapse-button").on("click.collapse-menu",function(){var e=I()||961;B("#adminmenu div.wp-submenu").css("margin-top",""),s=e<=960?q.hasClass("auto-fold")?(q.removeClass("auto-fold").removeClass("folded"),setUserSetting("unfold",1),setUserSetting("mfold","o"),"open"):(q.addClass("auto-fold"),setUserSetting("unfold",0),"folded"):q.hasClass("folded")?(q.removeClass("folded"),setUserSetting("mfold","o"),"open"):(q.addClass("folded"),setUserSetting("mfold","f"),"folded"),$.trigger("wp-collapse-menu",{state:s})}),("ontouchstart"in W||/IEMobile\/[1-9]/.test(navigator.userAgent))&&(q.on((E=c?"touchstart":"click")+".wp-mobile-hover",function(e){p.data("wp-responsive")||B(e.target).closest("#adminmenu").length||p.find("li.opensub").removeClass("opensub")}),p.find("a.wp-has-submenu").on(E+".wp-mobile-hover",function(e){var t=B(this).parent();p.data("wp-responsive")||t.hasClass("opensub")||t.hasClass("wp-menu-open")&&!(t.width()<40)||(e.preventDefault(),A(t),p.find("li.opensub").removeClass("opensub"),t.addClass("opensub"))})),c||R||(p.find("li.wp-has-submenu").hoverIntent({over:function(){var e=B(this),t=e.find(".wp-submenu"),t=parseInt(t.css("top"),10);isNaN(t)||-5<t||p.data("wp-responsive")||(A(e),p.find("li.opensub").removeClass("opensub"),e.addClass("opensub"))},out:function(){p.data("wp-responsive")||B(this).removeClass("opensub").find(".wp-submenu").css("margin-top","")},timeout:200,sensitivity:7,interval:90}),p.on("focus.adminmenu",".wp-submenu a",function(e){p.data("wp-responsive")||B(e.target).closest("li.menu-top").addClass("opensub")}).on("blur.adminmenu",".wp-submenu a",function(e){p.data("wp-responsive")||B(e.target).closest("li.menu-top").removeClass("opensub")}).find("li.wp-has-submenu.wp-not-current-submenu").on("focusin.adminmenu",function(){A(B(this))})),S.length||(S=B(".wrap h1, .wrap h2").first()),B("div.updated, div.error, div.notice").not(".inline, .below-h2").insertAfter(S),$.on("wp-updates-notice-added wp-plugin-install-error wp-plugin-update-error wp-plugin-delete-error wp-theme-install-error wp-theme-delete-error wp-notice-added",P),screenMeta.init(),q.on("click","tbody > tr > .check-column :checkbox",function(e){if("undefined"!=e.shiftKey){if(e.shiftKey){if(!r)return!0;n=B(r).closest("form").find(":checkbox").filter(":visible:enabled"),i=n.index(r),o=n.index(this),a=B(this).prop("checked"),0<i&&0<o&&i!=o&&(i<o?n.slice(i,o):n.slice(o,i)).prop("checked",function(){return!!B(this).closest("tr").is(":visible")&&a})}var t=B(r=this).closest("tbody").find("tr").find(":checkbox").filter(":visible:enabled").not(":checked");B(this).closest("table").children("thead, tfoot").find(":checkbox").prop("checked",function(){return 0===t.length})}return!0}),q.on("click.wp-toggle-checkboxes","thead .check-column :checkbox, tfoot .check-column :checkbox",function(e){var t=B(this),n=t.closest("table"),i=t.prop("checked"),o=e.shiftKey||t.data("wp-toggle");n.children("tbody").filter(":visible").children().children(".check-column").find(":checkbox").prop("checked",function(){return!B(this).is(":hidden,:disabled")&&(o?!B(this).prop("checked"):!!i)}),n.children("thead,  tfoot").filter(":visible").children().children(".check-column").find(":checkbox").prop("checked",function(){return!o&&!!i})}),T(B("#bulk-action-selector-top"),B("#doaction"),B("#bulk-action-selector-bottom"),B("#doaction2")),T(B("#new_role"),B("#changeit"),B("#new_role2"),B("#changeit2"));var M,_,E;function D(){M.prop("disabled",""===_.map(function(){return B(this).val()}).get().join(""))}function N(e){var t=H.scrollTop(),e=!e||"scroll"!==e.type;if(!c&&!p.data("wp-responsive"))if(x.menu+x.adminbar<x.window||x.menu+x.adminbar+20>x.wpwrap)O();else{if(L=!0,x.menu+x.adminbar>x.window){if(t<0)return void(k||(y=!(k=!0),d.css({position:"fixed",top:"",bottom:""})));if(t+x.window>$.height()-1)return void(y||(k=!(y=!0),d.css({position:"fixed",top:"",bottom:0})));w<t?k?(k=!1,(C=d.offset().top-x.adminbar-(t-w))+x.menu+x.adminbar<t+x.window&&(C=t+x.window-x.menu-x.adminbar),d.css({position:"absolute",top:C,bottom:""})):!y&&d.offset().top+x.menu<t+x.window&&(y=!0,d.css({position:"fixed",top:"",bottom:0})):t<w?y?(y=!1,(C=d.offset().top-x.adminbar+(w-t))+x.menu>t+x.window&&(C=t),d.css({position:"absolute",top:C,bottom:""})):!k&&d.offset().top>=t+x.adminbar&&(k=!0,d.css({position:"fixed",top:"",bottom:""})):e&&(k=y=!1,0<(C=t+x.window-x.menu-x.adminbar-1)?d.css({position:"absolute",top:C,bottom:""}):O())}w=t}}function F(){x={window:H.height(),wpwrap:u.height(),adminbar:b.height(),menu:d.height()}}function O(){!c&&L&&(k=y=L=!1,d.css({position:"",top:"",bottom:""}))}function j(){F(),p.data("wp-responsive")?(q.removeClass("sticky-menu"),O()):x.menu+x.adminbar>x.window?(N(),q.removeClass("sticky-menu")):(q.addClass("sticky-menu"),O())}function U(){B(".aria-button-if-js").attr("role","button")}function I(){var e=!1;return e=W.innerWidth?Math.max(W.innerWidth,document.documentElement.clientWidth):e}function K(){var e=I()||961;s=e<=782?"responsive":q.hasClass("folded")||q.hasClass("auto-fold")&&e<=960&&782<e?"folded":"open",$.trigger("wp-menu-state-set",{state:s})}B(".bulkactions").parents("form").on("submit",function(e){var t=!(!e.originalEvent||!e.originalEvent.submitter)&&e.originalEvent.submitter.name,n=this.querySelector("#current-page-selector");if(!n||n.defaultValue===n.value){n={bulk_action:W.bulkActionObserverIds.bulk_action,changeit:W.bulkActionObserverIds.changeit};if(Object.keys(n).includes(t)){n=new FormData(this).get(n[t])||"-1";if("-1"!==n)if(0<this.querySelectorAll('.wp-list-table tbody .check-column input[type="checkbox"]:checked').length)return;e.preventDefault(),e.stopPropagation(),B("html, body").animate({scrollTop:0});var i,o,t=Q("Please select at least one item to perform this action on.");e=B((n={id:"no-items-selected",type:"error",message:t,dismissible:!0}).selector),o=B(".wp-header-end"),delete n.selector,i=n.dismissible&&!0===n.dismissible?" is-dismissible":"",i='<div id="'+n.id+'" class="notice notice-'+n.type+i+'"><p>'+n.message+"</p></div>",(e=e.length?e:B("#"+n.id)).length?e.replaceWith(i):o.length?o.after(i):"customize"===pagenow?B(".customize-themes-notifications").append(i):B(".wrap").find("> h1").after(i),$.trigger("wp-notice-added"),wp.a11y.speak(t)}}}),B("#wpbody-content").on({focusin:function(){clearTimeout(e),t=B(this).find(".row-actions"),B(".row-actions").not(this).removeClass("visible"),t.addClass("visible")},focusout:function(){e=setTimeout(function(){t.removeClass("visible")},30)}},".table-view-list .has-row-actions"),B("tbody").on("click",".toggle-row",function(){B(this).closest("tr").toggleClass("is-expanded")}),B("#default-password-nag-no").on("click",function(){return setUserSetting("default_password_nag","hide"),B("div.default-password-nag").hide(),!1}),B("#newcontent").on("keydown.wpevent_InsertTab",function(e){var t,n,i,o,a=e.target;27==e.keyCode?(e.preventDefault(),B(a).data("tab-out",!0)):9!=e.keyCode||e.ctrlKey||e.altKey||e.shiftKey||(B(a).data("tab-out")?B(a).data("tab-out",!1):(t=a.selectionStart,n=a.selectionEnd,i=a.value,document.selection?(a.focus(),document.selection.createRange().text="\t"):0<=t&&(o=this.scrollTop,a.value=i.substring(0,t).concat("\t",i.substring(n)),a.selectionStart=a.selectionEnd=t+1,this.scrollTop=o),e.stopPropagation&&e.stopPropagation(),e.preventDefault&&e.preventDefault()))}),l.length&&l.closest("form").on("submit",function(){-1==B('select[name="action"]').val()&&l.val()==z&&l.val("1")}),B('.search-box input[type="search"], .search-box input[type="submit"]').on("mousedown",function(){B('select[name^="action"]').val("-1")}),B("#contextual-help-link, #show-settings-link").on("focus.scroll-into-view",function(e){e.target.scrollIntoViewIfNeeded&&e.target.scrollIntoViewIfNeeded(!1)}),(E=B("form.wp-upload-form")).length&&(M=E.find('input[type="submit"]'),_=E.find('input[type="file"]'),D(),_.on("change",D)),c||(H.on("scroll.pin-menu",N),$.on("tinymce-editor-init.pin-menu",function(e,t){t.on("wp-autoresize",F)})),W.wpResponsive={init:function(){var e=this;this.maybeDisableSortables=this.maybeDisableSortables.bind(this),$.on("wp-responsive-activate.wp-responsive",function(){e.activate(),e.toggleAriaHasPopup("add")}).on("wp-responsive-deactivate.wp-responsive",function(){e.deactivate(),e.toggleAriaHasPopup("remove")}),B("#wp-admin-bar-menu-toggle a").attr("aria-expanded","false"),B("#wp-admin-bar-menu-toggle").on("click.wp-responsive",function(e){e.preventDefault(),b.find(".hover").removeClass("hover"),u.toggleClass("wp-responsive-open"),u.hasClass("wp-responsive-open")?(B(this).find("a").attr("aria-expanded","true"),B("#adminmenu a:first").trigger("focus")):B(this).find("a").attr("aria-expanded","false")}),B(document).on("click",function(e){var t;u.hasClass("wp-responsive-open")&&document.hasFocus()&&(t=B.contains(B("#wp-admin-bar-menu-toggle")[0],e.target),e=B.contains(B("#adminmenuwrap")[0],e.target),t||e||B("#wp-admin-bar-menu-toggle").trigger("click.wp-responsive"))}),B(document).on("keyup",function(e){var n,i,o=B("#wp-admin-bar-menu-toggle")[0];u.hasClass("wp-responsive-open")&&(27===e.keyCode?(B(o).trigger("click.wp-responsive"),B(o).find("a").trigger("focus")):9===e.keyCode&&(n=B("#adminmenuwrap")[0],i=e.relatedTarget||document.activeElement,setTimeout(function(){var e=B.contains(o,i),t=B.contains(n,i);e||t||B(o).trigger("click.wp-responsive")},10)))}),p.on("click.wp-responsive","li.wp-has-submenu > a",function(e){var t;p.data("wp-responsive")&&(t="false"===B(this).attr("aria-expanded")?"true":"false",B(this).parent("li").toggleClass("selected"),B(this).attr("aria-expanded",t),B(this).trigger("focus"),e.preventDefault())}),e.trigger(),$.on("wp-window-resized.wp-responsive",this.trigger.bind(this)),H.on("load.wp-responsive",this.maybeDisableSortables),$.on("postbox-toggled",this.maybeDisableSortables),B("#screen-options-wrap input").on("click",this.maybeDisableSortables)},maybeDisableSortables:function(){(-1<navigator.userAgent.indexOf("AppleWebKit/")?H.width():W.innerWidth)<=782||g.find(".ui-sortable-handle:visible").length<=1&&jQuery(".columns-prefs-1 input").prop("checked")?this.disableSortables():this.enableSortables()},activate:function(){j(),q.hasClass("auto-fold")||q.addClass("auto-fold"),p.data("wp-responsive",1),this.disableSortables()},deactivate:function(){j(),p.removeData("wp-responsive"),this.maybeDisableSortables()},toggleAriaHasPopup:function(e){var t=p.find("[data-ariahaspopup]");"add"===e?t.each(function(){B(this).attr("aria-haspopup","menu").attr("aria-expanded","false")}):t.each(function(){B(this).removeAttr("aria-haspopup").removeAttr("aria-expanded")})},trigger:function(){var e=I();e&&(e<=782?v||($.trigger("wp-responsive-activate"),v=!0):v&&($.trigger("wp-responsive-deactivate"),v=!1),e<=480?this.enableOverlay():this.disableOverlay(),this.maybeDisableSortables())},enableOverlay:function(){0===m.length&&(m=B('<div id="wp-responsive-overlay"></div>').insertAfter("#wpcontent").hide().on("click.wp-responsive",function(){h.find(".menupop.hover").removeClass("hover"),B(this).hide()})),f.on("click.wp-responsive",function(){m.show()})},disableOverlay:function(){f.off("click.wp-responsive"),m.hide()},disableSortables:function(){if(g.length)try{g.sortable("disable"),g.find(".ui-sortable-handle").addClass("is-non-sortable")}catch(e){}},enableSortables:function(){if(g.length)try{g.sortable("enable"),g.find(".ui-sortable-handle").removeClass("is-non-sortable")}catch(e){}}},B(document).on("ajaxComplete",function(){U()}),$.on("wp-window-resized.set-menu-state",K),$.on("wp-menu-state-set wp-collapse-menu",function(e,t){var n,i=B("#collapse-button"),t="folded"===t.state?(n="false",Q("Expand Main menu")):(n="true",Q("Collapse Main menu"));i.attr({"aria-expanded":n,"aria-label":t})}),W.wpResponsive.init(),j(),K(),P(),U(),$.on("wp-pin-menu wp-window-resized.pin-menu postboxes-columnchange.pin-menu postbox-toggled.pin-menu wp-collapse-menu.pin-menu wp-scroll-start.pin-menu",j),B(".wp-initial-focus").trigger("focus"),q.on("click",".js-update-details-toggle",function(){var e=B(this).closest(".js-update-details"),t=B("#"+e.data("update-details"));t.hasClass("update-details-moved")||t.insertAfter(e).addClass("update-details-moved"),t.toggle(),B(this).attr("aria-expanded",t.is(":visible"))})}),B(function(e){var t,n;q.hasClass("update-php")&&(t=e("a.update-from-upload-overwrite"),n=e(".update-from-upload-expired"),t.length)&&n.length&&W.setTimeout(function(){t.hide(),n.removeClass("hidden"),W.wp&&W.wp.a11y&&W.wp.a11y.speak(n.text())},714e4)}),H.on("resize.wp-fire-once",function(){W.clearTimeout(t),t=W.setTimeout(d,200)}),"-ms-user-select"in document.documentElement.style&&navigator.userAgent.match(/IEMobile\/10\.0/)&&((n=document.createElement("style")).appendChild(document.createTextNode("@-ms-viewport{width:auto!important}")),document.getElementsByTagName("head")[0].appendChild(n))}(jQuery,window),function(){var e,i={},o={};i.pauseAll=!1,!window.matchMedia||(e=window.matchMedia("(prefers-reduced-motion: reduce)"))&&!e.matches||(i.pauseAll=!0),i.freezeAnimatedPluginIcons=function(l){function e(){var e=l.width,t=l.height,n=document.createElement("canvas");if(n.width=e,n.height=t,n.className=l.className,l.closest("#update-plugins-table"))for(var i=window.getComputedStyle(l),o=0,a=i.length;o<a;o++){var s=i[o],r=i.getPropertyValue(s);n.style[s]=r}n.getContext("2d").drawImage(l,0,0,e,t),n.setAttribute("aria-hidden","true"),n.setAttribute("role","presentation"),l.parentNode.insertBefore(n,l),l.style.opacity=.01,l.style.width="0px",l.style.height="0px"}l.complete?e():l.addEventListener("load",e,!0)},o.freezeAll=function(){for(var e=document.querySelectorAll(".plugin-icon, #update-plugins-table img"),t=0;t<e.length;t++)/\.gif(?:\?|$)/i.test(e[t].src)&&i.freezeAnimatedPluginIcons(e[t])},!0===i.pauseAll&&o.freezeAll(),e=jQuery,"plugin-install"===window.pagenow&&e(document).ajaxComplete(function(e,t,n){n.data&&"string"==typeof n.data&&n.data.includes("action=search-install-plugins")&&(window.matchMedia?window.matchMedia("(prefers-reduced-motion: reduce)").matches&&o.freezeAll():!0===i.pauseAll&&o.freezeAll())})}();