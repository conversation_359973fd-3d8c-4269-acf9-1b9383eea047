// SoloYLibre Ultimate Plugin JavaScript
jQuery(document).ready(function($) {

    // Animación de la bandera dominicana
    $('.dominican-flag').hover(
        function() {
            $(this).css('animation-duration', '0.5s');
        },
        function() {
            $(this).css('animation-duration', '2s');
        }
    );

    // Mensaje de bienvenida
    if (typeof pagenow !== 'undefined' && pagenow === 'dashboard') {
        setTimeout(function() {
            if ($('#soloylibre_ultimate_widget').length) {
                $('#soloylibre_ultimate_widget').effect('highlight', {color: '#22c55e'}, 2000);
            }
        }, 1000);
    }

    // Contador de visitas (demo)
    var visitCount = localStorage.getItem('soloylibre_visits') || 0;
    visitCount++;
    localStorage.setItem('soloylibre_visits', visitCount);

    // Mostrar estadísticas si existe el elemento
    if ($('.soloylibre-visit-count').length) {
        $('.soloylibre-visit-count').text(visitCount);
    }

    // Efecto de typing para el título
    function typeWriter(element, text, speed) {
        var i = 0;
        element.innerHTML = '';

        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }

        type();
    }

    // Aplicar efecto typing al título principal
    var mainTitle = $('.soloylibre-typing-title')[0];
    if (mainTitle) {
        var originalText = mainTitle.innerHTML;
        typeWriter(mainTitle, originalText, 100);
    }
});

// Función para mostrar información del desarrollador
function mostrarInfoDesarrollador() {
    alert('🇩🇴 Jose L Encarnacion (JoseTusabe)\n' +
          '📧 <EMAIL>\n' +
          '📞 ************\n' +
          '🏔️ San José de Ocoa, República Dominicana\n' +
          '🖥️ Synology RS3618xs - 56GB RAM - 36TB');
}

// Función para copiar información de contacto
function copiarContacto() {
    var contacto = 'Jose L Encarnacion (JoseTusabe)\n' +
                   'Email: <EMAIL>\n' +
                   'Teléfono: ************\n' +
                   'Ubicación: San José de Ocoa, República Dominicana';

    navigator.clipboard.writeText(contacto).then(function() {
        alert('📋 Información de contacto copiada al portapapeles');
    });
}
