<?php
/**
 * Optimizaciones de Rendimiento SoloYLibre
 * Desarrollado por <PERSON> (JoseTusabe)
 */

class SoloYLibre_Performance {

    public function __construct() {
        add_action('init', array($this, 'init_optimizations'));
        add_action('wp_enqueue_scripts', array($this, 'optimize_scripts'));
        add_filter('script_loader_tag', array($this, 'add_async_defer'), 10, 2);
    }

    public function init_optimizations() {
        // Habilitar compresión GZIP
        if (!ob_get_level()) {
            ob_start('ob_gzhandler');
        }

        // Optimizar consultas de base de datos
        add_action('pre_get_posts', array($this, 'optimize_queries'));

        // Limpiar head de WordPress
        $this->clean_wp_head();

        // Habilitar cache de objetos
        if (!wp_using_ext_object_cache()) {
            wp_cache_init();
        }
    }

    public function clean_wp_head() {
        // Remover enlaces innecesarios del head
        remove_action('wp_head', 'rsd_link');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'wp_shortlink_wp_head');
        remove_action('wp_head', 'adjacent_posts_rel_link_wp_head');

        // Remover emoji scripts
        remove_action('wp_head', 'print_emoji_detection_script', 7);
        remove_action('wp_print_styles', 'print_emoji_styles');
    }

    public function optimize_scripts() {
        // Mover jQuery al footer
        if (!is_admin()) {
            wp_deregister_script('jquery');
            wp_register_script('jquery', includes_url('/js/jquery/jquery.min.js'), false, null, true);
            wp_enqueue_script('jquery');
        }
    }

    public function add_async_defer($tag, $handle) {
        // Scripts que deben cargarse de forma asíncrona
        $async_scripts = array('soloylibre-ultimate-script');

        if (in_array($handle, $async_scripts)) {
            return str_replace('<script ', '<script async defer ', $tag);
        }

        return $tag;
    }

    public function optimize_queries($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Limitar posts en home
            if ($query->is_home()) {
                $query->set('posts_per_page', 10);
            }

            // Optimizar búsquedas
            if ($query->is_search()) {
                $query->set('posts_per_page', 5);
            }
        }
    }

    public function get_performance_stats() {
        $stats = array(
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => ini_get('memory_limit'),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'queries_count' => get_num_queries(),
            'cache_enabled' => wp_using_ext_object_cache(),
            'gzip_enabled' => extension_loaded('zlib'),
            'optimized_by' => 'Jose L Encarnacion (JoseTusabe) - San José de Ocoa 🇩🇴'
        );

        return $stats;
    }
}

new SoloYLibre_Performance();
?>
