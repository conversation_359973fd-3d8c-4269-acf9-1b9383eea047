<?php
/**
 * Sistema de Estadísticas SoloYLibre
 * Desarrollado por <PERSON> (JoseTusabe)
 */

class SoloYLibre_Stats {

    public function __construct() {
        add_action('wp_footer', array($this, 'track_visit'));
        add_action('wp_ajax_get_stats', array($this, 'get_stats'));
        add_action('wp_ajax_nopriv_get_stats', array($this, 'get_stats'));
    }

    public function track_visit() {
        if (!is_admin()) {
            $visits = get_option('soloylibre_visits', 0);
            $visits++;
            update_option('soloylibre_visits', $visits);

            // Registrar visita por día
            $today = date('Y-m-d');
            $daily_visits = get_option('soloylibre_daily_visits', array());

            if (!isset($daily_visits[$today])) {
                $daily_visits[$today] = 0;
            }

            $daily_visits[$today]++;
            update_option('soloylibre_daily_visits', $daily_visits);
        }
    }

    public function get_stats() {
        $stats = array(
            'total_visits' => get_option('soloylibre_visits', 0),
            'posts_count' => wp_count_posts()->publish,
            'pages_count' => wp_count_posts('page')->publish,
            'comments_count' => wp_count_comments()->approved,
            'users_count' => count_users()['total_users'],
            'plugins_count' => count(get_option('active_plugins', array())),
            'theme_name' => wp_get_theme()->get('Name'),
            'wp_version' => get_bloginfo('version'),
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'developer_info' => array(
                'name' => 'Jose L Encarnacion (JoseTusabe)',
                'email' => '<EMAIL>',
                'phone' => '************',
                'location' => 'San José de Ocoa, República Dominicana 🇩🇴',
                'server' => 'Synology RS3618xs - 56GB RAM - 36TB'
            )
        );

        wp_send_json_success($stats);
    }

    public function display_stats_widget() {
        $stats = array(
            'Visitas Totales' => get_option('soloylibre_visits', 0),
            'Posts Publicados' => wp_count_posts()->publish,
            'Páginas Creadas' => wp_count_posts('page')->publish,
            'Comentarios' => wp_count_comments()->approved,
            'Usuarios' => count_users()['total_users'],
            'Plugins Activos' => count(get_option('active_plugins', array()))
        );

        echo '<div class="soloylibre-stats">';
        foreach ($stats as $label => $value) {
            echo '<div class="soloylibre-stat-item">';
            echo '<div class="soloylibre-stat-number">' . $value . '</div>';
            echo '<div class="soloylibre-stat-label">' . $label . '</div>';
            echo '</div>';
        }
        echo '</div>';
    }
}

new SoloYLibre_Stats();
?>
