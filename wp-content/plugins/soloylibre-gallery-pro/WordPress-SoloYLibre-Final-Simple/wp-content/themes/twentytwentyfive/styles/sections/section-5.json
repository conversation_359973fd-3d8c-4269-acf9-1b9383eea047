{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "slug": "section-5", "title": "Style 5", "blockTypes": ["core/group", "core/columns", "core/column"], "styles": {"color": {"background": "var:preset|color|contrast", "text": "var:preset|color|base"}, "blocks": {"core/separator": {"color": {"text": "color-mix(in srgb, currentColor 25%, transparent)"}}, "core/post-author-name": {"color": {"text": "currentColor"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/post-date": {"color": {"text": "color-mix(in srgb, currentColor 85%, transparent)"}, "elements": {"link": {"color": {"text": "color-mix(in srgb, currentColor 85%, transparent)"}}}}, "core/post-terms": {"color": {"text": "currentColor"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/comment-author-name": {"color": {"text": "currentColor"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/comment-date": {"color": {"text": "currentColor"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/comment-edit-link": {"color": {"text": "currentColor"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/comment-reply-link": {"color": {"text": "currentColor"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/pullquote": {"color": {"text": "currentColor"}}, "core/quote": {"color": {"text": "currentColor"}}}, "elements": {"button": {"color": {"background": "var:preset|color|base", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--base) 80%, transparent)", "text": "var:preset|color|contrast"}}}}}}