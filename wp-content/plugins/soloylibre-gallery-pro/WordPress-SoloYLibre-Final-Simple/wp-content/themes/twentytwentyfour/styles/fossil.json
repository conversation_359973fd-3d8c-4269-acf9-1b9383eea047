{"$schema": "https://schemas.wp.org/wp/6.5/theme.json", "version": 2, "title": "Fossil", "settings": {"color": {"gradients": [{"slug": "gradient-1", "gradient": "linear-gradient(to bottom, #E1DFDB 0%, #D6D2CE 100%)", "name": "Vertical linen to beige"}, {"slug": "gradient-2", "gradient": "linear-gradient(to bottom, #958D86 0%, #D6D2CE 100%)", "name": "Vertical taupe to beige"}, {"slug": "gradient-3", "gradient": "linear-gradient(to bottom, #65574E 0%, #D6D2CE 100%)", "name": "Vertical sable to beige"}, {"slug": "gradient-4", "gradient": "linear-gradient(to bottom, #1A1514 0%, #D6D2CE 100%)", "name": "Vertical ebony to beige"}, {"slug": "gradient-5", "gradient": "linear-gradient(to bottom, #65574E 0%, #958D86 100%)", "name": "Vertical sable to beige"}, {"slug": "gradient-6", "gradient": "linear-gradient(to bottom, #1A1514 0%, #65574E 100%)", "name": "Vertical ebony to sable"}, {"slug": "gradient-7", "gradient": "linear-gradient(to bottom, #D6D2CE 50%, #E1DFDB 50%)", "name": "Vertical hard beige to linen"}, {"slug": "gradient-8", "gradient": "linear-gradient(to bottom, #958D86 50%, #D6D2CE 50%)", "name": "Vertical hard taupe to beige"}, {"slug": "gradient-9", "gradient": "linear-gradient(to bottom, #65574E 50%, #D6D2CE 50%)", "name": "Vertical hard sable to beige"}, {"slug": "gradient-10", "gradient": "linear-gradient(to bottom, #1A1514 50%, #D6D2CE 50%)", "name": "Vertical hard ebony to beige"}, {"slug": "gradient-11", "gradient": "linear-gradient(to bottom, #65574E 50%, #958D86 50%)", "name": "Vertical hard sable to taupe"}, {"slug": "gradient-12", "gradient": "linear-gradient(to bottom, #1A1514 50%, #65574E 50%)", "name": "Vertical hard ebony to sable"}], "palette": [{"color": "#D6D2CE", "name": "Base", "slug": "base"}, {"color": "#E1DFDB", "name": "Base / Two", "slug": "base-2"}, {"color": "#1A1514", "name": "Contrast", "slug": "contrast"}, {"color": "#65574E", "name": "Contrast / Two", "slug": "contrast-2"}, {"color": "#958D86", "name": "Contrast / Three", "slug": "contrast-3"}]}, "typography": {"fontFamilies": [{"fontFace": [{"fontFamily": "Inter", "fontStretch": "normal", "fontStyle": "normal", "fontWeight": "300 900", "src": ["file:./assets/fonts/inter/Inter-VariableFont_slnt,wght.woff2"]}], "fontFamily": "\"Inter\", sans-serif", "name": "Inter", "slug": "heading"}, {"fontFace": [{"fontFamily": "<PERSON><PERSON>", "fontStyle": "normal", "fontWeight": "400", "src": ["file:./assets/fonts/cardo/cardo_normal_400.woff2"]}, {"fontFamily": "<PERSON><PERSON>", "fontStyle": "italic", "fontWeight": "400", "src": ["file:./assets/fonts/cardo/cardo_italic_400.woff2"]}, {"fontFamily": "<PERSON><PERSON>", "fontStyle": "normal", "fontWeight": "700", "src": ["file:./assets/fonts/cardo/cardo_normal_700.woff2"]}], "fontFamily": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "slug": "body"}, {"fontFamily": "-apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif", "name": "System Sans-serif", "slug": "system-sans-serif"}, {"fontFamily": "Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol", "name": "System Serif", "slug": "system-serif"}], "fontSizes": [{"fluid": false, "name": "Small", "size": "1rem", "slug": "small"}, {"fluid": false, "name": "Medium", "size": "1.2rem", "slug": "medium"}, {"fluid": {"min": "1.5rem", "max": "2rem"}, "name": "Large", "size": "2rem", "slug": "large"}, {"fluid": {"min": "2rem", "max": "2.65rem"}, "name": "Extra Large", "size": "2.65rem", "slug": "x-large"}, {"fluid": {"min": "2.65rem", "max": "3.5rem"}, "name": "Extra Extra Large", "size": "3.5rem", "slug": "xx-large"}]}}, "styles": {"blocks": {"core/button": {"variations": {"outline": {"spacing": {"padding": {"bottom": "calc(0.9rem - 2px)", "left": "calc(2rem - 2px)", "right": "calc(2rem - 2px)", "top": "calc(0.9rem - 2px)"}}, "border": {"width": "2px"}}}}, "core/pullquote": {"typography": {"fontSize": "var(--wp--preset--font-size--large)", "fontStyle": "normal", "fontWeight": "normal", "lineHeight": "1.2"}}, "core/quote": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "var(--wp--preset--font-size--large)", "fontStyle": "normal"}, "variations": {"plain": {"typography": {"fontStyle": "normal", "fontWeight": "400"}}}}, "core/site-title": {"typography": {"fontWeight": "400"}}}, "elements": {"button": {"border": {"radius": "100px", "color": "var(--wp--preset--color--contrast-2)"}, "color": {"background": "var(--wp--preset--color--contrast-2)", "text": "var(--wp--preset--color--white)"}, "spacing": {"padding": {"bottom": "0.9rem", "left": "2rem", "right": "2rem", "top": "0.9rem"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "var(--wp--preset--font-size--small)", "fontStyle": "normal"}, ":hover": {"color": {"background": "var(--wp--preset--color--contrast)"}}}, "heading": {"typography": {"fontWeight": "normal", "letterSpacing": "0"}}}}}