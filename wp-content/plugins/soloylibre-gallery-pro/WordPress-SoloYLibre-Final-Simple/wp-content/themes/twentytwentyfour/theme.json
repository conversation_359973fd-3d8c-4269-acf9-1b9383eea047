{"$schema": "https://schemas.wp.org/wp/6.5/theme.json", "version": 2, "patterns": ["three-columns-of-services", "clients-section"], "settings": {"appearanceTools": true, "color": {"defaultDuotone": false, "defaultPalette": false, "defaultGradients": false, "duotone": [{"colors": ["#111111", "#ffffff"], "slug": "duotone-1", "name": "Black and white"}, {"colors": ["#111111", "#C2A990"], "slug": "duotone-2", "name": "Black and sandstone"}, {"colors": ["#111111", "#D8613C"], "slug": "duotone-3", "name": "Black and rust"}, {"colors": ["#111111", "#B1C5A4"], "slug": "duotone-4", "name": "Black and sage"}, {"colors": ["#111111", "#B5BDBC"], "slug": "duotone-5", "name": "Black and pastel blue"}], "gradients": [{"slug": "gradient-1", "gradient": "linear-gradient(to bottom, #cfcabe 0%, #F9F9F9 100%)", "name": "Vertical soft beige to white"}, {"slug": "gradient-2", "gradient": "linear-gradient(to bottom, #C2A990 0%, #F9F9F9 100%)", "name": "Vertical soft sandstone to white"}, {"slug": "gradient-3", "gradient": "linear-gradient(to bottom, #D8613C 0%, #F9F9F9 100%)", "name": "Vertical soft rust to white"}, {"slug": "gradient-4", "gradient": "linear-gradient(to bottom, #B1C5A4 0%, #F9F9F9 100%)", "name": "Vertical soft sage to white"}, {"slug": "gradient-5", "gradient": "linear-gradient(to bottom, #B5BDBC 0%, #F9F9F9 100%)", "name": "Vertical soft mint to white"}, {"slug": "gradient-6", "gradient": "linear-gradient(to bottom, #A4A4A4 0%, #F9F9F9 100%)", "name": "Vertical soft pewter to white"}, {"slug": "gradient-7", "gradient": "linear-gradient(to bottom, #cfcabe 50%, #F9F9F9 50%)", "name": "Vertical hard beige to white"}, {"slug": "gradient-8", "gradient": "linear-gradient(to bottom, #C2A990 50%, #F9F9F9 50%)", "name": "Vertical hard sandstone to white"}, {"slug": "gradient-9", "gradient": "linear-gradient(to bottom, #D8613C 50%, #F9F9F9 50%)", "name": "Vertical hard rust to white"}, {"slug": "gradient-10", "gradient": "linear-gradient(to bottom, #B1C5A4 50%, #F9F9F9 50%)", "name": "Vertical hard sage to white"}, {"slug": "gradient-11", "gradient": "linear-gradient(to bottom, #B5BDBC 50%, #F9F9F9 50%)", "name": "Vertical hard mint to white"}, {"slug": "gradient-12", "gradient": "linear-gradient(to bottom, #A4A4A4 50%, #F9F9F9 50%)", "name": "Vertical hard pewter to white"}], "palette": [{"color": "#f9f9f9", "name": "Base", "slug": "base"}, {"color": "#ffffff", "name": "Base / Two", "slug": "base-2"}, {"color": "#111111", "name": "Contrast", "slug": "contrast"}, {"color": "#636363", "name": "Contrast / Two", "slug": "contrast-2"}, {"color": "#A4A4A4", "name": "Contrast / Three", "slug": "contrast-3"}, {"color": "#cfcabe", "name": "Accent", "slug": "accent"}, {"color": "#c2a990", "name": "Accent / Two", "slug": "accent-2"}, {"color": "#d8613c", "name": "Accent / Three", "slug": "accent-3"}, {"color": "#b1c5a4", "name": "Accent / Four", "slug": "accent-4"}, {"color": "#b5bdbc", "name": "Accent / Five", "slug": "accent-5"}]}, "layout": {"contentSize": "620px", "wideSize": "1280px"}, "spacing": {"spacingScale": {"steps": 0}, "spacingSizes": [{"name": "1", "size": "1rem", "slug": "10"}, {"name": "2", "size": "min(1.5rem, 2vw)", "slug": "20"}, {"name": "3", "size": "min(2.5rem, 3vw)", "slug": "30"}, {"name": "4", "size": "min(4rem, 5vw)", "slug": "40"}, {"name": "5", "size": "min(6.5rem, 8vw)", "slug": "50"}, {"name": "6", "size": "min(10.5rem, 13vw)", "slug": "60"}], "units": ["%", "px", "em", "rem", "vh", "vw"]}, "typography": {"fluid": true, "fontFamilies": [{"fontFace": [{"fontFamily": "Inter", "fontStretch": "normal", "fontStyle": "normal", "fontWeight": "300 900", "src": ["file:./assets/fonts/inter/Inter-VariableFont_slnt,wght.woff2"]}], "fontFamily": "\"Inter\", sans-serif", "name": "Inter", "slug": "body"}, {"fontFace": [{"fontFamily": "<PERSON><PERSON>", "fontStyle": "normal", "fontWeight": "400", "src": ["file:./assets/fonts/cardo/cardo_normal_400.woff2"]}, {"fontFamily": "<PERSON><PERSON>", "fontStyle": "italic", "fontWeight": "400", "src": ["file:./assets/fonts/cardo/cardo_italic_400.woff2"]}, {"fontFamily": "<PERSON><PERSON>", "fontStyle": "normal", "fontWeight": "700", "src": ["file:./assets/fonts/cardo/cardo_normal_700.woff2"]}], "fontFamily": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "slug": "heading"}, {"fontFamily": "-apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif", "name": "System Sans-serif", "slug": "system-sans-serif"}, {"fontFamily": "Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol", "name": "System Serif", "slug": "system-serif"}], "fontSizes": [{"fluid": false, "name": "Small", "size": "0.9rem", "slug": "small"}, {"fluid": false, "name": "Medium", "size": "1.05rem", "slug": "medium"}, {"fluid": {"min": "1.39rem", "max": "1.85rem"}, "name": "Large", "size": "1.85rem", "slug": "large"}, {"fluid": {"min": "1.85rem", "max": "2.5rem"}, "name": "Extra Large", "size": "2.5rem", "slug": "x-large"}, {"fluid": {"min": "2.5rem", "max": "3.27rem"}, "name": "Extra Extra Large", "size": "3.27rem", "slug": "xx-large"}], "writingMode": true}, "useRootPaddingAwareAlignments": true}, "styles": {"blocks": {"core/avatar": {"border": {"radius": "90px"}}, "core/button": {"variations": {"outline": {"spacing": {"padding": {"bottom": "calc(0.6rem - 1px)", "left": "calc(1rem - 1px)", "right": "calc(1rem - 1px)", "top": "calc(0.6rem - 1px)"}}, "border": {"width": "1px"}}}}, "core/buttons": {"spacing": {"blockGap": "0.7rem"}}, "core/calendar": {"color": {"text": "var(--wp--preset--color--contrast)"}, "css": ".wp-block-calendar table:where(:not(.has-text-color)) th{background-color:var(--wp--preset--color--contrast-2);color:var(--wp--preset--color--base);border-color:var(--wp--preset--color--contrast-2)} & table:where(:not(.has-text-color)) td{border-color:var(--wp--preset--color--contrast-2)}"}, "core/categories": {"spacing": {"padding": {"left": "0px", "right": "0px"}}, "css": "& {list-style-type:none;} & li{margin-bottom: 0.5rem;}"}, "core/code": {"border": {"color": "var(--wp--preset--color--contrast)", "radius": "var(--wp--preset--spacing--20)"}, "color": {"background": "var(--wp--preset--color--base-2)", "text": "var(--wp--preset--color--contrast-2)"}, "spacing": {"padding": {"bottom": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "left": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "right": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "top": "calc(var(--wp--preset--spacing--30) + 0.75rem)"}}, "typography": {"fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "normal", "fontWeight": "400", "lineHeight": "1.6"}}, "core/comment-author-name": {"color": {"text": "var(--wp--preset--color--contrast)"}, "elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"textDecoration": "none"}, ":hover": {"typography": {"textDecoration": "underline"}}}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)", "fontStyle": "normal", "fontWeight": "600"}}, "core/comment-content": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}, "spacing": {"margin": {"top": "var(--wp--preset--spacing--20)", "bottom": "var(--wp--preset--spacing--20)"}}}, "core/comment-date": {"color": {"text": "var(--wp--preset--color--contrast-2)"}, "elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast-2)"}, "typography": {"textDecoration": "none"}, ":hover": {"typography": {"textDecoration": "underline"}}}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)"}, "spacing": {"margin": {"top": "0px", "bottom": "0px"}}}, "core/comment-edit-link": {"elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast-2)"}, "typography": {"textDecoration": "none"}, ":hover": {"typography": {"textDecoration": "underline"}}}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/comment-reply-link": {"elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast-2)"}, "typography": {"textDecoration": "none"}, ":hover": {"typography": {"textDecoration": "underline"}}}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/post-comments-form": {"css": "& textarea, input{border-radius:.33rem}"}, "core/comments-pagination": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/comments-pagination-next": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/comments-pagination-numbers": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/comments-pagination-previous": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/footnotes": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/gallery": {"spacing": {"margin": {"bottom": "var(--wp--preset--spacing--50)"}}}, "core/image": {"variations": {"rounded": {"border": {"radius": "var(--wp--preset--spacing--20)"}}}}, "core/list": {"spacing": {"padding": {"left": "var(--wp--preset--spacing--10)"}}}, "core/loginout": {"css": "& input{border-radius:.33rem;padding:calc(0.667em + 2px);border:1px solid #949494;}"}, "core/navigation": {"elements": {"link": {":hover": {"typography": {"textDecoration": "underline"}}, "typography": {"textDecoration": "none"}}}, "typography": {"fontWeight": "500"}}, "core/post-author": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/post-author-name": {"elements": {"link": {":hover": {"typography": {"textDecoration": "underline"}}, "typography": {"textDecoration": "none"}}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/post-date": {"color": {"text": "var(--wp--preset--color--contrast-2)"}, "elements": {"link": {":hover": {"typography": {"textDecoration": "underline"}}, "color": {"text": "var(--wp--preset--color--contrast-2)"}, "typography": {"textDecoration": "none"}}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/post-excerpt": {"typography": {"lineHeight": "1.6"}}, "core/post-featured-image": {"border": {"radius": "var(--wp--preset--spacing--20)"}}, "core/post-terms": {"elements": {"link": {":hover": {"typography": {"textDecoration": "underline"}}, "typography": {"textDecoration": "none"}}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)"}, "css": "& .wp-block-post-terms__prefix{color: var(--wp--preset--color--contrast-2);}"}, "core/post-title": {"elements": {"link": {":hover": {"typography": {"textDecoration": "underline"}}, "typography": {"textDecoration": "none"}}}}, "core/pullquote": {"border": {"radius": "var(--wp--preset--spacing--20)"}, "elements": {"cite": {"typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "normal"}}}, "spacing": {"padding": {"bottom": "var(--wp--preset--spacing--40)", "top": "var(--wp--preset--spacing--40)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "var(--wp--preset--font-size--x-large)", "fontStyle": "italic", "fontWeight": "400", "letterSpacing": "0em", "lineHeight": "1.5"}}, "core/query-title": {"css": "& span {font-style: italic;}"}, "core/query-no-results": {"spacing": {"padding": {"top": "var(--wp--preset--spacing--30)"}}}, "core/quote": {"border": {"radius": "var(--wp--preset--spacing--20)"}, "color": {"background": "var(--wp--preset--color--base-2)"}, "css": "& :where(p) {margin-block-start:0;margin-block-end:calc(var(--wp--preset--spacing--10) + 0.5rem);} & :where(:last-child) {margin-block-end:0;} &.has-text-align-right.is-style-plain, .rtl .is-style-plain.wp-block-quote:not(.has-text-align-center):not(.has-text-align-left){border-width: 0 2px 0 0;padding-left:calc(var(--wp--preset--spacing--20) + 0.5rem);padding-right:calc(var(--wp--preset--spacing--20) + 0.5rem);} &.has-text-align-left.is-style-plain, body:not(.rtl) .is-style-plain.wp-block-quote:not(.has-text-align-center):not(.has-text-align-right){border-width: 0 0 0 2px;padding-left:calc(var(--wp--preset--spacing--20) + 0.5rem);padding-right:calc(var(--wp--preset--spacing--20) + 0.5rem)}", "elements": {"cite": {"typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "var(--wp--preset--font-size--small)", "fontStyle": "normal"}}}, "spacing": {"padding": {"bottom": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "left": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "right": "calc(var(--wp--preset--spacing--30) + 0.75rem)", "top": "calc(var(--wp--preset--spacing--30) + 0.75rem)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "var(--wp--preset--font-size--large)", "fontStyle": "italic", "lineHeight": "1.3"}, "variations": {"plain": {"border": {"color": "var(--wp--preset--color--contrast)", "radius": "0", "style": "solid", "width": "0"}, "color": {"background": "transparent"}, "spacing": {"padding": {"bottom": "var(--wp--preset--spacing--20)", "left": "var(--wp--preset--spacing--20)", "right": "var(--wp--preset--spacing--20)", "top": "var(--wp--preset--spacing--20)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontStyle": "normal", "fontSize": "var(--wp--preset--font-size--medium)", "lineHeight": "1.5"}}}}, "core/search": {"css": "& .wp-block-search__input{border-radius:.33rem}", "typography": {"fontSize": "var(--wp--preset--font-size--small)"}, "elements": {"button": {"border": {"radius": {"ref": "styles.elements.button.border.radius"}}}}}, "core/separator": {"border": {"color": "currentColor", "style": "solid", "width": "0 0 1px 0"}, "color": {"text": "var(--wp--preset--color--contrast)"}, "css": " &:not(.is-style-wide):not(.is-style-dots):not(.alignwide):not(.alignfull){width: var(--wp--preset--spacing--60)}"}, "core/site-tagline": {"color": {"text": "var(--wp--preset--color--contrast-2)"}, "typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "core/site-title": {"elements": {"link": {":hover": {"typography": {"textDecoration": "none"}}, "typography": {"textDecoration": "none"}}}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "1.2rem", "fontStyle": "normal", "fontWeight": "600"}}}, "color": {"background": "var(--wp--preset--color--base)", "text": "var(--wp--preset--color--contrast)"}, "elements": {"button": {":active": {"color": {"background": "var(--wp--preset--color--contrast)", "text": "var(--wp--preset--color--base)"}}, ":focus": {"color": {"background": "var(--wp--preset--color--contrast-2)", "text": "var(--wp--preset--color--base)"}, "outline": {"color": "var(--wp--preset--color--contrast)", "offset": "2px"}, "border": {"color": "var(--wp--preset--color--contrast-2)"}}, ":hover": {"color": {"background": "var(--wp--preset--color--contrast-2)", "text": "var(--wp--preset--color--base)"}, "border": {"color": "var(--wp--preset--color--contrast-2)"}}, "border": {"radius": ".33rem", "color": "var(--wp--preset--color--contrast)"}, "color": {"background": "var(--wp--preset--color--contrast)", "text": "var(--wp--preset--color--base)"}, "spacing": {"padding": {"bottom": "0.6rem", "left": "1rem", "right": "1rem", "top": "0.6rem"}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)", "fontStyle": "normal", "fontWeight": "500"}}, "caption": {"color": {"text": "var(--wp--preset--color--contrast-2)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "0.8rem"}}, "h1": {"typography": {"fontSize": "var(--wp--preset--font-size--xx-large)", "lineHeight": "1.15"}}, "h2": {"typography": {"fontSize": "var(--wp--preset--font-size--x-large)"}}, "h3": {"typography": {"fontSize": "var(--wp--preset--font-size--large)"}}, "h4": {"typography": {"fontSize": "clamp(1.1rem, 1.1rem + ((1vw - 0.2rem) * 0.767), 1.5rem)"}}, "h5": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)"}}, "h6": {"typography": {"fontSize": "var(--wp--preset--font-size--small)"}}, "heading": {"color": {"text": "var(--wp--preset--color--contrast)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontWeight": "400", "lineHeight": "1.2"}}, "link": {":hover": {"typography": {"textDecoration": "none"}}, "color": {"text": "var(--wp--preset--color--contrast)"}}}, "spacing": {"blockGap": "1.2rem", "padding": {"left": "var(--wp--preset--spacing--50)", "right": "var(--wp--preset--spacing--50)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--body)", "fontSize": "var(--wp--preset--font-size--medium)", "fontStyle": "normal", "fontWeight": "400", "lineHeight": "1.55"}, "css": ":where(.wp-site-blocks *:focus){outline-width:2px;outline-style:solid}"}, "templateParts": [{"area": "header", "name": "header", "title": "Header"}, {"area": "footer", "name": "footer", "title": "Footer"}, {"area": "uncategorized", "name": "sidebar", "title": "Sidebar"}, {"area": "uncategorized", "name": "post-meta", "title": "Post Meta"}], "customTemplates": [{"name": "page-no-title", "postTypes": ["page"], "title": "Page No Title"}, {"name": "page-with-sidebar", "postTypes": ["page"], "title": "Page with <PERSON>bar"}, {"name": "page-wide", "postTypes": ["page"], "title": "Page with Wide Image"}, {"name": "single-with-sidebar", "postTypes": ["post"], "title": "Single with Sidebar"}]}