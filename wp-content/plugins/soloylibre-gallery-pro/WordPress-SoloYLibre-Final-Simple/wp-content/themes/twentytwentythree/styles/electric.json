{"$schema": "https://schemas.wp.org/wp/6.5/theme.json", "version": 2, "title": "Electric", "settings": {"color": {"palette": [{"color": "#f3f3f1", "name": "Base", "slug": "base"}, {"color": "#2500ff", "name": "Contrast", "slug": "contrast"}, {"color": "#f3f3f1", "name": "Primary", "slug": "primary"}, {"color": "#2500ff", "name": "Secondary", "slug": "secondary"}, {"color": "#f6f6f6", "name": "Tertiary", "slug": "tertiary"}]}}, "styles": {"elements": {"button": {"border": {"style": "solid", "width": "2px", "color": "var(--wp--preset--color--contrast)"}, "color": {"background": "var(--wp--preset--color--contrast)", "text": "var(--wp--preset--color--base)"}, "spacing": {"padding": {"top": ".667em", "right": "1.333em", "bottom": ".667em", "left": "1.333em"}}, ":active": {"typography": {"textDecoration": "underline dotted"}}, ":focus": {"typography": {"textDecoration": "underline dotted"}}, ":hover": {"border": {"color": "var(--wp--preset--color--contrast)", "style": "solid", "width": "2px"}, "color": {"background": "var(--wp--preset--color--base)", "text": "var(--wp--preset--color--contrast)"}}, ":visited": {"color": {"text": "var(--wp--preset--color--base)"}}}, "link": {":focus": {"typography": {"textDecoration": "underline dotted"}}, ":active": {"typography": {"textDecoration": "underline dotted"}}}}, "typography": {"fontFamily": "var(--wp--preset--font-family--dm-sans)"}}}