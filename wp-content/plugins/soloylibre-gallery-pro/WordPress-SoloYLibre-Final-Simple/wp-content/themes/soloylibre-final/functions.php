<?php
/**
 * SoloYLibre Ultimate Final Theme Functions
 * Desarrollado por Jose <PERSON>carnacion (JoseTusabe)
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

function soloylibre_final_setup() {
    add_theme_support( 'title-tag' );
    add_theme_support( 'post-thumbnails' );
    add_theme_support( 'html5', array( 'search-form', 'comment-form', 'comment-list', 'gallery', 'caption' ) );
    add_theme_support( 'automatic-feed-links' );
}
add_action( 'after_setup_theme', 'soloylibre_final_setup' );

function soloylibre_final_scripts() {
    wp_enqueue_style( 'soloylibre-final-style', get_stylesheet_uri(), array(), '1.0.0' );
}
add_action( 'wp_enqueue_scripts', 'soloylibre_final_scripts' );

function soloylibre_final_admin_footer() {
    echo '<span>🇩🇴 Desarrollado por <strong><PERSON> (JoseTusabe)</strong> desde <PERSON>, República Dominicana | 📧 <EMAIL></span>';
}
add_filter( 'admin_footer_text', 'soloylibre_final_admin_footer' );
?>
