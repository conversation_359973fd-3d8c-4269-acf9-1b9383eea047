/*
Theme Name: SoloYLibre Ultimate Final
Description: Tema profesional desarrollado por <PERSON> (JoseTusabe) desde San José <PERSON>, República Dominicana 🇩🇴
Author: <PERSON> (JoseTusabe)
Author URI: https://soloylibre.com
Version: 1.0.0
*/

:root {
    --primary: #667eea;
    --secondary: #764ba2;
    --dominican-red: #ce1126;
    --dominican-blue: #002d62;
    --success: #22c55e;
}

* { margin: 0; padding: 0; box-sizing: border-box; }

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1a202c;
    line-height: 1.6;
}

.site-header {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    padding: 2rem 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.site-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 900;
    margin-bottom: 0.5rem;
}

.site-title a {
    color: inherit;
    text-decoration: none;
}

.site-description {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.developer-info {
    font-size: 0.9rem;
    opacity: 0.8;
}

.dominican-flag {
    font-size: 1.2rem;
    margin: 0 0.5rem;
}

.content-area {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.site-main {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.entry-title {
    color: var(--primary);
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.entry-title a {
    color: inherit;
    text-decoration: none;
}

.entry-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success), #16a34a);
    color: white;
}

.site-footer {
    background: #1a202c;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

.success-notice {
    background: linear-gradient(135deg, var(--success), #16a34a);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin: 2rem 0;
    text-align: center;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.info-card {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 15px;
    border-left: 5px solid var(--primary);
}

@media (max-width: 768px) {
    .content-area {
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    .site-main {
        padding: 1.5rem;
    }
}
