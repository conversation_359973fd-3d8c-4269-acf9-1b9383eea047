<?php
/**
 * Instalador Específico para localhost:8888/wp/wordpress/
 * SoloYLibre Gallery Pro v2.0.0
 * Para <PERSON> (JoseTusabe)
 * Desarrollado por JEYKO AI
 */

echo "<h1>🚀 Instalador SoloYLibre Gallery Pro v2.0.0</h1>";
echo "<p><strong>Para:</strong> http://localhost:8888/wp/wordpress/</p>";
echo "<p><strong>Fotógrafo:</strong> Jose <PERSON> (JoseTusabe)</p>";

// Detectar la ruta de WordPress
$wordpress_paths = array(
    '/Applications/MAMP/htdocs/wp/wordpress/',
    '/Applications/XAMPP/htdocs/wp/wordpress/',
    '/var/www/html/wp/wordpress/',
    getcwd() . '/../../../wp/wordpress/',
    getcwd() . '/../../wp/wordpress/',
    getcwd() . '/../wp/wordpress/'
);

$wordpress_path = null;
foreach ($wordpress_paths as $path) {
    if (file_exists($path . 'wp-config.php')) {
        $wordpress_path = $path;
        break;
    }
}

if (!$wordpress_path) {
    echo "<p>❌ No se pudo encontrar la instalación de WordPress</p>";
    echo "<p>🔍 Rutas verificadas:</p>";
    echo "<ul>";
    foreach ($wordpress_paths as $path) {
        echo "<li>$path</li>";
    }
    echo "</ul>";
    echo "<p>💡 <strong>Solución:</strong> Copia manualmente el plugin a tu directorio de WordPress</p>";
    exit;
}

echo "<p>✅ WordPress encontrado en: <code>$wordpress_path</code></p>";

// Directorio de plugins
$plugins_dir = $wordpress_path . 'wp-content/plugins/';
$plugin_dir = $plugins_dir . 'soloylibre-gallery-pro/';

echo "<h2>📁 Preparando Directorios</h2>";

// Crear directorio del plugin
if (!is_dir($plugin_dir)) {
    if (mkdir($plugin_dir, 0755, true)) {
        echo "<p>✅ Directorio del plugin creado: <code>$plugin_dir</code></p>";
    } else {
        echo "<p>❌ Error creando directorio del plugin</p>";
        exit;
    }
} else {
    echo "<p>✅ Directorio del plugin ya existe</p>";
}

echo "<h2>📋 Copiando Archivos</h2>";

// Archivos a copiar
$files_to_copy = array(
    'soloylibre-gallery-plugin.php' => 'Archivo principal del plugin',
    'README.md' => 'Documentación',
    'quick-setup.php' => 'Configuración rápida'
);

// Directorios a copiar
$dirs_to_copy = array(
    'includes' => 'Clases PHP del plugin',
    'assets' => 'CSS, JS e imágenes',
    'admin' => 'Páginas de administración',
    'templates' => 'Plantillas',
    'languages' => 'Traducciones',
    'docs' => 'Documentación técnica'
);

// Copiar archivos individuales
foreach ($files_to_copy as $file => $description) {
    if (file_exists($file)) {
        if (copy($file, $plugin_dir . $file)) {
            echo "<p>✅ $description: <code>$file</code></p>";
        } else {
            echo "<p>❌ Error copiando: <code>$file</code></p>";
        }
    } else {
        echo "<p>⚠️ Archivo no encontrado: <code>$file</code></p>";
    }
}

// Función para copiar directorios recursivamente
function copy_directory($src, $dst) {
    if (!is_dir($src)) return false;
    
    if (!is_dir($dst)) {
        mkdir($dst, 0755, true);
    }
    
    $files = scandir($src);
    foreach ($files as $file) {
        if ($file != "." && $file != "..") {
            $src_file = $src . '/' . $file;
            $dst_file = $dst . '/' . $file;
            
            if (is_dir($src_file)) {
                copy_directory($src_file, $dst_file);
            } else {
                copy($src_file, $dst_file);
            }
        }
    }
    return true;
}

// Copiar directorios
foreach ($dirs_to_copy as $dir => $description) {
    if (is_dir($dir)) {
        if (copy_directory($dir, $plugin_dir . $dir)) {
            echo "<p>✅ $description: <code>$dir/</code></p>";
        } else {
            echo "<p>❌ Error copiando directorio: <code>$dir/</code></p>";
        }
    } else {
        echo "<p>⚠️ Directorio no encontrado: <code>$dir/</code></p>";
    }
}

echo "<h2>🔧 Configuración Automática</h2>";

// Crear archivo de configuración específico
$config_content = '<?php
/**
 * Configuración SoloYLibre Gallery Pro
 * Para localhost:8888/wp/wordpress/
 */

// Información del fotógrafo
define("SOLOYLIBRE_PHOTOGRAPHER_NAME", "Jose L Encarnacion");
define("SOLOYLIBRE_PHOTOGRAPHER_ALIAS", "JoseTusabe");
define("SOLOYLIBRE_PHOTOGRAPHER_BRAND", "SoloYLibre Photography");
define("SOLOYLIBRE_PHOTOGRAPHER_LOCATION", "San José de Ocoa, Dom. Rep. / USA");
define("SOLOYLIBRE_PHOTOGRAPHER_PHONE", "************");
define("SOLOYLIBRE_PHOTOGRAPHER_EMAIL", "<EMAIL>");

// URLs del sitio
define("SOLOYLIBRE_SITE_URL", "http://localhost:8888/wp/wordpress/");
define("SOLOYLIBRE_ADMIN_URL", "http://localhost:8888/wp/wordpress/wp-admin/");

// Configuración del servidor
define("SOLOYLIBRE_SERVER_MODEL", "Synology RS3618xs");
define("SOLOYLIBRE_SERVER_MEMORY", "56GB RAM");
define("SOLOYLIBRE_SERVER_STORAGE", "36TB");

// Credenciales por defecto
define("SOLOYLIBRE_DEFAULT_USERNAME", "admin_soloylibre");
define("SOLOYLIBRE_DEFAULT_PASSWORD", "JoseTusabe2025!");
define("SOLOYLIBRE_DEFAULT_EMAIL", "<EMAIL>");
';

file_put_contents($plugin_dir . 'config.php', $config_content);
echo "<p>✅ Archivo de configuración creado</p>";

echo "<h2>🎯 Activación del Plugin</h2>";

// Intentar cargar WordPress para activar el plugin
$wp_load_paths = array(
    $wordpress_path . 'wp-load.php',
    $wordpress_path . 'wp-config.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $wp_file) {
    if (file_exists($wp_file)) {
        try {
            require_once($wp_file);
            $wp_loaded = true;
            echo "<p>✅ WordPress cargado desde: <code>$wp_file</code></p>";
            break;
        } catch (Exception $e) {
            echo "<p>⚠️ Error cargando WordPress: " . $e->getMessage() . "</p>";
        }
    }
}

if ($wp_loaded && function_exists('activate_plugin')) {
    $plugin_file = 'soloylibre-gallery-pro/soloylibre-gallery-plugin.php';
    
    // Verificar si el plugin está activo
    if (function_exists('is_plugin_active') && is_plugin_active($plugin_file)) {
        echo "<p>✅ El plugin ya está activo</p>";
    } else {
        // Intentar activar el plugin
        $result = activate_plugin($plugin_file);
        if (is_wp_error($result)) {
            echo "<p>❌ Error activando plugin: " . $result->get_error_message() . "</p>";
        } else {
            echo "<p>✅ Plugin activado exitosamente</p>";
        }
    }
} else {
    echo "<p>⚠️ No se pudo cargar WordPress automáticamente</p>";
    echo "<p>💡 <strong>Activación manual:</strong></p>";
    echo "<ol>";
    echo "<li>Ve a <a href='http://localhost:8888/wp/wordpress/wp-admin/plugins.php' target='_blank'>Plugins</a></li>";
    echo "<li>Busca 'SoloYLibre Gallery Pro'</li>";
    echo "<li>Haz clic en 'Activar'</li>";
    echo "</ol>";
}

echo "<h2>🌐 Enlaces de Acceso</h2>";

$access_links = array(
    'WordPress Admin' => 'http://localhost:8888/wp/wordpress/wp-admin/',
    'Dashboard SoloYLibre' => 'http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery-dashboard',
    'Asistente de Fotos' => 'http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery-wizard',
    'Gestión de Plugins' => 'http://localhost:8888/wp/wordpress/wp-admin/plugins.php',
    'Sitio Principal' => 'http://localhost:8888/wp/wordpress/'
);

echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
foreach ($access_links as $title => $url) {
    echo "<p><a href='$url' target='_blank' style='color: #667eea; text-decoration: none; font-weight: bold;'>🔗 $title</a></p>";
}
echo "</div>";

echo "<h2>🔐 Credenciales</h2>";
echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;'>";
echo "<p><strong>Usuario:</strong> admin_soloylibre</p>";
echo "<p><strong>Contraseña:</strong> JoseTusabe2025!</p>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "</div>";

echo "<h2>📋 Información del Fotógrafo</h2>";
echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea;'>";
echo "<p><strong>Nombre:</strong> Jose L Encarnacion</p>";
echo "<p><strong>Alias:</strong> JoseTusabe</p>";
echo "<p><strong>Marca:</strong> SoloYLibre Photography</p>";
echo "<p><strong>Ubicación:</strong> San José de Ocoa, Dom. Rep. / USA</p>";
echo "<p><strong>Teléfono:</strong> ************</p>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Sitios Web:</strong></p>";
echo "<ul>";
echo "<li>josetusabe.com</li>";
echo "<li>soloylibre.com</li>";
echo "<li>1and1photo.com</li>";
echo "<li>joselencarnacion.com</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🚀 Próximos Pasos</h2>";
echo "<ol>";
echo "<li><strong>Accede al WordPress Admin</strong> usando el enlace de arriba</li>";
echo "<li><strong>Activa el plugin</strong> si no se activó automáticamente</li>";
echo "<li><strong>Ve al Dashboard SoloYLibre</strong> para empezar a usar el sistema</li>";
echo "<li><strong>Usa el Asistente de Fotos</strong> para gestionar tus imágenes</li>";
echo "</ol>";

echo "<h2>🎉 ¡Instalación Completada!</h2>";
echo "<p>El plugin <strong>SoloYLibre Gallery Pro v2.0.0</strong> ha sido instalado exitosamente para Jose L Encarnacion (JoseTusabe).</p>";

echo "<hr>";
echo "<p><em>🎨 Desarrollado por JEYKO AI para Jose L Encarnacion (JoseTusabe)</em></p>";
echo "<p><em>📸 SoloYLibre Photography - San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸</em></p>";

// CSS para mejorar la apariencia
echo "<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 40px; 
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

h1, h2 { 
    color: #333; 
    margin-top: 30px;
}

h1 { 
    border-bottom: 2px solid #667eea; 
    padding-bottom: 10px; 
    text-align: center;
    font-size: 28px;
}

p { 
    margin: 10px 0; 
    line-height: 1.6;
}

a { 
    color: #667eea; 
    text-decoration: none; 
    transition: all 0.3s ease;
}

a:hover { 
    text-decoration: underline; 
    color: #764ba2;
}

code {
    background: rgba(0,0,0,0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
}

ul, ol { 
    margin: 10px 0 10px 20px; 
}

hr { 
    margin: 30px 0; 
    border: none; 
    border-top: 1px solid #ddd; 
}
</style>";
?>
