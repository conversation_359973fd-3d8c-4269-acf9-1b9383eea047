<?php
/**
 * WordPress Configuration File - SoloYLibre Gallery Pro
 * Configuración con SQLite para desarrollo local
 * Desarrollado por JEYKO AI para Jose <PERSON> Encarnacion (JoseTusabe)
 */

// ** Configuración de Base de Datos SQLite ** //
define('DB_NAME', 'soloylibre_gallery');
define('DB_USER', 'admin');
define('DB_PASSWORD', '');
define('DB_HOST', 'localhost');
define('DB_CHARSET', 'utf8');
define('DB_COLLATE', '');

// ** Configuración SQLite ** //
define('DB_FILE', 'soloylibre_gallery.sqlite');
define('DB_DIR', dirname(__FILE__) . '/wp-content/database/');

// ** Claves de Seguridad ** //
define('AUTH_KEY',         'SoloYLibre-Auth-Key-Jose-<PERSON>-2025-Photography-Gallery-Pro');
define('SECURE_AUTH_KEY',  'SoloYLibre-Secure-Auth-Key-San-Jose-Ocoa-Dominican-Republic');
define('LOGGED_IN_KEY',    'SoloYLibre-Logged-In-Key-Professional-Photography-System');
define('NONCE_KEY',        'SoloYLibre-Nonce-Key-JEYKO-AI-Development-2025');
define('AUTH_SALT',        'SoloYLibre-Auth-Salt-Gallery-Management-System-Pro');
define('SECURE_AUTH_SALT', 'SoloYLibre-Secure-Auth-Salt-Photo-States-Manager');
define('LOGGED_IN_SALT',   'SoloYLibre-Logged-In-Salt-User-Interactions-System');
define('NONCE_SALT',       'SoloYLibre-Nonce-Salt-Modern-Dashboard-iPhone-Style');

// ** Prefijo de Tablas ** //
$table_prefix = 'sl_';

// ** Configuración de Debug ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);

// ** Configuración de WordPress ** //
define('WP_AUTO_UPDATE_CORE', false);
define('DISALLOW_FILE_EDIT', false);
define('WP_POST_REVISIONS', 3);
define('AUTOSAVE_INTERVAL', 300);

// ** URLs y Paths ** //
define('WP_HOME', 'http://localhost:8080');
define('WP_SITEURL', 'http://localhost:8080');
define('WP_CONTENT_URL', 'http://localhost:8080/wp-content');
define('WP_PLUGIN_URL', 'http://localhost:8080/wp-content/plugins');

// ** Configuración de Memoria ** //
define('WP_MEMORY_LIMIT', '256M');
define('WP_MAX_MEMORY_LIMIT', '512M');

// ** Configuración de SoloYLibre Gallery Pro ** //
define('SOLOYLIBRE_GALLERY_VERSION', '1.0.0');
define('SOLOYLIBRE_GALLERY_DEBUG', true);
define('SOLOYLIBRE_GALLERY_AUTO_INSTALL', true);

// ** Configuración de Usuario por Defecto ** //
define('SOLOYLIBRE_DEFAULT_USER', 'admin_soloylibre');
define('SOLOYLIBRE_DEFAULT_PASS', 'JoseTusabe2025!');
define('SOLOYLIBRE_DEFAULT_EMAIL', '<EMAIL>');

// ** Configuración de Fotografía ** //
define('SOLOYLIBRE_PHOTOGRAPHER_NAME', 'Jose L Encarnacion');
define('SOLOYLIBRE_PHOTOGRAPHER_NICKNAME', 'JoseTusabe');
define('SOLOYLIBRE_PHOTOGRAPHER_BRAND', 'SoloYLibre Photography');
define('SOLOYLIBRE_PHOTOGRAPHER_LOCATION', 'San José de Ocoa, Dom. Rep. / USA');
define('SOLOYLIBRE_PHOTOGRAPHER_PHONE', '************');

/* ¡Eso es todo, deja de editar! Feliz blogging. */

/** Absolute path to the WordPress directory. */
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

/** Sets up WordPress vars and included files. */
require_once(ABSPATH . 'wp-settings.php');
?>
