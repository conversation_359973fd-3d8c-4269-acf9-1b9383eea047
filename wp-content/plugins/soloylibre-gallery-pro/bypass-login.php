<?php
/**
 * Bypass Login para SoloYLibre Gallery Pro
 * Acceso directo sin contraseña para pruebas
 * Desarrollado por JEYKO AI para <PERSON> (JoseTusabe)
 */

// Cargar WordPress
require_once('wp-config.php');

// Verificar si wp-load.php existe
if (file_exists('wp-load.php')) {
    require_once('wp-load.php');
} else {
    // Cargar WordPress manualmente
    require_once('wp-settings.php');
}

// Verificar si ya hay una sesión activa
if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    echo "<h2>✅ Ya estás logueado como: " . $current_user->display_name . "</h2>";
    echo "<p><a href='/wp-admin/' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Ir al Admin</a></p>";
    echo "<p><a href='/wp-admin/admin.php?page=soloylibre-gallery-dashboard' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📱 Dashboard Moderno</a></p>";
    exit;
}

// Buscar o crear usuario administrador
$username = 'admin_soloylibre';
$password = 'JoseTusabe2025!';
$email = '<EMAIL>';

$user = get_user_by('login', $username);

if (!$user) {
    // Crear usuario si no existe
    $user_id = wp_create_user($username, $password, $email);
    
    if (is_wp_error($user_id)) {
        die("❌ Error creando usuario: " . $user_id->get_error_message());
    }
    
    $user = new WP_User($user_id);
    $user->set_role('administrator');
    
    // Actualizar información del usuario
    wp_update_user(array(
        'ID' => $user_id,
        'display_name' => 'Jose L Encarnacion (JoseTusabe)',
        'first_name' => 'Jose Luis',
        'last_name' => 'Encarnacion',
        'nickname' => 'JoseTusabe'
    ));
}

// Activar plugin si no está activo
$plugin_file = 'soloylibre-gallery-pro/soloylibre-gallery-plugin.php';
if (!is_plugin_active($plugin_file)) {
    require_once(ABSPATH . 'wp-admin/includes/plugin.php');
    activate_plugin($plugin_file);
}

// Login automático
wp_set_current_user($user->ID);
wp_set_auth_cookie($user->ID, true);

// Redirigir al dashboard del plugin
$redirect_url = admin_url('admin.php?page=soloylibre-gallery-dashboard');

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Gallery Pro - Acceso Directo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        
        .subtitle {
            opacity: 0.8;
            margin-bottom: 30px;
        }
        
        .success-message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .user-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .user-info h3 {
            margin: 0 0 10px 0;
            color: #feca57;
        }
        
        .credentials {
            text-align: left;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 15px 25px;
            text-decoration: none;
            border-radius: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: #ff6b6b;
            border-color: #ff6b6b;
        }
        
        .btn-primary:hover {
            background: #ff5252;
        }
        
        .footer {
            margin-top: 30px;
            opacity: 0.7;
            font-size: 12px;
        }
        
        .redirect-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📸</div>
        <h1>SoloYLibre Gallery Pro</h1>
        <p class="subtitle">Sistema de Gestión de Fotos Profesional</p>
        
        <div class="success-message">
            <h3>✅ ¡Acceso Configurado Exitosamente!</h3>
            <p>Plugin activado y usuario administrador creado</p>
        </div>
        
        <div class="user-info">
            <h3>👤 Credenciales de Acceso</h3>
            <div class="credentials">
                <strong>Usuario:</strong> <?php echo $username; ?><br>
                <strong>Contraseña:</strong> <?php echo $password; ?><br>
                <strong>Email:</strong> <?php echo $email; ?><br>
                <strong>Rol:</strong> Administrador
            </div>
        </div>
        
        <div class="buttons">
            <a href="<?php echo $redirect_url; ?>" class="btn btn-primary">
                📱 Dashboard Moderno
            </a>
            <a href="<?php echo admin_url(); ?>" class="btn">
                🔧 WordPress Admin
            </a>
            <a href="<?php echo admin_url('edit.php?post_type=soloylibre_photo'); ?>" class="btn">
                📸 Gestionar Fotos
            </a>
        </div>
        
        <div class="redirect-info">
            <strong>🚀 Redirigiendo automáticamente en 3 segundos...</strong><br>
            Si no se redirige, haz clic en "Dashboard Moderno"
        </div>
        
        <div class="footer">
            <p><strong>Desarrollado por JEYKO AI</strong><br>
            Para Jose L Encarnacion (JoseTusabe)<br>
            📍 San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸</p>
        </div>
    </div>
    
    <script>
        // Redirección automática
        setTimeout(function() {
            window.location.href = '<?php echo $redirect_url; ?>';
        }, 3000);
        
        // Mostrar información adicional
        console.log('🎉 SoloYLibre Gallery Pro - Plugin Activado');
        console.log('👤 Usuario: <?php echo $username; ?>');
        console.log('🔑 Contraseña: <?php echo $password; ?>');
        console.log('📱 Dashboard: <?php echo $redirect_url; ?>');
    </script>
</body>
</html>
