<?php
/**
 * Plugin Name: SoloYLibre Gallery - Bulletproof
 * Plugin URI: https://soloylibre.com
 * Description: 🇩🇴 JoseTusabe Photography - Bulletproof plugin with zero dependencies
 * Version: 2.0.0-bulletproof
 * Author: JEYKO AI for Jose L Encarnacion (JoseTusabe)
 * Author URI: https://josetusabe.com
 * Text Domain: soloylibre-gallery
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define constants
define('SOLOYLIBRE_BP_VERSION', '2.0.0-bulletproof');
define('SOLOYLIBRE_BP_URL', plugin_dir_url(__FILE__));
define('SOLOYLIBRE_BP_PATH', plugin_dir_path(__FILE__));

/**
 * Bulletproof Plugin Class - Zero Dependencies
 */
class SoloYLibre_Gallery_Bulletproof {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Use only the most basic WordPress hooks
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_soloylibre_bp_action', array($this, 'ajax_handler'));
        
        // Simple activation/deactivation
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'SoloYLibre Gallery',
            'SoloYLibre Gallery',
            'manage_options',
            'soloylibre-bp',
            array($this, 'dashboard_page'),
            'dashicons-camera',
            30
        );
        
        add_submenu_page(
            'soloylibre-bp',
            'Quick Posts',
            '⚡ Quick Posts',
            'manage_options',
            'soloylibre-bp-posts',
            array($this, 'posts_page')
        );
        
        add_submenu_page(
            'soloylibre-bp',
            'Photo Manager',
            '📁 Photos',
            'manage_options',
            'soloylibre-bp-photos',
            array($this, 'photos_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'soloylibre-bp') !== false) {
            wp_enqueue_script('jquery');
            
            // Inline JavaScript to avoid external dependencies
            wp_add_inline_script('jquery', '
                var soloylibreBP = {
                    ajaxurl: "' . admin_url('admin-ajax.php') . '",
                    nonce: "' . wp_create_nonce('soloylibre_bp_nonce') . '"
                };
            ');
        }
    }
    
    public function dashboard_page() {
        ?>
        <div class="wrap">
            <h1>📸 SoloYLibre Gallery - Bulletproof</h1>
            <p>🇩🇴 JoseTusabe Photography - Bulletproof plugin with zero dependencies</p>
            
            <div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
                <h2 style="color: #155724; margin: 0 0 10px 0;">🛡️ Bulletproof System Active!</h2>
                <p style="color: #155724; margin: 0;">Zero dependencies - Maximum stability - No critical errors</p>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0;">
                
                <div style="background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 25px; border-radius: 15px; text-align: center;">
                    <h3 style="margin: 0 0 15px 0;">⚡ Quick Posts</h3>
                    <p style="margin: 0 0 20px 0;">Create posts with photo galleries quickly and safely</p>
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-bp-posts'); ?>" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; display: inline-block;">Create Posts</a>
                </div>
                
                <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 25px; border-radius: 15px; text-align: center;">
                    <h3 style="margin: 0 0 15px 0;">📁 Photo Manager</h3>
                    <p style="margin: 0 0 20px 0;">Manage and organize your photography collection</p>
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-bp-photos'); ?>" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; display: inline-block;">Manage Photos</a>
                </div>
                
                <div style="background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 25px; border-radius: 15px; text-align: center;">
                    <h3 style="margin: 0 0 15px 0;">🛡️ System Status</h3>
                    <p style="margin: 0 0 20px 0;">All systems operational - No errors detected</p>
                    <a href="<?php echo home_url(); ?>" target="_blank" style="background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 25px; display: inline-block;">View Website</a>
                </div>
                
            </div>
            
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin: 30px 0;">
                <h3>🇩🇴 JoseTusabe Photography</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <p><strong>Photographer:</strong> Jose L Encarnacion</p>
                        <p><strong>Alias:</strong> JoseTusabe</p>
                        <p><strong>Brand:</strong> JoseTusabe Photography</p>
                        <p><strong>Location:</strong> San José de Ocoa, República Dominicana / USA</p>
                    </div>
                    <div>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Phone:</strong> ************</p>
                        <p><strong>Websites:</strong></p>
                        <ul style="margin: 5px 0;">
                            <li>josetusabe.com</li>
                            <li>soloylibre.com</li>
                            <li>1and1photo.com</li>
                            <li>joselencarnacion.com</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>📊 System Information</h3>
                <ul>
                    <li>✅ <strong>Plugin Version:</strong> <?php echo SOLOYLIBRE_BP_VERSION; ?></li>
                    <li>✅ <strong>WordPress:</strong> <?php echo get_bloginfo('version'); ?></li>
                    <li>✅ <strong>PHP:</strong> <?php echo PHP_VERSION; ?></li>
                    <li>✅ <strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></li>
                    <li>✅ <strong>Status:</strong> Bulletproof - No Dependencies</li>
                    <li>✅ <strong>Critical Errors:</strong> NONE</li>
                </ul>
            </div>
        </div>
        <?php
    }
    
    public function posts_page() {
        global $wpdb;
        
        // Get photo count
        $photo_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'attachment' AND post_mime_type LIKE 'image%'");
        
        ?>
        <div class="wrap">
            <h1>⚡ Quick Posts</h1>
            <p>🇩🇴 JoseTusabe Photography - Create posts with photo galleries</p>
            
            <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 20px 0;">
                <h3>🎯 Create New Post</h3>
                
                <form id="quick-post-form" style="max-width: 600px;">
                    <table class="form-table">
                        <tr>
                            <th><label for="post_title">📝 Post Title:</label></th>
                            <td><input type="text" id="post_title" name="post_title" value="Photography Collection - JoseTusabe" class="regular-text" required></td>
                        </tr>
                        <tr>
                            <th><label for="post_content">📖 Description:</label></th>
                            <td><textarea id="post_content" name="post_content" rows="4" class="large-text">New photography collection by JoseTusabe Photography, capturing the beauty of República Dominicana and special moments.</textarea></td>
                        </tr>
                        <tr>
                            <th><label for="photo_count">📊 Number of Photos:</label></th>
                            <td>
                                <input type="number" id="photo_count" name="photo_count" value="10" min="1" max="50" class="small-text">
                                <p class="description">Available photos: <?php echo $photo_count; ?></p>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <button type="button" class="button button-primary button-large" onclick="createQuickPost()">🚀 Create Post</button>
                    </p>
                </form>
                
                <div id="post-result" style="display: none; margin-top: 20px;"></div>
            </div>
            
            <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>ℹ️ How it works</h3>
                <ol>
                    <li>Enter your post title and description</li>
                    <li>Choose how many photos to include</li>
                    <li>Click "Create Post" to generate automatically</li>
                    <li>The post will be created as a draft for your review</li>
                    <li>Photos will be randomly selected from your media library</li>
                </ol>
            </div>
        </div>
        
        <script>
        function createQuickPost() {
            var title = document.getElementById('post_title').value;
            var content = document.getElementById('post_content').value;
            var photoCount = document.getElementById('photo_count').value;
            
            if (!title.trim()) {
                alert('Please enter a post title');
                return;
            }
            
            var button = document.querySelector('button');
            button.disabled = true;
            button.textContent = '⏳ Creating post...';
            
            jQuery.post(soloylibreBP.ajaxurl, {
                action: 'soloylibre_bp_action',
                bp_action: 'create_post',
                nonce: soloylibreBP.nonce,
                title: title,
                content: content,
                photo_count: photoCount
            }, function(response) {
                var resultDiv = document.getElementById('post-result');
                resultDiv.style.display = 'block';
                
                if (response.success) {
                    resultDiv.innerHTML = '<div style="background: #d4edda; padding: 15px; border-radius: 5px;"><h4>✅ Post Created Successfully!</h4><p>Post ID: ' + response.data.post_id + '</p><p><a href="' + response.data.edit_url + '" target="_blank" class="button button-primary">Edit Post</a></p></div>';
                } else {
                    resultDiv.innerHTML = '<div style="background: #f8d7da; padding: 15px; border-radius: 5px;"><h4>❌ Error</h4><p>' + response.data + '</p></div>';
                }
                
                button.disabled = false;
                button.textContent = '🚀 Create Post';
            });
        }
        </script>
        <?php
    }
    
    public function photos_page() {
        global $wpdb;
        
        // Get statistics
        $total_photos = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'attachment' AND post_mime_type LIKE 'image%'");
        $published_photos = count(get_option('soloylibre_published_photos', array()));
        $private_photos = count(get_option('soloylibre_private_photos', array()));
        
        ?>
        <div class="wrap">
            <h1>📁 Photo Manager</h1>
            <p>🇩🇴 JoseTusabe Photography - Manage your photography collection</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;">
                
                <div style="background: #007bff; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2em; font-weight: bold;"><?php echo $total_photos; ?></div>
                    <div>📷 Total Photos</div>
                </div>
                
                <div style="background: #28a745; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2em; font-weight: bold;"><?php echo $published_photos; ?></div>
                    <div>📝 Published</div>
                </div>
                
                <div style="background: #ffc107; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2em; font-weight: bold;"><?php echo $private_photos; ?></div>
                    <div>🔒 Private</div>
                </div>
                
                <div style="background: #17a2b8; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2em; font-weight: bold;"><?php echo max(0, $total_photos - $published_photos - $private_photos); ?></div>
                    <div>✨ Available</div>
                </div>
                
            </div>
            
            <div style="background: white; padding: 25px; border-radius: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 20px 0;">
                <h3>🔧 Photo Management Tools</h3>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
                        <h4>🔄 Reset Published Photos</h4>
                        <p style="font-size: 14px; color: #666;">Allow reuse of photos in new posts</p>
                        <button type="button" class="button button-secondary" onclick="resetPublishedPhotos()">Reset</button>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
                        <h4>📊 View Statistics</h4>
                        <p style="font-size: 14px; color: #666;">Detailed photo usage statistics</p>
                        <button type="button" class="button button-secondary" onclick="showStats()">View Stats</button>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
                        <h4>🧹 Clean Database</h4>
                        <p style="font-size: 14px; color: #666;">Remove orphaned photo data</p>
                        <button type="button" class="button button-secondary" onclick="cleanDatabase()">Clean</button>
                    </div>
                    
                </div>
                
                <div id="tool-result" style="display: none; margin-top: 20px;"></div>
            </div>
        </div>
        
        <script>
        function resetPublishedPhotos() {
            if (confirm('Reset all published photos? This will allow reusing photos in new posts.')) {
                executePhotoAction('reset_published', 'Published photos reset successfully');
            }
        }
        
        function showStats() {
            executePhotoAction('show_stats', 'Statistics loaded');
        }
        
        function cleanDatabase() {
            if (confirm('Clean orphaned photo data from database?')) {
                executePhotoAction('clean_database', 'Database cleaned successfully');
            }
        }
        
        function executePhotoAction(action, successMessage) {
            jQuery.post(soloylibreBP.ajaxurl, {
                action: 'soloylibre_bp_action',
                bp_action: action,
                nonce: soloylibreBP.nonce
            }, function(response) {
                var resultDiv = document.getElementById('tool-result');
                resultDiv.style.display = 'block';
                
                if (response.success) {
                    resultDiv.innerHTML = '<div style="background: #d4edda; padding: 15px; border-radius: 5px;">✅ ' + successMessage + '</div>';
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    resultDiv.innerHTML = '<div style="background: #f8d7da; padding: 15px; border-radius: 5px;">❌ Error: ' + response.data + '</div>';
                }
            });
        }
        </script>
        <?php
    }
    
    public function ajax_handler() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'soloylibre_bp_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }
        
        $bp_action = sanitize_text_field($_POST['bp_action']);
        
        switch ($bp_action) {
            case 'create_post':
                $this->ajax_create_post();
                break;
            case 'reset_published':
                $this->ajax_reset_published();
                break;
            case 'show_stats':
                $this->ajax_show_stats();
                break;
            case 'clean_database':
                $this->ajax_clean_database();
                break;
            default:
                wp_send_json_error('Invalid action');
        }
    }
    
    private function ajax_create_post() {
        $title = sanitize_text_field($_POST['title']);
        $content = sanitize_textarea_field($_POST['content']);
        $photo_count = intval($_POST['photo_count']);
        
        if (empty($title)) {
            wp_send_json_error('Title is required');
            return;
        }
        
        // Get random photos
        global $wpdb;
        $photos = $wpdb->get_results($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} WHERE post_type = 'attachment' AND post_mime_type LIKE 'image%%' ORDER BY RAND() LIMIT %d",
            $photo_count
        ));
        
        if (empty($photos)) {
            wp_send_json_error('No photos found in media library');
            return;
        }
        
        $photo_ids = array_map(function($photo) { return $photo->ID; }, $photos);
        
        // Create post
        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'draft',
            'post_type' => 'post',
            'post_author' => get_current_user_id()
        );
        
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            wp_send_json_error('Failed to create post');
            return;
        }
        
        // Add gallery shortcode
        $gallery_shortcode = '[gallery ids="' . implode(',', $photo_ids) . '" columns="3" size="medium"]';
        $updated_content = $content . "\n\n" . $gallery_shortcode . "\n\n---\n📸 JoseTusabe Photography\n🇩🇴 San José de Ocoa, República Dominicana";
        
        wp_update_post(array(
            'ID' => $post_id,
            'post_content' => $updated_content
        ));
        
        // Set featured image
        if (!empty($photo_ids)) {
            set_post_thumbnail($post_id, $photo_ids[0]);
        }
        
        // Track published photos
        $published_photos = get_option('soloylibre_published_photos', array());
        $published_photos = array_merge($published_photos, $photo_ids);
        update_option('soloylibre_published_photos', array_unique($published_photos));
        
        wp_send_json_success(array(
            'post_id' => $post_id,
            'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit')
        ));
    }
    
    private function ajax_reset_published() {
        update_option('soloylibre_published_photos', array());
        wp_send_json_success('Published photos reset');
    }
    
    private function ajax_show_stats() {
        global $wpdb;
        
        $stats = array(
            'total_photos' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'attachment' AND post_mime_type LIKE 'image%'"),
            'published_photos' => count(get_option('soloylibre_published_photos', array())),
            'private_photos' => count(get_option('soloylibre_private_photos', array())),
            'total_posts' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'post' AND post_status != 'trash'")
        );
        
        wp_send_json_success($stats);
    }
    
    private function ajax_clean_database() {
        global $wpdb;
        
        // Clean orphaned postmeta
        $wpdb->query("DELETE pm FROM {$wpdb->postmeta} pm LEFT JOIN {$wpdb->posts} p ON pm.post_id = p.ID WHERE p.ID IS NULL");
        
        // Clean orphaned options
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%' OR option_name LIKE '_site_transient_%'");
        
        wp_send_json_success('Database cleaned');
    }
    
    public function activate() {
        // Simple activation
        update_option('soloylibre_bp_version', SOLOYLIBRE_BP_VERSION);
        update_option('soloylibre_bp_activated', current_time('mysql'));
        
        // Initialize options
        if (!get_option('soloylibre_published_photos')) {
            add_option('soloylibre_published_photos', array());
        }
        if (!get_option('soloylibre_private_photos')) {
            add_option('soloylibre_private_photos', array());
        }
        
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        flush_rewrite_rules();
    }
}

// Initialize the plugin
function soloylibre_gallery_bulletproof_init() {
    return SoloYLibre_Gallery_Bulletproof::get_instance();
}

// Start the plugin
add_action('plugins_loaded', 'soloylibre_gallery_bulletproof_init');

// Add admin notice for successful activation
add_action('admin_notices', function() {
    if (get_transient('soloylibre_bp_activated')) {
        ?>
        <div class="notice notice-success is-dismissible">
            <p><strong>🛡️ SoloYLibre Gallery - Bulletproof activated!</strong> Zero dependencies - Maximum stability.</p>
        </div>
        <?php
        delete_transient('soloylibre_bp_activated');
    }
});

// Set activation transient
register_activation_hook(__FILE__, function() {
    set_transient('soloylibre_bp_activated', true, 5);
});
?>
