<?php
/**
 * Activate Glass System - SoloYLibre Gallery Plugin
 * Remove overlays and activate modern glassmorphism interface
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🎨 Activando Sistema Glass</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Transición a interfaz glassmorphism moderna</p>";

$changes_made = array();
$errors_found = array();

// 1. Update plugin options to use glass system
echo "<h2>⚙️ 1. Configurando Sistema Glass</h2>";

$glass_settings = array(
    'soloylibre_glass_enabled' => true,
    'soloylibre_glass_remove_overlays' => true,
    'soloylibre_glass_modern_ui' => true,
    'soloylibre_glass_photographer_name' => '<PERSON>',
    'soloylibre_glass_photographer_alias' => '<PERSON><PERSON><PERSON><PERSON>',
    'soloylibre_glass_photographer_brand' => 'JoseTusabe Photography',
    'soloylibre_glass_photographer_email' => '<EMAIL>',
    'soloylibre_glass_photographer_phone' => '************',
    'soloylibre_glass_photographer_location' => 'San José de Ocoa, Dom. Rep. / USA',
    'soloylibre_glass_website_main' => 'https://josetusabe.com',
    'soloylibre_glass_website_portfolio' => 'https://soloylibre.com',
    'soloylibre_glass_website_photo' => 'https://1and1photo.com',
    'soloylibre_glass_website_personal' => 'https://joselencarnacion.com',
    'soloylibre_glass_photos_per_page' => 50,
    'soloylibre_glass_default_gallery_style' => 'masonry',
    'soloylibre_glass_auto_feature_image' => true,
    'soloylibre_glass_enable_interactions' => true,
    'soloylibre_glass_ai_algorithm' => 'balanced',
    'soloylibre_glass_popularity_weight' => 30,
    'soloylibre_glass_enable_ai_suggestions' => true
);

foreach ($glass_settings as $option => $value) {
    if (update_option($option, $value)) {
        $changes_made[] = "✅ Configuración guardada: $option";
    } else {
        $errors_found[] = "❌ Error guardando: $option";
    }
}

// 2. Create assets directory if it doesn't exist
echo "<h2>📁 2. Verificando Estructura de Archivos</h2>";

$assets_dir = plugin_dir_path(__FILE__) . 'assets';
$js_dir = $assets_dir . '/js';
$css_dir = $assets_dir . '/css';

if (!file_exists($assets_dir)) {
    if (mkdir($assets_dir, 0755, true)) {
        $changes_made[] = "✅ Directorio assets creado";
    } else {
        $errors_found[] = "❌ Error creando directorio assets";
    }
}

if (!file_exists($js_dir)) {
    if (mkdir($js_dir, 0755, true)) {
        $changes_made[] = "✅ Directorio assets/js creado";
    } else {
        $errors_found[] = "❌ Error creando directorio assets/js";
    }
}

if (!file_exists($css_dir)) {
    if (mkdir($css_dir, 0755, true)) {
        $changes_made[] = "✅ Directorio assets/css creado";
    } else {
        $errors_found[] = "❌ Error creando directorio assets/css";
    }
}

// 3. Verify glass system files
echo "<h2>🔍 3. Verificando Archivos del Sistema Glass</h2>";

$required_files = array(
    'includes/class-glass-menu-system.php' => 'Sistema de menús glass',
    'assets/js/glass-system.js' => 'JavaScript del sistema glass'
);

foreach ($required_files as $file => $description) {
    $file_path = plugin_dir_path(__FILE__) . $file;
    if (file_exists($file_path)) {
        $changes_made[] = "✅ $description encontrado";
    } else {
        $errors_found[] = "❌ $description NO encontrado: $file";
    }
}

// 4. Test glass system initialization
echo "<h2>🧪 4. Probando Inicialización del Sistema Glass</h2>";

if (class_exists('SoloYLibre_Glass_Menu_System')) {
    $changes_made[] = "✅ Clase SoloYLibre_Glass_Menu_System disponible";
    
    try {
        $glass_system = new SoloYLibre_Glass_Menu_System();
        $changes_made[] = "✅ Sistema glass inicializado correctamente";
    } catch (Exception $e) {
        $errors_found[] = "❌ Error inicializando sistema glass: " . $e->getMessage();
    }
} else {
    $errors_found[] = "❌ Clase SoloYLibre_Glass_Menu_System NO disponible";
}

// 5. Update menu priorities to favor glass system
echo "<h2>📋 5. Configurando Prioridades de Menú</h2>";

// Remove old menu hooks if they exist
remove_action('admin_menu', 'soloylibre_add_admin_menu');
remove_action('admin_menu', array('SoloYLibre_Fullscreen_Wizard', 'add_admin_menu'));
remove_action('admin_menu', array('SoloYLibre_Bulk_Photo_Manager', 'add_admin_menu'));

$changes_made[] = "✅ Hooks de menú antiguos removidos";

// 6. Create CSS file for glass system
echo "<h2>🎨 6. Creando Archivos CSS del Sistema Glass</h2>";

$glass_css = "
/* Glass System CSS - SoloYLibre Gallery Plugin */
/* Modern glassmorphism interface styles */

.glass-system-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.glass-overlay-disabled {
    display: none !important;
}

.glass-fullscreen-disabled {
    position: static !important;
    z-index: auto !important;
    background: transparent !important;
}

/* Override old overlay styles */
.fullscreen-overlay,
.wizard-overlay,
.bulk-overlay {
    display: none !important;
}

/* Ensure glass cards are visible */
.glass-container,
.glass-card,
.glass-button {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .glass-container {
        margin: 5px;
        padding: 15px;
    }
    
    .glass-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}
";

$css_file = $css_dir . '/glass-system.css';
if (file_put_contents($css_file, $glass_css)) {
    $changes_made[] = "✅ Archivo CSS del sistema glass creado";
} else {
    $errors_found[] = "❌ Error creando archivo CSS del sistema glass";
}

// 7. Statistics and summary
echo "<h2>📊 7. Estadísticas de Activación</h2>";

$total_changes = count($changes_made);
$total_errors = count($errors_found);
$success_rate = $total_changes / ($total_changes + $total_errors) * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

// Success Card
echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_changes</div>";
echo "<div>✅ Cambios Exitosos</div>";
echo "</div>";

// Errors Card
echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_errors</div>";
echo "<div>❌ Errores Encontrados</div>";
echo "</div>";

// Success Rate Card
echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Tasa de Éxito</div>";
echo "</div>";

echo "</div>";

// 8. Show results
if (!empty($changes_made)) {
    echo "<h3 style='color: #28a745;'>✅ Cambios Realizados:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($changes_made as $change) {
        echo "<li>$change</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors_found)) {
    echo "<h3 style='color: #dc3545;'>❌ Errores Encontrados:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($errors_found as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// 9. Next steps
echo "<h2>🚀 9. Próximos Pasos</h2>";

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3 style='color: #0c5460; margin: 0 0 15px 0;'>🎯 Para completar la activación:</h3>";
echo "<ol style='color: #0c5460;'>";
echo "<li>Visita el nuevo dashboard glass: <a href='../../../wp-admin/admin.php?page=soloylibre-dashboard'>Dashboard Glass</a></li>";
echo "<li>Prueba las nuevas páginas sin overlays</li>";
echo "<li>Verifica que todas las funciones AJAX funcionen</li>";
echo "<li>Configura tus preferencias en la nueva página de configuraciones</li>";
echo "<li>Reporta cualquier problema encontrado</li>";
echo "</ol>";
echo "</div>";

// 10. Quick access links
echo "<h2>🔗 10. Enlaces de Acceso Rápido</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-dashboard' style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; backdrop-filter: blur(10px);'>🏠 Dashboard Glass</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-quick-posts' style='background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; backdrop-filter: blur(10px);'>⚡ Posts Rápidos</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-photo-manager' style='background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; backdrop-filter: blur(10px);'>📁 Gestor de Fotos</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-smart-selection' style='background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; backdrop-filter: blur(10px);'>🤖 Selección IA</a>";
echo "</div>";

// 11. Final status
echo "<div style='background: " . ($success_rate >= 80 ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' : 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)') . "; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center; backdrop-filter: blur(10px);'>";
echo "<h2 style='margin: 0 0 15px 0; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo ($success_rate >= 80 ? '🎉' : '⚠️') . " Sistema Glass " . ($success_rate >= 80 ? 'Activado' : 'Parcialmente Activado') . "</h2>";
echo "<p style='margin: 0; font-size: 18px; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo "Tasa de éxito: <strong>" . round($success_rate, 1) . "%</strong>";
echo "</p>";

if ($success_rate >= 80) {
    echo "<p style='margin: 10px 0 0 0; color: #155724; font-weight: bold;'>¡El sistema glass está activo! Los overlays han sido eliminados.</p>";
} else {
    echo "<p style='margin: 10px 0 0 0; color: #856404; font-weight: bold;'>Revisa los errores arriba para completar la activación.</p>";
}
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center; backdrop-filter: blur(10px);'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Sistema Glass Activado</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
