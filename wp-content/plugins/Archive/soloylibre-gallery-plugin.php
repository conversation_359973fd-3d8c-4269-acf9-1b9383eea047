<?php
/**
 * Plugin Name: SoloYLibre Gallery Pro
 * Plugin URI: https://soloylibre.com
 * Description: Professional photo management system with auto-loading photos, confetti effects, advanced interactions, centralized settings, enhanced lightbox, and comprehensive photo management for Jose L Encarnacion (JoseTusabe). Completely overhauled with massive improvements.
 * Version: 5.0.6
 * Author: <PERSON><PERSON><PERSON><PERSON> <PERSON> for SoloYLibre
 * Author URI: https://josetusabe.com
 * License: GPL v2 or later
 * Text Domain: soloylibre-gallery
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SOLOYLIBRE_GALLERY_VERSION', '3.0.0');
define('SOLOYLIBRE_GALLERY_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SOLOYLIBRE_GALLERY_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('SOLOYLIBRE_GALLERY_PLUGIN_FILE', __FILE__);

/**
 * Main SoloYLibre Gallery Plugin Class
 */
class SoloYLibre_Gallery_Plugin {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Admin hooks
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        
        // Shortcode
        add_shortcode('soloylibre_gallery', array($this, 'gallery_shortcode'));
        
        // AJAX hooks
        add_action('wp_ajax_load_more_photos', array($this, 'ajax_load_more_photos'));
        add_action('wp_ajax_nopriv_load_more_photos', array($this, 'ajax_load_more_photos'));
        
        // Custom post type
        add_action('init', array($this, 'register_post_types'));
        
        // Meta boxes
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_photo_meta'));
    }
    
    /**
     * Load dependencies safely
     */
    private function load_dependencies() {
        // Core classes - load only if they exist
        $core_classes = array(
            'includes/class-gallery-styles.php',
            'includes/class-membership-integration.php',
            'includes/class-content-protection.php',
            'includes/class-album-manager.php',
            'includes/class-photo-states-manager.php',
            'includes/class-user-interactions.php',
            'includes/class-fullscreen-wizard.php',
            'includes/class-simple-wizard.php',
            'includes/class-initial-setup.php',
            'includes/class-statistics-dashboard.php'
        );

        foreach ($core_classes as $class_file) {
            $file_path = SOLOYLIBRE_GALLERY_PLUGIN_PATH . $class_file;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }

        // Admin classes - essential for admin functionality
        $admin_classes = array(
            'includes/class-database.php',
            'includes/class-admin.php',
            'includes/class-dashboard.php'
        );

        foreach ($admin_classes as $class_file) {
            $file_path = SOLOYLIBRE_GALLERY_PLUGIN_PATH . $class_file;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }

        // Advanced features - SoloYLibre Professional System
        $advanced_classes = array(
            'includes/class-photo-wizard.php',
            'includes/class-bulk-photo-loader.php',
            'includes/class-api-manager.php',
            'includes/class-auth-manager.php'
        );

        foreach ($advanced_classes as $class_file) {
            $file_path = SOLOYLIBRE_GALLERY_PLUGIN_PATH . $class_file;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain
        load_plugin_textdomain('soloylibre-gallery', false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Include settings manager first (required by other classes)
        require_once plugin_dir_path(__FILE__) . 'includes/class-settings-manager.php';

        // Include confetti system
        require_once plugin_dir_path(__FILE__) . 'includes/class-confetti-system.php';

        // Include enhanced shortcode handler (depends on settings manager)
        require_once plugin_dir_path(__FILE__) . 'includes/class-enhanced-shortcode.php';

        // Initialize core classes safely
        if (class_exists('SoloYLibre_Gallery_Styles')) {
            $this->gallery_styles = new SoloYLibre_Gallery_Styles();
        }
        if (class_exists('SoloYLibre_Membership_Integration')) {
            $this->membership = new SoloYLibre_Membership_Integration();
        }
        if (class_exists('SoloYLibre_Content_Protection')) {
            $this->content_protection = new SoloYLibre_Content_Protection();
        }
        if (class_exists('SoloYLibre_Album_Manager')) {
            $this->album_manager = new SoloYLibre_Album_Manager();
        }
        if (class_exists('SoloYLibre_Photo_States_Manager')) {
            $this->photo_states = new SoloYLibre_Photo_States_Manager();
        }
        if (class_exists('SoloYLibre_User_Interactions')) {
            $this->user_interactions = new SoloYLibre_User_Interactions();
        }
        if (class_exists('SoloYLibre_Fullscreen_Wizard')) {
            $this->fullscreen_wizard = new SoloYLibre_Fullscreen_Wizard();
        }
        if (class_exists('SoloYLibre_Simple_Wizard')) {
            $this->simple_wizard = new SoloYLibre_Simple_Wizard();
        }
        if (class_exists('SoloYLibre_Initial_Setup')) {
            $this->initial_setup = new SoloYLibre_Initial_Setup();
        }
        if (class_exists('SoloYLibre_Statistics_Dashboard')) {
            $this->statistics_dashboard = new SoloYLibre_Statistics_Dashboard();
        }

        // Initialize admin safely
        if (is_admin() && class_exists('SoloYLibre_Gallery_Admin')) {
            $this->admin = new SoloYLibre_Gallery_Admin();
        }

        // Initialize advanced features for SoloYLibre Professional System
        if (class_exists('SoloYLibre_Photo_Wizard')) {
            $this->photo_wizard = new SoloYLibre_Photo_Wizard();
        }
        if (class_exists('SoloYLibre_Bulk_Photo_Loader')) {
            $this->bulk_loader = new SoloYLibre_Bulk_Photo_Loader();
        }
        // API Manager desactivado para evitar errores
        // if (class_exists('SoloYLibre_API_Manager')) {
        //     $this->api_manager = new SoloYLibre_API_Manager();
        // }
        if (class_exists('SoloYLibre_Auth_Manager')) {
            $this->auth_manager = new SoloYLibre_Auth_Manager();
        }

        // Add photographer branding
        add_action('wp_footer', array($this, 'add_photographer_branding'));
        add_action('admin_footer', array($this, 'add_photographer_branding'));
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        wp_enqueue_style(
            'soloylibre-gallery-style',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/css/gallery-styles.css',
            array(),
            SOLOYLIBRE_GALLERY_VERSION
        );
        
        wp_enqueue_script(
            'soloylibre-gallery-script',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/gallery-frontend.js',
            array('jquery'),
            SOLOYLIBRE_GALLERY_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('soloylibre-gallery-script', 'soloylibre_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('soloylibre_gallery_nonce'),
            'loading_text' => __('Cargando más fotos...', 'soloylibre-gallery'),
            'no_more_text' => __('No hay más fotos', 'soloylibre-gallery'),
            'photographer' => array(
                'name' => 'Jose L Encarnacion',
                'alias' => 'JoseTusabe',
                'brand' => 'SoloYLibre Photography',
                'location' => 'San José de Ocoa, Dom. Rep. / USA',
                'phone' => '************',
                'email' => '<EMAIL>',
                'websites' => array(
                    'josetusabe.com',
                    'soloylibre.com',
                    '1and1photo.com',
                    'joselencarnacion.com'
                )
            )
        ));
    }

    /**
     * Add photographer branding
     */
    public function add_photographer_branding() {
        echo '<!-- SoloYLibre Photography Platform by Jose L Encarnacion (JoseTusabe) - Developed by JEYKO AI -->';
        echo '<style>
        .soloylibre-branding {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .soloylibre-branding:hover {
            background: rgba(102, 126, 234, 1);
            transform: scale(1.05);
        }
        .soloylibre-branding-tooltip {
            position: absolute;
            bottom: 100%;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            margin-bottom: 5px;
        }
        .soloylibre-branding:hover .soloylibre-branding-tooltip {
            opacity: 1;
        }
        </style>';
        echo '<div class="soloylibre-branding" onclick="window.open(\'https://soloylibre.com\', \'_blank\')" title="SoloYLibre Photography - Jose L Encarnacion (JoseTusabe)">
            📸 SoloYLibre
            <div class="soloylibre-branding-tooltip">
                SoloYLibre Photography<br>
                Jose L Encarnacion (JoseTusabe)<br>
                San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸<br>
                📞 ************
            </div>
        </div>';
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        if (strpos($hook, 'soloylibre-gallery') !== false) {
            wp_enqueue_style(
                'soloylibre-gallery-admin',
                SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/css/admin-styles.css',
                array(),
                SOLOYLIBRE_GALLERY_VERSION
            );
            
            wp_enqueue_script(
                'soloylibre-gallery-admin',
                SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/admin-scripts.js',
                array('jquery', 'wp-color-picker'),
                SOLOYLIBRE_GALLERY_VERSION,
                true
            );
            
            wp_enqueue_style('wp-color-picker');
        }
        
        // Media uploader
        if (get_post_type() === 'soloylibre_photo') {
            wp_enqueue_media();
        }
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Set default options
        $default_options = array(
            'gallery_style' => 'grid',
            'photos_per_page' => 12,
            'enable_infinite_scroll' => true,
            'require_membership' => false,
            'photographer_name' => 'Jose L Encarnacion',
            'photographer_brand' => 'SoloYLibre',
            'photographer_email' => '<EMAIL>',
            'photographer_websites' => array(
                'josetusabe.com',
                'soloylibre.com',
                '1and1photo.com',
                'joselencarnacion.com'
            ),
            'photographer_phone' => '************',
            'photographer_location' => 'San José de Ocoa, Dom. Rep. / USA'
        );
        
        foreach ($default_options as $key => $value) {
            add_option('soloylibre_gallery_' . $key, $value);
        }
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Albums table
        $albums_table = $wpdb->prefix . 'soloylibre_albums';
        $albums_sql = "CREATE TABLE $albums_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            cover_image_id bigint(20),
            membership_level varchar(50),
            is_published tinyint(1) DEFAULT 1,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        // Photo metadata table
        $photos_table = $wpdb->prefix . 'soloylibre_photo_meta';
        $photos_sql = "CREATE TABLE $photos_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            album_id mediumint(9),
            is_publishable tinyint(1) DEFAULT 0,
            membership_level varchar(50),
            location varchar(255),
            camera_settings text,
            tags text,
            sort_order int(11) DEFAULT 0,
            views_count int(11) DEFAULT 0,
            likes_count int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY post_id (post_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($albums_sql);
        dbDelta($photos_sql);
    }
    
    /**
     * Register custom post types
     */
    public function register_post_types() {
        // Photo post type
        $labels = array(
            'name' => __('Fotos', 'soloylibre-gallery'),
            'singular_name' => __('Foto', 'soloylibre-gallery'),
            'menu_name' => __('SoloYLibre Gallery', 'soloylibre-gallery'),
            'add_new' => __('Añadir Nueva', 'soloylibre-gallery'),
            'add_new_item' => __('Añadir Nueva Foto', 'soloylibre-gallery'),
            'edit_item' => __('Editar Foto', 'soloylibre-gallery'),
            'new_item' => __('Nueva Foto', 'soloylibre-gallery'),
            'view_item' => __('Ver Foto', 'soloylibre-gallery'),
            'search_items' => __('Buscar Fotos', 'soloylibre-gallery'),
            'not_found' => __('No se encontraron fotos', 'soloylibre-gallery'),
            'not_found_in_trash' => __('No hay fotos en la papelera', 'soloylibre-gallery')
        );
        
        $args = array(
            'labels' => $labels,
            'public' => true,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'foto'),
            'capability_type' => 'post',
            'has_archive' => true,
            'hierarchical' => false,
            'menu_position' => 20,
            'menu_icon' => 'dashicons-camera',
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
            'show_in_rest' => true
        );
        
        register_post_type('soloylibre_photo', $args);
        
        // Register taxonomies
        register_taxonomy('photo_category', 'soloylibre_photo', array(
            'hierarchical' => true,
            'labels' => array(
                'name' => __('Categorías de Fotos', 'soloylibre-gallery'),
                'singular_name' => __('Categoría de Foto', 'soloylibre-gallery')
            ),
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'categoria-foto'),
            'show_in_rest' => true
        ));
        
        register_taxonomy('photo_tag', 'soloylibre_photo', array(
            'hierarchical' => false,
            'labels' => array(
                'name' => __('Etiquetas de Fotos', 'soloylibre-gallery'),
                'singular_name' => __('Etiqueta de Foto', 'soloylibre-gallery')
            ),
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'etiqueta-foto'),
            'show_in_rest' => true
        ));
    }
    
    /**
     * Admin init
     */
    public function admin_init() {
        // Register settings if needed
        register_setting('soloylibre_gallery_settings', 'soloylibre_gallery_options');

        // Ensure wizard assets are available
        if (isset($_GET['page']) && $_GET['page'] === 'soloylibre-wizard') {
            // Force load wizard assets
            add_action('admin_enqueue_scripts', array($this, 'enqueue_wizard_assets'), 5);
        }
    }

    /**
     * Enqueue wizard assets
     */
    public function enqueue_wizard_assets() {
        wp_enqueue_style(
            'soloylibre-wizard-style',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/css/fullscreen-wizard.css',
            array(),
            SOLOYLIBRE_GALLERY_VERSION
        );

        wp_enqueue_script(
            'soloylibre-wizard-script',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/fullscreen-wizard.js',
            array('jquery', 'wp-util'),
            SOLOYLIBRE_GALLERY_VERSION,
            true
        );
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Menú principal simplificado - solo lo esencial
        add_menu_page(
            'SoloYLibre Gallery',
            '📸 SoloYLibre',
            'edit_posts',
            'soloylibre-main',
            array($this, 'redirect_to_wizard'),
            'dashicons-camera',
            25
        );

        // Solo 2 submenús esenciales
        add_submenu_page(
            'soloylibre-main',
            'Crear Galería',
            '🧙‍♂️ Crear Galería',
            'edit_posts',
            'soloylibre-wizard',
            array($this, 'redirect_to_wizard')
        );

        add_submenu_page(
            'soloylibre-main',
            'Estadísticas',
            '📊 Estadísticas',
            'edit_posts',
            'soloylibre-statistics',
            array($this, 'redirect_to_stats')
        );
    }
    
    /**
     * Redirect to wizard (main functionality)
     */
    public function redirect_to_wizard() {
        wp_redirect(admin_url('admin.php?page=soloylibre-wizard'));
        exit;
    }

    /**
     * Redirect to statistics
     */
    public function redirect_to_stats() {
        wp_redirect(admin_url('admin.php?page=soloylibre-statistics'));
        exit;
    }

    /**
     * Legacy admin page (deprecated)
     */
    public function albums_page() {
        include SOLOYLIBRE_GALLERY_PLUGIN_PATH . 'admin/albums-page.php';
    }

    /**
     * Modern dashboard page
     */
    public function modern_dashboard_page() {
        include SOLOYLIBRE_GALLERY_PLUGIN_PATH . 'admin/modern-dashboard.php';
    }
    
    /**
     * Gallery shortcode
     */
    public function gallery_shortcode($atts) {
        $atts = shortcode_atts(array(
            'style' => get_option('soloylibre_gallery_gallery_style', 'grid'),
            'album' => '',
            'category' => '',
            'limit' => get_option('soloylibre_gallery_photos_per_page', 12),
            'membership_level' => ''
        ), $atts);
        
        return $this->gallery_styles->render_gallery($atts);
    }
    
    /**
     * AJAX load more photos
     */
    public function ajax_load_more_photos() {
        check_ajax_referer('soloylibre_gallery_nonce', 'nonce');
        
        $page = intval($_POST['page']);
        $style = sanitize_text_field($_POST['style']);
        $album = sanitize_text_field($_POST['album']);
        $category = sanitize_text_field($_POST['category']);
        $limit = intval($_POST['limit']);
        
        $photos = $this->get_photos(array(
            'page' => $page,
            'album' => $album,
            'category' => $category,
            'limit' => $limit
        ));
        
        if (!empty($photos)) {
            ob_start();
            foreach ($photos as $photo) {
                $this->gallery_styles->render_photo_item($photo, $style);
            }
            $html = ob_get_clean();
            
            wp_send_json_success(array(
                'html' => $html,
                'has_more' => count($photos) === $limit
            ));
        } else {
            wp_send_json_error(__('No hay más fotos', 'soloylibre-gallery'));
        }
    }
    
    /**
     * Get photos
     */
    private function get_photos($args = array()) {
        // Implementation for getting photos with filters
        // This will be expanded in the gallery styles class
        return array();
    }
}

// Initialize the plugin
function soloylibre_gallery_init() {
    return SoloYLibre_Gallery_Plugin::get_instance();
}

// Start the plugin
add_action('plugins_loaded', 'soloylibre_gallery_init');
