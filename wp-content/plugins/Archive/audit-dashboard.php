<?php
/**
 * SoloYLibre Gallery Pro - Audit Dashboard
 * Visual interface for plugin audit results
 * Developed by JEYKO AI for Jose <PERSON> (JoseTusabe)
 */

// Include WordPress
require_once('../../../wp-config.php');
require_once('audit-plugin.php');

// Run all audits
$audit = new SoloYLibre_Plugin_Audit();
$report = $audit->run_audit();

// Include additional test files
require_once('integrity-check.php');
require_once('user-interaction-test.php');

$integrity_checker = new SoloYLibre_Integrity_Check();
$integrity_report = $integrity_checker->run_check();

$interaction_tester = new SoloYLibre_User_Interaction_Test();
$interaction_report = $interaction_tester->run_tests();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Gallery Pro - Audit Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #CE1126;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .health-score {
            display: inline-block;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 20px 0;
        }
        
        .health-score.warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }
        
        .health-score.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .summary-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .summary-number.success {
            color: #28a745;
        }
        
        .summary-number.warning {
            color: #ffc107;
        }
        
        .summary-number.error {
            color: #dc3545;
        }
        
        .summary-label {
            color: #6c757d;
            font-weight: 600;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-left: 5px solid #CE1126;
            padding-left: 15px;
        }
        
        .results-list {
            list-style: none;
            padding: 0;
        }
        
        .results-list li {
            padding: 12px 20px;
            margin-bottom: 8px;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .results-list li:hover {
            transform: translateX(5px);
        }
        
        .results-list li.success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .results-list li.warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        
        .results-list li.error {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        .actions-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .action-btn {
            display: inline-block;
            background: linear-gradient(135deg, #CE1126, #a00e1f);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(206, 17, 38, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .action-btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .timestamp {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 20px;
        }
        
        .collapsible {
            cursor: pointer;
            user-select: none;
        }
        
        .collapsible:hover {
            background: rgba(0,0,0,0.05);
            border-radius: 10px;
            padding: 10px;
            margin: -10px;
        }
        
        .collapsible-content {
            max-height: 300px;
            overflow-y: auto;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
            
            .health-score {
                font-size: 1.2rem;
                padding: 12px 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔍 Plugin Audit Dashboard</h1>
            <p>SoloYLibre Gallery Pro v5.4.0 - Sistema de Diagnóstico</p>
            
            <?php
            $health_score = $report['summary']['health_score'];
            $health_class = '';
            if ($health_score >= 80) {
                $health_class = 'success';
            } elseif ($health_score >= 60) {
                $health_class = 'warning';
            } else {
                $health_class = 'danger';
            }
            ?>
            
            <div class="health-score <?php echo $health_class; ?>">
                🏥 Health Score: <?php echo $health_score; ?>%
            </div>
            
            <p class="timestamp">
                📅 Audit ejecutado: <?php echo $report['timestamp']; ?>
            </p>
        </div>
        
        <!-- Summary -->
        <div class="summary-grid">
            <div class="summary-card">
                <div class="summary-number success"><?php echo $report['summary']['success_count']; ?></div>
                <div class="summary-label">✅ Audit Básico</div>
            </div>

            <div class="summary-card">
                <div class="summary-number success"><?php echo $integrity_report['summary']['success']; ?></div>
                <div class="summary-label">🔒 Integridad</div>
            </div>

            <div class="summary-card">
                <div class="summary-number success"><?php echo $interaction_report['summary']['success']; ?></div>
                <div class="summary-label">👆 Interacciones</div>
            </div>

            <div class="summary-card">
                <div class="summary-number"><?php echo $report['summary']['health_score']; ?>%</div>
                <div class="summary-label">🏥 Health Score</div>
            </div>
        </div>

        <!-- Detailed Summary -->
        <div class="section">
            <h2>📊 Resumen Detallado de Audits</h2>

            <div class="features-grid">
                <div class="feature-card">
                    <h3>🔍 Audit Básico</h3>
                    <p>✅ Exitosos: <?php echo $report['summary']['success_count']; ?></p>
                    <p>⚠️ Advertencias: <?php echo $report['summary']['warnings_count']; ?></p>
                    <p>❌ Errores: <?php echo $report['summary']['errors_count']; ?></p>
                </div>

                <div class="feature-card">
                    <h3>🔒 Integridad de Archivos</h3>
                    <p>✅ Exitosos: <?php echo $integrity_report['summary']['success']; ?></p>
                    <p>⚠️ Advertencias: <?php echo $integrity_report['summary']['warnings']; ?></p>
                    <p>❌ Errores: <?php echo $integrity_report['summary']['errors']; ?></p>
                    <p>📊 Salud: <?php echo $integrity_report['summary']['health_percentage']; ?>%</p>
                </div>

                <div class="feature-card">
                    <h3>👆 Test de Interacciones</h3>
                    <p>✅ Exitosos: <?php echo $interaction_report['summary']['success']; ?></p>
                    <p>⚠️ Advertencias: <?php echo $interaction_report['summary']['warning']; ?></p>
                    <p>❌ Errores: <?php echo $interaction_report['summary']['error']; ?></p>
                    <p>📊 Tasa éxito: <?php echo $interaction_report['summary']['success_rate']; ?>%</p>
                </div>
            </div>
        </div>
        
        <!-- Errors Section -->
        <?php if (!empty($report['errors'])): ?>
        <div class="section">
            <h2 class="collapsible" onclick="toggleSection('errors')">❌ Errores Críticos (<?php echo count($report['errors']); ?>)</h2>
            <div id="errors" class="collapsible-content">
                <ul class="results-list">
                    <?php foreach ($report['errors'] as $error): ?>
                    <li class="error"><?php echo esc_html($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Warnings Section -->
        <?php if (!empty($report['warnings'])): ?>
        <div class="section">
            <h2 class="collapsible" onclick="toggleSection('warnings')">⚠️ Advertencias (<?php echo count($report['warnings']); ?>)</h2>
            <div id="warnings" class="collapsible-content">
                <ul class="results-list">
                    <?php foreach ($report['warnings'] as $warning): ?>
                    <li class="warning"><?php echo esc_html($warning); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Success Section -->
        <div class="section">
            <h2 class="collapsible" onclick="toggleSection('success')">✅ Checks Exitosos (<?php echo count($report['success']); ?>)</h2>
            <div id="success" class="collapsible-content" style="display: none;">
                <ul class="results-list">
                    <?php foreach ($report['success'] as $success): ?>
                    <li class="success"><?php echo esc_html($success); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="actions-section">
            <h3>🔧 Acciones Recomendadas</h3>
            <p>Basado en los resultados del audit, estas son las acciones recomendadas:</p>
            
            <div style="margin-top: 20px;">
                <a href="?run_audit=1" class="action-btn">🔄 Ejecutar Audit Nuevamente</a>
                <a href="../../../wp-admin/admin.php?page=soloylibre-main" class="action-btn secondary">🏠 Ir al Dashboard</a>
                <a href="../../../wp-admin/admin.php?page=soloylibre-wizard" class="action-btn secondary">🧙‍♂️ Abrir Wizard</a>
            </div>
            
            <?php if ($report['summary']['errors_count'] > 0): ?>
            <div style="margin-top: 20px; padding: 20px; background: #f8d7da; border-radius: 10px; color: #721c24;">
                <strong>⚠️ Acción Requerida:</strong> Se encontraron <?php echo $report['summary']['errors_count']; ?> errores críticos que requieren atención inmediata.
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function toggleSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section.style.display === 'none') {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        }
        
        // Auto-refresh every 30 seconds if there are errors
        <?php if ($report['summary']['errors_count'] > 0): ?>
        setTimeout(function() {
            if (confirm('¿Deseas ejecutar el audit nuevamente para verificar si los errores han sido corregidos?')) {
                window.location.reload();
            }
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>
