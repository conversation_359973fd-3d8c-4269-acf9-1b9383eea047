<?php
/**
 * Check database structure and fix issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

global $wpdb;

echo "<h2>🔍 Verificación de Base de Datos</h2>";

$table_name = $wpdb->prefix . 'soloylibre_interactions';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

echo "<h3>📊 Estado de la Tabla</h3>";
echo "<p><strong>Tabla:</strong> $table_name</p>";
echo "<p><strong>Existe:</strong> " . ($table_exists ? "✅ Sí" : "❌ No") . "</p>";

if ($table_exists) {
    // Show table structure
    echo "<h3>🏗️ Estructura de la Tabla</h3>";
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>{$column->Default}</td>";
        echo "<td>{$column->Extra}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show indexes
    echo "<h3>🔑 Índices</h3>";
    $indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
    
    if (!empty($indexes)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Key Name</th><th>Column</th><th>Unique</th></tr>";
        
        foreach ($indexes as $index) {
            echo "<tr>";
            echo "<td>{$index->Key_name}</td>";
            echo "<td>{$index->Column_name}</td>";
            echo "<td>" . ($index->Non_unique == 0 ? "Sí" : "No") . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No hay índices definidos</p>";
    }
    
    // Test data insertion
    echo "<h3>🧪 Test de Inserción de Datos</h3>";
    
    $test_photo_id = 999999;
    $test_action = 'test_view';
    $current_time = current_time('mysql');
    
    // Try to insert test data
    $result = $wpdb->query($wpdb->prepare(
        "INSERT INTO $table_name 
         (photo_id, interaction_type, interaction_count, user_ip, user_agent, created_at, updated_at)
         VALUES (%d, %s, 1, %s, %s, %s, %s)
         ON DUPLICATE KEY UPDATE 
         interaction_count = interaction_count + 1,
         updated_at = %s",
        $test_photo_id,
        $test_action,
        '127.0.0.1',
        'Test Agent',
        $current_time,
        $current_time,
        $current_time
    ));
    
    if ($result !== false) {
        echo "<p>✅ Inserción exitosa (affected rows: $result)</p>";
        
        // Check if data was actually inserted
        $test_count = $wpdb->get_var($wpdb->prepare(
            "SELECT interaction_count FROM $table_name 
             WHERE photo_id = %d AND interaction_type = %s",
            $test_photo_id, $test_action
        ));
        
        if ($test_count !== null) {
            echo "<p>✅ Datos recuperados correctamente (count: $test_count)</p>";
            
            // Clean up test data
            $deleted = $wpdb->delete($table_name, array(
                'photo_id' => $test_photo_id, 
                'interaction_type' => $test_action
            ));
            
            echo "<p>🧹 Datos de prueba eliminados (rows deleted: $deleted)</p>";
        } else {
            echo "<p>❌ No se pudieron recuperar los datos insertados</p>";
        }
    } else {
        echo "<p>❌ Error en la inserción: " . $wpdb->last_error . "</p>";
    }
    
    // Show recent data
    echo "<h3>📈 Datos Recientes</h3>";
    $recent_data = $wpdb->get_results(
        "SELECT * FROM $table_name 
         ORDER BY updated_at DESC 
         LIMIT 10"
    );
    
    if (!empty($recent_data)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Photo ID</th><th>Type</th><th>Count</th><th>IP</th><th>Updated</th></tr>";
        
        foreach ($recent_data as $row) {
            echo "<tr>";
            echo "<td>{$row->id}</td>";
            echo "<td>{$row->photo_id}</td>";
            echo "<td>{$row->interaction_type}</td>";
            echo "<td>{$row->interaction_count}</td>";
            echo "<td>{$row->user_ip}</td>";
            echo "<td>{$row->updated_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>⚠️ No hay datos en la tabla</p>";
    }
    
} else {
    echo "<h3>🔧 Creando Tabla</h3>";
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        photo_id bigint(20) NOT NULL,
        interaction_type varchar(50) NOT NULL,
        interaction_count int(11) DEFAULT 1,
        user_ip varchar(45) DEFAULT NULL,
        user_agent text DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY unique_photo_interaction (photo_id, interaction_type),
        KEY idx_photo_id (photo_id),
        KEY idx_interaction_type (interaction_type),
        KEY idx_updated_at (updated_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Check if table was created
    $table_created = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    
    if ($table_created) {
        echo "<p>✅ Tabla creada exitosamente</p>";
    } else {
        echo "<p>❌ Error al crear la tabla: " . $wpdb->last_error . "</p>";
    }
}

// Check WordPress options
echo "<h3>⚙️ Opciones de WordPress</h3>";
$options_to_check = array(
    'soloylibre_published_photos',
    'soloylibre_private_photos',
    'soloylibre_unwanted_photos',
    'soloylibre_deleted_photos'
);

foreach ($options_to_check as $option) {
    $value = get_option($option, array());
    $count = is_array($value) ? count($value) : 0;
    echo "<p><strong>$option:</strong> $count elementos</p>";
}

echo "<h3>🔄 Acciones de Reparación</h3>";
echo "<p><a href='?fix_table=1' style='background: #0073aa; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>🔧 Reparar Tabla</a></p>";
echo "<p><a href='?reset_options=1' style='background: #d63638; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>🔄 Reset Opciones</a></p>";

// Handle repair actions
if (isset($_GET['fix_table']) && $_GET['fix_table'] == '1') {
    echo "<h3>🔧 Reparando Tabla...</h3>";
    
    // Drop and recreate table
    $wpdb->query("DROP TABLE IF EXISTS $table_name");
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        photo_id bigint(20) NOT NULL,
        interaction_type varchar(50) NOT NULL,
        interaction_count int(11) DEFAULT 1,
        user_ip varchar(45) DEFAULT NULL,
        user_agent text DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY unique_photo_interaction (photo_id, interaction_type),
        KEY idx_photo_id (photo_id),
        KEY idx_interaction_type (interaction_type),
        KEY idx_updated_at (updated_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    echo "<p>✅ Tabla reparada</p>";
    echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
}

if (isset($_GET['reset_options']) && $_GET['reset_options'] == '1') {
    echo "<h3>🔄 Reseteando Opciones...</h3>";
    
    foreach ($options_to_check as $option) {
        update_option($option, array());
    }
    
    echo "<p>✅ Opciones reseteadas</p>";
    echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
}
?>
