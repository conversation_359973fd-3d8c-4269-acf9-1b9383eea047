<?php
/**
 * Fix Likes System - SoloYLibre Gallery Plugin
 * Ensure hearts/likes are properly saved and working
 * Developed by <PERSON><PERSON><PERSON><PERSON> AI for Jose L Encarnacion (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔧 Reparando Sistema de Likes</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Asegurando que los corazones se graben perfectamente</p>";

global $wpdb;

$fixes_applied = array();
$errors_found = array();

// 1. Ensure interactions table exists with correct structure
echo "<h2>🗄️ 1. Verificando/Creando Tabla de Interacciones</h2>";

$table_name = $wpdb->prefix . 'soloylibre_interactions';

// Create or update table with proper structure
$sql = "CREATE TABLE $table_name (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    photo_id bigint(20) NOT NULL,
    user_id bigint(20) DEFAULT NULL,
    user_ip varchar(45) DEFAULT NULL,
    reaction_type varchar(20) NOT NULL DEFAULT 'like',
    interaction_type varchar(20) DEFAULT NULL,
    interaction_count int(11) DEFAULT 1,
    is_simulated tinyint(1) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY photo_id (photo_id),
    KEY user_id (user_id),
    KEY reaction_type (reaction_type),
    KEY interaction_type (interaction_type),
    KEY created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
$result = dbDelta($sql);

if (!empty($result)) {
    $fixes_applied[] = "✅ Tabla de interacciones creada/actualizada correctamente";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Tabla de interacciones configurada correctamente</strong>";
    echo "</div>";
} else {
    $errors_found[] = "❌ Error configurando tabla de interacciones";
}

// 2. Verify table structure
echo "<h2>🔍 2. Verificando Estructura de la Tabla</h2>";

$columns = $wpdb->get_results("DESCRIBE $table_name");
$required_columns = array('photo_id', 'reaction_type', 'user_id', 'created_at');
$found_columns = array();

foreach ($columns as $column) {
    $found_columns[] = $column->Field;
}

$missing_columns = array_diff($required_columns, $found_columns);

if (empty($missing_columns)) {
    $fixes_applied[] = "✅ Todas las columnas requeridas están presentes";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Estructura de tabla correcta:</strong> " . implode(', ', $found_columns);
    echo "</div>";
} else {
    $errors_found[] = "❌ Columnas faltantes: " . implode(', ', $missing_columns);
}

// 3. Test and fix AJAX endpoints
echo "<h2>🔗 3. Verificando/Reparando Endpoints AJAX</h2>";

// Ensure User Interactions class is loaded and initialized
if (!class_exists('SoloYLibre_User_Interactions')) {
    require_once plugin_dir_path(__FILE__) . 'includes/class-user-interactions.php';
}

// Initialize the class to register AJAX hooks
if (class_exists('SoloYLibre_User_Interactions')) {
    $interactions_instance = new SoloYLibre_User_Interactions();
    $fixes_applied[] = "✅ Clase SoloYLibre_User_Interactions inicializada";
} else {
    $errors_found[] = "❌ Clase SoloYLibre_User_Interactions no disponible";
}

// Check AJAX endpoints
$ajax_endpoints = array(
    'add_photo_reaction',
    'remove_photo_reaction', 
    'get_photo_reactions',
    'generate_random_interactions'
);

foreach ($ajax_endpoints as $endpoint) {
    if (has_action("wp_ajax_$endpoint")) {
        $fixes_applied[] = "✅ Endpoint $endpoint registrado";
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "✅ <strong>$endpoint</strong> - Funcionando";
        echo "</div>";
    } else {
        $errors_found[] = "❌ Endpoint $endpoint NO registrado";
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "❌ <strong>$endpoint</strong> - NO registrado";
        echo "</div>";
    }
}

// 4. Add sample likes data for testing
echo "<h2>📊 4. Agregando Datos de Prueba</h2>";

// Get some sample photos
$sample_photos = $wpdb->get_results("
    SELECT ID FROM {$wpdb->posts} 
    WHERE post_type = 'attachment' 
    AND post_mime_type LIKE 'image%' 
    LIMIT 10
");

if (!empty($sample_photos)) {
    $likes_added = 0;
    
    foreach ($sample_photos as $photo) {
        // Add some random likes for testing
        $random_likes = rand(5, 25);
        
        for ($i = 0; $i < $random_likes; $i++) {
            $result = $wpdb->insert(
                $table_name,
                array(
                    'photo_id' => $photo->ID,
                    'user_id' => rand(1, 100),
                    'user_ip' => '127.0.0.' . rand(1, 255),
                    'reaction_type' => 'like',
                    'interaction_type' => 'like',
                    'interaction_count' => 1,
                    'is_simulated' => 1,
                    'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'))
                ),
                array('%d', '%d', '%s', '%s', '%s', '%d', '%d', '%s')
            );
            
            if ($result) {
                $likes_added++;
            }
        }
    }
    
    $fixes_applied[] = "✅ $likes_added likes de prueba agregados";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>$likes_added likes de prueba agregados</strong> para " . count($sample_photos) . " fotos";
    echo "</div>";
} else {
    $errors_found[] = "❌ No se encontraron fotos para agregar likes de prueba";
}

// 5. Create enhanced AJAX handler for glass system
echo "<h2>🎨 5. Integrando con Sistema Glass</h2>";

// Add glass system AJAX handler for likes
add_action('wp_ajax_glass_add_like', function() {
    check_ajax_referer('soloylibre_glass_nonce', 'nonce');
    
    $photo_id = intval($_POST['photo_id'] ?? 0);
    
    if (!$photo_id) {
        wp_send_json_error('ID de foto inválido');
        return;
    }
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'soloylibre_interactions';
    
    // Add like
    $result = $wpdb->insert(
        $table_name,
        array(
            'photo_id' => $photo_id,
            'user_id' => get_current_user_id() ?: null,
            'user_ip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'reaction_type' => 'like',
            'interaction_type' => 'like',
            'interaction_count' => 1,
            'is_simulated' => 0,
            'created_at' => current_time('mysql')
        ),
        array('%d', '%d', '%s', '%s', '%s', '%d', '%d', '%s')
    );
    
    if ($result) {
        // Get updated count
        $like_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE photo_id = %d AND reaction_type = 'like'",
            $photo_id
        ));
        
        wp_send_json_success(array(
            'like_count' => intval($like_count),
            'message' => 'Like agregado exitosamente'
        ));
    } else {
        wp_send_json_error('Error agregando like');
    }
});

add_action('wp_ajax_nopriv_glass_add_like', function() {
    // Same handler for non-logged users
    do_action('wp_ajax_glass_add_like');
});

$fixes_applied[] = "✅ Handler AJAX para sistema glass agregado";

// 6. Test the like system
echo "<h2>🧪 6. Probando Sistema de Likes</h2>";

if (!empty($sample_photos)) {
    $test_photo = $sample_photos[0]->ID;
    
    // Test adding a like
    $test_result = $wpdb->insert(
        $table_name,
        array(
            'photo_id' => $test_photo,
            'user_id' => 999,
            'user_ip' => '127.0.0.1',
            'reaction_type' => 'like',
            'interaction_type' => 'like',
            'interaction_count' => 1,
            'is_simulated' => 1,
            'created_at' => current_time('mysql')
        ),
        array('%d', '%d', '%s', '%s', '%s', '%d', '%d', '%s')
    );
    
    if ($test_result) {
        // Test getting likes count
        $likes_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE photo_id = %d AND reaction_type = 'like'",
            $test_photo
        ));
        
        $fixes_applied[] = "✅ Prueba de likes exitosa: $likes_count likes para foto $test_photo";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ <strong>Prueba exitosa:</strong> Foto $test_photo tiene $likes_count likes";
        echo "</div>";
    } else {
        $errors_found[] = "❌ Error en prueba de likes";
    }
}

// 7. Create JavaScript for frontend likes
echo "<h2>📱 7. Creando JavaScript para Frontend</h2>";

$js_content = "
/**
 * SoloYLibre Likes System JavaScript
 * Handles heart/like interactions
 */

jQuery(document).ready(function($) {
    // Handle like button clicks
    $(document).on('click', '.soloylibre-like-btn', function(e) {
        e.preventDefault();
        
        const \$btn = $(this);
        const photoId = \$btn.data('photo-id');
        const \$counter = \$btn.find('.like-count');
        
        if (!\$btn.hasClass('processing')) {
            \$btn.addClass('processing');
            
            $.ajax({
                url: soloylibreGlass.ajax_url || ajaxurl,
                type: 'POST',
                data: {
                    action: 'glass_add_like',
                    nonce: soloylibreGlass.nonce || soloylibre_interactions.nonce,
                    photo_id: photoId
                },
                success: function(response) {
                    if (response.success) {
                        \$counter.text(response.data.like_count);
                        \$btn.addClass('liked');
                        
                        // Add heart animation
                        \$btn.addClass('heart-animation');
                        setTimeout(() => \$btn.removeClass('heart-animation'), 600);
                        
                        // Show success message
                        showLikeMessage('💖 Like agregado!', 'success');
                    } else {
                        showLikeMessage('❌ Error: ' + response.data, 'error');
                    }
                },
                error: function() {
                    showLikeMessage('❌ Error de conexión', 'error');
                },
                complete: function() {
                    \$btn.removeClass('processing');
                }
            });
        }
    });
    
    function showLikeMessage(message, type) {
        const \$message = $('<div class=\"like-message like-message-' + type + '\">' + message + '</div>');
        $('body').append(\$message);
        
        setTimeout(() => \$message.fadeOut(() => \$message.remove()), 2000);
    }
});
";

$js_file = plugin_dir_path(__FILE__) . 'assets/js/likes-system.js';
if (file_put_contents($js_file, $js_content)) {
    $fixes_applied[] = "✅ Archivo JavaScript de likes creado";
} else {
    $errors_found[] = "❌ Error creando archivo JavaScript";
}

// 8. Create CSS for like buttons
$css_content = "
/* SoloYLibre Likes System CSS */
.soloylibre-like-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    font-weight: 500;
}

.soloylibre-like-btn:hover {
    background: linear-gradient(135deg, #ff5252, #d32f2f);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.soloylibre-like-btn.liked {
    background: linear-gradient(135deg, #e91e63, #ad1457);
}

.soloylibre-like-btn.processing {
    opacity: 0.7;
    cursor: not-allowed;
}

.soloylibre-like-btn.heart-animation {
    animation: heartBeat 0.6s ease;
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1.1); }
    75% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

.like-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    animation: slideInRight 0.3s ease;
}

.like-message-success {
    background: linear-gradient(135deg, #4caf50, #388e3c);
}

.like-message-error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
";

$css_file = plugin_dir_path(__FILE__) . 'assets/css/likes-system.css';
if (file_put_contents($css_file, $css_content)) {
    $fixes_applied[] = "✅ Archivo CSS de likes creado";
} else {
    $errors_found[] = "❌ Error creando archivo CSS";
}

// 9. Statistics
echo "<h2>📊 9. Estadísticas de Reparación</h2>";

$total_fixes = count($fixes_applied);
$total_errors = count($errors_found);
$success_rate = $total_fixes / ($total_fixes + $total_errors) * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

// Success Card
echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_fixes</div>";
echo "<div>✅ Reparaciones Exitosas</div>";
echo "</div>";

// Errors Card
echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_errors</div>";
echo "<div>❌ Errores Encontrados</div>";
echo "</div>";

// Success Rate Card
echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Tasa de Éxito</div>";
echo "</div>";

echo "</div>";

// Show results
if (!empty($fixes_applied)) {
    echo "<h3 style='color: #28a745;'>✅ Reparaciones Aplicadas:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors_found)) {
    echo "<h3 style='color: #dc3545;'>❌ Errores Encontrados:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($errors_found as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// 10. Test interface
echo "<h2>🎯 10. Interfaz de Prueba Final</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='verify-likes-system.php' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔍 Verificar Sistema</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-photo-manager' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>📁 Probar en Gestor</a>";
echo "</div>";

// 11. Final status
echo "<div style='background: " . ($success_rate >= 80 ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' : 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)') . "; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h2 style='margin: 0 0 15px 0; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo ($success_rate >= 80 ? '💖' : '⚠️') . " Sistema de Likes " . ($success_rate >= 80 ? 'Reparado' : 'Requiere Atención') . "</h2>";
echo "<p style='margin: 0; font-size: 18px; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo "Tasa de éxito: <strong>" . round($success_rate, 1) . "%</strong>";
echo "</p>";

if ($success_rate >= 80) {
    echo "<p style='margin: 10px 0 0 0; color: #155724; font-weight: bold;'>¡Los likes ahora se graban correctamente en la base de datos!</p>";
} else {
    echo "<p style='margin: 10px 0 0 0; color: #856404; font-weight: bold;'>Revisa los errores arriba para completar la reparación.</p>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Sistema de likes reparado</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
