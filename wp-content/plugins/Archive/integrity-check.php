<?php
/**
 * SoloYLibre Gallery Pro - File Integrity Check
 * Verifies plugin files and detects corruption
 * Developed by <PERSON><PERSON><PERSON><PERSON> <PERSON> for Jose L Encarnacion (JoseTusabe)
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

class SoloYLibre_Integrity_Check {
    
    private $plugin_dir;
    private $results = array();
    
    public function __construct() {
        $this->plugin_dir = plugin_dir_path(__FILE__);
    }
    
    /**
     * Run complete integrity check
     */
    public function run_check() {
        $this->check_core_files();
        $this->check_file_sizes();
        $this->check_syntax_errors();
        $this->check_file_modifications();
        $this->check_missing_functions();
        
        return array(
            'status' => 'completed',
            'timestamp' => current_time('mysql'),
            'results' => $this->results,
            'summary' => $this->generate_summary()
        );
    }
    
    /**
     * Check core plugin files
     */
    private function check_core_files() {
        $core_files = array(
            'soloylibre-gallery-plugin.php' => array('min_size' => 50000, 'required_functions' => array('soloylibre_gallery_init')),
            'includes/class-fullscreen-wizard.php' => array('min_size' => 20000, 'required_functions' => array('SoloYLibre_Fullscreen_Wizard')),
            'includes/class-gallery-styles.php' => array('min_size' => 10000, 'required_functions' => array('SoloYLibre_Gallery_Styles')),
            'includes/class-initial-setup.php' => array('min_size' => 5000, 'required_functions' => array('SoloYLibre_Initial_Setup')),
            'assets/js/fullscreen-wizard.js' => array('min_size' => 15000, 'required_functions' => array('initializeWizard')),
            'assets/css/fullscreen-wizard.css' => array('min_size' => 20000, 'required_functions' => array('.wizard-container'))
        );
        
        foreach ($core_files as $file => $requirements) {
            $file_path = $this->plugin_dir . $file;
            
            if (!file_exists($file_path)) {
                $this->results[] = array(
                    'type' => 'error',
                    'file' => $file,
                    'message' => 'Archivo crítico faltante'
                );
                continue;
            }
            
            $file_size = filesize($file_path);
            if ($file_size < $requirements['min_size']) {
                $this->results[] = array(
                    'type' => 'warning',
                    'file' => $file,
                    'message' => "Archivo muy pequeño (${file_size} bytes, mínimo {$requirements['min_size']})"
                );
            }
            
            $content = file_get_contents($file_path);
            $functions_found = 0;
            
            foreach ($requirements['required_functions'] as $function) {
                if (strpos($content, $function) !== false) {
                    $functions_found++;
                }
            }
            
            if ($functions_found == count($requirements['required_functions'])) {
                $this->results[] = array(
                    'type' => 'success',
                    'file' => $file,
                    'message' => 'Archivo íntegro y funcional'
                );
            } else {
                $this->results[] = array(
                    'type' => 'error',
                    'file' => $file,
                    'message' => "Funciones faltantes ({$functions_found}/" . count($requirements['required_functions']) . ")"
                );
            }
        }
    }
    
    /**
     * Check file sizes for anomalies
     */
    private function check_file_sizes() {
        $files = glob($this->plugin_dir . '**/*.php');
        
        foreach ($files as $file) {
            $size = filesize($file);
            $relative_path = str_replace($this->plugin_dir, '', $file);
            
            if ($size == 0) {
                $this->results[] = array(
                    'type' => 'error',
                    'file' => $relative_path,
                    'message' => 'Archivo vacío (0 bytes)'
                );
            } elseif ($size < 100) {
                $this->results[] = array(
                    'type' => 'warning',
                    'file' => $relative_path,
                    'message' => "Archivo sospechosamente pequeño ($size bytes)"
                );
            }
        }
    }
    
    /**
     * Check for PHP syntax errors
     */
    private function check_syntax_errors() {
        $php_files = glob($this->plugin_dir . '**/*.php');
        
        foreach ($php_files as $file) {
            $relative_path = str_replace($this->plugin_dir, '', $file);
            
            // Use php -l to check syntax
            $output = array();
            $return_var = 0;
            exec("php -l " . escapeshellarg($file) . " 2>&1", $output, $return_var);
            
            if ($return_var !== 0) {
                $this->results[] = array(
                    'type' => 'error',
                    'file' => $relative_path,
                    'message' => 'Error de sintaxis PHP: ' . implode(' ', $output)
                );
            } else {
                // Basic content checks
                $content = file_get_contents($file);
                
                // Check for PHP opening tag
                if (strpos($content, '<?php') === false) {
                    $this->results[] = array(
                        'type' => 'warning',
                        'file' => $relative_path,
                        'message' => 'Archivo PHP sin tag de apertura'
                    );
                }
                
                // Check for common issues
                if (strpos($content, 'parse error') !== false || strpos($content, 'fatal error') !== false) {
                    $this->results[] = array(
                        'type' => 'error',
                        'file' => $relative_path,
                        'message' => 'Posibles errores en el código'
                    );
                }
            }
        }
    }
    
    /**
     * Check for file modifications
     */
    private function check_file_modifications() {
        $critical_files = array(
            'soloylibre-gallery-plugin.php',
            'includes/class-fullscreen-wizard.php',
            'includes/class-gallery-styles.php'
        );
        
        foreach ($critical_files as $file) {
            $file_path = $this->plugin_dir . $file;
            
            if (file_exists($file_path)) {
                $mod_time = filemtime($file_path);
                $current_time = time();
                $hours_since_mod = ($current_time - $mod_time) / 3600;
                
                if ($hours_since_mod < 1) {
                    $this->results[] = array(
                        'type' => 'info',
                        'file' => $file,
                        'message' => 'Archivo modificado recientemente (' . round($hours_since_mod * 60) . ' minutos)'
                    );
                }
            }
        }
    }
    
    /**
     * Check for missing critical functions
     */
    private function check_missing_functions() {
        $critical_functions = array(
            'soloylibre_gallery_init' => 'soloylibre-gallery-plugin.php',
            'SoloYLibre_Gallery_Plugin' => 'soloylibre-gallery-plugin.php',
            'SoloYLibre_Fullscreen_Wizard' => 'includes/class-fullscreen-wizard.php',
            'SoloYLibre_Gallery_Styles' => 'includes/class-gallery-styles.php'
        );
        
        foreach ($critical_functions as $function => $file) {
            $file_path = $this->plugin_dir . $file;
            
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                if (strpos($content, $function) === false) {
                    $this->results[] = array(
                        'type' => 'error',
                        'file' => $file,
                        'message' => "Función crítica faltante: $function"
                    );
                } else {
                    $this->results[] = array(
                        'type' => 'success',
                        'file' => $file,
                        'message' => "Función crítica encontrada: $function"
                    );
                }
            }
        }
    }
    
    /**
     * Generate summary
     */
    private function generate_summary() {
        $summary = array(
            'total_checks' => count($this->results),
            'errors' => 0,
            'warnings' => 0,
            'success' => 0,
            'info' => 0
        );
        
        foreach ($this->results as $result) {
            $summary[$result['type']]++;
        }
        
        $summary['health_percentage'] = $summary['total_checks'] > 0 ? 
            round(($summary['success'] / $summary['total_checks']) * 100, 1) : 0;
        
        return $summary;
    }
    
    /**
     * Get results by type
     */
    public function get_results_by_type($type) {
        return array_filter($this->results, function($result) use ($type) {
            return $result['type'] === $type;
        });
    }
}

// Run check if accessed directly
if (isset($_GET['run_check']) && $_GET['run_check'] == '1') {
    $checker = new SoloYLibre_Integrity_Check();
    $report = $checker->run_check();
    
    header('Content-Type: application/json');
    echo json_encode($report, JSON_PRETTY_PRINT);
    exit;
}
?>
