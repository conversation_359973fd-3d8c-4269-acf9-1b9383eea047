<?php
/**
 * Create test photos for SoloYLibre Gallery
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

echo "<h2>📸 Creación de Fotos de Prueba</h2>";

// Check existing photos
$existing_photos = get_posts(array(
    'post_type' => 'attachment',
    'post_mime_type' => 'image',
    'posts_per_page' => -1,
    'post_status' => 'inherit'
));

echo "<h3>📊 Estado Actual</h3>";
echo "<p><strong>Fotos existentes:</strong> " . count($existing_photos) . "</p>";

if (count($existing_photos) > 0) {
    echo "<h4>🖼️ Fotos Encontradas:</h4>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 10px; margin: 20px 0;'>";
    
    foreach (array_slice($existing_photos, 0, 20) as $photo) {
        $thumbnail = wp_get_attachment_image_url($photo->ID, 'thumbnail');
        $title = get_the_title($photo->ID) ?: 'Sin título';
        
        echo "<div style='border: 1px solid #ddd; padding: 10px; text-align: center;'>";
        if ($thumbnail) {
            echo "<img src='$thumbnail' style='max-width: 100%; height: auto;' alt='$title'>";
        }
        echo "<p style='margin: 5px 0; font-size: 12px;'>ID: {$photo->ID}</p>";
        echo "<p style='margin: 5px 0; font-size: 12px;'>$title</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
    if (count($existing_photos) > 20) {
        echo "<p><em>... y " . (count($existing_photos) - 20) . " fotos más</em></p>";
    }
} else {
    echo "<p>⚠️ No hay fotos en la biblioteca de medios</p>";
    echo "<h3>🔧 Creando Fotos de Prueba</h3>";
    
    // Create test photos using placeholder images
    $test_photos = array(
        array('title' => 'Paisaje Montañoso', 'url' => 'https://picsum.photos/800/600?random=1'),
        array('title' => 'Atardecer en la Playa', 'url' => 'https://picsum.photos/800/600?random=2'),
        array('title' => 'Ciudad Nocturna', 'url' => 'https://picsum.photos/800/600?random=3'),
        array('title' => 'Bosque Verde', 'url' => 'https://picsum.photos/800/600?random=4'),
        array('title' => 'Flores Coloridas', 'url' => 'https://picsum.photos/800/600?random=5'),
        array('title' => 'Arquitectura Moderna', 'url' => 'https://picsum.photos/800/600?random=6'),
        array('title' => 'Retrato Artístico', 'url' => 'https://picsum.photos/600/800?random=7'),
        array('title' => 'Comida Deliciosa', 'url' => 'https://picsum.photos/800/600?random=8'),
        array('title' => 'Animales Salvajes', 'url' => 'https://picsum.photos/800/600?random=9'),
        array('title' => 'Deportes Extremos', 'url' => 'https://picsum.photos/800/600?random=10'),
        array('title' => 'Arte Callejero', 'url' => 'https://picsum.photos/800/600?random=11'),
        array('title' => 'Tecnología Futurista', 'url' => 'https://picsum.photos/800/600?random=12'),
        array('title' => 'Viajes Exóticos', 'url' => 'https://picsum.photos/800/600?random=13'),
        array('title' => 'Música en Vivo', 'url' => 'https://picsum.photos/800/600?random=14'),
        array('title' => 'Moda Elegante', 'url' => 'https://picsum.photos/600/800?random=15')
    );
    
    $created_count = 0;
    
    foreach ($test_photos as $index => $photo_data) {
        echo "<p>📥 Creando: {$photo_data['title']}...</p>";
        
        // Create a simple colored image instead of downloading
        $image_content = create_test_image($index + 1, $photo_data['title']);
        
        if ($image_content) {
            $upload_dir = wp_upload_dir();
            $filename = 'test-photo-' . ($index + 1) . '.jpg';
            $file_path = $upload_dir['path'] . '/' . $filename;
            
            // Save the image
            if (file_put_contents($file_path, $image_content)) {
                // Create attachment
                $attachment = array(
                    'guid' => $upload_dir['url'] . '/' . $filename,
                    'post_mime_type' => 'image/jpeg',
                    'post_title' => $photo_data['title'],
                    'post_content' => '',
                    'post_status' => 'inherit'
                );
                
                $attach_id = wp_insert_attachment($attachment, $file_path);
                
                if ($attach_id) {
                    // Generate metadata
                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                    $attach_data = wp_generate_attachment_metadata($attach_id, $file_path);
                    wp_update_attachment_metadata($attach_id, $attach_data);
                    
                    $created_count++;
                    echo "<p>✅ Creada: {$photo_data['title']} (ID: $attach_id)</p>";
                } else {
                    echo "<p>❌ Error al crear attachment para: {$photo_data['title']}</p>";
                }
            } else {
                echo "<p>❌ Error al guardar archivo para: {$photo_data['title']}</p>";
            }
        } else {
            echo "<p>❌ Error al crear imagen para: {$photo_data['title']}</p>";
        }
    }
    
    echo "<h3>✅ Resumen</h3>";
    echo "<p><strong>Fotos creadas:</strong> $created_count</p>";
    echo "<p><a href='?' style='background: #0073aa; color: white; padding: 10px; text-decoration: none; border-radius: 3px;'>🔄 Actualizar Página</a></p>";
}

// Test AJAX endpoint
echo "<h3>🧪 Test de AJAX Endpoint</h3>";
echo "<button onclick='testAjaxEndpoint()' style='background: #0073aa; color: white; padding: 10px; border: none; border-radius: 3px; cursor: pointer;'>🔍 Probar Vista Previa de Fotos</button>";
echo "<div id='ajax-result' style='margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 3px; display: none;'></div>";

?>

<script>
function testAjaxEndpoint() {
    const resultDiv = document.getElementById('ajax-result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '🔄 Probando endpoint AJAX...';
    
    fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=soloylibre_get_all_photos_preview&nonce=<?php echo wp_create_nonce('soloylibre_wizard_nonce'); ?>'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <h4>✅ AJAX Endpoint Funcionando</h4>
                <p><strong>Total fotos:</strong> ${data.data.total_photos}</p>
                <p><strong>Fotos publicadas:</strong> ${data.data.categorized.published}</p>
                <p><strong>Fotos privadas:</strong> ${data.data.categorized.private}</p>
                <p><strong>Fotos no deseadas:</strong> ${data.data.categorized.unwanted}</p>
                <p><strong>Fotos eliminadas:</strong> ${data.data.categorized.deleted}</p>
            `;
        } else {
            resultDiv.innerHTML = `<h4>❌ Error en AJAX</h4><p>${data.data}</p>`;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `<h4>❌ Error de Conexión</h4><p>${error.message}</p>`;
    });
}
</script>

<?php

function create_test_image($number, $title) {
    // Create a simple colored rectangle as test image
    $width = 800;
    $height = 600;
    
    // Create image
    $image = imagecreatetruecolor($width, $height);
    
    if (!$image) {
        return false;
    }
    
    // Create colors
    $colors = array(
        imagecolorallocate($image, 255, 99, 132),   // Red
        imagecolorallocate($image, 54, 162, 235),   // Blue
        imagecolorallocate($image, 255, 205, 86),   // Yellow
        imagecolorallocate($image, 75, 192, 192),   // Teal
        imagecolorallocate($image, 153, 102, 255),  // Purple
        imagecolorallocate($image, 255, 159, 64),   // Orange
        imagecolorallocate($image, 199, 199, 199),  // Grey
        imagecolorallocate($image, 83, 102, 255),   // Indigo
        imagecolorallocate($image, 255, 99, 255),   // Pink
        imagecolorallocate($image, 99, 255, 132),   // Green
    );
    
    $color_index = ($number - 1) % count($colors);
    $bg_color = $colors[$color_index];
    $text_color = imagecolorallocate($image, 255, 255, 255); // White
    
    // Fill background
    imagefill($image, 0, 0, $bg_color);
    
    // Add text
    $font_size = 24;
    $text = "Foto #$number";
    $text_box = imagettfbbox($font_size, 0, __DIR__ . '/arial.ttf', $text);
    
    // If font file doesn't exist, use imagestring instead
    if (!file_exists(__DIR__ . '/arial.ttf')) {
        $x = ($width - strlen($text) * 10) / 2;
        $y = $height / 2;
        imagestring($image, 5, $x, $y - 10, $text, $text_color);
        imagestring($image, 3, $x, $y + 20, substr($title, 0, 30), $text_color);
    }
    
    // Output to string
    ob_start();
    imagejpeg($image, null, 85);
    $image_content = ob_get_contents();
    ob_end_clean();
    
    // Clean up
    imagedestroy($image);
    
    return $image_content;
}
?>
