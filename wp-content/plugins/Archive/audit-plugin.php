<?php
/**
 * SoloYLibre Gallery Pro - Complete Plugin Audit
 * Comprehensive system check for critical errors
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Plugin_Audit {
    
    private $errors = array();
    private $warnings = array();
    private $success = array();
    private $db_tables = array();
    
    public function __construct() {
        $this->db_tables = array(
            'soloylibre_photos',
            'soloylibre_albums', 
            'soloylibre_interactions',
            'soloylibre_statistics'
        );
    }
    
    /**
     * Run complete audit
     */
    public function run_audit() {
        $this->check_database();
        $this->check_tables();
        $this->check_options();
        $this->check_files();
        $this->check_permissions();
        $this->check_ajax_endpoints();
        $this->check_shortcodes();
        $this->check_admin_pages();
        $this->check_user_capabilities();
        $this->check_javascript_errors();
        $this->check_css_loading();
        $this->check_plugin_conflicts();
        
        return $this->generate_report();
    }
    
    /**
     * Check database connection and status
     */
    private function check_database() {
        global $wpdb;
        
        try {
            $result = $wpdb->get_var("SELECT 1");
            if ($result == 1) {
                $this->success[] = "✅ Database connection: OK";
            } else {
                $this->errors[] = "❌ Database connection failed";
            }
        } catch (Exception $e) {
            $this->errors[] = "❌ Database error: " . $e->getMessage();
        }
        
        // Check database charset
        $charset = $wpdb->get_charset_collate();
        if (strpos($charset, 'utf8') !== false) {
            $this->success[] = "✅ Database charset: UTF8 compatible";
        } else {
            $this->warnings[] = "⚠️ Database charset may cause issues with special characters";
        }
    }
    
    /**
     * Check required database tables
     */
    private function check_tables() {
        global $wpdb;
        
        foreach ($this->db_tables as $table) {
            $table_name = $wpdb->prefix . $table;
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
            
            if ($exists) {
                $this->success[] = "✅ Table exists: $table_name";
                
                // Check table structure
                $columns = $wpdb->get_results("DESCRIBE $table_name");
                if (empty($columns)) {
                    $this->errors[] = "❌ Table $table_name has no columns";
                } else {
                    $this->success[] = "✅ Table structure OK: $table_name (" . count($columns) . " columns)";
                }
            } else {
                $this->errors[] = "❌ Missing table: $table_name";
            }
        }
        
        // Check WordPress core tables
        $core_tables = array('posts', 'postmeta', 'options', 'users');
        foreach ($core_tables as $table) {
            $table_name = $wpdb->prefix . $table;
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
            if (!$exists) {
                $this->errors[] = "❌ Critical: Missing WordPress core table: $table_name";
            }
        }
    }
    
    /**
     * Check plugin options and settings
     */
    private function check_options() {
        $required_options = array(
            'soloylibre_gallery_setup_complete',
            'soloylibre_photographer_info',
            'soloylibre_published_photos',
            'soloylibre_total_posts_created'
        );
        
        foreach ($required_options as $option) {
            $value = get_option($option);
            if ($value !== false) {
                $this->success[] = "✅ Option exists: $option";
            } else {
                $this->warnings[] = "⚠️ Missing option: $option (will be created on first use)";
            }
        }
        
        // Check for corrupted options
        $test_option = get_option('soloylibre_photographer_info');
        if ($test_option && !is_array($test_option)) {
            $this->errors[] = "❌ Corrupted option: soloylibre_photographer_info (not an array)";
        }
    }
    
    /**
     * Check required plugin files
     */
    private function check_files() {
        $required_files = array(
            'soloylibre-gallery-plugin.php',
            'includes/class-fullscreen-wizard.php',
            'includes/class-gallery-styles.php',
            'includes/class-initial-setup.php',
            'assets/js/fullscreen-wizard.js',
            'assets/css/fullscreen-wizard.css'
        );
        
        $plugin_dir = plugin_dir_path(__FILE__);
        
        foreach ($required_files as $file) {
            $file_path = $plugin_dir . $file;
            if (file_exists($file_path)) {
                $this->success[] = "✅ File exists: $file";
                
                // Check file permissions
                if (is_readable($file_path)) {
                    $this->success[] = "✅ File readable: $file";
                } else {
                    $this->errors[] = "❌ File not readable: $file";
                }
            } else {
                $this->errors[] = "❌ Missing file: $file";
            }
        }
    }
    
    /**
     * Check file and directory permissions
     */
    private function check_permissions() {
        $plugin_dir = plugin_dir_path(__FILE__);
        
        // Check main plugin directory
        if (is_writable($plugin_dir)) {
            $this->success[] = "✅ Plugin directory writable";
        } else {
            $this->warnings[] = "⚠️ Plugin directory not writable (may affect updates)";
        }
        
        // Check uploads directory
        $upload_dir = wp_upload_dir();
        if (is_writable($upload_dir['basedir'])) {
            $this->success[] = "✅ Uploads directory writable";
        } else {
            $this->errors[] = "❌ Uploads directory not writable";
        }
        
        // Check wp-content permissions
        if (is_writable(WP_CONTENT_DIR)) {
            $this->success[] = "✅ WP Content directory writable";
        } else {
            $this->warnings[] = "⚠️ WP Content directory not writable";
        }
    }
    
    /**
     * Check AJAX endpoints
     */
    private function check_ajax_endpoints() {
        $ajax_actions = array(
            'wizard_get_photos',
            'wizard_create_post',
            'wizard_automatic_posts',
            'soloylibre_get_all_photos_preview',
            'soloylibre_complete_setup',
            'soloylibre_reset_published_photos'
        );
        
        foreach ($ajax_actions as $action) {
            if (has_action("wp_ajax_$action")) {
                $this->success[] = "✅ AJAX endpoint registered: $action";
            } else {
                $this->errors[] = "❌ Missing AJAX endpoint: $action";
            }
        }
        
        // Check nonce system
        $nonce = wp_create_nonce('soloylibre_wizard_nonce');
        if ($nonce) {
            $this->success[] = "✅ Nonce system working";
        } else {
            $this->errors[] = "❌ Nonce system failed";
        }
    }
    
    /**
     * Check shortcodes
     */
    private function check_shortcodes() {
        global $shortcode_tags;
        
        $required_shortcodes = array('soloylibre_gallery');
        
        foreach ($required_shortcodes as $shortcode) {
            if (isset($shortcode_tags[$shortcode])) {
                $this->success[] = "✅ Shortcode registered: [$shortcode]";
            } else {
                $this->errors[] = "❌ Missing shortcode: [$shortcode]";
            }
        }
    }
    
    /**
     * Check admin pages and menus
     */
    private function check_admin_pages() {
        global $menu, $submenu;
        
        $found_main_menu = false;
        foreach ($menu as $menu_item) {
            if (strpos($menu_item[2], 'soloylibre') !== false) {
                $found_main_menu = true;
                break;
            }
        }
        
        if ($found_main_menu) {
            $this->success[] = "✅ Main admin menu registered";
        } else {
            $this->errors[] = "❌ Main admin menu not found";
        }
        
        // Check specific admin pages
        $admin_pages = array(
            'soloylibre-main',
            'soloylibre-wizard', 
            'soloylibre-statistics'
        );
        
        foreach ($admin_pages as $page) {
            if (menu_page_url($page, false)) {
                $this->success[] = "✅ Admin page exists: $page";
            } else {
                $this->warnings[] = "⚠️ Admin page may not be accessible: $page";
            }
        }
    }
    
    /**
     * Check user capabilities
     */
    private function check_user_capabilities() {
        $current_user = wp_get_current_user();
        
        if ($current_user->ID == 0) {
            $this->warnings[] = "⚠️ No user logged in for capability check";
            return;
        }
        
        $required_caps = array('edit_posts', 'upload_files', 'manage_options');
        
        foreach ($required_caps as $cap) {
            if (current_user_can($cap)) {
                $this->success[] = "✅ User capability: $cap";
            } else {
                $this->warnings[] = "⚠️ User lacks capability: $cap";
            }
        }
    }
    
    /**
     * Check for JavaScript errors (basic check)
     */
    private function check_javascript_errors() {
        $js_files = array(
            'assets/js/fullscreen-wizard.js',
            'assets/js/setup.js',
            'assets/js/admin.js'
        );
        
        $plugin_dir = plugin_dir_path(__FILE__);
        
        foreach ($js_files as $js_file) {
            $file_path = $plugin_dir . $js_file;
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                // Basic syntax checks
                if (strpos($content, 'function(') !== false || strpos($content, '=>') !== false) {
                    $this->success[] = "✅ JavaScript file syntax OK: $js_file";
                } else {
                    $this->warnings[] = "⚠️ JavaScript file may have issues: $js_file";
                }
                
                // Check for jQuery dependency
                if (strpos($content, '$') !== false || strpos($content, 'jQuery') !== false) {
                    $this->success[] = "✅ jQuery dependency found: $js_file";
                }
            }
        }
    }
    
    /**
     * Check CSS loading
     */
    private function check_css_loading() {
        $css_files = array(
            'assets/css/fullscreen-wizard.css',
            'assets/css/admin.css',
            'assets/css/gallery-styles.css'
        );
        
        $plugin_dir = plugin_dir_path(__FILE__);
        
        foreach ($css_files as $css_file) {
            $file_path = $plugin_dir . $css_file;
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                if (strpos($content, '{') !== false && strpos($content, '}') !== false) {
                    $this->success[] = "✅ CSS file syntax OK: $css_file";
                } else {
                    $this->warnings[] = "⚠️ CSS file may be empty or corrupted: $css_file";
                }
            }
        }
    }
    
    /**
     * Check for plugin conflicts
     */
    private function check_plugin_conflicts() {
        $active_plugins = get_option('active_plugins', array());
        
        $potential_conflicts = array(
            'nextgen-gallery' => 'NextGEN Gallery',
            'wp-gallery' => 'WP Gallery',
            'modula-best-grid-gallery' => 'Modula Gallery',
            'photo-gallery' => 'Photo Gallery'
        );
        
        foreach ($active_plugins as $plugin) {
            foreach ($potential_conflicts as $conflict_slug => $conflict_name) {
                if (strpos($plugin, $conflict_slug) !== false) {
                    $this->warnings[] = "⚠️ Potential conflict with: $conflict_name";
                }
            }
        }
        
        $this->success[] = "✅ Plugin conflict check completed";
    }
    
    /**
     * Generate audit report
     */
    private function generate_report() {
        $report = array(
            'status' => 'completed',
            'timestamp' => current_time('mysql'),
            'errors' => $this->errors,
            'warnings' => $this->warnings,
            'success' => $this->success,
            'summary' => array(
                'total_checks' => count($this->errors) + count($this->warnings) + count($this->success),
                'errors_count' => count($this->errors),
                'warnings_count' => count($this->warnings),
                'success_count' => count($this->success),
                'health_score' => $this->calculate_health_score()
            )
        );
        
        return $report;
    }
    
    /**
     * Calculate overall health score
     */
    private function calculate_health_score() {
        $total = count($this->errors) + count($this->warnings) + count($this->success);
        if ($total == 0) return 0;
        
        $score = (count($this->success) * 100 + count($this->warnings) * 50) / ($total * 100) * 100;
        return round($score, 1);
    }
}

// Run audit if accessed directly
if (isset($_GET['run_audit']) && $_GET['run_audit'] == '1') {
    $audit = new SoloYLibre_Plugin_Audit();
    $report = $audit->run_audit();
    
    header('Content-Type: application/json');
    echo json_encode($report, JSON_PRETTY_PRINT);
    exit;
}
?>
