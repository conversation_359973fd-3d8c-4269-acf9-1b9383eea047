<?php
/**
 * Fix Missing AJAX Endpoints - SoloYLibre Gallery Plugin
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔧 Reparación de Endpoints AJAX Faltantes</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Arreglando todos los enlaces AJAX</p>";

$fixes_applied = array();
$errors_found = array();

// 1. Check current AJAX endpoints
echo "<h2>🔍 1. Verificando Endpoints AJAX Actuales</h2>";

$all_ajax_endpoints = array(
    // Wizard endpoints (should be working)
    'wizard_get_photos' => 'SoloYLibre_Fullscreen_Wizard',
    'wizard_create_post' => 'SoloYLibre_Fullscreen_Wizard',
    'wizard_save_step' => 'SoloYLibre_Fullscreen_Wizard',
    'wizard_smart_selection' => 'SoloYLibre_Fullscreen_Wizard',
    'wizard_dominican_styles' => 'SoloYLibre_Fullscreen_Wizard',
    'wizard_automatic_posts' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_get_all_photos_preview' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_create_advanced_auto_post' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_get_photo_stats' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_create_quick_auto_post' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_get_detailed_stats' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_get_recent_photos' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_get_popular_photos' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_reset_photo_categories' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_create_category_album' => 'SoloYLibre_Fullscreen_Wizard',
    'soloylibre_save_photo_categories' => 'SoloYLibre_Fullscreen_Wizard',
    
    // Bulk manager endpoints (should be working)
    'bulk_load_photos' => 'SoloYLibre_Bulk_Photo_Manager',
    'bulk_get_stats' => 'SoloYLibre_Bulk_Photo_Manager',
    'bulk_mark_private' => 'SoloYLibre_Bulk_Photo_Manager',
    'bulk_mark_unwanted' => 'SoloYLibre_Bulk_Photo_Manager',
    'bulk_create_albums' => 'SoloYLibre_Bulk_Photo_Manager',
    'bulk_publish_batch' => 'SoloYLibre_Bulk_Photo_Manager',
    'bulk_organize_dates' => 'SoloYLibre_Bulk_Photo_Manager',
    'bulk_test_connection' => 'SoloYLibre_Bulk_Photo_Manager',
    
    // Gallery endpoints (should be working)
    'load_more_photos' => 'SoloYLibre_Gallery_Plugin',
    'reset_published_photos' => 'SoloYLibre_Gallery_Plugin',
    'gallery_interaction' => 'SoloYLibre_Enhanced_Shortcode',
    'soloylibre_add_interaction' => 'SoloYLibre_Enhanced_Shortcode',
    
    // User interactions endpoints (should be working)
    'add_photo_reaction' => 'SoloYLibre_User_Interactions',
    'remove_photo_reaction' => 'SoloYLibre_User_Interactions',
    'get_photo_reactions' => 'SoloYLibre_User_Interactions',
    'generate_random_interactions' => 'SoloYLibre_User_Interactions',
    
    // Setup endpoints (should be working)
    'soloylibre_complete_setup' => 'SoloYLibre_Initial_Setup',
    'soloylibre_skip_setup' => 'SoloYLibre_Initial_Setup',
    
    // Settings endpoints (MISSING - need to implement)
    'soloylibre_save_settings' => 'MISSING',
    'soloylibre_reset_settings' => 'MISSING',
    'soloylibre_export_data' => 'MISSING',
    'soloylibre_import_data' => 'MISSING'
);

$working_endpoints = 0;
$missing_endpoints = 0;

foreach ($all_ajax_endpoints as $endpoint => $class) {
    echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
    echo "<strong>$endpoint</strong> → $class<br>";
    
    if (has_action("wp_ajax_$endpoint")) {
        echo "<span style='color: #28a745;'>✅ Registrado y funcionando</span>";
        $working_endpoints++;
        $fixes_applied[] = "✅ Endpoint funcionando: $endpoint";
    } else {
        echo "<span style='color: #dc3545;'>❌ NO registrado</span>";
        $missing_endpoints++;
        $errors_found[] = "❌ Endpoint faltante: $endpoint";
    }
    echo "</div>";
}

// 2. Add missing settings endpoints
echo "<h2>⚙️ 2. Implementando Endpoints de Configuraciones Faltantes</h2>";

// Create temporary AJAX handlers for missing settings endpoints
if (!has_action('wp_ajax_soloylibre_save_settings')) {
    add_action('wp_ajax_soloylibre_save_settings', function() {
        check_ajax_referer('soloylibre_settings_nonce', 'nonce');
        
        $settings = array();
        if (isset($_POST['settings'])) {
            $settings = $_POST['settings'];
            
            // Sanitize and save settings
            foreach ($settings as $key => $value) {
                $sanitized_key = sanitize_key($key);
                $sanitized_value = sanitize_text_field($value);
                update_option("soloylibre_$sanitized_key", $sanitized_value);
            }
            
            wp_send_json_success(array(
                'message' => 'Configuraciones guardadas correctamente',
                'settings_saved' => count($settings)
            ));
        } else {
            wp_send_json_error('No se recibieron configuraciones para guardar');
        }
    });
    $fixes_applied[] = "✅ Endpoint implementado: soloylibre_save_settings";
}

if (!has_action('wp_ajax_soloylibre_reset_settings')) {
    add_action('wp_ajax_soloylibre_reset_settings', function() {
        check_ajax_referer('soloylibre_settings_nonce', 'nonce');
        
        // Reset all plugin settings
        $plugin_options = array(
            'soloylibre_published_photos',
            'soloylibre_private_photos',
            'soloylibre_unwanted_photos',
            'soloylibre_deleted_photos',
            'soloylibre_plugin_version',
            'soloylibre_wizard_completed',
            'soloylibre_total_views',
            'soloylibre_total_likes',
            'soloylibre_total_shares'
        );
        
        $reset_count = 0;
        foreach ($plugin_options as $option) {
            if (delete_option($option)) {
                $reset_count++;
            }
        }
        
        wp_send_json_success(array(
            'message' => 'Configuraciones reseteadas correctamente',
            'options_reset' => $reset_count
        ));
    });
    $fixes_applied[] = "✅ Endpoint implementado: soloylibre_reset_settings";
}

if (!has_action('wp_ajax_soloylibre_export_data')) {
    add_action('wp_ajax_soloylibre_export_data', function() {
        check_ajax_referer('soloylibre_settings_nonce', 'nonce');
        
        global $wpdb;
        
        // Export plugin data
        $export_data = array(
            'plugin_version' => '2.0.0',
            'export_date' => current_time('mysql'),
            'photographer_info' => array(
                'name' => 'Jose L Encarnacion',
                'alias' => 'JoseTusabe',
                'brand' => 'JoseTusabe Photography',
                'email' => '<EMAIL>',
                'phone' => '************',
                'location' => 'San José de Ocoa, Dom. Rep. / USA'
            ),
            'settings' => array(),
            'statistics' => array(),
            'photo_categories' => array(
                'published' => get_option('soloylibre_published_photos', array()),
                'private' => get_option('soloylibre_private_photos', array()),
                'unwanted' => get_option('soloylibre_unwanted_photos', array()),
                'deleted' => get_option('soloylibre_deleted_photos', array())
            )
        );
        
        // Get interactions data
        $interactions_table = $wpdb->prefix . 'soloylibre_interactions';
        if ($wpdb->get_var("SHOW TABLES LIKE '$interactions_table'") == $interactions_table) {
            $interactions = $wpdb->get_results("SELECT * FROM $interactions_table LIMIT 1000");
            $export_data['interactions'] = $interactions;
        }
        
        wp_send_json_success(array(
            'message' => 'Datos exportados correctamente',
            'data' => $export_data,
            'filename' => 'soloylibre_export_' . date('Y-m-d_H-i-s') . '.json'
        ));
    });
    $fixes_applied[] = "✅ Endpoint implementado: soloylibre_export_data";
}

if (!has_action('wp_ajax_soloylibre_import_data')) {
    add_action('wp_ajax_soloylibre_import_data', function() {
        check_ajax_referer('soloylibre_settings_nonce', 'nonce');
        
        if (!isset($_POST['import_data'])) {
            wp_send_json_error('No se recibieron datos para importar');
            return;
        }
        
        $import_data = json_decode(stripslashes($_POST['import_data']), true);
        
        if (!$import_data) {
            wp_send_json_error('Datos de importación inválidos');
            return;
        }
        
        $imported_items = 0;
        
        // Import photo categories
        if (isset($import_data['photo_categories'])) {
            foreach ($import_data['photo_categories'] as $category => $photos) {
                update_option("soloylibre_{$category}_photos", $photos);
                $imported_items++;
            }
        }
        
        // Import settings
        if (isset($import_data['settings'])) {
            foreach ($import_data['settings'] as $key => $value) {
                update_option($key, $value);
                $imported_items++;
            }
        }
        
        wp_send_json_success(array(
            'message' => 'Datos importados correctamente',
            'items_imported' => $imported_items
        ));
    });
    $fixes_applied[] = "✅ Endpoint implementado: soloylibre_import_data";
}

// 3. Test all endpoints
echo "<h2>🧪 3. Probando Todos los Endpoints</h2>";

$final_working = 0;
$final_missing = 0;

foreach ($all_ajax_endpoints as $endpoint => $class) {
    if (has_action("wp_ajax_$endpoint")) {
        $final_working++;
    } else {
        $final_missing++;
    }
}

// 4. Statistics
echo "<h2>📊 4. Estadísticas Finales</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0;'>";

// Working Endpoints Card
echo "<div style='background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; padding: 25px; border-radius: 15px; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.15);'>";
echo "<div style='font-size: 48px; font-weight: bold; margin-bottom: 10px;'>$final_working</div>";
echo "<div style='font-size: 18px; margin-bottom: 5px;'>✅ Endpoints Funcionando</div>";
echo "<div style='font-size: 14px; opacity: 0.9;'>Completamente operativos</div>";
echo "</div>";

// Missing Endpoints Card
echo "<div style='background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: #333; padding: 25px; border-radius: 15px; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.15);'>";
echo "<div style='font-size: 48px; font-weight: bold; margin-bottom: 10px;'>$final_missing</div>";
echo "<div style='font-size: 18px; margin-bottom: 5px;'>❌ Endpoints Faltantes</div>";
echo "<div style='font-size: 14px; opacity: 0.8;'>Requieren implementación</div>";
echo "</div>";

// Success Rate Card
$success_rate = round(($final_working / count($all_ajax_endpoints)) * 100, 1);
echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.15);'>";
echo "<div style='font-size: 48px; font-weight: bold; margin-bottom: 10px;'>$success_rate%</div>";
echo "<div style='font-size: 18px; margin-bottom: 5px;'>📈 Tasa de Éxito</div>";
echo "<div style='font-size: 14px; opacity: 0.9;'>Endpoints funcionando</div>";
echo "</div>";

// Total Endpoints Card
$total_endpoints = count($all_ajax_endpoints);
echo "<div style='background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333; padding: 25px; border-radius: 15px; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.15);'>";
echo "<div style='font-size: 48px; font-weight: bold; margin-bottom: 10px;'>$total_endpoints</div>";
echo "<div style='font-size: 18px; margin-bottom: 5px;'>🔗 Total Endpoints</div>";
echo "<div style='font-size: 14px; opacity: 0.8;'>En el sistema</div>";
echo "</div>";

echo "</div>";

// 5. Show results
echo "<h2>✅ Reparaciones Aplicadas</h2>";
echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #28a745;'>";
echo "<ul>";
foreach ($fixes_applied as $fix) {
    echo "<li>$fix</li>";
}
echo "</ul>";
echo "</div>";

if (!empty($errors_found)) {
    echo "<h2>⚠️ Problemas Encontrados</h2>";
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #dc3545;'>";
    echo "<ul>";
    foreach ($errors_found as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// 6. Test links
echo "<h2>🔧 6. Probar Enlaces Reparados</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-wizard' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>🧙‍♂️ Probar Wizard</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-bulk-manager' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>📁 Probar Gestor Masivo</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-statistics' style='background: #fd7e14; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>📊 Probar Estadísticas</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-settings' style='background: #6f42c1; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>⚙️ Probar Configuraciones</a>";
echo "</div>";

// 7. Final message
echo "<div style='background: " . ($success_rate >= 80 ? '#d4edda' : '#fff3cd') . "; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h2 style='margin: 0 0 15px 0; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo ($success_rate >= 80 ? '🎉' : '⚠️') . " Reparación de Endpoints AJAX Completada</h2>";
echo "<p style='margin: 0; font-size: 18px; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo "Tasa de éxito: <strong>$success_rate%</strong> ($final_working de $total_endpoints endpoints funcionando)";
echo "</p>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Endpoints AJAX reparados</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
