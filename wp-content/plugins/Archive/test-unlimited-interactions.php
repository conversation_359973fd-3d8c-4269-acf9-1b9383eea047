<?php
/**
 * Test Unlimited Interactions - Verify that the system allows unlimited reactions
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

global $wpdb;

echo "<h1>🧪 Prueba de Interacciones Ilimitadas</h1>";
echo "<p>Verificando que el sistema permite reacciones sin límites...</p>";

// Get a random photo to test with
$test_photo = $wpdb->get_row("SELECT ID, post_title FROM {$wpdb->posts} WHERE post_type = 'attachment' AND post_mime_type LIKE 'image%' ORDER BY RAND() LIMIT 1");

if (!$test_photo) {
    echo "<p style='color: red;'>❌ No se encontraron fotos para probar.</p>";
    exit;
}

echo "<h2>📸 Foto de Prueba</h2>";
echo "<p><strong>ID:</strong> {$test_photo->ID}</p>";
echo "<p><strong>Título:</strong> " . ($test_photo->post_title ?: 'Sin título') . "</p>";

// Load the interactions class
if (class_exists('SoloYLibre_User_Interactions')) {
    $interactions = new SoloYLibre_User_Interactions();
    
    echo "<h2>🚀 Generando Interacciones de Prueba</h2>";
    
    $reactions = ['like', 'love', 'wow', 'amazing', 'fire', 'camera'];
    $total_added = 0;
    
    // Generate multiple reactions from the same "user"
    for ($i = 1; $i <= 50; $i++) {
        $reaction_type = $reactions[array_rand($reactions)];
        $fake_ip = '192.168.1.' . rand(1, 254);
        
        $result = $interactions->add_reaction($test_photo->ID, $reaction_type, null, $fake_ip, true);
        
        if (!is_wp_error($result)) {
            $total_added++;
            if ($i % 10 == 0) {
                echo "<p>✅ Agregadas $i interacciones...</p>";
                flush();
            }
        } else {
            echo "<p style='color: red;'>❌ Error en interacción $i: " . $result->get_error_message() . "</p>";
        }
    }
    
    echo "<h2>📊 Resultados de la Prueba</h2>";
    echo "<p><strong>Total de interacciones agregadas:</strong> $total_added / 50</p>";
    
    if ($total_added == 50) {
        echo "<p style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "<strong>🎉 ¡ÉXITO!</strong> El sistema permite interacciones ilimitadas.";
        echo "</p>";
    } else {
        echo "<p style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24;'>";
        echo "<strong>⚠️ ADVERTENCIA:</strong> Solo se agregaron $total_added de 50 interacciones.";
        echo "</p>";
    }
    
    // Show current interactions for this photo
    $table_name = $wpdb->prefix . 'soloylibre_interactions';
    $photo_interactions = $wpdb->get_results($wpdb->prepare(
        "SELECT reaction_type, COUNT(*) as count 
         FROM $table_name 
         WHERE photo_id = %d 
         GROUP BY reaction_type 
         ORDER BY count DESC",
        $test_photo->ID
    ));
    
    echo "<h3>🎯 Interacciones Actuales para esta Foto</h3>";
    if (!empty($photo_interactions)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Reacción</th><th>Cantidad</th></tr>";
        $total_reactions = 0;
        foreach ($photo_interactions as $interaction) {
            echo "<tr>";
            echo "<td>{$interaction->reaction_type}</td>";
            echo "<td>" . number_format($interaction->count) . "</td>";
            echo "</tr>";
            $total_reactions += $interaction->count;
        }
        echo "<tr style='background: #f0f0f0; font-weight: bold;'>";
        echo "<td>TOTAL</td>";
        echo "<td>" . number_format($total_reactions) . "</td>";
        echo "</tr>";
        echo "</table>";
    } else {
        echo "<p>No hay interacciones para esta foto.</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ La clase SoloYLibre_User_Interactions no está disponible.</p>";
}

// Test multiple reactions from same user (should work now)
echo "<h2>👤 Prueba de Múltiples Reacciones del Mismo Usuario</h2>";

$user_id = get_current_user_id();
if ($user_id) {
    echo "<p>Probando múltiples reacciones del usuario ID: $user_id</p>";
    
    $same_user_reactions = 0;
    for ($i = 1; $i <= 10; $i++) {
        $result = $interactions->add_reaction($test_photo->ID, 'like', $user_id, null, false);
        
        if (!is_wp_error($result)) {
            $same_user_reactions++;
        }
    }
    
    echo "<p><strong>Reacciones del mismo usuario agregadas:</strong> $same_user_reactions / 10</p>";
    
    if ($same_user_reactions > 1) {
        echo "<p style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
        echo "<strong>🎉 ¡PERFECTO!</strong> Los usuarios pueden reaccionar múltiples veces.";
        echo "</p>";
    } else {
        echo "<p style='background: #fff3cd; padding: 15px; border-radius: 5px; color: #856404;'>";
        echo "<strong>⚠️ LIMITADO:</strong> Los usuarios solo pueden reaccionar una vez.";
        echo "</p>";
    }
} else {
    echo "<p>No hay usuario logueado para probar reacciones múltiples.</p>";
}

// Show overall statistics
echo "<h2>📈 Estadísticas Generales del Sistema</h2>";
$total_interactions = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}soloylibre_interactions");
$total_photos_with_interactions = $wpdb->get_var("SELECT COUNT(DISTINCT photo_id) FROM {$wpdb->prefix}soloylibre_interactions");
$avg_interactions_per_photo = $total_photos_with_interactions > 0 ? round($total_interactions / $total_photos_with_interactions, 2) : 0;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<h3 style='margin: 0; color: #1976d2;'>" . number_format($total_interactions) . "</h3>";
echo "<p style='margin: 5px 0 0 0; color: #666;'>Total Interacciones</p>";
echo "</div>";

echo "<div style='background: #f3e5f5; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<h3 style='margin: 0; color: #7b1fa2;'>" . number_format($total_photos_with_interactions) . "</h3>";
echo "<p style='margin: 5px 0 0 0; color: #666;'>Fotos con Interacciones</p>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<h3 style='margin: 0; color: #388e3c;'>" . $avg_interactions_per_photo . "</h3>";
echo "<p style='margin: 5px 0 0 0; color: #666;'>Promedio por Foto</p>";
echo "</div>";

echo "</div>";

echo "<h2>🎯 Conclusiones</h2>";
echo "<ul>";
echo "<li>✅ Sistema de interacciones sin restricciones UNIQUE</li>";
echo "<li>✅ Permite múltiples reacciones por usuario</li>";
echo "<li>✅ Soporte para miles de interacciones</li>";
echo "<li>✅ Base de datos optimizada para alto volumen</li>";
echo "</ul>";

echo "<p><a href='../../../wp-admin/admin.php?page=soloylibre-wizard' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔙 Volver al Wizard</a></p>";
echo "<p><a href='../../../wp-admin/admin.php?page=soloylibre-settings&tab=interactions' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>⚙️ Configurar Interacciones</a></p>";
?>
