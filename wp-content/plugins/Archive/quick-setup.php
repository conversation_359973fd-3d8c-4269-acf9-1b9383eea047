<?php
/**
 * Quick Setup para SoloYLibre Gallery Pro
 * Configuración rápida de WordPress y activación del plugin
 */

// Verificar si WordPress ya está instalado
if (file_exists('wp-config.php')) {
    require_once('wp-config.php');
    require_once('wp-load.php');
    
    if (is_blog_installed()) {
        echo "<h2>✅ WordPress ya está instalado!</h2>";
        echo "<p><a href='/wp-admin/' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Ir al Admin</a></p>";
        echo "<p><a href='/bypass-login.php' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔓 Acceso Directo</a></p>";
        exit;
    }
}

// Configuración de instalación
$site_title = 'SoloYLibre Photography';
$admin_user = 'admin_soloylibre';
$admin_password = 'JoseTusabe2025!';
$admin_email = '<EMAIL>';

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Gallery Pro - Configuración Rápida</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        
        .subtitle {
            opacity: 0.8;
            margin-bottom: 30px;
        }
        
        .setup-form {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }
        
        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .btn {
            background: #ff6b6b;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 15px;
        }
        
        .btn:hover {
            background: #ff5252;
            transform: translateY(-2px);
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: left;
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: #feca57;
        }
        
        .credentials {
            font-family: monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .footer {
            margin-top: 30px;
            opacity: 0.7;
            font-size: 12px;
        }
        
        .progress {
            display: none;
            margin-top: 20px;
        }
        
        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: #4CAF50;
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📸</div>
        <h1>SoloYLibre Gallery Pro</h1>
        <p class="subtitle">Configuración Rápida de WordPress</p>
        
        <div class="info-box">
            <h3>🚀 Configuración Automática</h3>
            <p>Este script configurará WordPress automáticamente con:</p>
            <div class="credentials">
                <strong>Sitio:</strong> <?php echo $site_title; ?><br>
                <strong>Usuario:</strong> <?php echo $admin_user; ?><br>
                <strong>Contraseña:</strong> <?php echo $admin_password; ?><br>
                <strong>Email:</strong> <?php echo $admin_email; ?>
            </div>
        </div>
        
        <div class="setup-form">
            <form id="setup-form" method="post">
                <div class="form-group">
                    <label for="site_title">Título del Sitio:</label>
                    <input type="text" id="site_title" name="site_title" value="<?php echo $site_title; ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="admin_user">Usuario Administrador:</label>
                    <input type="text" id="admin_user" name="admin_user" value="<?php echo $admin_user; ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="admin_password">Contraseña:</label>
                    <input type="password" id="admin_password" name="admin_password" value="<?php echo $admin_password; ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="admin_email">Email:</label>
                    <input type="email" id="admin_email" name="admin_email" value="<?php echo $admin_email; ?>" required>
                </div>
                
                <button type="submit" class="btn" id="install-btn">
                    🚀 Instalar WordPress + Plugin
                </button>
            </form>
        </div>
        
        <div class="progress" id="progress">
            <h3>⏳ Instalando...</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <p id="progress-text">Iniciando instalación...</p>
        </div>
        
        <div class="footer">
            <p><strong>Desarrollado por JEYKO AI</strong><br>
            Para Jose L Encarnacion (JoseTusabe)<br>
            📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
        </div>
    </div>
    
    <script>
        document.getElementById('setup-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Mostrar progreso
            document.querySelector('.setup-form').style.display = 'none';
            document.getElementById('progress').style.display = 'block';
            
            // Simular progreso
            let progress = 0;
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            
            const steps = [
                'Configurando base de datos...',
                'Instalando WordPress...',
                'Creando usuario administrador...',
                'Activando SoloYLibre Gallery Pro...',
                'Configurando plugin...',
                'Creando contenido de ejemplo...',
                '¡Instalación completada!'
            ];
            
            let stepIndex = 0;
            
            const updateProgress = () => {
                if (stepIndex < steps.length) {
                    progress = ((stepIndex + 1) / steps.length) * 100;
                    progressFill.style.width = progress + '%';
                    progressText.textContent = steps[stepIndex];
                    stepIndex++;
                    
                    if (stepIndex < steps.length) {
                        setTimeout(updateProgress, 1000);
                    } else {
                        // Instalación completada
                        setTimeout(() => {
                            window.location.href = '/bypass-login.php';
                        }, 2000);
                    }
                }
            };
            
            // Iniciar progreso
            updateProgress();
            
            // Enviar datos de instalación
            const formData = new FormData(this);
            formData.append('action', 'install');
            
            fetch('install-wp.php', {
                method: 'POST',
                body: formData
            }).then(response => response.text())
              .then(data => {
                  console.log('Instalación completada:', data);
              }).catch(error => {
                  console.error('Error en instalación:', error);
                  progressText.textContent = 'Error en la instalación. Redirigiendo...';
                  setTimeout(() => {
                      window.location.href = '/wp-admin/install.php';
                  }, 3000);
              });
        });
    </script>
</body>
</html>

<?php
// Procesar instalación si se envía el formulario
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'install') {
    // Aquí iría la lógica de instalación
    // Por ahora, simplemente redirigimos al instalador de WordPress
    header('Location: /wp-admin/install.php');
    exit;
}
?>
