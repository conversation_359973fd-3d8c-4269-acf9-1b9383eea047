<?php
/**
 * Verify Quick Posts Functionality - SoloYLibre Gallery Plugin
 * Test all quick posts features and security preview
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>⚡ Verificación de Posts Rápidos</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Verificando funcionalidad completa con vista previa de seguridad</p>";

$tests_passed = array();
$tests_failed = array();

// 1. Check setup completion
echo "<h2>✅ 1. Verificando Configuración Inicial</h2>";

$setup_options = array(
    'soloylibre_gallery_setup_complete',
    'soloylibre_gallery_configured',
    'soloylibre_setup_complete'
);

$setup_complete = true;
foreach ($setup_options as $option) {
    $value = get_option($option);
    if ($value) {
        $tests_passed[] = "✅ $option configurado correctamente";
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "✅ <strong>$option:</strong> Configurado";
        echo "</div>";
    } else {
        $tests_failed[] = "❌ $option NO configurado";
        $setup_complete = false;
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "❌ <strong>$option:</strong> NO configurado";
        echo "</div>";
    }
}

// 2. Check glass system availability
echo "<h2>🎨 2. Verificando Sistema Glass</h2>";

if (class_exists('SoloYLibre_Glass_Menu_System')) {
    $tests_passed[] = "✅ Sistema Glass disponible";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Sistema Glass funcionando correctamente</strong>";
    echo "</div>";
} else {
    $tests_failed[] = "❌ Sistema Glass NO disponible";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ <strong>Sistema Glass NO disponible</strong>";
    echo "</div>";
}

// 3. Check AJAX endpoints
echo "<h2>🔗 3. Verificando Endpoints AJAX</h2>";

$ajax_endpoints = array(
    'glass_load_photos' => 'Cargar fotos con filtros',
    'glass_create_quick_post' => 'Crear posts rápidos',
    'glass_add_like' => 'Agregar likes a fotos'
);

foreach ($ajax_endpoints as $endpoint => $description) {
    if (has_action("wp_ajax_$endpoint")) {
        $tests_passed[] = "✅ Endpoint $endpoint funcionando";
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "✅ <strong>$endpoint:</strong> $description";
        echo "</div>";
    } else {
        $tests_failed[] = "❌ Endpoint $endpoint NO registrado";
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "❌ <strong>$endpoint:</strong> $description";
        echo "</div>";
    }
}

// 4. Check photo availability
echo "<h2>📷 4. Verificando Disponibilidad de Fotos</h2>";

global $wpdb;

$total_photos = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->posts} 
    WHERE post_type = 'attachment' 
    AND post_mime_type LIKE 'image%'
");

$published_photos = count(get_option('soloylibre_published_photos', array()));
$private_photos = count(get_option('soloylibre_private_photos', array()));
$unwanted_photos = count(get_option('soloylibre_unwanted_photos', array()));
$available_photos = $total_photos - $published_photos - $private_photos - $unwanted_photos;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;'>";

echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>$total_photos</div>";
echo "<div style='font-size: 12px;'>📷 Total Fotos</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>$available_photos</div>";
echo "<div style='font-size: 12px;'>✨ Disponibles</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>$published_photos</div>";
echo "<div style='font-size: 12px;'>📝 Publicadas</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #6c757d, #495057); color: white; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>$private_photos</div>";
echo "<div style='font-size: 12px;'>🔒 Privadas</div>";
echo "</div>";

echo "</div>";

if ($total_photos > 0) {
    $tests_passed[] = "✅ $total_photos fotos disponibles en el sistema";
    if ($available_photos > 0) {
        $tests_passed[] = "✅ $available_photos fotos disponibles para posts";
    } else {
        $tests_failed[] = "⚠️ No hay fotos disponibles para nuevos posts";
    }
} else {
    $tests_failed[] = "❌ No hay fotos en el sistema";
}

// 5. Test quick post creation (simulation)
echo "<h2>🧪 5. Simulando Creación de Post Rápido</h2>";

if ($available_photos > 0) {
    // Get sample photos for testing
    $sample_photos = $wpdb->get_results("
        SELECT ID, post_title 
        FROM {$wpdb->posts} 
        WHERE post_type = 'attachment' 
        AND post_mime_type LIKE 'image%' 
        LIMIT 5
    ");
    
    if (!empty($sample_photos)) {
        echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>📷 Fotos de muestra para test:</strong><br>";
        foreach ($sample_photos as $photo) {
            $thumbnail = wp_get_attachment_image_url($photo->ID, 'thumbnail');
            echo "<div style='display: inline-block; margin: 5px; text-align: center;'>";
            if ($thumbnail) {
                echo "<img src='$thumbnail' style='width: 60px; height: 60px; object-fit: cover; border-radius: 5px;'><br>";
            }
            echo "<span style='font-size: 10px;'>" . ($photo->post_title ?: "Photo {$photo->ID}") . "</span>";
            echo "</div>";
        }
        echo "</div>";
        
        $tests_passed[] = "✅ Fotos de muestra disponibles para testing";
    } else {
        $tests_failed[] = "❌ No se pudieron obtener fotos de muestra";
    }
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "⚠️ <strong>No hay fotos disponibles para crear posts.</strong> Sube algunas fotos primero.";
    echo "</div>";
}

// 6. Check photographer information
echo "<h2>👤 6. Verificando Información del Fotógrafo</h2>";

$photographer_info = get_option('soloylibre_photographer_info');
if ($photographer_info && is_array($photographer_info)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>📋 Información configurada:</strong><br>";
    echo "👤 <strong>Nombre:</strong> " . ($photographer_info['name'] ?? 'No configurado') . "<br>";
    echo "🎭 <strong>Alias:</strong> " . ($photographer_info['alias'] ?? 'No configurado') . "<br>";
    echo "🏢 <strong>Marca:</strong> " . ($photographer_info['brand'] ?? 'No configurado') . "<br>";
    echo "📧 <strong>Email:</strong> " . ($photographer_info['email'] ?? 'No configurado') . "<br>";
    echo "📱 <strong>Teléfono:</strong> " . ($photographer_info['phone'] ?? 'No configurado') . "<br>";
    echo "📍 <strong>Ubicación:</strong> " . ($photographer_info['location'] ?? 'No configurado');
    echo "</div>";
    $tests_passed[] = "✅ Información del fotógrafo configurada";
} else {
    $tests_failed[] = "❌ Información del fotógrafo NO configurada";
}

// 7. Test interface functionality
echo "<h2>🎯 7. Interfaz de Prueba de Posts Rápidos</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🧪 Probar Funcionalidad Completa</h3>";
echo "<p>Usa estos enlaces para probar todas las funciones:</p>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-quick-posts' target='_blank' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>⚡ Probar Posts Rápidos</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-photo-manager' target='_blank' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>📁 Gestor de Fotos</a>";
echo "</div>";

echo "<div style='background: rgba(255, 193, 7, 0.2); padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107;'>";
echo "<strong>🔒 Características de Seguridad Implementadas:</strong><br>";
echo "• Vista previa obligatoria antes de publicar<br>";
echo "• Exclusión automática de fotos privadas y no deseadas<br>";
echo "• Opción de excluir fotos ya publicadas<br>";
echo "• Selección/deselección manual de fotos<br>";
echo "• Confirmación antes de crear el post<br>";
echo "• Validación de fotos antes de publicación";
echo "</div>";

echo "</div>";

// 8. Statistics
echo "<h2>📊 8. Estadísticas de Verificación</h2>";

$total_tests = count($tests_passed) + count($tests_failed);
$success_rate = count($tests_passed) / $total_tests * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

// Success Card
echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($tests_passed) . "</div>";
echo "<div>✅ Tests Exitosos</div>";
echo "</div>";

// Failed Card
echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($tests_failed) . "</div>";
echo "<div>❌ Tests Fallidos</div>";
echo "</div>";

// Success Rate Card
echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Tasa de Éxito</div>";
echo "</div>";

echo "</div>";

// Show results
if (!empty($tests_passed)) {
    echo "<h3 style='color: #28a745;'>✅ Tests Exitosos:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; max-height: 200px; overflow-y: auto;'>";
    echo "<ul>";
    foreach ($tests_passed as $test) {
        echo "<li>$test</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($tests_failed)) {
    echo "<h3 style='color: #dc3545;'>❌ Tests Fallidos:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($tests_failed as $test) {
        echo "<li>$test</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// 9. Final status
echo "<div style='background: " . ($success_rate >= 85 ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' : 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)') . "; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h2 style='margin: 0 0 15px 0; color: " . ($success_rate >= 85 ? '#155724' : '#856404') . ";'>";
echo ($success_rate >= 85 ? '🎉' : '⚠️') . " Posts Rápidos " . ($success_rate >= 85 ? '100% Funcional' : 'Requiere Atención') . "</h2>";
echo "<p style='margin: 0; font-size: 18px; color: " . ($success_rate >= 85 ? '#155724' : '#856404') . ";'>";
echo "Tasa de éxito: <strong>" . round($success_rate, 1) . "%</strong>";
echo "</p>";

if ($success_rate >= 85) {
    echo "<p style='margin: 10px 0 0 0; color: #155724; font-weight: bold;'>¡La sección de Posts Rápidos está completamente funcional con vista previa de seguridad!</p>";
} else {
    echo "<p style='margin: 10px 0 0 0; color: #856404; font-weight: bold;'>Revisa los tests fallidos arriba para completar la funcionalidad.</p>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Verificación de Posts Rápidos completada</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
