<?php
/**
 * Verificador de Instalación - SoloYLibre Gallery Pro v2.0.0
 * Para http://localhost:8888/wp/wordpress/
 * <PERSON> (JoseTusabe)
 */

echo "<h1>🔍 Verificador de Instalación - SoloYLibre Gallery Pro v2.0.0</h1>";

// Configuración para localhost:8888
$WORDPRESS_URL = 'http://localhost:8888/wp/wordpress/';
$ADMIN_URL = $WORDPRESS_URL . 'wp-admin/';

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h2>🎯 Información de la Instalación</h2>";
echo "<p><strong>WordPress URL:</strong> <a href='$WORDPRESS_URL' target='_blank'>$WORDPRESS_URL</a></p>";
echo "<p><strong>Admin URL:</strong> <a href='$ADMIN_URL' target='_blank'>$ADMIN_URL</a></p>";
echo "<p><strong>Fotógrafo:</strong> Jose L Encarnacion (JoseTusabe)</p>";
echo "<p><strong>Marca:</strong> SoloYLibre Photography</p>";
echo "</div>";

echo "<h2>📋 Verificación de Archivos del Plugin</h2>";

// Archivos principales a verificar
$required_files = array(
    'soloylibre-gallery-plugin.php' => 'Archivo principal del plugin',
    'includes/class-admin.php' => 'Clase de administración',
    'includes/class-dashboard.php' => 'Dashboard principal',
    'includes/class-photo-wizard.php' => 'Asistente de fotos',
    'includes/class-bulk-photo-loader.php' => 'Cargador masivo',
    'includes/class-api-manager.php' => 'Gestor de API',
    'includes/class-auth-manager.php' => 'Gestor de autenticación',
    'assets/css/admin.css' => 'Estilos de administración',
    'assets/js/admin.js' => 'JavaScript de administración',
    'README.md' => 'Documentación principal'
);

$files_status = array();
$missing_files = array();
$existing_files = array();

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        $files_status[$file] = true;
        $existing_files[] = $file;
        echo "<p>✅ <strong>$description:</strong> <code>$file</code></p>";
    } else {
        $files_status[$file] = false;
        $missing_files[] = $file;
        echo "<p>❌ <strong>Falta:</strong> <code>$file</code> - $description</p>";
    }
}

// Resumen de archivos
echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; margin: 20px 0;'>";
echo "<h3>📊 Resumen de Archivos</h3>";
echo "<p><strong>Archivos encontrados:</strong> " . count($existing_files) . "/" . count($required_files) . "</p>";
echo "<p><strong>Archivos faltantes:</strong> " . count($missing_files) . "</p>";

if (count($missing_files) > 0) {
    echo "<p><strong>⚠️ Archivos que faltan:</strong></p>";
    echo "<ul>";
    foreach ($missing_files as $file) {
        echo "<li><code>$file</code></li>";
    }
    echo "</ul>";
}
echo "</div>";

echo "<h2>📁 Instrucciones de Instalación</h2>";

if (count($missing_files) > 0) {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
    echo "<h3>⚠️ Instalación Incompleta</h3>";
    echo "<p>Algunos archivos del plugin no se encontraron. Para completar la instalación:</p>";
    echo "<ol>";
    echo "<li><strong>Copia todos los archivos</strong> del plugin al directorio de WordPress</li>";
    echo "<li><strong>Ruta de destino:</strong> <code>/Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/soloylibre-gallery-pro/</code></li>";
    echo "<li><strong>Verifica permisos</strong> de escritura en el directorio</li>";
    echo "<li><strong>Activa el plugin</strong> desde WordPress Admin</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
    echo "<h3>✅ Archivos Completos</h3>";
    echo "<p>Todos los archivos del plugin están presentes. Ahora puedes proceder con la activación.</p>";
    echo "</div>";
}

echo "<h2>🔧 Comandos de Terminal</h2>";

echo "<div style='background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 8px; margin: 20px 0; font-family: Monaco, monospace;'>";
echo "<h3 style='color: #81c784; margin-top: 0;'>📁 Copiar Plugin Completo</h3>";
echo "<pre style='margin: 0; white-space: pre-wrap;'>";
echo "# Desde el directorio donde tienes el plugin:\n";
echo "cd /ruta/donde/tienes/soloylibre-gallery-pro\n\n";
echo "# Copiar todo el plugin a WordPress:\n";
echo "cp -r . /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/soloylibre-gallery-pro/\n\n";
echo "# Verificar que se copió correctamente:\n";
echo "ls -la /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/soloylibre-gallery-pro/\n\n";
echo "# Dar permisos correctos:\n";
echo "chmod -R 755 /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/soloylibre-gallery-pro/";
echo "</pre>";
echo "</div>";

echo "<h2>🌐 Enlaces de Acceso Directo</h2>";

$access_links = array(
    '🏠 WordPress Admin' => $ADMIN_URL,
    '🔌 Gestión de Plugins' => $ADMIN_URL . 'plugins.php',
    '📊 Dashboard SoloYLibre' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-dashboard',
    '🧙‍♂️ Asistente de Fotos' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-wizard',
    '📸 Gestión de Fotos' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-photos',
    '📁 Álbumes' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-albums',
    '📈 Analytics' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-analytics',
    '⚙️ Configuración' => $ADMIN_URL . 'admin.php?page=soloylibre-gallery-settings'
);

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 15px; margin: 20px 0;'>";
foreach ($access_links as $title => $url) {
    echo "<a href='$url' target='_blank' style='background: rgba(255,255,255,0.9); padding: 15px; border-radius: 8px; text-decoration: none; color: #333; border-left: 4px solid #667eea; transition: all 0.3s ease; display: block; text-align: center;' onmouseover='this.style.transform=\"translateY(-2px)\"; this.style.boxShadow=\"0 4px 12px rgba(0,0,0,0.1)\"' onmouseout='this.style.transform=\"translateY(0)\"; this.style.boxShadow=\"none\"'>";
    echo "<strong>$title</strong>";
    echo "</a>";
}
echo "</div>";

echo "<h2>🔐 Credenciales del Sistema</h2>";

echo "<div style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 12px; margin: 20px 0;'>";
echo "<h3 style='margin: 0 0 20px 0;'>👤 Usuario Administrador Automático</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;'>";
echo "<div>";
echo "<p><strong>Usuario:</strong> admin_soloylibre</p>";
echo "<p><strong>Contraseña:</strong> JoseTusabe2025!</p>";
echo "</div>";
echo "<div>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Rol:</strong> Administrador</p>";
echo "</div>";
echo "</div>";
echo "<p style='margin: 0; font-size: 14px; opacity: 0.9;'>Este usuario se crea automáticamente cuando activas el plugin.</p>";
echo "</div>";

echo "<h2>📸 Información del Fotógrafo</h2>";

echo "<div style='background: rgba(255,255,255,0.9); padding: 25px; border-radius: 12px; border-left: 4px solid #764ba2; margin: 20px 0;'>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 25px;'>";

echo "<div>";
echo "<h3 style='margin: 0 0 15px 0; color: #333;'>👤 Datos Personales</h3>";
echo "<p><strong>Nombre:</strong> Jose L Encarnacion</p>";
echo "<p><strong>Alias:</strong> JoseTusabe</p>";
echo "<p><strong>Marca:</strong> SoloYLibre Photography</p>";
echo "<p><strong>Ubicación:</strong> San José de Ocoa, Dom. Rep. / USA</p>";
echo "</div>";

echo "<div>";
echo "<h3 style='margin: 0 0 15px 0; color: #333;'>📞 Contacto</h3>";
echo "<p><strong>Teléfono:</strong> ************</p>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Servidor:</strong> Synology RS3618xs</p>";
echo "<p><strong>Recursos:</strong> 56GB RAM, 36TB Storage</p>";
echo "</div>";

echo "<div>";
echo "<h3 style='margin: 0 0 15px 0; color: #333;'>🌐 Sitios Web</h3>";
echo "<p>• josetusabe.com</p>";
echo "<p>• soloylibre.com</p>";
echo "<p>• 1and1photo.com</p>";
echo "<p>• joselencarnacion.com</p>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<h2>🚀 Próximos Pasos</h2>";

if (count($missing_files) > 0) {
    echo "<div style='background: #fff3cd; padding: 25px; border-radius: 12px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
    echo "<h3 style='margin: 0 0 20px 0; color: #856404;'>📋 Lista de Tareas Pendientes</h3>";
    echo "<ol style='margin: 0; padding-left: 20px;'>";
    echo "<li style='margin: 10px 0;'>❌ <strong>Copiar archivos faltantes</strong> al directorio de WordPress</li>";
    echo "<li style='margin: 10px 0;'>⏳ <strong>Activar el plugin</strong> desde WordPress Admin</li>";
    echo "<li style='margin: 10px 0;'>⏳ <strong>Verificar usuario</strong> admin_soloylibre creado</li>";
    echo "<li style='margin: 10px 0;'>⏳ <strong>Acceder al Dashboard</strong> SoloYLibre</li>";
    echo "<li style='margin: 10px 0;'>⏳ <strong>Usar el Asistente</strong> para gestionar fotos</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #e8f5e8; padding: 25px; border-radius: 12px; border-left: 4px solid #28a745; margin: 20px 0;'>";
    echo "<h3 style='margin: 0 0 20px 0; color: #155724;'>✅ Lista de Verificación</h3>";
    echo "<ol style='margin: 0; padding-left: 20px;'>";
    echo "<li style='margin: 10px 0;'>✅ <strong>Archivos del plugin</strong> verificados y completos</li>";
    echo "<li style='margin: 10px 0;'>🔄 <strong>Activar el plugin</strong> desde WordPress Admin</li>";
    echo "<li style='margin: 10px 0;'>👤 <strong>Verificar usuario</strong> admin_soloylibre creado</li>";
    echo "<li style='margin: 10px 0;'>📊 <strong>Acceder al Dashboard</strong> SoloYLibre</li>";
    echo "<li style='margin: 10px 0;'>🧙‍♂️ <strong>Usar el Asistente</strong> para gestionar fotos</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='$ADMIN_URL" . "plugins.php' target='_blank' style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px 40px; text-decoration: none; border-radius: 12px; font-weight: bold; font-size: 18px; display: inline-block; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);' onmouseover='this.style.transform=\"translateY(-2px)\"; this.style.boxShadow=\"0 6px 20px rgba(102, 126, 234, 0.6)\"' onmouseout='this.style.transform=\"translateY(0)\"; this.style.boxShadow=\"0 4px 15px rgba(102, 126, 234, 0.4)\"'>";
echo "🔌 Ir a WordPress Plugins";
echo "</a>";
echo "</div>";

echo "<hr style='margin: 40px 0; border: none; border-top: 2px solid #ddd;'>";
echo "<div style='text-align: center; color: #666;'>";
echo "<p><em>🎨 Desarrollado por JEYKO AI para Jose L Encarnacion (JoseTusabe)</em></p>";
echo "<p><em>📸 SoloYLibre Photography - San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸</em></p>";
echo "<p><em>🚀 Plugin Version 2.0.0 - Verificado para localhost:8888</em></p>";
echo "</div>";

// CSS para mejorar la apariencia
echo "<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 0;
    padding: 40px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

h1 { 
    text-align: center;
    color: #333;
    font-size: 32px;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

h2 { 
    color: #333; 
    margin-top: 40px;
    font-size: 24px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

h3 {
    color: #333;
    margin-top: 20px;
    font-size: 18px;
}

p { 
    margin: 10px 0; 
}

a { 
    color: #667eea; 
    text-decoration: none; 
    transition: all 0.3s ease;
}

a:hover { 
    color: #764ba2;
}

code {
    background: rgba(0,0,0,0.1);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 14px;
}

ul, ol { 
    margin: 15px 0; 
    padding-left: 25px;
}

li {
    margin: 8px 0;
}

pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.5;
}

strong {
    color: #333;
}
</style>";
?>
