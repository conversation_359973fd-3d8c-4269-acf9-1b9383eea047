<?php
/**
 * <PERSON>ript para activar SoloYLibre Gallery Pro y crear usuario de prueba
 * Desarrollado por JEYKO AI para <PERSON> (JoseTusabe)
 */

// Cargar WordPress
require_once('wp-config.php');
require_once('wp-load.php');
require_once('wp-admin/includes/plugin.php');
require_once('wp-admin/includes/user.php');

echo "🚀 Activando SoloYLibre Gallery Pro...\n\n";

// Activar el plugin
$plugin_file = 'soloylibre-gallery-pro/soloylibre-gallery-plugin.php';

if (!is_plugin_active($plugin_file)) {
    $result = activate_plugin($plugin_file);
    
    if (is_wp_error($result)) {
        echo "❌ Error activando plugin: " . $result->get_error_message() . "\n";
    } else {
        echo "✅ Plugin SoloYLibre Gallery Pro activado exitosamente!\n\n";
    }
} else {
    echo "✅ Plugin ya está activo!\n\n";
}

// Crear usuario de prueba si no existe
$username = 'admin_soloylibre';
$password = 'JoseTusabe2025!';
$email = '<EMAIL>';

$user_id = username_exists($username);

if (!$user_id) {
    $user_id = wp_create_user($username, $password, $email);
    
    if (is_wp_error($user_id)) {
        echo "❌ Error creando usuario: " . $user_id->get_error_message() . "\n";
    } else {
        // Hacer al usuario administrador
        $user = new WP_User($user_id);
        $user->set_role('administrator');
        
        echo "✅ Usuario administrador creado:\n";
        echo "   👤 Usuario: $username\n";
        echo "   🔑 Contraseña: $password\n";
        echo "   📧 Email: $email\n\n";
    }
} else {
    echo "✅ Usuario administrador ya existe:\n";
    echo "   👤 Usuario: $username\n";
    echo "   🔑 Contraseña: $password\n\n";
}

// Crear algunas fotos de ejemplo
echo "📸 Creando fotos de ejemplo...\n";

$sample_photos = array(
    array(
        'title' => 'Atardecer en San José de Ocoa',
        'description' => 'Hermoso atardecer capturado desde las montañas de San José de Ocoa, República Dominicana.',
        'state' => 'public'
    ),
    array(
        'title' => 'Paisaje Dominicano',
        'description' => 'Vista panorámica de los valles verdes de la región central.',
        'state' => 'public'
    ),
    array(
        'title' => 'Fotografía Urbana NYC',
        'description' => 'Captura urbana desde mi nueva vida en Estados Unidos.',
        'state' => 'private'
    ),
    array(
        'title' => 'Retrato Personal',
        'description' => 'Sesión de fotos personal - solo para mis ojos.',
        'state' => 'personal'
    ),
    array(
        'title' => 'Foto de Prueba',
        'description' => 'Foto de prueba técnica - para eliminar.',
        'state' => 'trash'
    )
);

foreach ($sample_photos as $photo_data) {
    $post_data = array(
        'post_title' => $photo_data['title'],
        'post_content' => $photo_data['description'],
        'post_status' => 'publish',
        'post_type' => 'soloylibre_photo',
        'post_author' => $user_id
    );
    
    $photo_id = wp_insert_post($post_data);
    
    if ($photo_id && !is_wp_error($photo_id)) {
        // Establecer estado de la foto
        update_post_meta($photo_id, '_soloylibre_photo_state', $photo_data['state']);
        
        // Marcar como revisada y publicable si es pública
        if ($photo_data['state'] === 'public') {
            update_post_meta($photo_id, '_soloylibre_is_publishable', '1');
            update_post_meta($photo_id, '_soloylibre_is_reviewed', '1');
        }
        
        // Añadir algunas reacciones aleatorias
        $reactions_count = rand(5, 25);
        update_post_meta($photo_id, '_soloylibre_total_reactions', $reactions_count);
        update_post_meta($photo_id, '_soloylibre_reactions_like', rand(1, 10));
        update_post_meta($photo_id, '_soloylibre_reactions_love', rand(0, 5));
        update_post_meta($photo_id, '_soloylibre_reactions_wow', rand(0, 3));
        
        echo "   ✅ Creada: " . $photo_data['title'] . " (Estado: " . $photo_data['state'] . ")\n";
    }
}

echo "\n🎉 ¡Configuración completada!\n\n";

// Información de acceso
echo "🌐 INFORMACIÓN DE ACCESO:\n";
echo "================================\n";
echo "🏠 URL del sitio: http://localhost:8080\n";
echo "🔧 Admin: http://localhost:8080/wp-admin\n";
echo "👤 Usuario: admin_soloylibre\n";
echo "🔑 Contraseña: JoseTusabe2025!\n";
echo "📧 Email: <EMAIL>\n\n";

echo "📱 ACCESOS DIRECTOS AL PLUGIN:\n";
echo "================================\n";
echo "📊 Dashboard Moderno: http://localhost:8080/wp-admin/admin.php?page=soloylibre-gallery-dashboard\n";
echo "📸 Gestión de Fotos: http://localhost:8080/wp-admin/edit.php?post_type=soloylibre_photo\n";
echo "⚙️ Configuración: http://localhost:8080/wp-admin/admin.php?page=soloylibre-gallery-settings\n";
echo "📁 Álbumes: http://localhost:8080/wp-admin/admin.php?page=soloylibre-gallery-albums\n\n";

// Crear página de demostración
$demo_page = array(
    'post_title' => 'Galería SoloYLibre - Demo',
    'post_content' => '
<h2>🎨 Galería Estilo TikTok</h2>
[soloylibre_gallery style="tiktok" limit="10"]

<h2>🖼️ Galería Grid Profesional</h2>
[soloylibre_gallery style="grid" limit="12"]

<h2>🧱 Galería Masonry</h2>
[soloylibre_gallery style="masonry" limit="15"]

<p><strong>Desarrollado por JEYKO AI para Jose L Encarnacion (JoseTusabe)</strong><br>
📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸<br>
📞 718-713-5500 | 📧 <EMAIL></p>
    ',
    'post_status' => 'publish',
    'post_type' => 'page',
    'post_author' => $user_id
);

$demo_page_id = wp_insert_post($demo_page);

if ($demo_page_id && !is_wp_error($demo_page_id)) {
    echo "🎨 Página de demostración creada: http://localhost:8080/?page_id=$demo_page_id\n\n";
}

echo "🚀 ¡Todo listo para probar SoloYLibre Gallery Pro!\n";
echo "💡 Tip: Usa el Dashboard Moderno para la mejor experiencia.\n\n";
?>
