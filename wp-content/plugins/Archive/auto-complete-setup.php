<?php
/**
 * Auto Complete Setup - SoloYLibre Gallery Plugin
 * Automatically complete initial setup to remove configuration notices
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🚀 Completando Configuración Automáticamente</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Eliminando mensajes de configuración inicial</p>";

$fixes_applied = array();
$errors_found = array();

// 1. Mark setup as complete
echo "<h2>✅ 1. Marcando Setup como Completado</h2>";

$setup_options = array(
    'soloylibre_gallery_setup_complete' => true,
    'soloylibre_gallery_configured' => true,
    'soloylibre_setup_complete' => true,
    'soloylibre_gallery_setup_date' => current_time('mysql'),
    'soloylibre_gallery_version' => '2.0.0'
);

foreach ($setup_options as $option => $value) {
    if (update_option($option, $value)) {
        $fixes_applied[] = "✅ Opción configurada: $option";
    } else {
        $errors_found[] = "❌ Error configurando: $option";
    }
}

// 2. Set photographer information
echo "<h2>👤 2. Configurando Información del Fotógrafo</h2>";

$photographer_info = array(
    'name' => 'Jose L Encarnacion',
    'alias' => 'JoseTusabe',
    'brand' => 'JoseTusabe Photography',
    'email' => '<EMAIL>',
    'phone' => '************',
    'location' => 'San José de Ocoa, Dom. Rep. / USA',
    'websites' => array(
        'josetusabe.com',
        'soloylibre.com',
        '1and1photo.com',
        'joselencarnacion.com'
    ),
    'server_info' => array(
        'model' => 'Synology RS3618xs',
        'memory' => '56GB RAM',
        'storage' => '36TB'
    )
);

$photographer_options = array(
    'soloylibre_photographer_info' => $photographer_info,
    'soloylibre_gallery_photographer_name' => $photographer_info['name'],
    'soloylibre_gallery_photographer_brand' => $photographer_info['brand'],
    'soloylibre_gallery_photographer_email' => $photographer_info['email'],
    'soloylibre_gallery_photographer_phone' => $photographer_info['phone'],
    'soloylibre_gallery_photographer_location' => $photographer_info['location'],
    'soloylibre_gallery_photographer_websites' => $photographer_info['websites']
);

foreach ($photographer_options as $option => $value) {
    if (update_option($option, $value)) {
        $fixes_applied[] = "✅ Información del fotógrafo: $option";
    } else {
        $errors_found[] = "❌ Error configurando información: $option";
    }
}

// 3. Set default gallery settings
echo "<h2>🎨 3. Configurando Ajustes de Galería</h2>";

$gallery_settings = array(
    'soloylibre_gallery_style' => 'masonry',
    'soloylibre_gallery_photos_per_page' => 50,
    'soloylibre_gallery_enable_infinite_scroll' => true,
    'soloylibre_gallery_enable_lightbox' => true,
    'soloylibre_gallery_show_photographer_info' => true,
    'soloylibre_gallery_dominican_theme' => true,
    'soloylibre_gallery_require_membership' => false,
    'soloylibre_gallery_enable_interactions' => true,
    'soloylibre_gallery_enable_likes' => true,
    'soloylibre_gallery_enable_shares' => true,
    'soloylibre_gallery_enable_comments' => false
);

foreach ($gallery_settings as $option => $value) {
    if (update_option($option, $value)) {
        $fixes_applied[] = "✅ Configuración de galería: $option";
    } else {
        $errors_found[] = "❌ Error en configuración: $option";
    }
}

// 4. Initialize plugin statistics
echo "<h2>📊 4. Inicializando Estadísticas del Plugin</h2>";

$stats_options = array(
    'soloylibre_published_photos' => array(),
    'soloylibre_private_photos' => array(),
    'soloylibre_unwanted_photos' => array(),
    'soloylibre_deleted_photos' => array(),
    'soloylibre_total_posts_created' => 0,
    'soloylibre_total_views' => 0,
    'soloylibre_total_likes' => 0,
    'soloylibre_total_shares' => 0,
    'soloylibre_plugin_activated_date' => current_time('mysql'),
    'soloylibre_last_activity' => current_time('mysql')
);

foreach ($stats_options as $option => $value) {
    if (get_option($option) === false) {
        if (add_option($option, $value)) {
            $fixes_applied[] = "✅ Estadística inicializada: $option";
        } else {
            $errors_found[] = "❌ Error inicializando: $option";
        }
    } else {
        $fixes_applied[] = "✅ Estadística ya existe: $option";
    }
}

// 5. Create default categories and tags
echo "<h2>🏷️ 5. Creando Categorías y Tags por Defecto</h2>";

// Create default categories
$default_categories = array(
    'Fotografía Profesional' => 'Trabajos profesionales de JoseTusabe Photography',
    'República Dominicana' => 'Fotos de la hermosa República Dominicana',
    'San José de Ocoa' => 'Mi ciudad natal en República Dominicana',
    'Portfolio' => 'Selección de mejores trabajos fotográficos',
    'Eventos' => 'Fotografía de eventos y celebraciones',
    'Naturaleza' => 'Paisajes y naturaleza dominicana'
);

foreach ($default_categories as $cat_name => $cat_description) {
    $cat_id = wp_create_category($cat_name);
    if ($cat_id && !is_wp_error($cat_id)) {
        // Update category description
        wp_update_term($cat_id, 'category', array('description' => $cat_description));
        $fixes_applied[] = "✅ Categoría creada: $cat_name";
    }
}

// Create default tags
$default_tags = array(
    'dominicana', 'republica-dominicana', 'caribe', 'tropical',
    'san-jose-de-ocoa', 'josetusabe', 'soloylibre', 'fotografia-profesional',
    'photography', 'dominican-republic', 'caribbean', 'professional-photographer'
);

foreach ($default_tags as $tag) {
    $tag_obj = wp_insert_term($tag, 'post_tag');
    if (!is_wp_error($tag_obj)) {
        $fixes_applied[] = "✅ Tag creado: $tag";
    }
}

// 6. Ensure database tables exist
echo "<h2>🗄️ 6. Verificando Tablas de Base de Datos</h2>";

global $wpdb;

// Create interactions table if it doesn't exist
$interactions_table = $wpdb->prefix . 'soloylibre_interactions';
$sql = "CREATE TABLE IF NOT EXISTS $interactions_table (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    photo_id bigint(20) NOT NULL,
    user_id bigint(20) DEFAULT NULL,
    user_ip varchar(45) DEFAULT NULL,
    reaction_type varchar(20) NOT NULL DEFAULT 'like',
    interaction_type varchar(20) DEFAULT NULL,
    interaction_count int(11) DEFAULT 1,
    is_simulated tinyint(1) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY photo_id (photo_id),
    KEY user_id (user_id),
    KEY reaction_type (reaction_type),
    KEY created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
$result = dbDelta($sql);

if (!empty($result)) {
    $fixes_applied[] = "✅ Tabla de interacciones verificada/creada";
} else {
    $errors_found[] = "❌ Error verificando tabla de interacciones";
}

// Create albums table if it doesn't exist
$albums_table = $wpdb->prefix . 'soloylibre_albums';
$sql = "CREATE TABLE IF NOT EXISTS $albums_table (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    description text,
    cover_image_id bigint(20),
    membership_level varchar(50),
    is_published tinyint(1) DEFAULT 1,
    sort_order int(11) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

$result = dbDelta($sql);

if (!empty($result)) {
    $fixes_applied[] = "✅ Tabla de álbumes verificada/creada";
} else {
    $errors_found[] = "❌ Error verificando tabla de álbumes";
}

// 7. Disable setup notices
echo "<h2>🔕 7. Deshabilitando Notificaciones de Setup</h2>";

// Remove setup notice hooks
remove_action('admin_notices', array('SoloYLibre_Initial_Setup', 'show_setup_notice'));

// Mark all setup-related options as complete
$disable_notices = array(
    'soloylibre_setup_notice_dismissed' => true,
    'soloylibre_welcome_notice_dismissed' => true,
    'soloylibre_configuration_notice_dismissed' => true,
    'soloylibre_hide_setup_notice' => true
);

foreach ($disable_notices as $option => $value) {
    if (update_option($option, $value)) {
        $fixes_applied[] = "✅ Notificación deshabilitada: $option";
    }
}

// 8. Statistics
echo "<h2>📊 8. Estadísticas de Configuración</h2>";

$total_fixes = count($fixes_applied);
$total_errors = count($errors_found);
$success_rate = $total_fixes / ($total_fixes + $total_errors) * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

// Success Card
echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_fixes</div>";
echo "<div>✅ Configuraciones Aplicadas</div>";
echo "</div>";

// Errors Card
echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$total_errors</div>";
echo "<div>❌ Errores Encontrados</div>";
echo "</div>";

// Success Rate Card
echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Tasa de Éxito</div>";
echo "</div>";

echo "</div>";

// Show results
if (!empty($fixes_applied)) {
    echo "<h3 style='color: #28a745;'>✅ Configuraciones Aplicadas:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; max-height: 300px; overflow-y: auto;'>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors_found)) {
    echo "<h3 style='color: #dc3545;'>❌ Errores Encontrados:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($errors_found as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// 9. Test links
echo "<h2>🔗 9. Enlaces de Verificación</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-quick-posts' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>⚡ Probar Posts Rápidos</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-dashboard' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🏠 Dashboard</a>";
echo "<a href='../../../wp-admin/' style='background: linear-gradient(135deg, #6c757d, #495057); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔙 Admin WordPress</a>";
echo "</div>";

// 10. Final status
echo "<div style='background: " . ($success_rate >= 90 ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' : 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)') . "; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h2 style='margin: 0 0 15px 0; color: " . ($success_rate >= 90 ? '#155724' : '#856404') . ";'>";
echo ($success_rate >= 90 ? '🎉' : '⚠️') . " Configuración Automática " . ($success_rate >= 90 ? 'Completada' : 'Parcial') . "</h2>";
echo "<p style='margin: 0; font-size: 18px; color: " . ($success_rate >= 90 ? '#155724' : '#856404') . ";'>";
echo "Tasa de éxito: <strong>" . round($success_rate, 1) . "%</strong>";
echo "</p>";

if ($success_rate >= 90) {
    echo "<p style='margin: 10px 0 0 0; color: #155724; font-weight: bold;'>¡Ya no deberías ver mensajes de configuración inicial!</p>";
} else {
    echo "<p style='margin: 10px 0 0 0; color: #856404; font-weight: bold;'>Revisa los errores arriba para completar la configuración.</p>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Configuración automática completada</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
