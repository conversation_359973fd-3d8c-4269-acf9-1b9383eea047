<?php
/**
 * Settings Page for SoloYLibre Gallery Pro
 * Developed by JEYKO AI for Jose L Encarnac<PERSON> (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['soloylibre_settings_nonce'], 'soloylibre_settings')) {
    // Save settings
    $settings = array(
        'gallery_style' => sanitize_text_field($_POST['gallery_style']),
        'photos_per_page' => intval($_POST['photos_per_page']),
        'enable_infinite_scroll' => isset($_POST['enable_infinite_scroll']),
        'require_membership' => isset($_POST['require_membership']),
        'photographer_name' => sanitize_text_field($_POST['photographer_name']),
        'photographer_brand' => sanitize_text_field($_POST['photographer_brand']),
        'photographer_email' => sanitize_email($_POST['photographer_email']),
        'photographer_phone' => sanitize_text_field($_POST['photographer_phone']),
        'photographer_location' => sanitize_text_field($_POST['photographer_location'])
    );
    
    foreach ($settings as $key => $value) {
        update_option('soloylibre_gallery_' . $key, $value);
    }
    
    echo '<div class="notice notice-success"><p>✅ Configuración guardada exitosamente!</p></div>';
}

// Get current settings
$current_settings = array(
    'gallery_style' => get_option('soloylibre_gallery_gallery_style', 'grid'),
    'photos_per_page' => get_option('soloylibre_gallery_photos_per_page', 12),
    'enable_infinite_scroll' => get_option('soloylibre_gallery_enable_infinite_scroll', true),
    'require_membership' => get_option('soloylibre_gallery_require_membership', false),
    'photographer_name' => get_option('soloylibre_gallery_photographer_name', 'Jose L Encarnacion'),
    'photographer_brand' => get_option('soloylibre_gallery_photographer_brand', 'SoloYLibre'),
    'photographer_email' => get_option('soloylibre_gallery_photographer_email', '<EMAIL>'),
    'photographer_phone' => get_option('soloylibre_gallery_photographer_phone', '************'),
    'photographer_location' => get_option('soloylibre_gallery_photographer_location', 'San José de Ocoa, Dom. Rep. / USA')
);

$available_styles = array(
    'grid' => 'Grid Portfolio',
    'tiktok' => 'TikTok Style',
    'dominican' => 'Dominican Style (🇩🇴)',
    'masonry' => 'Masonry Layout',
    'carousel' => 'Carousel Slider',
    'lightbox' => 'Lightbox Gallery',
    'professional' => 'Professional Portfolio'
);
?>

<div class="wrap soloylibre-settings-page">
    <div class="soloylibre-header">
        <h1>
            <span class="dashicons dashicons-camera" style="color: #667eea;"></span>
            Configuración - SoloYLibre Gallery Pro
        </h1>
        <p class="subtitle">
            Configuración profesional para <strong><?php echo esc_html($current_settings['photographer_name']); ?></strong>
        </p>
    </div>

    <div class="soloylibre-settings-container">
        <form method="post" action="">
            <?php wp_nonce_field('soloylibre_settings', 'soloylibre_settings_nonce'); ?>
            
            <div class="settings-section">
                <h2>🎨 Configuración de Galería</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="gallery_style">Estilo de Galería por Defecto</label>
                        </th>
                        <td>
                            <select name="gallery_style" id="gallery_style" class="regular-text">
                                <?php foreach ($available_styles as $value => $label): ?>
                                    <option value="<?php echo esc_attr($value); ?>" 
                                            <?php selected($current_settings['gallery_style'], $value); ?>>
                                        <?php echo esc_html($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">Selecciona el estilo de galería que se usará por defecto.</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="photos_per_page">Fotos por Página</label>
                        </th>
                        <td>
                            <input type="number" name="photos_per_page" id="photos_per_page" 
                                   value="<?php echo esc_attr($current_settings['photos_per_page']); ?>" 
                                   min="1" max="100" class="small-text">
                            <p class="description">Número de fotos a mostrar por página.</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Scroll Infinito</th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_infinite_scroll" value="1" 
                                       <?php checked($current_settings['enable_infinite_scroll']); ?>>
                                Habilitar scroll infinito (carga automática de más fotos)
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Membresía Requerida</th>
                        <td>
                            <label>
                                <input type="checkbox" name="require_membership" value="1" 
                                       <?php checked($current_settings['require_membership']); ?>>
                                Requerir membresía para ver fotos
                            </label>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="settings-section">
                <h2>👨‍💻 Información del Fotógrafo</h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="photographer_name">Nombre Completo</label>
                        </th>
                        <td>
                            <input type="text" name="photographer_name" id="photographer_name" 
                                   value="<?php echo esc_attr($current_settings['photographer_name']); ?>" 
                                   class="regular-text">
                            <p class="description">Tu nombre completo como fotógrafo profesional.</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="photographer_brand">Marca/Alias</label>
                        </th>
                        <td>
                            <input type="text" name="photographer_brand" id="photographer_brand" 
                                   value="<?php echo esc_attr($current_settings['photographer_brand']); ?>" 
                                   class="regular-text">
                            <p class="description">Tu marca o alias profesional (ej: SoloYLibre, JoseTusabe).</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="photographer_email">Email de Contacto</label>
                        </th>
                        <td>
                            <input type="email" name="photographer_email" id="photographer_email" 
                                   value="<?php echo esc_attr($current_settings['photographer_email']); ?>" 
                                   class="regular-text">
                            <p class="description">Email para contacto profesional.</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="photographer_phone">Teléfono</label>
                        </th>
                        <td>
                            <input type="text" name="photographer_phone" id="photographer_phone" 
                                   value="<?php echo esc_attr($current_settings['photographer_phone']); ?>" 
                                   class="regular-text">
                            <p class="description">Número de teléfono para contacto.</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="photographer_location">Ubicación</label>
                        </th>
                        <td>
                            <input type="text" name="photographer_location" id="photographer_location" 
                                   value="<?php echo esc_attr($current_settings['photographer_location']); ?>" 
                                   class="regular-text">
                            <p class="description">Tu ubicación (ej: San José de Ocoa, Dom. Rep. / USA).</p>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="settings-section">
                <h2>🔗 Shortcodes Disponibles</h2>
                <div class="shortcode-examples">
                    <h4>Ejemplos de uso:</h4>
                    <code>[soloylibre_gallery]</code> - Galería básica<br>
                    <code>[soloylibre_gallery style="dominican"]</code> - Estilo dominicano 🇩🇴<br>
                    <code>[soloylibre_gallery style="tiktok" limit="6"]</code> - Estilo TikTok con 6 fotos<br>
                    <code>[soloylibre_gallery category="paisajes"]</code> - Solo categoría paisajes<br>
                    <code>[soloylibre_gallery style="professional" limit="8"]</code> - Estilo profesional
                </div>
            </div>

            <div class="settings-section">
                <h2>🌐 Enlaces Útiles</h2>
                <div class="useful-links">
                    <p><strong>🖼️ Ver Galería:</strong> 
                       <a href="<?php echo home_url('/soloylibre-gallery-jose-l-encarnacion-photography/'); ?>" target="_blank">
                           Ver galería pública
                       </a>
                    </p>
                    <p><strong>📝 Gestionar Fotos:</strong> 
                       <a href="<?php echo admin_url('edit.php?post_type=soloylibre_photo'); ?>">
                           Administrar fotos
                       </a>
                    </p>
                    <p><strong>📁 Categorías:</strong> 
                       <a href="<?php echo admin_url('edit-tags.php?taxonomy=photo_category&post_type=soloylibre_photo'); ?>">
                           Gestionar categorías
                       </a>
                    </p>
                    <p><strong>🏷️ Etiquetas:</strong> 
                       <a href="<?php echo admin_url('edit-tags.php?taxonomy=photo_tag&post_type=soloylibre_photo'); ?>">
                           Gestionar etiquetas
                       </a>
                    </p>
                </div>
            </div>

            <p class="submit">
                <input type="submit" name="submit" id="submit" class="button-primary" 
                       value="💾 Guardar Configuración">
            </p>
        </form>
    </div>

    <div class="soloylibre-footer-info">
        <h3>📞 Información de Contacto</h3>
        <p>
            <strong>👨‍💻 Desarrollado por:</strong> JEYKO AI<br>
            <strong>📧 Cliente:</strong> Jose L Encarnacion (JoseTusabe)<br>
            <strong>📱 Teléfono:</strong> ************<br>
            <strong>🌐 Sitios Web:</strong> 
            <a href="https://josetusabe.com" target="_blank">josetusabe.com</a> | 
            <a href="https://soloylibre.com" target="_blank">soloylibre.com</a> | 
            <a href="https://1and1photo.com" target="_blank">1and1photo.com</a> | 
            <a href="https://joselencarnacion.com" target="_blank">joselencarnacion.com</a>
        </p>
    </div>
</div>

<style>
.soloylibre-settings-page {
    max-width: 1000px;
}

.soloylibre-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.soloylibre-header h1 {
    color: white;
    margin: 0;
}

.settings-section {
    background: white;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.settings-section h2 {
    margin-top: 0;
    color: #667eea;
    border-bottom: 2px solid #f0f0f1;
    padding-bottom: 10px;
}

.shortcode-examples {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #667eea;
}

.shortcode-examples code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

.useful-links {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #28a745;
}

.soloylibre-footer-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #CE1126;
    margin-top: 20px;
}

.soloylibre-footer-info h3 {
    color: #CE1126;
    margin-top: 0;
}
</style>
