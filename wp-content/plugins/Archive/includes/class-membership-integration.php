<?php
/**
 * Membership Integration Class
 * Handles integration with Paid Memberships Pro and other membership plugins
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Membership_Integration {
    
    private $supported_plugins = array(
        'pmpro' => 'Paid Memberships Pro',
        'memberpress' => 'MemberPress',
        'restrict_content' => 'Restrict Content Pro'
    );
    
    private $active_plugin = null;
    
    public function __construct() {
        $this->detect_membership_plugin();
        $this->init_hooks();
    }
    
    /**
     * Detect active membership plugin
     */
    private function detect_membership_plugin() {
        if (function_exists('pmpro_hasMembershipLevel')) {
            $this->active_plugin = 'pmpro';
        } elseif (class_exists('MemberPress')) {
            $this->active_plugin = 'memberpress';
        } elseif (function_exists('rcp_user_has_active_membership')) {
            $this->active_plugin = 'restrict_content';
        }
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_ajax_check_membership_access', array($this, 'ajax_check_membership_access'));
        add_action('wp_ajax_nopriv_check_membership_access', array($this, 'ajax_check_membership_access'));
        
        // Add membership level meta box
        add_action('add_meta_boxes', array($this, 'add_membership_meta_box'));
        add_action('save_post', array($this, 'save_membership_meta'));
        
        // Filter gallery content based on membership
        add_filter('soloylibre_gallery_photos', array($this, 'filter_photos_by_membership'), 10, 2);
    }
    
    /**
     * Get available membership levels
     */
    public function get_membership_levels() {
        $levels = array();
        
        switch ($this->active_plugin) {
            case 'pmpro':
                if (function_exists('pmpro_getAllLevels')) {
                    $pmpro_levels = pmpro_getAllLevels();
                    foreach ($pmpro_levels as $level) {
                        $levels[$level->id] = $level->name;
                    }
                }
                break;
                
            case 'memberpress':
                if (class_exists('MeprProduct')) {
                    $products = MeprProduct::get_all();
                    foreach ($products as $product) {
                        $levels[$product->ID] = $product->post_title;
                    }
                }
                break;
                
            case 'restrict_content':
                if (function_exists('rcp_get_membership_levels')) {
                    $rcp_levels = rcp_get_membership_levels();
                    foreach ($rcp_levels as $level) {
                        $levels[$level->id] = $level->name;
                    }
                }
                break;
        }
        
        return $levels;
    }
    
    /**
     * Check if user has specific membership level
     */
    public function user_has_membership_level($level_id, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return false;
        }
        
        switch ($this->active_plugin) {
            case 'pmpro':
                return function_exists('pmpro_hasMembershipLevel') && 
                       pmpro_hasMembershipLevel($level_id, $user_id);
                
            case 'memberpress':
                if (class_exists('MeprUser')) {
                    $user = new MeprUser($user_id);
                    return $user->has_access_to_product($level_id);
                }
                break;
                
            case 'restrict_content':
                if (function_exists('rcp_user_has_active_membership')) {
                    return rcp_user_has_active_membership($user_id) && 
                           rcp_user_has_membership_level($user_id, $level_id);
                }
                break;
        }
        
        return false;
    }
    
    /**
     * Get user's current membership level
     */
    public function get_user_membership_level($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return null;
        }
        
        switch ($this->active_plugin) {
            case 'pmpro':
                if (function_exists('pmpro_getMembershipLevelForUser')) {
                    return pmpro_getMembershipLevelForUser($user_id);
                }
                break;
                
            case 'memberpress':
                if (class_exists('MeprUser')) {
                    $user = new MeprUser($user_id);
                    $memberships = $user->active_product_subscriptions('products');
                    return !empty($memberships) ? $memberships[0] : null;
                }
                break;
                
            case 'restrict_content':
                if (function_exists('rcp_get_customer_by_user_id')) {
                    $customer = rcp_get_customer_by_user_id($user_id);
                    return $customer ? $customer->get_membership_level() : null;
                }
                break;
        }
        
        return null;
    }
    
    /**
     * Get membership badge HTML
     */
    public function get_membership_badge($user_id = null) {
        $level = $this->get_user_membership_level($user_id);
        
        if (!$level) {
            return '<div class="membership-badge guest">Visitante</div>';
        }
        
        $level_name = '';
        $level_class = 'basic';
        
        switch ($this->active_plugin) {
            case 'pmpro':
                $level_name = $level->name;
                $level_class = strtolower(str_replace(' ', '-', $level->name));
                break;
                
            case 'memberpress':
                $level_name = get_the_title($level->ID);
                $level_class = 'memberpress-' . $level->ID;
                break;
                
            case 'restrict_content':
                $level_name = $level->name;
                $level_class = 'rcp-' . $level->id;
                break;
        }
        
        return sprintf(
            '<div class="membership-badge %s">%s</div>',
            esc_attr($level_class),
            esc_html($level_name)
        );
    }
    
    /**
     * Get upgrade URL
     */
    public function get_upgrade_url() {
        switch ($this->active_plugin) {
            case 'pmpro':
                if (function_exists('pmpro_url')) {
                    return pmpro_url('levels');
                }
                break;
                
            case 'memberpress':
                // MemberPress doesn't have a standard function, use custom page
                $pricing_page = get_option('soloylibre_memberpress_pricing_page');
                if ($pricing_page) {
                    return get_permalink($pricing_page);
                }
                break;
                
            case 'restrict_content':
                if (function_exists('rcp_get_registration_page_url')) {
                    return rcp_get_registration_page_url();
                }
                break;
        }
        
        return home_url('/membership/');
    }
    
    /**
     * Check if photo is accessible to user
     */
    public function can_user_access_photo($photo_id, $user_id = null) {
        $required_level = get_post_meta($photo_id, '_soloylibre_membership_level', true);
        
        // If no membership level required, photo is public
        if (empty($required_level)) {
            return true;
        }
        
        // Check if user has required level
        return $this->user_has_membership_level($required_level, $user_id);
    }
    
    /**
     * Filter photos by membership level
     */
    public function filter_photos_by_membership($photos, $args) {
        if (!is_user_logged_in() && get_option('soloylibre_gallery_require_login', false)) {
            return array(); // Return empty if login required
        }
        
        $filtered_photos = array();
        
        foreach ($photos as $photo) {
            if ($this->can_user_access_photo($photo->ID)) {
                $filtered_photos[] = $photo;
            } else {
                // Add photo but mark as restricted
                $photo->is_restricted = true;
                $filtered_photos[] = $photo;
            }
        }
        
        return $filtered_photos;
    }
    
    /**
     * Add membership meta box to photo edit screen
     */
    public function add_membership_meta_box() {
        add_meta_box(
            'soloylibre_membership_meta',
            __('Configuración de Membresía', 'soloylibre-gallery'),
            array($this, 'render_membership_meta_box'),
            'soloylibre_photo',
            'side',
            'high'
        );
    }
    
    /**
     * Render membership meta box
     */
    public function render_membership_meta_box($post) {
        wp_nonce_field('soloylibre_membership_meta', 'soloylibre_membership_nonce');
        
        $current_level = get_post_meta($post->ID, '_soloylibre_membership_level', true);
        $levels = $this->get_membership_levels();
        
        ?>
        <div class="soloylibre-membership-settings">
            <p>
                <label for="soloylibre_membership_level">
                    <strong><?php _e('Nivel de Membresía Requerido:', 'soloylibre-gallery'); ?></strong>
                </label>
            </p>
            <select name="soloylibre_membership_level" id="soloylibre_membership_level" style="width: 100%;">
                <option value=""><?php _e('Público (Sin restricciones)', 'soloylibre-gallery'); ?></option>
                <?php foreach ($levels as $level_id => $level_name): ?>
                    <option value="<?php echo esc_attr($level_id); ?>" <?php selected($current_level, $level_id); ?>>
                        <?php echo esc_html($level_name); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            
            <p class="description">
                <?php _e('Selecciona el nivel de membresía requerido para ver esta foto. Déjalo en "Público" para que todos puedan verla.', 'soloylibre-gallery'); ?>
            </p>
            
            <?php if (!$this->active_plugin): ?>
                <div class="notice notice-warning inline">
                    <p>
                        <strong><?php _e('Aviso:', 'soloylibre-gallery'); ?></strong>
                        <?php _e('No se detectó ningún plugin de membresía activo. Las restricciones no funcionarán hasta que instales y actives un plugin compatible.', 'soloylibre-gallery'); ?>
                    </p>
                    <p>
                        <?php _e('Plugins compatibles:', 'soloylibre-gallery'); ?>
                        <ul>
                            <li>• Paid Memberships Pro (Recomendado)</li>
                            <li>• MemberPress</li>
                            <li>• Restrict Content Pro</li>
                        </ul>
                    </p>
                </div>
            <?php endif; ?>
            
            <div class="soloylibre-membership-preview">
                <h4><?php _e('Vista Previa de Acceso:', 'soloylibre-gallery'); ?></h4>
                <div id="membership-preview-content">
                    <?php if (empty($current_level)): ?>
                        <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
                        <?php _e('Esta foto será visible para todos los visitantes', 'soloylibre-gallery'); ?>
                    <?php else: ?>
                        <span class="dashicons dashicons-lock" style="color: orange;"></span>
                        <?php 
                        printf(
                            __('Esta foto solo será visible para miembros con nivel: %s', 'soloylibre-gallery'),
                            '<strong>' . esc_html($levels[$current_level] ?? $current_level) . '</strong>'
                        ); 
                        ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#soloylibre_membership_level').on('change', function() {
                var selectedLevel = $(this).val();
                var selectedText = $(this).find('option:selected').text();
                var previewContent = $('#membership-preview-content');
                
                if (selectedLevel === '') {
                    previewContent.html('<span class="dashicons dashicons-yes-alt" style="color: green;"></span> Esta foto será visible para todos los visitantes');
                } else {
                    previewContent.html('<span class="dashicons dashicons-lock" style="color: orange;"></span> Esta foto solo será visible para miembros con nivel: <strong>' + selectedText + '</strong>');
                }
            });
        });
        </script>
        
        <style>
        .soloylibre-membership-settings {
            padding: 10px 0;
        }
        
        .soloylibre-membership-preview {
            margin-top: 15px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        
        .soloylibre-membership-preview h4 {
            margin: 0 0 8px 0;
            font-size: 13px;
        }
        
        #membership-preview-content {
            font-size: 12px;
            line-height: 1.4;
        }
        
        #membership-preview-content .dashicons {
            font-size: 16px;
            width: 16px;
            height: 16px;
            vertical-align: middle;
            margin-right: 5px;
        }
        </style>
        <?php
    }
    
    /**
     * Save membership meta
     */
    public function save_membership_meta($post_id) {
        // Check nonce
        if (!isset($_POST['soloylibre_membership_nonce']) || 
            !wp_verify_nonce($_POST['soloylibre_membership_nonce'], 'soloylibre_membership_meta')) {
            return;
        }
        
        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Save membership level
        if (isset($_POST['soloylibre_membership_level'])) {
            $membership_level = sanitize_text_field($_POST['soloylibre_membership_level']);
            update_post_meta($post_id, '_soloylibre_membership_level', $membership_level);
        }
    }
    
    /**
     * AJAX check membership access
     */
    public function ajax_check_membership_access() {
        check_ajax_referer('soloylibre_gallery_nonce', 'nonce');
        
        $photo_id = intval($_POST['photo_id']);
        $can_access = $this->can_user_access_photo($photo_id);
        
        wp_send_json_success(array(
            'can_access' => $can_access,
            'upgrade_url' => $this->get_upgrade_url(),
            'membership_badge' => $this->get_membership_badge()
        ));
    }
    
    /**
     * Get active membership plugin name
     */
    public function get_active_plugin_name() {
        return $this->active_plugin ? $this->supported_plugins[$this->active_plugin] : null;
    }
    
    /**
     * Check if any membership plugin is active
     */
    public function has_membership_plugin() {
        return !is_null($this->active_plugin);
    }
    
    /**
     * Get membership statistics
     */
    public function get_membership_stats() {
        $stats = array(
            'total_members' => 0,
            'active_members' => 0,
            'levels' => array()
        );
        
        switch ($this->active_plugin) {
            case 'pmpro':
                if (function_exists('pmpro_getMembershipStats')) {
                    // Get PMP Pro stats
                    $levels = $this->get_membership_levels();
                    foreach ($levels as $level_id => $level_name) {
                        $count = pmpro_getMembershipLevelForUser($level_id);
                        $stats['levels'][$level_name] = $count;
                    }
                }
                break;
        }
        
        return $stats;
    }
}
