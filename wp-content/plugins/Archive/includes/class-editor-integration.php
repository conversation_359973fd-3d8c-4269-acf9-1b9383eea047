<?php
/**
 * SoloYLibre Gallery Editor Integration
 * Integrates gallery functionality directly into WordPress editor
 * 
 * @package SoloYLibre_Gallery_Pro
 * @version 5.3.0
 * <AUTHOR> AI for Jose L Encarnacion (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Editor_Integration {
    
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Classic Editor Integration
        add_action('media_buttons', array($this, 'add_media_button'));
        add_action('admin_footer', array($this, 'add_editor_modal'));
        
        // Gutenberg Block Integration
        add_action('init', array($this, 'register_gutenberg_block'));
        add_action('enqueue_block_editor_assets', array($this, 'enqueue_block_assets'));
        
        // AJAX Handlers
        add_action('wp_ajax_soloylibre_editor_get_photos', array($this, 'ajax_get_photos'));
        add_action('wp_ajax_soloylibre_editor_create_gallery', array($this, 'ajax_create_gallery'));
        add_action('wp_ajax_soloylibre_editor_auto_generate', array($this, 'ajax_auto_generate'));
        
        // Enqueue scripts for editor
        add_action('admin_enqueue_scripts', array($this, 'enqueue_editor_scripts'));
    }
    
    /**
     * Add SoloYLibre Gallery button to classic editor
     */
    public function add_media_button() {
        global $pagenow, $typenow;
        
        // Only show on post edit screens
        if (!in_array($pagenow, array('post.php', 'page.php', 'post-new.php', 'post-new.php'))) {
            return;
        }
        
        echo '<button type="button" class="button soloylibre-editor-btn" data-editor="classic">';
        echo '<span class="wp-media-buttons-icon dashicons dashicons-camera"></span>';
        echo ' 📸 SoloYLibre Gallery';
        echo '</button>';
    }
    
    /**
     * Add modal for editor integration
     */
    public function add_editor_modal() {
        global $pagenow;
        
        if (!in_array($pagenow, array('post.php', 'page.php', 'post-new.php', 'post-new.php'))) {
            return;
        }
        
        ?>
        <div id="soloylibre-editor-modal" class="soloylibre-modal-overlay" style="display: none;">
            <div class="soloylibre-modal editor-modal">
                <div class="modal-header">
                    <h3>📸 SoloYLibre Gallery - Selector de Fotos</h3>
                    <button class="close-modal" onclick="closeSoloYLibreModal()">×</button>
                </div>
                
                <div class="modal-content">
                    <!-- Tabs -->
                    <div class="editor-tabs">
                        <button class="tab-btn active" data-tab="select">📸 Seleccionar Fotos</button>
                        <button class="tab-btn" data-tab="auto">🤖 Auto-Generar</button>
                        <button class="tab-btn" data-tab="styles">🇩🇴 Estilos</button>
                    </div>
                    
                    <!-- Tab Content: Select Photos -->
                    <div class="tab-content active" id="tab-select">
                        <div class="photo-controls">
                            <div class="control-group">
                                <label>🔍 Buscar fotos:</label>
                                <input type="text" id="photo-search" placeholder="Buscar por título...">
                            </div>
                            <div class="control-group">
                                <label>📊 Mostrar:</label>
                                <select id="photos-per-page">
                                    <option value="20">20 fotos</option>
                                    <option value="50" selected>50 fotos</option>
                                    <option value="100">100 fotos</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label>🔄 Ordenar:</label>
                                <select id="photo-order">
                                    <option value="desc">Más recientes</option>
                                    <option value="asc">Más antiguas</option>
                                    <option value="random">Aleatorio</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="photo-stats">
                            <span id="photos-available">0 fotos disponibles</span>
                            <span id="photos-selected">0 seleccionadas</span>
                        </div>
                        
                        <div id="photos-loading" class="loading-section">
                            <div class="loading-spinner"></div>
                            <p>Cargando fotos disponibles...</p>
                        </div>
                        
                        <div id="photos-grid" class="editor-photos-grid"></div>
                        
                        <div class="load-more-section">
                            <button id="load-more-photos" class="btn btn-secondary" style="display: none;">
                                📥 Cargar Más Fotos
                            </button>
                        </div>
                    </div>
                    
                    <!-- Tab Content: Auto Generate -->
                    <div class="tab-content" id="tab-auto">
                        <div class="auto-generate-section">
                            <h4>🤖 Generación Automática de Galería</h4>
                            <p>Selecciona automáticamente las mejores fotos no publicadas para tu galería.</p>
                            
                            <div class="auto-controls">
                                <div class="control-group">
                                    <label for="auto-photo-count">📸 Número de fotos:</label>
                                    <input type="number" id="auto-photo-count" min="5" max="50" value="15" placeholder="Ej: 15">
                                    <small>Mínimo 5, máximo 50 fotos</small>
                                </div>
                                
                                <div class="control-group">
                                    <label for="auto-style">🎨 Estilo de galería:</label>
                                    <select id="auto-style">
                                        <option value="masonry">Masonry (Recomendado)</option>
                                        <option value="grid">Grid Uniforme</option>
                                        <option value="carousel">Carousel</option>
                                    </select>
                                </div>
                                
                                <div class="control-group">
                                    <label for="auto-columns">📊 Columnas:</label>
                                    <select id="auto-columns">
                                        <option value="2">2 columnas</option>
                                        <option value="3">3 columnas</option>
                                        <option value="4" selected>4 columnas</option>
                                        <option value="5">5 columnas</option>
                                        <option value="6">6 columnas</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="auto-preview" id="auto-preview" style="display: none;">
                                <h5>Vista previa de fotos seleccionadas:</h5>
                                <div id="auto-photos-preview" class="auto-photos-grid"></div>
                            </div>
                            
                            <button id="generate-auto-gallery" class="btn btn-primary btn-large">
                                🎲 Generar Galería Automática
                            </button>
                        </div>
                    </div>
                    
                    <!-- Tab Content: Styles -->
                    <div class="tab-content" id="tab-styles">
                        <div class="styles-section">
                            <h4>🇩🇴 Estilos Dominicanos</h4>
                            <p>Selecciona un estilo inspirado en la cultura dominicana:</p>
                            
                            <div class="dominican-styles-grid">
                                <div class="style-option" data-style="bandera">
                                    <div class="style-preview bandera-style">
                                        <div class="style-overlay">
                                            <h5>🇩🇴 Bandera Dominicana</h5>
                                            <p>Colores patrióticos</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="style-option" data-style="merengue">
                                    <div class="style-preview merengue-style">
                                        <div class="style-overlay">
                                            <h5>🎵 Merengue</h5>
                                            <p>Colores vibrantes</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="style-option" data-style="caribe">
                                    <div class="style-preview caribe-style">
                                        <div class="style-overlay">
                                            <h5>🏝️ Caribeño</h5>
                                            <p>Azules tropicales</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="style-option" data-style="colonial">
                                    <div class="style-preview colonial-style">
                                        <div class="style-overlay">
                                            <h5>🏛️ Colonial</h5>
                                            <p>Elegancia histórica</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <div class="gallery-options">
                        <div class="option-group">
                            <label for="gallery-style">🎨 Estilo:</label>
                            <select id="gallery-style">
                                <option value="masonry">Masonry</option>
                                <option value="grid">Grid</option>
                                <option value="carousel">Carousel</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label for="gallery-columns">📊 Columnas:</label>
                            <select id="gallery-columns">
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4" selected>4</option>
                                <option value="5">5</option>
                                <option value="6">6</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="enable-interactions" checked>
                                ❤️ Habilitar interacciones
                            </label>
                        </div>
                    </div>
                    
                    <div class="modal-actions">
                        <button class="btn btn-secondary" onclick="closeSoloYLibreModal()">Cancelar</button>
                        <button id="insert-gallery" class="btn btn-primary" disabled>
                            🚀 Insertar Galería
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Enqueue scripts for editor integration
     */
    public function enqueue_editor_scripts($hook) {
        global $pagenow;
        
        if (!in_array($pagenow, array('post.php', 'page.php', 'post-new.php', 'post-new.php'))) {
            return;
        }
        
        wp_enqueue_script(
            'soloylibre-editor-integration',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/editor-integration.js',
            array('jquery'),
            '5.3.0',
            true
        );
        
        wp_enqueue_style(
            'soloylibre-editor-integration',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/css/editor-integration.css',
            array(),
            '5.3.0'
        );
        
        wp_localize_script('soloylibre-editor-integration', 'soloylibre_editor', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('soloylibre_editor_nonce'),
            'strings' => array(
                'loading' => 'Cargando...',
                'error' => 'Error al cargar fotos',
                'no_photos' => 'No hay fotos disponibles',
                'select_photos' => 'Selecciona al menos una foto',
                'gallery_inserted' => 'Galería insertada exitosamente'
            )
        ));
    }

    /**
     * AJAX: Get photos for editor
     */
    public function ajax_get_photos() {
        check_ajax_referer('soloylibre_editor_nonce', 'nonce');

        $page = intval($_POST['page'] ?? 1);
        $per_page = intval($_POST['per_page'] ?? 50);
        $search = sanitize_text_field($_POST['search'] ?? '');
        $order = sanitize_text_field($_POST['order'] ?? 'desc');

        // Get all photos
        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'fields' => 'ids'
        );

        if (!empty($search)) {
            $args['s'] = $search;
        }

        $all_photo_ids = get_posts($args);

        // Get published photos to exclude
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unused_photos = get_option('soloylibre_unused_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        $categorized_photos = array_merge($published_photos, $private_photos, $unused_photos, $unwanted_photos);

        // Filter available photos
        $available_photo_ids = array_diff($all_photo_ids, $categorized_photos);

        if (empty($available_photo_ids)) {
            wp_send_json_success(array(
                'photos' => array(),
                'total_available' => 0,
                'has_more' => false,
                'current_page' => $page
            ));
            return;
        }

        // Apply ordering
        $photos_query = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => $per_page,
            'offset' => ($page - 1) * $per_page,
            'post_status' => 'inherit',
            'post__in' => $available_photo_ids,
            'orderby' => $order === 'random' ? 'rand' : 'date',
            'order' => $order === 'asc' ? 'ASC' : 'DESC'
        );

        if (!empty($search)) {
            $photos_query['s'] = $search;
        }

        $photos = get_posts($photos_query);
        $formatted_photos = array();

        foreach ($photos as $photo) {
            $formatted_photos[] = array(
                'id' => $photo->ID,
                'title' => get_the_title($photo->ID) ?: 'Sin título',
                'url' => wp_get_attachment_image_url($photo->ID, 'medium'),
                'thumbnail' => wp_get_attachment_image_url($photo->ID, 'thumbnail'),
                'full' => wp_get_attachment_image_url($photo->ID, 'full'),
                'date' => $photo->post_date,
                'size' => $this->get_file_size($photo->ID)
            );
        }

        $total_available = count($available_photo_ids);
        $total_pages = ceil($total_available / $per_page);

        wp_send_json_success(array(
            'photos' => $formatted_photos,
            'total_available' => $total_available,
            'has_more' => $page < $total_pages,
            'current_page' => $page,
            'total_pages' => $total_pages
        ));
    }

    /**
     * AJAX: Create gallery shortcode
     */
    public function ajax_create_gallery() {
        check_ajax_referer('soloylibre_editor_nonce', 'nonce');

        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());
        $style = sanitize_text_field($_POST['style'] ?? 'masonry');
        $columns = intval($_POST['columns'] ?? 4);
        $interactions = $_POST['interactions'] === 'true';
        $dominican_style = sanitize_text_field($_POST['dominican_style'] ?? '');

        if (empty($photo_ids)) {
            wp_send_json_error('No se seleccionaron fotos');
            return;
        }

        // Mark photos as published
        $published_photos = get_option('soloylibre_published_photos', array());
        $published_photos = array_unique(array_merge($published_photos, $photo_ids));
        update_option('soloylibre_published_photos', $published_photos);

        // Update monthly statistics
        $monthly_key = 'soloylibre_monthly_published_' . date('Y_m');
        $monthly_published = get_option($monthly_key, array());
        $monthly_published = array_unique(array_merge($monthly_published, $photo_ids));
        update_option($monthly_key, $monthly_published);

        // Create shortcode
        $shortcode_attrs = array(
            'style="' . $style . '"',
            'ids="' . implode(',', $photo_ids) . '"',
            'columns="' . $columns . '"',
            'enable_interactions="' . ($interactions ? 'true' : 'false') . '"',
            'aspect_ratio="natural"'
        );

        if (!empty($dominican_style)) {
            $shortcode_attrs[] = 'dominican_style="' . $dominican_style . '"';
        }

        $shortcode = '[soloylibre_gallery ' . implode(' ', $shortcode_attrs) . ']';

        wp_send_json_success(array(
            'shortcode' => $shortcode,
            'photos_count' => count($photo_ids),
            'message' => 'Galería creada con ' . count($photo_ids) . ' fotos'
        ));
    }

    /**
     * AJAX: Auto generate gallery
     */
    public function ajax_auto_generate() {
        check_ajax_referer('soloylibre_editor_nonce', 'nonce');

        $photo_count = intval($_POST['photo_count'] ?? 15);
        $style = sanitize_text_field($_POST['style'] ?? 'masonry');
        $columns = intval($_POST['columns'] ?? 4);

        // Validate photo count
        if ($photo_count < 5 || $photo_count > 50) {
            wp_send_json_error('El número de fotos debe estar entre 5 y 50');
            return;
        }

        // Get available photos
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC',
            'fields' => 'ids'
        ));

        $published_photos = get_option('soloylibre_published_photos', array());
        $categorized_photos = array_merge(
            $published_photos,
            get_option('soloylibre_private_photos', array()),
            get_option('soloylibre_unused_photos', array()),
            get_option('soloylibre_unwanted_photos', array())
        );

        $available_photos = array_diff($all_photos, $categorized_photos);

        if (empty($available_photos)) {
            wp_send_json_error('No hay fotos disponibles para auto-generar');
            return;
        }

        if (count($available_photos) < $photo_count) {
            $photo_count = count($available_photos);
        }

        // Select photos intelligently (most recent)
        $selected_photos = array_slice($available_photos, 0, $photo_count);

        // Get photo details for preview
        $photo_details = array();
        foreach ($selected_photos as $photo_id) {
            $photo_details[] = array(
                'id' => $photo_id,
                'title' => get_the_title($photo_id) ?: 'Sin título',
                'thumbnail' => wp_get_attachment_image_url($photo_id, 'thumbnail'),
                'url' => wp_get_attachment_image_url($photo_id, 'medium')
            );
        }

        wp_send_json_success(array(
            'photos' => $photo_details,
            'photo_ids' => $selected_photos,
            'count' => count($selected_photos),
            'message' => 'Se seleccionaron automáticamente ' . count($selected_photos) . ' fotos'
        ));
    }

    /**
     * Get file size helper
     */
    private function get_file_size($attachment_id) {
        $file_path = get_attached_file($attachment_id);
        if ($file_path && file_exists($file_path)) {
            $size = filesize($file_path);
            if ($size >= 1048576) {
                return round($size / 1048576, 1) . ' MB';
            } elseif ($size >= 1024) {
                return round($size / 1024, 1) . ' KB';
            }
            return $size . ' B';
        }
        return 'N/A';
    }
}
