<?php
/**
 * SoloYLibre Settings Manager
 * Gestor de configuraciones centralizadas
 */

class SoloYLibre_Settings_Manager {
    
    private $options_group = "soloylibre_settings";
    private $options_name = "soloylibre_options";
    
    public function __construct() {
        add_action("admin_menu", array($this, "add_settings_page"));
        add_action("admin_init", array($this, "register_settings"));
        add_action("wp_ajax_toggle_confetti", array($this, "ajax_toggle_confetti"));
        add_action("wp_ajax_generate_interactions", array($this, "ajax_generate_interactions"));
    }
    
    public function add_settings_page() {
        add_submenu_page(
            "soloylibre-main",
            "Configuraciones",
            "⚙️ Configuraciones",
            "manage_options",
            "soloylibre-settings",
            array($this, "render_settings_page")
        );
    }
    
    public function register_settings() {
        register_setting($this->options_group, $this->options_name);
        
        // Sección General
        add_settings_section(
            "general_section",
            "Configuraciones Generales",
            null,
            "soloylibre_settings"
        );
        
        // Campos de configuración
        $fields = array(
            "enable_confetti" => array(
                "title" => "Efectos de Confetti",
                "type" => "checkbox",
                "description" => "Mostrar efectos de confetti al usar el plugin"
            ),
            "gallery_default_style" => array(
                "title" => "Estilo por Defecto",
                "type" => "select",
                "options" => array(
                    "dominican" => "🇩🇴 Estilo Dominicano",
                    "grid" => "📱 Grid Moderno",
                    "masonry" => "🧱 Masonry",
                    "carousel" => "🎠 Carousel",
                    "professional" => "💼 Profesional"
                ),
                "description" => "Estilo por defecto para nuevas galerías"
            ),
            "auto_load_photos" => array(
                "title" => "Fotos Auto-cargadas",
                "type" => "number",
                "description" => "Número de fotos a cargar automáticamente (50-500)"
            ),
            "enable_interactions" => array(
                "title" => "Interacciones de Usuario",
                "type" => "checkbox",
                "description" => "Habilitar likes, vistas y compartir"
            ),
            "photographer_signature" => array(
                "title" => "Firma del Fotógrafo",
                "type" => "checkbox",
                "description" => "Mostrar firma en todas las galerías"
            ),
            "lightbox_enabled" => array(
                "title" => "Lightbox por Defecto",
                "type" => "checkbox",
                "description" => "Habilitar lightbox en todas las galerías"
            ),
            "random_interactions" => array(
                "title" => "Interacciones Aleatorias",
                "type" => "checkbox",
                "description" => "Generar likes y vistas aleatorias"
            )
        );
        
        foreach ($fields as $field_id => $field) {
            add_settings_field(
                $field_id,
                $field["title"],
                array($this, "render_field"),
                "soloylibre_settings",
                "general_section",
                array("field_id" => $field_id, "field" => $field)
            );
        }
    }
    
    public function render_field($args) {
        $field_id = $args["field_id"];
        $field = $args["field"];
        $options = get_option($this->options_name, array());
        $value = isset($options[$field_id]) ? $options[$field_id] : "";
        
        $name = $this->options_name . "[" . $field_id . "]";
        
        switch ($field["type"]) {
            case "checkbox":
                echo "<input type=\"checkbox\" name=\"$name\" value=\"1\" " . checked(1, $value, false) . ">";
                break;
                
            case "select":
                echo "<select name=\"$name\">";
                foreach ($field["options"] as $option_value => $option_label) {
                    echo "<option value=\"$option_value\" " . selected($option_value, $value, false) . ">$option_label</option>";
                }
                echo "</select>";
                break;
                
            case "number":
                $default = $field_id === "auto_load_photos" ? 300 : 0;
                $value = $value ?: $default;
                echo "<input type=\"number\" name=\"$name\" value=\"$value\" min=\"50\" max=\"500\">";
                break;
                
            default:
                echo "<input type=\"text\" name=\"$name\" value=\"$value\">";
        }
        
        if (isset($field["description"])) {
            echo "<p class=\"description\">" . $field["description"] . "</p>";
        }
    }
    
    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1>⚙️ Configuraciones SoloYLibre Gallery Pro</h1>
            
            <div style="background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h2 style="color: white; margin: 0;">🇩🇴 SoloYLibre Photography</h2>
                <p style="margin: 5px 0;">Jose L Encarnacion (JoseTusabe) - San José de Ocoa, República Dominicana</p>
            </div>
            
            <div class="soloylibre-settings-container">
                <form method="post" action="options.php">
                    <?php
                    settings_fields($this->options_group);
                    do_settings_sections("soloylibre_settings");
                    submit_button("💾 Guardar Configuraciones");
                    ?>
                </form>
                
                <div class="soloylibre-actions" style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h3>🚀 Acciones Rápidas</h3>
                    
                    <button type="button" class="button button-secondary" onclick="toggleConfetti()">
                        🎉 Probar Confetti
                    </button>
                    
                    <button type="button" class="button button-secondary" onclick="generateInteractions()">
                        📊 Generar Interacciones Aleatorias
                    </button>
                    
                    <button type="button" class="button button-primary" onclick="window.open('<?php echo admin_url("admin.php?page=soloylibre-wizard"); ?>', '_blank')">
                        🧙‍♂️ Abrir Wizard
                    </button>
                </div>
            </div>
            
            <style>
            .soloylibre-settings-container {
                max-width: 800px;
            }
            
            .form-table th {
                width: 200px;
            }
            
            .soloylibre-actions button {
                margin-right: 10px;
                margin-bottom: 10px;
            }
            </style>
            
            <script>
            function toggleConfetti() {
                // Crear efecto de confetti
                if (typeof confetti !== "undefined") {
                    confetti({
                        particleCount: 100,
                        spread: 70,
                        origin: { y: 0.6 }
                    });
                } else {
                    alert("¡Confetti activado! 🎉");
                }
            }
            
            function generateInteractions() {
                if (confirm("¿Generar interacciones aleatorias para las galerías?")) {
                    jQuery.post(ajaxurl, {
                        action: "generate_interactions",
                        nonce: "<?php echo wp_create_nonce("generate_interactions"); ?>"
                    }, function(response) {
                        if (response.success) {
                            alert("✅ " + response.data.message);
                        } else {
                            alert("❌ Error: " + response.data.message);
                        }
                    });
                }
            }
            </script>
        </div>
        <?php
    }
    
    public function ajax_toggle_confetti() {
        wp_send_json_success(array("message" => "Confetti activado"));
    }
    
    public function ajax_generate_interactions() {
        check_ajax_referer("generate_interactions", "nonce");
        
        global $wpdb;
        
        // Obtener fotos de la biblioteca
        $photos = get_posts(array(
            "post_type" => "attachment",
            "post_mime_type" => "image",
            "posts_per_page" => 20,
            "post_status" => "inherit"
        ));
        
        $interactions_created = 0;
        
        foreach ($photos as $photo) {
            // Generar interacciones aleatorias
            $views = rand(10, 500);
            $likes = rand(1, 50);
            $shares = rand(0, 10);
            
            // Insertar en base de datos
            $wpdb->insert(
                $wpdb->prefix . "soloylibre_interactions",
                array(
                    "photo_id" => $photo->ID,
                    "interaction_type" => "view",
                    "interaction_count" => $views,
                    "created_at" => current_time("mysql")
                )
            );
            
            $wpdb->insert(
                $wpdb->prefix . "soloylibre_interactions",
                array(
                    "photo_id" => $photo->ID,
                    "interaction_type" => "like",
                    "interaction_count" => $likes,
                    "created_at" => current_time("mysql")
                )
            );
            
            $wpdb->insert(
                $wpdb->prefix . "soloylibre_interactions",
                array(
                    "photo_id" => $photo->ID,
                    "interaction_type" => "share",
                    "interaction_count" => $shares,
                    "created_at" => current_time("mysql")
                )
            );
            
            $interactions_created += 3;
        }
        
        wp_send_json_success(array(
            "message" => "Se generaron $interactions_created interacciones para " . count($photos) . " fotos"
        ));
    }
    
    public function get_option($key, $default = null) {
        $options = get_option($this->options_name, array());
        return isset($options[$key]) ? $options[$key] : $default;
    }
}

new SoloYLibre_Settings_Manager();
?>