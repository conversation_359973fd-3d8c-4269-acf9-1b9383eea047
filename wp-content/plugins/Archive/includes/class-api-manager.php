<?php
/**
 * SoloYLibre Photography Platform - API Manager
 * Arquitectura REST API robusta y escalable
 * Desarrollado por JEYKO AI para <PERSON>carnacion (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_API_Manager {

    /**
     * API Version
     */
    const API_VERSION = 'v1';

    /**
     * API Namespace
     */
    const API_NAMESPACE = 'soloylibre/v1';

    /**
     * Rate limiting configuration
     */
    private $rate_limits = array(
        'default' => array('requests' => 100, 'window' => 3600), // 100 requests per hour
        'upload' => array('requests' => 50, 'window' => 3600),   // 50 uploads per hour
        'bulk' => array('requests' => 10, 'window' => 3600),     // 10 bulk operations per hour
        'admin' => array('requests' => 1000, 'window' => 3600)   // 1000 requests per hour for admins
    );

    /**
     * Photographer information
     */
    private $photographer_info = array(
        'name' => 'Jose L Encarnacion',
        'alias' => '<PERSON><PERSON><PERSON><PERSON>',
        'brand' => 'SoloYLibre Photography',
        'location' => 'San José de <PERSON>coa, Dom. Rep. / USA',
        'email' => '<EMAIL>',
        'phone' => '************',
        'websites' => array(
            'josetusabe.com',
            'soloylibre.com',
            '1and1photo.com',
            'joselencarnacion.com'
        )
    );

    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // REST API initialization
        add_action('rest_api_init', array($this, 'register_api_routes'));

        // Authentication hooks
        add_filter('rest_authentication_errors', array($this, 'custom_authentication'));
        add_action('rest_api_init', array($this, 'add_cors_support'));

        // Rate limiting
        add_action('rest_api_init', array($this, 'init_rate_limiting'));

        // API logging
        add_action('rest_pre_dispatch', array($this, 'log_api_request'), 10, 3);
        add_action('rest_post_dispatch', array($this, 'log_api_response'), 10, 3);

        // Error handling
        add_filter('rest_request_after_callbacks', array($this, 'handle_api_errors'), 10, 3);
    }

    /**
     * Register API routes
     */
    public function register_api_routes() {
        // Photos endpoints
        $this->register_photos_routes();

        // Albums endpoints
        $this->register_albums_routes();

        // Analytics endpoints
        $this->register_analytics_routes();

        // User management endpoints
        $this->register_user_routes();

        // System endpoints
        $this->register_system_routes();

        // AI endpoints
        $this->register_ai_routes();
    }

    /**
     * Register photos routes
     */
    private function register_photos_routes() {
        // Get photos
        register_rest_route(self::API_NAMESPACE, '/photos', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_photos'),
            'permission_callback' => array($this, 'check_read_permission'),
            'args' => array(
                'page' => array(
                    'default' => 1,
                    'sanitize_callback' => 'absint'
                ),
                'per_page' => array(
                    'default' => 20,
                    'sanitize_callback' => 'absint'
                ),
                'state' => array(
                    'default' => 'all',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'search' => array(
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'orderby' => array(
                    'default' => 'date',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'order' => array(
                    'default' => 'DESC',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Get single photo
        register_rest_route(self::API_NAMESPACE, '/photos/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_photo'),
            'permission_callback' => array($this, 'check_read_permission'),
            'args' => array(
                'id' => array(
                    'sanitize_callback' => 'absint'
                )
            )
        ));

        // Create photo
        register_rest_route(self::API_NAMESPACE, '/photos', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_photo'),
            'permission_callback' => array($this, 'check_write_permission'),
            'args' => array(
                'title' => array(
                    'required' => true,
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'description' => array(
                    'sanitize_callback' => 'sanitize_textarea_field'
                ),
                'state' => array(
                    'default' => 'private',
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'file' => array(
                    'required' => true
                )
            )
        ));

        // Update photo
        register_rest_route(self::API_NAMESPACE, '/photos/(?P<id>\d+)', array(
            'methods' => 'PUT',
            'callback' => array($this, 'update_photo'),
            'permission_callback' => array($this, 'check_write_permission'),
            'args' => array(
                'id' => array(
                    'sanitize_callback' => 'absint'
                ),
                'title' => array(
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'description' => array(
                    'sanitize_callback' => 'sanitize_textarea_field'
                ),
                'state' => array(
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Delete photo
        register_rest_route(self::API_NAMESPACE, '/photos/(?P<id>\d+)', array(
            'methods' => 'DELETE',
            'callback' => array($this, 'delete_photo'),
            'permission_callback' => array($this, 'check_delete_permission'),
            'args' => array(
                'id' => array(
                    'sanitize_callback' => 'absint'
                )
            )
        ));

        // Bulk operations
        register_rest_route(self::API_NAMESPACE, '/photos/bulk', array(
            'methods' => 'POST',
            'callback' => array($this, 'bulk_photo_operations'),
            'permission_callback' => array($this, 'check_write_permission'),
            'args' => array(
                'action' => array(
                    'required' => true,
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'photo_ids' => array(
                    'required' => true,
                    'sanitize_callback' => array($this, 'sanitize_photo_ids')
                ),
                'data' => array(
                    'sanitize_callback' => array($this, 'sanitize_bulk_data')
                )
            )
        ));

        // Photo reactions
        register_rest_route(self::API_NAMESPACE, '/photos/(?P<id>\d+)/reactions', array(
            'methods' => 'POST',
            'callback' => array($this, 'add_photo_reaction'),
            'permission_callback' => array($this, 'check_read_permission'),
            'args' => array(
                'id' => array(
                    'sanitize_callback' => 'absint'
                ),
                'reaction' => array(
                    'required' => true,
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));
    }

    /**
     * Register albums routes
     */
    private function register_albums_routes() {
        // Get albums
        register_rest_route(self::API_NAMESPACE, '/albums', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_albums'),
            'permission_callback' => array($this, 'check_read_permission'),
            'args' => array(
                'page' => array(
                    'default' => 1,
                    'sanitize_callback' => 'absint'
                ),
                'per_page' => array(
                    'default' => 10,
                    'sanitize_callback' => 'absint'
                ),
                'search' => array(
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Get single album
        register_rest_route(self::API_NAMESPACE, '/albums/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_album'),
            'permission_callback' => array($this, 'check_read_permission'),
            'args' => array(
                'id' => array(
                    'required' => true,
                    'sanitize_callback' => 'absint'
                )
            )
        ));

        // Create album
        register_rest_route(self::API_NAMESPACE, '/albums', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_album'),
            'permission_callback' => array($this, 'check_edit_permission'),
            'args' => array(
                'name' => array(
                    'required' => true,
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'description' => array(
                    'sanitize_callback' => 'sanitize_textarea_field'
                ),
                'state' => array(
                    'default' => 'public',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Update album
        register_rest_route(self::API_NAMESPACE, '/albums/(?P<id>\d+)', array(
            'methods' => 'PUT',
            'callback' => array($this, 'update_album'),
            'permission_callback' => array($this, 'check_edit_permission'),
            'args' => array(
                'id' => array(
                    'required' => true,
                    'sanitize_callback' => 'absint'
                ),
                'name' => array(
                    'sanitize_callback' => 'sanitize_text_field'
                ),
                'description' => array(
                    'sanitize_callback' => 'sanitize_textarea_field'
                ),
                'state' => array(
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Delete album
        register_rest_route(self::API_NAMESPACE, '/albums/(?P<id>\d+)', array(
            'methods' => 'DELETE',
            'callback' => array($this, 'delete_album'),
            'permission_callback' => array($this, 'check_edit_permission'),
            'args' => array(
                'id' => array(
                    'required' => true,
                    'sanitize_callback' => 'absint'
                )
            )
        ));
    }

    /**
     * Register analytics routes
     */
    private function register_analytics_routes() {
        // Dashboard stats
        register_rest_route(self::API_NAMESPACE, '/analytics/dashboard', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_dashboard_stats'),
            'permission_callback' => array($this, 'check_read_permission')
        ));

        // Photo engagement stats
        register_rest_route(self::API_NAMESPACE, '/analytics/engagement', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_engagement_stats'),
            'permission_callback' => array($this, 'check_read_permission'),
            'args' => array(
                'period' => array(
                    'default' => '30days',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Performance metrics
        register_rest_route(self::API_NAMESPACE, '/analytics/performance', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_performance_metrics'),
            'permission_callback' => array($this, 'check_admin_permission')
        ));
    }

    /**
     * Register system routes
     */
    private function register_system_routes() {
        // System status
        register_rest_route(self::API_NAMESPACE, '/system/status', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_system_status'),
            'permission_callback' => array($this, 'check_admin_permission')
        ));

        // Photographer info
        register_rest_route(self::API_NAMESPACE, '/system/photographer', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_photographer_info'),
            'permission_callback' => '__return_true' // Public endpoint
        ));

        // Health check
        register_rest_route(self::API_NAMESPACE, '/system/health', array(
            'methods' => 'GET',
            'callback' => array($this, 'health_check'),
            'permission_callback' => '__return_true'
        ));
    }

    /**
     * Get photos endpoint
     */
    public function get_photos($request) {
        try {
            $params = $request->get_params();

            // Build query arguments
            $args = array(
                'post_type' => 'soloylibre_photo',
                'post_status' => 'publish',
                'posts_per_page' => min($params['per_page'], 100), // Max 100 per request
                'paged' => $params['page'],
                'orderby' => $params['orderby'],
                'order' => $params['order']
            );

            // Add state filter
            if ($params['state'] !== 'all') {
                $args['meta_query'] = array(
                    array(
                        'key' => '_soloylibre_photo_state',
                        'value' => $params['state'],
                        'compare' => '='
                    )
                );
            }

            // Add search filter
            if (!empty($params['search'])) {
                $args['s'] = $params['search'];
            }

            $query = new WP_Query($args);
            $photos = array();

            foreach ($query->posts as $post) {
                $photos[] = $this->format_photo_response($post);
            }

            return new WP_REST_Response(array(
                'success' => true,
                'data' => $photos,
                'pagination' => array(
                    'page' => $params['page'],
                    'per_page' => $params['per_page'],
                    'total' => $query->found_posts,
                    'total_pages' => $query->max_num_pages
                ),
                'photographer' => $this->photographer_info
            ), 200);

        } catch (Exception $e) {
            return $this->error_response('Error retrieving photos: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get dashboard stats
     */
    public function get_dashboard_stats($request) {
        try {
            global $wpdb;

            // Get photo counts by state
            $photo_counts = $wpdb->get_results("
                SELECT
                    COALESCE(pm.meta_value, 'unknown') as state,
                    COUNT(*) as count
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_soloylibre_photo_state'
                WHERE p.post_type = 'soloylibre_photo'
                AND p.post_status = 'publish'
                GROUP BY pm.meta_value
            ");

            $stats = array(
                'total_photos' => 0,
                'public_photos' => 0,
                'private_photos' => 0,
                'personal_photos' => 0,
                'trash_photos' => 0,
                'total_reactions' => 0,
                'engagement_rate' => 0,
                'storage_used' => $this->get_storage_usage(),
                'last_updated' => current_time('mysql')
            );

            foreach ($photo_counts as $count) {
                $stats['total_photos'] += $count->count;

                switch ($count->state) {
                    case 'public':
                        $stats['public_photos'] = $count->count;
                        break;
                    case 'private':
                        $stats['private_photos'] = $count->count;
                        break;
                    case 'personal':
                        $stats['personal_photos'] = $count->count;
                        break;
                    case 'trash':
                        $stats['trash_photos'] = $count->count;
                        break;
                }
            }

            // Get total reactions
            $total_reactions = $wpdb->get_var("
                SELECT SUM(CAST(meta_value AS UNSIGNED))
                FROM {$wpdb->postmeta}
                WHERE meta_key = '_soloylibre_total_reactions'
            ");

            $stats['total_reactions'] = intval($total_reactions);

            // Calculate engagement rate
            if ($stats['total_photos'] > 0) {
                $stats['engagement_rate'] = round(($stats['total_reactions'] / $stats['total_photos']) * 100, 2);
            }

            return new WP_REST_Response(array(
                'success' => true,
                'data' => $stats,
                'photographer' => $this->photographer_info,
                'server_info' => array(
                    'server' => 'Synology RS3618xs',
                    'memory' => '56GB RAM',
                    'storage' => '36TB',
                    'location' => 'San José de Ocoa, Dom. Rep. / USA'
                )
            ), 200);

        } catch (Exception $e) {
            return $this->error_response('Error retrieving dashboard stats: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get photographer info
     */
    public function get_photographer_info($request) {
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $this->photographer_info,
            'brand_message' => 'Capturing Dreams, Creating Memories',
            'specialties' => array(
                'Portrait Photography',
                'Landscape Photography',
                'Event Photography',
                'Commercial Photography',
                'Digital Art'
            ),
            'experience' => array(
                'years' => '10+',
                'projects' => '500+',
                'clients' => '200+',
                'awards' => '15+'
            )
        ), 200);
    }

    /**
     * Health check endpoint
     */
    public function health_check($request) {
        $health_status = array(
            'status' => 'healthy',
            'timestamp' => current_time('c'),
            'version' => SOLOYLIBRE_GALLERY_VERSION,
            'photographer' => $this->photographer_info['alias'],
            'checks' => array(
                'database' => $this->check_database_health(),
                'storage' => $this->check_storage_health(),
                'memory' => $this->check_memory_health(),
                'api' => 'operational'
            )
        );

        $overall_status = 200;
        foreach ($health_status['checks'] as $check => $status) {
            if ($status !== 'healthy' && $status !== 'operational') {
                $health_status['status'] = 'degraded';
                $overall_status = 503;
                break;
            }
        }

        return new WP_REST_Response($health_status, $overall_status);
    }

    /**
     * Format photo response
     */
    private function format_photo_response($post) {
        $photo_data = array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'description' => $post->post_content,
            'date_created' => $post->post_date,
            'date_modified' => $post->post_modified,
            'state' => get_post_meta($post->ID, '_soloylibre_photo_state', true) ?: 'private',
            'is_publishable' => get_post_meta($post->ID, '_soloylibre_is_publishable', true) === '1',
            'is_reviewed' => get_post_meta($post->ID, '_soloylibre_is_reviewed', true) === '1',
            'reactions' => array(
                'total' => intval(get_post_meta($post->ID, '_soloylibre_total_reactions', true)),
                'like' => intval(get_post_meta($post->ID, '_soloylibre_reactions_like', true)),
                'love' => intval(get_post_meta($post->ID, '_soloylibre_reactions_love', true)),
                'wow' => intval(get_post_meta($post->ID, '_soloylibre_reactions_wow', true)),
                'amazing' => intval(get_post_meta($post->ID, '_soloylibre_reactions_amazing', true)),
                'fire' => intval(get_post_meta($post->ID, '_soloylibre_reactions_fire', true)),
                'camera' => intval(get_post_meta($post->ID, '_soloylibre_reactions_camera', true))
            ),
            'metadata' => array(
                'photographer' => get_post_meta($post->ID, '_soloylibre_photographer', true) ?: $this->photographer_info['name'],
                'brand' => get_post_meta($post->ID, '_soloylibre_brand', true) ?: $this->photographer_info['brand'],
                'location' => get_post_meta($post->ID, '_soloylibre_location', true) ?: $this->photographer_info['location'],
                'file_size' => get_post_meta($post->ID, '_soloylibre_file_size', true),
                'dimensions' => get_post_meta($post->ID, '_soloylibre_dimensions', true)
            )
        );

        // Get featured image
        $thumbnail_id = get_post_thumbnail_id($post->ID);
        if ($thumbnail_id) {
            $photo_data['images'] = array(
                'thumbnail' => wp_get_attachment_image_url($thumbnail_id, 'thumbnail'),
                'medium' => wp_get_attachment_image_url($thumbnail_id, 'medium'),
                'large' => wp_get_attachment_image_url($thumbnail_id, 'large'),
                'full' => wp_get_attachment_image_url($thumbnail_id, 'full')
            );
        }

        return $photo_data;
    }

    /**
     * Permission callbacks
     */
    public function check_read_permission($request) {
        return current_user_can('read');
    }

    public function check_write_permission($request) {
        return current_user_can('edit_posts');
    }

    public function check_delete_permission($request) {
        return current_user_can('delete_posts');
    }

    public function check_admin_permission($request) {
        return current_user_can('manage_options');
    }

    /**
     * Error response helper
     */
    private function error_response($message, $status_code = 400) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'message' => $message,
                'code' => $status_code,
                'timestamp' => current_time('c'),
                'photographer' => $this->photographer_info['alias']
            )
        ), $status_code);
    }

    /**
     * Health check helpers
     */
    private function check_database_health() {
        global $wpdb;
        try {
            $wpdb->get_var("SELECT 1");
            return 'healthy';
        } catch (Exception $e) {
            return 'unhealthy';
        }
    }

    private function check_storage_health() {
        $upload_dir = wp_upload_dir();
        return is_writable($upload_dir['basedir']) ? 'healthy' : 'unhealthy';
    }

    private function check_memory_health() {
        $memory_limit = ini_get('memory_limit');
        $memory_usage = memory_get_usage(true);
        $memory_limit_bytes = $this->convert_to_bytes($memory_limit);

        $usage_percentage = ($memory_usage / $memory_limit_bytes) * 100;

        if ($usage_percentage > 90) {
            return 'critical';
        } elseif ($usage_percentage > 75) {
            return 'warning';
        } else {
            return 'healthy';
        }
    }

    private function get_storage_usage() {
        $upload_dir = wp_upload_dir();
        $size = 0;

        if (is_dir($upload_dir['basedir'])) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($upload_dir['basedir'])
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }

        return $this->format_bytes($size);
    }

    private function convert_to_bytes($value) {
        $unit = strtolower(substr($value, -1));
        $value = (int) $value;

        switch ($unit) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    private function format_bytes($size, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }

        return round($size, $precision) . ' ' . $units[$i];
    }

    /**
     * CORS support
     */
    public function add_cors_support() {
        remove_filter('rest_pre_serve_request', 'rest_send_cors_headers');
        add_filter('rest_pre_serve_request', array($this, 'custom_cors_headers'));
    }

    public function custom_cors_headers($value) {
        $allowed_origins = array(
            'https://josetusabe.com',
            'https://soloylibre.com',
            'https://1and1photo.com',
            'https://joselencarnacion.com',
            'http://localhost:8080'
        );

        $origin = get_http_origin();

        if (in_array($origin, $allowed_origins)) {
            header('Access-Control-Allow-Origin: ' . $origin);
        }

        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Authorization, Content-Type, X-WP-Nonce');
        header('Access-Control-Allow-Credentials: true');

        return $value;
    }

    /**
     * Get albums endpoint
     */
    public function get_albums($request) {
        try {
            global $wpdb;
            $params = $request->get_params();

            $page = isset($params['page']) ? max(1, intval($params['page'])) : 1;
            $per_page = isset($params['per_page']) ? max(1, min(100, intval($params['per_page']))) : 10;
            $offset = ($page - 1) * $per_page;

            $where_clause = "WHERE 1=1";
            $search_params = array();

            if (!empty($params['search'])) {
                $where_clause .= " AND (name LIKE %s OR description LIKE %s)";
                $search_term = '%' . $wpdb->esc_like($params['search']) . '%';
                $search_params[] = $search_term;
                $search_params[] = $search_term;
            }

            // Get total count
            $count_query = "SELECT COUNT(*) FROM {$wpdb->prefix}soloylibre_albums $where_clause";
            if (!empty($search_params)) {
                $count_query = $wpdb->prepare($count_query, $search_params);
            }
            $total = $wpdb->get_var($count_query);

            // Get albums
            $query = "SELECT * FROM {$wpdb->prefix}soloylibre_albums $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d";
            $query_params = array_merge($search_params, array($per_page, $offset));
            $albums = $wpdb->get_results($wpdb->prepare($query, $query_params));

            $formatted_albums = array();
            foreach ($albums as $album) {
                $formatted_albums[] = $this->format_album_response($album);
            }

            return new WP_REST_Response(array(
                'success' => true,
                'data' => $formatted_albums,
                'pagination' => array(
                    'page' => $page,
                    'per_page' => $per_page,
                    'total' => intval($total),
                    'total_pages' => ceil($total / $per_page)
                )
            ), 200);

        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => 'Error retrieving albums: ' . $e->getMessage()
            ), 500);
        }
    }

    /**
     * Get single album endpoint
     */
    public function get_album($request) {
        try {
            global $wpdb;
            $album_id = $request->get_param('id');

            $album = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}soloylibre_albums WHERE id = %d",
                $album_id
            ));

            if (!$album) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'message' => 'Album not found'
                ), 404);
            }

            return new WP_REST_Response(array(
                'success' => true,
                'data' => $this->format_album_response($album)
            ), 200);

        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => 'Error retrieving album: ' . $e->getMessage()
            ), 500);
        }
    }

    /**
     * Create album endpoint
     */
    public function create_album($request) {
        try {
            global $wpdb;
            $params = $request->get_params();

            $result = $wpdb->insert(
                $wpdb->prefix . 'soloylibre_albums',
                array(
                    'name' => $params['name'],
                    'description' => isset($params['description']) ? $params['description'] : '',
                    'state' => isset($params['state']) ? $params['state'] : 'public',
                    'photographer_id' => get_current_user_id(),
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ),
                array('%s', '%s', '%s', '%d', '%s', '%s')
            );

            if ($result === false) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'message' => 'Failed to create album'
                ), 500);
            }

            $album_id = $wpdb->insert_id;
            $album = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}soloylibre_albums WHERE id = %d",
                $album_id
            ));

            return new WP_REST_Response(array(
                'success' => true,
                'message' => 'Album created successfully',
                'data' => $this->format_album_response($album)
            ), 201);

        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => 'Error creating album: ' . $e->getMessage()
            ), 500);
        }
    }

    /**
     * Update album endpoint
     */
    public function update_album($request) {
        try {
            global $wpdb;
            $album_id = $request->get_param('id');
            $params = $request->get_params();

            // Check if album exists
            $album = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}soloylibre_albums WHERE id = %d",
                $album_id
            ));

            if (!$album) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'message' => 'Album not found'
                ), 404);
            }

            $update_data = array('updated_at' => current_time('mysql'));
            $update_format = array('%s');

            if (isset($params['name'])) {
                $update_data['name'] = $params['name'];
                $update_format[] = '%s';
            }

            if (isset($params['description'])) {
                $update_data['description'] = $params['description'];
                $update_format[] = '%s';
            }

            if (isset($params['state'])) {
                $update_data['state'] = $params['state'];
                $update_format[] = '%s';
            }

            $result = $wpdb->update(
                $wpdb->prefix . 'soloylibre_albums',
                $update_data,
                array('id' => $album_id),
                $update_format,
                array('%d')
            );

            if ($result === false) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'message' => 'Failed to update album'
                ), 500);
            }

            $updated_album = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}soloylibre_albums WHERE id = %d",
                $album_id
            ));

            return new WP_REST_Response(array(
                'success' => true,
                'message' => 'Album updated successfully',
                'data' => $this->format_album_response($updated_album)
            ), 200);

        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => 'Error updating album: ' . $e->getMessage()
            ), 500);
        }
    }

    /**
     * Delete album endpoint
     */
    public function delete_album($request) {
        try {
            global $wpdb;
            $album_id = $request->get_param('id');

            // Check if album exists
            $album = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}soloylibre_albums WHERE id = %d",
                $album_id
            ));

            if (!$album) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'message' => 'Album not found'
                ), 404);
            }

            $result = $wpdb->delete(
                $wpdb->prefix . 'soloylibre_albums',
                array('id' => $album_id),
                array('%d')
            );

            if ($result === false) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'message' => 'Failed to delete album'
                ), 500);
            }

            return new WP_REST_Response(array(
                'success' => true,
                'message' => 'Album deleted successfully'
            ), 200);

        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'message' => 'Error deleting album: ' . $e->getMessage()
            ), 500);
        }
    }

    /**
     * Format album response
     */
    private function format_album_response($album) {
        global $wpdb;

        // Get photo count for this album
        $photo_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}soloylibre_photos WHERE album_id = %d",
            $album->id
        ));

        return array(
            'id' => intval($album->id),
            'name' => $album->name,
            'description' => $album->description,
            'state' => $album->state,
            'photo_count' => intval($photo_count),
            'cover_photo_id' => $album->cover_photo_id ? intval($album->cover_photo_id) : null,
            'photographer_id' => intval($album->photographer_id),
            'created_at' => $album->created_at,
            'updated_at' => $album->updated_at
        );
    }
}