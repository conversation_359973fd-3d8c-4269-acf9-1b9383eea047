<?php
/**
 * Bulk Photo Loader Class
 * Sistema de carga masiva de fotos con optimización de rendimiento
 * Desarrollado por JEYKO AI para <PERSON>carnac<PERSON> (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Bulk_Photo_Loader {
    
    /**
     * Configuración por defecto
     */
    private $default_settings = array(
        'max_photos_per_batch' => 50,
        'max_execution_time' => 25,
        'memory_limit_mb' => 128,
        'supported_formats' => array('jpg', 'jpeg', 'png', 'gif', 'webp'),
        'thumbnail_size' => array(300, 300),
        'chunk_size' => 10
    );
    
    /**
     * Directorio de fotos por defecto
     */
    private $photos_directory;
    
    public function __construct() {
        $this->photos_directory = wp_upload_dir()['basedir'] . '/soloylibre-photos/';
        $this->init_hooks();
        $this->ensure_directories();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX handlers
        add_action('wp_ajax_bulk_load_photos', array($this, 'ajax_bulk_load_photos'));
        add_action('wp_ajax_scan_photo_directory', array($this, 'ajax_scan_photo_directory'));
        add_action('wp_ajax_process_photo_batch', array($this, 'ajax_process_photo_batch'));
        add_action('wp_ajax_get_load_progress', array($this, 'ajax_get_load_progress'));
        add_action('wp_ajax_cancel_bulk_load', array($this, 'ajax_cancel_bulk_load'));
        add_action('wp_ajax_reset_photo_tracking', array($this, 'ajax_reset_photo_tracking'));
        
        // Scheduled events
        add_action('soloylibre_cleanup_temp_files', array($this, 'cleanup_temp_files'));
        
        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('soloylibre_cleanup_temp_files')) {
            wp_schedule_event(time(), 'daily', 'soloylibre_cleanup_temp_files');
        }
    }
    
    /**
     * Ensure required directories exist
     */
    private function ensure_directories() {
        $directories = array(
            $this->photos_directory,
            $this->photos_directory . 'processed/',
            $this->photos_directory . 'thumbnails/',
            $this->photos_directory . 'temp/',
            wp_upload_dir()['basedir'] . '/soloylibre-cache/'
        );
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                wp_mkdir_p($dir);
                
                // Add .htaccess for security
                $htaccess_content = "Options -Indexes\nDeny from all\n";
                file_put_contents($dir . '.htaccess', $htaccess_content);
            }
        }
    }
    
    /**
     * Scan photo directory for available photos
     */
    public function scan_photo_directory($directory = null, $recursive = true) {
        if (!$directory) {
            $directory = $this->photos_directory;
        }
        
        $photos = array();
        $processed_photos = $this->get_processed_photos_list();
        
        if (!is_dir($directory)) {
            return $photos;
        }
        
        $iterator = $recursive ? 
            new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory)) :
            new DirectoryIterator($directory);
        
        foreach ($iterator as $file) {
            if ($file->isDot() || $file->isDir()) {
                continue;
            }
            
            $file_path = $file->getPathname();
            $file_extension = strtolower($file->getExtension());
            
            // Check if file is supported format
            if (!in_array($file_extension, $this->default_settings['supported_formats'])) {
                continue;
            }
            
            // Skip if already processed
            $file_hash = md5($file_path . $file->getMTime());
            if (in_array($file_hash, $processed_photos)) {
                continue;
            }
            
            $photos[] = array(
                'path' => $file_path,
                'name' => $file->getBasename(),
                'size' => $file->getSize(),
                'modified' => $file->getMTime(),
                'hash' => $file_hash,
                'extension' => $file_extension,
                'relative_path' => str_replace($this->photos_directory, '', $file_path)
            );
        }
        
        return $photos;
    }
    
    /**
     * Get list of already processed photos
     */
    private function get_processed_photos_list() {
        return get_option('soloylibre_processed_photos', array());
    }
    
    /**
     * Add photo to processed list
     */
    private function mark_photo_as_processed($photo_hash) {
        $processed = $this->get_processed_photos_list();
        if (!in_array($photo_hash, $processed)) {
            $processed[] = $photo_hash;
            update_option('soloylibre_processed_photos', $processed);
        }
    }
    
    /**
     * Load photos in batches with progress tracking
     */
    public function bulk_load_photos($options = array()) {
        $settings = wp_parse_args($options, $this->default_settings);
        
        // Set memory and time limits
        ini_set('memory_limit', $settings['memory_limit_mb'] . 'M');
        set_time_limit($settings['max_execution_time']);
        
        // Scan for available photos
        $available_photos = $this->scan_photo_directory();
        
        if (empty($available_photos)) {
            return array(
                'success' => false,
                'message' => 'No se encontraron fotos nuevas para procesar'
            );
        }
        
        // Apply sorting
        $available_photos = $this->sort_photos($available_photos, $settings['sort_order'] ?? 'random');
        
        // Limit photos
        $max_photos = min($settings['max_photos_per_batch'], count($available_photos));
        $photos_to_process = array_slice($available_photos, 0, $max_photos);
        
        // Initialize progress tracking
        $batch_id = uniqid('batch_');
        $this->init_batch_progress($batch_id, $photos_to_process);
        
        return array(
            'success' => true,
            'batch_id' => $batch_id,
            'total_photos' => count($photos_to_process),
            'message' => "Iniciando procesamiento de {$max_photos} fotos"
        );
    }
    
    /**
     * Sort photos array
     */
    private function sort_photos($photos, $sort_order) {
        switch ($sort_order) {
            case 'random':
                shuffle($photos);
                break;
            case 'asc':
                usort($photos, function($a, $b) {
                    return strcmp($a['name'], $b['name']);
                });
                break;
            case 'desc':
                usort($photos, function($a, $b) {
                    return strcmp($b['name'], $a['name']);
                });
                break;
            case 'newest':
                usort($photos, function($a, $b) {
                    return $b['modified'] - $a['modified'];
                });
                break;
            case 'oldest':
                usort($photos, function($a, $b) {
                    return $a['modified'] - $b['modified'];
                });
                break;
            case 'largest':
                usort($photos, function($a, $b) {
                    return $b['size'] - $a['size'];
                });
                break;
            case 'smallest':
                usort($photos, function($a, $b) {
                    return $a['size'] - $b['size'];
                });
                break;
        }
        
        return $photos;
    }
    
    /**
     * Initialize batch progress tracking
     */
    private function init_batch_progress($batch_id, $photos) {
        $progress_data = array(
            'batch_id' => $batch_id,
            'total_photos' => count($photos),
            'processed_photos' => 0,
            'failed_photos' => 0,
            'start_time' => time(),
            'status' => 'processing',
            'photos' => $photos,
            'current_chunk' => 0,
            'chunks_total' => ceil(count($photos) / $this->default_settings['chunk_size'])
        );
        
        set_transient('soloylibre_batch_' . $batch_id, $progress_data, HOUR_IN_SECONDS);
        
        return $progress_data;
    }
    
    /**
     * Process a chunk of photos
     */
    public function process_photo_chunk($batch_id, $chunk_index = 0) {
        $progress_data = get_transient('soloylibre_batch_' . $batch_id);
        
        if (!$progress_data) {
            return array(
                'success' => false,
                'message' => 'Batch no encontrado o expirado'
            );
        }
        
        $chunk_size = $this->default_settings['chunk_size'];
        $start_index = $chunk_index * $chunk_size;
        $photos_chunk = array_slice($progress_data['photos'], $start_index, $chunk_size);
        
        $processed_count = 0;
        $failed_count = 0;
        $processed_photos = array();
        
        foreach ($photos_chunk as $photo) {
            $result = $this->process_single_photo($photo);
            
            if ($result['success']) {
                $processed_count++;
                $processed_photos[] = $result['photo_data'];
                $this->mark_photo_as_processed($photo['hash']);
            } else {
                $failed_count++;
                error_log('SoloYLibre: Error procesando foto ' . $photo['path'] . ': ' . $result['message']);
            }
            
            // Prevent timeout
            if (time() - $progress_data['start_time'] > ($this->default_settings['max_execution_time'] - 5)) {
                break;
            }
        }
        
        // Update progress
        $progress_data['processed_photos'] += $processed_count;
        $progress_data['failed_photos'] += $failed_count;
        $progress_data['current_chunk'] = $chunk_index + 1;
        
        // Check if batch is complete
        if ($progress_data['current_chunk'] >= $progress_data['chunks_total']) {
            $progress_data['status'] = 'completed';
            $progress_data['end_time'] = time();
        }
        
        set_transient('soloylibre_batch_' . $batch_id, $progress_data, HOUR_IN_SECONDS);
        
        return array(
            'success' => true,
            'processed_count' => $processed_count,
            'failed_count' => $failed_count,
            'processed_photos' => $processed_photos,
            'progress' => $progress_data
        );
    }
    
    /**
     * Process a single photo
     */
    private function process_single_photo($photo) {
        try {
            // Validate file exists and is readable
            if (!file_exists($photo['path']) || !is_readable($photo['path'])) {
                return array(
                    'success' => false,
                    'message' => 'Archivo no encontrado o no legible'
                );
            }
            
            // Get image info
            $image_info = getimagesize($photo['path']);
            if (!$image_info) {
                return array(
                    'success' => false,
                    'message' => 'No es una imagen válida'
                );
            }
            
            // Create WordPress post
            $post_data = array(
                'post_title' => $this->generate_photo_title($photo),
                'post_content' => $this->generate_photo_description($photo),
                'post_status' => 'draft', // Start as draft for review
                'post_type' => 'soloylibre_photo',
                'post_author' => get_current_user_id() ?: 1
            );
            
            $post_id = wp_insert_post($post_data);
            
            if (is_wp_error($post_id)) {
                return array(
                    'success' => false,
                    'message' => 'Error creando post: ' . $post_id->get_error_message()
                );
            }
            
            // Copy file to WordPress uploads
            $upload_result = $this->copy_to_uploads($photo, $post_id);
            
            if (!$upload_result['success']) {
                wp_delete_post($post_id, true);
                return $upload_result;
            }
            
            // Set as featured image
            set_post_thumbnail($post_id, $upload_result['attachment_id']);
            
            // Add metadata
            $this->add_photo_metadata($post_id, $photo, $image_info);
            
            // Generate thumbnail
            $this->generate_thumbnail($upload_result['file_path'], $post_id);
            
            return array(
                'success' => true,
                'photo_data' => array(
                    'id' => $post_id,
                    'title' => $post_data['post_title'],
                    'thumbnail' => wp_get_attachment_image_url($upload_result['attachment_id'], 'thumbnail'),
                    'state' => 'private', // Default state
                    'file_size' => $photo['size'],
                    'dimensions' => $image_info[0] . 'x' . $image_info[1]
                )
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Excepción: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Generate photo title from filename
     */
    private function generate_photo_title($photo) {
        $title = pathinfo($photo['name'], PATHINFO_FILENAME);
        $title = str_replace(array('-', '_'), ' ', $title);
        $title = ucwords(strtolower($title));
        
        // Add photographer signature
        $title .= ' - SoloYLibre Photography';
        
        return $title;
    }
    
    /**
     * Generate photo description
     */
    private function generate_photo_description($photo) {
        $descriptions = array(
            'Fotografía profesional capturada por Jose L Encarnacion (JoseTusabe)',
            'Imagen de la colección SoloYLibre Photography',
            'Captura fotográfica profesional desde San José de Ocoa, República Dominicana',
            'Fotografía artística de la marca SoloYLibre',
            'Imagen profesional por JoseTusabe Photography'
        );
        
        return $descriptions[array_rand($descriptions)];
    }
    
    /**
     * Copy photo to WordPress uploads directory
     */
    private function copy_to_uploads($photo, $post_id) {
        $upload_dir = wp_upload_dir();
        $filename = 'soloylibre_' . $post_id . '_' . sanitize_file_name($photo['name']);
        $upload_path = $upload_dir['path'] . '/' . $filename;
        $upload_url = $upload_dir['url'] . '/' . $filename;
        
        // Copy file
        if (!copy($photo['path'], $upload_path)) {
            return array(
                'success' => false,
                'message' => 'Error copiando archivo'
            );
        }
        
        // Create attachment
        $attachment = array(
            'guid' => $upload_url,
            'post_mime_type' => wp_check_filetype($filename)['type'],
            'post_title' => preg_replace('/\.[^.]+$/', '', $filename),
            'post_content' => '',
            'post_status' => 'inherit',
            'post_parent' => $post_id
        );
        
        $attachment_id = wp_insert_attachment($attachment, $upload_path, $post_id);
        
        if (is_wp_error($attachment_id)) {
            unlink($upload_path);
            return array(
                'success' => false,
                'message' => 'Error creando attachment'
            );
        }
        
        // Generate attachment metadata
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attachment_data = wp_generate_attachment_metadata($attachment_id, $upload_path);
        wp_update_attachment_metadata($attachment_id, $attachment_data);
        
        return array(
            'success' => true,
            'attachment_id' => $attachment_id,
            'file_path' => $upload_path,
            'file_url' => $upload_url
        );
    }
    
    /**
     * Add photo metadata
     */
    private function add_photo_metadata($post_id, $photo, $image_info) {
        // Basic metadata
        update_post_meta($post_id, '_soloylibre_photo_state', 'private');
        update_post_meta($post_id, '_soloylibre_original_path', $photo['path']);
        update_post_meta($post_id, '_soloylibre_file_hash', $photo['hash']);
        update_post_meta($post_id, '_soloylibre_file_size', $photo['size']);
        update_post_meta($post_id, '_soloylibre_dimensions', $image_info[0] . 'x' . $image_info[1]);
        update_post_meta($post_id, '_soloylibre_import_date', current_time('mysql'));
        
        // Photographer info
        update_post_meta($post_id, '_soloylibre_photographer', 'Jose L Encarnacion');
        update_post_meta($post_id, '_soloylibre_photographer_alias', 'JoseTusabe');
        update_post_meta($post_id, '_soloylibre_brand', 'SoloYLibre Photography');
        update_post_meta($post_id, '_soloylibre_location', 'San José de Ocoa, Dom. Rep. / USA');
        
        // Protection settings
        update_post_meta($post_id, '_soloylibre_is_publishable', '0');
        update_post_meta($post_id, '_soloylibre_is_reviewed', '0');
        update_post_meta($post_id, '_soloylibre_protection_level', 'manual_review');
        
        // Initialize interaction counters
        update_post_meta($post_id, '_soloylibre_total_reactions', 0);
        foreach (array('like', 'love', 'wow', 'amazing', 'fire', 'camera') as $reaction) {
            update_post_meta($post_id, '_soloylibre_reactions_' . $reaction, 0);
        }
    }
    
    /**
     * Generate thumbnail
     */
    private function generate_thumbnail($file_path, $post_id) {
        $thumbnail_dir = $this->photos_directory . 'thumbnails/';
        $thumbnail_path = $thumbnail_dir . 'thumb_' . $post_id . '.jpg';
        
        // Create thumbnail using WordPress image functions
        $image = wp_get_image_editor($file_path);
        
        if (!is_wp_error($image)) {
            $image->resize($this->default_settings['thumbnail_size'][0], $this->default_settings['thumbnail_size'][1], true);
            $image->save($thumbnail_path, 'image/jpeg');
            
            update_post_meta($post_id, '_soloylibre_thumbnail_path', $thumbnail_path);
        }
    }
    
    /**
     * Get batch progress
     */
    public function get_batch_progress($batch_id) {
        return get_transient('soloylibre_batch_' . $batch_id);
    }
    
    /**
     * Cancel batch processing
     */
    public function cancel_batch($batch_id) {
        $progress_data = get_transient('soloylibre_batch_' . $batch_id);
        
        if ($progress_data) {
            $progress_data['status'] = 'cancelled';
            $progress_data['end_time'] = time();
            set_transient('soloylibre_batch_' . $batch_id, $progress_data, HOUR_IN_SECONDS);
        }
        
        return true;
    }
    
    /**
     * Reset photo tracking (for category reset)
     */
    public function reset_photo_tracking($category = 'all') {
        if ($category === 'all') {
            delete_option('soloylibre_processed_photos');
        } else {
            // Reset specific category tracking
            $processed = $this->get_processed_photos_list();
            // Implementation for category-specific reset would go here
            update_option('soloylibre_processed_photos', $processed);
        }
        
        return true;
    }
    
    /**
     * Cleanup temporary files
     */
    public function cleanup_temp_files() {
        $temp_dir = $this->photos_directory . 'temp/';
        $cache_dir = wp_upload_dir()['basedir'] . '/soloylibre-cache/';
        
        $directories = array($temp_dir, $cache_dir);
        
        foreach ($directories as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '*');
                foreach ($files as $file) {
                    if (is_file($file) && (time() - filemtime($file)) > DAY_IN_SECONDS) {
                        unlink($file);
                    }
                }
            }
        }
    }
    
    /**
     * AJAX Handlers
     */
    public function ajax_bulk_load_photos() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $options = array(
            'max_photos_per_batch' => intval($_POST['limit'] ?? 500),
            'sort_order' => sanitize_text_field($_POST['sort_order'] ?? 'random')
        );
        
        $result = $this->bulk_load_photos($options);
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    public function ajax_scan_photo_directory() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $photos = $this->scan_photo_directory();
        
        wp_send_json_success(array(
            'photos' => $photos,
            'count' => count($photos)
        ));
    }
    
    public function ajax_process_photo_batch() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $batch_id = sanitize_text_field($_POST['batch_id']);
        $chunk_index = intval($_POST['chunk_index'] ?? 0);
        
        $result = $this->process_photo_chunk($batch_id, $chunk_index);
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    public function ajax_get_load_progress() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $batch_id = sanitize_text_field($_POST['batch_id']);
        $progress = $this->get_batch_progress($batch_id);
        
        if ($progress) {
            wp_send_json_success($progress);
        } else {
            wp_send_json_error('Batch no encontrado');
        }
    }
    
    public function ajax_cancel_bulk_load() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $batch_id = sanitize_text_field($_POST['batch_id']);
        $this->cancel_batch($batch_id);
        
        wp_send_json_success('Procesamiento cancelado');
    }
    
    public function ajax_reset_photo_tracking() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permisos insuficientes');
        }
        
        $category = sanitize_text_field($_POST['category'] ?? 'all');
        $this->reset_photo_tracking($category);
        
        wp_send_json_success('Tracking reiniciado para: ' . $category);
    }
}
