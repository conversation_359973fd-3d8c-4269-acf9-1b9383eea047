<?php
/**
 * Fullscreen Wizard Class
 * Creates a fullscreen wizard for photo selection and post creation
 * Developed by JEYKO AI for Jose L <PERSON> (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Fullscreen_Wizard {
    
    private $wizard_steps = array(
        'welcome' => 'Bienvenida',
        'select_photos' => 'Seleccionar Fotos',
        'organize' => 'Organizar',
        'settings' => 'Configuración',
        'create_post' => 'Crear Post',
        'complete' => 'Completado'
    );
    
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_wizard_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_wizard_scripts'));
        add_action('wp_ajax_wizard_get_photos', array($this, 'ajax_get_photos'));
        add_action('wp_ajax_wizard_create_post', array($this, 'ajax_create_post'));
        add_action('wp_ajax_wizard_save_step', array($this, 'ajax_save_step'));
        add_action('wp_ajax_wizard_smart_selection', array($this, 'ajax_smart_selection'));
        add_action('wp_ajax_wizard_dominican_styles', array($this, 'ajax_dominican_styles'));
        add_action('wp_ajax_wizard_automatic_posts', array($this, 'ajax_automatic_posts'));
        add_action('wp_ajax_soloylibre_get_all_photos_preview', array($this, 'ajax_get_all_photos_preview'));
        add_action('wp_ajax_soloylibre_create_advanced_auto_post', array($this, 'ajax_create_advanced_auto_post'));
        add_action('wp_ajax_soloylibre_create_category_album', array($this, 'ajax_create_category_album'));
        add_action('wp_ajax_soloylibre_save_photo_categories', array($this, 'ajax_save_photo_categories'));
    }
    
    /**
     * Add wizard menu
     */
    public function add_wizard_menu() {
        add_menu_page(
            'SoloYLibre Wizard',
            '📸 SoloYLibre Wizard',
            'edit_posts',
            'soloylibre-wizard',
            array($this, 'render_wizard'),
            'dashicons-camera',
            25
        );
    }
    
    /**
     * Enqueue wizard scripts
     */
    public function enqueue_wizard_scripts($hook) {
        if ($hook !== 'toplevel_page_soloylibre-wizard') {
            return;
        }
        
        wp_enqueue_style(
            'soloylibre-wizard-style',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/css/fullscreen-wizard.css',
            array(),
            SOLOYLIBRE_GALLERY_VERSION
        );
        
        wp_enqueue_script(
            'soloylibre-wizard-script',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/fullscreen-wizard.js',
            array('jquery', 'wp-util'),
            SOLOYLIBRE_GALLERY_VERSION,
            true
        );
        
        wp_localize_script('soloylibre-wizard-script', 'soloylibre_wizard', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('soloylibre_wizard_nonce'),
            'photographer' => array(
                'name' => 'Jose L Encarnacion',
                'alias' => 'JoseTusabe',
                'brand' => 'JoseTusabe Photography',
                'location' => 'San José de Ocoa, Dom. Rep. / USA',
                'phone' => '************',
                'email' => '<EMAIL>'
            ),
            'texts' => array(
                'loading' => __('Cargando...', 'soloylibre-gallery'),
                'error' => __('Error al procesar la solicitud', 'soloylibre-gallery'),
                'success' => __('¡Éxito!', 'soloylibre-gallery'),
                'select_photos' => __('Selecciona las fotos para tu galería', 'soloylibre-gallery'),
                'creating_post' => __('Creando post con galería...', 'soloylibre-gallery'),
                'post_created' => __('¡Post creado exitosamente!', 'soloylibre-gallery')
            )
        ));
    }
    
    /**
     * Render wizard interface
     */
    public function render_wizard() {
        ?>
        <div id="soloylibre-fullscreen-wizard" class="soloylibre-wizard-container">
            <!-- Wizard Header -->
            <div class="wizard-header">
                <div class="wizard-brand">
                    <h1>📸 SoloYLibre Gallery Wizard</h1>
                    <p>Crea galerías profesionales fácilmente</p>
                </div>
                <div class="wizard-controls">
                    <button id="minimize-wizard" class="wizard-btn wizard-btn-secondary">
                        <span class="dashicons dashicons-minus"></span> Minimizar
                    </button>
                    <button id="close-wizard" class="wizard-btn wizard-btn-danger">
                        <span class="dashicons dashicons-no"></span> Cerrar
                    </button>
                </div>
            </div>
            
            <!-- Progress Bar -->
            <div class="wizard-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="wizard-progress-fill"></div>
                </div>
                <div class="progress-steps">
                    <?php foreach ($this->wizard_steps as $step_key => $step_name): ?>
                        <div class="progress-step" data-step="<?php echo esc_attr($step_key); ?>">
                            <div class="step-circle"></div>
                            <span class="step-label"><?php echo esc_html($step_name); ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Wizard Content -->
            <div class="wizard-content">
                <!-- Step 1: Welcome -->
                <div class="wizard-step active" data-step="welcome">
                    <div class="step-content">
                        <div class="welcome-hero">
                            <div class="dominican-flag">🇩🇴</div>
                            <h2>¡Bienvenido al Wizard de SoloYLibre!</h2>
                            <p class="hero-subtitle">
                                Crea galerías profesionales para <strong>Jose L Encarnacion (JoseTusabe)</strong>
                            </p>
                            <p class="hero-description">
                                Este wizard te ayudará a seleccionar fotos, organizarlas y crear posts de WordPress 
                                con galerías hermosas en pocos pasos.
                            </p>
                        </div>
                        
                        <div class="feature-grid">
                            <div class="feature-card clickable" onclick="openSmartSelection()">
                                <div class="feature-icon">📸</div>
                                <h3>Selección Inteligente</h3>
                                <p>Selecciona fotos de tu biblioteca de medios con vista previa</p>
                                <div class="feature-action">
                                    <button class="btn btn-primary">🎯 Abrir Selector</button>
                                </div>
                            </div>
                            <div class="feature-card clickable" onclick="openDominicanStyles()">
                                <div class="feature-icon">🎨</div>
                                <h3>Estilos Dominicanos</h3>
                                <p>Galerías con temática de República Dominicana</p>
                                <div class="feature-action">
                                    <button class="btn btn-primary">🇩🇴 Ver Estilos</button>
                                </div>
                            </div>
                            <div class="feature-card clickable" onclick="openAutomaticPosts()">
                                <div class="feature-icon">📝</div>
                                <h3>Posts Automáticos</h3>
                                <p>Crea posts de WordPress con galerías integradas</p>
                                <div class="feature-action">
                                    <button class="btn btn-primary">🤖 Crear Post</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 2: Select Photos -->
                <div class="wizard-step" data-step="select_photos">
                    <div class="step-content">
                        <h2>📸 Seleccionar Fotos</h2>
                        <p>Selecciona las fotos que quieres incluir en tu galería</p>
                        
                        <div class="photo-selection-controls">
                            <div class="search-box">
                                <input type="text" id="photo-search" placeholder="Buscar fotos...">
                                <button class="search-btn">🔍</button>
                            </div>
                            <div class="filter-controls">
                                <select id="photo-category-filter">
                                    <option value="">Todas las categorías</option>
                                </select>
                                <select id="photo-date-filter">
                                    <option value="">Todas las fechas</option>
                                    <option value="today">Hoy</option>
                                    <option value="week">Esta semana</option>
                                    <option value="month">Este mes</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="selected-counter">
                            <span id="selected-count">0</span> fotos seleccionadas
                        </div>
                        
                        <div id="photo-grid" class="photo-grid">
                            <!-- Photos will be loaded here via AJAX -->
                        </div>
                        
                        <div class="loading-spinner" id="photos-loading">
                            <div class="spinner"></div>
                            <p>Cargando fotos...</p>
                        </div>
                    </div>
                </div>
                
                <!-- Step 3: Organize -->
                <div class="wizard-step" data-step="organize">
                    <div class="step-content">
                        <h2>🎨 Organizar Galería</h2>
                        <p>Organiza y configura tu galería</p>
                        
                        <div class="organize-grid">
                            <div class="organize-section">
                                <h3>Fotos Seleccionadas</h3>
                                <div id="selected-photos-preview" class="selected-photos-grid">
                                    <!-- Selected photos will appear here -->
                                </div>
                            </div>
                            
                            <div class="organize-section">
                                <h3>Configuración de Galería</h3>
                                <div class="form-group">
                                    <label for="gallery-style">Estilo de Galería:</label>
                                    <select id="gallery-style">
                                        <option value="grid">Grid Profesional</option>
                                        <option value="dominican">Estilo Dominicano 🇩🇴</option>
                                        <option value="masonry">Masonry</option>
                                        <option value="carousel">Carousel</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="gallery-columns">Columnas:</label>
                                    <select id="gallery-columns">
                                        <option value="2">2 Columnas</option>
                                        <option value="3" selected>3 Columnas</option>
                                        <option value="4">4 Columnas</option>
                                        <option value="5">5 Columnas</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="show-captions" checked>
                                        Mostrar títulos de fotos
                                    </label>
                                </div>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enable-lightbox" checked>
                                        Habilitar lightbox
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 4: Settings -->
                <div class="wizard-step" data-step="settings">
                    <div class="step-content">
                        <h2>⚙️ Configuración del Post</h2>
                        <p>Configura los detalles del post que se creará</p>
                        
                        <div class="settings-form">
                            <div class="form-group">
                                <label for="post-title">Título del Post:</label>
                                <input type="text" id="post-title" placeholder="Ej: Galería de Paisajes Dominicanos">
                            </div>
                            
                            <div class="form-group">
                                <label for="post-content">Descripción:</label>
                                <textarea id="post-content" rows="4" placeholder="Describe tu galería..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="post-category">Categoría del Post:</label>
                                <select id="post-category">
                                    <option value="">Seleccionar categoría...</option>
                                    <?php
                                    $categories = get_categories();
                                    foreach ($categories as $category) {
                                        echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="post-tags">Tags (separados por comas):</label>
                                <input type="text" id="post-tags" placeholder="fotografía, dominicana, paisajes">
                            </div>
                            
                            <div class="form-group">
                                <label for="post-status">Estado del Post:</label>
                                <select id="post-status">
                                    <option value="draft">Borrador</option>
                                    <option value="publish">Publicar inmediatamente</option>
                                </select>
                            </div>
                            
                            <div class="photographer-signature">
                                <h4>📸 Firma del Fotógrafo</h4>
                                <div class="signature-preview">
                                    <p><strong>Jose L Encarnacion (JoseTusabe)</strong></p>
                                    <p>JoseTusabe Photography</p>
                                    <p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
                                    <p>📞 ************ | 📧 <EMAIL></p>
                                </div>
                                <label>
                                    <input type="checkbox" id="include-signature" checked>
                                    Incluir firma en el post
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 5: Create Post -->
                <div class="wizard-step" data-step="create_post">
                    <div class="step-content">
                        <h2>🚀 Crear Post</h2>
                        <p>Revisión final y creación del post</p>
                        
                        <div class="creation-preview">
                            <div class="preview-section">
                                <h3>Vista Previa del Post</h3>
                                <div id="post-preview" class="post-preview-container">
                                    <!-- Preview will be generated here -->
                                </div>
                            </div>
                            
                            <div class="creation-status">
                                <div id="creation-progress" class="creation-progress hidden">
                                    <div class="progress-circle">
                                        <div class="spinner"></div>
                                    </div>
                                    <p>Creando tu galería...</p>
                                </div>
                                
                                <div id="creation-success" class="creation-result hidden">
                                    <div class="success-icon">✅</div>
                                    <h3>¡Post Creado Exitosamente!</h3>
                                    <p>Tu galería ha sido creada y está lista.</p>
                                    <div class="result-actions">
                                        <a href="#" id="view-post-link" class="wizard-btn wizard-btn-primary" target="_blank">
                                            Ver Post
                                        </a>
                                        <a href="#" id="edit-post-link" class="wizard-btn wizard-btn-secondary" target="_blank">
                                            Editar Post
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 6: Complete -->
                <div class="wizard-step" data-step="complete">
                    <div class="step-content">
                        <div class="completion-hero">
                            <div class="success-animation">🎉</div>
                            <h2>¡Galería Creada Exitosamente!</h2>
                            <p>Tu galería profesional de SoloYLibre está lista</p>
                            
                            <div class="completion-stats">
                                <div class="stat-item">
                                    <span class="stat-number" id="final-photo-count">0</span>
                                    <span class="stat-label">Fotos</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">1</span>
                                    <span class="stat-label">Post Creado</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">100%</span>
                                    <span class="stat-label">Completado</span>
                                </div>
                            </div>
                            
                            <div class="completion-actions">
                                <button id="create-another" class="wizard-btn wizard-btn-primary">
                                    📸 Crear Otra Galería
                                </button>
                                <button id="go-to-dashboard" class="wizard-btn wizard-btn-secondary">
                                    📊 Ir al Dashboard
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Wizard Navigation -->
            <div class="wizard-navigation">
                <button id="prev-step" class="wizard-btn wizard-btn-secondary" disabled>
                    ← Anterior
                </button>
                <div class="nav-info">
                    <span id="current-step-info">Paso 1 de 6</span>
                </div>
                <button id="next-step" class="wizard-btn wizard-btn-primary">
                    Siguiente →
                </button>
            </div>
        </div>
        
        <!-- Minimized State -->
        <div id="wizard-minimized" class="wizard-minimized hidden">
            <div class="minimized-content">
                <span class="minimized-icon">📸</span>
                <span class="minimized-text">SoloYLibre Wizard</span>
                <button id="restore-wizard" class="restore-btn">↗</button>
            </div>
        </div>
        <?php
    }
    
    /**
     * AJAX: Get photos for selection
     */
    public function ajax_get_photos() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $search = sanitize_text_field($_POST['search'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');
        $date_filter = sanitize_text_field($_POST['date_filter'] ?? '');
        
        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => 50,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        if (!empty($search)) {
            $args['s'] = $search;
        }
        
        if (!empty($date_filter)) {
            switch ($date_filter) {
                case 'today':
                    $args['date_query'] = array(
                        array('after' => '1 day ago')
                    );
                    break;
                case 'week':
                    $args['date_query'] = array(
                        array('after' => '1 week ago')
                    );
                    break;
                case 'month':
                    $args['date_query'] = array(
                        array('after' => '1 month ago')
                    );
                    break;
            }
        }
        
        $photos = get_posts($args);
        $photo_data = array();
        
        foreach ($photos as $photo) {
            $photo_data[] = array(
                'id' => $photo->ID,
                'title' => $photo->post_title,
                'url' => wp_get_attachment_url($photo->ID),
                'thumbnail' => wp_get_attachment_image_url($photo->ID, 'medium'),
                'alt' => get_post_meta($photo->ID, '_wp_attachment_image_alt', true)
            );
        }
        
        wp_send_json_success($photo_data);
    }
    
    /**
     * AJAX: Create post with gallery
     */
    public function ajax_create_post() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());
        $post_title = sanitize_text_field($_POST['post_title'] ?? '');
        $post_content = wp_kses_post($_POST['post_content'] ?? '');
        $post_category = intval($_POST['post_category'] ?? 0);
        $post_tags = sanitize_text_field($_POST['post_tags'] ?? '');
        $post_status = sanitize_text_field($_POST['post_status'] ?? 'draft');
        $gallery_style = sanitize_text_field($_POST['gallery_style'] ?? 'grid');
        $gallery_columns = intval($_POST['gallery_columns'] ?? 4);
        $include_signature = !empty($_POST['include_signature']);

        if (empty($photo_ids) || empty($post_title)) {
            wp_send_json_error('Faltan datos requeridos');
        }

        // Create gallery shortcode
        $gallery_shortcode = '[soloylibre_gallery style="' . $gallery_style . '" ids="' . implode(',', $photo_ids) . '" columns="' . $gallery_columns . '" enable_interactions="true" aspect_ratio="natural"]';
        
        // Add photographer signature if requested
        $signature = '';
        if ($include_signature) {
            $signature = '
            
<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #CE1126; margin-top: 40px;">
    <h4 style="color: #CE1126; margin-bottom: 10px;">📸 Fotografía por Jose L Encarnacion (JoseTusabe)</h4>
    <p><strong>JoseTusabe Photography</strong></p>
    <p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
    <p>📞 ************ | 📧 <EMAIL></p>
    <p>🌐 <a href="https://josetusabe.com">josetusabe.com</a> | <a href="https://soloylibre.com">soloylibre.com</a></p>
</div>';
        }
        
        // Create post
        $post_data = array(
            'post_title' => $post_title,
            'post_content' => $post_content . "\n\n" . $gallery_shortcode . $signature,
            'post_status' => $post_status,
            'post_type' => 'post',
            'post_author' => get_current_user_id()
        );
        
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            wp_send_json_error('Error al crear el post');
        }
        
        // Set category
        if ($post_category > 0) {
            wp_set_post_categories($post_id, array($post_category));
        }
        
        // Set tags
        if (!empty($post_tags)) {
            wp_set_post_tags($post_id, $post_tags);
        }
        
        // Set featured image (intelligent selection)
        if (!empty($photo_ids)) {
            $featured_image_id = $this->set_featured_image_from_gallery($post_id, $photo_ids);
            if (!$featured_image_id) {
                // Fallback to first photo if intelligent selection fails
                set_post_thumbnail($post_id, $photo_ids[0]);
                $featured_image_id = $photo_ids[0];
            }

            // Save featured image metadata for reference
            update_post_meta($post_id, '_soloylibre_featured_image_id', $featured_image_id);
            update_post_meta($post_id, '_soloylibre_featured_image_auto_selected', true);
        }
        
        // Save gallery metadata
        update_post_meta($post_id, '_soloylibre_gallery_photos', $photo_ids);
        update_post_meta($post_id, '_soloylibre_gallery_style', $gallery_style);
        update_post_meta($post_id, '_soloylibre_created_by_wizard', true);
        update_post_meta($post_id, '_soloylibre_creation_date', current_time('mysql'));
        update_post_meta($post_id, '_soloylibre_creation_method', 'manual_wizard');

        // Increment total posts created counter (persistent)
        $total_posts_created = get_option('soloylibre_total_posts_created', 0);
        update_option('soloylibre_total_posts_created', $total_posts_created + 1);
        
        wp_send_json_success(array(
            'post_id' => $post_id,
            'post_url' => get_permalink($post_id),
            'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit'),
            'photo_count' => count($photo_ids),
            'featured_image_id' => $featured_image_id,
            'featured_image_url' => wp_get_attachment_image_url($featured_image_id, 'medium'),
            'featured_image_title' => get_the_title($featured_image_id)
        ));
    }
    
    /**
     * AJAX: Save wizard step data
     */
    public function ajax_save_step() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $step = sanitize_text_field($_POST['step'] ?? '');
        $data = $_POST['data'] ?? array();
        
        // Save step data to user meta for persistence
        $user_id = get_current_user_id();
        update_user_meta($user_id, 'soloylibre_wizard_' . $step, $data);
        
        wp_send_json_success();
    }

    /**
     * AJAX: Smart Selection - Selección Inteligente
     */
    public function ajax_smart_selection() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        // Obtener fotos disponibles (no publicadas)
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unused_photos = get_option('soloylibre_unused_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        $categorized_photos = array_merge($published_photos, $private_photos, $unused_photos, $unwanted_photos);

        $available_photos = array();
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $categorized_photos)) {
                $available_photos[] = array(
                    'id' => $photo->ID,
                    'title' => get_the_title($photo->ID) ?: 'Sin título',
                    'url' => wp_get_attachment_image_url($photo->ID, 'medium'),
                    'thumbnail' => wp_get_attachment_image_url($photo->ID, 'thumbnail'),
                    'date' => $photo->post_date,
                    'size' => $this->get_file_size($photo->ID)
                );
            }
        }

        // Selección inteligente: mejores fotos basadas en tamaño y fecha
        usort($available_photos, function($a, $b) {
            // Priorizar fotos más recientes y de mayor tamaño
            $date_diff = strtotime($b['date']) - strtotime($a['date']);
            return $date_diff;
        });

        $smart_selection = array_slice($available_photos, 0, 30); // Top 30 fotos

        wp_send_json_success(array(
            'photos' => $smart_selection,
            'total_available' => count($available_photos),
            'selection_count' => count($smart_selection),
            'message' => 'Selección inteligente completada con ' . count($smart_selection) . ' fotos de alta calidad'
        ));
    }

    /**
     * AJAX: Dominican Styles - Estilos Dominicanos
     */
    public function ajax_dominican_styles() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        $dominican_styles = array(
            'bandera' => array(
                'name' => '🇩🇴 Estilo Bandera Dominicana',
                'description' => 'Galería con colores de la bandera: rojo, azul y blanco',
                'css' => array(
                    'background' => 'linear-gradient(135deg, #CE1126 0%, #FFFFFF 50%, #002D62 100%)',
                    'border' => '3px solid #CE1126',
                    'accent_color' => '#CE1126'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-bandera.jpg'
            ),
            'merengue' => array(
                'name' => '🎵 Estilo Merengue',
                'description' => 'Colores vibrantes inspirados en el merengue dominicano',
                'css' => array(
                    'background' => 'linear-gradient(45deg, #FF6B35, #F7931E, #FFD23F)',
                    'border' => '2px solid #FF6B35',
                    'accent_color' => '#FF6B35'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-merengue.jpg'
            ),
            'caribe' => array(
                'name' => '🏝️ Estilo Caribeño',
                'description' => 'Azules del mar Caribe y verdes tropicales',
                'css' => array(
                    'background' => 'linear-gradient(135deg, #00B4DB 0%, #0083B0 100%)',
                    'border' => '2px solid #00B4DB',
                    'accent_color' => '#00B4DB'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-caribe.jpg'
            ),
            'colonial' => array(
                'name' => '🏛️ Estilo Colonial',
                'description' => 'Elegancia de la arquitectura colonial dominicana',
                'css' => array(
                    'background' => 'linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%)',
                    'border' => '3px solid #8B4513',
                    'accent_color' => '#8B4513'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-colonial.jpg'
            ),
            'bachata' => array(
                'name' => '💃 Estilo Bachata',
                'description' => 'Colores románticos de la bachata dominicana',
                'css' => array(
                    'background' => 'linear-gradient(135deg, #FF69B4 0%, #FF1493 50%, #DC143C 100%)',
                    'border' => '2px solid #FF1493',
                    'accent_color' => '#FF1493'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-bachata.jpg'
            )
        );

        wp_send_json_success(array(
            'styles' => $dominican_styles,
            'message' => 'Estilos dominicanos cargados exitosamente',
            'total_styles' => count($dominican_styles)
        ));
    }

    /**
     * AJAX: Automatic Posts - Posts Automáticos
     */
    public function ajax_automatic_posts() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        $post_type = sanitize_text_field($_POST['post_type'] ?? 'auto');
        $photo_count = intval($_POST['photo_count'] ?? 15);
        $style = sanitize_text_field($_POST['style'] ?? 'masonry');

        // Obtener fotos disponibles
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        $published_photos = get_option('soloylibre_published_photos', array());
        $categorized_photos = array_merge(
            $published_photos,
            get_option('soloylibre_private_photos', array()),
            get_option('soloylibre_unused_photos', array()),
            get_option('soloylibre_unwanted_photos', array())
        );

        $available_photos = array();
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $categorized_photos)) {
                $available_photos[] = $photo->ID;
            }
        }

        if (empty($available_photos)) {
            wp_send_json_error('No hay fotos disponibles para crear posts automáticos');
            return;
        }

        // Seleccionar fotos para el post
        $selected_photos = array_slice($available_photos, 0, min($photo_count, count($available_photos)));

        // Generar título automático
        $photographer_info = get_option('soloylibre_photographer_info', array(
            'nickname' => 'JoseTusabe',
            'business_name' => 'JoseTusabe Photography'
        ));

        // Generar título creativo
        $title = $this->generate_creative_title();

        // Crear contenido del post con mensaje inspiracional
        $columns = intval($_POST['columns'] ?? 4);
        $inspirational_message = $this->generate_inspirational_message();

        $post_content = "[soloylibre_gallery style=\"$style\" ids=\"" . implode(',', $selected_photos) . "\" columns=\"$columns\" enable_interactions=\"true\" aspect_ratio=\"natural\"]\n\n";

        // Agregar mensaje inspiracional único
        $post_content .= "<div style='background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 25px; border-radius: 15px; border-left: 5px solid #CE1126; margin: 20px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>\n";
        $post_content .= "<h3 style='color: #2c3e50; margin-bottom: 15px; font-size: 1.3rem;'>💫 Mensaje Especial</h3>\n";
        $post_content .= "<p style='color: #495057; line-height: 1.6; font-style: italic; margin-bottom: 15px;'>$inspirational_message</p>\n";
        $post_content .= "<p style='color: #6c757d; font-size: 0.9rem; text-align: right; margin: 0;'><strong>— JoseTusabe Photography</strong></p>\n";
        $post_content .= "</div>\n\n";

        $post_content .= "<hr style='border: none; height: 2px; background: linear-gradient(135deg, #CE1126, #002D62); margin: 30px 0; border-radius: 2px;'>\n\n";
        $post_content .= "<p style='text-align: center; color: #6c757d; font-size: 0.9rem;'>📸 <strong>Galería creada automáticamente por SoloYLibre Gallery Pro</strong></p>\n";
        $post_content .= "<p style='text-align: center; color: #6c757d; font-size: 0.9rem;'>🇩🇴 <em>Fotografía profesional desde San José de Ocoa, República Dominicana</em></p>\n";
        $post_content .= "<p style='text-align: center; color: #6c757d; font-size: 0.9rem;'>👨‍💻 <strong>Jose L Encarnacion (JoseTusabe)</strong> | 📧 <EMAIL> | 📞 ************</p>";

        // Crear el post
        $post_id = wp_insert_post(array(
            'post_title' => $title,
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => get_current_user_id(),
            'post_category' => array(get_option('default_category')),
            'meta_input' => array(
                '_soloylibre_gallery_photos' => $selected_photos,
                '_soloylibre_gallery_style' => $style,
                '_soloylibre_auto_generated' => true,
                '_soloylibre_creation_date' => current_time('mysql'),
                '_soloylibre_photographer' => $photographer_info['nickname']
            )
        ));

        if ($post_id) {
            // Set featured image (intelligent selection)
            $featured_image_id = $this->set_featured_image_from_gallery($post_id, $selected_photos);
            if (!$featured_image_id) {
                // Fallback to first photo if intelligent selection fails
                set_post_thumbnail($post_id, $selected_photos[0]);
                $featured_image_id = $selected_photos[0];
            }

            // Update post meta with featured image info
            update_post_meta($post_id, '_soloylibre_featured_image_id', $featured_image_id);
            update_post_meta($post_id, '_soloylibre_featured_image_auto_selected', true);

            // Marcar fotos como publicadas
            $published_photos = array_unique(array_merge($published_photos, $selected_photos));
            update_option('soloylibre_published_photos', $published_photos);

            // Actualizar estadísticas
            $monthly_key = 'soloylibre_monthly_published_' . date('Y_m');
            $monthly_published = get_option($monthly_key, array());
            $monthly_published = array_unique(array_merge($monthly_published, $selected_photos));
            update_option($monthly_key, $monthly_published);

            // Incrementar contador
            $auto_posts = get_option('soloylibre_auto_posts_created', 0);
            update_option('soloylibre_auto_posts_created', $auto_posts + 1);

            // Increment total posts created counter (persistent)
            $total_posts_created = get_option('soloylibre_total_posts_created', 0);
            update_option('soloylibre_total_posts_created', $total_posts_created + 1);

            // Save post metadata
            update_post_meta($post_id, '_soloylibre_created_by_wizard', true);
            update_post_meta($post_id, '_soloylibre_creation_date', current_time('mysql'));
            update_post_meta($post_id, '_soloylibre_creation_method', 'automatic_post');
            update_post_meta($post_id, '_soloylibre_gallery_photos', $selected_photos);
            update_post_meta($post_id, '_soloylibre_gallery_style', $style);

            wp_send_json_success(array(
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'edit_url' => get_edit_post_link($post_id),
                'title' => $title,
                'photos_count' => count($selected_photos),
                'featured_image_id' => $featured_image_id,
                'featured_image_url' => wp_get_attachment_image_url($featured_image_id, 'medium'),
                'featured_image_title' => get_the_title($featured_image_id),
                'message' => "Post automático creado exitosamente: '$title' con " . count($selected_photos) . " fotos. Imagen destacada: " . get_the_title($featured_image_id)
            ));
        } else {
            wp_send_json_error('Error al crear el post automático');
        }
    }

    /**
     * Get file size helper
     */
    private function get_file_size($attachment_id) {
        $file_path = get_attached_file($attachment_id);
        if ($file_path && file_exists($file_path)) {
            $size = filesize($file_path);
            if ($size >= 1048576) {
                return round($size / 1048576, 1) . ' MB';
            } elseif ($size >= 1024) {
                return round($size / 1024, 1) . ' KB';
            }
            return $size . ' B';
        }
        return 'N/A';
    }

    /**
     * Set featured image for post from gallery photos
     * Selects the best photo automatically based on criteria
     */
    private function set_featured_image_from_gallery($post_id, $photo_ids) {
        if (empty($photo_ids) || !$post_id) {
            return false;
        }

        // Strategy 1: Try to find the largest/highest quality image
        $best_photo_id = $this->select_best_featured_image($photo_ids);

        if ($best_photo_id) {
            // Set as featured image
            $result = set_post_thumbnail($post_id, $best_photo_id);

            if ($result) {
                // Log the action for debugging
                error_log("SoloYLibre: Featured image set for post $post_id using photo $best_photo_id");
                return $best_photo_id;
            }
        }

        return false;
    }

    /**
     * Select the best photo for featured image
     * Uses multiple criteria to choose the optimal image
     */
    private function select_best_featured_image($photo_ids) {
        if (empty($photo_ids)) {
            return false;
        }

        $best_photo = null;
        $best_score = 0;

        foreach ($photo_ids as $photo_id) {
            $score = $this->calculate_photo_score($photo_id);

            if ($score > $best_score) {
                $best_score = $score;
                $best_photo = $photo_id;
            }
        }

        // Fallback: if no scoring worked, use the first photo
        return $best_photo ?: $photo_ids[0];
    }

    /**
     * Calculate photo score for featured image selection
     * Higher score = better candidate for featured image
     */
    private function calculate_photo_score($photo_id) {
        $score = 0;

        // Get image metadata
        $metadata = wp_get_attachment_metadata($photo_id);
        if (!$metadata) {
            return 0;
        }

        // Factor 1: Image size (larger = better, up to a point)
        if (isset($metadata['width']) && isset($metadata['height'])) {
            $width = $metadata['width'];
            $height = $metadata['height'];
            $total_pixels = $width * $height;

            // Optimal range: 1200x800 to 2400x1600 pixels
            $optimal_min = 1200 * 800;
            $optimal_max = 2400 * 1600;

            if ($total_pixels >= $optimal_min && $total_pixels <= $optimal_max) {
                $score += 50; // Perfect size range
            } elseif ($total_pixels > $optimal_max) {
                $score += 30; // Too large but still good
            } elseif ($total_pixels > 800 * 600) {
                $score += 20; // Decent size
            } else {
                $score += 5; // Too small
            }

            // Factor 2: Aspect ratio (prefer landscape or square)
            $aspect_ratio = $width / $height;
            if ($aspect_ratio >= 1.0 && $aspect_ratio <= 1.8) {
                $score += 20; // Good aspect ratio for featured images
            } elseif ($aspect_ratio >= 0.8 && $aspect_ratio < 1.0) {
                $score += 15; // Portrait but acceptable
            } else {
                $score += 5; // Too wide or too tall
            }
        }

        // Factor 3: File size (indicates quality)
        $file_path = get_attached_file($photo_id);
        if ($file_path && file_exists($file_path)) {
            $file_size = filesize($file_path);

            // Prefer files between 500KB and 5MB
            if ($file_size >= 500000 && $file_size <= 5000000) {
                $score += 15; // Good file size
            } elseif ($file_size > 5000000) {
                $score += 10; // Large but acceptable
            } elseif ($file_size > 100000) {
                $score += 5; // Small but usable
            }
        }

        // Factor 4: Image title/alt text (prefer descriptive names)
        $title = get_the_title($photo_id);
        $alt_text = get_post_meta($photo_id, '_wp_attachment_image_alt', true);

        if (!empty($title) && strlen($title) > 5) {
            $score += 5; // Has descriptive title
        }

        if (!empty($alt_text)) {
            $score += 5; // Has alt text
        }

        // Factor 5: Upload date (prefer recent photos)
        $upload_date = get_post_field('post_date', $photo_id);
        $days_old = (time() - strtotime($upload_date)) / (24 * 60 * 60);

        if ($days_old <= 30) {
            $score += 10; // Recent photo
        } elseif ($days_old <= 90) {
            $score += 5; // Moderately recent
        }

        return $score;
    }

    /**
     * Generate creative title for album
     */
    private function generate_creative_title() {
        // Get current album number
        $album_number = get_option('soloylibre_total_posts_created', 0) + 1;

        $creative_titles = array(
            "✨ AutoAlbum-#$album_number - Momentos Mágicos " . date('d/m/Y'),
            "🌟 AutoAlbum-#$album_number - Recuerdos de Oro " . date('F Y'),
            "📸 AutoAlbum-#$album_number - Capturando Sonrisas " . date('d/m/Y'),
            "💫 AutoAlbum-#$album_number - Instantes Eternos JoseTusabe " . date('d/m/Y'),
            "🎨 AutoAlbum-#$album_number - Galería de Emociones " . date('d/m/Y'),
            "🇩🇴 AutoAlbum-#$album_number - Belleza Dominicana " . date('d/m/Y'),
            "❤️ AutoAlbum-#$album_number - Álbum del Corazón " . date('d/m/Y'),
            "🌅 AutoAlbum-#$album_number - Nuevos Horizontes " . date('d/m/Y'),
            "🎭 AutoAlbum-#$album_number - Teatro de Memorias " . date('d/m/Y'),
            "🌺 AutoAlbum-#$album_number - Jardín de Recuerdos " . date('d/m/Y'),
            "🎪 AutoAlbum-#$album_number - Carnaval de Momentos " . date('d/m/Y'),
            "🏝️ AutoAlbum-#$album_number - Paraíso Capturado " . date('d/m/Y'),
            "🎵 AutoAlbum-#$album_number - Sinfonía Visual JoseTusabe " . date('d/m/Y'),
            "🌈 AutoAlbum-#$album_number - Colores de Vida " . date('d/m/Y'),
            "🕊️ AutoAlbum-#$album_number - Vuelo de Emociones " . date('d/m/Y'),
            "🌙 AutoAlbum-#$album_number - Sueños Fotografiados " . date('d/m/Y'),
            "🎯 AutoAlbum-#$album_number - Enfoque Perfecto " . date('d/m/Y'),
            "🎨 AutoAlbum-#$album_number - Pinceladas de Luz " . date('d/m/Y'),
            "🌊 AutoAlbum-#$album_number - Olas de Nostalgia " . date('d/m/Y'),
            "🦋 AutoAlbum-#$album_number - Metamorfosis Visual " . date('d/m/Y')
        );

        return $creative_titles[array_rand($creative_titles)];
    }

    /**
     * Generate inspirational message for album
     */
    private function generate_inspirational_message() {
        $inspirational_messages = array(
            "💝 Este álbum es un abrazo visual lleno de amor. Cada imagen aquí capturada lleva consigo la calidez de los momentos compartidos y la promesa de que los mejores recuerdos nunca se desvanecen. Que estas fotos te recuerden siempre la importancia de valorar cada instante junto a quienes amas.",

            "🌟 En cada fotografía de esta galería vive una historia de amistad verdadera. Los amigos son los hermanos que elegimos, y estas imágenes son el testimonio de esos lazos inquebrantables que nos hacen más fuertes. Que la alegría capturada aquí ilumine tus días más grises.",

            "👨‍👩‍👧‍👦 La familia es el tesoro más preciado que tenemos, y este álbum es el cofre donde guardamos sus momentos más hermosos. Cada sonrisa, cada abrazo, cada mirada cómplice aquí preservada es un recordatorio de que el amor familiar es el fundamento de toda felicidad.",

            "🇩🇴 Desde el corazón de República Dominicana, estas imágenes llevan consigo la calidez de nuestra tierra y la alegría de nuestra gente. Que cada foto te transporte a esos momentos especiales donde el amor y la unión familiar brillan como el sol caribeño.",

            "💕 El amor verdadero se refleja en los pequeños detalles, en las miradas cómplices y en los momentos compartidos. Este álbum es una celebración de ese amor que trasciende el tiempo y que se hace eterno a través de cada imagen capturada con el corazón.",

            "🌈 Después de cada tormenta viene el arcoíris, y después de cada momento difícil llegan estos instantes de pura felicidad. Que estas fotografías te recuerden que siempre hay razones para sonreír y que la vida está llena de momentos hermosos esperando ser vividos.",

            "🎭 La vida es un teatro de emociones, y estas fotos son los mejores actos de esa obra maravillosa. Cada imagen cuenta una historia de risas, lágrimas de alegría y momentos que merecen ser recordados para siempre. Que este álbum sea tu libro de memorias favorito.",

            "🌺 Como las flores que embellecen nuestros jardines, estos momentos capturados embellecen nuestras vidas. Cada fotografía es un pétalo de felicidad que, unidos, forman el ramo más hermoso de recuerdos. Que su fragancia llene tu corazón de alegría.",

            "🕊️ La paz se encuentra en los momentos simples, en las sonrisas sinceras y en la compañía de quienes amamos. Este álbum es un santuario de esa paz, un lugar donde puedes refugiarte cuando necesites recordar que la vida está llena de bendiciones.",

            "🌅 Cada nuevo día trae consigo la oportunidad de crear nuevos recuerdos hermosos. Estas fotografías son el testimonio de que has sabido aprovechar esas oportunidades, capturando momentos que ahora brillan como estrellas en el cielo de tu memoria.",

            "🎪 La vida es un carnaval de emociones, y tú has sabido capturar los mejores momentos de esa celebración. Que estas imágenes te recuerden que siempre hay motivos para festejar: el amor de la familia, la lealtad de los amigos y la belleza de estar vivos.",

            "🌊 Como las olas que van y vienen, los momentos pasan, pero las fotografías los hacen eternos. En este álbum, el tiempo se detiene para preservar esos instantes preciosos que definen quiénes somos y qué valoramos en la vida.",

            "🦋 Así como la mariposa emerge hermosa de su capullo, estos momentos capturados emergen hermosos del fluir cotidiano de la vida. Cada imagen es una transformación de un instante ordinario en un recuerdo extraordinario.",

            "🌙 En la quietud de la noche, cuando el mundo descansa, estos recuerdos brillan como lunas llenas en el cielo de tu memoria. Que estas fotografías sean tu luz en los momentos oscuros y tu compañía en las noches de nostalgia.",

            "🎯 Has apuntado tu cámara hacia los momentos correctos, capturando no solo imágenes, sino emociones puras. Este álbum es la prueba de que sabes reconocer la belleza en lo cotidiano y la magia en lo simple.",

            "🌟 Cada estrella en el cielo tiene su propia luz, y cada fotografía en este álbum tiene su propia magia. Juntas, forman una constelación de recuerdos que iluminará tu camino siempre que necesites recordar lo hermosa que es la vida.",

            "🎨 Eres el artista de tu propia vida, y estas fotografías son las pinceladas maestras de tu obra. Cada imagen es un color en la paleta de tu existencia, creando un cuadro hermoso lleno de amor, amistad y momentos inolvidables.",

            "🏝️ Este álbum es tu isla de tranquilidad en el océano de la vida cotidiana. Aquí puedes refugiarte cuando necesites recordar que existen momentos perfectos, personas especiales y razones infinitas para ser feliz.",

            "🎵 La vida tiene su propia melodía, y estas fotografías son las notas más hermosas de esa sinfonía. Cada imagen es un acorde perfecto en la música de tus recuerdos, creando una canción que nunca dejará de sonar en tu corazón.",

            "💎 Los diamantes se forman bajo presión, y estos momentos hermosos se forman en medio de la vida cotidiana. Este álbum es tu joyero personal, lleno de gemas preciosas que brillan con la luz del amor y la felicidad compartida."
        );

        return $inspirational_messages[array_rand($inspirational_messages)];
    }

    /**
     * Get file size for photo
     */
    private function get_file_size($photo_id) {
        $file_path = get_attached_file($photo_id);
        if ($file_path && file_exists($file_path)) {
            $size_bytes = filesize($file_path);
            if ($size_bytes >= 1048576) {
                return round($size_bytes / 1048576, 1) . ' MB';
            } elseif ($size_bytes >= 1024) {
                return round($size_bytes / 1024, 1) . ' KB';
            } else {
                return $size_bytes . ' B';
            }
        }
        return 'N/A';
    }

    /**
     * AJAX: Get all photos preview for advanced auto post
     */
    public function ajax_get_all_photos_preview() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        // Get all photos
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        // Get categorized photos
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());
        $deleted_photos = get_option('soloylibre_deleted_photos', array());

        $formatted_photos = array();
        foreach ($all_photos as $photo) {
            $formatted_photos[] = array(
                'id' => $photo->ID,
                'title' => get_the_title($photo->ID) ?: 'Sin título',
                'url' => wp_get_attachment_image_url($photo->ID, 'medium'),
                'thumbnail' => wp_get_attachment_image_url($photo->ID, 'thumbnail'),
                'full' => wp_get_attachment_image_url($photo->ID, 'full'),
                'date' => $photo->post_date,
                'size' => $this->get_file_size($photo->ID)
            );
        }

        wp_send_json_success(array(
            'photos' => $formatted_photos,
            'total_photos' => count($all_photos),
            'categorized' => array(
                'published' => count($published_photos),
                'private' => count($private_photos),
                'unwanted' => count($unwanted_photos),
                'deleted' => count($deleted_photos)
            )
        ));
    }

    /**
     * AJAX: Create advanced auto post with categorized photos
     */
    public function ajax_create_advanced_auto_post() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());
        $style = sanitize_text_field($_POST['style'] ?? 'masonry');
        $columns = intval($_POST['columns'] ?? 4);
        $categorized_photos = $_POST['categorized_photos'] ?? array();

        if (empty($photo_ids)) {
            wp_send_json_error('No se seleccionaron fotos');
            return;
        }

        // Save categorized photos
        if (!empty($categorized_photos['private'])) {
            $current_private = get_option('soloylibre_private_photos', array());
            $updated_private = array_unique(array_merge($current_private, array_map('intval', $categorized_photos['private'])));
            update_option('soloylibre_private_photos', $updated_private);
        }

        if (!empty($categorized_photos['unwanted'])) {
            $current_unwanted = get_option('soloylibre_unwanted_photos', array());
            $updated_unwanted = array_unique(array_merge($current_unwanted, array_map('intval', $categorized_photos['unwanted'])));
            update_option('soloylibre_unwanted_photos', $updated_unwanted);
        }

        if (!empty($categorized_photos['deleted'])) {
            $current_deleted = get_option('soloylibre_deleted_photos', array());
            $updated_deleted = array_unique(array_merge($current_deleted, array_map('intval', $categorized_photos['deleted'])));
            update_option('soloylibre_deleted_photos', $updated_deleted);
        }

        // Generate creative title and inspirational message
        $title = $this->generate_creative_title();
        $inspirational_message = $this->generate_inspirational_message();

        // Create post content with inspirational message
        $post_content = "[soloylibre_gallery style=\"$style\" ids=\"" . implode(',', $photo_ids) . "\" columns=\"$columns\" enable_interactions=\"true\" aspect_ratio=\"natural\"]\n\n";

        // Add inspirational message
        $post_content .= "<div style='background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 25px; border-radius: 15px; border-left: 5px solid #CE1126; margin: 20px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>\n";
        $post_content .= "<h3 style='color: #2c3e50; margin-bottom: 15px; font-size: 1.3rem;'>💫 Mensaje Especial</h3>\n";
        $post_content .= "<p style='color: #495057; line-height: 1.6; font-style: italic; margin-bottom: 15px;'>$inspirational_message</p>\n";
        $post_content .= "<p style='color: #6c757d; font-size: 0.9rem; text-align: right; margin: 0;'><strong>— JoseTusabe Photography</strong></p>\n";
        $post_content .= "</div>\n\n";

        $post_content .= "<hr style='border: none; height: 2px; background: linear-gradient(135deg, #CE1126, #002D62); margin: 30px 0; border-radius: 2px;'>\n\n";
        $post_content .= "<p style='text-align: center; color: #6c757d; font-size: 0.9rem;'>📸 <strong>Galería creada con selección avanzada por SoloYLibre Gallery Pro</strong></p>\n";
        $post_content .= "<p style='text-align: center; color: #6c757d; font-size: 0.9rem;'>🇩🇴 <em>Fotografía profesional desde San José de Ocoa, República Dominicana</em></p>\n";
        $post_content .= "<p style='text-align: center; color: #6c757d; font-size: 0.9rem;'>👨‍💻 <strong>Jose L Encarnacion (JoseTusabe)</strong> | 📧 <EMAIL> | 📞 ************</p>";

        // Create post
        $post_data = array(
            'post_title' => $title,
            'post_content' => $post_content,
            'post_status' => 'draft',
            'post_type' => 'post',
            'post_author' => get_current_user_id()
        );

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            wp_send_json_error('Error al crear el post');
            return;
        }

        // Set featured image
        $featured_image_id = $this->set_featured_image_from_gallery($post_id, $photo_ids);
        if (!$featured_image_id) {
            set_post_thumbnail($post_id, $photo_ids[0]);
            $featured_image_id = $photo_ids[0];
        }

        // Update post metadata
        update_post_meta($post_id, '_soloylibre_featured_image_id', $featured_image_id);
        update_post_meta($post_id, '_soloylibre_featured_image_auto_selected', true);
        update_post_meta($post_id, '_soloylibre_created_by_wizard', true);
        update_post_meta($post_id, '_soloylibre_creation_date', current_time('mysql'));
        update_post_meta($post_id, '_soloylibre_creation_method', 'advanced_auto_post');
        update_post_meta($post_id, '_soloylibre_gallery_photos', $photo_ids);
        update_post_meta($post_id, '_soloylibre_gallery_style', $style);

        // Mark photos as published
        $published_photos = get_option('soloylibre_published_photos', array());
        $published_photos = array_unique(array_merge($published_photos, $photo_ids));
        update_option('soloylibre_published_photos', $published_photos);

        // Update statistics
        $monthly_key = 'soloylibre_monthly_published_' . date('Y_m');
        $monthly_published = get_option($monthly_key, array());
        $monthly_published = array_unique(array_merge($monthly_published, $photo_ids));
        update_option($monthly_key, $monthly_published);

        // Increment counters
        $auto_posts = get_option('soloylibre_auto_posts_created', 0);
        update_option('soloylibre_auto_posts_created', $auto_posts + 1);

        $total_posts_created = get_option('soloylibre_total_posts_created', 0);
        update_option('soloylibre_total_posts_created', $total_posts_created + 1);

        wp_send_json_success(array(
            'post_id' => $post_id,
            'post_url' => get_permalink($post_id),
            'edit_url' => get_edit_post_link($post_id),
            'title' => $title,
            'photos_count' => count($photo_ids),
            'featured_image_id' => $featured_image_id,
            'featured_image_url' => wp_get_attachment_image_url($featured_image_id, 'medium'),
            'featured_image_title' => get_the_title($featured_image_id),
            'message' => "Post avanzado creado exitosamente: '$title' con " . count($photo_ids) . " fotos seleccionadas. Imagen destacada: " . get_the_title($featured_image_id)
        ));
    }

    /**
     * AJAX: Create album from category
     */
    public function ajax_create_category_album() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());
        $category = sanitize_text_field($_POST['category'] ?? '');
        $category_name = sanitize_text_field($_POST['category_name'] ?? '');

        if (empty($photo_ids) || empty($category)) {
            wp_send_json_error('Datos insuficientes para crear el álbum');
            return;
        }

        // Generate title for category album
        $title = "📚 Álbum de Fotos $category_name - " . date('d/m/Y');

        // Create post content
        $post_content = "[soloylibre_gallery style=\"masonry\" ids=\"" . implode(',', $photo_ids) . "\" columns=\"4\" enable_interactions=\"true\" aspect_ratio=\"natural\"]\n\n";

        $post_content .= "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #6c757d; margin: 20px 0;'>\n";
        $post_content .= "<h3 style='color: #495057; margin-bottom: 10px;'>📂 Álbum de Categoría: $category_name</h3>\n";
        $post_content .= "<p style='color: #6c757d; margin: 0;'>Este álbum contiene " . count($photo_ids) . " fotos categorizadas como '$category_name' para organización y referencia futura.</p>\n";
        $post_content .= "</div>\n\n";

        $post_content .= "<p style='text-align: center; color: #6c757d; font-size: 0.9rem;'>📸 <strong>Álbum de categoría creado por SoloYLibre Gallery Pro</strong></p>\n";
        $post_content .= "<p style='text-align: center; color: #6c757d; font-size: 0.9rem;'>🇩🇴 <em>Sistema de organización fotográfica profesional</em></p>";

        // Create post
        $post_data = array(
            'post_title' => $title,
            'post_content' => $post_content,
            'post_status' => 'draft',
            'post_type' => 'post',
            'post_author' => get_current_user_id()
        );

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            wp_send_json_error('Error al crear el álbum');
            return;
        }

        // Set featured image
        $featured_image_id = $this->set_featured_image_from_gallery($post_id, $photo_ids);
        if (!$featured_image_id) {
            set_post_thumbnail($post_id, $photo_ids[0]);
            $featured_image_id = $photo_ids[0];
        }

        // Update post metadata
        update_post_meta($post_id, '_soloylibre_featured_image_id', $featured_image_id);
        update_post_meta($post_id, '_soloylibre_featured_image_auto_selected', true);
        update_post_meta($post_id, '_soloylibre_created_by_wizard', true);
        update_post_meta($post_id, '_soloylibre_creation_date', current_time('mysql'));
        update_post_meta($post_id, '_soloylibre_creation_method', 'category_album');
        update_post_meta($post_id, '_soloylibre_category_type', $category);
        update_post_meta($post_id, '_soloylibre_gallery_photos', $photo_ids);

        // Increment total posts counter
        $total_posts_created = get_option('soloylibre_total_posts_created', 0);
        update_option('soloylibre_total_posts_created', $total_posts_created + 1);

        wp_send_json_success(array(
            'post_id' => $post_id,
            'post_url' => get_permalink($post_id),
            'edit_url' => get_edit_post_link($post_id),
            'title' => $title,
            'photos_count' => count($photo_ids),
            'category' => $category_name,
            'message' => "Álbum de $category_name creado exitosamente con " . count($photo_ids) . " fotos"
        ));
    }

    /**
     * AJAX: Save photo categories (for persistence)
     */
    public function ajax_save_photo_categories() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        $categorized_photos = $_POST['categorized_photos'] ?? array();

        // Save each category
        foreach ($categorized_photos as $category => $photo_ids) {
            $photo_ids = array_map('intval', $photo_ids);
            $option_name = 'soloylibre_' . $category . '_photos';

            $current_photos = get_option($option_name, array());
            $updated_photos = array_unique(array_merge($current_photos, $photo_ids));
            update_option($option_name, $updated_photos);
        }

        wp_send_json_success(array(
            'message' => 'Categorías de fotos guardadas exitosamente',
            'categories_saved' => array_keys($categorized_photos)
        ));
    }
}
