<?php
/**
 * Fullscreen Wizard Class
 * Creates a fullscreen wizard for photo selection and post creation
 * Developed by JEYKO AI for Jose L <PERSON> (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Fullscreen_Wizard {
    
    private $wizard_steps = array(
        'welcome' => 'Bienvenida',
        'select_photos' => 'Seleccionar Fotos',
        'organize' => 'Organizar',
        'settings' => 'Configuración',
        'create_post' => 'Crear Post',
        'complete' => 'Completado'
    );
    
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_wizard_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_wizard_scripts'));
        add_action('wp_ajax_wizard_get_photos', array($this, 'ajax_get_photos'));
        add_action('wp_ajax_wizard_create_post', array($this, 'ajax_create_post'));
        add_action('wp_ajax_wizard_save_step', array($this, 'ajax_save_step'));
        add_action('wp_ajax_wizard_smart_selection', array($this, 'ajax_smart_selection'));
        add_action('wp_ajax_wizard_dominican_styles', array($this, 'ajax_dominican_styles'));
        add_action('wp_ajax_wizard_automatic_posts', array($this, 'ajax_automatic_posts'));
    }
    
    /**
     * Add wizard menu
     */
    public function add_wizard_menu() {
        add_menu_page(
            'SoloYLibre Wizard',
            '📸 SoloYLibre Wizard',
            'edit_posts',
            'soloylibre-wizard',
            array($this, 'render_wizard'),
            'dashicons-camera',
            25
        );
    }
    
    /**
     * Enqueue wizard scripts
     */
    public function enqueue_wizard_scripts($hook) {
        if ($hook !== 'toplevel_page_soloylibre-wizard') {
            return;
        }
        
        wp_enqueue_style(
            'soloylibre-wizard-style',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/css/fullscreen-wizard.css',
            array(),
            SOLOYLIBRE_GALLERY_VERSION
        );
        
        wp_enqueue_script(
            'soloylibre-wizard-script',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/fullscreen-wizard.js',
            array('jquery', 'wp-util'),
            SOLOYLIBRE_GALLERY_VERSION,
            true
        );
        
        wp_localize_script('soloylibre-wizard-script', 'soloylibre_wizard', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('soloylibre_wizard_nonce'),
            'photographer' => array(
                'name' => 'Jose L Encarnacion',
                'alias' => 'JoseTusabe',
                'brand' => 'SoloYLibre Photography',
                'location' => 'San José de Ocoa, Dom. Rep. / USA',
                'phone' => '************',
                'email' => '<EMAIL>'
            ),
            'texts' => array(
                'loading' => __('Cargando...', 'soloylibre-gallery'),
                'error' => __('Error al procesar la solicitud', 'soloylibre-gallery'),
                'success' => __('¡Éxito!', 'soloylibre-gallery'),
                'select_photos' => __('Selecciona las fotos para tu galería', 'soloylibre-gallery'),
                'creating_post' => __('Creando post con galería...', 'soloylibre-gallery'),
                'post_created' => __('¡Post creado exitosamente!', 'soloylibre-gallery')
            )
        ));
    }
    
    /**
     * Render wizard interface
     */
    public function render_wizard() {
        ?>
        <div id="soloylibre-fullscreen-wizard" class="soloylibre-wizard-container">
            <!-- Wizard Header -->
            <div class="wizard-header">
                <div class="wizard-brand">
                    <h1>📸 SoloYLibre Gallery Wizard</h1>
                    <p>Crea galerías profesionales fácilmente</p>
                </div>
                <div class="wizard-controls">
                    <button id="minimize-wizard" class="wizard-btn wizard-btn-secondary">
                        <span class="dashicons dashicons-minus"></span> Minimizar
                    </button>
                    <button id="close-wizard" class="wizard-btn wizard-btn-danger">
                        <span class="dashicons dashicons-no"></span> Cerrar
                    </button>
                </div>
            </div>
            
            <!-- Progress Bar -->
            <div class="wizard-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="wizard-progress-fill"></div>
                </div>
                <div class="progress-steps">
                    <?php foreach ($this->wizard_steps as $step_key => $step_name): ?>
                        <div class="progress-step" data-step="<?php echo esc_attr($step_key); ?>">
                            <div class="step-circle"></div>
                            <span class="step-label"><?php echo esc_html($step_name); ?></span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Wizard Content -->
            <div class="wizard-content">
                <!-- Step 1: Welcome -->
                <div class="wizard-step active" data-step="welcome">
                    <div class="step-content">
                        <div class="welcome-hero">
                            <div class="dominican-flag">🇩🇴</div>
                            <h2>¡Bienvenido al Wizard de SoloYLibre!</h2>
                            <p class="hero-subtitle">
                                Crea galerías profesionales para <strong>Jose L Encarnacion (JoseTusabe)</strong>
                            </p>
                            <p class="hero-description">
                                Este wizard te ayudará a seleccionar fotos, organizarlas y crear posts de WordPress 
                                con galerías hermosas en pocos pasos.
                            </p>
                        </div>
                        
                        <div class="feature-grid">
                            <div class="feature-card clickable" onclick="openSmartSelection()">
                                <div class="feature-icon">📸</div>
                                <h3>Selección Inteligente</h3>
                                <p>Selecciona fotos de tu biblioteca de medios con vista previa</p>
                                <div class="feature-action">
                                    <button class="btn btn-primary">🎯 Abrir Selector</button>
                                </div>
                            </div>
                            <div class="feature-card clickable" onclick="openDominicanStyles()">
                                <div class="feature-icon">🎨</div>
                                <h3>Estilos Dominicanos</h3>
                                <p>Galerías con temática de República Dominicana</p>
                                <div class="feature-action">
                                    <button class="btn btn-primary">🇩🇴 Ver Estilos</button>
                                </div>
                            </div>
                            <div class="feature-card clickable" onclick="openAutomaticPosts()">
                                <div class="feature-icon">📝</div>
                                <h3>Posts Automáticos</h3>
                                <p>Crea posts de WordPress con galerías integradas</p>
                                <div class="feature-action">
                                    <button class="btn btn-primary">🤖 Crear Post</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 2: Select Photos -->
                <div class="wizard-step" data-step="select_photos">
                    <div class="step-content">
                        <h2>📸 Seleccionar Fotos</h2>
                        <p>Selecciona las fotos que quieres incluir en tu galería</p>
                        
                        <div class="photo-selection-controls">
                            <div class="search-box">
                                <input type="text" id="photo-search" placeholder="Buscar fotos...">
                                <button class="search-btn">🔍</button>
                            </div>
                            <div class="filter-controls">
                                <select id="photo-category-filter">
                                    <option value="">Todas las categorías</option>
                                </select>
                                <select id="photo-date-filter">
                                    <option value="">Todas las fechas</option>
                                    <option value="today">Hoy</option>
                                    <option value="week">Esta semana</option>
                                    <option value="month">Este mes</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="selected-counter">
                            <span id="selected-count">0</span> fotos seleccionadas
                        </div>
                        
                        <div id="photo-grid" class="photo-grid">
                            <!-- Photos will be loaded here via AJAX -->
                        </div>
                        
                        <div class="loading-spinner" id="photos-loading">
                            <div class="spinner"></div>
                            <p>Cargando fotos...</p>
                        </div>
                    </div>
                </div>
                
                <!-- Step 3: Organize -->
                <div class="wizard-step" data-step="organize">
                    <div class="step-content">
                        <h2>🎨 Organizar Galería</h2>
                        <p>Organiza y configura tu galería</p>
                        
                        <div class="organize-grid">
                            <div class="organize-section">
                                <h3>Fotos Seleccionadas</h3>
                                <div id="selected-photos-preview" class="selected-photos-grid">
                                    <!-- Selected photos will appear here -->
                                </div>
                            </div>
                            
                            <div class="organize-section">
                                <h3>Configuración de Galería</h3>
                                <div class="form-group">
                                    <label for="gallery-style">Estilo de Galería:</label>
                                    <select id="gallery-style">
                                        <option value="grid">Grid Profesional</option>
                                        <option value="dominican">Estilo Dominicano 🇩🇴</option>
                                        <option value="masonry">Masonry</option>
                                        <option value="carousel">Carousel</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="gallery-columns">Columnas:</label>
                                    <select id="gallery-columns">
                                        <option value="2">2 Columnas</option>
                                        <option value="3" selected>3 Columnas</option>
                                        <option value="4">4 Columnas</option>
                                        <option value="5">5 Columnas</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="show-captions" checked>
                                        Mostrar títulos de fotos
                                    </label>
                                </div>
                                
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="enable-lightbox" checked>
                                        Habilitar lightbox
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 4: Settings -->
                <div class="wizard-step" data-step="settings">
                    <div class="step-content">
                        <h2>⚙️ Configuración del Post</h2>
                        <p>Configura los detalles del post que se creará</p>
                        
                        <div class="settings-form">
                            <div class="form-group">
                                <label for="post-title">Título del Post:</label>
                                <input type="text" id="post-title" placeholder="Ej: Galería de Paisajes Dominicanos">
                            </div>
                            
                            <div class="form-group">
                                <label for="post-content">Descripción:</label>
                                <textarea id="post-content" rows="4" placeholder="Describe tu galería..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="post-category">Categoría del Post:</label>
                                <select id="post-category">
                                    <option value="">Seleccionar categoría...</option>
                                    <?php
                                    $categories = get_categories();
                                    foreach ($categories as $category) {
                                        echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="post-tags">Tags (separados por comas):</label>
                                <input type="text" id="post-tags" placeholder="fotografía, dominicana, paisajes">
                            </div>
                            
                            <div class="form-group">
                                <label for="post-status">Estado del Post:</label>
                                <select id="post-status">
                                    <option value="draft">Borrador</option>
                                    <option value="publish">Publicar inmediatamente</option>
                                </select>
                            </div>
                            
                            <div class="photographer-signature">
                                <h4>📸 Firma del Fotógrafo</h4>
                                <div class="signature-preview">
                                    <p><strong>Jose L Encarnacion (JoseTusabe)</strong></p>
                                    <p>SoloYLibre Photography</p>
                                    <p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
                                    <p>📞 ************ | 📧 <EMAIL></p>
                                </div>
                                <label>
                                    <input type="checkbox" id="include-signature" checked>
                                    Incluir firma en el post
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 5: Create Post -->
                <div class="wizard-step" data-step="create_post">
                    <div class="step-content">
                        <h2>🚀 Crear Post</h2>
                        <p>Revisión final y creación del post</p>
                        
                        <div class="creation-preview">
                            <div class="preview-section">
                                <h3>Vista Previa del Post</h3>
                                <div id="post-preview" class="post-preview-container">
                                    <!-- Preview will be generated here -->
                                </div>
                            </div>
                            
                            <div class="creation-status">
                                <div id="creation-progress" class="creation-progress hidden">
                                    <div class="progress-circle">
                                        <div class="spinner"></div>
                                    </div>
                                    <p>Creando tu galería...</p>
                                </div>
                                
                                <div id="creation-success" class="creation-result hidden">
                                    <div class="success-icon">✅</div>
                                    <h3>¡Post Creado Exitosamente!</h3>
                                    <p>Tu galería ha sido creada y está lista.</p>
                                    <div class="result-actions">
                                        <a href="#" id="view-post-link" class="wizard-btn wizard-btn-primary" target="_blank">
                                            Ver Post
                                        </a>
                                        <a href="#" id="edit-post-link" class="wizard-btn wizard-btn-secondary" target="_blank">
                                            Editar Post
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 6: Complete -->
                <div class="wizard-step" data-step="complete">
                    <div class="step-content">
                        <div class="completion-hero">
                            <div class="success-animation">🎉</div>
                            <h2>¡Galería Creada Exitosamente!</h2>
                            <p>Tu galería profesional de SoloYLibre está lista</p>
                            
                            <div class="completion-stats">
                                <div class="stat-item">
                                    <span class="stat-number" id="final-photo-count">0</span>
                                    <span class="stat-label">Fotos</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">1</span>
                                    <span class="stat-label">Post Creado</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">100%</span>
                                    <span class="stat-label">Completado</span>
                                </div>
                            </div>
                            
                            <div class="completion-actions">
                                <button id="create-another" class="wizard-btn wizard-btn-primary">
                                    📸 Crear Otra Galería
                                </button>
                                <button id="go-to-dashboard" class="wizard-btn wizard-btn-secondary">
                                    📊 Ir al Dashboard
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Wizard Navigation -->
            <div class="wizard-navigation">
                <button id="prev-step" class="wizard-btn wizard-btn-secondary" disabled>
                    ← Anterior
                </button>
                <div class="nav-info">
                    <span id="current-step-info">Paso 1 de 6</span>
                </div>
                <button id="next-step" class="wizard-btn wizard-btn-primary">
                    Siguiente →
                </button>
            </div>
        </div>
        
        <!-- Minimized State -->
        <div id="wizard-minimized" class="wizard-minimized hidden">
            <div class="minimized-content">
                <span class="minimized-icon">📸</span>
                <span class="minimized-text">SoloYLibre Wizard</span>
                <button id="restore-wizard" class="restore-btn">↗</button>
            </div>
        </div>
        <?php
    }
    
    /**
     * AJAX: Get photos for selection
     */
    public function ajax_get_photos() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $search = sanitize_text_field($_POST['search'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');
        $date_filter = sanitize_text_field($_POST['date_filter'] ?? '');
        
        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => 50,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        if (!empty($search)) {
            $args['s'] = $search;
        }
        
        if (!empty($date_filter)) {
            switch ($date_filter) {
                case 'today':
                    $args['date_query'] = array(
                        array('after' => '1 day ago')
                    );
                    break;
                case 'week':
                    $args['date_query'] = array(
                        array('after' => '1 week ago')
                    );
                    break;
                case 'month':
                    $args['date_query'] = array(
                        array('after' => '1 month ago')
                    );
                    break;
            }
        }
        
        $photos = get_posts($args);
        $photo_data = array();
        
        foreach ($photos as $photo) {
            $photo_data[] = array(
                'id' => $photo->ID,
                'title' => $photo->post_title,
                'url' => wp_get_attachment_url($photo->ID),
                'thumbnail' => wp_get_attachment_image_url($photo->ID, 'medium'),
                'alt' => get_post_meta($photo->ID, '_wp_attachment_image_alt', true)
            );
        }
        
        wp_send_json_success($photo_data);
    }
    
    /**
     * AJAX: Create post with gallery
     */
    public function ajax_create_post() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());
        $post_title = sanitize_text_field($_POST['post_title'] ?? '');
        $post_content = wp_kses_post($_POST['post_content'] ?? '');
        $post_category = intval($_POST['post_category'] ?? 0);
        $post_tags = sanitize_text_field($_POST['post_tags'] ?? '');
        $post_status = sanitize_text_field($_POST['post_status'] ?? 'draft');
        $gallery_style = sanitize_text_field($_POST['gallery_style'] ?? 'grid');
        $gallery_columns = intval($_POST['gallery_columns'] ?? 4);
        $include_signature = !empty($_POST['include_signature']);

        if (empty($photo_ids) || empty($post_title)) {
            wp_send_json_error('Faltan datos requeridos');
        }

        // Create gallery shortcode
        $gallery_shortcode = '[soloylibre_gallery style="' . $gallery_style . '" ids="' . implode(',', $photo_ids) . '" columns="' . $gallery_columns . '" enable_interactions="true" aspect_ratio="natural"]';
        
        // Add photographer signature if requested
        $signature = '';
        if ($include_signature) {
            $signature = '
            
<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #CE1126; margin-top: 40px;">
    <h4 style="color: #CE1126; margin-bottom: 10px;">📸 Fotografía por Jose L Encarnacion (JoseTusabe)</h4>
    <p><strong>SoloYLibre Photography</strong></p>
    <p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
    <p>📞 ************ | 📧 <EMAIL></p>
    <p>🌐 <a href="https://josetusabe.com">josetusabe.com</a> | <a href="https://soloylibre.com">soloylibre.com</a></p>
</div>';
        }
        
        // Create post
        $post_data = array(
            'post_title' => $post_title,
            'post_content' => $post_content . "\n\n" . $gallery_shortcode . $signature,
            'post_status' => $post_status,
            'post_type' => 'post',
            'post_author' => get_current_user_id()
        );
        
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            wp_send_json_error('Error al crear el post');
        }
        
        // Set category
        if ($post_category > 0) {
            wp_set_post_categories($post_id, array($post_category));
        }
        
        // Set tags
        if (!empty($post_tags)) {
            wp_set_post_tags($post_id, $post_tags);
        }
        
        // Set featured image (first photo)
        if (!empty($photo_ids)) {
            set_post_thumbnail($post_id, $photo_ids[0]);
        }
        
        // Save gallery metadata
        update_post_meta($post_id, '_soloylibre_gallery_photos', $photo_ids);
        update_post_meta($post_id, '_soloylibre_gallery_style', $gallery_style);
        update_post_meta($post_id, '_soloylibre_created_by_wizard', true);
        
        wp_send_json_success(array(
            'post_id' => $post_id,
            'post_url' => get_permalink($post_id),
            'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit'),
            'photo_count' => count($photo_ids)
        ));
    }
    
    /**
     * AJAX: Save wizard step data
     */
    public function ajax_save_step() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');
        
        $step = sanitize_text_field($_POST['step'] ?? '');
        $data = $_POST['data'] ?? array();
        
        // Save step data to user meta for persistence
        $user_id = get_current_user_id();
        update_user_meta($user_id, 'soloylibre_wizard_' . $step, $data);
        
        wp_send_json_success();
    }

    /**
     * AJAX: Smart Selection - Selección Inteligente
     */
    public function ajax_smart_selection() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        // Obtener fotos disponibles (no publicadas)
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unused_photos = get_option('soloylibre_unused_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        $categorized_photos = array_merge($published_photos, $private_photos, $unused_photos, $unwanted_photos);

        $available_photos = array();
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $categorized_photos)) {
                $available_photos[] = array(
                    'id' => $photo->ID,
                    'title' => get_the_title($photo->ID) ?: 'Sin título',
                    'url' => wp_get_attachment_image_url($photo->ID, 'medium'),
                    'thumbnail' => wp_get_attachment_image_url($photo->ID, 'thumbnail'),
                    'date' => $photo->post_date,
                    'size' => $this->get_file_size($photo->ID)
                );
            }
        }

        // Selección inteligente: mejores fotos basadas en tamaño y fecha
        usort($available_photos, function($a, $b) {
            // Priorizar fotos más recientes y de mayor tamaño
            $date_diff = strtotime($b['date']) - strtotime($a['date']);
            return $date_diff;
        });

        $smart_selection = array_slice($available_photos, 0, 30); // Top 30 fotos

        wp_send_json_success(array(
            'photos' => $smart_selection,
            'total_available' => count($available_photos),
            'selection_count' => count($smart_selection),
            'message' => 'Selección inteligente completada con ' . count($smart_selection) . ' fotos de alta calidad'
        ));
    }

    /**
     * AJAX: Dominican Styles - Estilos Dominicanos
     */
    public function ajax_dominican_styles() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        $dominican_styles = array(
            'bandera' => array(
                'name' => '🇩🇴 Estilo Bandera Dominicana',
                'description' => 'Galería con colores de la bandera: rojo, azul y blanco',
                'css' => array(
                    'background' => 'linear-gradient(135deg, #CE1126 0%, #FFFFFF 50%, #002D62 100%)',
                    'border' => '3px solid #CE1126',
                    'accent_color' => '#CE1126'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-bandera.jpg'
            ),
            'merengue' => array(
                'name' => '🎵 Estilo Merengue',
                'description' => 'Colores vibrantes inspirados en el merengue dominicano',
                'css' => array(
                    'background' => 'linear-gradient(45deg, #FF6B35, #F7931E, #FFD23F)',
                    'border' => '2px solid #FF6B35',
                    'accent_color' => '#FF6B35'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-merengue.jpg'
            ),
            'caribe' => array(
                'name' => '🏝️ Estilo Caribeño',
                'description' => 'Azules del mar Caribe y verdes tropicales',
                'css' => array(
                    'background' => 'linear-gradient(135deg, #00B4DB 0%, #0083B0 100%)',
                    'border' => '2px solid #00B4DB',
                    'accent_color' => '#00B4DB'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-caribe.jpg'
            ),
            'colonial' => array(
                'name' => '🏛️ Estilo Colonial',
                'description' => 'Elegancia de la arquitectura colonial dominicana',
                'css' => array(
                    'background' => 'linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%)',
                    'border' => '3px solid #8B4513',
                    'accent_color' => '#8B4513'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-colonial.jpg'
            ),
            'bachata' => array(
                'name' => '💃 Estilo Bachata',
                'description' => 'Colores románticos de la bachata dominicana',
                'css' => array(
                    'background' => 'linear-gradient(135deg, #FF69B4 0%, #FF1493 50%, #DC143C 100%)',
                    'border' => '2px solid #FF1493',
                    'accent_color' => '#FF1493'
                ),
                'preview' => SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/images/style-bachata.jpg'
            )
        );

        wp_send_json_success(array(
            'styles' => $dominican_styles,
            'message' => 'Estilos dominicanos cargados exitosamente',
            'total_styles' => count($dominican_styles)
        ));
    }

    /**
     * AJAX: Automatic Posts - Posts Automáticos
     */
    public function ajax_automatic_posts() {
        check_ajax_referer('soloylibre_wizard_nonce', 'nonce');

        $post_type = sanitize_text_field($_POST['post_type'] ?? 'auto');
        $photo_count = intval($_POST['photo_count'] ?? 15);
        $style = sanitize_text_field($_POST['style'] ?? 'masonry');

        // Obtener fotos disponibles
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        $published_photos = get_option('soloylibre_published_photos', array());
        $categorized_photos = array_merge(
            $published_photos,
            get_option('soloylibre_private_photos', array()),
            get_option('soloylibre_unused_photos', array()),
            get_option('soloylibre_unwanted_photos', array())
        );

        $available_photos = array();
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $categorized_photos)) {
                $available_photos[] = $photo->ID;
            }
        }

        if (empty($available_photos)) {
            wp_send_json_error('No hay fotos disponibles para crear posts automáticos');
            return;
        }

        // Seleccionar fotos para el post
        $selected_photos = array_slice($available_photos, 0, min($photo_count, count($available_photos)));

        // Generar título automático
        $photographer_info = get_option('soloylibre_photographer_info', array(
            'nickname' => 'JoseTusabe',
            'business_name' => 'SoloYLibre Photography'
        ));

        $auto_titles = array(
            "📸 Galería Automática - {$photographer_info['nickname']} " . date('d/m/Y'),
            "🌟 Nuevas Capturas SoloYLibre - " . date('F Y'),
            "📷 Selección Automática del Día - " . date('d/m/Y'),
            "🎯 Auto-Post SoloYLibre - " . date('d/m/Y'),
            "✨ Fotos Destacadas - {$photographer_info['business_name']} " . date('d/m/Y'),
            "🇩🇴 Galería Dominicana - " . date('d/m/Y'),
            "📸 Momentos Capturados - San José de Ocoa " . date('d/m/Y')
        );

        $title = $auto_titles[array_rand($auto_titles)];

        // Crear contenido del post
        $columns = intval($_POST['columns'] ?? 4);
        $post_content = "[soloylibre_gallery style=\"$style\" ids=\"" . implode(',', $selected_photos) . "\" columns=\"$columns\" enable_interactions=\"true\" aspect_ratio=\"natural\"]\n\n";
        $post_content .= "<p>📸 <strong>Galería creada automáticamente por SoloYLibre Gallery Pro</strong></p>\n";
        $post_content .= "<p>🇩🇴 <em>Fotografía profesional desde San José de Ocoa, República Dominicana</em></p>\n";
        $post_content .= "<p>👨‍💻 <strong>Jose L Encarnacion (JoseTusabe)</strong> | 📧 <EMAIL> | 📞 ************</p>";

        // Crear el post
        $post_id = wp_insert_post(array(
            'post_title' => $title,
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => get_current_user_id(),
            'post_category' => array(get_option('default_category')),
            'meta_input' => array(
                '_soloylibre_gallery_photos' => $selected_photos,
                '_soloylibre_gallery_style' => $style,
                '_soloylibre_auto_generated' => true,
                '_soloylibre_creation_date' => current_time('mysql'),
                '_soloylibre_photographer' => $photographer_info['nickname']
            )
        ));

        if ($post_id) {
            // Marcar fotos como publicadas
            $published_photos = array_unique(array_merge($published_photos, $selected_photos));
            update_option('soloylibre_published_photos', $published_photos);

            // Actualizar estadísticas
            $monthly_key = 'soloylibre_monthly_published_' . date('Y_m');
            $monthly_published = get_option($monthly_key, array());
            $monthly_published = array_unique(array_merge($monthly_published, $selected_photos));
            update_option($monthly_key, $monthly_published);

            // Incrementar contador
            $auto_posts = get_option('soloylibre_auto_posts_created', 0);
            update_option('soloylibre_auto_posts_created', $auto_posts + 1);

            wp_send_json_success(array(
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'edit_url' => get_edit_post_link($post_id),
                'title' => $title,
                'photos_count' => count($selected_photos),
                'message' => "Post automático creado exitosamente: '$title' con " . count($selected_photos) . " fotos"
            ));
        } else {
            wp_send_json_error('Error al crear el post automático');
        }
    }

    /**
     * Get file size helper
     */
    private function get_file_size($attachment_id) {
        $file_path = get_attached_file($attachment_id);
        if ($file_path && file_exists($file_path)) {
            $size = filesize($file_path);
            if ($size >= 1048576) {
                return round($size / 1048576, 1) . ' MB';
            } elseif ($size >= 1024) {
                return round($size / 1024, 1) . ' KB';
            }
            return $size . ' B';
        }
        return 'N/A';
    }
}
