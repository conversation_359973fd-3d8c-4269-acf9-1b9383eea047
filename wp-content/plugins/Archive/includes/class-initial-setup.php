<?php
/**
 * Initial Setup Class for SoloYLibre Gallery Pro
 * Handles the initial configuration and setup wizard
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Initial_Setup {

    private $setup_option_key = 'soloylibre_gallery_setup_complete';
    private $photographer_data = array(
        'name' => 'Jose L Encarnac<PERSON>',
        'alias' => 'JoseTusabe',
        'brand' => 'SoloYLibre Photography',
        'email' => '<EMAIL>',
        'phone' => '************',
        'location' => 'San José de <PERSON>coa, República Dominicana / USA',
        'websites' => array(
            'josetusabe.com',
            'soloylibre.com',
            '1and1photo.com',
            'joselencarnacion.com'
        )
    );

    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_notices', array($this, 'show_setup_notice'));
        add_action('wp_ajax_soloylibre_complete_setup', array($this, 'ajax_complete_setup'));
        add_action('wp_ajax_soloylibre_skip_setup', array($this, 'ajax_skip_setup'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_setup_scripts'));
    }

    /**
     * Check if setup is complete
     */
    public function is_setup_complete() {
        return get_option($this->setup_option_key, false);
    }

    /**
     * Show setup notice
     */
    public function show_setup_notice() {
        if ($this->is_setup_complete()) {
            return;
        }

        // Only show on admin pages
        if (!is_admin()) {
            return;
        }

        // Don't show on setup page itself
        if (isset($_GET['page']) && $_GET['page'] === 'soloylibre-setup') {
            return;
        }

        ?>
        <div class="notice notice-info is-dismissible soloylibre-setup-notice" id="soloylibre-setup-notice">
            <div class="soloylibre-setup-content">
                <div class="setup-icon">📸</div>
                <div class="setup-text">
                    <h3>🇩🇴 Configuración Inicial Requerida - SoloYLibre Gallery Pro</h3>
                    <p>
                        <strong>¡Bienvenido Jose L Encarnacion (JoseTusabe)!</strong><br>
                        SoloYLibre Gallery Pro necesita configuración inicial para funcionar correctamente.
                        Configura tu información de fotógrafo profesional dominicano.
                    </p>
                </div>
                <div class="setup-actions">
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-setup'); ?>" class="button button-primary setup-btn">
                        🚀 Configurar Ahora
                    </a>
                    <button class="button setup-skip-btn" data-action="skip">
                        Omitir por ahora
                    </button>
                </div>
            </div>
        </div>

        <style>
        .soloylibre-setup-notice {
            border-left: 4px solid #CE1126 !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 20px !important;
            margin: 15px 0 !important;
        }

        .soloylibre-setup-content {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .setup-icon {
            font-size: 3rem;
            flex-shrink: 0;
        }

        .setup-text {
            flex: 1;
        }

        .setup-text h3 {
            margin: 0 0 10px 0;
            color: #CE1126;
            font-size: 1.2rem;
        }

        .setup-text p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }

        .setup-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
            flex-shrink: 0;
        }

        .setup-btn {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%) !important;
            border: none !important;
            color: white !important;
            padding: 12px 24px !important;
            border-radius: 25px !important;
            font-weight: 600 !important;
            text-decoration: none !important;
            transition: all 0.3s ease !important;
            text-align: center !important;
        }

        .setup-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(206, 17, 38, 0.3) !important;
            color: white !important;
        }

        .setup-skip-btn {
            background: transparent !important;
            border: 1px solid #ddd !important;
            color: #666 !important;
            padding: 8px 16px !important;
            border-radius: 20px !important;
            font-size: 0.9rem !important;
        }

        .setup-skip-btn:hover {
            background: #f0f0f0 !important;
        }

        @media (max-width: 768px) {
            .soloylibre-setup-content {
                flex-direction: column;
                text-align: center;
            }

            .setup-actions {
                width: 100%;
            }
        }
        </style>
        <?php
    }

    /**
     * Enqueue setup scripts
     */
    public function enqueue_setup_scripts() {
        if ($this->is_setup_complete()) {
            return;
        }

        wp_enqueue_script(
            'soloylibre-setup-js',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/setup.js',
            array('jquery'),
            SOLOYLIBRE_GALLERY_VERSION,
            true
        );

        wp_localize_script('soloylibre-setup-js', 'soloylibre_setup', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('soloylibre_setup_nonce'),
            'photographer' => $this->photographer_data,
            'texts' => array(
                'completing' => __('Completando configuración...', 'soloylibre-gallery'),
                'success' => __('¡Configuración completada!', 'soloylibre-gallery'),
                'error' => __('Error en la configuración', 'soloylibre-gallery'),
                'skipped' => __('Configuración omitida', 'soloylibre-gallery')
            )
        ));
    }

    /**
     * AJAX: Complete setup
     */
    public function ajax_complete_setup() {
        check_ajax_referer('soloylibre_setup_nonce', 'nonce');

        // Save photographer information
        $photographer_settings = array(
            'photographer_name' => $this->photographer_data['name'],
            'photographer_alias' => $this->photographer_data['alias'],
            'photographer_brand' => $this->photographer_data['brand'],
            'photographer_email' => $this->photographer_data['email'],
            'photographer_phone' => $this->photographer_data['phone'],
            'photographer_location' => $this->photographer_data['location'],
            'gallery_style' => 'dominican',
            'photos_per_page' => 12,
            'enable_infinite_scroll' => true,
            'enable_lightbox' => true,
            'show_photographer_info' => true,
            'dominican_theme' => true
        );

        foreach ($photographer_settings as $key => $value) {
            update_option('soloylibre_gallery_' . $key, $value);
        }

        // Save websites
        update_option('soloylibre_gallery_websites', $this->photographer_data['websites']);

        // Create default categories if they don't exist
        $default_categories = array(
            'paisajes' => 'Paisajes Dominicanos',
            'retratos' => 'Retratos',
            'eventos' => 'Eventos y Celebraciones',
            'arquitectura' => 'Arquitectura Colonial',
            'naturaleza' => 'Naturaleza y Vida Silvestre',
            'cultura' => 'Cultura Dominicana'
        );

        foreach ($default_categories as $slug => $name) {
            if (!term_exists($slug, 'photo_category')) {
                wp_insert_term($name, 'photo_category', array('slug' => $slug));
            }
        }

        // Create default tags
        $default_tags = array(
            'dominicana', 'republica-dominicana', 'caribe', 'tropical',
            'san-jose-de-ocoa', 'josetusabe', 'soloylibre', 'fotografia-profesional'
        );

        foreach ($default_tags as $tag) {
            if (!term_exists($tag, 'photo_tag')) {
                wp_insert_term(ucfirst(str_replace('-', ' ', $tag)), 'photo_tag', array('slug' => $tag));
            }
        }

        // Mark setup as complete
        update_option($this->setup_option_key, true);
        update_option('soloylibre_gallery_setup_date', current_time('mysql'));

        // Create welcome post
        $this->create_welcome_post();

        wp_send_json_success(array(
            'message' => '¡Configuración completada exitosamente!',
            'redirect_url' => admin_url('admin.php?page=soloylibre-wizard')
        ));
    }

    /**
     * AJAX: Skip setup
     */
    public function ajax_skip_setup() {
        check_ajax_referer('soloylibre_setup_nonce', 'nonce');

        // Mark setup as complete but with minimal configuration
        update_option($this->setup_option_key, true);
        update_option('soloylibre_gallery_setup_skipped', true);
        update_option('soloylibre_gallery_setup_date', current_time('mysql'));

        wp_send_json_success(array(
            'message' => 'Configuración omitida. Puedes configurar más tarde.',
            'redirect_url' => admin_url('admin.php?page=soloylibre-wizard')
        ));
    }

    /**
     * Create welcome post
     */
    private function create_welcome_post() {
        // Check if welcome post already exists
        $existing_post = get_posts(array(
            'meta_key' => '_soloylibre_welcome_post',
            'meta_value' => '1',
            'post_type' => 'post',
            'posts_per_page' => 1
        ));

        if (!empty($existing_post)) {
            return;
        }

        $welcome_content = '
<div style="text-align: center; padding: 40px 20px; background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; border-radius: 15px; margin-bottom: 30px;">
    <h1 style="color: white; font-size: 2.5rem; margin-bottom: 15px;">🇩🇴 ¡Bienvenido a SoloYLibre Photography!</h1>
    <h2 style="color: white; opacity: 0.9; font-size: 1.5rem; margin-bottom: 10px;">Jose L Encarnacion (JoseTusabe)</h2>
    <p style="font-size: 1.2rem; opacity: 0.8;">📍 San José de Ocoa, República Dominicana / USA</p>
    <p style="font-size: 1rem; opacity: 0.7;">📞 ************ | 📧 <EMAIL></p>
</div>

<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin: 40px 0;">
    <div style="background: #f8f9fa; padding: 30px; border-radius: 15px; border-left: 5px solid #CE1126;">
        <h3 style="color: #CE1126; margin-bottom: 15px;">📸 Fotografía Profesional</h3>
        <p>Especializado en capturar la esencia y belleza de República Dominicana, desde paisajes hasta cultura dominicana.</p>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 15px; border-left: 5px solid #002D62;">
        <h3 style="color: #002D62; margin-bottom: 15px;">🎨 Estilo Único</h3>
        <p>Combinando técnicas modernas con la rica cultura dominicana para crear imágenes memorables.</p>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 15px; border-left: 5px solid #28a745;">
        <h3 style="color: #28a745; margin-bottom: 15px;">🌐 Presencia Digital</h3>
        <p>Encuentra mi trabajo en múltiples plataformas digitales y sitios web profesionales.</p>
    </div>
</div>

<div style="text-align: center; margin: 50px 0;">
    <h3 style="color: #333; margin-bottom: 30px;">🌐 Visita Mis Sitios Web</h3>
    <p style="font-size: 1.1rem;">
        <a href="https://josetusabe.com" target="_blank" style="color: #CE1126; text-decoration: none; margin: 0 15px; font-weight: 600;">🌐 josetusabe.com</a>
        <a href="https://soloylibre.com" target="_blank" style="color: #002D62; text-decoration: none; margin: 0 15px; font-weight: 600;">🌐 soloylibre.com</a>
        <a href="https://1and1photo.com" target="_blank" style="color: #CE1126; text-decoration: none; margin: 0 15px; font-weight: 600;">📸 1and1photo.com</a>
        <a href="https://joselencarnacion.com" target="_blank" style="color: #002D62; text-decoration: none; margin: 0 15px; font-weight: 600;">👨‍💼 joselencarnacion.com</a>
    </p>
</div>

<div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 30px; border-radius: 15px; text-align: center; margin-top: 40px;">
    <h3 style="color: #333; margin-bottom: 20px;">📞 Información de Contacto</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; text-align: left;">
        <div>
            <strong>📧 Email:</strong> <EMAIL><br>
            <strong>📱 Teléfono:</strong> ************<br>
            <strong>📍 Ubicación:</strong> San José de Ocoa, Dom. Rep. / USA
        </div>
        <div>
            <strong>🎯 Especialidades:</strong> Paisajes, Retratos, Eventos<br>
            <strong>📷 Equipo:</strong> Canon EOS R5, Lentes Profesionales<br>
            <strong>🇩🇴 Origen:</strong> República Dominicana
        </div>
    </div>
</div>

<div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
    <p style="color: #666; font-style: italic; margin: 0;">
        "Capturando la belleza de República Dominicana y el mundo, una foto a la vez."<br>
        <strong style="color: #CE1126;">- Jose L Encarnacion (JoseTusabe)</strong>
    </p>
</div>
        ';

        $post_data = array(
            'post_title' => '🇩🇴 Bienvenido a SoloYLibre Photography - Jose L Encarnacion',
            'post_content' => $welcome_content,
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                '_soloylibre_welcome_post' => '1',
                '_soloylibre_featured_post' => '1'
            )
        );

        $post_id = wp_insert_post($post_data);

        if ($post_id) {
            // Set categories
            $category_ids = array();
            $welcome_categories = array('general', 'bienvenida', 'fotografia');

            foreach ($welcome_categories as $cat_slug) {
                $term = get_term_by('slug', $cat_slug, 'category');
                if (!$term) {
                    $term_data = wp_insert_term(ucfirst($cat_slug), 'category', array('slug' => $cat_slug));
                    if (!is_wp_error($term_data)) {
                        $category_ids[] = $term_data['term_id'];
                    }
                } else {
                    $category_ids[] = $term->term_id;
                }
            }

            if (!empty($category_ids)) {
                wp_set_post_categories($post_id, $category_ids);
            }

            // Set tags
            wp_set_post_tags($post_id, 'bienvenida, soloylibre, josetusabe, fotografia, dominicana');
        }
    }

    /**
     * Get setup progress
     */
    public function get_setup_progress() {
        if (!$this->is_setup_complete()) {
            return 0;
        }

        $progress_items = array(
            'photographer_name' => get_option('soloylibre_gallery_photographer_name'),
            'gallery_style' => get_option('soloylibre_gallery_gallery_style'),
            'categories' => get_terms(array('taxonomy' => 'photo_category', 'hide_empty' => false)),
            'welcome_post' => get_posts(array('meta_key' => '_soloylibre_welcome_post', 'meta_value' => '1'))
        );

        $completed = 0;
        $total = count($progress_items);

        foreach ($progress_items as $item) {
            if (!empty($item)) {
                $completed++;
            }
        }

        return round(($completed / $total) * 100);
    }
}