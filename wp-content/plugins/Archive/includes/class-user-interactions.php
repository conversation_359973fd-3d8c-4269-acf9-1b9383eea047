<?php
/**
 * User Interactions Class
 * Handles likes, dislikes, reactions, and engagement simulation
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_User_Interactions {
    
    /**
     * Available reactions
     */
    private $reactions = array(
        'like' => array(
            'emoji' => '❤️',
            'label' => 'Me gusta',
            'color' => '#e74c3c'
        ),
        'love' => array(
            'emoji' => '😍',
            'label' => 'Me encanta',
            'color' => '#e91e63'
        ),
        'wow' => array(
            'emoji' => '😮',
            'label' => 'Wow',
            'color' => '#f39c12'
        ),
        'amazing' => array(
            'emoji' => '🤩',
            'label' => 'Increíble',
            'color' => '#9b59b6'
        ),
        'fire' => array(
            'emoji' => '🔥',
            'label' => 'Fuego',
            'color' => '#e67e22'
        ),
        'camera' => array(
            'emoji' => '📸',
            'label' => 'Gran foto',
            'color' => '#3498db'
        )
    );
    
    public function __construct() {
        $this->init_hooks();
        $this->schedule_random_interactions();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX handlers
        add_action('wp_ajax_add_photo_reaction', array($this, 'ajax_add_reaction'));
        add_action('wp_ajax_nopriv_add_photo_reaction', array($this, 'ajax_add_reaction'));
        add_action('wp_ajax_remove_photo_reaction', array($this, 'ajax_remove_reaction'));
        add_action('wp_ajax_nopriv_remove_photo_reaction', array($this, 'ajax_remove_reaction'));
        add_action('wp_ajax_get_photo_reactions', array($this, 'ajax_get_reactions'));
        add_action('wp_ajax_nopriv_get_photo_reactions', array($this, 'ajax_get_reactions'));
        add_action('wp_ajax_generate_random_interactions', array($this, 'ajax_generate_random_interactions'));
        
        // Enqueue scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_interaction_scripts'));
        
        // Add meta boxes
        add_action('add_meta_boxes', array($this, 'add_interactions_meta_box'));
        
        // Add admin columns
        add_filter('manage_soloylibre_photo_posts_columns', array($this, 'add_interactions_column'));
        add_action('manage_soloylibre_photo_posts_custom_column', array($this, 'display_interactions_column'), 10, 2);
        
        // Scheduled events
        add_action('soloylibre_generate_random_interactions', array($this, 'generate_random_interactions_cron'));
        
        // Database tables
        add_action('init', array($this, 'create_interactions_table'));
    }
    
    /**
     * Create interactions database table
     */
    public function create_interactions_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_interactions';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            photo_id bigint(20) NOT NULL,
            user_id bigint(20) DEFAULT NULL,
            user_ip varchar(45) DEFAULT NULL,
            reaction_type varchar(20) NOT NULL,
            is_simulated tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY photo_id (photo_id),
            KEY user_id (user_id),
            KEY reaction_type (reaction_type),
            UNIQUE KEY unique_user_photo_reaction (photo_id, user_id, reaction_type)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Add reaction to photo
     */
    public function add_reaction($photo_id, $reaction_type, $user_id = null, $user_ip = null, $is_simulated = false) {
        global $wpdb;
        
        if (!array_key_exists($reaction_type, $this->reactions)) {
            return new WP_Error('invalid_reaction', __('Tipo de reacción inválido.', 'soloylibre-gallery'));
        }
        
        $table_name = $wpdb->prefix . 'soloylibre_interactions';
        
        // Get user info if not provided
        if (!$user_id && !$is_simulated) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_ip && !$is_simulated) {
            $user_ip = $this->get_user_ip();
        }
        
        // Check if user already reacted
        if ($user_id && !$is_simulated) {
            $existing = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE photo_id = %d AND user_id = %d AND reaction_type = %s",
                $photo_id, $user_id, $reaction_type
            ));
            
            if ($existing) {
                return new WP_Error('already_reacted', __('Ya has reaccionado a esta foto.', 'soloylibre-gallery'));
            }
        }
        
        // Insert reaction
        $result = $wpdb->insert(
            $table_name,
            array(
                'photo_id' => $photo_id,
                'user_id' => $user_id,
                'user_ip' => $user_ip,
                'reaction_type' => $reaction_type,
                'is_simulated' => $is_simulated ? 1 : 0,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%d', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('reaction_failed', __('Error al añadir reacción.', 'soloylibre-gallery'));
        }
        
        // Update reaction count cache
        $this->update_reaction_counts($photo_id);
        
        // Trigger action
        do_action('soloylibre_reaction_added', $photo_id, $reaction_type, $user_id, $is_simulated);
        
        return $wpdb->insert_id;
    }
    
    /**
     * Remove reaction from photo
     */
    public function remove_reaction($photo_id, $reaction_type, $user_id = null) {
        global $wpdb;
        
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return new WP_Error('no_user', __('Usuario no identificado.', 'soloylibre-gallery'));
        }
        
        $table_name = $wpdb->prefix . 'soloylibre_interactions';
        
        $result = $wpdb->delete(
            $table_name,
            array(
                'photo_id' => $photo_id,
                'user_id' => $user_id,
                'reaction_type' => $reaction_type
            ),
            array('%d', '%d', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('remove_failed', __('Error al quitar reacción.', 'soloylibre-gallery'));
        }
        
        // Update reaction count cache
        $this->update_reaction_counts($photo_id);
        
        // Trigger action
        do_action('soloylibre_reaction_removed', $photo_id, $reaction_type, $user_id);
        
        return true;
    }
    
    /**
     * Get photo reactions
     */
    public function get_photo_reactions($photo_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_interactions';
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT reaction_type, COUNT(*) as count 
             FROM $table_name 
             WHERE photo_id = %d 
             GROUP BY reaction_type 
             ORDER BY count DESC",
            $photo_id
        ));
        
        $reactions = array();
        $total_reactions = 0;
        
        foreach ($results as $result) {
            $reaction_info = $this->reactions[$result->reaction_type] ?? null;
            if ($reaction_info) {
                $reactions[$result->reaction_type] = array(
                    'count' => intval($result->count),
                    'emoji' => $reaction_info['emoji'],
                    'label' => $reaction_info['label'],
                    'color' => $reaction_info['color']
                );
                $total_reactions += intval($result->count);
            }
        }
        
        return array(
            'reactions' => $reactions,
            'total' => $total_reactions
        );
    }
    
    /**
     * Get user reaction for photo
     */
    public function get_user_reaction($photo_id, $user_id = null) {
        global $wpdb;
        
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        
        if (!$user_id) {
            return null;
        }
        
        $table_name = $wpdb->prefix . 'soloylibre_interactions';
        
        return $wpdb->get_var($wpdb->prepare(
            "SELECT reaction_type FROM $table_name WHERE photo_id = %d AND user_id = %d LIMIT 1",
            $photo_id, $user_id
        ));
    }
    
    /**
     * Update reaction counts cache
     */
    private function update_reaction_counts($photo_id) {
        $reactions_data = $this->get_photo_reactions($photo_id);
        
        // Store total count
        update_post_meta($photo_id, '_soloylibre_total_reactions', $reactions_data['total']);
        
        // Store individual reaction counts
        foreach ($this->reactions as $reaction_type => $reaction_info) {
            $count = $reactions_data['reactions'][$reaction_type]['count'] ?? 0;
            update_post_meta($photo_id, '_soloylibre_reactions_' . $reaction_type, $count);
        }
        
        // Store most popular reaction
        if (!empty($reactions_data['reactions'])) {
            $most_popular = array_keys($reactions_data['reactions'])[0];
            update_post_meta($photo_id, '_soloylibre_most_popular_reaction', $most_popular);
        }
    }
    
    /**
     * Generate random interactions for engagement
     */
    public function generate_random_interactions($photo_id = null, $count = null) {
        if (!$photo_id) {
            // Get random photos that need engagement
            $photos = get_posts(array(
                'post_type' => 'soloylibre_photo',
                'post_status' => array('publish', 'draft'),
                'posts_per_page' => 10,
                'orderby' => 'rand',
                'meta_query' => array(
                    array(
                        'key' => '_soloylibre_photo_state',
                        'value' => array('public', 'private'),
                        'compare' => 'IN'
                    )
                )
            ));
        } else {
            $photos = array(get_post($photo_id));
        }
        
        if (empty($photos)) {
            return 0;
        }
        
        $generated_count = 0;
        
        foreach ($photos as $photo) {
            if (!$count) {
                // Generate random number of interactions (1-15)
                $interactions_to_add = rand(1, 15);
            } else {
                $interactions_to_add = $count;
            }
            
            for ($i = 0; $i < $interactions_to_add; $i++) {
                // Random reaction type
                $reaction_types = array_keys($this->reactions);
                $reaction_type = $reaction_types[array_rand($reaction_types)];
                
                // Generate fake user data
                $fake_user_id = null; // Simulated users don't have real user IDs
                $fake_ip = $this->generate_fake_ip();
                
                $result = $this->add_reaction($photo->ID, $reaction_type, $fake_user_id, $fake_ip, true);
                
                if (!is_wp_error($result)) {
                    $generated_count++;
                }
                
                // Small delay to make it more realistic
                usleep(100000); // 0.1 seconds
            }
        }
        
        return $generated_count;
    }
    
    /**
     * Schedule random interactions
     */
    private function schedule_random_interactions() {
        if (!wp_next_scheduled('soloylibre_generate_random_interactions')) {
            wp_schedule_event(time(), 'hourly', 'soloylibre_generate_random_interactions');
        }
    }
    
    /**
     * Cron job for random interactions
     */
    public function generate_random_interactions_cron() {
        if (get_option('soloylibre_gallery_enable_random_interactions', true)) {
            $this->generate_random_interactions();
        }
    }
    
    /**
     * Generate fake IP address
     */
    private function generate_fake_ip() {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }
    
    /**
     * Get user IP address
     */
    private function get_user_ip() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        }
    }
    
    /**
     * Enqueue interaction scripts
     */
    public function enqueue_interaction_scripts() {
        if (is_singular() && has_shortcode(get_post()->post_content, 'soloylibre_gallery')) {
            wp_enqueue_script(
                'soloylibre-interactions',
                SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/interactions.js',
                array('jquery'),
                SOLOYLIBRE_GALLERY_VERSION,
                true
            );
            
            wp_localize_script('soloylibre-interactions', 'soloylibre_interactions', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('soloylibre_interactions_nonce'),
                'reactions' => $this->reactions,
                'user_id' => get_current_user_id(),
                'strings' => array(
                    'loading' => __('Cargando...', 'soloylibre-gallery'),
                    'error' => __('Error al procesar reacción', 'soloylibre-gallery'),
                    'login_required' => __('Debes iniciar sesión para reaccionar', 'soloylibre-gallery')
                )
            ));
        }
    }
    
    /**
     * Add interactions meta box
     */
    public function add_interactions_meta_box() {
        add_meta_box(
            'soloylibre_interactions_meta',
            __('💝 Interacciones y Reacciones', 'soloylibre-gallery'),
            array($this, 'render_interactions_meta_box'),
            'soloylibre_photo',
            'normal',
            'default'
        );
    }
    
    /**
     * Render interactions meta box
     */
    public function render_interactions_meta_box($post) {
        $reactions_data = $this->get_photo_reactions($post->ID);
        $total_reactions = $reactions_data['total'];
        
        ?>
        <div class="soloylibre-interactions-meta">
            <div class="interactions-summary">
                <h4>📊 Resumen de Interacciones</h4>
                <div class="total-reactions">
                    <span class="total-count"><?php echo number_format($total_reactions); ?></span>
                    <span class="total-label">reacciones totales</span>
                </div>
            </div>
            
            <div class="reactions-breakdown">
                <h4>🎭 Desglose por Reacción</h4>
                <div class="reactions-grid">
                    <?php foreach ($this->reactions as $reaction_type => $reaction_info): ?>
                        <?php 
                        $count = $reactions_data['reactions'][$reaction_type]['count'] ?? 0;
                        $percentage = $total_reactions > 0 ? ($count / $total_reactions) * 100 : 0;
                        ?>
                        <div class="reaction-item">
                            <div class="reaction-header">
                                <span class="reaction-emoji"><?php echo $reaction_info['emoji']; ?></span>
                                <span class="reaction-label"><?php echo esc_html($reaction_info['label']); ?></span>
                                <span class="reaction-count"><?php echo number_format($count); ?></span>
                            </div>
                            <div class="reaction-bar">
                                <div class="reaction-fill" style="width: <?php echo $percentage; ?>%; background-color: <?php echo $reaction_info['color']; ?>;"></div>
                            </div>
                            <div class="reaction-percentage"><?php echo round($percentage, 1); ?>%</div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <div class="interactions-actions">
                <h4>⚡ Acciones Rápidas</h4>
                <div class="action-buttons">
                    <button type="button" class="button" id="generate-random-interactions" data-photo-id="<?php echo $post->ID; ?>">
                        🎲 Generar Interacciones Aleatorias
                    </button>
                    <button type="button" class="button" id="clear-all-interactions" data-photo-id="<?php echo $post->ID; ?>">
                        🗑️ Limpiar Todas las Interacciones
                    </button>
                    <button type="button" class="button" id="refresh-interactions" data-photo-id="<?php echo $post->ID; ?>">
                        🔄 Actualizar Estadísticas
                    </button>
                </div>
                
                <div class="manual-interaction">
                    <label for="manual-reaction-count">Cantidad de interacciones a generar:</label>
                    <input type="number" id="manual-reaction-count" min="1" max="100" value="10">
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('#generate-random-interactions').on('click', function() {
                var $btn = $(this);
                var photoId = $btn.data('photo-id');
                var count = $('#manual-reaction-count').val();
                
                $btn.prop('disabled', true).text('Generando...');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'generate_random_interactions',
                        nonce: '<?php echo wp_create_nonce("soloylibre_interactions_nonce"); ?>',
                        photo_id: photoId,
                        count: count
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Se generaron ' + response.data.count + ' interacciones aleatorias.');
                            location.reload();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    },
                    complete: function() {
                        $btn.prop('disabled', false).text('🎲 Generar Interacciones Aleatorias');
                    }
                });
            });
            
            $('#clear-all-interactions').on('click', function() {
                if (confirm('¿Estás seguro de que quieres eliminar todas las interacciones de esta foto?')) {
                    var $btn = $(this);
                    var photoId = $btn.data('photo-id');
                    
                    $btn.prop('disabled', true).text('Limpiando...');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'clear_photo_interactions',
                            nonce: '<?php echo wp_create_nonce("soloylibre_interactions_nonce"); ?>',
                            photo_id: photoId
                        },
                        success: function(response) {
                            if (response.success) {
                                alert('Todas las interacciones han sido eliminadas.');
                                location.reload();
                            } else {
                                alert('Error: ' + response.data);
                            }
                        },
                        complete: function() {
                            $btn.prop('disabled', false).text('🗑️ Limpiar Todas las Interacciones');
                        }
                    });
                }
            });
            
            $('#refresh-interactions').on('click', function() {
                location.reload();
            });
        });
        </script>
        
        <style>
        .soloylibre-interactions-meta {
            padding: 15px 0;
        }
        
        .interactions-summary {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .interactions-summary h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .total-count {
            font-size: 32px;
            font-weight: bold;
            color: #e74c3c;
            display: block;
        }
        
        .total-label {
            font-size: 14px;
            color: #666;
        }
        
        .reactions-breakdown h4,
        .interactions-actions h4 {
            margin: 0 0 15px 0;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        
        .reactions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .reaction-item {
            background: white;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .reaction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .reaction-emoji {
            font-size: 18px;
        }
        
        .reaction-label {
            flex: 1;
            margin-left: 8px;
            font-weight: 500;
        }
        
        .reaction-count {
            font-weight: bold;
            color: #333;
        }
        
        .reaction-bar {
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 5px;
        }
        
        .reaction-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .reaction-percentage {
            text-align: right;
            font-size: 11px;
            color: #666;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .action-buttons .button {
            font-size: 12px;
        }
        
        .manual-interaction {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #0073aa;
        }
        
        .manual-interaction label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .manual-interaction input {
            width: 100px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        </style>
        <?php
    }
    
    /**
     * Add interactions column
     */
    public function add_interactions_column($columns) {
        $columns['interactions'] = __('💝 Interacciones', 'soloylibre-gallery');
        return $columns;
    }
    
    /**
     * Display interactions column
     */
    public function display_interactions_column($column, $post_id) {
        if ($column === 'interactions') {
            $total_reactions = get_post_meta($post_id, '_soloylibre_total_reactions', true) ?: 0;
            $most_popular = get_post_meta($post_id, '_soloylibre_most_popular_reaction', true);
            
            echo '<div class="interactions-summary">';
            echo '<div class="total-reactions">' . number_format($total_reactions) . ' reacciones</div>';
            
            if ($most_popular && isset($this->reactions[$most_popular])) {
                echo '<div class="popular-reaction">';
                echo $this->reactions[$most_popular]['emoji'] . ' ' . $this->reactions[$most_popular]['label'];
                echo '</div>';
            }
            echo '</div>';
        }
    }
    
    /**
     * AJAX handlers
     */
    public function ajax_add_reaction() {
        check_ajax_referer('soloylibre_interactions_nonce', 'nonce');
        
        $photo_id = intval($_POST['photo_id']);
        $reaction_type = sanitize_text_field($_POST['reaction_type']);
        
        $result = $this->add_reaction($photo_id, $reaction_type);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        $reactions_data = $this->get_photo_reactions($photo_id);
        wp_send_json_success($reactions_data);
    }
    
    public function ajax_remove_reaction() {
        check_ajax_referer('soloylibre_interactions_nonce', 'nonce');
        
        $photo_id = intval($_POST['photo_id']);
        $reaction_type = sanitize_text_field($_POST['reaction_type']);
        
        $result = $this->remove_reaction($photo_id, $reaction_type);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }
        
        $reactions_data = $this->get_photo_reactions($photo_id);
        wp_send_json_success($reactions_data);
    }
    
    public function ajax_get_reactions() {
        check_ajax_referer('soloylibre_interactions_nonce', 'nonce');
        
        $photo_id = intval($_POST['photo_id']);
        $reactions_data = $this->get_photo_reactions($photo_id);
        
        wp_send_json_success($reactions_data);
    }
    
    public function ajax_generate_random_interactions() {
        check_ajax_referer('soloylibre_interactions_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(__('No tienes permisos para esta acción.', 'soloylibre-gallery'));
        }
        
        $photo_id = isset($_POST['photo_id']) ? intval($_POST['photo_id']) : null;
        $count = isset($_POST['count']) ? intval($_POST['count']) : null;
        
        $generated = $this->generate_random_interactions($photo_id, $count);
        
        wp_send_json_success(array(
            'count' => $generated,
            'message' => sprintf(__('Se generaron %d interacciones aleatorias.', 'soloylibre-gallery'), $generated)
        ));
    }
}
