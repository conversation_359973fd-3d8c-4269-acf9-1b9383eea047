<?php
/**
 * Simple One-Page Wizard for SoloYLibre Gallery Pro
 * Simplified wizard interface in a single page
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Simple_Wizard {
    
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_wizard_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_wizard_scripts'));
        add_action('wp_ajax_simple_wizard_create_gallery', array($this, 'ajax_create_gallery'));
        add_action('wp_ajax_simple_wizard_debug', array($this, 'ajax_debug_info'));
    }
    
    /**
     * Add wizard menu
     */
    public function add_wizard_menu() {
        add_menu_page(
            'SoloYLibre Wizard',
            '📸 SoloYLibre Wizard',
            'edit_posts',
            'soloylibre-simple-wizard',
            array($this, 'render_wizard'),
            'dashicons-camera',
            25
        );
    }
    
    /**
     * Enqueue wizard scripts
     */
    public function enqueue_wizard_scripts($hook) {
        if ($hook !== 'toplevel_page_soloylibre-simple-wizard') {
            return;
        }
        
        wp_enqueue_media();
        
        wp_enqueue_style(
            'soloylibre-simple-wizard-style',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/css/simple-wizard.css',
            array(),
            SOLOYLIBRE_GALLERY_VERSION
        );
        
        wp_enqueue_script(
            'soloylibre-simple-wizard-script',
            SOLOYLIBRE_GALLERY_PLUGIN_URL . 'assets/js/simple-wizard.js',
            array('jquery', 'media-upload', 'wp-util'),
            SOLOYLIBRE_GALLERY_VERSION,
            true
        );
        
        wp_localize_script('soloylibre-simple-wizard-script', 'soloylibre_simple_wizard', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('soloylibre_simple_wizard_nonce'),
            'photographer' => array(
                'name' => 'Jose L Encarnacion',
                'alias' => 'JoseTusabe',
                'brand' => 'SoloYLibre Photography',
                'location' => 'San José de Ocoa, Dom. Rep. / USA',
                'phone' => '************',
                'email' => '<EMAIL>'
            ),
            'texts' => array(
                'select_images' => __('Seleccionar Imágenes', 'soloylibre-gallery'),
                'creating_gallery' => __('Creando galería...', 'soloylibre-gallery'),
                'gallery_created' => __('¡Galería creada exitosamente!', 'soloylibre-gallery'),
                'error' => __('Error al crear la galería', 'soloylibre-gallery')
            )
        ));
    }
    
    /**
     * Render simple wizard interface
     */
    public function render_wizard() {
        ?>
        <div class="wrap soloylibre-simple-wizard">
            <!-- Header -->
            <div class="wizard-header">
                <div class="header-content">
                    <h1>📸 SoloYLibre Gallery Wizard</h1>
                    <p class="header-subtitle">
                        Crea galerías profesionales fácilmente - <strong>Jose L Encarnacion (JoseTusabe)</strong>
                        <span class="dominican-flag">🇩🇴</span>
                    </p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" id="debug-btn">
                        🐛 Debug Info
                    </button>
                    <a href="<?php echo admin_url('admin.php?page=soloylibre-statistics'); ?>" class="btn btn-primary">
                        📊 Ver Estadísticas
                    </a>
                </div>
            </div>

            <!-- Main Wizard Form -->
            <div class="wizard-container">
                <form id="simple-wizard-form" class="wizard-form">
                    <?php wp_nonce_field('soloylibre_simple_wizard_nonce', 'wizard_nonce'); ?>
                    
                    <!-- Gallery Info Section -->
                    <div class="form-section">
                        <h2>📝 Información de la Galería</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="gallery_title">Título de la Galería *</label>
                                <input type="text" id="gallery_title" name="gallery_title" 
                                       placeholder="Ej: Paisajes Dominicanos 2025" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="gallery_description">Descripción</label>
                                <textarea id="gallery_description" name="gallery_description" rows="3"
                                          placeholder="Describe tu galería..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="gallery_style">Estilo de Galería</label>
                                <select id="gallery_style" name="gallery_style">
                                    <option value="dominican">🇩🇴 Estilo Dominicano</option>
                                    <option value="grid">📱 Grid Moderno</option>
                                    <option value="masonry">🧱 Masonry</option>
                                    <option value="carousel">🎠 Carousel</option>
                                    <option value="professional">💼 Profesional</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="gallery_category">Categoría</label>
                                <select id="gallery_category" name="gallery_category">
                                    <option value="">Seleccionar categoría...</option>
                                    <?php
                                    $categories = get_categories();
                                    foreach ($categories as $category) {
                                        echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Photo Selection Section -->
                    <div class="form-section">
                        <h2>📸 Seleccionar Fotos</h2>
                        <div class="photo-selection-area">
                            <button type="button" id="select-photos-btn" class="select-photos-button">
                                <div class="upload-icon">📷</div>
                                <h3>Seleccionar Fotos</h3>
                                <p>Haz clic para seleccionar fotos de tu biblioteca de medios</p>
                            </button>
                            
                            <div id="selected-photos-preview" class="selected-photos-grid" style="display: none;">
                                <h4>Fotos Seleccionadas (<span id="photo-count">0</span>)</h4>
                                <div id="photos-container" class="photos-container"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Section -->
                    <div class="form-section">
                        <h2>⚙️ Configuración</h2>
                        <div class="settings-grid">
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="include_signature" name="include_signature" checked>
                                    <span class="checkmark"></span>
                                    Incluir firma del fotógrafo
                                </label>
                            </div>
                            
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="enable_lightbox" name="enable_lightbox" checked>
                                    <span class="checkmark"></span>
                                    Habilitar lightbox
                                </label>
                            </div>
                            
                            <div class="setting-group">
                                <label>
                                    <input type="checkbox" id="show_metadata" name="show_metadata">
                                    <span class="checkmark"></span>
                                    Mostrar información de fotos
                                </label>
                            </div>
                            
                            <div class="setting-group">
                                <label for="post_status">Estado del Post:</label>
                                <select id="post_status" name="post_status">
                                    <option value="draft">Borrador</option>
                                    <option value="publish">Publicar inmediatamente</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Photographer Signature Preview -->
                    <div class="form-section signature-preview">
                        <h2>📸 Vista Previa de Firma</h2>
                        <div class="signature-card">
                            <div class="signature-content">
                                <h3>Jose L Encarnacion (JoseTusabe)</h3>
                                <p><strong>SoloYLibre Photography</strong></p>
                                <p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
                                <p>📞 ************ | 📧 <EMAIL></p>
                                <div class="websites">
                                    <a href="https://josetusabe.com" target="_blank">🌐 josetusabe.com</a>
                                    <a href="https://soloylibre.com" target="_blank">🌐 soloylibre.com</a>
                                    <a href="https://1and1photo.com" target="_blank">📸 1and1photo.com</a>
                                    <a href="https://joselencarnacion.com" target="_blank">👨‍💼 joselencarnacion.com</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Section -->
                    <div class="form-section submit-section">
                        <button type="submit" id="create-gallery-btn" class="create-gallery-button">
                            <span class="btn-icon">🚀</span>
                            <span class="btn-text">Crear Galería</span>
                        </button>
                        
                        <div id="creation-status" class="creation-status" style="display: none;">
                            <div class="status-icon">⏳</div>
                            <div class="status-text">Creando tu galería profesional...</div>
                        </div>
                        
                        <div id="creation-success" class="creation-success" style="display: none;">
                            <div class="success-icon">✅</div>
                            <div class="success-content">
                                <h3>¡Galería Creada Exitosamente!</h3>
                                <p>Tu galería profesional de SoloYLibre está lista</p>
                                <div class="success-actions">
                                    <a href="#" id="view-gallery-link" class="btn btn-primary" target="_blank">
                                        👁️ Ver Galería
                                    </a>
                                    <a href="#" id="edit-post-link" class="btn btn-secondary" target="_blank">
                                        ✏️ Editar Post
                                    </a>
                                    <button type="button" id="create-another-btn" class="btn btn-outline">
                                        📸 Crear Otra Galería
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Debug Panel (Hidden by default) -->
            <div id="debug-panel" class="debug-panel" style="display: none;">
                <div class="debug-header">
                    <h3>🐛 Debug Information</h3>
                    <button type="button" id="close-debug" class="close-debug">×</button>
                </div>
                <div class="debug-content">
                    <div class="debug-loading">
                        <div class="spinner"></div>
                        <p>Obteniendo información de debug...</p>
                    </div>
                    <div id="debug-info" class="debug-info"></div>
                </div>
            </div>

            <!-- Footer -->
            <div class="wizard-footer">
                <div class="footer-content">
                    <div class="photographer-info">
                        <h4>📸 SoloYLibre Photography</h4>
                        <p>Jose L Encarnacion (JoseTusabe) - Fotógrafo Profesional Dominicano</p>
                        <p>🇩🇴 Capturando la belleza de República Dominicana y el mundo</p>
                    </div>
                    <div class="footer-links">
                        <a href="<?php echo admin_url('edit.php?post_type=soloylibre_photo'); ?>">📸 Gestionar Fotos</a>
                        <a href="<?php echo admin_url('admin.php?page=soloylibre-statistics'); ?>">📊 Ver Estadísticas</a>
                        <a href="<?php echo home_url(); ?>" target="_blank">🌐 Ver Sitio</a>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * AJAX: Create gallery from simple wizard
     */
    public function ajax_create_gallery() {
        check_ajax_referer('soloylibre_simple_wizard_nonce', 'nonce');
        
        $gallery_title = sanitize_text_field($_POST['gallery_title'] ?? '');
        $gallery_description = wp_kses_post($_POST['gallery_description'] ?? '');
        $gallery_style = sanitize_text_field($_POST['gallery_style'] ?? 'dominican');
        $gallery_category = intval($_POST['gallery_category'] ?? 0);
        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());
        $include_signature = !empty($_POST['include_signature']);
        $enable_lightbox = !empty($_POST['enable_lightbox']);
        $show_metadata = !empty($_POST['show_metadata']);
        $post_status = sanitize_text_field($_POST['post_status'] ?? 'draft');
        
        if (empty($gallery_title) || empty($photo_ids)) {
            wp_send_json_error('Título y fotos son requeridos');
        }
        
        // Create gallery shortcode
        $shortcode_atts = array(
            'style="' . $gallery_style . '"',
            'ids="' . implode(',', $photo_ids) . '"'
        );
        
        if ($enable_lightbox) {
            $shortcode_atts[] = 'lightbox="true"';
        }
        
        if ($show_metadata) {
            $shortcode_atts[] = 'show_metadata="true"';
        }
        
        $gallery_shortcode = '[soloylibre_gallery ' . implode(' ', $shortcode_atts) . ']';
        
        // Build post content
        $post_content = $gallery_description;
        if (!empty($post_content)) {
            $post_content .= "\n\n";
        }
        $post_content .= $gallery_shortcode;
        
        // Add photographer signature if requested
        if ($include_signature) {
            $post_content .= '

<div style="background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 30px; border-radius: 15px; margin-top: 40px; text-align: center;">
    <h3 style="color: white; margin-bottom: 15px;">📸 Fotografía por Jose L Encarnacion (JoseTusabe)</h3>
    <p style="margin: 10px 0;"><strong>SoloYLibre Photography</strong></p>
    <p style="margin: 10px 0;">📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
    <p style="margin: 10px 0;">📞 ************ | 📧 <EMAIL></p>
    <div style="margin-top: 20px;">
        <a href="https://josetusabe.com" target="_blank" style="color: white; margin: 0 10px; text-decoration: none;">🌐 josetusabe.com</a>
        <a href="https://soloylibre.com" target="_blank" style="color: white; margin: 0 10px; text-decoration: none;">🌐 soloylibre.com</a>
        <a href="https://1and1photo.com" target="_blank" style="color: white; margin: 0 10px; text-decoration: none;">📸 1and1photo.com</a>
        <a href="https://joselencarnacion.com" target="_blank" style="color: white; margin: 0 10px; text-decoration: none;">👨‍💼 joselencarnacion.com</a>
    </div>
</div>';
        }
        
        // Create post
        $post_data = array(
            'post_title' => $gallery_title,
            'post_content' => $post_content,
            'post_status' => $post_status,
            'post_type' => 'post',
            'post_author' => get_current_user_id()
        );
        
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            wp_send_json_error('Error al crear el post: ' . $post_id->get_error_message());
        }
        
        // Set category
        if ($gallery_category > 0) {
            wp_set_post_categories($post_id, array($gallery_category));
        }
        
        // Set featured image (first photo)
        if (!empty($photo_ids)) {
            set_post_thumbnail($post_id, $photo_ids[0]);
        }
        
        // Save gallery metadata
        update_post_meta($post_id, '_soloylibre_gallery_photos', $photo_ids);
        update_post_meta($post_id, '_soloylibre_gallery_style', $gallery_style);
        update_post_meta($post_id, '_soloylibre_created_by_simple_wizard', true);
        update_post_meta($post_id, '_soloylibre_creation_date', current_time('mysql'));
        
        // Add tags
        $auto_tags = array('soloylibre', 'josetusabe', 'fotografia', 'dominicana', $gallery_style);
        wp_set_post_tags($post_id, implode(', ', $auto_tags));
        
        wp_send_json_success(array(
            'post_id' => $post_id,
            'post_url' => get_permalink($post_id),
            'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit'),
            'photo_count' => count($photo_ids),
            'gallery_title' => $gallery_title
        ));
    }
    
    /**
     * AJAX: Get debug information
     */
    public function ajax_debug_info() {
        check_ajax_referer('soloylibre_simple_wizard_nonce', 'nonce');
        
        $debug_info = $this->collect_debug_info();
        
        wp_send_json_success($debug_info);
    }
    
    /**
     * Collect comprehensive debug information
     */
    private function collect_debug_info() {
        global $wpdb;
        
        $debug = array();
        
        // WordPress Info
        $debug['wordpress'] = array(
            'version' => get_bloginfo('version'),
            'site_url' => site_url(),
            'home_url' => home_url(),
            'admin_url' => admin_url(),
            'wp_debug' => defined('WP_DEBUG') ? WP_DEBUG : false,
            'wp_debug_log' => defined('WP_DEBUG_LOG') ? WP_DEBUG_LOG : false
        );
        
        // Plugin Info
        $plugin_file = 'Archive/soloylibre-gallery-plugin.php';
        $debug['plugin'] = array(
            'active' => is_plugin_active($plugin_file),
            'file_exists' => file_exists(WP_PLUGIN_DIR . '/' . $plugin_file),
            'version' => defined('SOLOYLIBRE_GALLERY_VERSION') ? SOLOYLIBRE_GALLERY_VERSION : 'Unknown',
            'url' => defined('SOLOYLIBRE_GALLERY_PLUGIN_URL') ? SOLOYLIBRE_GALLERY_PLUGIN_URL : 'Unknown'
        );
        
        // Classes Status
        $classes = array(
            'SoloYLibre_Gallery_Plugin',
            'SoloYLibre_Simple_Wizard',
            'SoloYLibre_Initial_Setup',
            'SoloYLibre_Statistics_Dashboard',
            'SoloYLibre_Gallery_Styles'
        );
        
        $debug['classes'] = array();
        foreach ($classes as $class) {
            $debug['classes'][$class] = class_exists($class);
        }
        
        // Database Tables
        $tables = array(
            $wpdb->prefix . 'soloylibre_albums',
            $wpdb->prefix . 'soloylibre_photo_meta',
            $wpdb->prefix . 'soloylibre_user_interactions',
            $wpdb->prefix . 'soloylibre_projects',
            $wpdb->prefix . 'soloylibre_photographer_settings'
        );
        
        $debug['database'] = array();
        foreach ($tables as $table) {
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
            $count = $exists ? $wpdb->get_var("SELECT COUNT(*) FROM $table") : 0;
            $debug['database'][$table] = array(
                'exists' => $exists,
                'count' => intval($count)
            );
        }
        
        // Post Types
        $debug['post_types'] = array();
        $post_types = get_post_types(array('_builtin' => false), 'objects');
        foreach ($post_types as $post_type) {
            if (strpos($post_type->name, 'soloylibre') !== false) {
                $count = wp_count_posts($post_type->name);
                $debug['post_types'][$post_type->name] = array(
                    'label' => $post_type->label,
                    'published' => $count->publish ?? 0,
                    'draft' => $count->draft ?? 0
                );
            }
        }
        
        // Options
        $options = array(
            'soloylibre_gallery_photographer_name',
            'soloylibre_gallery_photographer_brand',
            'soloylibre_gallery_gallery_style',
            'soloylibre_gallery_setup_complete'
        );
        
        $debug['options'] = array();
        foreach ($options as $option) {
            $debug['options'][$option] = get_option($option, 'Not set');
        }
        
        // PHP Info
        $debug['php'] = array(
            'version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size')
        );
        
        // Recent Errors
        $debug['errors'] = $this->get_recent_errors();
        
        // File Permissions
        $debug['permissions'] = array(
            'wp_content' => $this->check_file_permissions(WP_CONTENT_DIR),
            'plugins' => $this->check_file_permissions(WP_PLUGIN_DIR),
            'uploads' => $this->check_file_permissions(wp_upload_dir()['basedir'])
        );
        
        return $debug;
    }
    
    /**
     * Get recent errors from logs
     */
    private function get_recent_errors() {
        $errors = array();
        
        $log_files = array(
            WP_CONTENT_DIR . '/debug.log',
            ini_get('error_log')
        );
        
        foreach ($log_files as $log_file) {
            if ($log_file && file_exists($log_file)) {
                $content = file_get_contents($log_file);
                $lines = explode("\n", $content);
                $recent_lines = array_slice($lines, -50); // Last 50 lines
                
                foreach ($recent_lines as $line) {
                    if (!empty($line) && (
                        strpos($line, 'soloylibre') !== false ||
                        strpos($line, 'SoloYLibre') !== false ||
                        strpos($line, 'Fatal error') !== false ||
                        strpos($line, 'Warning') !== false
                    )) {
                        $errors[] = $line;
                    }
                }
            }
        }
        
        return array_slice($errors, -10); // Return last 10 errors
    }
    
    /**
     * Check file permissions
     */
    private function check_file_permissions($path) {
        if (!file_exists($path)) {
            return 'Path does not exist';
        }
        
        $perms = fileperms($path);
        return substr(sprintf('%o', $perms), -4);
    }
}
