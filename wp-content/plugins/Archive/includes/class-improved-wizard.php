<?php
/**
 * Simple Gallery Wizard - Auto-load Photos with Duplicate Prevention
 * Wizard simple de gal<PERSON>ías - Auto-carga fotos con prevención de duplicados
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

class SoloYLibre_Improved_Wizard {
    
    public function __construct() {
        add_action("admin_menu", array($this, "add_admin_menu"));
        add_action("admin_enqueue_scripts", array($this, "enqueue_scripts"));
        add_action("wp_ajax_load_photos_simple", array($this, "ajax_load_photos"));
        add_action("wp_ajax_create_gallery_quick", array($this, "ajax_create_gallery"));
        add_action("wp_ajax_get_published_status", array($this, "ajax_get_published_status"));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            "SoloYLibre Wizard",
            "📸 SoloYLibre",
            "edit_posts",
            "soloylibre-wizard",
            array($this, "render_wizard"),
            "dashicons-camera",
            25
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== "toplevel_page_soloylibre-wizard") return;
        
        wp_enqueue_script("jquery");
        wp_add_inline_script("jquery", $this->get_wizard_js());
        wp_add_inline_style("wp-admin", $this->get_wizard_css());
    }
    
    public function render_wizard() {
        ?>
        <div class="soloylibre-simple-wizard">
            <div class="wizard-header">
                <h1>📸 Crear Galería SoloYLibre - Simple y Rápido</h1>
                <p>🇩🇴 Jose L Encarnacion (JoseTusabe) - SoloYLibre Photography</p>
            </div>
            
            <div class="wizard-content">
                <!-- Estado de fotos -->
                <div class="photo-status">
                    <div class="status-card">
                        <h3>📊 Estado de Fotos</h3>
                        <div class="status-grid">
                            <div class="status-item">
                                <span class="status-number" id="total-photos">...</span>
                                <span class="status-label">📷 Total Disponibles</span>
                            </div>
                            <div class="status-item">
                                <span class="status-number" id="published-photos">...</span>
                                <span class="status-label">✅ Ya Publicadas</span>
                            </div>
                            <div class="status-item">
                                <span class="status-number" id="new-photos">...</span>
                                <span class="status-label">🆕 Nuevas</span>
                            </div>
                        </div>
                        <button id="refresh-status" class="btn btn-secondary">🔄 Actualizar</button>
                    </div>
                </div>
                
                <!-- Configuración simple -->
                <div class="gallery-config">
                    <div class="config-card">
                        <h3>⚙️ Configuración Rápida</h3>
                        <div class="config-row">
                            <div class="config-item">
                                <label for="gallery-title">📝 Título de la Galería</label>
                                <input type="text" id="gallery-title" placeholder="Ej: Fotos Nuevas - <?php echo date('M Y'); ?>" value="Galería SoloYLibre - <?php echo date('M Y'); ?>">
                            </div>
                            <div class="config-item">
                                <label for="gallery-style">🎨 Estilo</label>
                                <select id="gallery-style">
                                    <option value="masonry">🧱 Masonry (Recomendado)</option>
                                    <option value="dominican">🇩🇴 Dominicano</option>
                                    <option value="grid">📱 Grid</option>
                                    <option value="professional">💼 Profesional</option>
                                </select>
                            </div>
                            <div class="config-item">
                                <label for="photos-limit">📷 Cantidad de Fotos</label>
                                <select id="photos-limit">
                                    <option value="20">20 fotos</option>
                                    <option value="50" selected>50 fotos</option>
                                    <option value="100">100 fotos</option>
                                    <option value="all">Todas las nuevas</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Vista previa de fotos -->
                <div class="photo-preview">
                    <div class="preview-card">
                        <h3>🖼️ Vista Previa de Fotos Nuevas</h3>
                        <div id="loading-photos" class="loading">
                            <span>🔄 Cargando fotos nuevas...</span>
                        </div>
                        <div id="photos-grid" class="photos-grid"></div>
                        <div id="no-photos" class="no-photos" style="display: none;">
                            <p>✅ ¡Todas las fotos ya están publicadas!</p>
                            <p>Sube nuevas fotos a la biblioteca de medios para crear más galerías.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Botones de acción -->
                <div class="action-buttons">
                    <button id="create-gallery" class="btn btn-primary" disabled>
                        🚀 Crear y Publicar Galería
                    </button>
                    <button id="preview-gallery" class="btn btn-secondary" disabled>
                        👁️ Vista Previa
                    </button>
                </div>
                
                <!-- Resultado -->
                <div id="result-message" class="result-message" style="display: none;"></div>
            </div>
        </div>
        <?php
    }
    
    public function ajax_load_photos() {
        // Obtener todas las fotos disponibles
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        // Obtener fotos ya publicadas
        $published_photos = get_option('soloylibre_published_photos', array());
        
        // Filtrar fotos nuevas (no publicadas)
        $new_photos = array();
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $published_photos)) {
                $image_url = wp_get_attachment_image_src($photo->ID, 'thumbnail');
                if ($image_url) {
                    $new_photos[] = array(
                        'id' => $photo->ID,
                        'title' => get_the_title($photo->ID),
                        'url' => $image_url[0],
                        'date' => $photo->post_date
                    );
                }
            }
        }
        
        wp_send_json_success(array(
            'total_photos' => count($all_photos),
            'published_photos' => count($published_photos),
            'new_photos' => $new_photos,
            'new_count' => count($new_photos)
        ));
    }
    
    public function ajax_get_published_status() {
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit'
        ));
        
        $published_photos = get_option('soloylibre_published_photos', array());
        
        wp_send_json_success(array(
            'total' => count($all_photos),
            'published' => count($published_photos),
            'new' => count($all_photos) - count($published_photos)
        ));
    }
    
    public function ajax_create_gallery() {
        $title = sanitize_text_field($_POST['title']);
        $style = sanitize_text_field($_POST['style']);
        $limit = sanitize_text_field($_POST['limit']);
        
        // Obtener fotos nuevas
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        $published_photos = get_option('soloylibre_published_photos', array());
        $new_photos = array();
        
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $published_photos)) {
                $new_photos[] = $photo->ID;
            }
        }
        
        if (empty($new_photos)) {
            wp_send_json_error('No hay fotos nuevas para publicar');
            return;
        }
        
        // Limitar fotos según selección
        if ($limit !== 'all' && is_numeric($limit)) {
            $new_photos = array_slice($new_photos, 0, intval($limit));
        }
        
        // Crear el post de galería
        $post_content = "[soloylibre_gallery style=\"$style\" ids=\"" . implode(',', $new_photos) . "\" columns=\"4\" enable_interactions=\"true\" aspect_ratio=\"natural\"]";
        
        $post_id = wp_insert_post(array(
            'post_title' => $title,
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => get_current_user_id()
        ));
        
        if ($post_id) {
            // Marcar fotos como publicadas
            $published_photos = array_merge($published_photos, $new_photos);
            update_option('soloylibre_published_photos', $published_photos);
            
            wp_send_json_success(array(
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'photos_published' => count($new_photos),
                'message' => "¡Galería '$title' creada exitosamente con " . count($new_photos) . " fotos!"
            ));
        } else {
            wp_send_json_error('Error al crear la galería');
        }
    }

    private function get_wizard_css() {
        return "
        .soloylibre-simple-wizard {
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .wizard-header {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .wizard-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
        }

        .wizard-content {
            padding: 30px;
        }

        .photo-status, .gallery-config, .photo-preview {
            margin-bottom: 30px;
        }

        .status-card, .config-card, .preview-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .status-number {
            display: block;
            font-size: 2rem;
            font-weight: bold;
            color: #CE1126;
        }

        .status-label {
            display: block;
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .config-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .config-item label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #495057;
        }

        .config-item input, .config-item select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }

        .photos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }

        .photo-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e9ecef;
        }

        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #CE1126;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #a00e1f;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #545b62;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1rem;
        }

        .no-photos {
            text-align: center;
            padding: 40px;
            color: #28a745;
        }

        .result-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
        }

        .result-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        ";
    }

    private function get_wizard_js() {
        return "
        jQuery(document).ready(function($) {
            // Cargar estado inicial
            loadPhotosAndStatus();

            // Event listeners
            $('#refresh-status').click(loadPhotosAndStatus);
            $('#create-gallery').click(createGallery);
            $('#preview-gallery').click(previewGallery);

            function loadPhotosAndStatus() {
                $('#loading-photos').show();
                $('#photos-grid').empty();
                $('#no-photos').hide();

                $.post(ajaxurl, {
                    action: 'load_photos_simple'
                }, function(response) {
                    $('#loading-photos').hide();

                    if (response.success) {
                        updateStatus(response.data);
                        displayPhotos(response.data.new_photos);

                        if (response.data.new_count > 0) {
                            $('#create-gallery, #preview-gallery').prop('disabled', false);
                        } else {
                            $('#no-photos').show();
                            $('#create-gallery, #preview-gallery').prop('disabled', true);
                        }
                    }
                });
            }

            function updateStatus(data) {
                $('#total-photos').text(data.total_photos);
                $('#published-photos').text(data.published_photos);
                $('#new-photos').text(data.new_count);
            }

            function displayPhotos(photos) {
                const grid = $('#photos-grid');
                grid.empty();

                photos.slice(0, 20).forEach(function(photo) {
                    const item = $('<div class=\"photo-item\"><img src=\"' + photo.url + '\" alt=\"' + photo.title + '\" title=\"' + photo.title + '\"></div>');
                    grid.append(item);
                });

                if (photos.length > 20) {
                    grid.append('<div class=\"photo-item\" style=\"display: flex; align-items: center; justify-content: center; background: #f8f9fa; color: #6c757d; font-weight: bold;\">+' + (photos.length - 20) + ' más</div>');
                }
            }

            function createGallery() {
                const title = $('#gallery-title').val();
                const style = $('#gallery-style').val();
                const limit = $('#photos-limit').val();

                if (!title.trim()) {
                    alert('Por favor ingresa un título para la galería');
                    return;
                }

                $('#create-gallery').prop('disabled', true).text('🔄 Creando...');

                $.post(ajaxurl, {
                    action: 'create_gallery_quick',
                    title: title,
                    style: style,
                    limit: limit
                }, function(response) {
                    $('#create-gallery').prop('disabled', false).text('🚀 Crear y Publicar Galería');

                    const resultDiv = $('#result-message');

                    if (response.success) {
                        resultDiv.removeClass('error').addClass('success');
                        resultDiv.html('✅ ' + response.data.message + '<br><a href=\"' + response.data.post_url + '\" target=\"_blank\">👁️ Ver Galería Publicada</a>');
                        resultDiv.show();

                        // Recargar estado
                        setTimeout(loadPhotosAndStatus, 2000);
                    } else {
                        resultDiv.removeClass('success').addClass('error');
                        resultDiv.text('❌ ' + response.data);
                        resultDiv.show();
                    }
                });
            }

            function previewGallery() {
                const style = $('#gallery-style').val();
                const limit = $('#photos-limit').val();

                alert('Vista previa: Se creará una galería estilo \"' + style + '\" con ' + (limit === 'all' ? 'todas las fotos nuevas' : limit + ' fotos'));
            }
        });
        ";
    }
}

// Inicializar el wizard
new SoloYLibre_Improved_Wizard();
?>
