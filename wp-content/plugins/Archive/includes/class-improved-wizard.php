<?php
/**
 * Simple Gallery Wizard - Auto-load Photos with Duplicate Prevention
 * Wizard simple de gal<PERSON>ías - Auto-carga fotos con prevención de duplicados
 * Developed by JEYKO AI for Jose L Encarnac<PERSON> (JoseTusabe)
 */

class SoloYLibre_Improved_Wizard {
    
    public function __construct() {
        add_action("admin_menu", array($this, "add_admin_menu"));
        add_action("admin_enqueue_scripts", array($this, "enqueue_scripts"));
        add_action("wp_ajax_load_photos_simple", array($this, "ajax_load_photos"));
        add_action("wp_ajax_create_gallery_quick", array($this, "ajax_create_gallery"));
        add_action("wp_ajax_get_published_status", array($this, "ajax_get_published_status"));
        add_action("wp_ajax_auto_select_photos", array($this, "ajax_auto_select_photos"));
        add_action("wp_ajax_save_private_photo", array($this, "ajax_save_private_photo"));
        add_action("wp_ajax_get_private_photos", array($this, "ajax_get_private_photos"));
        add_action("wp_ajax_mark_photo_unused", array($this, "ajax_mark_photo_unused"));
        add_action("wp_ajax_mark_photo_unwanted", array($this, "ajax_mark_photo_unwanted"));
        add_action("wp_ajax_get_photo_categories", array($this, "ajax_get_photo_categories"));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            "SoloYLibre Wizard",
            "📸 SoloYLibre",
            "edit_posts",
            "soloylibre-wizard",
            array($this, "render_wizard"),
            "dashicons-camera",
            25
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== "toplevel_page_soloylibre-wizard") return;
        
        wp_enqueue_script("jquery");
        wp_add_inline_script("jquery", $this->get_wizard_js());
        wp_add_inline_style("wp-admin", $this->get_wizard_css());
    }
    
    public function render_wizard() {
        // Obtener información del fotógrafo
        $photographer_info = get_option('soloylibre_photographer_info', array(
            'full_name' => 'Jose L Encarnacion',
            'nickname' => 'JoseTusabe',
            'business_name' => 'SoloYLibre Photography'
        ));
        ?>
        <div class="soloylibre-simple-wizard">
            <div class="wizard-header">
                <h1>📸 Crear Galería SoloYLibre - Simple y Rápido</h1>
                <p>🇩🇴 <?php echo esc_html($photographer_info['full_name']); ?> (<?php echo esc_html($photographer_info['nickname']); ?>) - <?php echo esc_html($photographer_info['business_name']); ?></p>
            </div>
            
            <div class="wizard-content">
                <!-- Estado de fotos -->
                <div class="photo-status">
                    <div class="status-card">
                        <h3>📊 Estado de Fotos</h3>
                        <div class="status-grid">
                            <div class="status-item">
                                <span class="status-number" id="total-photos">...</span>
                                <span class="status-label">📷 Total Disponibles</span>
                            </div>
                            <div class="status-item">
                                <span class="status-number" id="published-photos">...</span>
                                <span class="status-label">✅ Ya Publicadas</span>
                            </div>
                            <div class="status-item">
                                <span class="status-number" id="new-photos">...</span>
                                <span class="status-label">🆕 Nuevas</span>
                            </div>
                        </div>
                        <button id="refresh-status" class="btn btn-secondary">🔄 Actualizar</button>
                    </div>
                </div>
                
                <!-- Configuración simple -->
                <div class="gallery-config">
                    <div class="config-card">
                        <h3>⚙️ Configuración Rápida</h3>
                        <div class="config-row">
                            <div class="config-item">
                                <label for="gallery-title">📝 Título de la Galería</label>
                                <input type="text" id="gallery-title" placeholder="Ej: Fotos Nuevas - <?php echo date('M Y'); ?>" value="Galería SoloYLibre - <?php echo date('M Y'); ?>">
                            </div>
                            <div class="config-item">
                                <label for="gallery-style">🎨 Estilo</label>
                                <select id="gallery-style">
                                    <option value="masonry">🧱 Masonry (Recomendado)</option>
                                    <option value="dominican">🇩🇴 Dominicano</option>
                                    <option value="grid">📱 Grid</option>
                                    <option value="professional">💼 Profesional</option>
                                </select>
                            </div>
                            <div class="config-item">
                                <label for="photos-limit">📷 Cantidad de Fotos</label>
                                <select id="photos-limit">
                                    <option value="20">20 fotos</option>
                                    <option value="50" selected>50 fotos</option>
                                    <option value="100">100 fotos</option>
                                    <option value="all">Todas las nuevas</option>
                                </select>
                            </div>
                        </div>

                        <!-- Modo de selección -->
                        <div class="selection-mode">
                            <h4>🎯 Modo de Selección</h4>
                            <div class="mode-buttons">
                                <button type="button" id="auto-mode" class="mode-btn active">🎲 Auto-Selección</button>
                                <button type="button" id="manual-mode" class="mode-btn">✋ Selección Manual</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auto-selección de fotos -->
                <div id="auto-selection" class="auto-selection">
                    <div class="auto-card">
                        <h3>🎲 Auto-Selección Inteligente</h3>
                        <p>El sistema seleccionará fotos aleatoriamente. Puedes revisar y quitar las que no quieras.</p>
                        <button id="generate-selection" class="btn btn-secondary">🎯 Generar Selección</button>
                        <div id="selected-photos" class="selected-photos"></div>
                    </div>
                </div>
                
                <!-- Vista previa de fotos -->
                <div class="photo-preview">
                    <div class="preview-card">
                        <h3>🖼️ Vista Previa de Fotos Nuevas</h3>
                        <div id="loading-photos" class="loading">
                            <span>🔄 Cargando fotos nuevas...</span>
                        </div>
                        <div id="photos-grid" class="photos-grid"></div>
                        <div id="no-photos" class="no-photos" style="display: none;">
                            <p>✅ ¡Todas las fotos ya están publicadas!</p>
                            <p>Sube nuevas fotos a la biblioteca de medios para crear más galerías.</p>
                        </div>
                    </div>
                </div>

                <!-- Fotos privadas -->
                <div class="private-photos">
                    <div class="private-card">
                        <h3>👁️ Fotos Privadas - Solo Para Tus Ojos</h3>
                        <p>Guarda fotos que quieres revisar más tarde, editar o publicar en privado.</p>
                        <button id="view-private" class="btn btn-secondary">👁️ Ver Mis Fotos Privadas</button>
                        <div id="private-gallery" class="private-gallery" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- Botones de acción -->
                <div class="action-buttons">
                    <button id="create-gallery" class="btn btn-primary" disabled>
                        🚀 Crear y Publicar Galería
                    </button>
                    <button id="preview-gallery" class="btn btn-secondary" disabled>
                        👁️ Vista Previa
                    </button>
                </div>
                
                <!-- Resultado -->
                <div id="result-message" class="result-message" style="display: none;"></div>
            </div>
        </div>
        <?php
    }
    
    public function ajax_load_photos() {
        // Obtener todas las fotos disponibles
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        // Obtener fotos ya publicadas
        $published_photos = get_option('soloylibre_published_photos', array());
        
        // Filtrar fotos nuevas (no publicadas)
        $new_photos = array();
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $published_photos)) {
                $image_url = wp_get_attachment_image_src($photo->ID, 'thumbnail');
                if ($image_url) {
                    $new_photos[] = array(
                        'id' => $photo->ID,
                        'title' => get_the_title($photo->ID),
                        'url' => $image_url[0],
                        'date' => $photo->post_date
                    );
                }
            }
        }
        
        wp_send_json_success(array(
            'total_photos' => count($all_photos),
            'published_photos' => count($published_photos),
            'new_photos' => $new_photos,
            'new_count' => count($new_photos)
        ));
    }
    
    public function ajax_get_published_status() {
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit'
        ));
        
        $published_photos = get_option('soloylibre_published_photos', array());
        
        wp_send_json_success(array(
            'total' => count($all_photos),
            'published' => count($published_photos),
            'new' => count($all_photos) - count($published_photos)
        ));
    }
    
    public function ajax_create_gallery() {
        // Verificar nonce para seguridad
        if (!wp_verify_nonce($_POST['nonce'], 'soloylibre_wizard_nonce')) {
            wp_send_json_error('Error de seguridad');
            return;
        }

        $title = sanitize_text_field($_POST['title']);
        $style = sanitize_text_field($_POST['style']);
        $limit = sanitize_text_field($_POST['limit']);
        $selected_photos = isset($_POST['selected_photos']) ? array_map('intval', $_POST['selected_photos']) : array();

        // Si hay fotos seleccionadas específicamente, usar esas
        if (!empty($selected_photos)) {
            $photos_to_publish = $selected_photos;
        } else {
            // Obtener fotos nuevas (no publicadas)
            $all_photos = get_posts(array(
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'posts_per_page' => -1,
                'post_status' => 'inherit',
                'orderby' => 'date',
                'order' => 'DESC'
            ));

            $published_photos = get_option('soloylibre_published_photos', array());
            $private_photos = get_option('soloylibre_private_photos', array());
            $unused_photos = get_option('soloylibre_unused_photos', array());
            $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

            // Fotos que ya están categorizadas
            $categorized_photos = array_merge($published_photos, $private_photos, $unused_photos, $unwanted_photos);

            $new_photos = array();
            foreach ($all_photos as $photo) {
                if (!in_array($photo->ID, $categorized_photos)) {
                    $new_photos[] = $photo->ID;
                }
            }

            if (empty($new_photos)) {
                wp_send_json_error('No hay fotos nuevas para publicar. Todas las fotos ya están categorizadas.');
                return;
            }

            // Limitar fotos según selección
            if ($limit !== 'all' && is_numeric($limit)) {
                $new_photos = array_slice($new_photos, 0, intval($limit));
            }

            $photos_to_publish = $new_photos;
        }

        if (empty($photos_to_publish)) {
            wp_send_json_error('No hay fotos para publicar');
            return;
        }

        // Crear el post de galería
        $post_content = "[soloylibre_gallery style=\"$style\" ids=\"" . implode(',', $photos_to_publish) . "\" columns=\"4\" enable_interactions=\"true\" aspect_ratio=\"natural\"]";

        $post_id = wp_insert_post(array(
            'post_title' => $title,
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                '_soloylibre_gallery_photos' => $photos_to_publish,
                '_soloylibre_gallery_style' => $style,
                '_soloylibre_creation_date' => current_time('mysql')
            )
        ));

        if ($post_id) {
            // Marcar fotos como publicadas (evitar duplicados)
            $published_photos = get_option('soloylibre_published_photos', array());
            $new_published = array_unique(array_merge($published_photos, $photos_to_publish));
            update_option('soloylibre_published_photos', $new_published);

            // Actualizar estadísticas mensuales
            $monthly_key = 'soloylibre_monthly_published_' . date('Y_m');
            $monthly_published = get_option($monthly_key, array());
            $monthly_published = array_unique(array_merge($monthly_published, $photos_to_publish));
            update_option($monthly_key, $monthly_published);

            // Incrementar contador de galerías creadas
            $galleries_created = get_option('soloylibre_galleries_created', 0);
            update_option('soloylibre_galleries_created', $galleries_created + 1);

            wp_send_json_success(array(
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'edit_url' => get_edit_post_link($post_id),
                'view_url' => get_permalink($post_id),
                'photos_published' => count($photos_to_publish),
                'total_published' => count($new_published),
                'gallery_title' => $title,
                'message' => "¡Galería '$title' creada exitosamente con " . count($photos_to_publish) . " fotos!",
                'success_html' => $this->get_success_html($post_id, $title, count($photos_to_publish))
            ));
        } else {
            wp_send_json_error('Error al crear la galería');
        }
    }

    /**
     * Get success HTML for gallery creation
     */
    private function get_success_html($post_id, $title, $photo_count) {
        $post_url = get_permalink($post_id);
        $edit_url = get_edit_post_link($post_id);

        return "
        <div class='gallery-success-container'>
            <div class='success-header'>
                <h3>🎉 ¡Galería Creada Exitosamente!</h3>
                <p class='gallery-title'>\"$title\" con $photo_count fotos</p>
            </div>

            <div class='success-actions'>
                <a href='$post_url' target='_blank' class='btn btn-primary'>
                    👁️ Ver Galería
                </a>
                <a href='$edit_url' target='_blank' class='btn btn-secondary'>
                    ✏️ Editar Post
                </a>
                <button class='btn btn-success' onclick='createAnotherGallery()'>
                    ➕ Crear Otra Galería
                </button>
                <button class='btn btn-info' onclick='goToStatistics()'>
                    📊 Ver Estadísticas
                </button>
            </div>

            <div class='success-stats'>
                <div class='stat-item'>
                    <span class='stat-label'>ID del Post:</span>
                    <span class='stat-value'>#$post_id</span>
                </div>
                <div class='stat-item'>
                    <span class='stat-label'>Fotos Publicadas:</span>
                    <span class='stat-value'>$photo_count</span>
                </div>
                <div class='stat-item'>
                    <span class='stat-label'>Fecha:</span>
                    <span class='stat-value'>" . date('d/m/Y H:i') . "</span>
                </div>
            </div>

            <div class='success-links'>
                <p><strong>Enlaces directos:</strong></p>
                <div class='link-item'>
                    <label>Ver Galería:</label>
                    <input type='text' value='$post_url' readonly onclick='this.select()' class='link-input'>
                </div>
                <div class='link-item'>
                    <label>Editar Post:</label>
                    <input type='text' value='$edit_url' readonly onclick='this.select()' class='link-input'>
                </div>
            </div>
        </div>";
    }

    public function ajax_auto_select_photos() {
        $limit = intval($_POST['limit']);

        // Obtener fotos nuevas
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $new_photos = array();

        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $published_photos) && !in_array($photo->ID, $private_photos)) {
                $new_photos[] = $photo;
            }
        }

        if (empty($new_photos)) {
            wp_send_json_error('No hay fotos nuevas disponibles');
            return;
        }

        // Selección aleatoria
        shuffle($new_photos);
        $selected = array_slice($new_photos, 0, $limit);

        $selected_data = array();
        foreach ($selected as $photo) {
            $image_url = wp_get_attachment_image_src($photo->ID, 'medium');
            if ($image_url) {
                $selected_data[] = array(
                    'id' => $photo->ID,
                    'title' => get_the_title($photo->ID),
                    'url' => $image_url[0],
                    'date' => $photo->post_date
                );
            }
        }

        wp_send_json_success(array(
            'selected_photos' => $selected_data,
            'count' => count($selected_data)
        ));
    }

    public function ajax_save_private_photo() {
        $photo_id = intval($_POST['photo_id']);

        if (!$photo_id) {
            wp_send_json_error('ID de foto inválido');
            return;
        }

        $private_photos = get_option('soloylibre_private_photos', array());

        if (!in_array($photo_id, $private_photos)) {
            $private_photos[] = $photo_id;
            update_option('soloylibre_private_photos', $private_photos);
        }

        wp_send_json_success(array(
            'message' => 'Foto guardada en privado',
            'total_private' => count($private_photos)
        ));
    }

    public function ajax_get_private_photos() {
        $private_photos = get_option('soloylibre_private_photos', array());

        $private_data = array();
        foreach ($private_photos as $photo_id) {
            $image_url = wp_get_attachment_image_src($photo_id, 'medium');
            if ($image_url) {
                $private_data[] = array(
                    'id' => $photo_id,
                    'title' => get_the_title($photo_id),
                    'url' => $image_url[0],
                    'date' => get_the_date('Y-m-d H:i:s', $photo_id)
                );
            }
        }

        wp_send_json_success(array(
            'private_photos' => $private_data,
            'count' => count($private_data)
        ));
    }

    private function get_wizard_css() {
        return "
        .soloylibre-simple-wizard {
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .wizard-header {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            z-index: 10;
        }

        .wizard-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 700;
            letter-spacing: 1px;
        }

        .wizard-header p {
            margin: 5px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
            font-size: 1.1rem;
        }

        .wizard-content {
            padding: 30px;
        }

        .photo-status, .gallery-config, .photo-preview, .auto-selection, .private-photos {
            margin-bottom: 30px;
        }

        .status-card, .config-card, .preview-card, .auto-card, .private-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .selection-mode {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .mode-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .mode-btn {
            padding: 10px 20px;
            border: 2px solid #CE1126;
            background: white;
            color: #CE1126;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .mode-btn.active {
            background: #CE1126;
            color: white;
        }

        .mode-btn:hover {
            background: #CE1126;
            color: white;
        }

        .auto-selection {
            display: block;
        }

        .selected-photos {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .selected-photo {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .selected-photo:hover {
            border-color: #CE1126;
        }

        .selected-photo img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .photo-actions {
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
        }

        .photo-action-btn {
            width: 25px;
            height: 25px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
        }

        .private-btn {
            background: #6c757d;
            color: white;
        }

        .photo-action-btn:hover {
            transform: scale(1.1);
        }

        .unused-btn {
            background: #6c757d;
            color: white;
        }

        .unwanted-btn {
            background: #dc3545;
            color: white;
        }

        .private-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .private-photo {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #6c757d;
        }

        .private-photo img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .private-photo-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 10px 5px 5px;
            font-size: 0.8rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .status-number {
            display: block;
            font-size: 2rem;
            font-weight: bold;
            color: #CE1126;
        }

        .status-label {
            display: block;
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .config-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .config-item label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #495057;
        }

        .config-item input, .config-item select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }

        .photos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }

        .photo-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e9ecef;
        }

        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #CE1126;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #a00e1f;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #545b62;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1rem;
        }

        .no-photos {
            text-align: center;
            padding: 40px;
            color: #28a745;
        }

        .result-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
        }

        .result-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        ";
    }

    private function get_wizard_js() {
        return "
        jQuery(document).ready(function($) {
            // Cargar estado inicial
            loadPhotosAndStatus();

            // Event listeners
            $('#refresh-status').click(loadPhotosAndStatus);
            $('#create-gallery').click(createGallery);
            $('#preview-gallery').click(previewGallery);
            $('#auto-mode').click(function() { switchMode('auto'); });
            $('#manual-mode').click(function() { switchMode('manual'); });
            $('#generate-selection').click(generateAutoSelection);
            $('#view-private').click(loadPrivatePhotos);

            function loadPhotosAndStatus() {
                $('#loading-photos').show();
                $('#photos-grid').empty();
                $('#no-photos').hide();

                $.post(ajaxurl, {
                    action: 'load_photos_simple'
                }, function(response) {
                    $('#loading-photos').hide();

                    if (response.success) {
                        updateStatus(response.data);
                        displayPhotos(response.data.new_photos);

                        if (response.data.new_count > 0) {
                            $('#create-gallery, #preview-gallery').prop('disabled', false);
                        } else {
                            $('#no-photos').show();
                            $('#create-gallery, #preview-gallery').prop('disabled', true);
                        }
                    }
                });
            }

            function updateStatus(data) {
                $('#total-photos').text(data.total_photos);
                $('#published-photos').text(data.published_photos);
                $('#new-photos').text(data.new_count);
            }

            function displayPhotos(photos) {
                const grid = $('#photos-grid');
                grid.empty();

                photos.slice(0, 20).forEach(function(photo) {
                    const item = $('<div class=\"photo-item\"><img src=\"' + photo.url + '\" alt=\"' + photo.title + '\" title=\"' + photo.title + '\"></div>');
                    grid.append(item);
                });

                if (photos.length > 20) {
                    grid.append('<div class=\"photo-item\" style=\"display: flex; align-items: center; justify-content: center; background: #f8f9fa; color: #6c757d; font-weight: bold;\">+' + (photos.length - 20) + ' más</div>');
                }
            }

            function createGallery() {
                const title = $('#gallery-title').val();
                const style = $('#gallery-style').val();
                const limit = $('#photos-limit').val();
                const selectedPhotos = getSelectedPhotos();

                if (!title.trim()) {
                    alert('Por favor ingresa un título para la galería');
                    return;
                }

                $('#create-gallery').prop('disabled', true).text('🔄 Creando...');

                $.post(ajaxurl, {
                    action: 'create_gallery_quick',
                    title: title,
                    style: style,
                    limit: limit,
                    selected_photos: selectedPhotos,
                    nonce: '<?php echo wp_create_nonce("soloylibre_wizard_nonce"); ?>'
                }, function(response) {
                    $('#create-gallery').prop('disabled', false).text('🚀 Crear y Publicar Galería');

                    if (response.success) {
                        // Mostrar modal de éxito con cierre del wizard
                        showSuccessModal(response.data);

                        // Recargar estado después de un momento
                        setTimeout(loadPhotosAndStatus, 3000);
                    } else {
                        const resultDiv = $('#result-message');
                        resultDiv.removeClass('success').addClass('error');
                        resultDiv.text('❌ ' + response.data);
                        resultDiv.show();
                    }
                });
            }

            function getSelectedPhotos() {
                const selected = [];
                $('.photo-item.selected').each(function() {
                    const photoId = $(this).data('photo-id');
                    if (photoId) {
                        selected.push(photoId);
                    }
                });
                return selected;
            }

            function showSuccessModal(data) {
                // Crear modal de éxito
                const modal = $('<div class=\"success-modal-overlay\">').html(data.success_html);

                // Agregar estilos del modal
                modal.append(`
                    <style>
                        .success-modal-overlay {
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0,0,0,0.8);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            z-index: 10000;
                        }
                        .gallery-success-container {
                            background: white;
                            border-radius: 20px;
                            padding: 30px;
                            max-width: 600px;
                            width: 90%;
                            text-align: center;
                            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                        }
                        .success-header h3 {
                            color: #28a745;
                            font-size: 2rem;
                            margin: 0 0 10px 0;
                        }
                        .success-actions {
                            margin: 20px 0;
                            display: flex;
                            gap: 10px;
                            justify-content: center;
                            flex-wrap: wrap;
                        }
                        .success-actions .btn {
                            padding: 12px 20px;
                            border: none;
                            border-radius: 25px;
                            text-decoration: none;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        }
                        .btn-primary { background: #007cba; color: white; }
                        .btn-secondary { background: #6c757d; color: white; }
                        .btn-success { background: #28a745; color: white; }
                        .btn-info { background: #17a2b8; color: white; }
                        .success-stats {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                            gap: 15px;
                            margin: 20px 0;
                        }
                        .stat-item {
                            background: #f8f9fa;
                            padding: 10px;
                            border-radius: 8px;
                        }
                        .link-input {
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            font-size: 12px;
                        }
                    </style>
                `);

                $('body').append(modal);

                // Cerrar modal al hacer clic fuera
                modal.click(function(e) {
                    if (e.target === this) {
                        $(this).remove();
                    }
                });
            }

            // Funciones globales para los botones del modal
            window.createAnotherGallery = function() {
                $('.success-modal-overlay').remove();
                $('#gallery-title').val('');
                loadPhotosAndStatus();
            };

            window.goToStatistics = function() {
                window.open('admin.php?page=soloylibre-statistics', '_blank');
            };

            // Funciones para categorizar fotos
            window.markUnused = function(photoId) {
                $.post(ajaxurl, {
                    action: 'mark_photo_unused',
                    photo_id: photoId
                }, function(response) {
                    if (response.success) {
                        $('[data-id=\"' + photoId + '\"]').fadeOut(300, function() {
                            $(this).remove();
                        });
                        showNotification('📦 Foto marcada como no usada', 'success');
                        updatePhotoCount();
                    } else {
                        showNotification('❌ Error: ' + response.data, 'error');
                    }
                });
            };

            window.markUnwanted = function(photoId) {
                if (confirm('¿Estás seguro de marcar esta foto como no deseada?')) {
                    $.post(ajaxurl, {
                        action: 'mark_photo_unwanted',
                        photo_id: photoId
                    }, function(response) {
                        if (response.success) {
                            $('[data-id=\"' + photoId + '\"]').fadeOut(300, function() {
                                $(this).remove();
                            });
                            showNotification('❌ Foto marcada como no deseada', 'success');
                            updatePhotoCount();
                        } else {
                            showNotification('❌ Error: ' + response.data, 'error');
                        }
                    });
                }
            };

            function showNotification(message, type) {
                const notification = $('<div class=\"wizard-notification ' + type + '\">' + message + '</div>');
                notification.css({
                    position: 'fixed',
                    top: '20px',
                    right: '20px',
                    background: type === 'success' ? '#28a745' : '#dc3545',
                    color: 'white',
                    padding: '15px 20px',
                    borderRadius: '8px',
                    zIndex: '10001',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
                });

                $('body').append(notification);

                setTimeout(function() {
                    notification.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            function updatePhotoCount() {
                const count = $('.selected-photo').length;
                $('#photos-count').text(count + ' fotos seleccionadas');

                if (count > 0) {
                    $('#create-gallery, #preview-gallery').prop('disabled', false);
                } else {
                    $('#create-gallery, #preview-gallery').prop('disabled', true);
                }
            }

            function previewGallery() {
                const style = $('#gallery-style').val();
                const limit = $('#photos-limit').val();

                alert('Vista previa: Se creará una galería estilo \"' + style + '\" con ' + (limit === 'all' ? 'todas las fotos nuevas' : limit + ' fotos'));
            }

            function switchMode(mode) {
                $('.mode-btn').removeClass('active');
                if (mode === 'auto') {
                    $('#auto-mode').addClass('active');
                    $('#auto-selection').show();
                } else {
                    $('#manual-mode').addClass('active');
                    $('#auto-selection').hide();
                }
            }

            function generateAutoSelection() {
                const limit = $('#photos-limit').val();
                const numLimit = limit === 'all' ? 50 : parseInt(limit);

                $('#generate-selection').prop('disabled', true).text('🔄 Generando...');

                $.post(ajaxurl, {
                    action: 'auto_select_photos',
                    limit: numLimit
                }, function(response) {
                    $('#generate-selection').prop('disabled', false).text('🎯 Generar Selección');

                    if (response.success) {
                        displaySelectedPhotos(response.data.selected_photos);
                    } else {
                        alert('Error: ' + response.data);
                    }
                });
            }

            function displaySelectedPhotos(photos) {
                const container = $('#selected-photos');
                container.empty();

                photos.forEach(function(photo) {
                    const photoDiv = $('<div class=\"selected-photo\" data-id=\"' + photo.id + '\">');
                    photoDiv.html(
                        '<img src=\"' + photo.url + '\" alt=\"' + photo.title + '\">' +
                        '<div class=\"photo-actions\">' +
                        '<button class=\"photo-action-btn remove-btn\" onclick=\"removePhoto(' + photo.id + ')\" title=\"Quitar de selección\">×</button>' +
                        '<button class=\"photo-action-btn private-btn\" onclick=\"savePrivate(' + photo.id + ')\" title=\"Guardar en privado\">👁️</button>' +
                        '<button class=\"photo-action-btn unused-btn\" onclick=\"markUnused(' + photo.id + ')\" title=\"Marcar como no usada\">📦</button>' +
                        '<button class=\"photo-action-btn unwanted-btn\" onclick=\"markUnwanted(' + photo.id + ')\" title=\"Marcar como no deseada\">❌</button>' +
                        '</div>'
                    );
                    container.append(photoDiv);
                });
            }

            window.removePhoto = function(photoId) {
                $('.selected-photo[data-id=\"' + photoId + '\"]').remove();
            };

            window.savePrivate = function(photoId) {
                $.post(ajaxurl, {
                    action: 'save_private_photo',
                    photo_id: photoId
                }, function(response) {
                    if (response.success) {
                        alert('✅ ' + response.data.message);
                        removePhoto(photoId);
                    } else {
                        alert('❌ Error: ' + response.data);
                    }
                });
            };

            function loadPrivatePhotos() {
                $('#view-private').prop('disabled', true).text('🔄 Cargando...');

                $.post(ajaxurl, {
                    action: 'get_private_photos'
                }, function(response) {
                    $('#view-private').prop('disabled', false).text('👁️ Ver Mis Fotos Privadas');

                    if (response.success) {
                        displayPrivatePhotos(response.data.private_photos);
                        $('#private-gallery').show();
                    } else {
                        alert('Error: ' + response.data);
                    }
                });
            }

            function displayPrivatePhotos(photos) {
                const container = $('#private-gallery');
                container.empty();

                if (photos.length === 0) {
                    container.html('<p style=\"text-align: center; color: #6c757d;\">No tienes fotos privadas guardadas.</p>');
                    return;
                }

                photos.forEach(function(photo) {
                    const photoDiv = $('<div class=\"private-photo\">');
                    photoDiv.html(
                        '<img src=\"' + photo.url + '\" alt=\"' + photo.title + '\">' +
                        '<div class=\"private-photo-info\">' +
                        '<div>' + photo.title + '</div>' +
                        '<div style=\"font-size: 0.7rem; opacity: 0.8;\">' + photo.date + '</div>' +
                        '</div>'
                    );
                    container.append(photoDiv);
                });
            }
        });
        ";
    }

    /**
     * Mark photo as unused
     */
    public function ajax_mark_photo_unused() {
        $photo_id = intval($_POST['photo_id']);

        if (!$photo_id) {
            wp_send_json_error('ID de foto inválido');
            return;
        }

        $unused_photos = get_option('soloylibre_unused_photos', array());

        if (!in_array($photo_id, $unused_photos)) {
            $unused_photos[] = $photo_id;
            update_option('soloylibre_unused_photos', $unused_photos);

            // Remover de otras categorías si existe
            $this->remove_from_other_categories($photo_id, 'unused');
        }

        wp_send_json_success(array(
            'message' => 'Foto marcada como no usada',
            'total_unused' => count($unused_photos)
        ));
    }

    /**
     * Mark photo as unwanted
     */
    public function ajax_mark_photo_unwanted() {
        $photo_id = intval($_POST['photo_id']);

        if (!$photo_id) {
            wp_send_json_error('ID de foto inválido');
            return;
        }

        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        if (!in_array($photo_id, $unwanted_photos)) {
            $unwanted_photos[] = $photo_id;
            update_option('soloylibre_unwanted_photos', $unwanted_photos);

            // Remover de otras categorías si existe
            $this->remove_from_other_categories($photo_id, 'unwanted');
        }

        wp_send_json_success(array(
            'message' => 'Foto marcada como no deseada',
            'total_unwanted' => count($unwanted_photos)
        ));
    }

    /**
     * Get photo categories
     */
    public function ajax_get_photo_categories() {
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unused_photos = get_option('soloylibre_unused_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        wp_send_json_success(array(
            'published' => $published_photos,
            'private' => $private_photos,
            'unused' => $unused_photos,
            'unwanted' => $unwanted_photos,
            'counts' => array(
                'published' => count($published_photos),
                'private' => count($private_photos),
                'unused' => count($unused_photos),
                'unwanted' => count($unwanted_photos)
            )
        ));
    }

    /**
     * Remove photo from other categories
     */
    private function remove_from_other_categories($photo_id, $current_category) {
        $categories = array(
            'published' => 'soloylibre_published_photos',
            'private' => 'soloylibre_private_photos',
            'unused' => 'soloylibre_unused_photos',
            'unwanted' => 'soloylibre_unwanted_photos'
        );

        foreach ($categories as $category => $option_name) {
            if ($category !== $current_category) {
                $photos = get_option($option_name, array());
                $key = array_search($photo_id, $photos);
                if ($key !== false) {
                    unset($photos[$key]);
                    update_option($option_name, array_values($photos));
                }
            }
        }
    }
}

// Inicializar el wizard
new SoloYLibre_Improved_Wizard();
?>
