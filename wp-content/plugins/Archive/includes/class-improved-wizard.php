<?php
/**
 * Simple Gallery Wizard - Auto-load Photos with Duplicate Prevention
 * Wizard simple de gal<PERSON>ías - Auto-carga fotos con prevención de duplicados
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

class SoloYLibre_Improved_Wizard {
    
    public function __construct() {
        add_action("admin_menu", array($this, "add_admin_menu"));
        add_action("admin_enqueue_scripts", array($this, "enqueue_scripts"));
        add_action("wp_ajax_load_photos_simple", array($this, "ajax_load_photos"));
        add_action("wp_ajax_create_gallery_quick", array($this, "ajax_create_gallery"));
        add_action("wp_ajax_get_published_status", array($this, "ajax_get_published_status"));
        add_action("wp_ajax_auto_select_photos", array($this, "ajax_auto_select_photos"));
        add_action("wp_ajax_save_private_photo", array($this, "ajax_save_private_photo"));
        add_action("wp_ajax_get_private_photos", array($this, "ajax_get_private_photos"));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            "SoloYLibre Wizard",
            "📸 SoloYLibre",
            "edit_posts",
            "soloylibre-wizard",
            array($this, "render_wizard"),
            "dashicons-camera",
            25
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== "toplevel_page_soloylibre-wizard") return;
        
        wp_enqueue_script("jquery");
        wp_add_inline_script("jquery", $this->get_wizard_js());
        wp_add_inline_style("wp-admin", $this->get_wizard_css());
    }
    
    public function render_wizard() {
        // Obtener información del fotógrafo
        $photographer_info = get_option('soloylibre_photographer_info', array(
            'full_name' => 'Jose L Encarnacion',
            'nickname' => 'JoseTusabe',
            'business_name' => 'SoloYLibre Photography'
        ));
        ?>
        <div class="soloylibre-simple-wizard">
            <div class="wizard-header">
                <h1>📸 Crear Galería SoloYLibre - Simple y Rápido</h1>
                <p>🇩🇴 <?php echo esc_html($photographer_info['full_name']); ?> (<?php echo esc_html($photographer_info['nickname']); ?>) - <?php echo esc_html($photographer_info['business_name']); ?></p>
            </div>
            
            <div class="wizard-content">
                <!-- Estado de fotos -->
                <div class="photo-status">
                    <div class="status-card">
                        <h3>📊 Estado de Fotos</h3>
                        <div class="status-grid">
                            <div class="status-item">
                                <span class="status-number" id="total-photos">...</span>
                                <span class="status-label">📷 Total Disponibles</span>
                            </div>
                            <div class="status-item">
                                <span class="status-number" id="published-photos">...</span>
                                <span class="status-label">✅ Ya Publicadas</span>
                            </div>
                            <div class="status-item">
                                <span class="status-number" id="new-photos">...</span>
                                <span class="status-label">🆕 Nuevas</span>
                            </div>
                        </div>
                        <button id="refresh-status" class="btn btn-secondary">🔄 Actualizar</button>
                    </div>
                </div>
                
                <!-- Configuración simple -->
                <div class="gallery-config">
                    <div class="config-card">
                        <h3>⚙️ Configuración Rápida</h3>
                        <div class="config-row">
                            <div class="config-item">
                                <label for="gallery-title">📝 Título de la Galería</label>
                                <input type="text" id="gallery-title" placeholder="Ej: Fotos Nuevas - <?php echo date('M Y'); ?>" value="Galería SoloYLibre - <?php echo date('M Y'); ?>">
                            </div>
                            <div class="config-item">
                                <label for="gallery-style">🎨 Estilo</label>
                                <select id="gallery-style">
                                    <option value="masonry">🧱 Masonry (Recomendado)</option>
                                    <option value="dominican">🇩🇴 Dominicano</option>
                                    <option value="grid">📱 Grid</option>
                                    <option value="professional">💼 Profesional</option>
                                </select>
                            </div>
                            <div class="config-item">
                                <label for="photos-limit">📷 Cantidad de Fotos</label>
                                <select id="photos-limit">
                                    <option value="20">20 fotos</option>
                                    <option value="50" selected>50 fotos</option>
                                    <option value="100">100 fotos</option>
                                    <option value="all">Todas las nuevas</option>
                                </select>
                            </div>
                        </div>

                        <!-- Modo de selección -->
                        <div class="selection-mode">
                            <h4>🎯 Modo de Selección</h4>
                            <div class="mode-buttons">
                                <button type="button" id="auto-mode" class="mode-btn active">🎲 Auto-Selección</button>
                                <button type="button" id="manual-mode" class="mode-btn">✋ Selección Manual</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auto-selección de fotos -->
                <div id="auto-selection" class="auto-selection">
                    <div class="auto-card">
                        <h3>🎲 Auto-Selección Inteligente</h3>
                        <p>El sistema seleccionará fotos aleatoriamente. Puedes revisar y quitar las que no quieras.</p>
                        <button id="generate-selection" class="btn btn-secondary">🎯 Generar Selección</button>
                        <div id="selected-photos" class="selected-photos"></div>
                    </div>
                </div>
                
                <!-- Vista previa de fotos -->
                <div class="photo-preview">
                    <div class="preview-card">
                        <h3>🖼️ Vista Previa de Fotos Nuevas</h3>
                        <div id="loading-photos" class="loading">
                            <span>🔄 Cargando fotos nuevas...</span>
                        </div>
                        <div id="photos-grid" class="photos-grid"></div>
                        <div id="no-photos" class="no-photos" style="display: none;">
                            <p>✅ ¡Todas las fotos ya están publicadas!</p>
                            <p>Sube nuevas fotos a la biblioteca de medios para crear más galerías.</p>
                        </div>
                    </div>
                </div>

                <!-- Fotos privadas -->
                <div class="private-photos">
                    <div class="private-card">
                        <h3>👁️ Fotos Privadas - Solo Para Tus Ojos</h3>
                        <p>Guarda fotos que quieres revisar más tarde, editar o publicar en privado.</p>
                        <button id="view-private" class="btn btn-secondary">👁️ Ver Mis Fotos Privadas</button>
                        <div id="private-gallery" class="private-gallery" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- Botones de acción -->
                <div class="action-buttons">
                    <button id="create-gallery" class="btn btn-primary" disabled>
                        🚀 Crear y Publicar Galería
                    </button>
                    <button id="preview-gallery" class="btn btn-secondary" disabled>
                        👁️ Vista Previa
                    </button>
                </div>
                
                <!-- Resultado -->
                <div id="result-message" class="result-message" style="display: none;"></div>
            </div>
        </div>
        <?php
    }
    
    public function ajax_load_photos() {
        // Obtener todas las fotos disponibles
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        // Obtener fotos ya publicadas
        $published_photos = get_option('soloylibre_published_photos', array());
        
        // Filtrar fotos nuevas (no publicadas)
        $new_photos = array();
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $published_photos)) {
                $image_url = wp_get_attachment_image_src($photo->ID, 'thumbnail');
                if ($image_url) {
                    $new_photos[] = array(
                        'id' => $photo->ID,
                        'title' => get_the_title($photo->ID),
                        'url' => $image_url[0],
                        'date' => $photo->post_date
                    );
                }
            }
        }
        
        wp_send_json_success(array(
            'total_photos' => count($all_photos),
            'published_photos' => count($published_photos),
            'new_photos' => $new_photos,
            'new_count' => count($new_photos)
        ));
    }
    
    public function ajax_get_published_status() {
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit'
        ));
        
        $published_photos = get_option('soloylibre_published_photos', array());
        
        wp_send_json_success(array(
            'total' => count($all_photos),
            'published' => count($published_photos),
            'new' => count($all_photos) - count($published_photos)
        ));
    }
    
    public function ajax_create_gallery() {
        $title = sanitize_text_field($_POST['title']);
        $style = sanitize_text_field($_POST['style']);
        $limit = sanitize_text_field($_POST['limit']);
        
        // Obtener fotos nuevas
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        $published_photos = get_option('soloylibre_published_photos', array());
        $new_photos = array();
        
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $published_photos)) {
                $new_photos[] = $photo->ID;
            }
        }
        
        if (empty($new_photos)) {
            wp_send_json_error('No hay fotos nuevas para publicar');
            return;
        }
        
        // Limitar fotos según selección
        if ($limit !== 'all' && is_numeric($limit)) {
            $new_photos = array_slice($new_photos, 0, intval($limit));
        }
        
        // Crear el post de galería
        $post_content = "[soloylibre_gallery style=\"$style\" ids=\"" . implode(',', $new_photos) . "\" columns=\"4\" enable_interactions=\"true\" aspect_ratio=\"natural\"]";
        
        $post_id = wp_insert_post(array(
            'post_title' => $title,
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => get_current_user_id()
        ));
        
        if ($post_id) {
            // Marcar fotos como publicadas
            $published_photos = array_merge($published_photos, $new_photos);
            update_option('soloylibre_published_photos', $published_photos);
            
            wp_send_json_success(array(
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'photos_published' => count($new_photos),
                'message' => "¡Galería '$title' creada exitosamente con " . count($new_photos) . " fotos!"
            ));
        } else {
            wp_send_json_error('Error al crear la galería');
        }
    }

    public function ajax_auto_select_photos() {
        $limit = intval($_POST['limit']);

        // Obtener fotos nuevas
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $new_photos = array();

        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $published_photos) && !in_array($photo->ID, $private_photos)) {
                $new_photos[] = $photo;
            }
        }

        if (empty($new_photos)) {
            wp_send_json_error('No hay fotos nuevas disponibles');
            return;
        }

        // Selección aleatoria
        shuffle($new_photos);
        $selected = array_slice($new_photos, 0, $limit);

        $selected_data = array();
        foreach ($selected as $photo) {
            $image_url = wp_get_attachment_image_src($photo->ID, 'medium');
            if ($image_url) {
                $selected_data[] = array(
                    'id' => $photo->ID,
                    'title' => get_the_title($photo->ID),
                    'url' => $image_url[0],
                    'date' => $photo->post_date
                );
            }
        }

        wp_send_json_success(array(
            'selected_photos' => $selected_data,
            'count' => count($selected_data)
        ));
    }

    public function ajax_save_private_photo() {
        $photo_id = intval($_POST['photo_id']);

        if (!$photo_id) {
            wp_send_json_error('ID de foto inválido');
            return;
        }

        $private_photos = get_option('soloylibre_private_photos', array());

        if (!in_array($photo_id, $private_photos)) {
            $private_photos[] = $photo_id;
            update_option('soloylibre_private_photos', $private_photos);
        }

        wp_send_json_success(array(
            'message' => 'Foto guardada en privado',
            'total_private' => count($private_photos)
        ));
    }

    public function ajax_get_private_photos() {
        $private_photos = get_option('soloylibre_private_photos', array());

        $private_data = array();
        foreach ($private_photos as $photo_id) {
            $image_url = wp_get_attachment_image_src($photo_id, 'medium');
            if ($image_url) {
                $private_data[] = array(
                    'id' => $photo_id,
                    'title' => get_the_title($photo_id),
                    'url' => $image_url[0],
                    'date' => get_the_date('Y-m-d H:i:s', $photo_id)
                );
            }
        }

        wp_send_json_success(array(
            'private_photos' => $private_data,
            'count' => count($private_data)
        ));
    }

    private function get_wizard_css() {
        return "
        .soloylibre-simple-wizard {
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .wizard-header {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            z-index: 10;
        }

        .wizard-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 700;
            letter-spacing: 1px;
        }

        .wizard-header p {
            margin: 5px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
            font-size: 1.1rem;
        }

        .wizard-content {
            padding: 30px;
        }

        .photo-status, .gallery-config, .photo-preview, .auto-selection, .private-photos {
            margin-bottom: 30px;
        }

        .status-card, .config-card, .preview-card, .auto-card, .private-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .selection-mode {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .mode-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .mode-btn {
            padding: 10px 20px;
            border: 2px solid #CE1126;
            background: white;
            color: #CE1126;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .mode-btn.active {
            background: #CE1126;
            color: white;
        }

        .mode-btn:hover {
            background: #CE1126;
            color: white;
        }

        .auto-selection {
            display: block;
        }

        .selected-photos {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .selected-photo {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .selected-photo:hover {
            border-color: #CE1126;
        }

        .selected-photo img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .photo-actions {
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
        }

        .photo-action-btn {
            width: 25px;
            height: 25px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
        }

        .private-btn {
            background: #6c757d;
            color: white;
        }

        .photo-action-btn:hover {
            transform: scale(1.1);
        }

        .private-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .private-photo {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #6c757d;
        }

        .private-photo img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .private-photo-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 10px 5px 5px;
            font-size: 0.8rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .status-number {
            display: block;
            font-size: 2rem;
            font-weight: bold;
            color: #CE1126;
        }

        .status-label {
            display: block;
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .config-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .config-item label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #495057;
        }

        .config-item input, .config-item select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }

        .photos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }

        .photo-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e9ecef;
        }

        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #CE1126;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #a00e1f;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #545b62;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1rem;
        }

        .no-photos {
            text-align: center;
            padding: 40px;
            color: #28a745;
        }

        .result-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
        }

        .result-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        ";
    }

    private function get_wizard_js() {
        return "
        jQuery(document).ready(function($) {
            // Cargar estado inicial
            loadPhotosAndStatus();

            // Event listeners
            $('#refresh-status').click(loadPhotosAndStatus);
            $('#create-gallery').click(createGallery);
            $('#preview-gallery').click(previewGallery);
            $('#auto-mode').click(function() { switchMode('auto'); });
            $('#manual-mode').click(function() { switchMode('manual'); });
            $('#generate-selection').click(generateAutoSelection);
            $('#view-private').click(loadPrivatePhotos);

            function loadPhotosAndStatus() {
                $('#loading-photos').show();
                $('#photos-grid').empty();
                $('#no-photos').hide();

                $.post(ajaxurl, {
                    action: 'load_photos_simple'
                }, function(response) {
                    $('#loading-photos').hide();

                    if (response.success) {
                        updateStatus(response.data);
                        displayPhotos(response.data.new_photos);

                        if (response.data.new_count > 0) {
                            $('#create-gallery, #preview-gallery').prop('disabled', false);
                        } else {
                            $('#no-photos').show();
                            $('#create-gallery, #preview-gallery').prop('disabled', true);
                        }
                    }
                });
            }

            function updateStatus(data) {
                $('#total-photos').text(data.total_photos);
                $('#published-photos').text(data.published_photos);
                $('#new-photos').text(data.new_count);
            }

            function displayPhotos(photos) {
                const grid = $('#photos-grid');
                grid.empty();

                photos.slice(0, 20).forEach(function(photo) {
                    const item = $('<div class=\"photo-item\"><img src=\"' + photo.url + '\" alt=\"' + photo.title + '\" title=\"' + photo.title + '\"></div>');
                    grid.append(item);
                });

                if (photos.length > 20) {
                    grid.append('<div class=\"photo-item\" style=\"display: flex; align-items: center; justify-content: center; background: #f8f9fa; color: #6c757d; font-weight: bold;\">+' + (photos.length - 20) + ' más</div>');
                }
            }

            function createGallery() {
                const title = $('#gallery-title').val();
                const style = $('#gallery-style').val();
                const limit = $('#photos-limit').val();

                if (!title.trim()) {
                    alert('Por favor ingresa un título para la galería');
                    return;
                }

                $('#create-gallery').prop('disabled', true).text('🔄 Creando...');

                $.post(ajaxurl, {
                    action: 'create_gallery_quick',
                    title: title,
                    style: style,
                    limit: limit
                }, function(response) {
                    $('#create-gallery').prop('disabled', false).text('🚀 Crear y Publicar Galería');

                    const resultDiv = $('#result-message');

                    if (response.success) {
                        resultDiv.removeClass('error').addClass('success');
                        resultDiv.html('✅ ' + response.data.message + '<br><a href=\"' + response.data.post_url + '\" target=\"_blank\">👁️ Ver Galería Publicada</a>');
                        resultDiv.show();

                        // Recargar estado
                        setTimeout(loadPhotosAndStatus, 2000);
                    } else {
                        resultDiv.removeClass('success').addClass('error');
                        resultDiv.text('❌ ' + response.data);
                        resultDiv.show();
                    }
                });
            }

            function previewGallery() {
                const style = $('#gallery-style').val();
                const limit = $('#photos-limit').val();

                alert('Vista previa: Se creará una galería estilo \"' + style + '\" con ' + (limit === 'all' ? 'todas las fotos nuevas' : limit + ' fotos'));
            }

            function switchMode(mode) {
                $('.mode-btn').removeClass('active');
                if (mode === 'auto') {
                    $('#auto-mode').addClass('active');
                    $('#auto-selection').show();
                } else {
                    $('#manual-mode').addClass('active');
                    $('#auto-selection').hide();
                }
            }

            function generateAutoSelection() {
                const limit = $('#photos-limit').val();
                const numLimit = limit === 'all' ? 50 : parseInt(limit);

                $('#generate-selection').prop('disabled', true).text('🔄 Generando...');

                $.post(ajaxurl, {
                    action: 'auto_select_photos',
                    limit: numLimit
                }, function(response) {
                    $('#generate-selection').prop('disabled', false).text('🎯 Generar Selección');

                    if (response.success) {
                        displaySelectedPhotos(response.data.selected_photos);
                    } else {
                        alert('Error: ' + response.data);
                    }
                });
            }

            function displaySelectedPhotos(photos) {
                const container = $('#selected-photos');
                container.empty();

                photos.forEach(function(photo) {
                    const photoDiv = $('<div class=\"selected-photo\" data-id=\"' + photo.id + '\">');
                    photoDiv.html(
                        '<img src=\"' + photo.url + '\" alt=\"' + photo.title + '\">' +
                        '<div class=\"photo-actions\">' +
                        '<button class=\"photo-action-btn remove-btn\" onclick=\"removePhoto(' + photo.id + ')\" title=\"Quitar\">×</button>' +
                        '<button class=\"photo-action-btn private-btn\" onclick=\"savePrivate(' + photo.id + ')\" title=\"Guardar en privado\">👁️</button>' +
                        '</div>'
                    );
                    container.append(photoDiv);
                });
            }

            window.removePhoto = function(photoId) {
                $('.selected-photo[data-id=\"' + photoId + '\"]').remove();
            };

            window.savePrivate = function(photoId) {
                $.post(ajaxurl, {
                    action: 'save_private_photo',
                    photo_id: photoId
                }, function(response) {
                    if (response.success) {
                        alert('✅ ' + response.data.message);
                        removePhoto(photoId);
                    } else {
                        alert('❌ Error: ' + response.data);
                    }
                });
            };

            function loadPrivatePhotos() {
                $('#view-private').prop('disabled', true).text('🔄 Cargando...');

                $.post(ajaxurl, {
                    action: 'get_private_photos'
                }, function(response) {
                    $('#view-private').prop('disabled', false).text('👁️ Ver Mis Fotos Privadas');

                    if (response.success) {
                        displayPrivatePhotos(response.data.private_photos);
                        $('#private-gallery').show();
                    } else {
                        alert('Error: ' + response.data);
                    }
                });
            }

            function displayPrivatePhotos(photos) {
                const container = $('#private-gallery');
                container.empty();

                if (photos.length === 0) {
                    container.html('<p style=\"text-align: center; color: #6c757d;\">No tienes fotos privadas guardadas.</p>');
                    return;
                }

                photos.forEach(function(photo) {
                    const photoDiv = $('<div class=\"private-photo\">');
                    photoDiv.html(
                        '<img src=\"' + photo.url + '\" alt=\"' + photo.title + '\">' +
                        '<div class=\"private-photo-info\">' +
                        '<div>' + photo.title + '</div>' +
                        '<div style=\"font-size: 0.7rem; opacity: 0.8;\">' + photo.date + '</div>' +
                        '</div>'
                    );
                    container.append(photoDiv);
                });
            }
        });
        ";
    }
}

// Inicializar el wizard
new SoloYLibre_Improved_Wizard();
?>
