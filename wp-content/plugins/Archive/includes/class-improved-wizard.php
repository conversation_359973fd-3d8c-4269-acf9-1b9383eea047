<?php
/**
 * Simple Gallery Wizard - Auto-load Photos with Duplicate Prevention
 * Wizard simple de gal<PERSON>ías - Auto-carga fotos con prevención de duplicados
 * Developed by JEYKO AI for Jose L Encarnac<PERSON> (JoseTusabe)
 */

class SoloYLibre_Improved_Wizard {
    
    public function __construct() {
        add_action("admin_menu", array($this, "add_admin_menu"));
        add_action("admin_enqueue_scripts", array($this, "enqueue_scripts"));
        add_action("wp_ajax_load_photos_simple", array($this, "ajax_load_photos"));
        add_action("wp_ajax_create_gallery_quick", array($this, "ajax_create_gallery"));
        add_action("wp_ajax_get_published_status", array($this, "ajax_get_published_status"));
        add_action("wp_ajax_auto_select_photos", array($this, "ajax_auto_select_photos"));
        add_action("wp_ajax_save_private_photo", array($this, "ajax_save_private_photo"));
        add_action("wp_ajax_get_private_photos", array($this, "ajax_get_private_photos"));
        add_action("wp_ajax_mark_photo_unused", array($this, "ajax_mark_photo_unused"));
        add_action("wp_ajax_mark_photo_unwanted", array($this, "ajax_mark_photo_unwanted"));
        add_action("wp_ajax_get_photo_categories", array($this, "ajax_get_photo_categories"));
        add_action("wp_ajax_auto_generate_post", array($this, "ajax_auto_generate_post"));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            "SoloYLibre Wizard",
            "📸 SoloYLibre",
            "edit_posts",
            "soloylibre-wizard",
            array($this, "render_wizard"),
            "dashicons-camera",
            25
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== "toplevel_page_soloylibre-wizard") return;
        
        wp_enqueue_script("jquery");
        wp_add_inline_script("jquery", $this->get_wizard_js());
        wp_add_inline_style("wp-admin", $this->get_wizard_css());
    }
    
    public function render_wizard() {
        // Obtener información del fotógrafo
        $photographer_info = get_option('soloylibre_photographer_info', array(
            'full_name' => 'Jose L Encarnacion',
            'nickname' => 'JoseTusabe',
            'business_name' => 'SoloYLibre Photography'
        ));
        ?>
        <div class="soloylibre-simple-wizard">
            <div class="wizard-header">
                <h1>📸 Crear Galería SoloYLibre - Simple y Rápido</h1>
                <p>🇩🇴 <?php echo esc_html($photographer_info['full_name']); ?> (<?php echo esc_html($photographer_info['nickname']); ?>) - <?php echo esc_html($photographer_info['business_name']); ?></p>
            </div>
            
            <div class="wizard-content">
                <!-- Estado de fotos -->
                <div class="photo-status">
                    <div class="status-card">
                        <h3>📊 Estado de Fotos</h3>
                        <div class="status-grid">
                            <div class="status-item">
                                <span class="status-number" id="total-photos">...</span>
                                <span class="status-label">📷 Total Disponibles</span>
                            </div>
                            <div class="status-item">
                                <span class="status-number" id="published-photos">...</span>
                                <span class="status-label">✅ Ya Publicadas</span>
                            </div>
                            <div class="status-item">
                                <span class="status-number" id="new-photos">...</span>
                                <span class="status-label">🆕 Nuevas</span>
                            </div>
                        </div>
                        <button id="refresh-status" class="btn btn-secondary">🔄 Actualizar</button>
                    </div>
                </div>
                
                <!-- Configuración simple -->
                <div class="gallery-config">
                    <div class="config-card">
                        <h3>⚙️ Configuración Rápida</h3>
                        <div class="config-row">
                            <div class="config-item">
                                <label for="gallery-title">📝 Título de la Galería</label>
                                <input type="text" id="gallery-title" placeholder="Ej: Fotos Nuevas - <?php echo date('M Y'); ?>" value="Galería SoloYLibre - <?php echo date('M Y'); ?>">
                            </div>
                            <div class="config-item">
                                <label for="gallery-style">🎨 Estilo</label>
                                <select id="gallery-style">
                                    <option value="masonry">🧱 Masonry (Recomendado)</option>
                                    <option value="dominican">🇩🇴 Dominicano</option>
                                    <option value="grid">📱 Grid</option>
                                    <option value="professional">💼 Profesional</option>
                                </select>
                            </div>
                            <div class="config-item">
                                <label for="photos-limit">📷 Cantidad de Fotos</label>
                                <select id="photos-limit">
                                    <option value="20">20 fotos</option>
                                    <option value="50" selected>50 fotos</option>
                                    <option value="100">100 fotos</option>
                                    <option value="all">Todas las nuevas</option>
                                </select>
                            </div>
                        </div>

                        <!-- Modo de selección -->
                        <div class="selection-mode">
                            <h4>🎯 Modo de Selección</h4>
                            <div class="mode-buttons">
                                <button type="button" id="auto-mode" class="mode-btn active">🎲 Auto-Selección</button>
                                <button type="button" id="manual-mode" class="mode-btn">✋ Selección Manual</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auto-selección de fotos -->
                <div id="auto-selection" class="auto-selection">
                    <div class="auto-card">
                        <h3>🎲 Auto-Selección Inteligente</h3>
                        <p>El sistema seleccionará fotos aleatoriamente. Puedes revisar y quitar las que no quieras.</p>
                        <button id="generate-selection" class="btn btn-secondary">🎯 Generar Selección</button>
                        <div id="selected-photos" class="selected-photos"></div>
                    </div>
                </div>
                
                <!-- Vista previa de fotos -->
                <div class="photo-preview">
                    <div class="preview-card">
                        <div class="photos-header">
                            <h3>📸 Seleccionar Fotos</h3>
                            <div class="photos-controls">
                                <div class="control-group">
                                    <label for="sort-order">🔄 Ordenar:</label>
                                    <select id="sort-order" onchange="loadPhotosWithSort()">
                                        <option value="desc">📅 Más Recientes</option>
                                        <option value="asc">📅 Más Antiguas</option>
                                        <option value="random">🎲 Aleatorio</option>
                                        <option value="title">🔤 Por Título</option>
                                    </select>
                                </div>

                                <div class="control-group">
                                    <label for="photos-per-load">📊 Mostrar:</label>
                                    <select id="photos-per-load" onchange="loadPhotosWithSort()">
                                        <option value="25">25 fotos</option>
                                        <option value="50" selected>50 fotos</option>
                                        <option value="100">100 fotos</option>
                                        <option value="200">200 fotos</option>
                                    </select>
                                </div>

                                <div class="control-group">
                                    <button id="auto-generate-post" class="btn btn-auto" onclick="autoGeneratePost()">
                                        🤖 Auto-Generar Post
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="photos-stats">
                            <span id="photos-loaded-count">0 fotos cargadas</span>
                            <span id="photos-selected-count">0 seleccionadas</span>
                            <span id="photos-total-available">0 disponibles</span>
                        </div>

                        <div id="loading-photos" class="loading">
                            <span>🔄 Cargando fotos...</span>
                        </div>

                        <div id="photos-grid" class="photos-grid"></div>

                        <div class="load-more-section">
                            <button id="load-more-photos" class="btn btn-secondary" style="display: none;" onclick="loadMorePhotos()">
                                📥 Cargar Más Fotos
                            </button>
                            <div id="load-more-info" class="load-info"></div>
                        </div>

                        <div id="no-photos" class="no-photos" style="display: none;">
                            <p>✅ ¡Todas las fotos ya están publicadas!</p>
                            <p>Sube nuevas fotos a la biblioteca de medios para crear más galerías.</p>
                        </div>
                    </div>
                </div>

                <!-- Fotos privadas -->
                <div class="private-photos">
                    <div class="private-card">
                        <h3>👁️ Fotos Privadas - Solo Para Tus Ojos</h3>
                        <p>Guarda fotos que quieres revisar más tarde, editar o publicar en privado.</p>
                        <button id="view-private" class="btn btn-secondary">👁️ Ver Mis Fotos Privadas</button>
                        <div id="private-gallery" class="private-gallery" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- Botones de acción -->
                <div class="action-buttons">
                    <button id="create-gallery" class="btn btn-primary" disabled>
                        🚀 Crear y Publicar Galería
                    </button>
                    <button id="preview-gallery" class="btn btn-secondary" disabled>
                        👁️ Vista Previa
                    </button>
                </div>
                
                <!-- Resultado -->
                <div id="result-message" class="result-message" style="display: none;"></div>
            </div>
        </div>
        <?php
    }
    
    public function ajax_load_photos() {
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $per_page = isset($_POST['per_page']) ? intval($_POST['per_page']) : 50;
        $order = isset($_POST['order']) ? sanitize_text_field($_POST['order']) : 'desc';
        $orderby = 'date';

        // Configurar ordenamiento
        switch ($order) {
            case 'asc':
                $orderby = 'date';
                $order = 'ASC';
                break;
            case 'desc':
                $orderby = 'date';
                $order = 'DESC';
                break;
            case 'random':
                $orderby = 'rand';
                $order = '';
                break;
            case 'title':
                $orderby = 'title';
                $order = 'ASC';
                break;
            default:
                $orderby = 'date';
                $order = 'DESC';
        }

        // Obtener todas las fotos para contar
        $all_photos_query = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'fields' => 'ids'
        );

        $all_photo_ids = get_posts($all_photos_query);

        // Obtener fotos categorizadas
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unused_photos = get_option('soloylibre_unused_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        $categorized_photos = array_merge($published_photos, $private_photos, $unused_photos, $unwanted_photos);

        // Filtrar fotos no categorizadas
        $available_photo_ids = array_diff($all_photo_ids, $categorized_photos);

        if (empty($available_photo_ids)) {
            wp_send_json_success(array(
                'photos' => array(),
                'new_count' => 0,
                'published_photos' => count($published_photos),
                'total_photos' => count($all_photo_ids),
                'available_photos' => 0,
                'has_more' => false,
                'current_page' => $page,
                'total_pages' => 0
            ));
            return;
        }

        // Obtener fotos paginadas con ordenamiento
        $offset = ($page - 1) * $per_page;

        $photos_query = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => $per_page,
            'offset' => $offset,
            'post_status' => 'inherit',
            'orderby' => $orderby,
            'order' => $order,
            'post__in' => $available_photo_ids
        );

        $photos = get_posts($photos_query);
        $formatted_photos = array();

        foreach ($photos as $photo) {
            $image_url = wp_get_attachment_image_src($photo->ID, 'thumbnail');
            if ($image_url) {
                $formatted_photos[] = array(
                    'id' => $photo->ID,
                    'url' => $image_url[0],
                    'full_url' => wp_get_attachment_image_url($photo->ID, 'large'),
                    'title' => get_the_title($photo->ID) ?: 'Sin título',
                    'date' => $photo->post_date,
                    'size' => $this->get_file_size($photo->ID)
                );
            }
        }

        $total_available = count($available_photo_ids);
        $total_pages = ceil($total_available / $per_page);
        $has_more = $page < $total_pages;

        wp_send_json_success(array(
            'photos' => $formatted_photos,
            'new_count' => count($formatted_photos),
            'published_photos' => count($published_photos),
            'total_photos' => count($all_photo_ids),
            'available_photos' => $total_available,
            'has_more' => $has_more,
            'current_page' => $page,
            'total_pages' => $total_pages,
            'loaded_count' => count($formatted_photos),
            'order' => $order,
            'orderby' => $orderby
        ));
    }

    /**
     * Get file size for attachment
     */
    private function get_file_size($attachment_id) {
        $file_path = get_attached_file($attachment_id);
        if ($file_path && file_exists($file_path)) {
            $size = filesize($file_path);
            return $this->format_file_size($size);
        }
        return 'N/A';
    }

    /**
     * Format file size
     */
    private function format_file_size($size) {
        if ($size >= 1048576) {
            return round($size / 1048576, 1) . ' MB';
        } elseif ($size >= 1024) {
            return round($size / 1024, 1) . ' KB';
        }
        return $size . ' B';
    }

    public function ajax_get_published_status() {
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit'
        ));
        
        $published_photos = get_option('soloylibre_published_photos', array());
        
        wp_send_json_success(array(
            'total' => count($all_photos),
            'published' => count($published_photos),
            'new' => count($all_photos) - count($published_photos)
        ));
    }
    
    public function ajax_create_gallery() {
        // Verificar nonce para seguridad
        if (!wp_verify_nonce($_POST['nonce'], 'soloylibre_wizard_nonce')) {
            wp_send_json_error('Error de seguridad');
            return;
        }

        $title = sanitize_text_field($_POST['title']);
        $style = sanitize_text_field($_POST['style']);
        $limit = sanitize_text_field($_POST['limit']);
        $selected_photos = isset($_POST['selected_photos']) ? array_map('intval', $_POST['selected_photos']) : array();

        // Si hay fotos seleccionadas específicamente, usar esas
        if (!empty($selected_photos)) {
            $photos_to_publish = $selected_photos;
        } else {
            // Obtener fotos nuevas (no publicadas)
            $all_photos = get_posts(array(
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'posts_per_page' => -1,
                'post_status' => 'inherit',
                'orderby' => 'date',
                'order' => 'DESC'
            ));

            $published_photos = get_option('soloylibre_published_photos', array());
            $private_photos = get_option('soloylibre_private_photos', array());
            $unused_photos = get_option('soloylibre_unused_photos', array());
            $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

            // Fotos que ya están categorizadas
            $categorized_photos = array_merge($published_photos, $private_photos, $unused_photos, $unwanted_photos);

            $new_photos = array();
            foreach ($all_photos as $photo) {
                if (!in_array($photo->ID, $categorized_photos)) {
                    $new_photos[] = $photo->ID;
                }
            }

            if (empty($new_photos)) {
                wp_send_json_error('No hay fotos nuevas para publicar. Todas las fotos ya están categorizadas.');
                return;
            }

            // Limitar fotos según selección
            if ($limit !== 'all' && is_numeric($limit)) {
                $new_photos = array_slice($new_photos, 0, intval($limit));
            }

            $photos_to_publish = $new_photos;
        }

        if (empty($photos_to_publish)) {
            wp_send_json_error('No hay fotos para publicar');
            return;
        }

        // Crear el post de galería
        $post_content = "[soloylibre_gallery style=\"$style\" ids=\"" . implode(',', $photos_to_publish) . "\" columns=\"4\" enable_interactions=\"true\" aspect_ratio=\"natural\"]";

        $post_id = wp_insert_post(array(
            'post_title' => $title,
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                '_soloylibre_gallery_photos' => $photos_to_publish,
                '_soloylibre_gallery_style' => $style,
                '_soloylibre_creation_date' => current_time('mysql')
            )
        ));

        if ($post_id) {
            // Marcar fotos como publicadas (evitar duplicados)
            $published_photos = get_option('soloylibre_published_photos', array());
            $new_published = array_unique(array_merge($published_photos, $photos_to_publish));
            update_option('soloylibre_published_photos', $new_published);

            // Actualizar estadísticas mensuales
            $monthly_key = 'soloylibre_monthly_published_' . date('Y_m');
            $monthly_published = get_option($monthly_key, array());
            $monthly_published = array_unique(array_merge($monthly_published, $photos_to_publish));
            update_option($monthly_key, $monthly_published);

            // Incrementar contador de galerías creadas
            $galleries_created = get_option('soloylibre_galleries_created', 0);
            update_option('soloylibre_galleries_created', $galleries_created + 1);

            wp_send_json_success(array(
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'edit_url' => get_edit_post_link($post_id),
                'view_url' => get_permalink($post_id),
                'photos_published' => count($photos_to_publish),
                'total_published' => count($new_published),
                'gallery_title' => $title,
                'message' => "¡Galería '$title' creada exitosamente con " . count($photos_to_publish) . " fotos!",
                'success_html' => $this->get_success_html($post_id, $title, count($photos_to_publish))
            ));
        } else {
            wp_send_json_error('Error al crear la galería');
        }
    }

    /**
     * Get success HTML for gallery creation
     */
    private function get_success_html($post_id, $title, $photo_count) {
        $post_url = get_permalink($post_id);
        $edit_url = get_edit_post_link($post_id);

        return "
        <div class='gallery-success-container'>
            <div class='success-header'>
                <h3>🎉 ¡Galería Creada Exitosamente!</h3>
                <p class='gallery-title'>\"$title\" con $photo_count fotos</p>
            </div>

            <div class='success-actions'>
                <a href='$post_url' target='_blank' class='btn btn-primary'>
                    👁️ Ver Galería
                </a>
                <a href='$edit_url' target='_blank' class='btn btn-secondary'>
                    ✏️ Editar Post
                </a>
                <button class='btn btn-success' onclick='createAnotherGallery()'>
                    ➕ Crear Otra Galería
                </button>
                <button class='btn btn-info' onclick='goToStatistics()'>
                    📊 Ver Estadísticas
                </button>
            </div>

            <div class='success-stats'>
                <div class='stat-item'>
                    <span class='stat-label'>ID del Post:</span>
                    <span class='stat-value'>#$post_id</span>
                </div>
                <div class='stat-item'>
                    <span class='stat-label'>Fotos Publicadas:</span>
                    <span class='stat-value'>$photo_count</span>
                </div>
                <div class='stat-item'>
                    <span class='stat-label'>Fecha:</span>
                    <span class='stat-value'>" . date('d/m/Y H:i') . "</span>
                </div>
            </div>

            <div class='success-links'>
                <p><strong>Enlaces directos:</strong></p>
                <div class='link-item'>
                    <label>Ver Galería:</label>
                    <input type='text' value='$post_url' readonly onclick='this.select()' class='link-input'>
                </div>
                <div class='link-item'>
                    <label>Editar Post:</label>
                    <input type='text' value='$edit_url' readonly onclick='this.select()' class='link-input'>
                </div>
            </div>
        </div>";
    }

    public function ajax_auto_select_photos() {
        $limit = intval($_POST['limit']);

        // Obtener fotos nuevas
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $new_photos = array();

        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $published_photos) && !in_array($photo->ID, $private_photos)) {
                $new_photos[] = $photo;
            }
        }

        if (empty($new_photos)) {
            wp_send_json_error('No hay fotos nuevas disponibles');
            return;
        }

        // Selección aleatoria
        shuffle($new_photos);
        $selected = array_slice($new_photos, 0, $limit);

        $selected_data = array();
        foreach ($selected as $photo) {
            $image_url = wp_get_attachment_image_src($photo->ID, 'medium');
            if ($image_url) {
                $selected_data[] = array(
                    'id' => $photo->ID,
                    'title' => get_the_title($photo->ID),
                    'url' => $image_url[0],
                    'date' => $photo->post_date
                );
            }
        }

        wp_send_json_success(array(
            'selected_photos' => $selected_data,
            'count' => count($selected_data)
        ));
    }

    public function ajax_save_private_photo() {
        $photo_id = intval($_POST['photo_id']);

        if (!$photo_id) {
            wp_send_json_error('ID de foto inválido');
            return;
        }

        $private_photos = get_option('soloylibre_private_photos', array());

        if (!in_array($photo_id, $private_photos)) {
            $private_photos[] = $photo_id;
            update_option('soloylibre_private_photos', $private_photos);
        }

        wp_send_json_success(array(
            'message' => 'Foto guardada en privado',
            'total_private' => count($private_photos)
        ));
    }

    public function ajax_get_private_photos() {
        $private_photos = get_option('soloylibre_private_photos', array());

        $private_data = array();
        foreach ($private_photos as $photo_id) {
            $image_url = wp_get_attachment_image_src($photo_id, 'medium');
            if ($image_url) {
                $private_data[] = array(
                    'id' => $photo_id,
                    'title' => get_the_title($photo_id),
                    'url' => $image_url[0],
                    'date' => get_the_date('Y-m-d H:i:s', $photo_id)
                );
            }
        }

        wp_send_json_success(array(
            'private_photos' => $private_data,
            'count' => count($private_data)
        ));
    }

    private function get_wizard_css() {
        return "
        .soloylibre-simple-wizard {
            max-width: 1200px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .wizard-header {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            z-index: 10;
        }

        .wizard-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 700;
            letter-spacing: 1px;
        }

        .wizard-header p {
            margin: 5px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
            font-size: 1.1rem;
        }

        .wizard-content {
            padding: 30px;
        }

        .photo-status, .gallery-config, .photo-preview, .auto-selection, .private-photos {
            margin-bottom: 30px;
        }

        .status-card, .config-card, .preview-card, .auto-card, .private-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .selection-mode {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .mode-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .mode-btn {
            padding: 10px 20px;
            border: 2px solid #CE1126;
            background: white;
            color: #CE1126;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .mode-btn.active {
            background: #CE1126;
            color: white;
        }

        .mode-btn:hover {
            background: #CE1126;
            color: white;
        }

        .auto-selection {
            display: block;
        }

        .selected-photos {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .selected-photo {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .selected-photo:hover {
            border-color: #CE1126;
        }

        .selected-photo img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .photo-actions {
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
        }

        .photo-action-btn {
            width: 25px;
            height: 25px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
        }

        .private-btn {
            background: #6c757d;
            color: white;
        }

        .photo-action-btn:hover {
            transform: scale(1.1);
        }

        .unused-btn {
            background: #6c757d;
            color: white;
        }

        .unwanted-btn {
            background: #dc3545;
            color: white;
        }

        .private-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .private-photo {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #6c757d;
        }

        .private-photo img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }

        .private-photo-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 10px 5px 5px;
            font-size: 0.8rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .status-number {
            display: block;
            font-size: 2rem;
            font-weight: bold;
            color: #CE1126;
        }

        .status-label {
            display: block;
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .config-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .config-item label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #495057;
        }

        .config-item input, .config-item select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }

        .photos-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .photos-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .control-group select {
            padding: 6px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background: white;
            font-size: 14px;
            min-width: 120px;
        }

        .btn-auto {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-auto:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .photos-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
        }

        .photos-stats span {
            color: #495057;
        }

        .photos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
            min-height: 200px;
        }

        .load-more-section {
            text-align: center;
            margin: 20px 0;
        }

        .load-info {
            margin-top: 10px;
            font-size: 14px;
            color: #6c757d;
        }

        .photo-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 8px;
            overflow: hidden;
            border: 3px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .photo-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .photo-item.selected {
            border-color: #007cba;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 124, 186, 0.3);
        }

        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .photo-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.7));
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 8px;
        }

        .photo-item:hover .photo-overlay {
            opacity: 1;
        }

        .photo-info {
            color: white;
            font-size: 11px;
        }

        .photo-title {
            display: block;
            font-weight: 600;
            margin-bottom: 2px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .photo-size {
            display: block;
            opacity: 0.8;
        }

        .select-btn {
            background: #28a745;
            color: white;
        }

        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #CE1126;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #a00e1f;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #545b62;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-size: 1.1rem;
        }

        .no-photos {
            text-align: center;
            padding: 40px;
            color: #28a745;
        }

        .result-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
        }

        .result-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        ";
    }

    private function get_wizard_js() {
        return "
        jQuery(document).ready(function($) {
            // Cargar estado inicial
            loadPhotosAndStatus();

            // Event listeners
            $('#refresh-status').click(loadPhotosAndStatus);
            $('#create-gallery').click(createGallery);
            $('#preview-gallery').click(previewGallery);
            $('#auto-mode').click(function() { switchMode('auto'); });
            $('#manual-mode').click(function() { switchMode('manual'); });
            $('#generate-selection').click(generateAutoSelection);
            $('#view-private').click(loadPrivatePhotos);

            // Variables globales para paginación
            let currentPage = 1;
            let totalPages = 1;
            let currentOrder = 'desc';
            let currentPerPage = 50;
            let loadedPhotos = [];

            function loadPhotosAndStatus() {
                loadPhotosWithSort();
            }

            window.loadPhotosWithSort = function() {
                currentPage = 1;
                loadedPhotos = [];
                currentOrder = $('#sort-order').val();
                currentPerPage = parseInt($('#photos-per-load').val());

                loadPhotos(true);
            };

            window.loadMorePhotos = function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadPhotos(false);
                }
            };

            function loadPhotos(reset = false) {
                if (reset) {
                    $('#photos-grid').empty();
                    $('#load-more-photos').hide();
                    loadedPhotos = [];
                    currentPage = 1;
                }

                $('#loading-photos').show();
                $('#no-photos').hide();

                $.post(ajaxurl, {
                    action: 'load_photos_simple',
                    page: currentPage,
                    per_page: currentPerPage,
                    order: currentOrder
                }, function(response) {
                    $('#loading-photos').hide();

                    if (response.success) {
                        const data = response.data;

                        if (data.photos && data.photos.length > 0) {
                            if (reset) {
                                loadedPhotos = data.photos;
                            } else {
                                loadedPhotos = loadedPhotos.concat(data.photos);
                            }

                            displayPhotos(data.photos, !reset);
                            updatePhotoStats(data);

                            // Mostrar botón "Cargar más" si hay más fotos
                            if (data.has_more) {
                                $('#load-more-photos').show();
                                $('#load-more-info').text(`Página ${data.current_page} de ${data.total_pages} - ${data.available_photos} fotos disponibles`);
                            } else {
                                $('#load-more-photos').hide();
                                $('#load-more-info').text(`Todas las fotos cargadas (${data.available_photos} total)`);
                            }

                            totalPages = data.total_pages;
                            $('#create-gallery, #preview-gallery').prop('disabled', false);
                        } else {
                            $('#no-photos').show();
                            $('#create-gallery, #preview-gallery').prop('disabled', true);
                            $('#load-more-photos').hide();
                        }
                    }
                });
            }

            function updatePhotoStats(data) {
                $('#photos-loaded-count').text(`${loadedPhotos.length} fotos cargadas`);
                $('#photos-selected-count').text(`${getSelectedPhotos().length} seleccionadas`);
                $('#photos-total-available').text(`${data.available_photos} disponibles`);
            }

            function updateStatus(data) {
                $('#total-photos').text(data.total_photos);
                $('#published-photos').text(data.published_photos);
                $('#new-photos').text(data.new_count);
            }

            function displayPhotos(photos, append = false) {
                const grid = $('#photos-grid');

                if (!append) {
                    grid.empty();
                }

                photos.forEach(function(photo) {
                    const item = $(`
                        <div class="photo-item" data-photo-id="${photo.id}" onclick="togglePhotoSelection(this)">
                            <img src="${photo.url}" alt="${photo.title}" title="${photo.title} (${photo.size})">
                            <div class="photo-overlay">
                                <div class="photo-info">
                                    <span class="photo-title">${photo.title}</span>
                                    <span class="photo-size">${photo.size}</span>
                                </div>
                                <div class="photo-actions">
                                    <button class="photo-action-btn select-btn" onclick="event.stopPropagation(); togglePhotoSelection(this.closest('.photo-item'))" title="Seleccionar">✓</button>
                                    <button class="photo-action-btn private-btn" onclick="event.stopPropagation(); savePrivate(${photo.id})" title="Guardar en privado">👁️</button>
                                    <button class="photo-action-btn unused-btn" onclick="event.stopPropagation(); markUnused(${photo.id})" title="Marcar como no usada">📦</button>
                                    <button class="photo-action-btn unwanted-btn" onclick="event.stopPropagation(); markUnwanted(${photo.id})" title="Marcar como no deseada">❌</button>
                                </div>
                            </div>
                        </div>
                    `);
                    grid.append(item);
                });
            }

            window.togglePhotoSelection = function(element) {
                $(element).toggleClass('selected');
                updatePhotoSelectionCount();
            };

            function updatePhotoSelectionCount() {
                const selectedCount = $('.photo-item.selected').length;
                $('#photos-selected-count').text(`${selectedCount} seleccionadas`);

                if (selectedCount > 0) {
                    $('#create-gallery, #preview-gallery').prop('disabled', false);
                } else {
                    $('#create-gallery, #preview-gallery').prop('disabled', true);
                }
            }

            function createGallery() {
                const title = $('#gallery-title').val();
                const style = $('#gallery-style').val();
                const limit = $('#photos-limit').val();
                const selectedPhotos = getSelectedPhotos();

                if (!title.trim()) {
                    alert('Por favor ingresa un título para la galería');
                    return;
                }

                $('#create-gallery').prop('disabled', true).text('🔄 Creando...');

                $.post(ajaxurl, {
                    action: 'create_gallery_quick',
                    title: title,
                    style: style,
                    limit: limit,
                    selected_photos: selectedPhotos,
                    nonce: '<?php echo wp_create_nonce("soloylibre_wizard_nonce"); ?>'
                }, function(response) {
                    $('#create-gallery').prop('disabled', false).text('🚀 Crear y Publicar Galería');

                    if (response.success) {
                        // Mostrar modal de éxito con cierre del wizard
                        showSuccessModal(response.data);

                        // Recargar estado después de un momento
                        setTimeout(loadPhotosAndStatus, 3000);
                    } else {
                        const resultDiv = $('#result-message');
                        resultDiv.removeClass('success').addClass('error');
                        resultDiv.text('❌ ' + response.data);
                        resultDiv.show();
                    }
                });
            }

            function getSelectedPhotos() {
                const selected = [];
                $('.photo-item.selected').each(function() {
                    const photoId = $(this).data('photo-id');
                    if (photoId) {
                        selected.push(photoId);
                    }
                });
                return selected;
            }

            function showSuccessModal(data) {
                // Crear modal de éxito
                const modal = $('<div class=\"success-modal-overlay\">').html(data.success_html);

                // Agregar estilos del modal
                modal.append(`
                    <style>
                        .success-modal-overlay {
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0,0,0,0.8);
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            z-index: 10000;
                        }
                        .gallery-success-container {
                            background: white;
                            border-radius: 20px;
                            padding: 30px;
                            max-width: 600px;
                            width: 90%;
                            text-align: center;
                            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                        }
                        .success-header h3 {
                            color: #28a745;
                            font-size: 2rem;
                            margin: 0 0 10px 0;
                        }
                        .success-actions {
                            margin: 20px 0;
                            display: flex;
                            gap: 10px;
                            justify-content: center;
                            flex-wrap: wrap;
                        }
                        .success-actions .btn {
                            padding: 12px 20px;
                            border: none;
                            border-radius: 25px;
                            text-decoration: none;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        }
                        .btn-primary { background: #007cba; color: white; }
                        .btn-secondary { background: #6c757d; color: white; }
                        .btn-success { background: #28a745; color: white; }
                        .btn-info { background: #17a2b8; color: white; }
                        .success-stats {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                            gap: 15px;
                            margin: 20px 0;
                        }
                        .stat-item {
                            background: #f8f9fa;
                            padding: 10px;
                            border-radius: 8px;
                        }
                        .link-input {
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            font-size: 12px;
                        }
                    </style>
                `);

                $('body').append(modal);

                // Cerrar modal al hacer clic fuera
                modal.click(function(e) {
                    if (e.target === this) {
                        $(this).remove();
                    }
                });
            }

            // Funciones globales para los botones del modal
            window.createAnotherGallery = function() {
                $('.success-modal-overlay').remove();
                $('#gallery-title').val('');
                loadPhotosAndStatus();
            };

            window.goToStatistics = function() {
                window.open('admin.php?page=soloylibre-statistics', '_blank');
            };

            // Función para auto-generar post
            window.autoGeneratePost = function() {
                if (!confirm('¿Quieres auto-generar un post con selección inteligente de fotos?')) {
                    return;
                }

                $('#auto-generate-post').prop('disabled', true).text('🤖 Generando...');

                $.post(ajaxurl, {
                    action: 'auto_generate_post'
                }, function(response) {
                    $('#auto-generate-post').prop('disabled', false).text('🤖 Auto-Generar Post');

                    if (response.success) {
                        showAutoSuccessModal(response.data);
                        setTimeout(loadPhotosAndStatus, 2000);
                    } else {
                        alert('❌ Error: ' + response.data);
                    }
                });
            };

            function showAutoSuccessModal(data) {
                const modal = $('<div class=\"success-modal-overlay\">').html(data.success_html);

                modal.append(`
                    <style>
                        .auto-success-container {
                            background: linear-gradient(135deg, #28a745, #20c997);
                            color: white;
                            border-radius: 20px;
                            padding: 30px;
                            max-width: 600px;
                            width: 90%;
                            text-align: center;
                            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                        }
                        .auto-header h3 {
                            font-size: 2rem;
                            margin: 0 0 10px 0;
                        }
                        .auto-actions {
                            margin: 20px 0;
                            display: flex;
                            gap: 10px;
                            justify-content: center;
                            flex-wrap: wrap;
                        }
                        .auto-info {
                            background: rgba(255,255,255,0.1);
                            border-radius: 10px;
                            padding: 15px;
                            margin-top: 20px;
                            text-align: left;
                        }
                        .auto-info ul {
                            margin: 10px 0;
                            padding-left: 20px;
                        }
                    </style>
                `);

                $('body').append(modal);

                modal.click(function(e) {
                    if (e.target === this) {
                        $(this).remove();
                    }
                });
            }

            window.autoGenerateAnother = function() {
                $('.success-modal-overlay').remove();
                autoGeneratePost();
            };

            // Funciones para categorizar fotos
            window.markUnused = function(photoId) {
                $.post(ajaxurl, {
                    action: 'mark_photo_unused',
                    photo_id: photoId
                }, function(response) {
                    if (response.success) {
                        $('[data-id=\"' + photoId + '\"]').fadeOut(300, function() {
                            $(this).remove();
                        });
                        showNotification('📦 Foto marcada como no usada', 'success');
                        updatePhotoCount();
                    } else {
                        showNotification('❌ Error: ' + response.data, 'error');
                    }
                });
            };

            window.markUnwanted = function(photoId) {
                if (confirm('¿Estás seguro de marcar esta foto como no deseada?')) {
                    $.post(ajaxurl, {
                        action: 'mark_photo_unwanted',
                        photo_id: photoId
                    }, function(response) {
                        if (response.success) {
                            $('[data-id=\"' + photoId + '\"]').fadeOut(300, function() {
                                $(this).remove();
                            });
                            showNotification('❌ Foto marcada como no deseada', 'success');
                            updatePhotoCount();
                        } else {
                            showNotification('❌ Error: ' + response.data, 'error');
                        }
                    });
                }
            };

            function showNotification(message, type) {
                const notification = $('<div class=\"wizard-notification ' + type + '\">' + message + '</div>');
                notification.css({
                    position: 'fixed',
                    top: '20px',
                    right: '20px',
                    background: type === 'success' ? '#28a745' : '#dc3545',
                    color: 'white',
                    padding: '15px 20px',
                    borderRadius: '8px',
                    zIndex: '10001',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
                });

                $('body').append(notification);

                setTimeout(function() {
                    notification.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            function updatePhotoCount() {
                const count = $('.selected-photo').length;
                $('#photos-count').text(count + ' fotos seleccionadas');

                if (count > 0) {
                    $('#create-gallery, #preview-gallery').prop('disabled', false);
                } else {
                    $('#create-gallery, #preview-gallery').prop('disabled', true);
                }
            }

            function previewGallery() {
                const style = $('#gallery-style').val();
                const limit = $('#photos-limit').val();

                alert('Vista previa: Se creará una galería estilo \"' + style + '\" con ' + (limit === 'all' ? 'todas las fotos nuevas' : limit + ' fotos'));
            }

            function switchMode(mode) {
                $('.mode-btn').removeClass('active');
                if (mode === 'auto') {
                    $('#auto-mode').addClass('active');
                    $('#auto-selection').show();
                } else {
                    $('#manual-mode').addClass('active');
                    $('#auto-selection').hide();
                }
            }

            function generateAutoSelection() {
                const limit = $('#photos-limit').val();
                const numLimit = limit === 'all' ? 50 : parseInt(limit);

                $('#generate-selection').prop('disabled', true).text('🔄 Generando...');

                $.post(ajaxurl, {
                    action: 'auto_select_photos',
                    limit: numLimit
                }, function(response) {
                    $('#generate-selection').prop('disabled', false).text('🎯 Generar Selección');

                    if (response.success) {
                        displaySelectedPhotos(response.data.selected_photos);
                    } else {
                        alert('Error: ' + response.data);
                    }
                });
            }

            function displaySelectedPhotos(photos) {
                const container = $('#selected-photos');
                container.empty();

                photos.forEach(function(photo) {
                    const photoDiv = $('<div class=\"selected-photo\" data-id=\"' + photo.id + '\">');
                    photoDiv.html(
                        '<img src=\"' + photo.url + '\" alt=\"' + photo.title + '\">' +
                        '<div class=\"photo-actions\">' +
                        '<button class=\"photo-action-btn remove-btn\" onclick=\"removePhoto(' + photo.id + ')\" title=\"Quitar de selección\">×</button>' +
                        '<button class=\"photo-action-btn private-btn\" onclick=\"savePrivate(' + photo.id + ')\" title=\"Guardar en privado\">👁️</button>' +
                        '<button class=\"photo-action-btn unused-btn\" onclick=\"markUnused(' + photo.id + ')\" title=\"Marcar como no usada\">📦</button>' +
                        '<button class=\"photo-action-btn unwanted-btn\" onclick=\"markUnwanted(' + photo.id + ')\" title=\"Marcar como no deseada\">❌</button>' +
                        '</div>'
                    );
                    container.append(photoDiv);
                });
            }

            window.removePhoto = function(photoId) {
                $('.selected-photo[data-id=\"' + photoId + '\"]').remove();
            };

            window.savePrivate = function(photoId) {
                $.post(ajaxurl, {
                    action: 'save_private_photo',
                    photo_id: photoId
                }, function(response) {
                    if (response.success) {
                        alert('✅ ' + response.data.message);
                        removePhoto(photoId);
                    } else {
                        alert('❌ Error: ' + response.data);
                    }
                });
            };

            function loadPrivatePhotos() {
                $('#view-private').prop('disabled', true).text('🔄 Cargando...');

                $.post(ajaxurl, {
                    action: 'get_private_photos'
                }, function(response) {
                    $('#view-private').prop('disabled', false).text('👁️ Ver Mis Fotos Privadas');

                    if (response.success) {
                        displayPrivatePhotos(response.data.private_photos);
                        $('#private-gallery').show();
                    } else {
                        alert('Error: ' + response.data);
                    }
                });
            }

            function displayPrivatePhotos(photos) {
                const container = $('#private-gallery');
                container.empty();

                if (photos.length === 0) {
                    container.html('<p style=\"text-align: center; color: #6c757d;\">No tienes fotos privadas guardadas.</p>');
                    return;
                }

                photos.forEach(function(photo) {
                    const photoDiv = $('<div class=\"private-photo\">');
                    photoDiv.html(
                        '<img src=\"' + photo.url + '\" alt=\"' + photo.title + '\">' +
                        '<div class=\"private-photo-info\">' +
                        '<div>' + photo.title + '</div>' +
                        '<div style=\"font-size: 0.7rem; opacity: 0.8;\">' + photo.date + '</div>' +
                        '</div>'
                    );
                    container.append(photoDiv);
                });
            }
        });
        ";
    }

    /**
     * Mark photo as unused
     */
    public function ajax_mark_photo_unused() {
        $photo_id = intval($_POST['photo_id']);

        if (!$photo_id) {
            wp_send_json_error('ID de foto inválido');
            return;
        }

        $unused_photos = get_option('soloylibre_unused_photos', array());

        if (!in_array($photo_id, $unused_photos)) {
            $unused_photos[] = $photo_id;
            update_option('soloylibre_unused_photos', $unused_photos);

            // Remover de otras categorías si existe
            $this->remove_from_other_categories($photo_id, 'unused');
        }

        wp_send_json_success(array(
            'message' => 'Foto marcada como no usada',
            'total_unused' => count($unused_photos)
        ));
    }

    /**
     * Mark photo as unwanted
     */
    public function ajax_mark_photo_unwanted() {
        $photo_id = intval($_POST['photo_id']);

        if (!$photo_id) {
            wp_send_json_error('ID de foto inválido');
            return;
        }

        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        if (!in_array($photo_id, $unwanted_photos)) {
            $unwanted_photos[] = $photo_id;
            update_option('soloylibre_unwanted_photos', $unwanted_photos);

            // Remover de otras categorías si existe
            $this->remove_from_other_categories($photo_id, 'unwanted');
        }

        wp_send_json_success(array(
            'message' => 'Foto marcada como no deseada',
            'total_unwanted' => count($unwanted_photos)
        ));
    }

    /**
     * Get photo categories
     */
    public function ajax_get_photo_categories() {
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unused_photos = get_option('soloylibre_unused_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        wp_send_json_success(array(
            'published' => $published_photos,
            'private' => $private_photos,
            'unused' => $unused_photos,
            'unwanted' => $unwanted_photos,
            'counts' => array(
                'published' => count($published_photos),
                'private' => count($private_photos),
                'unused' => count($unused_photos),
                'unwanted' => count($unwanted_photos)
            )
        ));
    }

    /**
     * Remove photo from other categories
     */
    private function remove_from_other_categories($photo_id, $current_category) {
        $categories = array(
            'published' => 'soloylibre_published_photos',
            'private' => 'soloylibre_private_photos',
            'unused' => 'soloylibre_unused_photos',
            'unwanted' => 'soloylibre_unwanted_photos'
        );

        foreach ($categories as $category => $option_name) {
            if ($category !== $current_category) {
                $photos = get_option($option_name, array());
                $key = array_search($photo_id, $photos);
                if ($key !== false) {
                    unset($photos[$key]);
                    update_option($option_name, array_values($photos));
                }
            }
        }
    }

    /**
     * Auto generate post with smart selection
     */
    public function ajax_auto_generate_post() {
        // Obtener fotos disponibles
        $all_photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        // Obtener fotos categorizadas
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unused_photos = get_option('soloylibre_unused_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        $categorized_photos = array_merge($published_photos, $private_photos, $unused_photos, $unwanted_photos);

        // Filtrar fotos disponibles
        $available_photos = array();
        foreach ($all_photos as $photo) {
            if (!in_array($photo->ID, $categorized_photos)) {
                $available_photos[] = $photo->ID;
            }
        }

        if (empty($available_photos)) {
            wp_send_json_error('No hay fotos disponibles para auto-generar post');
            return;
        }

        // Seleccionar fotos inteligentemente
        $selected_count = min(20, count($available_photos)); // Máximo 20 fotos
        $selected_photos = array_slice($available_photos, 0, $selected_count);

        // Generar título automático
        $current_date = date('d/m/Y');
        $photographer_info = get_option('soloylibre_photographer_info', array(
            'nickname' => 'JoseTusabe',
            'business_name' => 'SoloYLibre Photography'
        ));

        $auto_titles = array(
            "📸 Nuevas Capturas - {$photographer_info['nickname']} {$current_date}",
            "🌟 Galería Automática - SoloYLibre {$current_date}",
            "📷 Selección del Día - {$current_date}",
            "🎯 Auto-Galería SoloYLibre - {$current_date}",
            "✨ Fotos Destacadas - {$photographer_info['business_name']} {$current_date}"
        );

        $title = $auto_titles[array_rand($auto_titles)];

        // Crear el post automáticamente
        $post_content = "[soloylibre_gallery style=\"masonry\" ids=\"" . implode(',', $selected_photos) . "\" columns=\"4\" enable_interactions=\"true\" aspect_ratio=\"natural\"]";

        $post_id = wp_insert_post(array(
            'post_title' => $title,
            'post_content' => $post_content,
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                '_soloylibre_gallery_photos' => $selected_photos,
                '_soloylibre_gallery_style' => 'masonry',
                '_soloylibre_auto_generated' => true,
                '_soloylibre_creation_date' => current_time('mysql')
            )
        ));

        if ($post_id) {
            // Marcar fotos como publicadas
            $published_photos = array_unique(array_merge($published_photos, $selected_photos));
            update_option('soloylibre_published_photos', $published_photos);

            // Actualizar estadísticas
            $monthly_key = 'soloylibre_monthly_published_' . date('Y_m');
            $monthly_published = get_option($monthly_key, array());
            $monthly_published = array_unique(array_merge($monthly_published, $selected_photos));
            update_option($monthly_key, $monthly_published);

            // Incrementar contador de posts auto-generados
            $auto_posts = get_option('soloylibre_auto_posts_created', 0);
            update_option('soloylibre_auto_posts_created', $auto_posts + 1);

            wp_send_json_success(array(
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'edit_url' => get_edit_post_link($post_id),
                'view_url' => get_permalink($post_id),
                'photos_published' => count($selected_photos),
                'total_published' => count($published_photos),
                'gallery_title' => $title,
                'message' => "🤖 Post auto-generado exitosamente: '$title' con " . count($selected_photos) . " fotos!",
                'success_html' => $this->get_auto_success_html($post_id, $title, count($selected_photos))
            ));
        } else {
            wp_send_json_error('Error al auto-generar el post');
        }
    }

    /**
     * Get auto-generation success HTML
     */
    private function get_auto_success_html($post_id, $title, $photo_count) {
        $post_url = get_permalink($post_id);
        $edit_url = get_edit_post_link($post_id);

        return "
        <div class='auto-success-container'>
            <div class='auto-header'>
                <h3>🤖 ¡Post Auto-Generado Exitosamente!</h3>
                <p class='auto-title'>\"$title\"</p>
                <p class='auto-stats'>$photo_count fotos seleccionadas automáticamente</p>
            </div>

            <div class='auto-actions'>
                <a href='$post_url' target='_blank' class='btn btn-primary'>
                    👁️ Ver Post
                </a>
                <a href='$edit_url' target='_blank' class='btn btn-secondary'>
                    ✏️ Editar
                </a>
                <button class='btn btn-success' onclick='autoGenerateAnother()'>
                    🤖 Generar Otro
                </button>
                <button class='btn btn-info' onclick='goToStatistics()'>
                    📊 Estadísticas
                </button>
            </div>

            <div class='auto-info'>
                <p><strong>🎯 Selección Inteligente:</strong></p>
                <ul>
                    <li>✅ $photo_count fotos más recientes</li>
                    <li>🚫 Evitó fotos ya publicadas</li>
                    <li>📊 Estilo masonry optimizado</li>
                    <li>🔄 Interacciones habilitadas</li>
                </ul>
            </div>
        </div>";
    }
}

// Inicializar el wizard
new SoloYLibre_Improved_Wizard();
?>
