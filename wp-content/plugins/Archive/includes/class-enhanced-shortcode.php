<?php
/**
 * Enhanced SoloYLibre Gallery Shortcode Handler
 * Manejador mejorado de shortcodes con interacciones
 */

class SoloYLibre_Enhanced_Shortcode {
    
    private $settings;
    
    public function __construct() {
        $this->settings = new SoloYLibre_Settings_Manager();
        
        add_shortcode("soloylibre_gallery", array($this, "render_gallery"));
        add_action("wp_enqueue_scripts", array($this, "enqueue_assets"));
        add_action("wp_ajax_gallery_interaction", array($this, "handle_interaction"));
        add_action("wp_ajax_nopriv_gallery_interaction", array($this, "handle_interaction"));
    }
    
    public function enqueue_assets() {
        wp_enqueue_script("jquery");
        
        // CSS mejorado
        wp_add_inline_style("wp-block-library", $this->get_enhanced_css());
        
        // JavaScript con interacciones
        wp_add_inline_script("jquery", $this->get_enhanced_js());
        
        // Localizar script para AJAX
        wp_localize_script("jquery", "soloylibre_ajax", array(
            "ajax_url" => admin_url("admin-ajax.php"),
            "nonce" => wp_create_nonce("gallery_interaction")
        ));
    }
    
    public function render_gallery($atts) {
        $defaults = array(
            "ids" => "",
            "style" => $this->settings->get_option("gallery_default_style", "dominican"),
            "columns" => "3",
            "size" => "large",
            "lightbox" => $this->settings->get_option("lightbox_enabled", true) ? "true" : "false",
            "show_metadata" => "true",
            "show_title" => "true",
            "show_caption" => "false",
            "hover_effect" => "zoom",
            "border_radius" => "12",
            "spacing" => "20",
            "photographer_signature" => $this->settings->get_option("photographer_signature", true) ? "true" : "false",
            "enable_interactions" => $this->settings->get_option("enable_interactions", true) ? "true" : "false",
            "auto_confetti" => "true"
        );
        
        $atts = shortcode_atts($defaults, $atts, "soloylibre_gallery");
        
        if (empty($atts["ids"])) {
            // Si no hay IDs, cargar fotos automáticamente
            $auto_load_count = $this->settings->get_option("auto_load_photos", 300);
            $photos = get_posts(array(
                "post_type" => "attachment",
                "post_mime_type" => "image",
                "posts_per_page" => min($auto_load_count, 50), // Límite para shortcode
                "post_status" => "inherit",
                "orderby" => "date",
                "order" => "DESC"
            ));
            
            if (empty($photos)) {
                return "<p style=\"color: #dc3545; padding: 20px; background: #f8d7da; border-radius: 8px; text-align: center;\">📸 No se encontraron imágenes en la biblioteca de medios.</p>";
            }
            
            $atts["ids"] = implode(",", wp_list_pluck($photos, "ID"));
        }
        
        $image_ids = explode(",", $atts["ids"]);
        $image_ids = array_map("trim", $image_ids);
        $image_ids = array_filter($image_ids, "is_numeric");
        
        if (empty($image_ids)) {
            return "<p style=\"color: #dc3545; padding: 20px; background: #f8d7da; border-radius: 8px; text-align: center;\">❌ Error: IDs de imágenes no válidos.</p>";
        }
        
        $gallery_id = "soloylibre-gallery-" . uniqid();
        $output = "";
        
        // Contenedor principal con datos
        $output .= "<div id=\"$gallery_id\" class=\"soloylibre-gallery soloylibre-style-{$atts["style"]}\" data-confetti=\"{$atts["auto_confetti"]}\" data-interactions=\"{$atts["enable_interactions"]}\">";
        
        // Título de la galería
        $output .= "<div class=\"gallery-header\">";
        $output .= "<h3 class=\"gallery-title\">📸 Galería SoloYLibre</h3>";
        $output .= "<p class=\"gallery-subtitle\">🇩🇴 Jose L Encarnacion (JoseTusabe) - " . count($image_ids) . " fotos</p>";
        $output .= "</div>";
        
        // Grid de imágenes mejorado
        $columns = intval($atts["columns"]);
        $spacing = intval($atts["spacing"]);
        $border_radius = intval($atts["border_radius"]);
        
        $output .= "<div class=\"gallery-grid enhanced-grid\" style=\"grid-template-columns: repeat($columns, 1fr); gap: {$spacing}px;\">";
        
        foreach ($image_ids as $index => $image_id) {
            $image_id = intval($image_id);
            $image = wp_get_attachment_image_src($image_id, $atts["size"]);
            $full_image = wp_get_attachment_image_src($image_id, "full");
            $metadata = wp_get_attachment_metadata($image_id);
            $title = get_the_title($image_id) ?: "Foto #$image_id";
            $caption = wp_get_attachment_caption($image_id);
            $alt = get_post_meta($image_id, "_wp_attachment_image_alt", true) ?: $title;
            
            if (!$image) continue;
            
            // Obtener estadísticas de interacción
            $interactions = $this->get_photo_interactions($image_id);
            
            $output .= "<div class=\"gallery-item enhanced-item hover-{$atts["hover_effect"]}\" style=\"border-radius: {$border_radius}px;\" data-photo-id=\"$image_id\" data-index=\"$index\">";
            
            // Imagen principal
            if ($atts["lightbox"] === "true" && $full_image) {
                $output .= "<a href=\"{$full_image[0]}\" class=\"gallery-link enhanced-link\" data-lightbox=\"gallery-$gallery_id\" data-title=\"" . esc_attr($title) . "\">";
            } else {
                $output .= "<div class=\"gallery-link enhanced-link\">";
            }
            
            $output .= "<img src=\"{$image[0]}\" alt=\"" . esc_attr($alt) . "\" title=\"" . esc_attr($title) . "\" loading=\"lazy\" class=\"gallery-image\">";
            
            // Overlay mejorado con información
            $output .= "<div class=\"gallery-overlay enhanced-overlay\">";
            
            if ($atts["show_title"] === "true") {
                $output .= "<h4 class=\"image-title\">$title</h4>";
            }
            
            if ($atts["show_caption"] === "true" && !empty($caption)) {
                $output .= "<p class=\"image-caption\">$caption</p>";
            }
            
            if ($atts["show_metadata"] === "true" && $metadata) {
                $output .= "<div class=\"image-metadata\">";
                if (isset($metadata["width"]) && isset($metadata["height"])) {
                    $output .= "<span class=\"meta-item\">📐 {$metadata["width"]} × {$metadata["height"]}</span>";
                }
                if (isset($metadata["filesize"])) {
                    $size_mb = round($metadata["filesize"] / 1024 / 1024, 2);
                    $output .= "<span class=\"meta-item\">💾 {$size_mb} MB</span>";
                }
                $output .= "</div>";
            }
            
            $output .= "</div>";
            
            if ($atts["lightbox"] === "true") {
                $output .= "</a>";
            } else {
                $output .= "</div>";
            }
            
            // Panel de interacciones
            if ($atts["enable_interactions"] === "true") {
                $output .= "<div class=\"gallery-interactions\">";
                $output .= "<button class=\"interaction-btn like-btn\" data-action=\"like\" data-photo-id=\"$image_id\">";
                $output .= "<span class=\"icon\">❤️</span>";
                $output .= "<span class=\"count\">{$interactions["likes"]}</span>";
                $output .= "</button>";
                
                $output .= "<button class=\"interaction-btn view-btn\" data-action=\"view\" data-photo-id=\"$image_id\">";
                $output .= "<span class=\"icon\">👁️</span>";
                $output .= "<span class=\"count\">{$interactions["views"]}</span>";
                $output .= "</button>";
                
                $output .= "<button class=\"interaction-btn share-btn\" data-action=\"share\" data-photo-id=\"$image_id\">";
                $output .= "<span class=\"icon\">📤</span>";
                $output .= "<span class=\"count\">{$interactions["shares"]}</span>";
                $output .= "</button>";
                $output .= "</div>";
            }
            
            $output .= "</div>";
        }
        
        $output .= "</div>"; // Cierre gallery-grid
        
        // Estadísticas de la galería
        if ($atts["enable_interactions"] === "true") {
            $total_stats = $this->get_gallery_stats($image_ids);
            $output .= "<div class=\"gallery-stats\">";
            $output .= "<div class=\"stat-item\"><span class=\"icon\">❤️</span> {$total_stats["total_likes"]} likes</div>";
            $output .= "<div class=\"stat-item\"><span class=\"icon\">👁️</span> {$total_stats["total_views"]} vistas</div>";
            $output .= "<div class=\"stat-item\"><span class=\"icon\">📤</span> {$total_stats["total_shares"]} compartidas</div>";
            $output .= "</div>";
        }
        
        // Firma del fotógrafo mejorada
        if ($atts["photographer_signature"] === "true") {
            $output .= "<div class=\"photographer-signature enhanced-signature\">";
            $output .= "<div class=\"signature-content\">";
            $output .= "<div class=\"photographer-info\">";
            $output .= "<h4>📸 Jose L Encarnacion (JoseTusabe)</h4>";
            $output .= "<p class=\"brand\">SoloYLibre Photography</p>";
            $output .= "<p class=\"location\">📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
            $output .= "</div>";
            $output .= "<div class=\"contact-info\">";
            $output .= "<p>📞 718-713-5500 | 📧 <EMAIL></p>";
            $output .= "<div class=\"websites\">";
            $output .= "<a href=\"https://josetusabe.com\" target=\"_blank\">🌐 josetusabe.com</a>";
            $output .= "<a href=\"https://soloylibre.com\" target=\"_blank\">🌐 soloylibre.com</a>";
            $output .= "<a href=\"https://1and1photo.com\" target=\"_blank\">📸 1and1photo.com</a>";
            $output .= "</div>";
            $output .= "</div>";
            $output .= "</div>";
            $output .= "</div>";
        }
        
        $output .= "</div>"; // Cierre contenedor principal
        
        return $output;
    }
    
    private function get_photo_interactions($photo_id) {
        global $wpdb;
        
        $interactions = array(
            "likes" => 0,
            "views" => 0,
            "shares" => 0
        );
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT interaction_type, SUM(interaction_count) as total 
             FROM {$wpdb->prefix}soloylibre_interactions 
             WHERE photo_id = %d 
             GROUP BY interaction_type",
            $photo_id
        ));
        
        foreach ($results as $result) {
            if (isset($interactions[$result->interaction_type])) {
                $interactions[$result->interaction_type] = intval($result->total);
            }
        }
        
        return $interactions;
    }
    
    private function get_gallery_stats($image_ids) {
        global $wpdb;
        
        $ids_placeholder = implode(",", array_fill(0, count($image_ids), "%d"));
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT interaction_type, SUM(interaction_count) as total 
             FROM {$wpdb->prefix}soloylibre_interactions 
             WHERE photo_id IN ($ids_placeholder) 
             GROUP BY interaction_type",
            ...$image_ids
        ));
        
        $stats = array(
            "total_likes" => 0,
            "total_views" => 0,
            "total_shares" => 0
        );
        
        foreach ($results as $result) {
            $key = "total_" . $result->interaction_type . "s";
            if (isset($stats[$key])) {
                $stats[$key] = intval($result->total);
            }
        }
        
        return $stats;
    }
    
    public function handle_interaction() {
        // Verificar nonce
        if (!wp_verify_nonce($_POST["nonce"], "gallery_interaction")) {
            wp_send_json_error("Nonce inválido");
            return;
        }

        $photo_id = intval($_POST["photo_id"]);
        $action = sanitize_text_field($_POST["action_type"]);

        // Validar datos
        if (!$photo_id || !in_array($action, array("like", "view", "share"))) {
            wp_send_json_error("Datos inválidos: photo_id=$photo_id, action=$action");
            return;
        }

        // Verificar que la foto existe
        $photo_exists = get_post($photo_id);
        if (!$photo_exists || $photo_exists->post_type !== 'attachment') {
            wp_send_json_error("Foto no encontrada");
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . "soloylibre_interactions";

        // Verificar que la tabla existe
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            wp_send_json_error("Tabla de interacciones no existe");
            return;
        }

        // Obtener contador actual
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT interaction_count FROM $table_name
             WHERE photo_id = %d AND interaction_type = %s",
            $photo_id, $action
        ));

        $new_count = 1;

        if ($existing !== null) {
            // Actualizar contador existente
            $new_count = intval($existing) + 1;
            $result = $wpdb->update(
                $table_name,
                array(
                    "interaction_count" => $new_count,
                    "updated_at" => current_time("mysql")
                ),
                array(
                    "photo_id" => $photo_id,
                    "interaction_type" => $action
                ),
                array("%d", "%s"),
                array("%d", "%s")
            );
        } else {
            // Crear nueva entrada
            $result = $wpdb->insert(
                $table_name,
                array(
                    "photo_id" => $photo_id,
                    "interaction_type" => $action,
                    "interaction_count" => 1,
                    "user_ip" => $_SERVER['REMOTE_ADDR'],
                    "user_agent" => $_SERVER['HTTP_USER_AGENT'],
                    "created_at" => current_time("mysql")
                ),
                array("%d", "%s", "%d", "%s", "%s", "%s")
            );
        }

        if ($result === false) {
            wp_send_json_error("Error al guardar en base de datos: " . $wpdb->last_error);
            return;
        }

        // Log para debugging
        error_log("SoloYLibre: Interacción registrada - Photo: $photo_id, Action: $action, Count: $new_count");

        wp_send_json_success(array(
            "new_count" => $new_count,
            "message" => "Interacción registrada exitosamente",
            "photo_id" => $photo_id,
            "action" => $action
        ));
    }

    private function get_enhanced_css() {
        return '
        .soloylibre-gallery {
            margin: 30px 0;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .soloylibre-gallery::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(206,17,38,0.05), transparent);
            animation: shine 4s infinite;
            pointer-events: none;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .gallery-header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .gallery-title {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0 0 10px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .gallery-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
            font-weight: 500;
        }

        .gallery-grid.enhanced-grid {
            display: grid;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .gallery-item.enhanced-item {
            position: relative;
            overflow: hidden;
            background: white;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .gallery-item.enhanced-item:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            z-index: 10;
        }

        .gallery-link.enhanced-link {
            display: block;
            position: relative;
            text-decoration: none;
            overflow: hidden;
        }

        .gallery-image {
            width: 100%;
            height: 300px;
            object-fit: cover;
            transition: all 0.4s ease;
        }

        .hover-zoom .gallery-item:hover .gallery-image {
            transform: scale(1.1);
        }

        .hover-fade .gallery-item:hover .gallery-image {
            opacity: 0.8;
            filter: brightness(1.1);
        }

        .gallery-overlay.enhanced-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.9));
            color: white;
            padding: 20px;
            transform: translateY(100%);
            transition: transform 0.4s ease;
        }

        .gallery-item:hover .gallery-overlay.enhanced-overlay {
            transform: translateY(0);
        }

        .image-title {
            margin: 0 0 8px 0;
            font-size: 1.2rem;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .image-caption {
            margin: 0 0 12px 0;
            font-size: 0.95rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .image-metadata {
            display: flex;
            gap: 15px;
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .meta-item {
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 12px;
            backdrop-filter: blur(5px);
        }

        .gallery-interactions {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.3s ease;
        }

        .gallery-item:hover .gallery-interactions {
            opacity: 1;
            transform: translateX(0);
        }

        .interaction-btn {
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            position: relative;
        }

        .interaction-btn:hover {
            transform: scale(1.1);
            background: rgba(255,255,255,1);
        }

        .interaction-btn .icon {
            font-size: 1.2rem;
        }

        .interaction-btn .count {
            position: absolute;
            bottom: -8px;
            right: -8px;
            background: #CE1126;
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
            font-weight: 600;
        }

        .like-btn:hover {
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
            color: white;
        }

        .view-btn:hover {
            background: linear-gradient(135deg, #4ecdc4, #45b7b8);
            color: white;
        }

        .share-btn:hover {
            background: linear-gradient(135deg, #3742fa, #5352ed);
            color: white;
        }

        .gallery-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 30px 0;
            padding: 20px;
            background: rgba(255,255,255,0.8);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 2;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            color: #495057;
        }

        .stat-item .icon {
            font-size: 1.3rem;
        }

        .photographer-signature.enhanced-signature {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            position: relative;
            overflow: hidden;
            z-index: 2;
        }

        .photographer-signature.enhanced-signature::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
            pointer-events: none;
        }

        .signature-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            position: relative;
            z-index: 1;
        }

        .photographer-info h4 {
            margin: 0 0 10px 0;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .brand {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0 0 8px 0;
            opacity: 0.9;
        }

        .location {
            margin: 0;
            opacity: 0.8;
            font-size: 0.95rem;
        }

        .contact-info p {
            margin: 0 0 15px 0;
            font-size: 1rem;
        }

        .websites {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .websites a {
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.2);
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .websites a:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        /* Estilos específicos por tipo */
        .soloylibre-style-dominican .gallery-item {
            border: 3px solid transparent;
            background: linear-gradient(white, white) padding-box,
                        linear-gradient(135deg, #CE1126, #002D62) border-box;
        }

        .soloylibre-style-dominican .gallery-item:hover {
            border-width: 4px;
        }

        .soloylibre-style-dominican .gallery-title {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Estilo Grid Moderno */
        .soloylibre-style-grid {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .soloylibre-style-grid .gallery-item {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .soloylibre-style-grid .gallery-item:hover {
            transform: translateY(-15px) scale(1.05);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.5);
        }

        .soloylibre-style-grid .gallery-title {
            color: #667eea;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        /* Estilo Masonry */
        .soloylibre-style-masonry {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        }

        .soloylibre-style-masonry .gallery-grid {
            column-count: var(--columns, 3);
            column-gap: 20px;
            display: block;
        }

        .soloylibre-style-masonry .gallery-item {
            break-inside: avoid;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
        }

        .soloylibre-style-masonry .gallery-image {
            height: auto;
            min-height: 200px;
        }

        .soloylibre-style-masonry .gallery-title {
            color: #ff6b9d;
            font-style: italic;
        }

        /* Estilo Carousel */
        .soloylibre-style-carousel {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            overflow-x: hidden;
        }

        .soloylibre-style-carousel .gallery-grid {
            display: flex;
            gap: 25px;
            overflow-x: auto;
            padding: 20px 0;
            scroll-behavior: smooth;
        }

        .soloylibre-style-carousel .gallery-item {
            flex: 0 0 350px;
            border-radius: 25px;
            box-shadow: 0 15px 35px rgba(168, 237, 234, 0.4);
        }

        .soloylibre-style-carousel .gallery-title {
            color: #20bf6b;
            font-weight: 800;
        }

        /* Estilo Profesional */
        .soloylibre-style-professional {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: #ecf0f1;
        }

        .soloylibre-style-professional .gallery-item {
            background: #34495e;
            border: 1px solid #7f8c8d;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .soloylibre-style-professional .gallery-item:hover {
            border-color: #3498db;
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
        }

        .soloylibre-style-professional .gallery-title {
            color: #3498db;
            font-family: "Georgia", serif;
            font-weight: 600;
        }

        .soloylibre-style-professional .gallery-stats {
            background: rgba(52, 73, 94, 0.8);
            color: #ecf0f1;
        }

        /* Responsive mejorado */
        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 15px !important;
            }

            .gallery-title {
                font-size: 2rem;
            }

            .signature-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .gallery-stats {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }

        @media (max-width: 480px) {
            .gallery-grid {
                grid-template-columns: 1fr !important;
            }

            .gallery-title {
                font-size: 1.8rem;
            }

            .soloylibre-gallery {
                padding: 20px;
            }

            .gallery-image {
                height: 250px;
            }
        }

        /* Animaciones de entrada */
        .gallery-item {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Delay escalonado para items */
        .gallery-item:nth-child(1) { animation-delay: 0.1s; }
        .gallery-item:nth-child(2) { animation-delay: 0.2s; }
        .gallery-item:nth-child(3) { animation-delay: 0.3s; }
        .gallery-item:nth-child(4) { animation-delay: 0.4s; }
        .gallery-item:nth-child(5) { animation-delay: 0.5s; }
        .gallery-item:nth-child(6) { animation-delay: 0.6s; }
        .gallery-item:nth-child(n+7) { animation-delay: 0.7s; }
        ';
    }

    private function get_enhanced_js() {
        return "
        jQuery(document).ready(function($) {
            // Lightbox mejorado
            $('.soloylibre-gallery a[data-lightbox]').click(function(e) {
                e.preventDefault();

                var src = $(this).attr('href');
                var title = $(this).data('title') || '';
                var gallery = $(this).closest('.soloylibre-gallery');

                // Crear lightbox mejorado
                var lightbox = $('<div>').addClass('soloylibre-lightbox-enhanced').css({
                    'position': 'fixed',
                    'top': '0',
                    'left': '0',
                    'width': '100%',
                    'height': '100%',
                    'background': 'rgba(0,0,0,0.95)',
                    'z-index': '99999',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center',
                    'cursor': 'pointer',
                    'opacity': '0',
                    'backdrop-filter': 'blur(5px)'
                });

                var container = $('<div>').css({
                    'position': 'relative',
                    'max-width': '90%',
                    'max-height': '90%',
                    'display': 'flex',
                    'flex-direction': 'column',
                    'align-items': 'center'
                });

                var img = $('<img>').attr('src', src).attr('alt', title).css({
                    'max-width': '100%',
                    'max-height': '80vh',
                    'object-fit': 'contain',
                    'border-radius': '10px',
                    'box-shadow': '0 20px 60px rgba(0,0,0,0.5)'
                });

                var titleDiv = $('<div>').text(title).css({
                    'color': 'white',
                    'font-size': '1.2rem',
                    'margin-top': '20px',
                    'text-align': 'center',
                    'font-weight': '600'
                });

                var close = $('<span>&times;</span>').css({
                    'position': 'absolute',
                    'top': '20px',
                    'right': '30px',
                    'color': 'white',
                    'font-size': '3rem',
                    'cursor': 'pointer',
                    'z-index': '100000',
                    'background': 'rgba(0,0,0,0.5)',
                    'border-radius': '50%',
                    'width': '60px',
                    'height': '60px',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center',
                    'transition': 'all 0.3s ease'
                });

                close.hover(function() {
                    $(this).css('background', 'rgba(206,17,38,0.8)');
                }, function() {
                    $(this).css('background', 'rgba(0,0,0,0.5)');
                });

                container.append(img).append(titleDiv);
                lightbox.append(container).append(close);
                $('body').append(lightbox);

                // Animación de entrada
                lightbox.animate({'opacity': '1'}, 300);

                // Confetti si está habilitado
                if (gallery.data('confetti') === 'true' && typeof confetti !== 'undefined') {
                    setTimeout(function() {
                        confetti({
                            particleCount: 30,
                            spread: 50,
                            origin: { y: 0.8 },
                            colors: ['#CE1126', '#002D62', '#FFD700']
                        });
                    }, 500);
                }

                // Cerrar lightbox
                lightbox.click(function() {
                    lightbox.animate({'opacity': '0'}, 300, function() {
                        lightbox.remove();
                    });
                });

                img.click(function(e) {
                    e.stopPropagation();
                });

                container.click(function(e) {
                    e.stopPropagation();
                });
            });

            // Manejo de interacciones (usando delegación de eventos)
            $(document).on('click', '.interaction-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var btn = $(this);
                var photoId = btn.data('photo-id');
                var action = btn.data('action');
                var countSpan = btn.find('.count');

                // Animación del botón
                btn.addClass('soloylibre-success-animation');
                setTimeout(function() {
                    btn.removeClass('soloylibre-success-animation');
                }, 600);

                // Confetti para likes
                if (action === 'like' && typeof confetti !== 'undefined') {
                    confetti({
                        particleCount: 15,
                        spread: 30,
                        origin: {
                            x: (btn.offset().left + btn.width()/2) / $(window).width(),
                            y: (btn.offset().top + btn.height()/2) / $(window).height()
                        },
                        colors: ['#ff6b6b', '#ff8e8e', '#ffb3b3']
                    });
                }

                // Debug info
                console.log('SoloYLibre: Registrando interacción', {
                    photoId: photoId,
                    action: action,
                    ajaxUrl: soloylibre_ajax.ajax_url,
                    nonce: soloylibre_ajax.nonce
                });

                // AJAX request
                $.post(soloylibre_ajax.ajax_url, {
                    action: 'gallery_interaction',
                    photo_id: photoId,
                    action_type: action,
                    nonce: soloylibre_ajax.nonce
                }, function(response) {
                    console.log('SoloYLibre: Respuesta AJAX', response);

                    if (response.success) {
                        countSpan.text(response.data.new_count);

                        // Mostrar mensaje de éxito
                        var successMsg = $('<div class=\"interaction-success\">✅ ' + response.data.message + '</div>');
                        successMsg.css({
                            'position': 'fixed',
                            'top': '20px',
                            'right': '20px',
                            'background': '#28a745',
                            'color': 'white',
                            'padding': '10px 15px',
                            'border-radius': '5px',
                            'z-index': '9999',
                            'font-size': '14px'
                        });
                        $('body').append(successMsg);
                        setTimeout(function() {
                            successMsg.fadeOut(function() {
                                successMsg.remove();
                            });
                        }, 2000);

                        // Actualizar estadísticas de la galería
                        updateGalleryStats();
                    } else {
                        console.error('SoloYLibre: Error en interacción', response);
                        alert('❌ Error: ' + (response.data ? response.data : 'Error desconocido'));
                    }
                }).fail(function(xhr, status, error) {
                    console.error('SoloYLibre: Error AJAX', {xhr: xhr, status: status, error: error});
                    alert('❌ Error de conexión: ' + error);
                });
            });

            // Función para actualizar estadísticas
            function updateGalleryStats() {
                $('.gallery-stats').each(function() {
                    var statsContainer = $(this);
                    var gallery = statsContainer.closest('.soloylibre-gallery');

                    // Recalcular totales
                    var totalLikes = 0;
                    var totalViews = 0;
                    var totalShares = 0;

                    gallery.find('.like-btn .count').each(function() {
                        totalLikes += parseInt($(this).text()) || 0;
                    });

                    gallery.find('.view-btn .count').each(function() {
                        totalViews += parseInt($(this).text()) || 0;
                    });

                    gallery.find('.share-btn .count').each(function() {
                        totalShares += parseInt($(this).text()) || 0;
                    });

                    // Actualizar display
                    statsContainer.find('.stat-item').eq(0).html('<span class=\"icon\">❤️</span> ' + totalLikes + ' likes');
                    statsContainer.find('.stat-item').eq(1).html('<span class=\"icon\">👁️</span> ' + totalViews + ' vistas');
                    statsContainer.find('.stat-item').eq(2).html('<span class=\"icon\">📤</span> ' + totalShares + ' compartidas');
                });
            }

            // Registrar vista automáticamente
            $('.gallery-item').each(function() {
                var item = $(this);
                var photoId = item.data('photo-id');

                if (photoId) {
                    var observer = new IntersectionObserver(function(entries) {
                        entries.forEach(function(entry) {
                            if (entry.isIntersecting) {
                                // Registrar vista
                                $.post(soloylibre_ajax.ajax_url, {
                                    action: 'gallery_interaction',
                                    photo_id: photoId,
                                    action_type: 'view',
                                    nonce: soloylibre_ajax.nonce
                                }, function(response) {
                                    if (response.success) {
                                        item.find('.view-btn .count').text(response.data.new_count);
                                        updateGalleryStats();
                                    }
                                });

                                observer.unobserve(entry.target);
                            }
                        });
                    }, { threshold: 0.5 });

                    observer.observe(this);
                }
            });

            // Efecto parallax sutil en el fondo
            $(window).scroll(function() {
                var scrolled = $(this).scrollTop();
                $('.soloylibre-gallery').each(function() {
                    var rate = scrolled * -0.5;
                    $(this).css('background-position', 'center ' + rate + 'px');
                });
            });
        });
        ";
    }
}

// Reemplazar el shortcode handler anterior
if (class_exists("SoloYLibre_Shortcode_Handler")) {
    // Remover shortcode anterior
    remove_shortcode("soloylibre_gallery");
}

new SoloYLibre_Enhanced_Shortcode();
?>