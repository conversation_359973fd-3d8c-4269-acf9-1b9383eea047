<?php
/**
 * Bulk Photo Manager - Full Width Page
 * Developed by JEYKO AI for Jose <PERSON> (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

if (!defined('ABSPATH')) {
    exit;
}

class SoloYLibre_Bulk_Photo_Manager {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // AJAX endpoints
        add_action('wp_ajax_bulk_load_photos', array($this, 'ajax_load_photos'));
        add_action('wp_ajax_bulk_select_all', array($this, 'ajax_select_all'));
        add_action('wp_ajax_bulk_mark_private', array($this, 'ajax_mark_private'));
        add_action('wp_ajax_bulk_mark_unwanted', array($this, 'ajax_mark_unwanted'));
        add_action('wp_ajax_bulk_create_albums', array($this, 'ajax_create_albums'));
        add_action('wp_ajax_bulk_publish_batch', array($this, 'ajax_publish_batch'));
        add_action('wp_ajax_bulk_organize_dates', array($this, 'ajax_organize_dates'));
        add_action('wp_ajax_bulk_get_stats', array($this, 'ajax_get_stats'));
        add_action('wp_ajax_bulk_test_connection', array($this, 'ajax_test_connection'));
    }
    
    public function add_admin_menu() {
        add_submenu_page(
            'soloylibre-gallery',
            '📁 Gestor de Fotos Masivo',
            '📁 Gestor Masivo',
            'manage_options',
            'soloylibre-bulk-manager',
            array($this, 'render_page')
        );
    }
    
    public function enqueue_scripts($hook) {
        if ($hook !== 'soloylibre-gallery_page_soloylibre-bulk-manager') {
            return;
        }
        
        wp_enqueue_script('jquery');
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_script('jquery-ui-selectable');
        
        wp_localize_script('jquery', 'bulkManager', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('bulk_manager_nonce')
        ));
    }
    
    public function render_page() {
        ?>
        <div class="wrap bulk-photo-manager-page">
            <style>
            .bulk-photo-manager-page {
                margin: 0 !important;
                padding: 0 !important;
                max-width: none !important;
                width: 100vw;
                margin-left: -20px;
                background: #f1f1f1;
                min-height: 100vh;
            }
            
            .bulk-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px 30px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .bulk-header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 300;
            }
            
            .bulk-header .subtitle {
                margin: 5px 0 0 0;
                opacity: 0.9;
                font-size: 16px;
            }
            
            .bulk-controls {
                background: white;
                padding: 20px 30px;
                border-bottom: 1px solid #ddd;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 15px;
            }
            
            .bulk-actions {
                display: flex;
                gap: 10px;
                flex-wrap: wrap;
            }
            
            .bulk-action-btn {
                background: #0073aa;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 5px;
                transition: all 0.3s ease;
            }
            
            .bulk-action-btn:hover {
                background: #005a87;
                transform: translateY(-1px);
            }
            
            .bulk-action-btn.secondary {
                background: #666;
            }
            
            .bulk-action-btn.secondary:hover {
                background: #444;
            }
            
            .bulk-action-btn.danger {
                background: #dc3545;
            }
            
            .bulk-action-btn.danger:hover {
                background: #c82333;
            }
            
            .bulk-action-btn.success {
                background: #28a745;
            }
            
            .bulk-action-btn.success:hover {
                background: #218838;
            }
            
            .bulk-stats {
                display: flex;
                gap: 20px;
                font-size: 14px;
            }
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 5px;
                padding: 5px 10px;
                background: #f8f9fa;
                border-radius: 3px;
            }
            
            .bulk-content {
                padding: 20px 30px;
                display: grid;
                grid-template-columns: 250px 1fr;
                gap: 20px;
                min-height: calc(100vh - 200px);
            }
            
            .bulk-sidebar {
                background: white;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                height: fit-content;
            }
            
            .bulk-main {
                background: white;
                border-radius: 8px;
                padding: 20px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }
            
            .filter-section {
                margin-bottom: 25px;
            }
            
            .filter-section h3 {
                margin: 0 0 10px 0;
                font-size: 16px;
                color: #333;
            }
            
            .filter-options {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .filter-option {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px;
                border-radius: 4px;
                cursor: pointer;
                transition: background 0.2s;
            }
            
            .filter-option:hover {
                background: #f8f9fa;
            }
            
            .filter-option input[type="checkbox"] {
                margin: 0;
            }
            
            .photos-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 15px;
                max-height: 70vh;
                overflow-y: auto;
                padding: 10px;
                border: 2px dashed #ddd;
                border-radius: 8px;
            }
            
            .photo-item {
                position: relative;
                border-radius: 8px;
                overflow: hidden;
                cursor: pointer;
                transition: all 0.3s ease;
                background: #f8f9fa;
            }
            
            .photo-item:hover {
                transform: scale(1.05);
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            }
            
            .photo-item.selected {
                border: 3px solid #0073aa;
                transform: scale(1.02);
            }
            
            .photo-item img {
                width: 100%;
                height: 120px;
                object-fit: cover;
                display: block;
            }
            
            .photo-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.7);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s;
            }
            
            .photo-item:hover .photo-overlay {
                opacity: 1;
            }
            
            .photo-checkbox {
                position: absolute;
                top: 5px;
                left: 5px;
                z-index: 10;
                transform: scale(1.2);
            }
            
            .photo-info {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0,0,0,0.8));
                color: white;
                padding: 10px 8px 5px;
                font-size: 11px;
                line-height: 1.2;
            }
            
            .loading-spinner {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 200px;
                font-size: 18px;
                color: #666;
            }
            
            .selection-info {
                background: #e3f2fd;
                border: 1px solid #2196f3;
                border-radius: 5px;
                padding: 15px;
                margin-bottom: 20px;
                display: none;
            }
            
            .selection-info.active {
                display: block;
            }
            
            @media (max-width: 1200px) {
                .bulk-content {
                    grid-template-columns: 1fr;
                }
                
                .bulk-sidebar {
                    order: 2;
                }
            }
            
            @media (max-width: 768px) {
                .bulk-controls {
                    flex-direction: column;
                    align-items: stretch;
                }
                
                .bulk-actions {
                    justify-content: center;
                }
                
                .photos-grid {
                    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                    gap: 10px;
                }
            }

            /* Notification styles */
            .bulk-notification {
                padding: 12px 20px;
                margin: 10px 0;
                border-radius: 8px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: 500;
                animation: slideInDown 0.3s ease;
                position: relative;
                z-index: 1000;
            }

            .bulk-notification-success {
                background: rgba(40, 167, 69, 0.9);
                border-left: 4px solid #28a745;
                color: white;
            }

            .bulk-notification-error {
                background: rgba(220, 53, 69, 0.9);
                border-left: 4px solid #dc3545;
                color: white;
            }

            .bulk-notification-info {
                background: rgba(0, 123, 255, 0.9);
                border-left: 4px solid #007bff;
                color: white;
            }

            .notification-close {
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                margin-left: 15px;
                opacity: 0.8;
                transition: opacity 0.3s ease;
            }

            .notification-close:hover {
                opacity: 1;
            }

            @keyframes slideInDown {
                from {
                    transform: translateY(-20px);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }
            </style>
            
            <div class="bulk-header">
                <h1>📁 Gestor de Fotos Masivo</h1>
                <p class="subtitle">🇩🇴 JoseTusabe Photography - Organiza y gestiona múltiples fotos a la vez</p>
            </div>
            
            <div class="bulk-controls">
                <div class="bulk-actions">
                    <button class="bulk-action-btn" id="select-all-btn">
                        <span>✅</span> Seleccionar Todas
                    </button>
                    <button class="bulk-action-btn secondary" id="deselect-all-btn">
                        <span>❌</span> Deseleccionar
                    </button>
                    <button class="bulk-action-btn" id="mark-private-btn" disabled>
                        <span>🔒</span> Marcar Privadas
                    </button>
                    <button class="bulk-action-btn secondary" id="mark-unwanted-btn" disabled>
                        <span>❌</span> No Deseadas
                    </button>
                    <button class="bulk-action-btn success" id="create-albums-btn" disabled>
                        <span>📚</span> Crear Álbumes
                    </button>
                    <button class="bulk-action-btn success" id="publish-batch-btn" disabled>
                        <span>🚀</span> Publicar Lote
                    </button>
                    <button class="bulk-action-btn" id="organize-dates-btn" disabled>
                        <span>📅</span> Organizar por Fechas
                    </button>
                </div>
                
                <div class="bulk-stats">
                    <div class="stat-item">
                        <span>📸</span>
                        <span id="total-photos">0</span> Total
                    </div>
                    <div class="stat-item">
                        <span>✅</span>
                        <span id="selected-photos">0</span> Seleccionadas
                    </div>
                    <div class="stat-item">
                        <span>🆕</span>
                        <span id="available-photos">0</span> Disponibles
                    </div>
                </div>
            </div>
            
            <div class="selection-info" id="selection-info">
                <h4>📋 Información de Selección</h4>
                <p id="selection-details">No hay fotos seleccionadas</p>
            </div>
            
            <div class="bulk-content">
                <div class="bulk-sidebar">
                    <div class="filter-section">
                        <h3>🔍 Filtros</h3>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="checkbox" id="filter-all" checked>
                                <span>📷 Todas las fotos</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" id="filter-available">
                                <span>🆕 Solo disponibles</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" id="filter-published">
                                <span>✅ Ya publicadas</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" id="filter-private">
                                <span>🔒 Privadas</span>
                            </label>
                            <label class="filter-option">
                                <input type="checkbox" id="filter-unwanted">
                                <span>❌ No deseadas</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="filter-section">
                        <h3>📅 Ordenar por</h3>
                        <div class="filter-options">
                            <label class="filter-option">
                                <input type="radio" name="sort" value="date_desc" checked>
                                <span>📅 Más recientes</span>
                            </label>
                            <label class="filter-option">
                                <input type="radio" name="sort" value="date_asc">
                                <span>📅 Más antiguas</span>
                            </label>
                            <label class="filter-option">
                                <input type="radio" name="sort" value="name_asc">
                                <span>🔤 Nombre A-Z</span>
                            </label>
                            <label class="filter-option">
                                <input type="radio" name="sort" value="size_desc">
                                <span>📏 Tamaño mayor</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="filter-section">
                        <h3>⚙️ Acciones Rápidas</h3>
                        <div class="filter-options">
                            <button class="bulk-action-btn" style="width: 100%; margin-bottom: 8px;" id="refresh-photos">
                                <span>🔄</span> Recargar Fotos
                            </button>
                            <button class="bulk-action-btn secondary" style="width: 100%; margin-bottom: 8px;" id="clear-selection">
                                <span>🧹</span> Limpiar Selección
                            </button>
                            <button class="bulk-action-btn success" style="width: 100%;" id="auto-select-recent">
                                <span>⚡</span> Seleccionar 50 Recientes
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="bulk-main">
                    <div id="photos-container">
                        <div class="loading-spinner">
                            <span>📸 Cargando fotos...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            let selectedPhotos = [];
            let allPhotos = [];
            let currentFilter = 'all';
            let currentSort = 'date_desc';
            
            // Test connection first
            testConnection();

            // Load initial photos
            loadPhotos();
            loadStats();
            
            // Event listeners
            $('#select-all-btn').click(selectAllPhotos);
            $('#deselect-all-btn').click(deselectAllPhotos);
            $('#mark-private-btn').click(markAsPrivate);
            $('#mark-unwanted-btn').click(markAsUnwanted);
            $('#create-albums-btn').click(createAlbums);
            $('#publish-batch-btn').click(publishBatch);
            $('#organize-dates-btn').click(organizeDates);
            $('#refresh-photos').click(loadPhotos);
            $('#clear-selection').click(clearSelection);
            $('#auto-select-recent').click(autoSelectRecent);
            
            // Filter listeners
            $('input[name="sort"]').change(function() {
                currentSort = $(this).val();
                loadPhotos();
            });
            
            $('.filter-options input[type="checkbox"]').change(function() {
                if (this.id === 'filter-all' && this.checked) {
                    $('.filter-options input[type="checkbox"]').not(this).prop('checked', false);
                    currentFilter = 'all';
                } else if (this.checked) {
                    $('#filter-all').prop('checked', false);
                    currentFilter = this.id.replace('filter-', '');
                }
                loadPhotos();
            });

            function testConnection() {
                console.log('Testing AJAX connection...');
                $.post(bulkManager.ajaxurl, {
                    action: 'bulk_test_connection',
                    nonce: bulkManager.nonce
                }, function(response) {
                    console.log('Connection test result:', response);
                    if (!response.success) {
                        $('#photos-container').html('<div class="loading-spinner"><span>❌ Error de conexión AJAX</span></div>');
                    }
                }).fail(function(xhr, status, error) {
                    console.error('AJAX connection failed:', status, error);
                    $('#photos-container').html('<div class="loading-spinner"><span>❌ Error de conexión: ' + error + '</span></div>');
                });
            }

            function loadPhotos() {
                $('#photos-container').html('<div class="loading-spinner"><span>📸 Cargando fotos...</span></div>');

                console.log('Loading photos with filter:', currentFilter, 'sort:', currentSort);

                $.post(bulkManager.ajaxurl, {
                    action: 'bulk_load_photos',
                    nonce: bulkManager.nonce,
                    filter: currentFilter,
                    sort: currentSort,
                    limit: 200
                }, function(response) {
                    console.log('Load photos response:', response);
                    if (response.success) {
                        allPhotos = response.data;
                        renderPhotos(allPhotos);
                        updateStats();
                    } else {
                        console.error('Load photos error:', response);
                        $('#photos-container').html('<div class="loading-spinner"><span>❌ Error: ' + (response.data || 'Error desconocido') + '</span></div>');
                    }
                }).fail(function(xhr, status, error) {
                    console.error('AJAX load photos failed:', status, error, xhr.responseText);
                    $('#photos-container').html('<div class="loading-spinner"><span>❌ Error AJAX: ' + error + '</span></div>');
                });
            }
            
            function renderPhotos(photos) {
                if (photos.length === 0) {
                    $('#photos-container').html('<div class="loading-spinner"><span>📷 No hay fotos disponibles</span></div>');
                    return;
                }
                
                let html = '<div class="photos-grid">';
                photos.forEach(function(photo) {
                    const isSelected = selectedPhotos.includes(photo.id);
                    html += `
                        <div class="photo-item ${isSelected ? 'selected' : ''}" data-photo-id="${photo.id}">
                            <input type="checkbox" class="photo-checkbox" ${isSelected ? 'checked' : ''}>
                            <img src="${photo.thumbnail}" alt="${photo.title}">
                            <div class="photo-overlay">
                                <span>${photo.title}</span>
                            </div>
                            <div class="photo-info">
                                <div>${photo.title}</div>
                                <div>${photo.date}</div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                
                $('#photos-container').html(html);
                
                // Add click handlers
                $('.photo-item').click(function(e) {
                    if (e.target.type !== 'checkbox') {
                        const checkbox = $(this).find('.photo-checkbox');
                        checkbox.prop('checked', !checkbox.prop('checked'));
                        checkbox.trigger('change');
                    }
                });
                
                $('.photo-checkbox').change(function() {
                    const photoId = parseInt($(this).closest('.photo-item').data('photo-id'));
                    const isChecked = $(this).prop('checked');
                    
                    if (isChecked) {
                        if (!selectedPhotos.includes(photoId)) {
                            selectedPhotos.push(photoId);
                        }
                        $(this).closest('.photo-item').addClass('selected');
                    } else {
                        selectedPhotos = selectedPhotos.filter(id => id !== photoId);
                        $(this).closest('.photo-item').removeClass('selected');
                    }
                    
                    updateSelectionUI();
                });
            }
            
            function updateSelectionUI() {
                const count = selectedPhotos.length;
                $('#selected-photos').text(count);
                
                // Enable/disable action buttons
                const hasSelection = count > 0;
                $('#mark-private-btn, #mark-unwanted-btn, #create-albums-btn, #publish-batch-btn, #organize-dates-btn')
                    .prop('disabled', !hasSelection);
                
                // Update selection info
                if (count > 0) {
                    $('#selection-info').addClass('active');
                    $('#selection-details').text(`${count} fotos seleccionadas. Usa los botones de arriba para aplicar acciones.`);
                } else {
                    $('#selection-info').removeClass('active');
                }
            }
            
            function loadStats() {
                $.post(bulkManager.ajaxurl, {
                    action: 'bulk_get_stats',
                    nonce: bulkManager.nonce
                }, function(response) {
                    if (response.success) {
                        $('#total-photos').text(response.data.total);
                        $('#available-photos').text(response.data.available);
                    }
                });
            }
            
            function updateStats() {
                $('#total-photos').text(allPhotos.length);
            }
            
            function selectAllPhotos() {
                // First, try to select all visible photos
                const visibleCheckboxes = $('.photo-checkbox:visible');

                if (visibleCheckboxes.length > 0) {
                    // Select all visible photos
                    visibleCheckboxes.prop('checked', true).trigger('change');

                    // Show success message
                    showNotification(`✅ ${visibleCheckboxes.length} fotos seleccionadas`, 'success');
                } else {
                    // If no photos are visible, try to load and select all via AJAX
                    $.post(bulkManager.ajaxurl, {
                        action: 'bulk_select_all',
                        nonce: bulkManager.nonce,
                        filter: currentFilter
                    }, function(response) {
                        if (response.success) {
                            // Update selectedPhotos array with all photo IDs
                            selectedPhotos = response.data.photo_ids;

                            // Update UI to reflect selection
                            updateSelectionUI();

                            // Show success message
                            showNotification(`✅ ${response.data.message}`, 'success');

                            // Reload photos to show selection
                            loadPhotos();
                        } else {
                            showNotification(`❌ Error: ${response.data}`, 'error');
                        }
                    }).fail(function() {
                        showNotification('❌ Error de conexión al seleccionar todas las fotos', 'error');
                    });
                }
            }
            
            function deselectAllPhotos() {
                $('.photo-checkbox').prop('checked', false).trigger('change');
            }
            
            function clearSelection() {
                selectedPhotos = [];
                $('.photo-item').removeClass('selected');
                $('.photo-checkbox').prop('checked', false);
                updateSelectionUI();
            }
            
            function autoSelectRecent() {
                clearSelection();
                $('.photo-item').slice(0, 50).each(function() {
                    $(this).find('.photo-checkbox').prop('checked', true).trigger('change');
                });
            }

            function showNotification(message, type = 'info') {
                // Remove existing notifications
                $('.bulk-notification').remove();

                // Create notification element
                const notification = $(`
                    <div class="bulk-notification bulk-notification-${type}">
                        ${message}
                        <button class="notification-close">×</button>
                    </div>
                `);

                // Add to page
                $('.bulk-controls').prepend(notification);

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    notification.fadeOut(() => notification.remove());
                }, 5000);

                // Close button functionality
                notification.find('.notification-close').click(() => {
                    notification.fadeOut(() => notification.remove());
                });
            }
            
            function markAsPrivate() {
                if (selectedPhotos.length === 0) return;
                
                if (confirm(`¿Marcar ${selectedPhotos.length} fotos como privadas?`)) {
                    performBulkAction('mark_private', 'Marcando como privadas...');
                }
            }
            
            function markAsUnwanted() {
                if (selectedPhotos.length === 0) return;
                
                if (confirm(`¿Marcar ${selectedPhotos.length} fotos como no deseadas?`)) {
                    performBulkAction('mark_unwanted', 'Marcando como no deseadas...');
                }
            }
            
            function createAlbums() {
                if (selectedPhotos.length === 0) return;
                
                const albumsCount = Math.ceil(selectedPhotos.length / 20);
                if (confirm(`¿Crear ${albumsCount} álbumes con ${selectedPhotos.length} fotos?`)) {
                    performBulkAction('create_albums', 'Creando álbumes...');
                }
            }
            
            function publishBatch() {
                if (selectedPhotos.length === 0) return;
                
                if (confirm(`¿Publicar ${selectedPhotos.length} fotos en lotes?`)) {
                    performBulkAction('publish_batch', 'Publicando en lotes...');
                }
            }
            
            function organizeDates() {
                if (selectedPhotos.length === 0) return;
                
                if (confirm(`¿Organizar ${selectedPhotos.length} fotos por fechas?`)) {
                    performBulkAction('organize_dates', 'Organizando por fechas...');
                }
            }
            
            function performBulkAction(action, message) {
                // Show loading state
                const originalText = $(`#${action.replace('_', '-')}-btn`).html();
                $(`#${action.replace('_', '-')}-btn`).html(`<span>⏳</span> ${message}`).prop('disabled', true);
                
                $.post(bulkManager.ajaxurl, {
                    action: `bulk_${action}`,
                    nonce: bulkManager.nonce,
                    photo_ids: selectedPhotos
                }, function(response) {
                    if (response.success) {
                        alert(`✅ ${response.data.message}`);
                        clearSelection();
                        loadPhotos();
                        loadStats();
                    } else {
                        alert(`❌ Error: ${response.data}`);
                    }
                }).always(function() {
                    $(`#${action.replace('_', '-')}-btn`).html(originalText).prop('disabled', false);
                });
            }
        });
        </script>
        <?php
    }

    /**
     * AJAX: Load photos for bulk management
     */
    public function ajax_load_photos() {
        // More flexible nonce check
        if (!wp_verify_nonce($_POST['nonce'], 'bulk_manager_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        $filter = isset($_POST['filter']) ? sanitize_text_field($_POST['filter']) : 'all';
        $sort = isset($_POST['sort']) ? sanitize_text_field($_POST['sort']) : 'date_desc';
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 100;

        // Get photos based on filter
        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => $limit,
            'post_status' => 'inherit'
        );

        // Apply sorting
        switch ($sort) {
            case 'date_asc':
                $args['orderby'] = 'date';
                $args['order'] = 'ASC';
                break;
            case 'name_asc':
                $args['orderby'] = 'title';
                $args['order'] = 'ASC';
                break;
            case 'size_desc':
                $args['meta_key'] = '_wp_attachment_metadata';
                $args['orderby'] = 'meta_value';
                $args['order'] = 'DESC';
                break;
            default: // date_desc
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
        }

        // Apply filters
        if ($filter !== 'all') {
            $published_photos = get_option('soloylibre_published_photos', array());
            $private_photos = get_option('soloylibre_private_photos', array());
            $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

            switch ($filter) {
                case 'available':
                    $exclude_ids = array_merge($published_photos, $private_photos, $unwanted_photos);
                    if (!empty($exclude_ids)) {
                        $args['exclude'] = $exclude_ids;
                    }
                    break;
                case 'published':
                    if (!empty($published_photos)) {
                        $args['include'] = $published_photos;
                    } else {
                        wp_send_json_success(array());
                        return;
                    }
                    break;
                case 'private':
                    if (!empty($private_photos)) {
                        $args['include'] = $private_photos;
                    } else {
                        wp_send_json_success(array());
                        return;
                    }
                    break;
                case 'unwanted':
                    if (!empty($unwanted_photos)) {
                        $args['include'] = $unwanted_photos;
                    } else {
                        wp_send_json_success(array());
                        return;
                    }
                    break;
            }
        }

        try {
            $photos = get_posts($args);
            $photo_data = array();

            if (empty($photos)) {
                wp_send_json_success(array());
                return;
            }

            foreach ($photos as $photo) {
                $thumbnail = wp_get_attachment_image_url($photo->ID, 'thumbnail');
                if (!$thumbnail) {
                    $thumbnail = wp_get_attachment_image_url($photo->ID, 'medium');
                }
                if (!$thumbnail) {
                    $thumbnail = wp_get_attachment_url($photo->ID);
                }

                $photo_data[] = array(
                    'id' => $photo->ID,
                    'title' => get_the_title($photo->ID) ?: 'Sin título',
                    'thumbnail' => $thumbnail ?: '',
                    'full_url' => wp_get_attachment_image_url($photo->ID, 'full') ?: wp_get_attachment_url($photo->ID),
                    'date' => get_the_date('d/m/Y', $photo->ID) ?: date('d/m/Y'),
                    'size' => $this->get_file_size($photo->ID)
                );
            }

            wp_send_json_success($photo_data);
        } catch (Exception $e) {
            wp_send_json_error('Error loading photos: ' . $e->getMessage());
        }
    }

    /**
     * AJAX: Get bulk management statistics
     */
    public function ajax_get_stats() {
        check_ajax_referer('bulk_manager_nonce', 'nonce');

        $total_photos = wp_count_posts('attachment');
        $total_images = 0;

        if (isset($total_photos->inherit)) {
            $images = get_posts(array(
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'posts_per_page' => -1,
                'post_status' => 'inherit',
                'fields' => 'ids'
            ));
            $total_images = count($images);
        }

        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        $used_photos = array_merge($published_photos, $private_photos, $unwanted_photos);
        $available = max(0, $total_images - count(array_unique($used_photos)));

        wp_send_json_success(array(
            'total' => $total_images,
            'published' => count($published_photos),
            'private' => count($private_photos),
            'unwanted' => count($unwanted_photos),
            'available' => $available
        ));
    }

    /**
     * AJAX: Mark photos as private
     */
    public function ajax_mark_private() {
        check_ajax_referer('bulk_manager_nonce', 'nonce');

        $photo_ids = array_map('intval', $_POST['photo_ids']);

        if (empty($photo_ids)) {
            wp_send_json_error('No se seleccionaron fotos');
        }

        $private_photos = get_option('soloylibre_private_photos', array());
        $private_photos = array_merge($private_photos, $photo_ids);
        $private_photos = array_unique($private_photos);

        update_option('soloylibre_private_photos', $private_photos);

        wp_send_json_success(array(
            'message' => count($photo_ids) . ' fotos marcadas como privadas exitosamente'
        ));
    }

    /**
     * AJAX: Mark photos as unwanted
     */
    public function ajax_mark_unwanted() {
        check_ajax_referer('bulk_manager_nonce', 'nonce');

        $photo_ids = array_map('intval', $_POST['photo_ids']);

        if (empty($photo_ids)) {
            wp_send_json_error('No se seleccionaron fotos');
        }

        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());
        $unwanted_photos = array_merge($unwanted_photos, $photo_ids);
        $unwanted_photos = array_unique($unwanted_photos);

        update_option('soloylibre_unwanted_photos', $unwanted_photos);

        wp_send_json_success(array(
            'message' => count($photo_ids) . ' fotos marcadas como no deseadas exitosamente'
        ));
    }

    /**
     * AJAX: Create albums from selected photos
     */
    public function ajax_create_albums() {
        check_ajax_referer('bulk_manager_nonce', 'nonce');

        $photo_ids = array_map('intval', $_POST['photo_ids']);

        if (empty($photo_ids)) {
            wp_send_json_error('No se seleccionaron fotos');
        }

        $photos_per_album = 20;
        $albums_created = 0;
        $photo_chunks = array_chunk($photo_ids, $photos_per_album);

        foreach ($photo_chunks as $index => $chunk) {
            $album_number = $index + 1;
            $post_title = 'AutoAlbum-Masivo-' . date('Y-m-d') . '-' . $album_number;

            $post_content = '[soloylibre_gallery ids="' . implode(',', $chunk) . '" style="masonry" columns="4"]';

            $post_data = array(
                'post_title' => $post_title,
                'post_content' => $post_content,
                'post_status' => 'publish',
                'post_type' => 'post',
                'post_author' => get_current_user_id()
            );

            $post_id = wp_insert_post($post_data);

            if (!is_wp_error($post_id)) {
                // Set featured image
                if (!empty($chunk)) {
                    set_post_thumbnail($post_id, $chunk[0]);
                }
                $albums_created++;
            }
        }

        // Update published photos
        $published_photos = get_option('soloylibre_published_photos', array());
        $published_photos = array_merge($published_photos, $photo_ids);
        update_option('soloylibre_published_photos', array_unique($published_photos));

        wp_send_json_success(array(
            'message' => "$albums_created álbumes creados con " . count($photo_ids) . " fotos"
        ));
    }

    /**
     * AJAX: Publish photos in batches
     */
    public function ajax_publish_batch() {
        check_ajax_referer('bulk_manager_nonce', 'nonce');

        $photo_ids = array_map('intval', $_POST['photo_ids']);

        if (empty($photo_ids)) {
            wp_send_json_error('No se seleccionaron fotos');
        }

        $photos_per_post = 15;
        $posts_created = 0;
        $photo_chunks = array_chunk($photo_ids, $photos_per_post);

        foreach ($photo_chunks as $index => $chunk) {
            $batch_number = $index + 1;
            $post_title = 'Lote-' . date('Y-m-d-H-i') . '-' . $batch_number;

            $post_content = '[soloylibre_gallery ids="' . implode(',', $chunk) . '" style="masonry" columns="4"]';

            $post_data = array(
                'post_title' => $post_title,
                'post_content' => $post_content,
                'post_status' => 'publish',
                'post_type' => 'post',
                'post_author' => get_current_user_id()
            );

            $post_id = wp_insert_post($post_data);

            if (!is_wp_error($post_id)) {
                // Set featured image
                if (!empty($chunk)) {
                    set_post_thumbnail($post_id, $chunk[0]);
                }
                $posts_created++;
            }
        }

        // Update published photos
        $published_photos = get_option('soloylibre_published_photos', array());
        $published_photos = array_merge($published_photos, $photo_ids);
        update_option('soloylibre_published_photos', array_unique($published_photos));

        wp_send_json_success(array(
            'message' => "$posts_created posts creados con " . count($photo_ids) . " fotos"
        ));
    }

    /**
     * AJAX: Organize photos by dates
     */
    public function ajax_organize_dates() {
        check_ajax_referer('bulk_manager_nonce', 'nonce');

        $photo_ids = array_map('intval', $_POST['photo_ids']);

        if (empty($photo_ids)) {
            wp_send_json_error('No se seleccionaron fotos');
        }

        // Group photos by month/year
        $photos_by_date = array();

        foreach ($photo_ids as $photo_id) {
            $date = get_the_date('Y-m', $photo_id);
            if (!isset($photos_by_date[$date])) {
                $photos_by_date[$date] = array();
            }
            $photos_by_date[$date][] = $photo_id;
        }

        $albums_created = 0;

        foreach ($photos_by_date as $date => $photos) {
            $date_formatted = date('F Y', strtotime($date . '-01'));
            $post_title = 'Álbum-' . $date_formatted;

            $post_content = '[soloylibre_gallery ids="' . implode(',', $photos) . '" style="masonry" columns="4"]';

            $post_data = array(
                'post_title' => $post_title,
                'post_content' => $post_content,
                'post_status' => 'publish',
                'post_type' => 'post',
                'post_author' => get_current_user_id()
            );

            $post_id = wp_insert_post($post_data);

            if (!is_wp_error($post_id)) {
                // Set featured image
                if (!empty($photos)) {
                    set_post_thumbnail($post_id, $photos[0]);
                }
                $albums_created++;
            }
        }

        // Update published photos
        $published_photos = get_option('soloylibre_published_photos', array());
        $published_photos = array_merge($published_photos, $photo_ids);
        update_option('soloylibre_published_photos', array_unique($published_photos));

        wp_send_json_success(array(
            'message' => "$albums_created álbumes organizados por fechas con " . count($photo_ids) . " fotos"
        ));
    }

    /**
     * Get file size in human readable format
     */
    private function get_file_size($attachment_id) {
        try {
            $file_path = get_attached_file($attachment_id);
            if ($file_path && file_exists($file_path)) {
                $size = filesize($file_path);
                if ($size !== false) {
                    return size_format($size);
                }
            }

            // Fallback: try to get from metadata
            $metadata = wp_get_attachment_metadata($attachment_id);
            if (isset($metadata['filesize'])) {
                return size_format($metadata['filesize']);
            }

            return 'N/A';
        } catch (Exception $e) {
            return 'N/A';
        }
    }

    /**
     * AJAX: Test connection and basic functionality
     */
    public function ajax_test_connection() {
        wp_send_json_success(array(
            'message' => 'Connection working!',
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'photos_count' => wp_count_posts('attachment')->inherit
        ));
    }

    /**
     * AJAX: Select all photos (returns list of all photo IDs)
     */
    public function ajax_select_all() {
        // More flexible nonce check
        if (!wp_verify_nonce($_POST['nonce'], 'bulk_manager_nonce')) {
            wp_send_json_error('Invalid nonce');
            return;
        }

        try {
            // Get all photos based on current filter
            $filter = isset($_POST['filter']) ? sanitize_text_field($_POST['filter']) : 'all';

            $args = array(
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'post_status' => 'inherit',
                'posts_per_page' => -1, // Get all photos
                'fields' => 'ids'
            );

            // Apply filter logic
            $published_photos = get_option('soloylibre_published_photos', array());
            $private_photos = get_option('soloylibre_private_photos', array());
            $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

            switch ($filter) {
                case 'published':
                    if (!empty($published_photos)) {
                        $args['post__in'] = $published_photos;
                    } else {
                        wp_send_json_success(array('photo_ids' => array(), 'count' => 0));
                        return;
                    }
                    break;
                case 'private':
                    if (!empty($private_photos)) {
                        $args['post__in'] = $private_photos;
                    } else {
                        wp_send_json_success(array('photo_ids' => array(), 'count' => 0));
                        return;
                    }
                    break;
                case 'unwanted':
                    if (!empty($unwanted_photos)) {
                        $args['post__in'] = $unwanted_photos;
                    } else {
                        wp_send_json_success(array('photo_ids' => array(), 'count' => 0));
                        return;
                    }
                    break;
                case 'available':
                    $used_photos = array_merge($published_photos, $private_photos, $unwanted_photos);
                    if (!empty($used_photos)) {
                        $args['post__not_in'] = array_unique($used_photos);
                    }
                    break;
                // 'all' case doesn't need additional filtering
            }

            $photo_ids = get_posts($args);

            wp_send_json_success(array(
                'photo_ids' => $photo_ids,
                'count' => count($photo_ids),
                'filter' => $filter,
                'message' => count($photo_ids) . ' fotos seleccionadas'
            ));

        } catch (Exception $e) {
            wp_send_json_error('Error selecting all photos: ' . $e->getMessage());
        }
    }
}

// Initialize the bulk photo manager
new SoloYLibre_Bulk_Photo_Manager();
