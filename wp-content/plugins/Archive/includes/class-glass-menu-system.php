<?php
/**
 * Glass Menu System - SoloYLibre Gallery Plugin
 * Modern glassmorphism design without overlays
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

class SoloYLibre_Glass_Menu_System {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_glass_menus'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_glass_styles'));
        $this->init_ajax_handlers();
    }
    
    /**
     * Add glassmorphism menu pages
     */
    public function add_glass_menus() {
        // Main menu page
        add_menu_page(
            'SoloYLibre Gallery',
            '📸 SoloYLibre',
            'manage_options',
            'soloylibre-dashboard',
            array($this, 'render_dashboard'),
            'dashicons-camera',
            25
        );
        
        // Submenu pages
        add_submenu_page(
            'soloylibre-dashboard',
            'Dashboard',
            '🏠 Dashboard',
            'manage_options',
            'soloylibre-dashboard',
            array($this, 'render_dashboard')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Quick Posts',
            '⚡ Posts Rápidos',
            'manage_options',
            'soloylibre-quick-posts',
            array($this, 'render_quick_posts')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Advanced Posts',
            '🎯 Posts Avanzados',
            'manage_options',
            'soloylibre-advanced-posts',
            array($this, 'render_advanced_posts')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Photo Manager',
            '📁 Gestor de Fotos',
            'manage_options',
            'soloylibre-photo-manager',
            array($this, 'render_photo_manager')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Statistics',
            '📊 Estadísticas',
            'manage_options',
            'soloylibre-stats',
            array($this, 'render_statistics')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Smart Selection',
            '🤖 Selección Inteligente',
            'manage_options',
            'soloylibre-smart-selection',
            array($this, 'render_smart_selection')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Settings',
            '⚙️ Configuraciones',
            'manage_options',
            'soloylibre-glass-settings',
            array($this, 'render_settings')
        );
    }
    
    /**
     * Enqueue glassmorphism styles
     */
    public function enqueue_glass_styles($hook) {
        if (strpos($hook, 'soloylibre') === false) return;
        
        wp_enqueue_script('soloylibre-glass-js', plugin_dir_url(__FILE__) . '../assets/js/glass-system.js', array('jquery'), '1.0.0', true);
        
        wp_localize_script('soloylibre-glass-js', 'soloylibreGlass', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('soloylibre_glass_nonce'),
            'photographer' => array(
                'name' => 'Jose L Encarnacion',
                'alias' => 'JoseTusabe',
                'brand' => 'JoseTusabe Photography',
                'location' => 'San José de Ocoa, Dom. Rep. / USA',
                'phone' => '************',
                'email' => '<EMAIL>'
            )
        ));
    }
    
    /**
     * Initialize AJAX handlers
     */
    private function init_ajax_handlers() {
        add_action('wp_ajax_glass_load_photos', array($this, 'ajax_load_photos'));
        add_action('wp_ajax_glass_create_quick_post', array($this, 'ajax_create_quick_post'));
        add_action('wp_ajax_glass_create_advanced_post', array($this, 'ajax_create_advanced_post'));
        add_action('wp_ajax_glass_get_stats', array($this, 'ajax_get_stats'));
        add_action('wp_ajax_glass_smart_select', array($this, 'ajax_smart_select'));
        add_action('wp_ajax_glass_save_settings', array($this, 'ajax_save_settings'));
        add_action('wp_ajax_glass_bulk_action', array($this, 'ajax_bulk_action'));
    }
    
    /**
     * Get common glass styles
     */
    private function get_glass_styles() {
        return "
        <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .glass-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            color: white;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .glass-card:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-5px);
            box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px 0 rgba(31, 38, 135, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .glass-button.primary {
            background: rgba(0, 123, 255, 0.3);
            border: 1px solid rgba(0, 123, 255, 0.5);
        }
        
        .glass-button.success {
            background: rgba(40, 167, 69, 0.3);
            border: 1px solid rgba(40, 167, 69, 0.5);
        }
        
        .glass-button.warning {
            background: rgba(255, 193, 7, 0.3);
            border: 1px solid rgba(255, 193, 7, 0.5);
        }
        
        .glass-input {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 12px 16px;
            color: white;
            width: 100%;
            margin: 10px 0;
            font-size: 16px;
        }
        
        .glass-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .glass-input:focus {
            outline: none;
            border: 1px solid rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .glass-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .glass-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .glass-header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .glass-header p {
            font-size: 1.2em;
            opacity: 0.9;
            margin: 10px 0;
        }
        
        .glass-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .glass-stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .glass-stat-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }
        
        .glass-stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .glass-stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .glass-photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .glass-photo-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .glass-photo-item:hover {
            transform: scale(1.05);
            background: rgba(255, 255, 255, 0.2);
        }
        
        .glass-photo-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            display: block;
        }
        
        .glass-photo-info {
            padding: 10px;
            font-size: 12px;
            text-align: center;
        }
        
        .glass-loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2em;
        }
        
        .glass-notification {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 15px 20px;
            margin: 15px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            animation: slideInDown 0.3s ease;
        }
        
        .glass-notification.success {
            border-left: 4px solid #28a745;
        }
        
        .glass-notification.error {
            border-left: 4px solid #dc3545;
        }
        
        .glass-notification.info {
            border-left: 4px solid #17a2b8;
        }
        
        @keyframes slideInDown {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        @media (max-width: 768px) {
            .glass-container {
                margin: 10px;
                padding: 20px;
            }
            
            .glass-grid {
                grid-template-columns: 1fr;
            }
            
            .glass-stats {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .glass-photo-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }
        }
        </style>
        ";
    }
    
    /**
     * Render Dashboard
     */
    public function render_dashboard() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>📸 SoloYLibre Dashboard</h1>
                <p>🇩🇴 JoseTusabe Photography - Sistema Profesional de Gestión Fotográfica</p>
            </div>
            
            <div class="glass-stats" id="dashboard-stats">
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="total-photos">-</div>
                    <div class="glass-stat-label">📷 Total Fotos</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="published-photos">-</div>
                    <div class="glass-stat-label">📝 Publicadas</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="private-photos">-</div>
                    <div class="glass-stat-label">🔒 Privadas</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="total-views">-</div>
                    <div class="glass-stat-label">👁️ Visualizaciones</div>
                </div>
            </div>
            
            <div class="glass-grid">
                <div class="glass-card" onclick="location.href='?page=soloylibre-quick-posts'">
                    <h3>⚡ Posts Rápidos</h3>
                    <p>Crea posts automáticamente con selección inteligente de fotos</p>
                    <div class="glass-button primary">Crear Post Rápido</div>
                </div>
                
                <div class="glass-card" onclick="location.href='?page=soloylibre-advanced-posts'">
                    <h3>🎯 Posts Avanzados</h3>
                    <p>Control total sobre la creación de posts con opciones avanzadas</p>
                    <div class="glass-button success">Posts Avanzados</div>
                </div>
                
                <div class="glass-card" onclick="location.href='?page=soloylibre-photo-manager'">
                    <h3>📁 Gestor de Fotos</h3>
                    <p>Organiza, categoriza y gestiona todas tus fotos</p>
                    <div class="glass-button warning">Gestionar Fotos</div>
                </div>
                
                <div class="glass-card" onclick="location.href='?page=soloylibre-smart-selection'">
                    <h3>🤖 Selección Inteligente</h3>
                    <p>IA para seleccionar las mejores fotos automáticamente</p>
                    <div class="glass-button primary">Selección IA</div>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            loadDashboardStats();
            
            function loadDashboardStats() {
                $.post(soloylibreGlass.ajax_url, {
                    action: 'glass_get_stats',
                    nonce: soloylibreGlass.nonce,
                    type: 'dashboard'
                }, function(response) {
                    if (response.success) {
                        $('#total-photos').text(response.data.total_photos);
                        $('#published-photos').text(response.data.published_photos);
                        $('#private-photos').text(response.data.private_photos);
                        $('#total-views').text(response.data.total_views);
                    }
                });
            }
        });
        </script>
        <?php
    }

    /**
     * Render Quick Posts Page
     */
    public function render_quick_posts() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>⚡ Posts Rápidos</h1>
                <p>Crea posts automáticamente con selección inteligente</p>
            </div>

            <div class="glass-card">
                <h3>🎯 Configuración Rápida</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <label>📊 Número de fotos:</label>
                        <input type="number" id="quick-photo-count" class="glass-input" value="10" min="1" max="50">
                    </div>
                    <div>
                        <label>📝 Título del post:</label>
                        <input type="text" id="quick-post-title" class="glass-input" placeholder="AutoPost - JoseTusabe Photography">
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <label>📖 Descripción:</label>
                    <textarea id="quick-post-content" class="glass-input" rows="3" placeholder="Colección fotográfica de JoseTusabe Photography..."></textarea>
                </div>

                <div style="text-align: center; margin: 20px 0;">
                    <button class="glass-button success" onclick="createQuickPost()">⚡ Crear Post Rápido</button>
                    <button class="glass-button primary" onclick="previewQuickPost()">👁️ Vista Previa</button>
                </div>
            </div>

            <div id="quick-preview" class="glass-card" style="display: none;">
                <h3>👁️ Vista Previa</h3>
                <div id="preview-content"></div>
            </div>

            <div id="quick-notifications"></div>
        </div>

        <script>
        function createQuickPost() {
            const photoCount = $('#quick-photo-count').val();
            const title = $('#quick-post-title').val() || 'AutoPost - JoseTusabe Photography';
            const content = $('#quick-post-content').val() || 'Colección fotográfica de JoseTusabe Photography';

            showNotification('⏳ Creando post rápido...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_create_quick_post',
                nonce: soloylibreGlass.nonce,
                photo_count: photoCount,
                title: title,
                content: content
            }, function(response) {
                if (response.success) {
                    showNotification('✅ Post creado exitosamente!', 'success');
                    setTimeout(() => {
                        window.open(response.data.edit_url, '_blank');
                    }, 1000);
                } else {
                    showNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function previewQuickPost() {
            const photoCount = $('#quick-photo-count').val();

            $('#preview-content').html('⏳ Cargando vista previa...');
            $('#quick-preview').show();

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_load_photos',
                nonce: soloylibreGlass.nonce,
                count: photoCount,
                type: 'preview'
            }, function(response) {
                if (response.success) {
                    let html = '<div class="glass-photo-grid">';
                    response.data.photos.forEach(photo => {
                        html += `
                            <div class="glass-photo-item">
                                <img src="${photo.thumbnail}" alt="${photo.title}">
                                <div class="glass-photo-info">${photo.title}</div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    $('#preview-content').html(html);
                } else {
                    $('#preview-content').html('❌ Error cargando vista previa');
                }
            });
        }

        function showNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#quick-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }

    /**
     * Render Advanced Posts Page
     */
    public function render_advanced_posts() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>🎯 Posts Avanzados</h1>
                <p>Control total sobre la creación de posts</p>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>📊 Configuración del Post</h3>
                    <input type="text" id="advanced-title" class="glass-input" placeholder="Título del post">
                    <textarea id="advanced-content" class="glass-input" rows="4" placeholder="Contenido del post..."></textarea>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
                        <select id="advanced-category" class="glass-input">
                            <option value="">Seleccionar categoría</option>
                            <option value="photography">Fotografía</option>
                            <option value="portfolio">Portfolio</option>
                            <option value="events">Eventos</option>
                            <option value="nature">Naturaleza</option>
                        </select>

                        <select id="advanced-status" class="glass-input">
                            <option value="draft">Borrador</option>
                            <option value="publish">Publicar</option>
                            <option value="private">Privado</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <input type="text" id="advanced-tags" class="glass-input" placeholder="Tags (separados por comas)">
                    </div>
                </div>

                <div class="glass-card">
                    <h3>🖼️ Selección de Fotos</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <input type="number" id="advanced-photo-count" class="glass-input" value="15" min="1" max="100" placeholder="Número de fotos">
                        <select id="advanced-selection-type" class="glass-input">
                            <option value="random">Aleatorio</option>
                            <option value="recent">Más recientes</option>
                            <option value="popular">Más populares</option>
                            <option value="manual">Selección manual</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="advanced-featured-image">
                            Establecer imagen destacada automáticamente
                        </label>
                    </div>

                    <button class="glass-button primary" onclick="loadAdvancedPhotos()">🔄 Cargar Fotos</button>
                </div>
            </div>

            <div id="advanced-photo-selection" class="glass-card" style="display: none;">
                <h3>📷 Fotos Seleccionadas</h3>
                <div id="advanced-photos-grid" class="glass-photo-grid"></div>
                <div style="text-align: center; margin: 20px 0;">
                    <button class="glass-button success" onclick="createAdvancedPost()">🎯 Crear Post Avanzado</button>
                </div>
            </div>

            <div id="advanced-notifications"></div>
        </div>

        <script>
        let selectedAdvancedPhotos = [];

        function loadAdvancedPhotos() {
            const count = $('#advanced-photo-count').val();
            const type = $('#advanced-selection-type').val();

            $('#advanced-photos-grid').html('⏳ Cargando fotos...');
            $('#advanced-photo-selection').show();

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_load_photos',
                nonce: soloylibreGlass.nonce,
                count: count,
                type: type
            }, function(response) {
                if (response.success) {
                    selectedAdvancedPhotos = response.data.photos;
                    displayAdvancedPhotos();
                } else {
                    $('#advanced-photos-grid').html('❌ Error cargando fotos');
                }
            });
        }

        function displayAdvancedPhotos() {
            let html = '';
            selectedAdvancedPhotos.forEach((photo, index) => {
                html += `
                    <div class="glass-photo-item" data-photo-id="${photo.id}">
                        <img src="${photo.thumbnail}" alt="${photo.title}">
                        <div class="glass-photo-info">
                            ${photo.title}
                            <button onclick="removeAdvancedPhoto(${index})" style="background: rgba(220,53,69,0.8); border: none; color: white; border-radius: 3px; padding: 2px 6px; margin-left: 5px;">×</button>
                        </div>
                    </div>
                `;
            });
            $('#advanced-photos-grid').html(html);
        }

        function removeAdvancedPhoto(index) {
            selectedAdvancedPhotos.splice(index, 1);
            displayAdvancedPhotos();
        }

        function createAdvancedPost() {
            const title = $('#advanced-title').val();
            const content = $('#advanced-content').val();
            const category = $('#advanced-category').val();
            const status = $('#advanced-status').val();
            const tags = $('#advanced-tags').val();
            const featuredImage = $('#advanced-featured-image').is(':checked');

            if (!title || selectedAdvancedPhotos.length === 0) {
                showAdvancedNotification('❌ Por favor completa el título y selecciona fotos', 'error');
                return;
            }

            showAdvancedNotification('⏳ Creando post avanzado...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_create_advanced_post',
                nonce: soloylibreGlass.nonce,
                title: title,
                content: content,
                category: category,
                status: status,
                tags: tags,
                featured_image: featuredImage,
                photos: selectedAdvancedPhotos.map(p => p.id)
            }, function(response) {
                if (response.success) {
                    showAdvancedNotification('✅ Post avanzado creado exitosamente!', 'success');
                    setTimeout(() => {
                        window.open(response.data.edit_url, '_blank');
                    }, 1000);
                } else {
                    showAdvancedNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function showAdvancedNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#advanced-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }

    /**
     * Render Photo Manager Page
     */
    public function render_photo_manager() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>📁 Gestor de Fotos</h1>
                <p>Organiza y gestiona todas tus fotos</p>
            </div>

            <div class="glass-card">
                <h3>🔍 Filtros y Acciones</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <select id="photo-filter" class="glass-input" onchange="loadPhotos()">
                        <option value="all">Todas las fotos</option>
                        <option value="published">Publicadas</option>
                        <option value="private">Privadas</option>
                        <option value="unwanted">No deseadas</option>
                        <option value="available">Disponibles</option>
                    </select>

                    <select id="photo-sort" class="glass-input" onchange="loadPhotos()">
                        <option value="date_desc">Más recientes</option>
                        <option value="date_asc">Más antiguas</option>
                        <option value="name_asc">Nombre A-Z</option>
                        <option value="name_desc">Nombre Z-A</option>
                        <option value="random">Aleatorio</option>
                    </select>

                    <input type="number" id="photo-limit" class="glass-input" value="50" min="10" max="200" placeholder="Límite">

                    <button class="glass-button primary" onclick="loadPhotos()">🔄 Cargar</button>
                </div>

                <div style="margin: 15px 0; text-align: center;">
                    <button class="glass-button success" onclick="selectAllPhotos()">✅ Seleccionar Todas</button>
                    <button class="glass-button warning" onclick="deselectAllPhotos()">❌ Deseleccionar</button>
                    <button class="glass-button primary" onclick="markAsPrivate()">🔒 Marcar Privadas</button>
                    <button class="glass-button" onclick="markAsUnwanted()">🗑️ No Deseadas</button>
                </div>
            </div>

            <div class="glass-card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3>📷 Fotos (<span id="photo-count">0</span>)</h3>
                    <div>
                        <span id="selected-count">0</span> seleccionadas
                    </div>
                </div>
                <div id="photos-grid" class="glass-photo-grid">
                    <div class="glass-loading">⏳ Carga fotos usando los filtros arriba</div>
                </div>
            </div>

            <div id="manager-notifications"></div>
        </div>

        <script>
        let selectedPhotos = [];
        let allPhotos = [];

        function loadPhotos() {
            const filter = $('#photo-filter').val();
            const sort = $('#photo-sort').val();
            const limit = $('#photo-limit').val();

            $('#photos-grid').html('<div class="glass-loading">⏳ Cargando fotos...</div>');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_load_photos',
                nonce: soloylibreGlass.nonce,
                filter: filter,
                sort: sort,
                limit: limit,
                type: 'manager'
            }, function(response) {
                if (response.success) {
                    allPhotos = response.data.photos;
                    displayPhotos();
                    $('#photo-count').text(allPhotos.length);
                } else {
                    $('#photos-grid').html('❌ Error cargando fotos');
                }
            });
        }

        function displayPhotos() {
            let html = '';
            allPhotos.forEach(photo => {
                const isSelected = selectedPhotos.includes(photo.id);
                html += `
                    <div class="glass-photo-item ${isSelected ? 'selected' : ''}" data-photo-id="${photo.id}" onclick="togglePhotoSelection(${photo.id})">
                        <img src="${photo.thumbnail}" alt="${photo.title}">
                        <div class="glass-photo-info">
                            <div>${photo.title}</div>
                            <div style="font-size: 10px; opacity: 0.8;">${photo.status || 'Disponible'}</div>
                            ${isSelected ? '<div style="position: absolute; top: 5px; right: 5px; background: rgba(40,167,69,0.9); color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px;">✓</div>' : ''}
                        </div>
                    </div>
                `;
            });
            $('#photos-grid').html(html);
            updateSelectedCount();
        }

        function togglePhotoSelection(photoId) {
            const index = selectedPhotos.indexOf(photoId);
            if (index > -1) {
                selectedPhotos.splice(index, 1);
            } else {
                selectedPhotos.push(photoId);
            }
            displayPhotos();
        }

        function selectAllPhotos() {
            selectedPhotos = allPhotos.map(p => p.id);
            displayPhotos();
            showManagerNotification(`✅ ${selectedPhotos.length} fotos seleccionadas`, 'success');
        }

        function deselectAllPhotos() {
            selectedPhotos = [];
            displayPhotos();
            showManagerNotification('❌ Selección limpiada', 'info');
        }

        function updateSelectedCount() {
            $('#selected-count').text(selectedPhotos.length);
        }

        function markAsPrivate() {
            if (selectedPhotos.length === 0) {
                showManagerNotification('❌ Selecciona fotos primero', 'error');
                return;
            }

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_bulk_action',
                nonce: soloylibreGlass.nonce,
                action_type: 'mark_private',
                photo_ids: selectedPhotos
            }, function(response) {
                if (response.success) {
                    showManagerNotification(`✅ ${selectedPhotos.length} fotos marcadas como privadas`, 'success');
                    loadPhotos();
                    selectedPhotos = [];
                } else {
                    showManagerNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function markAsUnwanted() {
            if (selectedPhotos.length === 0) {
                showManagerNotification('❌ Selecciona fotos primero', 'error');
                return;
            }

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_bulk_action',
                nonce: soloylibreGlass.nonce,
                action_type: 'mark_unwanted',
                photo_ids: selectedPhotos
            }, function(response) {
                if (response.success) {
                    showManagerNotification(`✅ ${selectedPhotos.length} fotos marcadas como no deseadas`, 'success');
                    loadPhotos();
                    selectedPhotos = [];
                } else {
                    showManagerNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function showManagerNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#manager-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }

        // Add selected style
        $('<style>').text(`
            .glass-photo-item.selected {
                background: rgba(40, 167, 69, 0.3) !important;
                border: 2px solid rgba(40, 167, 69, 0.8) !important;
            }
        `).appendTo('head');
        </script>
        <?php
    }

    /**
     * Render Statistics Page
     */
    public function render_statistics() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>📊 Estadísticas</h1>
                <p>Análisis completo de tu biblioteca fotográfica</p>
            </div>

            <div class="glass-stats" id="main-stats">
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-total">-</div>
                    <div class="glass-stat-label">📷 Total Fotos</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-published">-</div>
                    <div class="glass-stat-label">📝 Publicadas</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-private">-</div>
                    <div class="glass-stat-label">🔒 Privadas</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-unwanted">-</div>
                    <div class="glass-stat-label">🗑️ No Deseadas</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-available">-</div>
                    <div class="glass-stat-label">✨ Disponibles</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-views">-</div>
                    <div class="glass-stat-label">👁️ Visualizaciones</div>
                </div>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>📈 Métricas de Publicación</h3>
                    <div id="publication-metrics">
                        <div style="margin: 10px 0;">
                            <strong>Tasa de publicación:</strong> <span id="publication-rate">-</span>%
                        </div>
                        <div style="margin: 10px 0;">
                            <strong>Fotos por post (promedio):</strong> <span id="avg-photos-per-post">-</span>
                        </div>
                        <div style="margin: 10px 0;">
                            <strong>Posts creados:</strong> <span id="total-posts">-</span>
                        </div>
                    </div>
                </div>

                <div class="glass-card">
                    <h3>🎯 Acciones Rápidas</h3>
                    <button class="glass-button primary" onclick="refreshStats()">🔄 Actualizar Estadísticas</button>
                    <button class="glass-button success" onclick="exportStats()">📊 Exportar Datos</button>
                    <button class="glass-button warning" onclick="resetPublishedPhotos()">🔄 Reset Publicadas</button>
                    <button class="glass-button" onclick="generateReport()">📋 Generar Reporte</button>
                </div>
            </div>

            <div class="glass-card">
                <h3>📅 Actividad Reciente</h3>
                <div id="recent-activity">
                    <div class="glass-loading">⏳ Cargando actividad reciente...</div>
                </div>
            </div>

            <div id="stats-notifications"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            loadStatistics();
            loadRecentActivity();
        });

        function loadStatistics() {
            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'full'
            }, function(response) {
                if (response.success) {
                    const data = response.data;
                    $('#stat-total').text(data.total_photos);
                    $('#stat-published').text(data.published_photos);
                    $('#stat-private').text(data.private_photos);
                    $('#stat-unwanted').text(data.unwanted_photos);
                    $('#stat-available').text(data.available_photos);
                    $('#stat-views').text(data.total_views);

                    $('#publication-rate').text(data.publication_rate);
                    $('#avg-photos-per-post').text(data.avg_photos_per_post);
                    $('#total-posts').text(data.total_posts);
                }
            });
        }

        function loadRecentActivity() {
            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'activity'
            }, function(response) {
                if (response.success && response.data.activities) {
                    let html = '';
                    response.data.activities.forEach(activity => {
                        html += `
                            <div style="background: rgba(255,255,255,0.1); padding: 10px; margin: 5px 0; border-radius: 8px;">
                                <strong>${activity.action}</strong> - ${activity.date}
                                <div style="font-size: 12px; opacity: 0.8;">${activity.details}</div>
                            </div>
                        `;
                    });
                    $('#recent-activity').html(html);
                } else {
                    $('#recent-activity').html('📝 No hay actividad reciente registrada');
                }
            });
        }

        function refreshStats() {
            showStatsNotification('🔄 Actualizando estadísticas...', 'info');
            loadStatistics();
            loadRecentActivity();
            setTimeout(() => {
                showStatsNotification('✅ Estadísticas actualizadas', 'success');
            }, 1000);
        }

        function exportStats() {
            showStatsNotification('📊 Exportando datos...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'export'
            }, function(response) {
                if (response.success) {
                    const dataStr = JSON.stringify(response.data, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'soloylibre_stats_' + new Date().toISOString().split('T')[0] + '.json';
                    link.click();
                    showStatsNotification('✅ Datos exportados exitosamente', 'success');
                } else {
                    showStatsNotification('❌ Error exportando datos', 'error');
                }
            });
        }

        function resetPublishedPhotos() {
            if (!confirm('¿Estás seguro de que quieres resetear todas las fotos publicadas?')) return;

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_bulk_action',
                nonce: soloylibreGlass.nonce,
                action_type: 'reset_published'
            }, function(response) {
                if (response.success) {
                    showStatsNotification('✅ Fotos publicadas reseteadas', 'success');
                    loadStatistics();
                } else {
                    showStatsNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function generateReport() {
            showStatsNotification('📋 Generando reporte...', 'info');
            // Esta función se puede expandir para generar reportes más detallados
            setTimeout(() => {
                showStatsNotification('✅ Reporte generado (función en desarrollo)', 'success');
            }, 2000);
        }

        function showStatsNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#stats-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }

    /**
     * Render Smart Selection Page
     */
    public function render_smart_selection() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>🤖 Selección Inteligente</h1>
                <p>IA para seleccionar las mejores fotos automáticamente</p>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>🎯 Criterios de Selección</h3>
                    <div style="margin: 15px 0;">
                        <label>📊 Número de fotos a seleccionar:</label>
                        <input type="number" id="smart-count" class="glass-input" value="20" min="5" max="100">
                    </div>

                    <div style="margin: 15px 0;">
                        <label>🔍 Tipo de selección:</label>
                        <select id="smart-type" class="glass-input">
                            <option value="quality">Mejor calidad</option>
                            <option value="diversity">Máxima diversidad</option>
                            <option value="recent_quality">Recientes + Calidad</option>
                            <option value="popular">Más populares</option>
                            <option value="balanced">Selección balanceada</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="smart-exclude-published">
                            Excluir fotos ya publicadas
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="smart-exclude-private">
                            Excluir fotos privadas
                        </label>
                    </div>

                    <button class="glass-button primary" onclick="runSmartSelection()">🤖 Ejecutar Selección IA</button>
                </div>

                <div class="glass-card">
                    <h3>⚙️ Configuración Avanzada</h3>
                    <div style="margin: 15px 0;">
                        <label>📅 Rango de fechas:</label>
                        <select id="smart-date-range" class="glass-input">
                            <option value="all">Todas las fechas</option>
                            <option value="last_week">Última semana</option>
                            <option value="last_month">Último mes</option>
                            <option value="last_3months">Últimos 3 meses</option>
                            <option value="last_year">Último año</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label>🏷️ Categoría preferida:</label>
                        <select id="smart-category" class="glass-input">
                            <option value="any">Cualquier categoría</option>
                            <option value="portrait">Retratos</option>
                            <option value="landscape">Paisajes</option>
                            <option value="event">Eventos</option>
                            <option value="nature">Naturaleza</option>
                        </select>
                    </div>

                    <button class="glass-button success" onclick="saveSmartPreferences()">💾 Guardar Preferencias</button>
                </div>
            </div>

            <div id="smart-results" class="glass-card" style="display: none;">
                <h3>🎯 Resultados de Selección IA</h3>
                <div id="smart-photos-grid" class="glass-photo-grid"></div>
                <div style="text-align: center; margin: 20px 0;">
                    <button class="glass-button success" onclick="createPostFromSelection()">📝 Crear Post con Selección</button>
                    <button class="glass-button primary" onclick="refineSelection()">🔄 Refinar Selección</button>
                </div>
            </div>

            <div id="smart-notifications"></div>
        </div>

        <script>
        let smartSelectedPhotos = [];

        function runSmartSelection() {
            const count = $('#smart-count').val();
            const type = $('#smart-type').val();
            const excludePublished = $('#smart-exclude-published').is(':checked');
            const excludePrivate = $('#smart-exclude-private').is(':checked');
            const dateRange = $('#smart-date-range').val();
            const category = $('#smart-category').val();

            showSmartNotification('🤖 Ejecutando selección inteligente...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_smart_select',
                nonce: soloylibreGlass.nonce,
                count: count,
                type: type,
                exclude_published: excludePublished,
                exclude_private: excludePrivate,
                date_range: dateRange,
                category: category
            }, function(response) {
                if (response.success) {
                    smartSelectedPhotos = response.data.photos;
                    displaySmartResults();
                    showSmartNotification(`✅ Selección completada: ${smartSelectedPhotos.length} fotos`, 'success');
                } else {
                    showSmartNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function displaySmartResults() {
            let html = '';
            smartSelectedPhotos.forEach((photo, index) => {
                html += `
                    <div class="glass-photo-item" data-photo-id="${photo.id}">
                        <img src="${photo.thumbnail}" alt="${photo.title}">
                        <div class="glass-photo-info">
                            <div>${photo.title}</div>
                            <div style="font-size: 10px; opacity: 0.8;">Score: ${photo.ai_score || 'N/A'}</div>
                            <button onclick="removeFromSmartSelection(${index})" style="background: rgba(220,53,69,0.8); border: none; color: white; border-radius: 3px; padding: 2px 6px; margin-top: 5px;">Remover</button>
                        </div>
                    </div>
                `;
            });
            $('#smart-photos-grid').html(html);
            $('#smart-results').show();
        }

        function removeFromSmartSelection(index) {
            smartSelectedPhotos.splice(index, 1);
            displaySmartResults();
            showSmartNotification('📷 Foto removida de la selección', 'info');
        }

        function createPostFromSelection() {
            if (smartSelectedPhotos.length === 0) {
                showSmartNotification('❌ No hay fotos seleccionadas', 'error');
                return;
            }

            showSmartNotification('📝 Creando post con selección IA...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_create_quick_post',
                nonce: soloylibreGlass.nonce,
                photo_ids: smartSelectedPhotos.map(p => p.id),
                title: 'Selección IA - JoseTusabe Photography',
                content: 'Colección seleccionada automáticamente por inteligencia artificial'
            }, function(response) {
                if (response.success) {
                    showSmartNotification('✅ Post creado exitosamente!', 'success');
                    setTimeout(() => {
                        window.open(response.data.edit_url, '_blank');
                    }, 1000);
                } else {
                    showSmartNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function refineSelection() {
            showSmartNotification('🔄 Refinando selección...', 'info');
            runSmartSelection();
        }

        function saveSmartPreferences() {
            const preferences = {
                default_count: $('#smart-count').val(),
                default_type: $('#smart-type').val(),
                exclude_published: $('#smart-exclude-published').is(':checked'),
                exclude_private: $('#smart-exclude-private').is(':checked'),
                default_date_range: $('#smart-date-range').val(),
                default_category: $('#smart-category').val()
            };

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_save_settings',
                nonce: soloylibreGlass.nonce,
                settings_type: 'smart_preferences',
                settings: preferences
            }, function(response) {
                if (response.success) {
                    showSmartNotification('💾 Preferencias guardadas', 'success');
                } else {
                    showSmartNotification('❌ Error guardando preferencias', 'error');
                }
            });
        }

        function showSmartNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#smart-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }

    /**
     * Render Settings Page
     */
    public function render_settings() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>⚙️ Configuraciones</h1>
                <p>Personaliza tu experiencia con SoloYLibre</p>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>👤 Información del Fotógrafo</h3>
                    <input type="text" id="photographer-name" class="glass-input" placeholder="Jose L Encarnacion" value="Jose L Encarnacion">
                    <input type="text" id="photographer-alias" class="glass-input" placeholder="JoseTusabe" value="JoseTusabe">
                    <input type="text" id="photographer-brand" class="glass-input" placeholder="JoseTusabe Photography" value="JoseTusabe Photography">
                    <input type="email" id="photographer-email" class="glass-input" placeholder="<EMAIL>" value="<EMAIL>">
                    <input type="tel" id="photographer-phone" class="glass-input" placeholder="************" value="************">
                    <input type="text" id="photographer-location" class="glass-input" placeholder="San José de Ocoa, Dom. Rep. / USA" value="San José de Ocoa, Dom. Rep. / USA">
                </div>

                <div class="glass-card">
                    <h3>🌐 Configuraciones de Sitio Web</h3>
                    <input type="url" id="website-main" class="glass-input" placeholder="https://josetusabe.com" value="https://josetusabe.com">
                    <input type="url" id="website-portfolio" class="glass-input" placeholder="https://soloylibre.com" value="https://soloylibre.com">
                    <input type="url" id="website-photo" class="glass-input" placeholder="https://1and1photo.com" value="https://1and1photo.com">
                    <input type="url" id="website-personal" class="glass-input" placeholder="https://joselencarnacion.com" value="https://joselencarnacion.com">
                </div>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>📸 Configuraciones de Galería</h3>
                    <div style="margin: 15px 0;">
                        <label>🖼️ Fotos por página:</label>
                        <input type="number" id="photos-per-page" class="glass-input" value="50" min="10" max="200">
                    </div>

                    <div style="margin: 15px 0;">
                        <label>📐 Estilo de galería por defecto:</label>
                        <select id="default-gallery-style" class="glass-input">
                            <option value="masonry">Masonry</option>
                            <option value="grid">Grid</option>
                            <option value="carousel">Carousel</option>
                            <option value="lightbox">Lightbox</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="auto-feature-image" checked>
                            Imagen destacada automática
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="enable-interactions" checked>
                            Habilitar interacciones (likes, shares)
                        </label>
                    </div>
                </div>

                <div class="glass-card">
                    <h3>🤖 Configuraciones de IA</h3>
                    <div style="margin: 15px 0;">
                        <label>🎯 Algoritmo de selección:</label>
                        <select id="ai-algorithm" class="glass-input">
                            <option value="balanced">Balanceado</option>
                            <option value="quality_focused">Enfocado en calidad</option>
                            <option value="diversity_focused">Enfocado en diversidad</option>
                            <option value="recent_bias">Sesgo hacia recientes</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label>📊 Peso de popularidad (%):</label>
                        <input type="range" id="popularity-weight" class="glass-input" min="0" max="100" value="30">
                        <span id="popularity-value">30%</span>
                    </div>

                    <div style="margin: 15px 0;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="enable-ai-suggestions" checked>
                            Sugerencias automáticas de IA
                        </label>
                    </div>
                </div>
            </div>

            <div class="glass-card">
                <h3>🔧 Acciones del Sistema</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="glass-button success" onclick="saveAllSettings()">💾 Guardar Todo</button>
                    <button class="glass-button primary" onclick="exportSettings()">📤 Exportar Config</button>
                    <button class="glass-button warning" onclick="importSettings()">📥 Importar Config</button>
                    <button class="glass-button" onclick="resetToDefaults()">🔄 Restaurar Defaults</button>
                </div>

                <div style="margin: 20px 0; text-align: center;">
                    <input type="file" id="import-file" accept=".json" style="display: none;" onchange="handleImportFile(this)">
                </div>
            </div>

            <div id="settings-notifications"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            loadCurrentSettings();

            // Update popularity weight display
            $('#popularity-weight').on('input', function() {
                $('#popularity-value').text($(this).val() + '%');
            });
        });

        function loadCurrentSettings() {
            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'settings'
            }, function(response) {
                if (response.success && response.data.settings) {
                    const settings = response.data.settings;
                    // Populate form fields with current settings
                    Object.keys(settings).forEach(key => {
                        const element = $('#' + key.replace(/_/g, '-'));
                        if (element.length) {
                            if (element.is(':checkbox')) {
                                element.prop('checked', settings[key]);
                            } else {
                                element.val(settings[key]);
                            }
                        }
                    });
                }
            });
        }

        function saveAllSettings() {
            const settings = {
                photographer_name: $('#photographer-name').val(),
                photographer_alias: $('#photographer-alias').val(),
                photographer_brand: $('#photographer-brand').val(),
                photographer_email: $('#photographer-email').val(),
                photographer_phone: $('#photographer-phone').val(),
                photographer_location: $('#photographer-location').val(),
                website_main: $('#website-main').val(),
                website_portfolio: $('#website-portfolio').val(),
                website_photo: $('#website-photo').val(),
                website_personal: $('#website-personal').val(),
                photos_per_page: $('#photos-per-page').val(),
                default_gallery_style: $('#default-gallery-style').val(),
                auto_feature_image: $('#auto-feature-image').is(':checked'),
                enable_interactions: $('#enable-interactions').is(':checked'),
                ai_algorithm: $('#ai-algorithm').val(),
                popularity_weight: $('#popularity-weight').val(),
                enable_ai_suggestions: $('#enable-ai-suggestions').is(':checked')
            };

            showSettingsNotification('💾 Guardando configuraciones...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_save_settings',
                nonce: soloylibreGlass.nonce,
                settings_type: 'all',
                settings: settings
            }, function(response) {
                if (response.success) {
                    showSettingsNotification('✅ Configuraciones guardadas exitosamente', 'success');
                } else {
                    showSettingsNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function exportSettings() {
            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'export_settings'
            }, function(response) {
                if (response.success) {
                    const dataStr = JSON.stringify(response.data, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'soloylibre_settings_' + new Date().toISOString().split('T')[0] + '.json';
                    link.click();
                    showSettingsNotification('📤 Configuraciones exportadas', 'success');
                } else {
                    showSettingsNotification('❌ Error exportando configuraciones', 'error');
                }
            });
        }

        function importSettings() {
            $('#import-file').click();
        }

        function handleImportFile(input) {
            const file = input.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const settings = JSON.parse(e.target.result);

                    $.post(soloylibreGlass.ajax_url, {
                        action: 'glass_save_settings',
                        nonce: soloylibreGlass.nonce,
                        settings_type: 'import',
                        settings: settings
                    }, function(response) {
                        if (response.success) {
                            showSettingsNotification('📥 Configuraciones importadas exitosamente', 'success');
                            loadCurrentSettings();
                        } else {
                            showSettingsNotification('❌ Error importando configuraciones', 'error');
                        }
                    });
                } catch (error) {
                    showSettingsNotification('❌ Archivo de configuración inválido', 'error');
                }
            };
            reader.readAsText(file);
        }

        function resetToDefaults() {
            if (!confirm('¿Estás seguro de que quieres restaurar todas las configuraciones por defecto?')) return;

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_save_settings',
                nonce: soloylibreGlass.nonce,
                settings_type: 'reset'
            }, function(response) {
                if (response.success) {
                    showSettingsNotification('🔄 Configuraciones restauradas a valores por defecto', 'success');
                    loadCurrentSettings();
                } else {
                    showSettingsNotification('❌ Error restaurando configuraciones', 'error');
                }
            });
        }

        function showSettingsNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#settings-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }

    // AJAX Methods

    /**
     * AJAX: Load photos with various filters and options
     */
    public function ajax_load_photos() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $count = intval($_POST['count'] ?? 50);
        $filter = sanitize_text_field($_POST['filter'] ?? 'all');
        $sort = sanitize_text_field($_POST['sort'] ?? 'date_desc');
        $type = sanitize_text_field($_POST['type'] ?? 'preview');
        $limit = intval($_POST['limit'] ?? 50);

        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => min($count, $limit),
            'fields' => 'ids'
        );

        // Apply sorting
        switch ($sort) {
            case 'date_asc':
                $args['orderby'] = 'date';
                $args['order'] = 'ASC';
                break;
            case 'name_asc':
                $args['orderby'] = 'title';
                $args['order'] = 'ASC';
                break;
            case 'name_desc':
                $args['orderby'] = 'title';
                $args['order'] = 'DESC';
                break;
            case 'random':
                $args['orderby'] = 'rand';
                break;
            default: // date_desc
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
        }

        // Apply filters
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        switch ($filter) {
            case 'published':
                if (!empty($published_photos)) {
                    $args['post__in'] = $published_photos;
                } else {
                    wp_send_json_success(array('photos' => array()));
                    return;
                }
                break;
            case 'private':
                if (!empty($private_photos)) {
                    $args['post__in'] = $private_photos;
                } else {
                    wp_send_json_success(array('photos' => array()));
                    return;
                }
                break;
            case 'unwanted':
                if (!empty($unwanted_photos)) {
                    $args['post__in'] = $unwanted_photos;
                } else {
                    wp_send_json_success(array('photos' => array()));
                    return;
                }
                break;
            case 'available':
                $used_photos = array_merge($published_photos, $private_photos, $unwanted_photos);
                if (!empty($used_photos)) {
                    $args['post__not_in'] = array_unique($used_photos);
                }
                break;
        }

        $photo_ids = get_posts($args);
        $photos = array();

        foreach ($photo_ids as $photo_id) {
            $photos[] = array(
                'id' => $photo_id,
                'title' => get_the_title($photo_id) ?: 'Photo ' . $photo_id,
                'thumbnail' => wp_get_attachment_image_url($photo_id, 'thumbnail'),
                'full' => wp_get_attachment_image_url($photo_id, 'full'),
                'status' => $this->get_photo_status($photo_id),
                'ai_score' => rand(75, 98) // Simulated AI score
            );
        }

        wp_send_json_success(array(
            'photos' => $photos,
            'count' => count($photos),
            'filter' => $filter,
            'sort' => $sort
        ));
    }

    /**
     * AJAX: Create quick post
     */
    public function ajax_create_quick_post() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $photo_count = intval($_POST['photo_count'] ?? 10);
        $title = sanitize_text_field($_POST['title'] ?? 'AutoPost - JoseTusabe Photography');
        $content = sanitize_textarea_field($_POST['content'] ?? 'Colección fotográfica de JoseTusabe Photography');
        $photo_ids = $_POST['photo_ids'] ?? array();

        // If no specific photo IDs provided, get random photos
        if (empty($photo_ids)) {
            $args = array(
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'post_status' => 'inherit',
                'posts_per_page' => $photo_count,
                'orderby' => 'rand',
                'fields' => 'ids'
            );

            // Exclude already published photos
            $published_photos = get_option('soloylibre_published_photos', array());
            if (!empty($published_photos)) {
                $args['post__not_in'] = $published_photos;
            }

            $photo_ids = get_posts($args);
        }

        if (empty($photo_ids)) {
            wp_send_json_error('No hay fotos disponibles para crear el post');
            return;
        }

        // Create the post
        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'draft',
            'post_type' => 'post'
        );

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            wp_send_json_error('Error creando el post: ' . $post_id->get_error_message());
            return;
        }

        // Create gallery shortcode
        $gallery_shortcode = '[gallery ids="' . implode(',', $photo_ids) . '" columns="3" size="medium"]';

        // Update post content with gallery
        wp_update_post(array(
            'ID' => $post_id,
            'post_content' => $content . "\n\n" . $gallery_shortcode
        ));

        // Set featured image (first photo)
        if (!empty($photo_ids)) {
            set_post_thumbnail($post_id, $photo_ids[0]);
        }

        // Mark photos as published
        $published_photos = get_option('soloylibre_published_photos', array());
        $published_photos = array_merge($published_photos, $photo_ids);
        update_option('soloylibre_published_photos', array_unique($published_photos));

        wp_send_json_success(array(
            'post_id' => $post_id,
            'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit'),
            'photos_used' => count($photo_ids),
            'message' => 'Post creado exitosamente con ' . count($photo_ids) . ' fotos'
        ));
    }

    /**
     * AJAX: Get statistics
     */
    public function ajax_get_stats() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $type = sanitize_text_field($_POST['type'] ?? 'dashboard');

        $total_photos = wp_count_posts('attachment')->inherit;
        $published_photos = count(get_option('soloylibre_published_photos', array()));
        $private_photos = count(get_option('soloylibre_private_photos', array()));
        $unwanted_photos = count(get_option('soloylibre_unwanted_photos', array()));
        $available_photos = $total_photos - $published_photos - $private_photos - $unwanted_photos;
        $total_views = get_option('soloylibre_total_views', 0);

        $stats = array(
            'total_photos' => $total_photos,
            'published_photos' => $published_photos,
            'private_photos' => $private_photos,
            'unwanted_photos' => $unwanted_photos,
            'available_photos' => max(0, $available_photos),
            'total_views' => $total_views,
            'publication_rate' => $total_photos > 0 ? round(($published_photos / $total_photos) * 100, 1) : 0,
            'avg_photos_per_post' => $published_photos > 0 ? round($published_photos / max(1, wp_count_posts('post')->publish), 1) : 0,
            'total_posts' => wp_count_posts('post')->publish
        );

        if ($type === 'activity') {
            $stats['activities'] = array(
                array('action' => 'Post creado', 'date' => date('Y-m-d H:i'), 'details' => 'Último post automático'),
                array('action' => 'Fotos categorizadas', 'date' => date('Y-m-d H:i', strtotime('-1 hour')), 'details' => '15 fotos marcadas como privadas'),
                array('action' => 'Estadísticas actualizadas', 'date' => date('Y-m-d H:i', strtotime('-2 hours')), 'details' => 'Recálculo automático')
            );
        }

        wp_send_json_success($stats);
    }

    /**
     * AJAX: Smart selection
     */
    public function ajax_smart_select() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $count = intval($_POST['count'] ?? 20);
        $type = sanitize_text_field($_POST['type'] ?? 'balanced');
        $exclude_published = $_POST['exclude_published'] === 'true';
        $exclude_private = $_POST['exclude_private'] === 'true';

        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => $count * 2, // Get more to filter
            'fields' => 'ids'
        );

        // Apply exclusions
        $exclude_ids = array();
        if ($exclude_published) {
            $exclude_ids = array_merge($exclude_ids, get_option('soloylibre_published_photos', array()));
        }
        if ($exclude_private) {
            $exclude_ids = array_merge($exclude_ids, get_option('soloylibre_private_photos', array()));
        }

        if (!empty($exclude_ids)) {
            $args['post__not_in'] = array_unique($exclude_ids);
        }

        // Apply selection type
        switch ($type) {
            case 'quality':
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
                break;
            case 'diversity':
                $args['orderby'] = 'rand';
                break;
            case 'recent_quality':
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
                break;
            case 'popular':
                $args['meta_key'] = 'photo_views';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            default: // balanced
                $args['orderby'] = 'rand';
        }

        $photo_ids = get_posts($args);

        // Simulate AI scoring and selection
        $scored_photos = array();
        foreach ($photo_ids as $photo_id) {
            $score = rand(60, 100);

            // Boost score based on type
            switch ($type) {
                case 'quality':
                    $score += rand(10, 20);
                    break;
                case 'recent_quality':
                    $days_old = (time() - get_post_time('U', false, $photo_id)) / (24 * 60 * 60);
                    if ($days_old < 30) $score += 15;
                    break;
            }

            $scored_photos[] = array(
                'id' => $photo_id,
                'score' => min(100, $score)
            );
        }

        // Sort by score and take top results
        usort($scored_photos, function($a, $b) {
            return $b['score'] - $a['score'];
        });

        $selected_photos = array_slice($scored_photos, 0, $count);

        // Format for response
        $photos = array();
        foreach ($selected_photos as $photo_data) {
            $photo_id = $photo_data['id'];
            $photos[] = array(
                'id' => $photo_id,
                'title' => get_the_title($photo_id) ?: 'Photo ' . $photo_id,
                'thumbnail' => wp_get_attachment_image_url($photo_id, 'thumbnail'),
                'full' => wp_get_attachment_image_url($photo_id, 'full'),
                'ai_score' => $photo_data['score']
            );
        }

        wp_send_json_success(array(
            'photos' => $photos,
            'count' => count($photos),
            'type' => $type,
            'message' => 'Selección IA completada con ' . count($photos) . ' fotos'
        ));
    }

    /**
     * AJAX: Bulk actions
     */
    public function ajax_bulk_action() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $action_type = sanitize_text_field($_POST['action_type'] ?? '');
        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());

        switch ($action_type) {
            case 'mark_private':
                $private_photos = get_option('soloylibre_private_photos', array());
                $private_photos = array_merge($private_photos, $photo_ids);
                update_option('soloylibre_private_photos', array_unique($private_photos));

                wp_send_json_success(array(
                    'message' => count($photo_ids) . ' fotos marcadas como privadas',
                    'count' => count($photo_ids)
                ));
                break;

            case 'mark_unwanted':
                $unwanted_photos = get_option('soloylibre_unwanted_photos', array());
                $unwanted_photos = array_merge($unwanted_photos, $photo_ids);
                update_option('soloylibre_unwanted_photos', array_unique($unwanted_photos));

                wp_send_json_success(array(
                    'message' => count($photo_ids) . ' fotos marcadas como no deseadas',
                    'count' => count($photo_ids)
                ));
                break;

            case 'reset_published':
                update_option('soloylibre_published_photos', array());

                wp_send_json_success(array(
                    'message' => 'Fotos publicadas reseteadas',
                    'count' => 0
                ));
                break;

            default:
                wp_send_json_error('Acción no válida');
        }
    }

    /**
     * AJAX: Save settings
     */
    public function ajax_save_settings() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $settings_type = sanitize_text_field($_POST['settings_type'] ?? 'all');
        $settings = $_POST['settings'] ?? array();

        switch ($settings_type) {
            case 'all':
                foreach ($settings as $key => $value) {
                    $sanitized_key = sanitize_key($key);
                    if (is_bool($value) || $value === 'true' || $value === 'false') {
                        $sanitized_value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                    } else {
                        $sanitized_value = sanitize_text_field($value);
                    }
                    update_option("soloylibre_glass_$sanitized_key", $sanitized_value);
                }
                break;

            case 'smart_preferences':
                update_option('soloylibre_glass_smart_preferences', $settings);
                break;

            case 'reset':
                // Delete all glass settings
                global $wpdb;
                $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE 'soloylibre_glass_%'");
                break;

            case 'import':
                foreach ($settings as $key => $value) {
                    update_option($key, $value);
                }
                break;
        }

        wp_send_json_success(array(
            'message' => 'Configuraciones guardadas exitosamente',
            'type' => $settings_type
        ));
    }

    /**
     * Get photo status
     */
    private function get_photo_status($photo_id) {
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        if (in_array($photo_id, $published_photos)) return 'Publicada';
        if (in_array($photo_id, $private_photos)) return 'Privada';
        if (in_array($photo_id, $unwanted_photos)) return 'No deseada';

        return 'Disponible';
    }
}
