<?php
/**
 * Glass Menu System - SoloYLibre Gallery Plugin
 * Modern glassmorphism design without overlays
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

class SoloYLibre_Glass_Menu_System {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_glass_menus'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_glass_styles'));
        $this->init_ajax_handlers();
    }
    
    /**
     * Add glassmorphism menu pages
     */
    public function add_glass_menus() {
        // Main menu page
        add_menu_page(
            'SoloYLibre Gallery',
            '📸 SoloYLibre',
            'manage_options',
            'soloylibre-dashboard',
            array($this, 'render_dashboard'),
            'dashicons-camera',
            25
        );
        
        // Submenu pages
        add_submenu_page(
            'soloylibre-dashboard',
            'Dashboard',
            '🏠 Dashboard',
            'manage_options',
            'soloylibre-dashboard',
            array($this, 'render_dashboard')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Quick Posts',
            '⚡ Posts Rápidos',
            'manage_options',
            'soloylibre-quick-posts',
            array($this, 'render_quick_posts')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Advanced Posts',
            '🎯 Posts Avanzados',
            'manage_options',
            'soloylibre-advanced-posts',
            array($this, 'render_advanced_posts')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Photo Manager',
            '📁 Gestor de Fotos',
            'manage_options',
            'soloylibre-photo-manager',
            array($this, 'render_photo_manager')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Statistics',
            '📊 Estadísticas',
            'manage_options',
            'soloylibre-stats',
            array($this, 'render_statistics')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Smart Selection',
            '🤖 Selección Inteligente',
            'manage_options',
            'soloylibre-smart-selection',
            array($this, 'render_smart_selection')
        );
        
        add_submenu_page(
            'soloylibre-dashboard',
            'Settings',
            '⚙️ Configuraciones',
            'manage_options',
            'soloylibre-glass-settings',
            array($this, 'render_settings')
        );
    }
    
    /**
     * Enqueue glassmorphism styles
     */
    public function enqueue_glass_styles($hook) {
        if (strpos($hook, 'soloylibre') === false) return;
        
        wp_enqueue_script('soloylibre-glass-js', plugin_dir_url(__FILE__) . '../assets/js/glass-system.js', array('jquery'), '1.0.0', true);
        
        wp_localize_script('soloylibre-glass-js', 'soloylibreGlass', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('soloylibre_glass_nonce'),
            'photographer' => array(
                'name' => 'Jose L Encarnacion',
                'alias' => 'JoseTusabe',
                'brand' => 'JoseTusabe Photography',
                'location' => 'San José de Ocoa, Dom. Rep. / USA',
                'phone' => '************',
                'email' => '<EMAIL>'
            )
        ));
    }
    
    /**
     * Initialize AJAX handlers
     */
    private function init_ajax_handlers() {
        add_action('wp_ajax_glass_load_photos', array($this, 'ajax_load_photos'));
        add_action('wp_ajax_glass_create_quick_post', array($this, 'ajax_create_quick_post'));
        add_action('wp_ajax_glass_create_advanced_post', array($this, 'ajax_create_advanced_post'));
        add_action('wp_ajax_glass_get_stats', array($this, 'ajax_get_stats'));
        add_action('wp_ajax_glass_smart_select', array($this, 'ajax_smart_select'));
        add_action('wp_ajax_glass_save_settings', array($this, 'ajax_save_settings'));
        add_action('wp_ajax_glass_bulk_action', array($this, 'ajax_bulk_action'));
        add_action('wp_ajax_glass_add_like', array($this, 'ajax_add_like'));
        add_action('wp_ajax_nopriv_glass_add_like', array($this, 'ajax_add_like'));
        add_action('wp_ajax_glass_reset_published', array($this, 'ajax_reset_published'));
    }
    
    /**
     * Get common glass styles
     */
    private function get_glass_styles() {
        return "
        <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .glass-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 30px;
            margin: 20px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            color: white;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .glass-card:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-5px);
            box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
        }
        
        .glass-button {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .glass-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px 0 rgba(31, 38, 135, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .glass-button.primary {
            background: rgba(0, 123, 255, 0.3);
            border: 1px solid rgba(0, 123, 255, 0.5);
        }
        
        .glass-button.success {
            background: rgba(40, 167, 69, 0.3);
            border: 1px solid rgba(40, 167, 69, 0.5);
        }
        
        .glass-button.warning {
            background: rgba(255, 193, 7, 0.3);
            border: 1px solid rgba(255, 193, 7, 0.5);
        }
        
        .glass-input {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 12px 16px;
            color: white;
            width: 100%;
            margin: 10px 0;
            font-size: 16px;
        }
        
        .glass-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .glass-input:focus {
            outline: none;
            border: 1px solid rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .glass-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .glass-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .glass-header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .glass-header p {
            font-size: 1.2em;
            opacity: 0.9;
            margin: 10px 0;
        }
        
        .glass-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .glass-stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .glass-stat-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.05);
        }
        
        .glass-stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .glass-stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .glass-photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .glass-photo-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .glass-photo-item:hover {
            transform: scale(1.05);
            background: rgba(255, 255, 255, 0.2);
        }
        
        .glass-photo-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            display: block;
        }
        
        .glass-photo-info {
            padding: 10px;
            font-size: 12px;
            text-align: center;
        }
        
        .glass-loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2em;
        }
        
        .glass-notification {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 15px 20px;
            margin: 15px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            animation: slideInDown 0.3s ease;
        }
        
        .glass-notification.success {
            border-left: 4px solid #28a745;
        }
        
        .glass-notification.error {
            border-left: 4px solid #dc3545;
        }
        
        .glass-notification.info {
            border-left: 4px solid #17a2b8;
        }
        
        @keyframes slideInDown {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        /* Enhanced Dashboard Styles */
        .dashboard-enhanced {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        .glass-header-enhanced {
            position: relative;
            text-align: center;
            padding: 40px 20px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .header-animation {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            animation: headerFloat 6s ease-in-out infinite;
        }

        @keyframes headerFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(1deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .animated-title {
            font-size: 3.5em;
            font-weight: 800;
            background: linear-gradient(45deg, #fff, #f0f8ff, #fff);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: titleShine 3s ease-in-out infinite;
            margin: 0 0 15px 0;
            text-shadow: 0 0 30px rgba(255,255,255,0.5);
        }

        @keyframes titleShine {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .animated-subtitle {
            font-size: 1.3em;
            opacity: 0.9;
            margin: 0 0 25px 0;
            animation: subtitleFade 2s ease-in-out infinite alternate;
        }

        @keyframes subtitleFade {
            0% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        .photographer-badge {
            display: inline-flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            padding: 15px 25px;
            border-radius: 50px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            animation: badgeGlow 4s ease-in-out infinite;
        }

        @keyframes badgeGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(255,255,255,0.3); }
            50% { box-shadow: 0 0 40px rgba(255,255,255,0.6); }
        }

        .photographer-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
            border: 3px solid rgba(255, 255, 255, 0.5);
            animation: avatarRotate 10s linear infinite;
        }

        @keyframes avatarRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .photographer-info {
            text-align: left;
            line-height: 1.4;
        }

        .enhanced-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .enhanced-stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 25px;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            cursor: pointer;
        }

        .enhanced-stat-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            background: rgba(255, 255, 255, 0.2);
        }

        .enhanced-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .enhanced-stat-card:hover::before {
            left: 100%;
        }

        .enhanced-stat-card.primary { border-left: 5px solid #007bff; }
        .enhanced-stat-card.success { border-left: 5px solid #28a745; }
        .enhanced-stat-card.warning { border-left: 5px solid #ffc107; }
        .enhanced-stat-card.info { border-left: 5px solid #17a2b8; }
        .enhanced-stat-card.danger { border-left: 5px solid #dc3545; }
        .enhanced-stat-card.purple { border-left: 5px solid #6f42c1; }

        .stat-icon {
            font-size: 3em;
            position: absolute;
            top: 20px;
            right: 20px;
            opacity: 0.3;
            animation: iconFloat 3s ease-in-out infinite;
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .stat-content {
            position: relative;
            z-index: 2;
        }

        .stat-number {
            font-size: 2.8em;
            font-weight: 800;
            margin-bottom: 5px;
            color: white;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
            animation: numberPulse 2s ease-in-out infinite;
        }

        @keyframes numberPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .stat-label {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .stat-trend {
            font-size: 0.9em;
            opacity: 0.7;
            font-style: italic;
        }

        .stat-chart {
            position: absolute;
            bottom: 10px;
            right: 10px;
            opacity: 0.6;
        }

        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 25px;
            position: relative;
            overflow: hidden;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-header h3 {
            margin: 0;
            font-size: 1.4em;
            font-weight: 700;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .chart-btn:hover, .chart-btn.active {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .glass-container {
                margin: 10px;
                padding: 20px;
            }

            .glass-grid {
                grid-template-columns: 1fr;
            }

            .glass-stats {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .glass-photo-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }

            .enhanced-stats-grid {
                grid-template-columns: 1fr;
            }

            .charts-section {
                grid-template-columns: 1fr;
            }

            .animated-title {
                font-size: 2.5em;
            }
        }

        /* Quick Actions Enhanced */
        .quick-actions-enhanced {
            margin: 40px 0;
        }

        .quick-actions-enhanced h3 {
            font-size: 1.8em;
            font-weight: 700;
            margin-bottom: 25px;
            text-align: center;
            background: linear-gradient(45deg, #fff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .action-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 25px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .action-card:hover {
            transform: translateX(10px) scale(1.02);
            box-shadow: 0 15px 50px rgba(0,0,0,0.3);
            background: rgba(255, 255, 255, 0.2);
        }

        .action-card.lightning { border-left: 5px solid #ffd700; }
        .action-card.advanced { border-left: 5px solid #ff6b6b; }
        .action-card.manager { border-left: 5px solid #4ecdc4; }
        .action-card.ai { border-left: 5px solid #a8e6cf; }

        .action-icon {
            font-size: 3em;
            margin-right: 20px;
            animation: actionIconBounce 2s ease-in-out infinite;
        }

        @keyframes actionIconBounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .action-content {
            flex: 1;
        }

        .action-content h4 {
            margin: 0 0 8px 0;
            font-size: 1.3em;
            font-weight: 700;
        }

        .action-content p {
            margin: 0 0 8px 0;
            opacity: 0.8;
            font-size: 0.95em;
        }

        .action-stats {
            font-size: 0.85em;
            opacity: 0.6;
            font-style: italic;
        }

        .action-arrow {
            font-size: 1.5em;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .action-card:hover .action-arrow {
            opacity: 1;
            transform: translateX(5px);
        }

        /* Activity Feed */
        .activity-feed {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 25px;
            margin: 40px 0;
        }

        .activity-feed h3 {
            margin: 0 0 20px 0;
            font-size: 1.5em;
            font-weight: 700;
            background: linear-gradient(45deg, #fff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            animation: activitySlideIn 0.5s ease;
        }

        @keyframes activitySlideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .activity-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2em;
            animation: activityIconPulse 3s ease-in-out infinite;
        }

        @keyframes activityIconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .activity-icon.success { background: rgba(40, 167, 69, 0.3); }
        .activity-icon.info { background: rgba(23, 162, 184, 0.3); }
        .activity-icon.warning { background: rgba(255, 193, 7, 0.3); }

        .activity-content {
            flex: 1;
            line-height: 1.4;
        }

        .activity-content strong {
            display: block;
            font-weight: 700;
            margin-bottom: 3px;
        }

        .activity-content span {
            opacity: 0.8;
            font-size: 0.9em;
        }

        .activity-content time {
            display: block;
            opacity: 0.6;
            font-size: 0.8em;
            margin-top: 5px;
            font-style: italic;
        }

        /* Floating particles animation */
        .dashboard-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 2px, transparent 2px),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 2px, transparent 2px),
                radial-gradient(circle at 40% 40%, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 100px 100px, 150px 150px, 80px 80px;
            animation: particlesFloat 20s linear infinite;
            pointer-events: none;
        }

        @keyframes particlesFloat {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }
        </style>
        ";
    }
    
    /**
     * Render Dashboard
     */
    public function render_dashboard() {
        echo $this->get_glass_styles();
        ?>
        <!-- Chart.js CDN -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

        <div class="glass-container dashboard-enhanced">
            <!-- Header with animated background -->
            <div class="glass-header-enhanced">
                <div class="header-animation"></div>
                <div class="header-content">
                    <h1 class="animated-title">📸 SoloYLibre Dashboard</h1>
                    <p class="animated-subtitle">🇩🇴 JoseTusabe Photography - Sistema Profesional de Gestión Fotográfica</p>
                    <div class="photographer-badge">
                        <img src="https://www.gravatar.com/avatar/<?php echo md5('<EMAIL>'); ?>?s=60&d=identicon" class="photographer-avatar">
                        <div class="photographer-info">
                            <strong>Jose L Encarnacion</strong><br>
                            <span>JoseTusabe Photography</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Stats Cards with animations -->
            <div class="enhanced-stats-grid" id="dashboard-stats">
                <div class="enhanced-stat-card primary" data-stat="total-photos">
                    <div class="stat-icon">📷</div>
                    <div class="stat-content">
                        <div class="stat-number" id="total-photos">-</div>
                        <div class="stat-label">Total Fotos</div>
                        <div class="stat-trend">+12% este mes</div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="totalPhotosChart" width="80" height="40"></canvas>
                    </div>
                </div>

                <div class="enhanced-stat-card success" data-stat="published-photos">
                    <div class="stat-icon">📝</div>
                    <div class="stat-content">
                        <div class="stat-number" id="published-photos">-</div>
                        <div class="stat-label">Publicadas</div>
                        <div class="stat-trend">+8% esta semana</div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="publishedPhotosChart" width="80" height="40"></canvas>
                    </div>
                </div>

                <div class="enhanced-stat-card warning" data-stat="private-photos">
                    <div class="stat-icon">🔒</div>
                    <div class="stat-content">
                        <div class="stat-number" id="private-photos">-</div>
                        <div class="stat-label">Privadas</div>
                        <div class="stat-trend">Seguras</div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="privatePhotosChart" width="80" height="40"></canvas>
                    </div>
                </div>

                <div class="enhanced-stat-card info" data-stat="total-views">
                    <div class="stat-icon">👁️</div>
                    <div class="stat-content">
                        <div class="stat-number" id="total-views">-</div>
                        <div class="stat-label">Visualizaciones</div>
                        <div class="stat-trend">+25% hoy</div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="viewsChart" width="80" height="40"></canvas>
                    </div>
                </div>

                <div class="enhanced-stat-card danger" data-stat="total-likes">
                    <div class="stat-icon">💖</div>
                    <div class="stat-content">
                        <div class="stat-number" id="total-likes">-</div>
                        <div class="stat-label">Likes Totales</div>
                        <div class="stat-trend">+15% hoy</div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="likesChart" width="80" height="40"></canvas>
                    </div>
                </div>

                <div class="enhanced-stat-card purple" data-stat="ai-score">
                    <div class="stat-icon">🤖</div>
                    <div class="stat-content">
                        <div class="stat-number" id="ai-score">-</div>
                        <div class="stat-label">Score IA</div>
                        <div class="stat-trend">Excelente</div>
                    </div>
                    <div class="stat-chart">
                        <canvas id="aiScoreChart" width="80" height="40"></canvas>
                    </div>
                </div>
            </div>

            <!-- Main Charts Section -->
            <div class="charts-section">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>📊 Actividad Fotográfica</h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" data-period="7d">7 días</button>
                            <button class="chart-btn" data-period="30d">30 días</button>
                            <button class="chart-btn" data-period="90d">90 días</button>
                        </div>
                    </div>
                    <canvas id="activityChart" width="400" height="200"></canvas>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3>🎯 Distribución de Fotos</h3>
                    </div>
                    <canvas id="distributionChart" width="300" height="200"></canvas>
                </div>
            </div>

            <!-- Quick Actions with enhanced design -->
            <div class="quick-actions-enhanced">
                <h3>🚀 Acciones Rápidas</h3>
                <div class="actions-grid">
                    <div class="action-card lightning" onclick="location.href='?page=soloylibre-quick-posts'">
                        <div class="action-icon">⚡</div>
                        <div class="action-content">
                            <h4>Posts Rápidos</h4>
                            <p>Crea posts automáticamente con IA</p>
                            <div class="action-stats">Último: hace 2h</div>
                        </div>
                        <div class="action-arrow">→</div>
                    </div>

                    <div class="action-card advanced" onclick="location.href='?page=soloylibre-advanced-posts'">
                        <div class="action-icon">🎯</div>
                        <div class="action-content">
                            <h4>Posts Avanzados</h4>
                            <p>Control total sobre tus publicaciones</p>
                            <div class="action-stats">3 borradores</div>
                        </div>
                        <div class="action-arrow">→</div>
                    </div>

                    <div class="action-card manager" onclick="location.href='?page=soloylibre-photo-manager'">
                        <div class="action-icon">📁</div>
                        <div class="action-content">
                            <h4>Gestor de Fotos</h4>
                            <p>Organiza y categoriza tus fotos</p>
                            <div class="action-stats">15 sin categorizar</div>
                        </div>
                        <div class="action-arrow">→</div>
                    </div>

                    <div class="action-card ai" onclick="location.href='?page=soloylibre-smart-selection'">
                        <div class="action-icon">🤖</div>
                        <div class="action-content">
                            <h4>Selección IA</h4>
                            <p>Inteligencia artificial para fotos</p>
                            <div class="action-stats">92% precisión</div>
                        </div>
                        <div class="action-arrow">→</div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity Feed -->
            <div class="activity-feed">
                <h3>📈 Actividad Reciente</h3>
                <div class="activity-list" id="recent-activity">
                    <div class="activity-item">
                        <div class="activity-icon success">📝</div>
                        <div class="activity-content">
                            <strong>Post creado</strong>
                            <span>Colección Fotográfica - JoseTusabe Photography</span>
                            <time>hace 2 horas</time>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon info">📷</div>
                        <div class="activity-content">
                            <strong>15 fotos subidas</strong>
                            <span>Nuevas fotos de República Dominicana</span>
                            <time>hace 4 horas</time>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon warning">💖</div>
                        <div class="activity-content">
                            <strong>50 nuevos likes</strong>
                            <span>En fotos de San José de Ocoa</span>
                            <time>hace 6 horas</time>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Initialize enhanced dashboard
            initEnhancedDashboard();

            function initEnhancedDashboard() {
                loadDashboardStats();
                initCharts();
                initAnimations();
                initInteractions();
            }

            function loadDashboardStats() {
                $.post(soloylibreGlass.ajax_url, {
                    action: 'glass_get_stats',
                    nonce: soloylibreGlass.nonce,
                    type: 'enhanced_dashboard'
                }, function(response) {
                    if (response.success) {
                        const data = response.data;

                        // Animate numbers
                        animateNumber('#total-photos', data.total_photos || 0);
                        animateNumber('#published-photos', data.published_photos || 0);
                        animateNumber('#private-photos', data.private_photos || 0);
                        animateNumber('#total-views', data.total_views || 0);
                        animateNumber('#total-likes', data.total_likes || 0);
                        animateNumber('#ai-score', data.ai_score || 85);

                        // Update mini charts
                        updateMiniCharts(data);

                        // Update main charts
                        updateMainCharts(data);

                        // Update activity feed
                        updateActivityFeed(data.recent_activity || []);
                    }
                });
            }

            function animateNumber(selector, targetValue) {
                const element = $(selector);
                const startValue = 0;
                const duration = 2000;
                const startTime = Date.now();

                function updateNumber() {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // Easing function
                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                    const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);

                    element.text(currentValue.toLocaleString());

                    if (progress < 1) {
                        requestAnimationFrame(updateNumber);
                    }
                }

                updateNumber();
            }

            function initCharts() {
                // Chart.js default configuration
                Chart.defaults.color = 'rgba(255, 255, 255, 0.8)';
                Chart.defaults.borderColor = 'rgba(255, 255, 255, 0.1)';
                Chart.defaults.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            }

            function updateMiniCharts(data) {
                // Mini sparkline charts for stat cards
                const chartOptions = {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: {
                        point: { radius: 0 },
                        line: { borderWidth: 2 }
                    }
                };

                // Generate sample data for mini charts
                const generateSparklineData = (trend = 'up') => {
                    const data = [];
                    for (let i = 0; i < 7; i++) {
                        const base = 50;
                        const variation = trend === 'up' ? i * 5 + Math.random() * 10 :
                                        trend === 'down' ? (7-i) * 5 + Math.random() * 10 :
                                        Math.random() * 20;
                        data.push(base + variation);
                    }
                    return data;
                };

                // Total Photos Chart
                new Chart(document.getElementById('totalPhotosChart'), {
                    type: 'line',
                    data: {
                        labels: ['', '', '', '', '', '', ''],
                        datasets: [{
                            data: generateSparklineData('up'),
                            borderColor: '#007bff',
                            backgroundColor: 'rgba(0, 123, 255, 0.1)',
                            fill: true
                        }]
                    },
                    options: chartOptions
                });

                // Published Photos Chart
                new Chart(document.getElementById('publishedPhotosChart'), {
                    type: 'line',
                    data: {
                        labels: ['', '', '', '', '', '', ''],
                        datasets: [{
                            data: generateSparklineData('up'),
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            fill: true
                        }]
                    },
                    options: chartOptions
                });

                // Private Photos Chart
                new Chart(document.getElementById('privatePhotosChart'), {
                    type: 'line',
                    data: {
                        labels: ['', '', '', '', '', '', ''],
                        datasets: [{
                            data: generateSparklineData('stable'),
                            borderColor: '#ffc107',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            fill: true
                        }]
                    },
                    options: chartOptions
                });

                // Views Chart
                new Chart(document.getElementById('viewsChart'), {
                    type: 'line',
                    data: {
                        labels: ['', '', '', '', '', '', ''],
                        datasets: [{
                            data: generateSparklineData('up'),
                            borderColor: '#17a2b8',
                            backgroundColor: 'rgba(23, 162, 184, 0.1)',
                            fill: true
                        }]
                    },
                    options: chartOptions
                });

                // Likes Chart
                new Chart(document.getElementById('likesChart'), {
                    type: 'line',
                    data: {
                        labels: ['', '', '', '', '', '', ''],
                        datasets: [{
                            data: generateSparklineData('up'),
                            borderColor: '#dc3545',
                            backgroundColor: 'rgba(220, 53, 69, 0.1)',
                            fill: true
                        }]
                    },
                    options: chartOptions
                });

                // AI Score Chart (gauge-like)
                new Chart(document.getElementById('aiScoreChart'), {
                    type: 'doughnut',
                    data: {
                        datasets: [{
                            data: [data.ai_score || 85, 100 - (data.ai_score || 85)],
                            backgroundColor: ['#6f42c1', 'rgba(255, 255, 255, 0.1)'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '70%',
                        plugins: { legend: { display: false } }
                    }
                });
            }

            function updateMainCharts(data) {
                // Activity Chart
                const activityCtx = document.getElementById('activityChart');
                if (activityCtx) {
                    new Chart(activityCtx, {
                        type: 'line',
                        data: {
                            labels: ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'],
                            datasets: [
                                {
                                    label: 'Fotos Subidas',
                                    data: [12, 19, 8, 15, 22, 18, 25],
                                    borderColor: '#007bff',
                                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                                    fill: true,
                                    tension: 0.4
                                },
                                {
                                    label: 'Posts Creados',
                                    data: [2, 3, 1, 4, 2, 3, 5],
                                    borderColor: '#28a745',
                                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                                    fill: true,
                                    tension: 0.4
                                },
                                {
                                    label: 'Likes Recibidos',
                                    data: [45, 52, 38, 67, 73, 61, 89],
                                    borderColor: '#dc3545',
                                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                                    fill: true,
                                    tension: 0.4
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: true,
                            aspectRatio: 2,
                            animation: {
                                duration: 1000
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                    labels: {
                                        color: 'rgba(255, 255, 255, 0.8)',
                                        font: { size: 12 }
                                    }
                                }
                            },
                            scales: {
                                x: {
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                    ticks: {
                                        color: 'rgba(255, 255, 255, 0.8)',
                                        font: { size: 11 }
                                    }
                                },
                                y: {
                                    grid: { color: 'rgba(255, 255, 255, 0.1)' },
                                    ticks: {
                                        color: 'rgba(255, 255, 255, 0.8)',
                                        font: { size: 11 }
                                    }
                                }
                            }
                        }
                    });
                }

                // Distribution Chart
                const distributionCtx = document.getElementById('distributionChart');
                if (distributionCtx) {
                    new Chart(distributionCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Publicadas', 'Privadas', 'Disponibles', 'No Deseadas'],
                            datasets: [{
                                data: [
                                    data.published_photos || 0,
                                    data.private_photos || 0,
                                    data.available_photos || 0,
                                    data.unwanted_photos || 0
                                ],
                                backgroundColor: [
                                    'rgba(40, 167, 69, 0.8)',
                                    'rgba(255, 193, 7, 0.8)',
                                    'rgba(0, 123, 255, 0.8)',
                                    'rgba(220, 53, 69, 0.8)'
                                ],
                                borderColor: [
                                    '#28a745',
                                    '#ffc107',
                                    '#007bff',
                                    '#dc3545'
                                ],
                                borderWidth: 2
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        color: 'rgba(255, 255, 255, 0.8)',
                                        padding: 20
                                    }
                                }
                            }
                        }
                    });
                }
            }

            function updateActivityFeed(activities) {
                const activityList = $('#recent-activity');
                if (activities.length === 0) {
                    // Add default activities if none provided
                    activities = [
                        {
                            type: 'success',
                            icon: '📝',
                            title: 'Post creado',
                            description: 'Colección Fotográfica - JoseTusabe Photography',
                            time: 'hace 2 horas'
                        },
                        {
                            type: 'info',
                            icon: '📷',
                            title: '15 fotos subidas',
                            description: 'Nuevas fotos de República Dominicana',
                            time: 'hace 4 horas'
                        },
                        {
                            type: 'warning',
                            icon: '💖',
                            title: '50 nuevos likes',
                            description: 'En fotos de San José de Ocoa',
                            time: 'hace 6 horas'
                        },
                        {
                            type: 'success',
                            icon: '🤖',
                            title: 'IA mejorada',
                            description: 'Algoritmo de selección actualizado',
                            time: 'hace 1 día'
                        }
                    ];
                }

                let html = '';
                activities.forEach((activity, index) => {
                    html += `
                        <div class="activity-item" style="animation-delay: ${index * 0.1}s">
                            <div class="activity-icon ${activity.type}">
                                ${activity.icon}
                            </div>
                            <div class="activity-content">
                                <strong>${activity.title}</strong>
                                <span>${activity.description}</span>
                                <time>${activity.time}</time>
                            </div>
                        </div>
                    `;
                });

                activityList.html(html);
            }

            function initAnimations() {
                // Staggered animation for stat cards
                document.querySelectorAll('.enhanced-stat-card').forEach((card, index) => {
                    card.style.animationDelay = `${index * 0.1}s`;
                    card.style.animation = 'slideInUp 0.6s ease forwards';
                });
            }

            function initInteractions() {
                // Chart period buttons
                $('.chart-btn').on('click', function() {
                    $('.chart-btn').removeClass('active');
                    $(this).addClass('active');

                    const period = $(this).data('period');
                    console.log('Loading data for period:', period);
                });

                // Auto-refresh dashboard every 5 minutes
                setInterval(loadDashboardStats, 5 * 60 * 1000);
            }

            // Add CSS animations
            $('<style>').text(`
                @keyframes slideInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .enhanced-stat-card {
                    opacity: 0;
                    animation-fill-mode: forwards;
                }
            `).appendTo('head');
        });
        </script>
        <?php
    }

    /**
     * Render Quick Posts Page
     */
    public function render_quick_posts() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>⚡ Posts Rápidos</h1>
                <p>🇩🇴 JoseTusabe Photography - Crea posts automáticamente con vista previa de seguridad</p>
            </div>

            <div class="glass-card">
                <h3>🎯 Configuración del Post</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <label>📊 Número de fotos:</label>
                        <input type="number" id="quick-photo-count" class="glass-input" value="15" min="5" max="50">
                    </div>
                    <div>
                        <label>🎲 Tipo de selección:</label>
                        <select id="quick-selection-type" class="glass-input">
                            <option value="random">Aleatorio</option>
                            <option value="recent">Más recientes</option>
                            <option value="popular">Más populares</option>
                            <option value="quality">Mejor calidad</option>
                        </select>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <label>📝 Título del post:</label>
                    <input type="text" id="quick-post-title" class="glass-input" placeholder="AutoPost - JoseTusabe Photography" value="Colección Fotográfica - JoseTusabe Photography">
                </div>

                <div style="margin: 20px 0;">
                    <label>📖 Descripción:</label>
                    <textarea id="quick-post-content" class="glass-input" rows="3" placeholder="Descripción del post...">Nueva colección de fotografías profesionales de JoseTusabe Photography, capturando la belleza de República Dominicana y momentos únicos.</textarea>
                </div>

                <div style="margin: 20px 0;">
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <input type="checkbox" id="quick-exclude-published" checked>
                        Excluir fotos ya publicadas
                    </label>
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <input type="checkbox" id="quick-exclude-private" checked>
                        Excluir fotos privadas
                    </label>
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <input type="checkbox" id="quick-auto-feature" checked>
                        Imagen destacada automática
                    </label>
                </div>

                <div style="text-align: center; margin: 20px 0;">
                    <button class="glass-button primary" onclick="loadQuickPreview()">👁️ Vista Previa de Seguridad</button>
                    <button class="glass-button warning" onclick="generateNewSelection()">🔄 Nueva Selección</button>
                </div>
            </div>

            <div id="quick-preview" class="glass-card" style="display: none;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>🔍 Vista Previa de Seguridad</h3>
                    <div>
                        <span id="selected-photos-count">0</span> fotos seleccionadas
                    </div>
                </div>

                <div style="background: rgba(255, 193, 7, 0.2); padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ffc107;">
                    <strong>⚠️ IMPORTANTE:</strong> Revisa cuidadosamente todas las fotos antes de publicar.
                    Haz clic en cualquier foto que NO debe publicarse para removerla de la selección.
                </div>

                <div id="preview-photos-grid" class="glass-photo-grid"></div>

                <div style="text-align: center; margin: 20px 0; padding: 20px; background: rgba(255, 255, 255, 0.1); border-radius: 10px;">
                    <div style="margin-bottom: 15px;">
                        <strong>📝 Título:</strong> <span id="preview-title">-</span><br>
                        <strong>📖 Descripción:</strong> <span id="preview-content">-</span>
                    </div>
                    <button class="glass-button success" onclick="confirmAndCreatePost()" id="create-post-btn" disabled>
                        ✅ Confirmar y Crear Post
                    </button>
                    <button class="glass-button" onclick="editSelection()">
                        ✏️ Editar Selección
                    </button>
                </div>
            </div>

            <div id="quick-notifications"></div>
        </div>

        <script>
        let selectedQuickPhotos = [];
        let allQuickPhotos = [];

        function loadQuickPreview() {
            const photoCount = $('#quick-photo-count').val();
            const selectionType = $('#quick-selection-type').val();
            const excludePublished = $('#quick-exclude-published').is(':checked');
            const excludePrivate = $('#quick-exclude-private').is(':checked');
            const title = $('#quick-post-title').val();
            const content = $('#quick-post-content').val();

            if (!title.trim()) {
                showQuickNotification('❌ Por favor ingresa un título para el post', 'error');
                return;
            }

            $('#preview-photos-grid').html('⏳ Cargando fotos para vista previa...');
            $('#quick-preview').show();
            $('#preview-title').text(title);
            $('#preview-content').text(content);

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_load_photos',
                nonce: soloylibreGlass.nonce,
                count: photoCount,
                type: selectionType,
                exclude_published: excludePublished,
                exclude_private: excludePrivate,
                filter: 'available'
            }, function(response) {
                if (response.success) {
                    allQuickPhotos = response.data.photos;
                    selectedQuickPhotos = allQuickPhotos.map(p => p.id);
                    displayQuickPhotos();
                    updateQuickSelectionCount();
                    $('#create-post-btn').prop('disabled', false);
                    showQuickNotification(`✅ ${allQuickPhotos.length} fotos cargadas para revisión`, 'success');
                } else {
                    $('#preview-photos-grid').html('❌ Error cargando fotos: ' + (response.data || 'Error desconocido'));
                    showQuickNotification('❌ Error cargando fotos', 'error');
                }
            });
        }

        function displayQuickPhotos() {
            let html = '';
            allQuickPhotos.forEach(photo => {
                const isSelected = selectedQuickPhotos.includes(photo.id);
                const selectedClass = isSelected ? 'selected' : 'removed';
                const statusIcon = isSelected ? '✅' : '❌';
                const statusText = isSelected ? 'Incluir' : 'EXCLUIDA';

                html += `
                    <div class="quick-photo-item ${selectedClass}" data-photo-id="${photo.id}" onclick="toggleQuickPhoto(${photo.id})">
                        <img src="${photo.thumbnail}" alt="${photo.title}" loading="lazy">
                        <div class="quick-photo-overlay">
                            <div class="quick-photo-status">${statusIcon} ${statusText}</div>
                        </div>
                        <div class="quick-photo-info">
                            <div class="photo-title">${photo.title}</div>
                            <div class="photo-status">${photo.status || 'Disponible'}</div>
                            ${photo.like_count ? `<div class="photo-likes">💖 ${photo.like_count}</div>` : ''}
                        </div>
                    </div>
                `;
            });
            $('#preview-photos-grid').html(html);
        }

        function toggleQuickPhoto(photoId) {
            const index = selectedQuickPhotos.indexOf(photoId);
            if (index > -1) {
                selectedQuickPhotos.splice(index, 1);
                showQuickNotification('📷 Foto excluida del post', 'info');
            } else {
                selectedQuickPhotos.push(photoId);
                showQuickNotification('📷 Foto incluida en el post', 'success');
            }
            displayQuickPhotos();
            updateQuickSelectionCount();

            // Disable create button if no photos selected
            $('#create-post-btn').prop('disabled', selectedQuickPhotos.length === 0);
        }

        function updateQuickSelectionCount() {
            $('#selected-photos-count').text(selectedQuickPhotos.length);
        }

        function generateNewSelection() {
            if (confirm('¿Generar una nueva selección de fotos? Esto reemplazará la selección actual.')) {
                loadQuickPreview();
            }
        }

        function editSelection() {
            showQuickNotification('💡 Haz clic en las fotos para incluir/excluir del post', 'info');
        }

        function confirmAndCreatePost() {
            if (selectedQuickPhotos.length === 0) {
                showQuickNotification('❌ Debes seleccionar al menos una foto', 'error');
                return;
            }

            const title = $('#quick-post-title').val();
            const content = $('#quick-post-content').val();
            const autoFeature = $('#quick-auto-feature').is(':checked');

            if (!confirm(`¿Crear post con ${selectedQuickPhotos.length} fotos seleccionadas?`)) {
                return;
            }

            $('#create-post-btn').prop('disabled', true).text('⏳ Creando post...');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_create_quick_post',
                nonce: soloylibreGlass.nonce,
                photo_ids: selectedQuickPhotos,
                title: title,
                content: content,
                auto_feature_image: autoFeature
            }, function(response) {
                if (response.success) {
                    showQuickNotification('🎉 ¡Post creado exitosamente!', 'success');

                    // Show success details
                    const successHtml = `
                        <div style="background: rgba(40, 167, 69, 0.2); padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
                            <h3 style="color: #28a745; margin: 0 0 15px 0;">🎉 ¡Post Creado Exitosamente!</h3>
                            <p><strong>📝 Título:</strong> ${title}</p>
                            <p><strong>📷 Fotos incluidas:</strong> ${selectedQuickPhotos.length}</p>
                            <p><strong>🆔 ID del post:</strong> ${response.data.post_id}</p>
                            <div style="margin-top: 20px;">
                                <a href="${response.data.edit_url}" target="_blank" class="glass-button success">✏️ Editar Post</a>
                                <button onclick="resetQuickForm()" class="glass-button primary">🔄 Crear Otro Post</button>
                            </div>
                        </div>
                    `;

                    $('#preview-photos-grid').html(successHtml);

                    // Auto-open post editor after delay
                    setTimeout(() => {
                        window.open(response.data.edit_url, '_blank');
                    }, 2000);

                } else {
                    showQuickNotification('❌ Error: ' + (response.data || 'Error desconocido'), 'error');
                    $('#create-post-btn').prop('disabled', false).text('✅ Confirmar y Crear Post');
                }
            }).fail(function() {
                showQuickNotification('❌ Error de conexión', 'error');
                $('#create-post-btn').prop('disabled', false).text('✅ Confirmar y Crear Post');
            });
        }

        function resetQuickForm() {
            $('#quick-preview').hide();
            $('#quick-post-title').val('Colección Fotográfica - JoseTusabe Photography');
            $('#quick-post-content').val('Nueva colección de fotografías profesionales de JoseTusabe Photography, capturando la belleza de República Dominicana y momentos únicos.');
            selectedQuickPhotos = [];
            allQuickPhotos = [];
            $('#create-post-btn').prop('disabled', true).text('✅ Confirmar y Crear Post');
            showQuickNotification('🔄 Formulario reseteado', 'info');
        }

        function showQuickNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#quick-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }

        // Add CSS for quick photos
        $('<style>').text(`
            .quick-photo-item {
                position: relative;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 10px;
                overflow: hidden;
                cursor: pointer;
                transition: all 0.3s ease;
                border: 2px solid transparent;
            }

            .quick-photo-item:hover {
                transform: scale(1.05);
                background: rgba(255, 255, 255, 0.2);
            }

            .quick-photo-item.selected {
                border-color: #28a745;
                background: rgba(40, 167, 69, 0.2);
            }

            .quick-photo-item.removed {
                border-color: #dc3545;
                background: rgba(220, 53, 69, 0.2);
                opacity: 0.7;
            }

            .quick-photo-overlay {
                position: absolute;
                top: 5px;
                right: 5px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 4px 8px;
                border-radius: 15px;
                font-size: 10px;
                font-weight: bold;
                z-index: 10;
            }

            .quick-photo-item img {
                width: 100%;
                height: 120px;
                object-fit: cover;
                display: block;
            }

            .quick-photo-info {
                padding: 8px;
                font-size: 11px;
                text-align: center;
            }

            .photo-likes {
                font-size: 10px;
                color: #ff6b6b;
                margin-top: 2px;
            }
        `).appendTo('head');
        </script>
        <?php
    }

    /**
     * Render Advanced Posts Page
     */
    public function render_advanced_posts() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>🎯 Posts Avanzados</h1>
                <p>Control total sobre la creación de posts</p>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>📊 Configuración del Post</h3>
                    <input type="text" id="advanced-title" class="glass-input" placeholder="Título del post">
                    <textarea id="advanced-content" class="glass-input" rows="4" placeholder="Contenido del post..."></textarea>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
                        <select id="advanced-category" class="glass-input">
                            <option value="">Seleccionar categoría</option>
                            <option value="photography">Fotografía</option>
                            <option value="portfolio">Portfolio</option>
                            <option value="events">Eventos</option>
                            <option value="nature">Naturaleza</option>
                        </select>

                        <select id="advanced-status" class="glass-input">
                            <option value="draft">Borrador</option>
                            <option value="publish">Publicar</option>
                            <option value="private">Privado</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <input type="text" id="advanced-tags" class="glass-input" placeholder="Tags (separados por comas)">
                    </div>
                </div>

                <div class="glass-card">
                    <h3>🖼️ Selección de Fotos</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <input type="number" id="advanced-photo-count" class="glass-input" value="15" min="1" max="100" placeholder="Número de fotos">
                        <select id="advanced-selection-type" class="glass-input">
                            <option value="random">Aleatorio</option>
                            <option value="recent">Más recientes</option>
                            <option value="popular">Más populares</option>
                            <option value="manual">Selección manual</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="advanced-featured-image">
                            Establecer imagen destacada automáticamente
                        </label>
                    </div>

                    <button class="glass-button primary" onclick="loadAdvancedPhotos()">🔄 Cargar Fotos</button>
                </div>
            </div>

            <div id="advanced-photo-selection" class="glass-card" style="display: none;">
                <h3>📷 Fotos Seleccionadas</h3>
                <div id="advanced-photos-grid" class="glass-photo-grid"></div>
                <div style="text-align: center; margin: 20px 0;">
                    <button class="glass-button success" onclick="createAdvancedPost()">🎯 Crear Post Avanzado</button>
                </div>
            </div>

            <div id="advanced-notifications"></div>
        </div>

        <script>
        let selectedAdvancedPhotos = [];

        function loadAdvancedPhotos() {
            const count = $('#advanced-photo-count').val();
            const type = $('#advanced-selection-type').val();

            $('#advanced-photos-grid').html('⏳ Cargando fotos...');
            $('#advanced-photo-selection').show();

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_load_photos',
                nonce: soloylibreGlass.nonce,
                count: count,
                type: type
            }, function(response) {
                if (response.success) {
                    selectedAdvancedPhotos = response.data.photos;
                    displayAdvancedPhotos();
                } else {
                    $('#advanced-photos-grid').html('❌ Error cargando fotos');
                }
            });
        }

        function displayAdvancedPhotos() {
            let html = '';
            selectedAdvancedPhotos.forEach((photo, index) => {
                html += `
                    <div class="glass-photo-item" data-photo-id="${photo.id}">
                        <img src="${photo.thumbnail}" alt="${photo.title}">
                        <div class="glass-photo-info">
                            ${photo.title}
                            <button onclick="removeAdvancedPhoto(${index})" style="background: rgba(220,53,69,0.8); border: none; color: white; border-radius: 3px; padding: 2px 6px; margin-left: 5px;">×</button>
                        </div>
                    </div>
                `;
            });
            $('#advanced-photos-grid').html(html);
        }

        function removeAdvancedPhoto(index) {
            selectedAdvancedPhotos.splice(index, 1);
            displayAdvancedPhotos();
        }

        function createAdvancedPost() {
            const title = $('#advanced-title').val();
            const content = $('#advanced-content').val();
            const category = $('#advanced-category').val();
            const status = $('#advanced-status').val();
            const tags = $('#advanced-tags').val();
            const featuredImage = $('#advanced-featured-image').is(':checked');

            if (!title || selectedAdvancedPhotos.length === 0) {
                showAdvancedNotification('❌ Por favor completa el título y selecciona fotos', 'error');
                return;
            }

            showAdvancedNotification('⏳ Creando post avanzado...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_create_advanced_post',
                nonce: soloylibreGlass.nonce,
                title: title,
                content: content,
                category: category,
                status: status,
                tags: tags,
                featured_image: featuredImage,
                photos: selectedAdvancedPhotos.map(p => p.id)
            }, function(response) {
                if (response.success) {
                    showAdvancedNotification('✅ Post avanzado creado exitosamente!', 'success');
                    setTimeout(() => {
                        window.open(response.data.edit_url, '_blank');
                    }, 1000);
                } else {
                    showAdvancedNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function showAdvancedNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#advanced-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }

    /**
     * Render Photo Manager Page
     */
    public function render_photo_manager() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>📁 Gestor de Fotos</h1>
                <p>Organiza y gestiona todas tus fotos</p>
            </div>

            <div class="glass-card">
                <h3>🔍 Filtros y Acciones</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <select id="photo-filter" class="glass-input" onchange="loadPhotos()">
                        <option value="all">Todas las fotos</option>
                        <option value="published">Publicadas</option>
                        <option value="private">Privadas</option>
                        <option value="unwanted">No deseadas</option>
                        <option value="available">Disponibles</option>
                    </select>

                    <select id="photo-sort" class="glass-input" onchange="loadPhotos()">
                        <option value="date_desc">Más recientes</option>
                        <option value="date_asc">Más antiguas</option>
                        <option value="name_asc">Nombre A-Z</option>
                        <option value="name_desc">Nombre Z-A</option>
                        <option value="random">Aleatorio</option>
                    </select>

                    <input type="number" id="photo-limit" class="glass-input" value="50" min="10" max="200" placeholder="Límite">

                    <button class="glass-button primary" onclick="loadPhotos()">🔄 Cargar</button>
                </div>

                <div style="margin: 15px 0; text-align: center;">
                    <button class="glass-button success" onclick="selectAllPhotos()">✅ Seleccionar Todas</button>
                    <button class="glass-button warning" onclick="deselectAllPhotos()">❌ Deseleccionar</button>
                    <button class="glass-button primary" onclick="markAsPrivate()">🔒 Marcar Privadas</button>
                    <button class="glass-button" onclick="markAsUnwanted()">🗑️ No Deseadas</button>
                </div>
            </div>

            <div class="glass-card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3>📷 Fotos (<span id="photo-count">0</span>)</h3>
                    <div>
                        <span id="selected-count">0</span> seleccionadas
                    </div>
                </div>
                <div id="photos-grid" class="glass-photo-grid">
                    <div class="glass-loading">⏳ Carga fotos usando los filtros arriba</div>
                </div>
            </div>

            <div id="manager-notifications"></div>
        </div>

        <script>
        let selectedPhotos = [];
        let allPhotos = [];

        function loadPhotos() {
            const filter = $('#photo-filter').val();
            const sort = $('#photo-sort').val();
            const limit = $('#photo-limit').val();

            $('#photos-grid').html('<div class="glass-loading">⏳ Cargando fotos...</div>');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_load_photos',
                nonce: soloylibreGlass.nonce,
                filter: filter,
                sort: sort,
                limit: limit,
                type: 'manager'
            }, function(response) {
                if (response.success) {
                    allPhotos = response.data.photos;
                    displayPhotos();
                    $('#photo-count').text(allPhotos.length);
                } else {
                    $('#photos-grid').html('❌ Error cargando fotos');
                }
            });
        }

        function displayPhotos() {
            let html = '';
            allPhotos.forEach(photo => {
                const isSelected = selectedPhotos.includes(photo.id);
                html += `
                    <div class="glass-photo-item ${isSelected ? 'selected' : ''}" data-photo-id="${photo.id}" onclick="togglePhotoSelection(${photo.id})">
                        <img src="${photo.thumbnail}" alt="${photo.title}">
                        <div class="glass-photo-info">
                            <div>${photo.title}</div>
                            <div style="font-size: 10px; opacity: 0.8;">${photo.status || 'Disponible'}</div>
                            ${isSelected ? '<div style="position: absolute; top: 5px; right: 5px; background: rgba(40,167,69,0.9); color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px;">✓</div>' : ''}
                        </div>
                    </div>
                `;
            });
            $('#photos-grid').html(html);
            updateSelectedCount();
        }

        function togglePhotoSelection(photoId) {
            const index = selectedPhotos.indexOf(photoId);
            if (index > -1) {
                selectedPhotos.splice(index, 1);
            } else {
                selectedPhotos.push(photoId);
            }
            displayPhotos();
        }

        function selectAllPhotos() {
            selectedPhotos = allPhotos.map(p => p.id);
            displayPhotos();
            showManagerNotification(`✅ ${selectedPhotos.length} fotos seleccionadas`, 'success');
        }

        function deselectAllPhotos() {
            selectedPhotos = [];
            displayPhotos();
            showManagerNotification('❌ Selección limpiada', 'info');
        }

        function updateSelectedCount() {
            $('#selected-count').text(selectedPhotos.length);
        }

        function markAsPrivate() {
            if (selectedPhotos.length === 0) {
                showManagerNotification('❌ Selecciona fotos primero', 'error');
                return;
            }

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_bulk_action',
                nonce: soloylibreGlass.nonce,
                action_type: 'mark_private',
                photo_ids: selectedPhotos
            }, function(response) {
                if (response.success) {
                    showManagerNotification(`✅ ${selectedPhotos.length} fotos marcadas como privadas`, 'success');
                    loadPhotos();
                    selectedPhotos = [];
                } else {
                    showManagerNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function markAsUnwanted() {
            if (selectedPhotos.length === 0) {
                showManagerNotification('❌ Selecciona fotos primero', 'error');
                return;
            }

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_bulk_action',
                nonce: soloylibreGlass.nonce,
                action_type: 'mark_unwanted',
                photo_ids: selectedPhotos
            }, function(response) {
                if (response.success) {
                    showManagerNotification(`✅ ${selectedPhotos.length} fotos marcadas como no deseadas`, 'success');
                    loadPhotos();
                    selectedPhotos = [];
                } else {
                    showManagerNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function showManagerNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#manager-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }

        // Add selected style
        $('<style>').text(`
            .glass-photo-item.selected {
                background: rgba(40, 167, 69, 0.3) !important;
                border: 2px solid rgba(40, 167, 69, 0.8) !important;
            }
        `).appendTo('head');
        </script>
        <?php
    }

    /**
     * Render Statistics Page
     */
    public function render_statistics() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>📊 Estadísticas</h1>
                <p>Análisis completo de tu biblioteca fotográfica</p>
            </div>

            <div class="glass-stats" id="main-stats">
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-total">-</div>
                    <div class="glass-stat-label">📷 Total Fotos</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-published">-</div>
                    <div class="glass-stat-label">📝 Publicadas</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-private">-</div>
                    <div class="glass-stat-label">🔒 Privadas</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-unwanted">-</div>
                    <div class="glass-stat-label">🗑️ No Deseadas</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-available">-</div>
                    <div class="glass-stat-label">✨ Disponibles</div>
                </div>
                <div class="glass-stat-card">
                    <div class="glass-stat-number" id="stat-views">-</div>
                    <div class="glass-stat-label">👁️ Visualizaciones</div>
                </div>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>📈 Métricas de Publicación</h3>
                    <div id="publication-metrics">
                        <div style="margin: 10px 0;">
                            <strong>Tasa de publicación:</strong> <span id="publication-rate">-</span>%
                        </div>
                        <div style="margin: 10px 0;">
                            <strong>Fotos por post (promedio):</strong> <span id="avg-photos-per-post">-</span>
                        </div>
                        <div style="margin: 10px 0;">
                            <strong>Posts creados:</strong> <span id="total-posts">-</span>
                        </div>
                    </div>
                </div>

                <div class="glass-card">
                    <h3>🎯 Acciones Rápidas</h3>
                    <button class="glass-button primary" onclick="refreshStats()">🔄 Actualizar Estadísticas</button>
                    <button class="glass-button success" onclick="exportStats()">📊 Exportar Datos</button>
                    <button class="glass-button warning" onclick="resetPublishedPhotos()">🔄 Reset Publicadas</button>
                    <button class="glass-button" onclick="generateReport()">📋 Generar Reporte</button>
                </div>
            </div>

            <div class="glass-card">
                <h3>📅 Actividad Reciente</h3>
                <div id="recent-activity">
                    <div class="glass-loading">⏳ Cargando actividad reciente...</div>
                </div>
            </div>

            <div id="stats-notifications"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            loadStatistics();
            loadRecentActivity();
        });

        function loadStatistics() {
            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'full'
            }, function(response) {
                if (response.success) {
                    const data = response.data;
                    $('#stat-total').text(data.total_photos);
                    $('#stat-published').text(data.published_photos);
                    $('#stat-private').text(data.private_photos);
                    $('#stat-unwanted').text(data.unwanted_photos);
                    $('#stat-available').text(data.available_photos);
                    $('#stat-views').text(data.total_views);

                    $('#publication-rate').text(data.publication_rate);
                    $('#avg-photos-per-post').text(data.avg_photos_per_post);
                    $('#total-posts').text(data.total_posts);
                }
            });
        }

        function loadRecentActivity() {
            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'activity'
            }, function(response) {
                if (response.success && response.data.activities) {
                    let html = '';
                    response.data.activities.forEach(activity => {
                        html += `
                            <div style="background: rgba(255,255,255,0.1); padding: 10px; margin: 5px 0; border-radius: 8px;">
                                <strong>${activity.action}</strong> - ${activity.date}
                                <div style="font-size: 12px; opacity: 0.8;">${activity.details}</div>
                            </div>
                        `;
                    });
                    $('#recent-activity').html(html);
                } else {
                    $('#recent-activity').html('📝 No hay actividad reciente registrada');
                }
            });
        }

        function refreshStats() {
            showStatsNotification('🔄 Actualizando estadísticas...', 'info');
            loadStatistics();
            loadRecentActivity();
            setTimeout(() => {
                showStatsNotification('✅ Estadísticas actualizadas', 'success');
            }, 1000);
        }

        function exportStats() {
            showStatsNotification('📊 Exportando datos...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'export'
            }, function(response) {
                if (response.success) {
                    const dataStr = JSON.stringify(response.data, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'soloylibre_stats_' + new Date().toISOString().split('T')[0] + '.json';
                    link.click();
                    showStatsNotification('✅ Datos exportados exitosamente', 'success');
                } else {
                    showStatsNotification('❌ Error exportando datos', 'error');
                }
            });
        }

        function resetPublishedPhotos() {
            if (!confirm('¿Estás seguro de que quieres resetear todas las fotos publicadas?')) return;

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_bulk_action',
                nonce: soloylibreGlass.nonce,
                action_type: 'reset_published'
            }, function(response) {
                if (response.success) {
                    showStatsNotification('✅ Fotos publicadas reseteadas', 'success');
                    loadStatistics();
                } else {
                    showStatsNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function generateReport() {
            showStatsNotification('📋 Generando reporte...', 'info');
            // Esta función se puede expandir para generar reportes más detallados
            setTimeout(() => {
                showStatsNotification('✅ Reporte generado (función en desarrollo)', 'success');
            }, 2000);
        }

        function showStatsNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#stats-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }

    /**
     * Render Smart Selection Page
     */
    public function render_smart_selection() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>🤖 Selección Inteligente</h1>
                <p>IA para seleccionar las mejores fotos automáticamente</p>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>🎯 Criterios de Selección</h3>
                    <div style="margin: 15px 0;">
                        <label>📊 Número de fotos a seleccionar:</label>
                        <input type="number" id="smart-count" class="glass-input" value="20" min="5" max="100">
                    </div>

                    <div style="margin: 15px 0;">
                        <label>🔍 Tipo de selección:</label>
                        <select id="smart-type" class="glass-input">
                            <option value="quality">Mejor calidad</option>
                            <option value="diversity">Máxima diversidad</option>
                            <option value="recent_quality">Recientes + Calidad</option>
                            <option value="popular">Más populares</option>
                            <option value="balanced">Selección balanceada</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="smart-exclude-published">
                            Excluir fotos ya publicadas
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="smart-exclude-private">
                            Excluir fotos privadas
                        </label>
                    </div>

                    <button class="glass-button primary" onclick="runSmartSelection()">🤖 Ejecutar Selección IA</button>
                </div>

                <div class="glass-card">
                    <h3>⚙️ Configuración Avanzada</h3>
                    <div style="margin: 15px 0;">
                        <label>📅 Rango de fechas:</label>
                        <select id="smart-date-range" class="glass-input">
                            <option value="all">Todas las fechas</option>
                            <option value="last_week">Última semana</option>
                            <option value="last_month">Último mes</option>
                            <option value="last_3months">Últimos 3 meses</option>
                            <option value="last_year">Último año</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label>🏷️ Categoría preferida:</label>
                        <select id="smart-category" class="glass-input">
                            <option value="any">Cualquier categoría</option>
                            <option value="portrait">Retratos</option>
                            <option value="landscape">Paisajes</option>
                            <option value="event">Eventos</option>
                            <option value="nature">Naturaleza</option>
                        </select>
                    </div>

                    <button class="glass-button success" onclick="saveSmartPreferences()">💾 Guardar Preferencias</button>
                </div>
            </div>

            <div id="smart-results" class="glass-card" style="display: none;">
                <h3>🎯 Resultados de Selección IA</h3>
                <div id="smart-photos-grid" class="glass-photo-grid"></div>
                <div style="text-align: center; margin: 20px 0;">
                    <button class="glass-button success" onclick="createPostFromSelection()">📝 Crear Post con Selección</button>
                    <button class="glass-button primary" onclick="refineSelection()">🔄 Refinar Selección</button>
                </div>
            </div>

            <div id="smart-notifications"></div>
        </div>

        <script>
        let smartSelectedPhotos = [];

        function runSmartSelection() {
            const count = $('#smart-count').val();
            const type = $('#smart-type').val();
            const excludePublished = $('#smart-exclude-published').is(':checked');
            const excludePrivate = $('#smart-exclude-private').is(':checked');
            const dateRange = $('#smart-date-range').val();
            const category = $('#smart-category').val();

            showSmartNotification('🤖 Ejecutando selección inteligente...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_smart_select',
                nonce: soloylibreGlass.nonce,
                count: count,
                type: type,
                exclude_published: excludePublished,
                exclude_private: excludePrivate,
                date_range: dateRange,
                category: category
            }, function(response) {
                if (response.success) {
                    smartSelectedPhotos = response.data.photos;
                    displaySmartResults();
                    showSmartNotification(`✅ Selección completada: ${smartSelectedPhotos.length} fotos`, 'success');
                } else {
                    showSmartNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function displaySmartResults() {
            let html = '';
            smartSelectedPhotos.forEach((photo, index) => {
                html += `
                    <div class="glass-photo-item" data-photo-id="${photo.id}">
                        <img src="${photo.thumbnail}" alt="${photo.title}">
                        <div class="glass-photo-info">
                            <div>${photo.title}</div>
                            <div style="font-size: 10px; opacity: 0.8;">Score: ${photo.ai_score || 'N/A'}</div>
                            <button onclick="removeFromSmartSelection(${index})" style="background: rgba(220,53,69,0.8); border: none; color: white; border-radius: 3px; padding: 2px 6px; margin-top: 5px;">Remover</button>
                        </div>
                    </div>
                `;
            });
            $('#smart-photos-grid').html(html);
            $('#smart-results').show();
        }

        function removeFromSmartSelection(index) {
            smartSelectedPhotos.splice(index, 1);
            displaySmartResults();
            showSmartNotification('📷 Foto removida de la selección', 'info');
        }

        function createPostFromSelection() {
            if (smartSelectedPhotos.length === 0) {
                showSmartNotification('❌ No hay fotos seleccionadas', 'error');
                return;
            }

            showSmartNotification('📝 Creando post con selección IA...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_create_quick_post',
                nonce: soloylibreGlass.nonce,
                photo_ids: smartSelectedPhotos.map(p => p.id),
                title: 'Selección IA - JoseTusabe Photography',
                content: 'Colección seleccionada automáticamente por inteligencia artificial'
            }, function(response) {
                if (response.success) {
                    showSmartNotification('✅ Post creado exitosamente!', 'success');
                    setTimeout(() => {
                        window.open(response.data.edit_url, '_blank');
                    }, 1000);
                } else {
                    showSmartNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function refineSelection() {
            showSmartNotification('🔄 Refinando selección...', 'info');
            runSmartSelection();
        }

        function saveSmartPreferences() {
            const preferences = {
                default_count: $('#smart-count').val(),
                default_type: $('#smart-type').val(),
                exclude_published: $('#smart-exclude-published').is(':checked'),
                exclude_private: $('#smart-exclude-private').is(':checked'),
                default_date_range: $('#smart-date-range').val(),
                default_category: $('#smart-category').val()
            };

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_save_settings',
                nonce: soloylibreGlass.nonce,
                settings_type: 'smart_preferences',
                settings: preferences
            }, function(response) {
                if (response.success) {
                    showSmartNotification('💾 Preferencias guardadas', 'success');
                } else {
                    showSmartNotification('❌ Error guardando preferencias', 'error');
                }
            });
        }

        function showSmartNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#smart-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }

    /**
     * Render Settings Page
     */
    public function render_settings() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>⚙️ Configuraciones</h1>
                <p>Personaliza tu experiencia con SoloYLibre</p>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>👤 Información del Fotógrafo</h3>
                    <input type="text" id="photographer-name" class="glass-input" placeholder="Jose L Encarnacion" value="Jose L Encarnacion">
                    <input type="text" id="photographer-alias" class="glass-input" placeholder="JoseTusabe" value="JoseTusabe">
                    <input type="text" id="photographer-brand" class="glass-input" placeholder="JoseTusabe Photography" value="JoseTusabe Photography">
                    <input type="email" id="photographer-email" class="glass-input" placeholder="<EMAIL>" value="<EMAIL>">
                    <input type="tel" id="photographer-phone" class="glass-input" placeholder="************" value="************">
                    <input type="text" id="photographer-location" class="glass-input" placeholder="San José de Ocoa, Dom. Rep. / USA" value="San José de Ocoa, Dom. Rep. / USA">
                </div>

                <div class="glass-card">
                    <h3>🌐 Configuraciones de Sitio Web</h3>
                    <input type="url" id="website-main" class="glass-input" placeholder="https://josetusabe.com" value="https://josetusabe.com">
                    <input type="url" id="website-portfolio" class="glass-input" placeholder="https://soloylibre.com" value="https://soloylibre.com">
                    <input type="url" id="website-photo" class="glass-input" placeholder="https://1and1photo.com" value="https://1and1photo.com">
                    <input type="url" id="website-personal" class="glass-input" placeholder="https://joselencarnacion.com" value="https://joselencarnacion.com">
                </div>
            </div>

            <div class="glass-grid">
                <div class="glass-card">
                    <h3>📸 Configuraciones de Galería</h3>
                    <div style="margin: 15px 0;">
                        <label>🖼️ Fotos por página:</label>
                        <input type="number" id="photos-per-page" class="glass-input" value="50" min="10" max="200">
                    </div>

                    <div style="margin: 15px 0;">
                        <label>📐 Estilo de galería por defecto:</label>
                        <select id="default-gallery-style" class="glass-input">
                            <option value="masonry">Masonry</option>
                            <option value="grid">Grid</option>
                            <option value="carousel">Carousel</option>
                            <option value="lightbox">Lightbox</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="auto-feature-image" checked>
                            Imagen destacada automática
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="enable-interactions" checked>
                            Habilitar interacciones (likes, shares)
                        </label>
                    </div>
                </div>

                <div class="glass-card">
                    <h3>🤖 Configuraciones de IA</h3>
                    <div style="margin: 15px 0;">
                        <label>🎯 Algoritmo de selección:</label>
                        <select id="ai-algorithm" class="glass-input">
                            <option value="balanced">Balanceado</option>
                            <option value="quality_focused">Enfocado en calidad</option>
                            <option value="diversity_focused">Enfocado en diversidad</option>
                            <option value="recent_bias">Sesgo hacia recientes</option>
                        </select>
                    </div>

                    <div style="margin: 15px 0;">
                        <label>📊 Peso de popularidad (%):</label>
                        <input type="range" id="popularity-weight" class="glass-input" min="0" max="100" value="30">
                        <span id="popularity-value">30%</span>
                    </div>

                    <div style="margin: 15px 0;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" id="enable-ai-suggestions" checked>
                            Sugerencias automáticas de IA
                        </label>
                    </div>
                </div>
            </div>

            <div class="glass-card">
                <h3>🔧 Acciones del Sistema</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="glass-button success" onclick="saveAllSettings()">💾 Guardar Todo</button>
                    <button class="glass-button primary" onclick="exportSettings()">📤 Exportar Config</button>
                    <button class="glass-button warning" onclick="importSettings()">📥 Importar Config</button>
                    <button class="glass-button" onclick="resetToDefaults()">🔄 Restaurar Defaults</button>
                </div>

                <div style="margin: 20px 0; text-align: center;">
                    <input type="file" id="import-file" accept=".json" style="display: none;" onchange="handleImportFile(this)">
                </div>
            </div>

            <div id="settings-notifications"></div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            loadCurrentSettings();

            // Update popularity weight display
            $('#popularity-weight').on('input', function() {
                $('#popularity-value').text($(this).val() + '%');
            });
        });

        function loadCurrentSettings() {
            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'settings'
            }, function(response) {
                if (response.success && response.data.settings) {
                    const settings = response.data.settings;
                    // Populate form fields with current settings
                    Object.keys(settings).forEach(key => {
                        const element = $('#' + key.replace(/_/g, '-'));
                        if (element.length) {
                            if (element.is(':checkbox')) {
                                element.prop('checked', settings[key]);
                            } else {
                                element.val(settings[key]);
                            }
                        }
                    });
                }
            });
        }

        function saveAllSettings() {
            const settings = {
                photographer_name: $('#photographer-name').val(),
                photographer_alias: $('#photographer-alias').val(),
                photographer_brand: $('#photographer-brand').val(),
                photographer_email: $('#photographer-email').val(),
                photographer_phone: $('#photographer-phone').val(),
                photographer_location: $('#photographer-location').val(),
                website_main: $('#website-main').val(),
                website_portfolio: $('#website-portfolio').val(),
                website_photo: $('#website-photo').val(),
                website_personal: $('#website-personal').val(),
                photos_per_page: $('#photos-per-page').val(),
                default_gallery_style: $('#default-gallery-style').val(),
                auto_feature_image: $('#auto-feature-image').is(':checked'),
                enable_interactions: $('#enable-interactions').is(':checked'),
                ai_algorithm: $('#ai-algorithm').val(),
                popularity_weight: $('#popularity-weight').val(),
                enable_ai_suggestions: $('#enable-ai-suggestions').is(':checked')
            };

            showSettingsNotification('💾 Guardando configuraciones...', 'info');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_save_settings',
                nonce: soloylibreGlass.nonce,
                settings_type: 'all',
                settings: settings
            }, function(response) {
                if (response.success) {
                    showSettingsNotification('✅ Configuraciones guardadas exitosamente', 'success');
                } else {
                    showSettingsNotification('❌ Error: ' + response.data, 'error');
                }
            });
        }

        function exportSettings() {
            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_get_stats',
                nonce: soloylibreGlass.nonce,
                type: 'export_settings'
            }, function(response) {
                if (response.success) {
                    const dataStr = JSON.stringify(response.data, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'soloylibre_settings_' + new Date().toISOString().split('T')[0] + '.json';
                    link.click();
                    showSettingsNotification('📤 Configuraciones exportadas', 'success');
                } else {
                    showSettingsNotification('❌ Error exportando configuraciones', 'error');
                }
            });
        }

        function importSettings() {
            $('#import-file').click();
        }

        function handleImportFile(input) {
            const file = input.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const settings = JSON.parse(e.target.result);

                    $.post(soloylibreGlass.ajax_url, {
                        action: 'glass_save_settings',
                        nonce: soloylibreGlass.nonce,
                        settings_type: 'import',
                        settings: settings
                    }, function(response) {
                        if (response.success) {
                            showSettingsNotification('📥 Configuraciones importadas exitosamente', 'success');
                            loadCurrentSettings();
                        } else {
                            showSettingsNotification('❌ Error importando configuraciones', 'error');
                        }
                    });
                } catch (error) {
                    showSettingsNotification('❌ Archivo de configuración inválido', 'error');
                }
            };
            reader.readAsText(file);
        }

        function resetToDefaults() {
            if (!confirm('¿Estás seguro de que quieres restaurar todas las configuraciones por defecto?')) return;

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_save_settings',
                nonce: soloylibreGlass.nonce,
                settings_type: 'reset'
            }, function(response) {
                if (response.success) {
                    showSettingsNotification('🔄 Configuraciones restauradas a valores por defecto', 'success');
                    loadCurrentSettings();
                } else {
                    showSettingsNotification('❌ Error restaurando configuraciones', 'error');
                }
            });
        }

        function showSettingsNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#settings-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }

    // AJAX Methods

    /**
     * AJAX: Load photos with various filters and options
     */
    public function ajax_load_photos() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $count = intval($_POST['count'] ?? 50);
        $filter = sanitize_text_field($_POST['filter'] ?? 'all');
        $sort = sanitize_text_field($_POST['sort'] ?? 'date_desc');
        $type = sanitize_text_field($_POST['type'] ?? 'preview');
        $limit = intval($_POST['limit'] ?? 50);

        // Handle unlimited selection
        $posts_per_page = ($count > 1000 || $count === 0) ? -1 : $count;
        if ($limit > 0 && $posts_per_page > 0) {
            $posts_per_page = min($posts_per_page, $limit);
        }

        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => $posts_per_page,
            'fields' => 'ids'
        );

        // Apply sorting
        switch ($sort) {
            case 'date_asc':
                $args['orderby'] = 'date';
                $args['order'] = 'ASC';
                break;
            case 'name_asc':
                $args['orderby'] = 'title';
                $args['order'] = 'ASC';
                break;
            case 'name_desc':
                $args['orderby'] = 'title';
                $args['order'] = 'DESC';
                break;
            case 'random':
                $args['orderby'] = 'rand';
                break;
            default: // date_desc
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
        }

        // Apply filters
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        // Handle exclude options from POST
        $exclude_published = $_POST['exclude_published'] === 'true';
        $exclude_private = $_POST['exclude_private'] === 'true';

        switch ($filter) {
            case 'published':
                if (!empty($published_photos)) {
                    $args['post__in'] = $published_photos;
                } else {
                    wp_send_json_success(array('photos' => array()));
                    return;
                }
                break;
            case 'private':
                if (!empty($private_photos)) {
                    $args['post__in'] = $private_photos;
                } else {
                    wp_send_json_success(array('photos' => array()));
                    return;
                }
                break;
            case 'unwanted':
                if (!empty($unwanted_photos)) {
                    $args['post__in'] = $unwanted_photos;
                } else {
                    wp_send_json_success(array('photos' => array()));
                    return;
                }
                break;
            case 'available':
            default:
                // Build exclusion list based on options
                $exclude_ids = array();

                if ($exclude_published) {
                    $exclude_ids = array_merge($exclude_ids, $published_photos);
                }
                if ($exclude_private) {
                    $exclude_ids = array_merge($exclude_ids, $private_photos);
                }

                // Always exclude unwanted photos for safety
                $exclude_ids = array_merge($exclude_ids, $unwanted_photos);

                if (!empty($exclude_ids)) {
                    $args['post__not_in'] = array_unique($exclude_ids);
                }
                break;
        }

        $photo_ids = get_posts($args);
        $photos = array();

        foreach ($photo_ids as $photo_id) {
            // Get current like count
            $like_count = get_post_meta($photo_id, '_soloylibre_likes_count', true);
            if (!$like_count) {
                global $wpdb;
                $table_name = $wpdb->prefix . 'soloylibre_interactions';
                $like_count = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $table_name WHERE photo_id = %d AND reaction_type = 'like'",
                    $photo_id
                ));
                update_post_meta($photo_id, '_soloylibre_likes_count', intval($like_count));
            }

            $photos[] = array(
                'id' => $photo_id,
                'title' => get_the_title($photo_id) ?: 'Photo ' . $photo_id,
                'thumbnail' => wp_get_attachment_image_url($photo_id, 'thumbnail'),
                'full' => wp_get_attachment_image_url($photo_id, 'full'),
                'status' => $this->get_photo_status($photo_id),
                'ai_score' => rand(75, 98), // Simulated AI score
                'like_count' => intval($like_count)
            );
        }

        $requested_count = $count;
        $actual_count = count($photos);
        $message = '';

        // Generate informative message
        if ($requested_count > $actual_count && $actual_count > 0) {
            $message = "Solo quedaban $actual_count fotos disponibles de las $requested_count solicitadas.";
        } elseif ($actual_count === 0) {
            $message = "No se encontraron fotos con los filtros aplicados.";
        } elseif ($actual_count >= $requested_count) {
            $message = "Se cargaron $actual_count fotos exitosamente.";
        }

        wp_send_json_success(array(
            'photos' => $photos,
            'count' => $actual_count,
            'requested' => $requested_count,
            'filter' => $filter,
            'sort' => $sort,
            'message' => $message,
            'has_more' => false // Could be implemented for pagination
        ));
    }

    /**
     * AJAX: Create quick post
     */
    public function ajax_create_quick_post() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $photo_count = intval($_POST['photo_count'] ?? 10);
        $title = sanitize_text_field($_POST['title'] ?? $this->generate_creative_title());
        $content = sanitize_textarea_field($_POST['content'] ?? 'Colección fotográfica de JoseTusabe Photography');
        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());
        $auto_feature_image = $_POST['auto_feature_image'] === 'true';

        // If no specific photo IDs provided, get photos based on filters
        if (empty($photo_ids)) {
            $args = array(
                'post_type' => 'attachment',
                'post_mime_type' => 'image',
                'post_status' => 'inherit',
                'posts_per_page' => $photo_count,
                'orderby' => 'rand',
                'fields' => 'ids'
            );

            // Exclude already published photos
            $published_photos = get_option('soloylibre_published_photos', array());
            $private_photos = get_option('soloylibre_private_photos', array());
            $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

            $exclude_ids = array_merge($published_photos, $private_photos, $unwanted_photos);
            if (!empty($exclude_ids)) {
                $args['post__not_in'] = array_unique($exclude_ids);
            }

            $photo_ids = get_posts($args);
        }

        if (empty($photo_ids)) {
            wp_send_json_error('No hay fotos disponibles para crear el post. Verifica que tengas fotos sin publicar.');
            return;
        }

        // Validate that all photo IDs exist and are images
        $valid_photo_ids = array();
        foreach ($photo_ids as $photo_id) {
            $post = get_post($photo_id);
            if ($post && $post->post_type === 'attachment' && strpos($post->post_mime_type, 'image/') === 0) {
                $valid_photo_ids[] = $photo_id;
            }
        }

        if (empty($valid_photo_ids)) {
            wp_send_json_error('No se encontraron fotos válidas para el post');
            return;
        }

        // Create the post
        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'draft',
            'post_type' => 'post',
            'post_author' => get_current_user_id(),
            'meta_input' => array(
                '_soloylibre_auto_generated' => true,
                '_soloylibre_creation_date' => current_time('mysql'),
                '_soloylibre_photo_count' => count($valid_photo_ids),
                '_soloylibre_photographer' => 'Jose L Encarnacion (JoseTusabe)'
            )
        );

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            wp_send_json_error('Error creando el post: ' . $post_id->get_error_message());
            return;
        }

        // Create enhanced gallery shortcode with JoseTusabe branding
        $gallery_shortcode = '[gallery ids="' . implode(',', $valid_photo_ids) . '" columns="3" size="medium" link="file"]';

        // Enhanced content with photographer signature
        $enhanced_content = $content . "\n\n" . $gallery_shortcode;
        $enhanced_content .= "\n\n---\n";
        $enhanced_content .= "📸 **JoseTusabe Photography**\n";
        $enhanced_content .= "🇩🇴 San José de Ocoa, República Dominicana\n";
        $enhanced_content .= "📧 <EMAIL> | 📱 ************\n";
        $enhanced_content .= "🌐 [josetusabe.com](https://josetusabe.com) | [soloylibre.com](https://soloylibre.com)\n\n";
        $enhanced_content .= "*Fotografía profesional capturando la belleza de República Dominicana y momentos únicos.*";

        // Update post content with enhanced gallery
        wp_update_post(array(
            'ID' => $post_id,
            'post_content' => $enhanced_content
        ));

        // Set featured image (first photo or random if auto_feature_image is true)
        if ($auto_feature_image && !empty($valid_photo_ids)) {
            $featured_image_id = $valid_photo_ids[0]; // Use first photo as featured
            set_post_thumbnail($post_id, $featured_image_id);
        }

        // Add default category
        $photography_cat = get_cat_ID('Fotografía Profesional');
        if (!$photography_cat) {
            $photography_cat = wp_create_category('Fotografía Profesional');
        }
        if ($photography_cat) {
            wp_set_post_categories($post_id, array($photography_cat));
        }

        // Add default tags
        wp_set_post_tags($post_id, 'josetusabe, soloylibre, fotografia-profesional, republica-dominicana, dominican-photography');

        // Mark photos as published
        $published_photos = get_option('soloylibre_published_photos', array());
        $published_photos = array_merge($published_photos, $valid_photo_ids);
        update_option('soloylibre_published_photos', array_unique($published_photos));

        // Update statistics
        $total_posts = get_option('soloylibre_total_posts_created', 0);
        update_option('soloylibre_total_posts_created', $total_posts + 1);
        update_option('soloylibre_last_activity', current_time('mysql'));

        wp_send_json_success(array(
            'post_id' => $post_id,
            'edit_url' => admin_url('post.php?post=' . $post_id . '&action=edit'),
            'view_url' => get_permalink($post_id),
            'photos_used' => count($valid_photo_ids),
            'featured_image_set' => $auto_feature_image,
            'message' => 'Post creado exitosamente con ' . count($valid_photo_ids) . ' fotos'
        ));
    }

    /**
     * AJAX: Get statistics
     */
    public function ajax_get_stats() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $type = sanitize_text_field($_POST['type'] ?? 'dashboard');

        global $wpdb;

        // Get photo counts
        $total_photos = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type LIKE 'image%'
        ");

        $published_photos = count(get_option('soloylibre_published_photos', array()));
        $private_photos = count(get_option('soloylibre_private_photos', array()));
        $unwanted_photos = count(get_option('soloylibre_unwanted_photos', array()));
        $available_photos = $total_photos - $published_photos - $private_photos - $unwanted_photos;

        // Get interaction stats
        $interactions_table = $wpdb->prefix . 'soloylibre_interactions';
        $total_views = $wpdb->get_var("SELECT SUM(interaction_count) FROM $interactions_table WHERE interaction_type = 'view'") ?: 0;
        $total_likes = $wpdb->get_var("SELECT COUNT(*) FROM $interactions_table WHERE reaction_type = 'like'") ?: 0;

        // Calculate AI score
        $ai_score = $this->calculate_ai_score($total_photos, $published_photos, $total_likes);

        $stats = array(
            'total_photos' => intval($total_photos),
            'published_photos' => $published_photos,
            'private_photos' => $private_photos,
            'unwanted_photos' => $unwanted_photos,
            'available_photos' => max(0, $available_photos),
            'total_views' => intval($total_views),
            'total_likes' => intval($total_likes),
            'ai_score' => $ai_score,
            'publication_rate' => $total_photos > 0 ? round(($published_photos / $total_photos) * 100, 1) : 0,
            'avg_photos_per_post' => $published_photos > 0 ? round($published_photos / max(1, wp_count_posts('post')->publish), 1) : 0,
            'total_posts' => wp_count_posts('post')->publish
        );

        // Enhanced data for dashboard
        if ($type === 'enhanced_dashboard') {
            $stats['recent_activity'] = $this->get_recent_activity();
            $stats['trends'] = array(
                'photos_trend' => '+12%',
                'published_trend' => '+8%',
                'views_trend' => '+25%',
                'likes_trend' => '+15%'
            );
            $stats['weekly_activity'] = $this->get_weekly_activity();
        }

        if ($type === 'activity') {
            $stats['activities'] = array(
                array('action' => 'Post creado', 'date' => date('Y-m-d H:i'), 'details' => 'Último post automático'),
                array('action' => 'Fotos categorizadas', 'date' => date('Y-m-d H:i', strtotime('-1 hour')), 'details' => '15 fotos marcadas como privadas'),
                array('action' => 'Estadísticas actualizadas', 'date' => date('Y-m-d H:i', strtotime('-2 hours')), 'details' => 'Recálculo automático')
            );
        }

        wp_send_json_success($stats);
    }

    /**
     * Calculate AI score based on various metrics
     */
    private function calculate_ai_score($total_photos, $published_photos, $total_likes) {
        if ($total_photos == 0) return 0;

        // Base score from photo organization
        $organization_score = min(100, ($published_photos / max(1, $total_photos)) * 100);

        // Engagement score from likes
        $engagement_score = min(100, ($total_likes / max(1, $published_photos)) * 10);

        // Activity score (simulated)
        $activity_score = rand(80, 95);

        // Weighted average
        $ai_score = ($organization_score * 0.4) + ($engagement_score * 0.3) + ($activity_score * 0.3);

        return round($ai_score);
    }

    /**
     * Get recent activity for dashboard
     */
    private function get_recent_activity() {
        global $wpdb;

        $activities = array();

        // Get recent posts
        $recent_posts = $wpdb->get_results("
            SELECT post_title, post_date
            FROM {$wpdb->posts}
            WHERE post_type = 'post'
            AND post_status = 'publish'
            ORDER BY post_date DESC
            LIMIT 3
        ");

        foreach ($recent_posts as $post) {
            $time_diff = human_time_diff(strtotime($post->post_date), current_time('timestamp'));
            $activities[] = array(
                'type' => 'success',
                'icon' => '📝',
                'title' => 'Post publicado',
                'description' => $post->post_title,
                'time' => 'hace ' . $time_diff
            );
        }

        // Add some default activities if none found
        if (empty($activities)) {
            $activities = array(
                array(
                    'type' => 'success',
                    'icon' => '🎉',
                    'title' => 'Sistema iniciado',
                    'description' => 'JoseTusabe Photography Dashboard activo',
                    'time' => 'hace 1 hora'
                ),
                array(
                    'type' => 'info',
                    'icon' => '🤖',
                    'title' => 'IA optimizada',
                    'description' => 'Sistema de selección inteligente mejorado',
                    'time' => 'hace 2 horas'
                )
            );
        }

        return array_slice($activities, 0, 5);
    }

    /**
     * Generate creative album titles
     */
    private function generate_creative_title() {
        $dominican_places = array(
            'San José de Ocoa', 'Santo Domingo', 'Punta Cana', 'Puerto Plata',
            'La Romana', 'Samaná', 'Barahona', 'Monte Cristi', 'Jarabacoa'
        );

        $creative_words = array(
            'Memorias', 'Momentos', 'Instantes', 'Recuerdos', 'Vivencias',
            'Colección', 'Galería', 'Tesoros', 'Joyas', 'Secretos',
            'Aventuras', 'Experiencias', 'Historias', 'Crónicas', 'Relatos'
        );

        $photo_themes = array(
            'Fotográficas', 'Visuales', 'Artísticas', 'Profesionales', 'Únicas',
            'Especiales', 'Exclusivas', 'Selectas', 'Premium', 'Magistrales',
            'Cautivadoras', 'Impresionantes', 'Extraordinarias', 'Sublimes'
        );

        $time_references = array(
            date('Y'), 'Temporada ' . date('Y'), 'Edición ' . date('m/Y'),
            'Serie ' . date('W'), 'Volumen ' . date('j'), 'Capítulo ' . date('z')
        );

        // Generate different title patterns
        $patterns = array(
            '{creative} {theme} de {place} - JoseTusabe',
            '{creative} {theme} - {time}',
            'JoseTusabe: {creative} desde {place}',
            '{creative} Dominicanas - {time}',
            '{place}: {creative} {theme}',
            'Colección {time} - {creative} de {place}'
        );

        $pattern = $patterns[array_rand($patterns)];
        $place = $dominican_places[array_rand($dominican_places)];
        $creative = $creative_words[array_rand($creative_words)];
        $theme = $photo_themes[array_rand($photo_themes)];
        $time = $time_references[array_rand($time_references)];

        $title = str_replace(
            array('{creative}', '{theme}', '{place}', '{time}'),
            array($creative, $theme, $place, $time),
            $pattern
        );

        return $title;
    }

    /**
     * Get weekly activity data for charts
     */
    private function get_weekly_activity() {
        return array(
            'photos_uploaded' => [12, 19, 8, 15, 22, 18, 25],
            'posts_created' => [2, 3, 1, 4, 2, 3, 5],
            'likes_received' => [45, 52, 38, 67, 73, 61, 89],
            'views_count' => [120, 150, 98, 180, 210, 165, 245]
        );
    }

    /**
     * AJAX: Smart selection
     */
    public function ajax_smart_select() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $count = intval($_POST['count'] ?? 20);
        $type = sanitize_text_field($_POST['type'] ?? 'balanced');
        $exclude_published = $_POST['exclude_published'] === 'true';
        $exclude_private = $_POST['exclude_private'] === 'true';

        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => $count * 2, // Get more to filter
            'fields' => 'ids'
        );

        // Apply exclusions
        $exclude_ids = array();
        if ($exclude_published) {
            $exclude_ids = array_merge($exclude_ids, get_option('soloylibre_published_photos', array()));
        }
        if ($exclude_private) {
            $exclude_ids = array_merge($exclude_ids, get_option('soloylibre_private_photos', array()));
        }

        if (!empty($exclude_ids)) {
            $args['post__not_in'] = array_unique($exclude_ids);
        }

        // Apply selection type
        switch ($type) {
            case 'quality':
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
                break;
            case 'diversity':
                $args['orderby'] = 'rand';
                break;
            case 'recent_quality':
                $args['orderby'] = 'date';
                $args['order'] = 'DESC';
                break;
            case 'popular':
                $args['meta_key'] = 'photo_views';
                $args['orderby'] = 'meta_value_num';
                $args['order'] = 'DESC';
                break;
            default: // balanced
                $args['orderby'] = 'rand';
        }

        $photo_ids = get_posts($args);

        // Simulate AI scoring and selection
        $scored_photos = array();
        foreach ($photo_ids as $photo_id) {
            $score = rand(60, 100);

            // Boost score based on type
            switch ($type) {
                case 'quality':
                    $score += rand(10, 20);
                    break;
                case 'recent_quality':
                    $days_old = (time() - get_post_time('U', false, $photo_id)) / (24 * 60 * 60);
                    if ($days_old < 30) $score += 15;
                    break;
            }

            $scored_photos[] = array(
                'id' => $photo_id,
                'score' => min(100, $score)
            );
        }

        // Sort by score and take top results
        usort($scored_photos, function($a, $b) {
            return $b['score'] - $a['score'];
        });

        $selected_photos = array_slice($scored_photos, 0, $count);

        // Format for response
        $photos = array();
        foreach ($selected_photos as $photo_data) {
            $photo_id = $photo_data['id'];
            $photos[] = array(
                'id' => $photo_id,
                'title' => get_the_title($photo_id) ?: 'Photo ' . $photo_id,
                'thumbnail' => wp_get_attachment_image_url($photo_id, 'thumbnail'),
                'full' => wp_get_attachment_image_url($photo_id, 'full'),
                'ai_score' => $photo_data['score']
            );
        }

        wp_send_json_success(array(
            'photos' => $photos,
            'count' => count($photos),
            'type' => $type,
            'message' => 'Selección IA completada con ' . count($photos) . ' fotos'
        ));
    }

    /**
     * AJAX: Bulk actions
     */
    public function ajax_bulk_action() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $action_type = sanitize_text_field($_POST['action_type'] ?? '');
        $photo_ids = array_map('intval', $_POST['photo_ids'] ?? array());

        switch ($action_type) {
            case 'mark_private':
                $private_photos = get_option('soloylibre_private_photos', array());
                $private_photos = array_merge($private_photos, $photo_ids);
                update_option('soloylibre_private_photos', array_unique($private_photos));

                wp_send_json_success(array(
                    'message' => count($photo_ids) . ' fotos marcadas como privadas',
                    'count' => count($photo_ids)
                ));
                break;

            case 'mark_unwanted':
                $unwanted_photos = get_option('soloylibre_unwanted_photos', array());
                $unwanted_photos = array_merge($unwanted_photos, $photo_ids);
                update_option('soloylibre_unwanted_photos', array_unique($unwanted_photos));

                wp_send_json_success(array(
                    'message' => count($photo_ids) . ' fotos marcadas como no deseadas',
                    'count' => count($photo_ids)
                ));
                break;

            case 'reset_published':
                update_option('soloylibre_published_photos', array());

                wp_send_json_success(array(
                    'message' => 'Fotos publicadas reseteadas',
                    'count' => 0
                ));
                break;

            default:
                wp_send_json_error('Acción no válida');
        }
    }

    /**
     * AJAX: Save settings
     */
    public function ajax_save_settings() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $settings_type = sanitize_text_field($_POST['settings_type'] ?? 'all');
        $settings = $_POST['settings'] ?? array();

        switch ($settings_type) {
            case 'all':
                foreach ($settings as $key => $value) {
                    $sanitized_key = sanitize_key($key);
                    if (is_bool($value) || $value === 'true' || $value === 'false') {
                        $sanitized_value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                    } else {
                        $sanitized_value = sanitize_text_field($value);
                    }
                    update_option("soloylibre_glass_$sanitized_key", $sanitized_value);
                }
                break;

            case 'smart_preferences':
                update_option('soloylibre_glass_smart_preferences', $settings);
                break;

            case 'reset':
                // Delete all glass settings
                global $wpdb;
                $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE 'soloylibre_glass_%'");
                break;

            case 'import':
                foreach ($settings as $key => $value) {
                    update_option($key, $value);
                }
                break;
        }

        wp_send_json_success(array(
            'message' => 'Configuraciones guardadas exitosamente',
            'type' => $settings_type
        ));
    }

    /**
     * Get photo status
     */
    private function get_photo_status($photo_id) {
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        if (in_array($photo_id, $published_photos)) return 'Publicada';
        if (in_array($photo_id, $private_photos)) return 'Privada';
        if (in_array($photo_id, $unwanted_photos)) return 'No deseada';

        return 'Disponible';
    }

    /**
     * AJAX: Add like to photo (unlimited likes allowed)
     */
    public function ajax_add_like() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        $photo_id = intval($_POST['photo_id'] ?? 0);

        if (!$photo_id) {
            wp_send_json_error('ID de foto inválido');
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'soloylibre_interactions';

        // Always add like - unlimited likes allowed
        $result = $wpdb->insert(
            $table_name,
            array(
                'photo_id' => $photo_id,
                'user_id' => get_current_user_id() ?: null,
                'user_ip' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                'reaction_type' => 'like',
                'interaction_type' => 'like',
                'interaction_count' => 1,
                'is_simulated' => 0,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s', '%d', '%d', '%s')
        );

        if ($result) {
            // Get updated like count
            $like_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE photo_id = %d AND reaction_type = 'like'",
                $photo_id
            ));

            // Update post meta for caching
            update_post_meta($photo_id, '_soloylibre_likes_count', intval($like_count));

            // Update global likes count
            $total_likes = get_option('soloylibre_total_likes', 0);
            update_option('soloylibre_total_likes', $total_likes + 1);

            wp_send_json_success(array(
                'like_count' => intval($like_count),
                'photo_id' => $photo_id,
                'message' => '💖 Like agregado exitosamente',
                'user_id' => get_current_user_id(),
                'unlimited' => true
            ));
        } else {
            wp_send_json_error('Error agregando like a la base de datos');
        }
    }

    /**
     * AJAX: Reset published photos count
     */
    public function ajax_reset_published() {
        check_ajax_referer('soloylibre_glass_nonce', 'nonce');

        // Get current counts
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());

        $published_count = count($published_photos);

        // Reset only published photos, keep private and unwanted
        update_option('soloylibre_published_photos', array());

        // Update statistics
        update_option('soloylibre_last_activity', current_time('mysql'));

        wp_send_json_success(array(
            'message' => "✅ Se resetearon $published_count fotos publicadas",
            'reset_count' => $published_count,
            'private_kept' => count($private_photos),
            'unwanted_kept' => count($unwanted_photos),
            'timestamp' => current_time('mysql')
        ));
    }

    /**
     * Render Wizard Page
     */
    public function render_wizard() {
        echo $this->get_glass_styles();
        ?>
        <div class="glass-container">
            <div class="glass-header">
                <h1>🧙‍♂️ SoloYLibre Wizard</h1>
                <p>🇩🇴 JoseTusabe Photography - Asistente inteligente para gestión fotográfica</p>
            </div>

            <div class="glass-card">
                <h3>🎯 Configuración del Wizard</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <label>📊 Número de fotos:</label>
                        <input type="number" id="wizard-photo-count" class="glass-input" value="25" min="1" max="200">
                    </div>
                    <div>
                        <label>🎲 Tipo de selección:</label>
                        <select id="wizard-selection-type" class="glass-input">
                            <option value="random">Aleatorio</option>
                            <option value="best">Mejores calificadas</option>
                            <option value="recent">Más recientes</option>
                            <option value="popular">Más populares</option>
                        </select>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <label>📝 Título del álbum:</label>
                    <input type="text" id="wizard-album-title" class="glass-input" placeholder="Se generará automáticamente...">
                </div>

                <div style="margin: 20px 0;">
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <input type="checkbox" id="wizard-exclude-published" checked>
                        Excluir fotos ya publicadas
                    </label>
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <input type="checkbox" id="wizard-exclude-private" checked>
                        Excluir fotos privadas
                    </label>
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <input type="checkbox" id="wizard-auto-publish" checked>
                        Publicar automáticamente
                    </label>
                </div>

                <div style="text-align: center; margin: 20px 0;">
                    <button class="glass-button primary" onclick="startWizard()">🚀 Iniciar Wizard</button>
                    <button class="glass-button warning" onclick="resetWizardPublished()">🔄 Reset Publicadas</button>
                </div>
            </div>

            <div id="wizard-results" class="glass-card" style="display: none;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>📸 Fotos Seleccionadas por el Wizard</h3>
                    <div>
                        <span id="wizard-selected-count">0</span> fotos seleccionadas
                    </div>
                </div>

                <div style="background: rgba(0, 123, 255, 0.2); padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #007bff;">
                    <strong>ℹ️ INFORMACIÓN:</strong> Revisa las fotos seleccionadas. Haz clic en cualquier foto para excluirla de la selección.
                </div>

                <div id="wizard-photos-grid" class="glass-photo-grid"></div>

                <div style="text-align: center; margin: 20px 0; padding: 20px; background: rgba(255, 255, 255, 0.1); border-radius: 10px;">
                    <div style="margin-bottom: 15px;">
                        <strong>📝 Título:</strong> <span id="wizard-preview-title">-</span><br>
                        <strong>📊 Estado:</strong> <span id="wizard-preview-status">-</span>
                    </div>
                    <button class="glass-button success" onclick="createWizardPost()" id="wizard-create-btn" disabled>
                        ✅ Crear Álbum con Wizard
                    </button>
                    <button class="glass-button warning" onclick="regenerateSelection()">
                        🔄 Nueva Selección
                    </button>
                    <button class="glass-button" onclick="editWizardSelection()">
                        ✏️ Editar Selección
                    </button>
                </div>
            </div>

            <div id="wizard-notifications"></div>
        </div>

        <script>
        let wizardSelectedPhotos = [];
        let wizardAllPhotos = [];

        function startWizard() {
            const photoCount = $('#wizard-photo-count').val();
            const selectionType = $('#wizard-selection-type').val();
            const excludePublished = $('#wizard-exclude-published').is(':checked');
            const excludePrivate = $('#wizard-exclude-private').is(':checked');
            const autoPublish = $('#wizard-auto-publish').is(':checked');
            let title = $('#wizard-album-title').val();

            if (!title.trim()) {
                // Generate creative title if empty
                title = generateCreativeTitle();
                $('#wizard-album-title').val(title);
            }

            $('#wizard-photos-grid').html('⏳ El wizard está seleccionando las mejores fotos...');
            $('#wizard-results').show();
            $('#wizard-preview-title').text(title);
            $('#wizard-preview-status').text(autoPublish ? 'Se publicará automáticamente' : 'Se guardará como borrador');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_load_photos',
                nonce: soloylibreGlass.nonce,
                count: photoCount,
                type: selectionType,
                exclude_published: excludePublished,
                exclude_private: excludePrivate,
                filter: 'available'
            }, function(response) {
                if (response.success) {
                    wizardAllPhotos = response.data.photos;
                    wizardSelectedPhotos = wizardAllPhotos.map(p => p.id);
                    displayWizardPhotos();
                    updateWizardSelectionCount();
                    $('#wizard-create-btn').prop('disabled', false);

                    if (response.data.message) {
                        showWizardNotification(response.data.message, 'info');
                    } else {
                        showWizardNotification(`🧙‍♂️ Wizard seleccionó ${wizardAllPhotos.length} fotos mágicamente`, 'success');
                    }
                } else {
                    $('#wizard-photos-grid').html('❌ Error: ' + (response.data || 'Error desconocido'));
                    showWizardNotification('❌ Error en el wizard', 'error');
                }
            });
        }

        function displayWizardPhotos() {
            let html = '';
            wizardAllPhotos.forEach(photo => {
                const isSelected = wizardSelectedPhotos.includes(photo.id);
                const selectedClass = isSelected ? 'selected' : 'removed';
                const statusIcon = isSelected ? '✅' : '❌';
                const statusText = isSelected ? 'Incluir' : 'EXCLUIDA';

                html += `
                    <div class="quick-photo-item ${selectedClass}" data-photo-id="${photo.id}" onclick="toggleWizardPhoto(${photo.id})">
                        <img src="${photo.thumbnail}" alt="${photo.title}" loading="lazy">
                        <div class="quick-photo-overlay">
                            <div class="quick-photo-status">${statusIcon} ${statusText}</div>
                        </div>
                        <div class="quick-photo-info">
                            <div class="photo-title">${photo.title}</div>
                            <div class="photo-status">${photo.status || 'Disponible'}</div>
                            ${photo.like_count ? `<div class="photo-likes">💖 ${photo.like_count}</div>` : ''}
                        </div>
                    </div>
                `;
            });
            $('#wizard-photos-grid').html(html);
        }

        function toggleWizardPhoto(photoId) {
            const index = wizardSelectedPhotos.indexOf(photoId);
            if (index > -1) {
                wizardSelectedPhotos.splice(index, 1);
                showWizardNotification('📷 Foto excluida por el wizard', 'info');
            } else {
                wizardSelectedPhotos.push(photoId);
                showWizardNotification('📷 Foto incluida por el wizard', 'success');
            }
            displayWizardPhotos();
            updateWizardSelectionCount();

            $('#wizard-create-btn').prop('disabled', wizardSelectedPhotos.length === 0);
        }

        function updateWizardSelectionCount() {
            $('#wizard-selected-count').text(wizardSelectedPhotos.length);
        }

        function createWizardPost() {
            if (wizardSelectedPhotos.length === 0) {
                showWizardNotification('❌ El wizard necesita al menos una foto', 'error');
                return;
            }

            const title = $('#wizard-album-title').val();
            const autoPublish = $('#wizard-auto-publish').is(':checked');

            if (!confirm(`¿Crear álbum con ${wizardSelectedPhotos.length} fotos usando el wizard?`)) {
                return;
            }

            $('#wizard-create-btn').prop('disabled', true).text('🧙‍♂️ Wizard creando...');

            $.post(soloylibreGlass.ajax_url, {
                action: 'glass_create_quick_post',
                nonce: soloylibreGlass.nonce,
                photo_ids: wizardSelectedPhotos,
                title: title,
                content: `Álbum creado mágicamente por el SoloYLibre Wizard con ${wizardSelectedPhotos.length} fotografías seleccionadas inteligentemente.`,
                auto_feature_image: true,
                auto_publish: autoPublish
            }, function(response) {
                if (response.success) {
                    showWizardNotification('🎉 ¡Álbum creado exitosamente por el wizard!', 'success');

                    const successHtml = `
                        <div style="background: rgba(40, 167, 69, 0.2); padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;">
                            <h3 style="color: #28a745; margin: 0 0 15px 0;">🧙‍♂️ ¡Wizard Completado Exitosamente!</h3>
                            <p><strong>📝 Título:</strong> ${title}</p>
                            <p><strong>📷 Fotos incluidas:</strong> ${wizardSelectedPhotos.length}</p>
                            <p><strong>🆔 ID del post:</strong> ${response.data.post_id}</p>
                            <div style="margin-top: 20px;">
                                <a href="${response.data.edit_url}" target="_blank" class="glass-button success">✏️ Editar Álbum</a>
                                <button onclick="resetWizardForm()" class="glass-button primary">🔄 Nuevo Wizard</button>
                            </div>
                        </div>
                    `;

                    $('#wizard-photos-grid').html(successHtml);

                    setTimeout(() => {
                        window.open(response.data.edit_url, '_blank');
                    }, 2000);

                } else {
                    showWizardNotification('❌ Error en el wizard: ' + (response.data || 'Error desconocido'), 'error');
                    $('#wizard-create-btn').prop('disabled', false).text('✅ Crear Álbum con Wizard');
                }
            });
        }

        function resetWizardPublished() {
            if (confirm('¿Resetear todas las fotos publicadas? Esto permitirá al wizard seleccionar fotos previamente usadas.')) {
                $.post(soloylibreGlass.ajax_url, {
                    action: 'glass_reset_published',
                    nonce: soloylibreGlass.nonce
                }, function(response) {
                    if (response.success) {
                        showWizardNotification(response.data.message, 'success');
                    } else {
                        showWizardNotification('❌ Error reseteando fotos', 'error');
                    }
                });
            }
        }

        function regenerateSelection() {
            if (confirm('¿Generar una nueva selección mágica con el wizard?')) {
                startWizard();
            }
        }

        function editWizardSelection() {
            showWizardNotification('💡 Haz clic en las fotos para incluir/excluir del álbum', 'info');
        }

        function resetWizardForm() {
            $('#wizard-results').hide();
            $('#wizard-album-title').val('');
            wizardSelectedPhotos = [];
            wizardAllPhotos = [];
            $('#wizard-create-btn').prop('disabled', true).text('✅ Crear Álbum con Wizard');
            showWizardNotification('🔄 Wizard reseteado', 'info');
        }

        function generateCreativeTitle() {
            const titles = [
                'Memorias Fotográficas de San José de Ocoa - JoseTusabe',
                'Colección Dominicana - JoseTusabe Photography',
                'Instantes Únicos - República Dominicana',
                'Tesoros Visuales de JoseTusabe',
                'Galería Profesional - Edición ' + new Date().getFullYear(),
                'Momentos Cautivadores - JoseTusabe Photography',
                'Aventuras Fotográficas Dominicanas',
                'Secretos de la Lente - JoseTusabe'
            ];
            return titles[Math.floor(Math.random() * titles.length)];
        }

        function showWizardNotification(message, type) {
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button onclick="$(this).parent().fadeOut()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
                </div>
            `);

            $('#wizard-notifications').prepend(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }
        </script>
        <?php
    }
}
