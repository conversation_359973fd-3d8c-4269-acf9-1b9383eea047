<?php
/**
 * Photographer Settings - Personal Information Configuration
 * Configuración del fotógrafo - Información personal
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

class SoloYLibre_Photographer_Settings {
    
    public function __construct() {
        add_action("admin_menu", array($this, "add_settings_page"));
        add_action("admin_init", array($this, "register_settings"));
        add_action("wp_ajax_save_photographer_info", array($this, "ajax_save_info"));
    }
    
    public function add_settings_page() {
        add_submenu_page(
            "soloylibre-wizard",
            "Información del Fotógrafo",
            "👤 Mi Información",
            "manage_options",
            "soloylibre-photographer-info",
            array($this, "render_settings_page")
        );
    }
    
    public function register_settings() {
        register_setting("soloylibre_photographer", "soloylibre_photographer_info");
    }
    
    public function render_settings_page() {
        $info = get_option("soloylibre_photographer_info", $this->get_default_info());
        ?>
        <div class="photographer-settings">
            <div class="settings-header">
                <h1>👤 Información del Fotógrafo</h1>
                <p>🇩🇴 Configura tu información personal para SoloYLibre Photography</p>
            </div>
            
            <div class="settings-content">
                <form id="photographer-form" method="post">
                    <?php wp_nonce_field('save_photographer_info', 'photographer_nonce'); ?>
                    
                    <div class="form-section">
                        <h3>📝 Información Personal</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="full_name">👤 Nombre Completo</label>
                                <input type="text" id="full_name" name="full_name" value="<?php echo esc_attr($info['full_name']); ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="nickname">🏷️ Apodo/Nickname</label>
                                <input type="text" id="nickname" name="nickname" value="<?php echo esc_attr($info['nickname']); ?>">
                            </div>
                            <div class="form-group">
                                <label for="business_name">🏢 Nombre del Negocio</label>
                                <input type="text" id="business_name" name="business_name" value="<?php echo esc_attr($info['business_name']); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>📍 Ubicación</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="city">🏙️ Ciudad</label>
                                <input type="text" id="city" name="city" value="<?php echo esc_attr($info['city']); ?>">
                            </div>
                            <div class="form-group">
                                <label for="country">🇩🇴 País</label>
                                <input type="text" id="country" name="country" value="<?php echo esc_attr($info['country']); ?>">
                            </div>
                            <div class="form-group">
                                <label for="current_location">📍 Ubicación Actual</label>
                                <input type="text" id="current_location" name="current_location" value="<?php echo esc_attr($info['current_location']); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>📞 Contacto</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="phone">📞 Teléfono</label>
                                <input type="tel" id="phone" name="phone" value="<?php echo esc_attr($info['phone']); ?>">
                            </div>
                            <div class="form-group">
                                <label for="email">📧 Email</label>
                                <input type="email" id="email" name="email" value="<?php echo esc_attr($info['email']); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>🌐 Sitios Web</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="website_main">🌐 Sitio Principal</label>
                                <input type="url" id="website_main" name="website_main" value="<?php echo esc_attr($info['website_main']); ?>">
                            </div>
                            <div class="form-group">
                                <label for="website_business">🏢 Sitio del Negocio</label>
                                <input type="url" id="website_business" name="website_business" value="<?php echo esc_attr($info['website_business']); ?>">
                            </div>
                            <div class="form-group">
                                <label for="website_portfolio">📸 Portfolio</label>
                                <input type="url" id="website_portfolio" name="website_portfolio" value="<?php echo esc_attr($info['website_portfolio']); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>📝 Descripción</h3>
                        <div class="form-group full-width">
                            <label for="description">📖 Descripción Profesional</label>
                            <textarea id="description" name="description" rows="4"><?php echo esc_textarea($info['description']); ?></textarea>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">💾 Guardar Información</button>
                        <button type="button" id="preview-info" class="btn btn-secondary">👁️ Vista Previa</button>
                        <button type="button" id="reset-default" class="btn btn-outline">🔄 Restaurar Predeterminado</button>
                    </div>
                </form>
                
                <!-- Vista previa -->
                <div id="info-preview" class="info-preview" style="display: none;">
                    <h3>👁️ Vista Previa de tu Información</h3>
                    <div id="preview-content" class="preview-content"></div>
                </div>
            </div>
        </div>
        
        <style>
        .photographer-settings {
            max-width: 1000px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .settings-header {
            background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .settings-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .settings-content {
            padding: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .form-section h3 {
            margin: 0 0 20px 0;
            color: #CE1126;
            font-size: 1.3rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #495057;
        }
        
        .form-group input, .form-group textarea {
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #CE1126;
            box-shadow: 0 0 0 2px rgba(206, 17, 38, 0.2);
        }
        
        .form-actions {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #CE1126;
            color: white;
        }
        
        .btn-primary:hover {
            background: #a00e1f;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 2px solid #6c757d;
        }
        
        .btn-outline:hover {
            background: #6c757d;
            color: white;
        }
        
        .info-preview {
            margin-top: 30px;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 10px;
            border: 1px solid #bbdefb;
        }
        
        .preview-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            $('#photographer-form').submit(function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                formData.append('action', 'save_photographer_info');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            alert('✅ Información guardada exitosamente');
                        } else {
                            alert('❌ Error: ' + response.data);
                        }
                    }
                });
            });
            
            $('#preview-info').click(function() {
                generatePreview();
                $('#info-preview').toggle();
            });
            
            $('#reset-default').click(function() {
                if (confirm('¿Estás seguro de que quieres restaurar la información predeterminada?')) {
                    location.reload();
                }
            });
            
            function generatePreview() {
                const info = {
                    full_name: $('#full_name').val(),
                    nickname: $('#nickname').val(),
                    business_name: $('#business_name').val(),
                    city: $('#city').val(),
                    country: $('#country').val(),
                    current_location: $('#current_location').val(),
                    phone: $('#phone').val(),
                    email: $('#email').val(),
                    website_main: $('#website_main').val(),
                    website_business: $('#website_business').val(),
                    website_portfolio: $('#website_portfolio').val(),
                    description: $('#description').val()
                };
                
                let preview = '<h4>📸 ' + info.business_name + '</h4>';
                preview += '<p><strong>' + info.full_name + ' (' + info.nickname + ')</strong></p>';
                preview += '<p>📍 ' + info.city + ', ' + info.country + ' / ' + info.current_location + '</p>';
                preview += '<p>📞 ' + info.phone + ' | 📧 ' + info.email + '</p>';
                preview += '<p>🌐 ' + info.website_main + ' | ' + info.website_business + '</p>';
                preview += '<p>📸 ' + info.website_portfolio + '</p>';
                if (info.description) {
                    preview += '<p>' + info.description + '</p>';
                }
                
                $('#preview-content').html(preview);
            }
        });
        </script>
        <?php
    }
    
    public function ajax_save_info() {
        check_ajax_referer('save_photographer_info', 'photographer_nonce');
        
        $info = array(
            'full_name' => sanitize_text_field($_POST['full_name']),
            'nickname' => sanitize_text_field($_POST['nickname']),
            'business_name' => sanitize_text_field($_POST['business_name']),
            'city' => sanitize_text_field($_POST['city']),
            'country' => sanitize_text_field($_POST['country']),
            'current_location' => sanitize_text_field($_POST['current_location']),
            'phone' => sanitize_text_field($_POST['phone']),
            'email' => sanitize_email($_POST['email']),
            'website_main' => esc_url_raw($_POST['website_main']),
            'website_business' => esc_url_raw($_POST['website_business']),
            'website_portfolio' => esc_url_raw($_POST['website_portfolio']),
            'description' => sanitize_textarea_field($_POST['description'])
        );
        
        update_option('soloylibre_photographer_info', $info);
        
        wp_send_json_success('Información guardada exitosamente');
    }
    
    private function get_default_info() {
        return array(
            'full_name' => 'Jose L Encarnacion',
            'nickname' => 'JoseTusabe',
            'business_name' => 'SoloYLibre Photography',
            'city' => 'San José de Ocoa',
            'country' => 'República Dominicana 🇩🇴',
            'current_location' => 'USA 🇺🇸',
            'phone' => '************',
            'email' => '<EMAIL>',
            'website_main' => 'josetusabe.com',
            'website_business' => 'soloylibre.com',
            'website_portfolio' => '1and1photo.com',
            'description' => 'Fotografía profesional por Jose L Encarnacion (JoseTusabe) - Capturando la belleza de República Dominicana y el mundo'
        );
    }
}

// Inicializar configuración del fotógrafo
new SoloYLibre_Photographer_Settings();
?>
