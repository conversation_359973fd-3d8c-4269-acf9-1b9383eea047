<?php
/**
 * SoloYLibre Gallery Shortcode Handler
 * Manejador de shortcodes para galerías
 */

class SoloYLibre_Shortcode_Handler {
    
    public function __construct() {
        add_shortcode('soloylibre_gallery', array($this, 'render_gallery'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'));
    }
    
    public function enqueue_assets() {
        wp_add_inline_style('wp-block-library', $this->get_css());
        wp_add_inline_script('jquery', $this->get_js());
    }
    
    public function render_gallery($atts) {
        $atts = shortcode_atts(array(
            'ids' => '',
            'style' => 'grid',
            'columns' => '3',
            'size' => 'medium',
            'lightbox' => 'true',
            'show_metadata' => 'false',
            'show_title' => 'true',
            'photographer_signature' => 'true'
        ), $atts, 'soloylibre_gallery');
        
        if (empty($atts['ids'])) {
            return '<p style="color: #dc3545; padding: 15px; background: #f8d7da; border-radius: 5px;">❌ Error: No se especificaron IDs de imágenes para la galería.</p>';
        }
        
        $image_ids = explode(',', $atts['ids']);
        $image_ids = array_map('trim', $image_ids);
        $image_ids = array_filter($image_ids, 'is_numeric');
        
        if (empty($image_ids)) {
            return '<p style="color: #dc3545; padding: 15px; background: #f8d7da; border-radius: 5px;">❌ Error: IDs de imágenes no válidos.</p>';
        }
        
        $gallery_id = 'soloylibre-gallery-' . uniqid();
        $output = '';
        
        // Contenedor principal
        $output .= '<div id="' . $gallery_id . '" class="soloylibre-gallery soloylibre-style-' . esc_attr($atts['style']) . '">';
        
        // Grid de imágenes
        $columns = intval($atts['columns']);
        $output .= '<div class="gallery-grid" style="grid-template-columns: repeat(' . $columns . ', 1fr);">';
        
        foreach ($image_ids as $image_id) {
            $image_id = intval($image_id);
            $image = wp_get_attachment_image_src($image_id, $atts['size']);
            $full_image = wp_get_attachment_image_src($image_id, 'full');
            $title = get_the_title($image_id);
            $alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
            
            if (!$image) continue;
            
            $output .= '<div class="gallery-item">';
            
            // Imagen con enlace
            if ($atts['lightbox'] === 'true' && $full_image) {
                $output .= '<a href="' . esc_url($full_image[0]) . '" class="gallery-link" data-lightbox="gallery-' . $gallery_id . '" data-title="' . esc_attr($title) . '">';
            } else {
                $output .= '<div class="gallery-link">';
            }
            
            $output .= '<img src="' . esc_url($image[0]) . '" alt="' . esc_attr($alt) . '" title="' . esc_attr($title) . '" loading="lazy">';
            
            // Overlay con información
            if ($atts['show_title'] === 'true' || $atts['show_metadata'] === 'true') {
                $output .= '<div class="gallery-overlay">';
                
                if ($atts['show_title'] === 'true' && !empty($title)) {
                    $output .= '<h4 class="image-title">' . esc_html($title) . '</h4>';
                }
                
                if ($atts['show_metadata'] === 'true') {
                    $metadata = wp_get_attachment_metadata($image_id);
                    if ($metadata && isset($metadata['width']) && isset($metadata['height'])) {
                        $output .= '<p class="image-meta">' . $metadata['width'] . ' × ' . $metadata['height'] . '</p>';
                    }
                }
                
                $output .= '</div>';
            }
            
            if ($atts['lightbox'] === 'true') {
                $output .= '</a>';
            } else {
                $output .= '</div>';
            }
            
            $output .= '</div>';
        }
        
        $output .= '</div>'; // Cierre gallery-grid
        
        // Firma del fotógrafo
        if ($atts['photographer_signature'] === 'true') {
            $output .= '<div class="photographer-signature">';
            $output .= '<p><strong>📸 Jose L Encarnacion (JoseTusabe)</strong><br>';
            $output .= 'SoloYLibre Photography<br>';
            $output .= '📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸<br>';
            $output .= '📞 718-713-5500 | 📧 <EMAIL><br>';
            $output .= '🌐 <a href="https://josetusabe.com" target="_blank">josetusabe.com</a> | ';
            $output .= '<a href="https://soloylibre.com" target="_blank">soloylibre.com</a></p>';
            $output .= '</div>';
        }
        
        $output .= '</div>'; // Cierre contenedor principal
        
        return $output;
    }
    
    private function get_css() {
        return '
        .soloylibre-gallery {
            margin: 20px 0;
            font-family: Arial, sans-serif;
        }
        
        .gallery-grid {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .gallery-item {
            position: relative;
            overflow: hidden;
            background: #f8f9fa;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .gallery-link {
            display: block;
            position: relative;
            text-decoration: none;
        }
        
        .gallery-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover img {
            transform: scale(1.05);
        }
        
        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 15px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover .gallery-overlay {
            transform: translateY(0);
        }
        
        .image-title {
            margin: 0 0 5px 0;
            font-size: 1rem;
            font-weight: 600;
        }
        
        .image-meta {
            margin: 0;
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .photographer-signature {
            background: #f8f9fa;
            border-left: 4px solid #CE1126;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 0.9rem;
            line-height: 1.6;
        }
        
        .photographer-signature a {
            color: #CE1126;
            text-decoration: none;
        }
        
        .photographer-signature a:hover {
            text-decoration: underline;
        }
        
        /* Estilo dominicano */
        .soloylibre-style-dominican .gallery-item {
            border: 2px solid #CE1126;
        }
        
        .soloylibre-style-dominican .gallery-item:hover {
            border-color: #002D62;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: repeat(2, 1fr) !important;
            }
        }
        
        @media (max-width: 480px) {
            .gallery-grid {
                grid-template-columns: 1fr !important;
            }
        }
        ';
    }
    
    private function get_js() {
        return "
        jQuery(document).ready(function($) {
            // Simple lightbox
            $('.soloylibre-gallery a[data-lightbox]').click(function(e) {
                e.preventDefault();
                
                var src = $(this).attr('href');
                var title = $(this).data('title') || '';
                
                var lightbox = $('<div>').addClass('soloylibre-lightbox').css({
                    'position': 'fixed',
                    'top': '0',
                    'left': '0',
                    'width': '100%',
                    'height': '100%',
                    'background': 'rgba(0,0,0,0.9)',
                    'z-index': '9999',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center',
                    'cursor': 'pointer'
                });
                
                var img = $('<img>').attr('src', src).attr('alt', title).css({
                    'max-width': '90%',
                    'max-height': '90%',
                    'object-fit': 'contain'
                });
                
                var close = $('<span>&times;</span>').css({
                    'position': 'absolute',
                    'top': '20px',
                    'right': '30px',
                    'color': 'white',
                    'font-size': '2rem',
                    'cursor': 'pointer'
                });
                
                lightbox.append(img).append(close);
                $('body').append(lightbox);
                
                lightbox.fadeIn(300);
                
                lightbox.click(function() {
                    lightbox.fadeOut(300, function() {
                        lightbox.remove();
                    });
                });
                
                img.click(function(e) {
                    e.stopPropagation();
                });
            });
        });
        ";
    }
}

new SoloYLibre_Shortcode_Handler();
?>
