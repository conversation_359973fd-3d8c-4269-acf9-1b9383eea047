<?php
/**
 * Verify Select-All Fix - SoloYLibre Gallery Plugin
 * Developed by JEYKO AI for Jose <PERSON> (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>✅ Verificación de Reparación Select-All</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Confirmando que la función select-all está completamente funcional</p>";

$fixes_verified = array();
$issues_found = array();

// 1. Verify AJAX endpoint
echo "<h2>🔍 1. Verificando Endpoint AJAX</h2>";

if (has_action('wp_ajax_bulk_select_all')) {
    $fixes_verified[] = "✅ Endpoint bulk_select_all registrado correctamente";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Endpoint bulk_select_all está registrado</strong>";
    echo "</div>";
} else {
    $issues_found[] = "❌ Endpoint bulk_select_all NO registrado";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ <strong>Endpoint bulk_select_all NO está registrado</strong>";
    echo "</div>";
}

// 2. Verify class and method
echo "<h2>📋 2. Verificando Clase y Método</h2>";

if (class_exists('SoloYLibre_Bulk_Photo_Manager')) {
    if (method_exists('SoloYLibre_Bulk_Photo_Manager', 'ajax_select_all')) {
        $fixes_verified[] = "✅ Método ajax_select_all implementado";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ <strong>Método ajax_select_all está implementado correctamente</strong>";
        echo "</div>";
    } else {
        $issues_found[] = "❌ Método ajax_select_all NO implementado";
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ <strong>Método ajax_select_all NO está implementado</strong>";
        echo "</div>";
    }
} else {
    $issues_found[] = "❌ Clase SoloYLibre_Bulk_Photo_Manager NO existe";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ <strong>Clase SoloYLibre_Bulk_Photo_Manager NO existe</strong>";
    echo "</div>";
}

// 3. Test the function directly
echo "<h2>🧪 3. Probando Función Directamente</h2>";

$test_nonce = wp_create_nonce('bulk_manager_nonce');

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<button onclick='testSelectAllNow()' style='background: #007cba; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;'>";
echo "🧪 Probar Select-All AHORA";
echo "</button>";
echo "</div>";

echo "<div id='test-result' style='margin: 20px 0;'></div>";

// 4. Check for any hardcoded "en desarrollo" messages
echo "<h2>🔍 4. Verificando Mensajes Hardcodeados</h2>";

$bulk_manager_file = plugin_dir_path(__FILE__) . 'includes/class-bulk-photo-manager.php';
if (file_exists($bulk_manager_file)) {
    $content = file_get_contents($bulk_manager_file);
    
    // Check for development messages
    $development_patterns = array(
        'en desarrollo',
        'development',
        'TODO',
        'FIXME',
        'not implemented',
        'coming soon'
    );
    
    $found_issues = false;
    foreach ($development_patterns as $pattern) {
        if (stripos($content, $pattern) !== false) {
            $issues_found[] = "⚠️ Encontrado texto '$pattern' en el código";
            $found_issues = true;
        }
    }
    
    if (!$found_issues) {
        $fixes_verified[] = "✅ No se encontraron mensajes de desarrollo hardcodeados";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ <strong>No se encontraron mensajes de desarrollo hardcodeados</strong>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "⚠️ <strong>Se encontraron algunos mensajes de desarrollo en el código</strong>";
        echo "</div>";
    }
}

// 5. Verify JavaScript functions
echo "<h2>📱 5. Verificando Funciones JavaScript</h2>";

$js_functions_to_check = array(
    'selectAllPhotos',
    'deselectAllPhotos',
    'showNotification',
    'performBulkAction'
);

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Funciones JavaScript que deben estar presentes:</strong>";
echo "<ul>";
foreach ($js_functions_to_check as $func) {
    if (strpos($content, "function $func") !== false) {
        echo "<li>✅ $func - Encontrada</li>";
        $fixes_verified[] = "✅ Función JavaScript $func implementada";
    } else {
        echo "<li>❌ $func - NO encontrada</li>";
        $issues_found[] = "❌ Función JavaScript $func NO implementada";
    }
}
echo "</ul>";
echo "</div>";

// 6. Statistics
echo "<h2>📊 6. Estadísticas de Verificación</h2>";

$total_checks = count($fixes_verified) + count($issues_found);
$success_rate = count($fixes_verified) / $total_checks * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

// Success Card
echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($fixes_verified) . "</div>";
echo "<div>✅ Verificaciones Exitosas</div>";
echo "</div>";

// Issues Card
echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($issues_found) . "</div>";
echo "<div>❌ Problemas Encontrados</div>";
echo "</div>";

// Success Rate Card
echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Tasa de Éxito</div>";
echo "</div>";

echo "</div>";

// 7. Show results
if (!empty($fixes_verified)) {
    echo "<h3 style='color: #28a745;'>✅ Verificaciones Exitosas:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($fixes_verified as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($issues_found)) {
    echo "<h3 style='color: #dc3545;'>❌ Problemas Encontrados:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($issues_found as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// 8. JavaScript for testing
echo "<script>
function testSelectAllNow() {
    const resultDiv = document.getElementById('test-result');
    resultDiv.innerHTML = '<div style=\"background: #e3f2fd; padding: 15px; border-radius: 5px;\">⏳ Probando función select-all...</div>';
    
    fetch('" . admin_url('admin-ajax.php') . "', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'bulk_select_all',
            nonce: '$test_nonce',
            filter: 'all'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 10px 0;'>
                    <h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 ¡Función Select-All Funciona Perfectamente!</h4>
                    <p style='margin: 5px 0; color: #155724;'><strong>📊 Fotos encontradas:</strong> \${data.data.count}</p>
                    <p style='margin: 5px 0; color: #155724;'><strong>🔍 Filtro aplicado:</strong> \${data.data.filter}</p>
                    <p style='margin: 5px 0; color: #155724;'><strong>💬 Mensaje:</strong> \${data.data.message}</p>
                    <p style='margin: 10px 0 0 0; color: #155724; font-weight: bold;'>✅ La función select-all está completamente reparada y funcional!</p>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 10px 0;'>
                    <h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ Error en función select-all</h4>
                    <p style='margin: 0; color: #721c24;'>\${data.data || 'Error desconocido'}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 10px 0;'>
                <h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ Error de conexión</h4>
                <p style='margin: 0; color: #721c24;'>\${error.message}</p>
            </div>
        `;
    });
}
</script>";

// 9. Quick access links
echo "<h2>🔗 9. Enlaces de Prueba</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-bulk-manager' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>📁 Ir al Gestor Masivo</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-wizard' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>🧙‍♂️ Ir al Wizard</a>";
echo "</div>";

// 10. Final status
echo "<div style='background: " . ($success_rate >= 80 ? '#d4edda' : '#fff3cd') . "; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h2 style='margin: 0 0 15px 0; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo ($success_rate >= 80 ? '🎉' : '⚠️') . " Verificación de Select-All Completada</h2>";
echo "<p style='margin: 0; font-size: 18px; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo "Tasa de éxito: <strong>" . round($success_rate, 1) . "%</strong>";
echo "</p>";

if ($success_rate >= 80) {
    echo "<p style='margin: 10px 0 0 0; color: #155724; font-weight: bold;'>¡La función select-all debería estar funcionando correctamente!</p>";
} else {
    echo "<p style='margin: 10px 0 0 0; color: #856404; font-weight: bold;'>Aún hay algunos problemas que requieren atención.</p>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Verificación de select-all completada</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
