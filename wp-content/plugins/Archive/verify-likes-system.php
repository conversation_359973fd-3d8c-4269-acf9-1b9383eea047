<?php
/**
 * Verify Likes System - SoloYLibre Gallery Plugin
 * Ensure hearts/likes are properly saved to database
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>💖 Verificación del Sistema de Likes</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Asegurando que los corazones se graben correctamente</p>";

global $wpdb;

$issues_found = array();
$fixes_applied = array();

// 1. Check interactions table structure
echo "<h2>🔍 1. Verificando Estructura de la Tabla de Interacciones</h2>";

$table_name = $wpdb->prefix . 'soloylibre_interactions';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Tabla de interacciones existe:</strong> $table_name";
    echo "</div>";
    
    // Get table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    
    echo "<h3>📋 Estructura actual de la tabla:</h3>";
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background: rgba(0,0,0,0.1);'><th style='padding: 8px; border: 1px solid #ddd;'>Campo</th><th style='padding: 8px; border: 1px solid #ddd;'>Tipo</th><th style='padding: 8px; border: 1px solid #ddd;'>Null</th><th style='padding: 8px; border: 1px solid #ddd;'>Default</th></tr>";
    
    $has_reaction_type = false;
    $has_interaction_type = false;
    $has_photo_id = false;
    $has_user_id = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$column->Field}</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$column->Type}</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$column->Null}</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$column->Default}</td>";
        echo "</tr>";
        
        if ($column->Field === 'reaction_type') $has_reaction_type = true;
        if ($column->Field === 'interaction_type') $has_interaction_type = true;
        if ($column->Field === 'photo_id') $has_photo_id = true;
        if ($column->Field === 'user_id') $has_user_id = true;
    }
    echo "</table>";
    echo "</div>";
    
    // Verify required columns
    if ($has_photo_id && ($has_reaction_type || $has_interaction_type)) {
        $fixes_applied[] = "✅ Tabla tiene las columnas necesarias para likes";
    } else {
        $issues_found[] = "❌ Tabla no tiene todas las columnas necesarias";
    }
    
} else {
    $issues_found[] = "❌ Tabla de interacciones NO existe";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ <strong>Tabla de interacciones NO existe:</strong> $table_name";
    echo "</div>";
}

// 2. Test current likes data
echo "<h2>📊 2. Verificando Datos Actuales de Likes</h2>";

if ($table_exists) {
    // Count total interactions
    $total_interactions = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    
    // Count likes specifically
    $like_interactions = 0;
    if ($has_reaction_type) {
        $like_interactions += $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE reaction_type = 'like'");
    }
    if ($has_interaction_type) {
        $like_interactions += $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE interaction_type = 'like'");
    }
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
    echo "<div style='font-size: 36px; font-weight: bold;'>$total_interactions</div>";
    echo "<div>📊 Total Interacciones</div>";
    echo "</div>";
    
    echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
    echo "<div style='font-size: 36px; font-weight: bold;'>$like_interactions</div>";
    echo "<div>💖 Likes Registrados</div>";
    echo "</div>";
    
    echo "</div>";
    
    if ($like_interactions > 0) {
        $fixes_applied[] = "✅ Se encontraron $like_interactions likes en la base de datos";
    } else {
        $issues_found[] = "❌ No se encontraron likes en la base de datos";
    }
}

// 3. Test AJAX endpoints for likes
echo "<h2>🔗 3. Verificando Endpoints AJAX de Likes</h2>";

$ajax_endpoints = array(
    'add_photo_reaction' => 'Agregar reacción/like',
    'remove_photo_reaction' => 'Remover reacción/like',
    'get_photo_reactions' => 'Obtener reacciones',
    'generate_random_interactions' => 'Generar interacciones aleatorias'
);

foreach ($ajax_endpoints as $endpoint => $description) {
    if (has_action("wp_ajax_$endpoint")) {
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "✅ <strong>$endpoint</strong> - $description";
        echo "</div>";
        $fixes_applied[] = "✅ Endpoint $endpoint registrado";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "❌ <strong>$endpoint</strong> - $description";
        echo "</div>";
        $issues_found[] = "❌ Endpoint $endpoint NO registrado";
    }
}

// 4. Test like functionality with sample data
echo "<h2>🧪 4. Probando Funcionalidad de Likes</h2>";

if (class_exists('SoloYLibre_User_Interactions')) {
    $interactions = new SoloYLibre_User_Interactions();
    
    // Get a sample photo
    $sample_photo = $wpdb->get_var("
        SELECT ID FROM {$wpdb->posts} 
        WHERE post_type = 'attachment' 
        AND post_mime_type LIKE 'image%' 
        LIMIT 1
    ");
    
    if ($sample_photo) {
        echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>📷 Foto de prueba:</strong> ID $sample_photo";
        
        // Test adding a like
        $test_result = $interactions->add_reaction($sample_photo, 'like', 999, '127.0.0.1', true);
        
        if (!is_wp_error($test_result)) {
            echo "<br>✅ <strong>Prueba de agregar like:</strong> EXITOSA (ID: $test_result)";
            $fixes_applied[] = "✅ Funcionalidad de agregar likes funciona correctamente";
            
            // Test getting reactions
            $reactions = $interactions->get_photo_reactions($sample_photo);
            if (!empty($reactions['reactions']['like']['count'])) {
                echo "<br>✅ <strong>Prueba de obtener likes:</strong> EXITOSA ({$reactions['reactions']['like']['count']} likes)";
                $fixes_applied[] = "✅ Funcionalidad de obtener likes funciona correctamente";
            } else {
                echo "<br>❌ <strong>Prueba de obtener likes:</strong> FALLÓ";
                $issues_found[] = "❌ No se pueden obtener likes correctamente";
            }
        } else {
            echo "<br>❌ <strong>Prueba de agregar like:</strong> FALLÓ - " . $test_result->get_error_message();
            $issues_found[] = "❌ No se pueden agregar likes: " . $test_result->get_error_message();
        }
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "⚠️ No se encontraron fotos para probar";
        echo "</div>";
    }
} else {
    $issues_found[] = "❌ Clase SoloYLibre_User_Interactions no disponible";
}

// 5. Create test interface for likes
echo "<h2>🎯 5. Interfaz de Prueba de Likes</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🧪 Probar Likes en Tiempo Real</h3>";

// Get some sample photos
$sample_photos = $wpdb->get_results("
    SELECT ID, post_title 
    FROM {$wpdb->posts} 
    WHERE post_type = 'attachment' 
    AND post_mime_type LIKE 'image%' 
    LIMIT 5
");

if (!empty($sample_photos)) {
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    foreach ($sample_photos as $photo) {
        $thumbnail = wp_get_attachment_image_url($photo->ID, 'thumbnail');
        $current_likes = 0;
        
        if ($table_exists) {
            if ($has_reaction_type) {
                $current_likes += $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE photo_id = %d AND reaction_type = 'like'", $photo->ID));
            }
            if ($has_interaction_type) {
                $current_likes += $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE photo_id = %d AND interaction_type = 'like'", $photo->ID));
            }
        }
        
        echo "<div style='background: white; padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";
        if ($thumbnail) {
            echo "<img src='$thumbnail' style='width: 100%; height: 100px; object-fit: cover; border-radius: 5px; margin-bottom: 10px;'>";
        }
        echo "<div style='font-size: 12px; margin-bottom: 10px;'>" . ($photo->post_title ?: "Photo {$photo->ID}") . "</div>";
        echo "<div style='margin-bottom: 10px;'>";
        echo "<span id='likes-count-{$photo->ID}' style='font-weight: bold; color: #dc3545;'>💖 $current_likes</span>";
        echo "</div>";
        echo "<button onclick='testLike({$photo->ID})' style='background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 20px; cursor: pointer;'>❤️ Like</button>";
        echo "</div>";
    }
    
    echo "</div>";
} else {
    echo "<p>No se encontraron fotos para mostrar.</p>";
}

echo "</div>";

// 6. JavaScript for testing likes
echo "<script>
function testLike(photoId) {
    // Create nonce for testing
    fetch('" . admin_url('admin-ajax.php') . "', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'add_photo_reaction',
            nonce: '" . wp_create_nonce('soloylibre_interactions_nonce') . "',
            photo_id: photoId,
            reaction_type: 'like'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const likesElement = document.getElementById('likes-count-' + photoId);
            if (data.data.reactions && data.data.reactions.like) {
                likesElement.innerHTML = '💖 ' + data.data.reactions.like.count;
            } else {
                // Increment current count
                const currentCount = parseInt(likesElement.textContent.replace('💖 ', ''));
                likesElement.innerHTML = '💖 ' + (currentCount + 1);
            }
            
            // Show success message
            showTestMessage('✅ Like agregado exitosamente!', 'success');
        } else {
            showTestMessage('❌ Error: ' + (data.data || 'Error desconocido'), 'error');
        }
    })
    .catch(error => {
        showTestMessage('❌ Error de conexión: ' + error.message, 'error');
    });
}

function showTestMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        background: \${type === 'success' ? '#28a745' : '#dc3545'};
    `;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
</script>";

// 7. Statistics and recommendations
echo "<h2>📊 7. Estadísticas y Recomendaciones</h2>";

$total_checks = count($fixes_applied) + count($issues_found);
$success_rate = count($fixes_applied) / $total_checks * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

// Success Card
echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($fixes_applied) . "</div>";
echo "<div>✅ Verificaciones Exitosas</div>";
echo "</div>";

// Issues Card
echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($issues_found) . "</div>";
echo "<div>❌ Problemas Encontrados</div>";
echo "</div>";

// Success Rate Card
echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Tasa de Éxito</div>";
echo "</div>";

echo "</div>";

// Show results
if (!empty($fixes_applied)) {
    echo "<h3 style='color: #28a745;'>✅ Sistema Funcionando Correctamente:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li>$fix</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($issues_found)) {
    echo "<h3 style='color: #dc3545;'>❌ Problemas Que Requieren Atención:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ul>";
    foreach ($issues_found as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// 8. Quick fix button
echo "<h2>🔧 8. Reparación Rápida</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<button onclick='fixLikesSystem()' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; border: none; border-radius: 25px; cursor: pointer; font-size: 16px; font-weight: bold;'>🔧 Reparar Sistema de Likes</button>";
echo "</div>";

echo "<script>
function fixLikesSystem() {
    if (confirm('¿Estás seguro de que quieres reparar el sistema de likes? Esto recreará la tabla si es necesario.')) {
        window.location.href = 'fix-likes-system.php';
    }
}
</script>";

// 9. Final status
echo "<div style='background: " . ($success_rate >= 80 ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' : 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)') . "; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h2 style='margin: 0 0 15px 0; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo ($success_rate >= 80 ? '💖' : '⚠️') . " Sistema de Likes " . ($success_rate >= 80 ? 'Funcionando' : 'Requiere Atención') . "</h2>";
echo "<p style='margin: 0; font-size: 18px; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo "Tasa de éxito: <strong>" . round($success_rate, 1) . "%</strong>";
echo "</p>";

if ($success_rate >= 80) {
    echo "<p style='margin: 10px 0 0 0; color: #155724; font-weight: bold;'>¡Los likes se están grabando correctamente en la base de datos!</p>";
} else {
    echo "<p style='margin: 10px 0 0 0; color: #856404; font-weight: bold;'>Revisa los problemas arriba para asegurar que los likes se graben correctamente.</p>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Verificación del sistema de likes completada</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
