<?php
/**
 * Verify Enhanced Dashboard - SoloYLibre Gallery Plugin
 * Test all enhanced dashboard features and graphics
 * Developed by JEYKO AI for Jose L <PERSON>carnac<PERSON> (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>📊 Verificación del Dashboard Mejorado</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Verificando gráficos y elementos visuales avanzados</p>";

$tests_passed = array();
$tests_failed = array();

// 1. Check Chart.js availability
echo "<h2>📈 1. Verificando Chart.js</h2>";

echo "<script>
if (typeof Chart !== 'undefined') {
    document.write('<div style=\"background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;\">✅ <strong>Chart.js cargado correctamente</strong> - Versión: ' + Chart.version + '</div>');
} else {
    document.write('<div style=\"background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;\">❌ <strong>Chart.js NO disponible</strong></div>');
}
</script>";

// 2. Check glass system availability
echo "<h2>🎨 2. Verificando Sistema Glass Mejorado</h2>";

if (class_exists('SoloYLibre_Glass_Menu_System')) {
    $tests_passed[] = "✅ Sistema Glass disponible";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Sistema Glass funcionando correctamente</strong>";
    echo "</div>";
} else {
    $tests_failed[] = "❌ Sistema Glass NO disponible";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ <strong>Sistema Glass NO disponible</strong>";
    echo "</div>";
}

// 3. Check enhanced AJAX endpoints
echo "<h2>🔗 3. Verificando Endpoints AJAX Mejorados</h2>";

$ajax_endpoints = array(
    'glass_get_stats' => 'Obtener estadísticas mejoradas',
    'glass_add_like' => 'Sistema de likes',
    'glass_load_photos' => 'Cargar fotos con filtros',
    'glass_create_quick_post' => 'Crear posts rápidos'
);

foreach ($ajax_endpoints as $endpoint => $description) {
    if (has_action("wp_ajax_$endpoint")) {
        $tests_passed[] = "✅ Endpoint $endpoint funcionando";
        echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "✅ <strong>$endpoint:</strong> $description";
        echo "</div>";
    } else {
        $tests_failed[] = "❌ Endpoint $endpoint NO registrado";
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "❌ <strong>$endpoint:</strong> $description";
        echo "</div>";
    }
}

// 4. Test statistics data
echo "<h2>📊 4. Probando Datos de Estadísticas</h2>";

global $wpdb;

$total_photos = $wpdb->get_var("
    SELECT COUNT(*) 
    FROM {$wpdb->posts} 
    WHERE post_type = 'attachment' 
    AND post_mime_type LIKE 'image%'
");

$published_photos = count(get_option('soloylibre_published_photos', array()));
$private_photos = count(get_option('soloylibre_private_photos', array()));
$unwanted_photos = count(get_option('soloylibre_unwanted_photos', array()));
$available_photos = $total_photos - $published_photos - $private_photos - $unwanted_photos;

// Get interaction stats
$interactions_table = $wpdb->prefix . 'soloylibre_interactions';
$total_likes = $wpdb->get_var("SELECT COUNT(*) FROM $interactions_table WHERE reaction_type = 'like'") ?: 0;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;'>";

echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>$total_photos</div>";
echo "<div style='font-size: 12px;'>📷 Total Fotos</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>$published_photos</div>";
echo "<div style='font-size: 12px;'>📝 Publicadas</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>$private_photos</div>";
echo "<div style='font-size: 12px;'>🔒 Privadas</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<div style='font-size: 24px; font-weight: bold;'>$total_likes</div>";
echo "<div style='font-size: 12px;'>💖 Likes</div>";
echo "</div>";

echo "</div>";

if ($total_photos > 0) {
    $tests_passed[] = "✅ Datos de estadísticas disponibles";
} else {
    $tests_failed[] = "❌ No hay datos de estadísticas";
}

// 5. Test chart functionality
echo "<h2>📈 5. Probando Funcionalidad de Gráficos</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🧪 Gráfico de Prueba</h3>";
echo "<canvas id='testChart' width='400' height='200'></canvas>";
echo "</div>";

echo "<script>
// Test Chart.js functionality
if (typeof Chart !== 'undefined') {
    const ctx = document.getElementById('testChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'],
                datasets: [{
                    label: 'Fotos Subidas',
                    data: [12, 19, 8, 15, 22, 18, 25],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        document.write('<div style=\"background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;\">✅ <strong>Gráfico de prueba creado exitosamente</strong></div>');
    } else {
        document.write('<div style=\"background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;\">❌ <strong>No se pudo crear el canvas para el gráfico</strong></div>');
    }
} else {
    document.write('<div style=\"background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;\">❌ <strong>Chart.js no disponible para crear gráficos</strong></div>');
}
</script>";

// 6. Test dashboard features
echo "<h2>🎯 6. Características del Dashboard Mejorado</h2>";

$dashboard_features = array(
    'Animaciones CSS avanzadas',
    'Gráficos interactivos con Chart.js',
    'Tarjetas de estadísticas animadas',
    'Feed de actividad en tiempo real',
    'Efectos glassmorphism',
    'Gradientes y partículas flotantes',
    'Responsive design',
    'Auto-refresh de datos'
);

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h4>🚀 Características Implementadas:</h4>";
echo "<ul>";
foreach ($dashboard_features as $feature) {
    echo "<li>✅ $feature</li>";
    $tests_passed[] = "✅ Característica: $feature";
}
echo "</ul>";
echo "</div>";

// 7. Test responsive design
echo "<h2>📱 7. Verificando Diseño Responsive</h2>";

echo "<script>
function checkResponsive() {
    const width = window.innerWidth;
    let responsiveStatus = '';
    
    if (width >= 1200) {
        responsiveStatus = '🖥️ Desktop (1200px+)';
    } else if (width >= 768) {
        responsiveStatus = '💻 Tablet (768px+)';
    } else {
        responsiveStatus = '📱 Mobile (<768px)';
    }
    
    document.write('<div style=\"background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;\">✅ <strong>Diseño responsive funcionando:</strong> ' + responsiveStatus + ' (Ancho: ' + width + 'px)</div>');
}

checkResponsive();
</script>";

// 8. Performance test
echo "<h2>⚡ 8. Test de Rendimiento</h2>";

$start_time = microtime(true);

// Simulate dashboard data loading
for ($i = 0; $i < 1000; $i++) {
    $dummy = array('test' => $i);
}

$end_time = microtime(true);
$execution_time = ($end_time - $start_time) * 1000; // Convert to milliseconds

if ($execution_time < 100) {
    $tests_passed[] = "✅ Rendimiento excelente: {$execution_time}ms";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Rendimiento excelente:</strong> " . round($execution_time, 2) . "ms";
    echo "</div>";
} else {
    $tests_failed[] = "⚠️ Rendimiento lento: {$execution_time}ms";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "⚠️ <strong>Rendimiento aceptable:</strong> " . round($execution_time, 2) . "ms";
    echo "</div>";
}

// 9. Statistics
echo "<h2>📊 9. Estadísticas de Verificación</h2>";

$total_tests = count($tests_passed) + count($tests_failed);
$success_rate = count($tests_passed) / $total_tests * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

// Success Card
echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($tests_passed) . "</div>";
echo "<div>✅ Tests Exitosos</div>";
echo "</div>";

// Failed Card
echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . count($tests_failed) . "</div>";
echo "<div>❌ Tests Fallidos</div>";
echo "</div>";

// Success Rate Card
echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Tasa de Éxito</div>";
echo "</div>";

echo "</div>";

// 10. Test links
echo "<h2>🔗 10. Enlaces de Prueba</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-dashboard' target='_blank' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>📊 Ver Dashboard Mejorado</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-quick-posts' target='_blank' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>⚡ Posts Rápidos</a>";
echo "</div>";

// 11. Final status
echo "<div style='background: " . ($success_rate >= 90 ? 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)' : 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)') . "; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h2 style='margin: 0 0 15px 0; color: " . ($success_rate >= 90 ? '#155724' : '#856404') . ";'>";
echo ($success_rate >= 90 ? '🎉' : '⚠️') . " Dashboard Mejorado " . ($success_rate >= 90 ? 'Completamente Funcional' : 'Requiere Atención') . "</h2>";
echo "<p style='margin: 0; font-size: 18px; color: " . ($success_rate >= 90 ? '#155724' : '#856404') . ";'>";
echo "Tasa de éxito: <strong>" . round($success_rate, 1) . "%</strong>";
echo "</p>";

if ($success_rate >= 90) {
    echo "<p style='margin: 10px 0 0 0; color: #155724; font-weight: bold;'>¡El dashboard tiene gráficos modernos y elementos visuales espectaculares!</p>";
} else {
    echo "<p style='margin: 10px 0 0 0; color: #856404; font-weight: bold;'>Revisa los tests fallidos arriba para completar las mejoras.</p>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Dashboard mejorado con gráficos y bainas bacanas</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
