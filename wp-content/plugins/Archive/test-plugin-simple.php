<?php
/**
 * Plugin Name: SoloYLibre Gallery Test
 * Plugin URI: https://soloylibre.com
 * Description: Test version to identify critical errors
 * Version: 1.0.0
 * Author: JEYKO AI for Jose L Encarnacion (JoseTusabe)
 * Author URI: https://josetusabe.com
 * Text Domain: soloylibre-gallery
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SOLOYLIBRE_GALLERY_VERSION', '1.0.0');
define('SOLOYLIBRE_GALLERY_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SOLOYLIBRE_GALLERY_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * Main Plugin Class - Simplified Version
 */
class SoloYLibre_Gallery_Test {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Plugin initialization
        load_plugin_textdomain('soloylibre-gallery', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'SoloYLibre Test',
            'SoloYLibre Test',
            'manage_options',
            'soloylibre-test',
            array($this, 'admin_page'),
            'dashicons-camera',
            30
        );
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>🧪 SoloYLibre Gallery Test</h1>
            <p>🇩🇴 JoseTusabe Photography - Plugin de prueba funcionando correctamente</p>
            
            <div style="background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h2>✅ Test Exitoso</h2>
                <p>Si puedes ver esta página, el plugin básico está funcionando.</p>
                <p><strong>Versión:</strong> <?php echo SOLOYLIBRE_GALLERY_VERSION; ?></p>
                <p><strong>WordPress:</strong> <?php echo get_bloginfo('version'); ?></p>
                <p><strong>PHP:</strong> <?php echo PHP_VERSION; ?></p>
            </div>
            
            <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>🔍 Información del Sistema</h3>
                <ul>
                    <li><strong>Plugin URL:</strong> <?php echo SOLOYLIBRE_GALLERY_PLUGIN_URL; ?></li>
                    <li><strong>Plugin Path:</strong> <?php echo SOLOYLIBRE_GALLERY_PLUGIN_PATH; ?></li>
                    <li><strong>WordPress Debug:</strong> <?php echo WP_DEBUG ? 'Activado' : 'Desactivado'; ?></li>
                    <li><strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></li>
                </ul>
            </div>
            
            <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>🔧 Próximos Pasos</h3>
                <p>Si este plugin funciona pero el principal no, el problema está en el código complejo.</p>
                <ol>
                    <li>Desactiva el plugin principal</li>
                    <li>Activa este plugin de prueba</li>
                    <li>Verifica que WordPress funcione</li>
                    <li>Identifica el archivo problemático</li>
                </ol>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="<?php echo admin_url('plugins.php'); ?>" class="button button-primary">🔧 Ir a Plugins</a>
                <a href="<?php echo admin_url(); ?>" class="button button-secondary">🏠 Dashboard</a>
            </div>
        </div>
        <?php
    }
    
    public function activate() {
        // Plugin activation
        add_option('soloylibre_test_activated', current_time('mysql'));
        
        // Create a simple option to test database
        update_option('soloylibre_test_version', SOLOYLIBRE_GALLERY_VERSION);
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        // Plugin deactivation
        delete_option('soloylibre_test_activated');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
}

// Initialize the plugin
function soloylibre_gallery_test_init() {
    return SoloYLibre_Gallery_Test::get_instance();
}

// Start the plugin
add_action('plugins_loaded', 'soloylibre_gallery_test_init');

// Add admin notice for successful activation
add_action('admin_notices', function() {
    if (get_transient('soloylibre_test_activated')) {
        ?>
        <div class="notice notice-success is-dismissible">
            <p><strong>🎉 SoloYLibre Gallery Test activado exitosamente!</strong> El plugin básico está funcionando correctamente.</p>
        </div>
        <?php
        delete_transient('soloylibre_test_activated');
    }
});

// Set activation transient
register_activation_hook(__FILE__, function() {
    set_transient('soloylibre_test_activated', true, 5);
});
?>
