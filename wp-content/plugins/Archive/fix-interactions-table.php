<?php
/**
 * Fix Interactions Table - Remove UNIQUE constraint to allow unlimited reactions
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

global $wpdb;

echo "<h1>🔧 Reparando Tabla de Interacciones</h1>";
echo "<p>Eliminando restricciones para permitir interacciones ilimitadas...</p>";

$table_name = $wpdb->prefix . 'soloylibre_interactions';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if (!$table_exists) {
    echo "<p style='color: red;'>❌ La tabla $table_name no existe.</p>";
    echo "<p>Creando tabla nueva...</p>";
    
    // Create new table without UNIQUE constraint
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        photo_id bigint(20) NOT NULL,
        user_id bigint(20) DEFAULT NULL,
        user_ip varchar(45) DEFAULT NULL,
        reaction_type varchar(20) NOT NULL,
        is_simulated tinyint(1) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY photo_id (photo_id),
        KEY user_id (user_id),
        KEY reaction_type (reaction_type),
        KEY photo_reaction (photo_id, reaction_type)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    echo "<p style='color: green;'>✅ Tabla creada exitosamente sin restricciones.</p>";
} else {
    echo "<p style='color: blue;'>ℹ️ La tabla existe. Verificando estructura...</p>";
    
    // Check if UNIQUE constraint exists
    $constraints = $wpdb->get_results("SHOW INDEX FROM $table_name WHERE Key_name = 'unique_user_photo_reaction'");
    
    if (!empty($constraints)) {
        echo "<p style='color: orange;'>⚠️ Encontrada restricción UNIQUE. Eliminando...</p>";
        
        // Drop the UNIQUE constraint
        $result = $wpdb->query("ALTER TABLE $table_name DROP INDEX unique_user_photo_reaction");
        
        if ($result !== false) {
            echo "<p style='color: green;'>✅ Restricción UNIQUE eliminada exitosamente.</p>";
        } else {
            echo "<p style='color: red;'>❌ Error al eliminar restricción: " . $wpdb->last_error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ No se encontraron restricciones UNIQUE. La tabla está lista.</p>";
    }
    
    // Add new index if it doesn't exist
    $photo_reaction_index = $wpdb->get_results("SHOW INDEX FROM $table_name WHERE Key_name = 'photo_reaction'");
    
    if (empty($photo_reaction_index)) {
        echo "<p>Agregando índice optimizado...</p>";
        $wpdb->query("ALTER TABLE $table_name ADD KEY photo_reaction (photo_id, reaction_type)");
        echo "<p style='color: green;'>✅ Índice agregado.</p>";
    }
}

// Show current table structure
echo "<h2>📋 Estructura Actual de la Tabla</h2>";
$columns = $wpdb->get_results("DESCRIBE $table_name");
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>{$column->Field}</td>";
    echo "<td>{$column->Type}</td>";
    echo "<td>{$column->Null}</td>";
    echo "<td>{$column->Key}</td>";
    echo "<td>{$column->Default}</td>";
    echo "<td>{$column->Extra}</td>";
    echo "</tr>";
}
echo "</table>";

// Show indexes
echo "<h2>🔍 Índices de la Tabla</h2>";
$indexes = $wpdb->get_results("SHOW INDEX FROM $table_name");
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Nombre</th><th>Columna</th><th>Único</th><th>Tipo</th></tr>";
foreach ($indexes as $index) {
    echo "<tr>";
    echo "<td>{$index->Key_name}</td>";
    echo "<td>{$index->Column_name}</td>";
    echo "<td>" . ($index->Non_unique ? 'No' : 'Sí') . "</td>";
    echo "<td>{$index->Index_type}</td>";
    echo "</tr>";
}
echo "</table>";

// Show current interaction counts
echo "<h2>📊 Estadísticas Actuales</h2>";
$total_interactions = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
$total_photos_with_interactions = $wpdb->get_var("SELECT COUNT(DISTINCT photo_id) FROM $table_name");
$reaction_types = $wpdb->get_results("SELECT reaction_type, COUNT(*) as count FROM $table_name GROUP BY reaction_type ORDER BY count DESC");

echo "<p><strong>Total de interacciones:</strong> " . number_format($total_interactions) . "</p>";
echo "<p><strong>Fotos con interacciones:</strong> " . number_format($total_photos_with_interactions) . "</p>";

if (!empty($reaction_types)) {
    echo "<h3>Por tipo de reacción:</h3>";
    echo "<ul>";
    foreach ($reaction_types as $reaction) {
        echo "<li><strong>{$reaction->reaction_type}:</strong> " . number_format($reaction->count) . "</li>";
    }
    echo "</ul>";
}

echo "<h2>🎯 Prueba de Interacciones Ilimitadas</h2>";
echo "<p>La tabla ahora permite interacciones ilimitadas. Los usuarios pueden:</p>";
echo "<ul>";
echo "<li>✅ Dar múltiples likes a la misma foto</li>";
echo "<li>✅ Reaccionar sin límites</li>";
echo "<li>✅ Generar miles de interacciones</li>";
echo "<li>✅ No hay restricciones de usuario único</li>";
echo "</ul>";

echo "<p style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724;'>";
echo "<strong>🎉 ¡Reparación Completada!</strong><br>";
echo "El sistema de interacciones ahora permite reacciones ilimitadas.";
echo "</p>";

echo "<p><a href='../../../wp-admin/admin.php?page=soloylibre-wizard' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔙 Volver al Wizard</a></p>";
?>
