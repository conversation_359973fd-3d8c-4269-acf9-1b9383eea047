/**
 * SoloYLibre Gallery Editor Integration Styles
 * CSS for WordPress editor integration
 * 
 * @package SoloYLibre_Gallery_Pro
 * @version 5.3.0
 * <AUTHOR> AI for <PERSON> (JoseTusabe)
 */

/* Editor Button */
.soloylibre-editor-btn {
    background: linear-gradient(135deg, #CE1126, #002D62) !important;
    color: white !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 8px 16px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.soloylibre-editor-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(206, 17, 38, 0.3) !important;
}

.soloylibre-editor-btn .wp-media-buttons-icon {
    margin-right: 5px !important;
}

/* Modal Overlay */
.soloylibre-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Modal */
.soloylibre-modal.editor-modal {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
    display: flex;
    flex-direction: column;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Modal Header */
.modal-header {
    background: linear-gradient(135deg, #CE1126, #002D62);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 1.8rem;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 50%;
    transition: background 0.3s ease;
    line-height: 1;
}

.close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Modal Content */
.modal-content {
    padding: 0;
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* Tabs */
.editor-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    flex-shrink: 0;
}

.tab-btn {
    background: none;
    border: none;
    padding: 15px 25px;
    cursor: pointer;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-btn.active {
    color: #CE1126;
    background: white;
    border-bottom-color: #CE1126;
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 30px;
    flex: 1;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

/* Photo Controls */
.photo-controls {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.control-group input,
.control-group select {
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.control-group input:focus,
.control-group select:focus {
    outline: none;
    border-color: #CE1126;
    box-shadow: 0 0 0 2px rgba(206, 17, 38, 0.1);
}

/* Photo Stats */
.photo-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: #e3f2fd;
    border-radius: 8px;
    font-weight: 600;
    color: #1976d2;
}

/* Loading Section */
.loading-section {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #CE1126;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Photos Grid */
.editor-photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.editor-photo-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    background: #f8f9fa;
}

.editor-photo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.editor-photo-item.selected {
    border-color: #CE1126;
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(206, 17, 38, 0.3);
}

.editor-photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
}

.editor-photo-item:hover .photo-overlay,
.editor-photo-item.selected .photo-overlay {
    opacity: 1;
}

.photo-info {
    color: white;
    font-size: 12px;
}

.photo-title {
    display: block;
    font-weight: 600;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.photo-size {
    display: block;
    opacity: 0.8;
}

.photo-actions {
    display: flex;
    justify-content: flex-end;
}

.select-btn {
    background: #CE1126;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.select-btn:hover {
    background: #a00e1f;
    transform: scale(1.1);
}

.select-btn.selected {
    background: #28a745;
}

/* Load More Section */
.load-more-section {
    text-align: center;
    margin: 20px 0;
}

/* Auto Generate Section */
.auto-generate-section {
    text-align: center;
}

.auto-generate-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.auto-generate-section p {
    color: #6c757d;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.auto-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
    text-align: left;
}

.auto-controls .control-group small {
    color: #6c757d;
    font-size: 12px;
    margin-top: 5px;
}

.auto-preview {
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.auto-preview h5 {
    color: #495057;
    margin-bottom: 15px;
}

.auto-photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
}

.auto-photo-item {
    aspect-ratio: 1;
    border-radius: 6px;
    overflow: hidden;
}

.auto-photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Dominican Styles */
.styles-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.styles-section p {
    color: #6c757d;
    margin-bottom: 30px;
}

.dominican-styles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.style-option {
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
    border: 3px solid transparent;
}

.style-option:hover {
    transform: scale(1.05);
}

.style-option.selected {
    border-color: #CE1126;
    transform: scale(1.05);
}

.style-preview {
    height: 150px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bandera-style {
    background: linear-gradient(135deg, #CE1126 0%, #FFFFFF 50%, #002D62 100%);
}

.merengue-style {
    background: linear-gradient(45deg, #FF6B35, #F7931E, #FFD23F);
}

.caribe-style {
    background: linear-gradient(135deg, #00B4DB 0%, #0083B0 100%);
}

.colonial-style {
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%);
}

.style-overlay {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 20px;
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.style-option:hover .style-overlay {
    opacity: 1;
}

.style-overlay h5 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
}

.style-overlay p {
    margin: 0;
    font-size: 0.9rem;
}

/* Modal Footer */
.modal-footer {
    padding: 20px 30px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.gallery-options {
    display: flex;
    gap: 20px;
    align-items: center;
}

.gallery-options .option-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.gallery-options .option-group label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    margin: 0;
}

.gallery-options select {
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.gallery-options input[type="checkbox"] {
    margin-right: 5px;
}

.modal-actions {
    display: flex;
    gap: 10px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-primary {
    background: #CE1126;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #a00e1f;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

.btn-large {
    padding: 15px 30px;
    font-size: 16px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Notifications */
.soloylibre-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 100001;
    animation: slideInRight 0.3s ease;
    max-width: 400px;
}

.soloylibre-notification.success {
    background: #28a745;
}

.soloylibre-notification.error {
    background: #dc3545;
}

.soloylibre-notification.info {
    background: #17a2b8;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .soloylibre-modal.editor-modal {
        width: 95%;
        max-height: 95vh;
    }
    
    .photo-controls {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .editor-photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 10px;
    }
    
    .auto-controls {
        grid-template-columns: 1fr;
    }
    
    .dominican-styles-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-footer {
        flex-direction: column;
        gap: 15px;
    }
    
    .gallery-options {
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }
    
    .modal-actions {
        width: 100%;
        justify-content: center;
    }
}
