/**
 * SoloYLibre Gallery Pro - Statistics Dashboard Styles
 * Professional statistics interface
 * Developed by <PERSON><PERSON><PERSON>KO <PERSON> for <PERSON> (JoseTusabe)
 */

/* ===== VARIABLES ===== */
:root {
    --dominican-red: #CE1126;
    --dominican-blue: #002D62;
    --dominican-white: #FFFFFF;
    --soloylibre-primary: #667eea;
    --soloylibre-secondary: #764ba2;
    --success-green: #28a745;
    --warning-yellow: #ffc107;
    --danger-red: #dc3545;
    --info-blue: #17a2b8;
    --dark-bg: #2d3748;
    --light-bg: #f8f9fa;
    --border-color: #e9ecef;
    --text-dark: #333333;
    --text-light: #666666;
    --text-muted: #999999;
    --border-radius: 12px;
    --shadow-light: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-medium: 0 8px 25px rgba(0,0,0,0.15);
    --shadow-heavy: 0 15px 35px rgba(0,0,0,0.2);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== DASHBOARD CONTAINER ===== */
.soloylibre-statistics-dashboard {
    background: var(--light-bg);
    min-height: 100vh;
    padding: 0;
    margin: 0 0 0 -20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== HEADER ===== */
.statistics-header {
    background: linear-gradient(135deg, var(--dominican-red) 0%, var(--dominican-blue) 100%);
    color: white;
    padding: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    box-shadow: var(--shadow-medium);
}

.header-content h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 900;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-subtitle {
    margin: 0;
    font-size: 1.2rem;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 10px;
}

.dominican-flag {
    font-size: 1.5rem;
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

.header-actions {
    display: flex;
    gap: 15px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.btn-secondary {
    background: white;
    color: var(--dominican-red);
    border: 1px solid white;
}

.btn-secondary:hover {
    background: var(--light-bg);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.8rem;
}

/* ===== QUICK STATS GRID ===== */
.quick-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    padding: 0 40px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow-light);
    border-left: 5px solid var(--soloylibre-primary);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.stat-card-primary { border-left-color: var(--soloylibre-primary); }
.stat-card-secondary { border-left-color: var(--soloylibre-secondary); }
.stat-card-success { border-left-color: var(--success-green); }
.stat-card-warning { border-left-color: var(--warning-yellow); }

.stat-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--text-dark);
}

.stat-content p {
    margin: 0 0 8px 0;
    color: var(--text-light);
    font-weight: 600;
    font-size: 1rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
}

.stat-change.positive {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-green);
}

.stat-change.negative {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-red);
}

/* ===== CHARTS SECTION ===== */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    padding: 0 40px;
    margin-bottom: 40px;
}

.chart-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow-light);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.chart-header h3 {
    margin: 0;
    color: var(--text-dark);
    font-size: 1.3rem;
    font-weight: 700;
}

.chart-controls select {
    padding: 8px 15px;
    border: 2px solid var(--border-color);
    border-radius: 20px;
    background: white;
    font-size: 0.9rem;
    outline: none;
    cursor: pointer;
    transition: var(--transition);
}

.chart-controls select:focus {
    border-color: var(--soloylibre-primary);
}

/* ===== TABLES SECTION ===== */
.tables-section {
    padding: 0 40px;
    margin-bottom: 40px;
}

.table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    margin-bottom: 30px;
    overflow: hidden;
}

.table-header {
    background: var(--light-bg);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.table-header h3 {
    margin: 0;
    color: var(--text-dark);
    font-size: 1.2rem;
    font-weight: 700;
}

.table-responsive {
    overflow-x: auto;
}

.statistics-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.statistics-table th {
    background: var(--light-bg);
    padding: 15px 20px;
    text-align: left;
    font-weight: 700;
    color: var(--text-dark);
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.statistics-table td {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.statistics-table tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

.photo-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-bg);
}

.photo-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    font-size: 1.5rem;
    color: var(--text-muted);
}

.photo-meta,
.album-meta {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 2px;
}

.category-badge {
    background: var(--soloylibre-primary);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-number {
    font-weight: 700;
    color: var(--text-dark);
}

.stat-number.likes {
    color: var(--danger-red);
}

.date-text {
    color: var(--text-light);
    font-size: 0.9rem;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-green);
}

.status-inactive {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-red);
}

/* ===== ENGAGEMENT SECTION ===== */
.engagement-section {
    padding: 0 40px;
    margin-bottom: 40px;
}

.engagement-header {
    text-align: center;
    margin-bottom: 40px;
}

.engagement-header h3 {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.engagement-header p {
    color: var(--text-light);
    font-size: 1.1rem;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.insight-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    border-top: 4px solid var(--soloylibre-primary);
}

.insight-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.insight-card:nth-child(2) { border-top-color: var(--success-green); }
.insight-card:nth-child(3) { border-top-color: var(--warning-yellow); }
.insight-card:nth-child(4) { border-top-color: var(--dominican-red); }

.insight-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.insight-card h4 {
    color: var(--text-dark);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.insight-value {
    font-size: 2rem;
    font-weight: 900;
    color: var(--soloylibre-primary);
    margin-bottom: 10px;
}

.insight-description {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

/* ===== GEOGRAPHIC SECTION ===== */
.geographic-section {
    padding: 0 40px;
    margin-bottom: 40px;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-header h3 {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 10px;
}

.section-header p {
    color: var(--text-light);
    font-size: 1.1rem;
}

.geographic-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.country-stats,
.dominican-focus {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow-light);
}

.country-stats h4,
.dominican-focus h4 {
    color: var(--text-dark);
    margin-bottom: 20px;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.country-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.country-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    border-radius: 8px;
    transition: var(--transition);
}

.country-item:hover {
    background: var(--light-bg);
}

.country-flag {
    font-size: 1.5rem;
}

.country-name {
    flex: 1;
    font-weight: 600;
    color: var(--text-dark);
}

.country-percentage {
    font-weight: 700;
    color: var(--soloylibre-primary);
}

.dominican-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: var(--text-light);
    font-weight: 600;
}

.stat-value {
    font-weight: 700;
    color: var(--dominican-red);
    font-size: 1.1rem;
}

/* ===== FOOTER ===== */
.statistics-footer {
    background: var(--dark-bg);
    color: white;
    padding: 40px;
    margin-top: 40px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: center;
}

.photographer-info h4 {
    color: white;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.photographer-info p {
    margin: 5px 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.footer-stats {
    text-align: right;
}

.footer-stats p {
    margin: 5px 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 1200px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .geographic-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .soloylibre-statistics-dashboard {
        margin-left: 0;
    }
    
    .statistics-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 30px 20px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .quick-stats-grid,
    .charts-section,
    .tables-section,
    .engagement-section,
    .geographic-section {
        padding: 0 20px;
    }
    
    .quick-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .insights-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
    }
    
    .footer-stats {
        text-align: center;
    }
    
    .table-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .statistics-table {
        font-size: 0.8rem;
    }
    
    .statistics-table th,
    .statistics-table td {
        padding: 10px 15px;
    }
}

/* ===== LOADING STATES ===== */
.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--soloylibre-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-card,
.chart-container,
.table-container,
.insight-card {
    animation: fadeInUp 0.6s ease forwards;
}

.stat-card:nth-child(2) { animation-delay: 0.1s; }
.stat-card:nth-child(3) { animation-delay: 0.2s; }
.stat-card:nth-child(4) { animation-delay: 0.3s; }
