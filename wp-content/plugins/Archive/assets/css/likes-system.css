
/* SoloYLibre Likes System CSS */
.soloylibre-like-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    font-weight: 500;
}

.soloylibre-like-btn:hover {
    background: linear-gradient(135deg, #ff5252, #d32f2f);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.soloylibre-like-btn.liked {
    background: linear-gradient(135deg, #e91e63, #ad1457);
}

.soloylibre-like-btn.processing {
    opacity: 0.7;
    cursor: not-allowed;
}

.soloylibre-like-btn.heart-animation {
    animation: heartBeat 0.6s ease;
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1.1); }
    75% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

.like-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    animation: slideInRight 0.3s ease;
}

.like-message-success {
    background: linear-gradient(135deg, #4caf50, #388e3c);
}

.like-message-error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
