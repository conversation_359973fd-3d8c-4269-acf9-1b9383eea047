/**
 * SoloYL<PERSON>re Simple Wizard Styles
 * One-page wizard interface
 * Developed by <PERSON><PERSON><PERSON><PERSON> <PERSON> for <PERSON> (JoseTusabe)
 */

/* ===== VARIABLES ===== */
:root {
    --dominican-red: #CE1126;
    --dominican-blue: #002D62;
    --dominican-white: #FFFFFF;
    --soloylibre-primary: #667eea;
    --soloylibre-secondary: #764ba2;
    --success-green: #28a745;
    --warning-yellow: #ffc107;
    --danger-red: #dc3545;
    --dark-bg: #2d3748;
    --light-bg: #f8f9fa;
    --border-color: #e9ecef;
    --text-dark: #333333;
    --text-light: #666666;
    --border-radius: 12px;
    --shadow-light: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-medium: 0 8px 25px rgba(0,0,0,0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== MAIN CONTAINER ===== */
.soloylibre-simple-wizard {
    background: var(--light-bg);
    min-height: 100vh;
    margin: 0 0 0 -20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== HEADER ===== */
.wizard-header {
    background: linear-gradient(135deg, var(--dominican-red) 0%, var(--dominican-blue) 100%);
    color: white;
    padding: 30px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-medium);
}

.header-content h1 {
    margin: 0 0 8px 0;
    font-size: 2.2rem;
    font-weight: 900;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-subtitle {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 10px;
}

.dominican-flag {
    font-size: 1.3rem;
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

.header-actions {
    display: flex;
    gap: 15px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.btn-secondary {
    background: white;
    color: var(--dominican-red);
    border: 1px solid white;
}

.btn-secondary:hover {
    background: var(--light-bg);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--soloylibre-primary);
    border: 2px solid var(--soloylibre-primary);
}

.btn-outline:hover {
    background: var(--soloylibre-primary);
    color: white;
}

/* ===== WIZARD CONTAINER ===== */
.wizard-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 40px;
}

.wizard-form {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

/* ===== FORM SECTIONS ===== */
.form-section {
    padding: 40px;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h2 {
    margin: 0 0 25px 0;
    color: var(--text-dark);
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: var(--transition);
    background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--soloylibre-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* ===== PHOTO SELECTION ===== */
.photo-selection-area {
    text-align: center;
}

.select-photos-button {
    background: linear-gradient(135deg, var(--light-bg) 0%, #ffffff 100%);
    border: 3px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 60px 40px;
    cursor: pointer;
    transition: var(--transition);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.select-photos-button:hover {
    border-color: var(--soloylibre-primary);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, #ffffff 100%);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.upload-icon {
    font-size: 4rem;
    opacity: 0.7;
}

.select-photos-button h3 {
    margin: 0;
    color: var(--text-dark);
    font-size: 1.5rem;
}

.select-photos-button p {
    margin: 0;
    color: var(--text-light);
    font-size: 1rem;
}

.selected-photos-grid {
    margin-top: 30px;
    text-align: left;
}

.selected-photos-grid h4 {
    color: var(--text-dark);
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.photos-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
}

.photo-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: var(--light-bg);
    aspect-ratio: 1;
    cursor: pointer;
    transition: var(--transition);
}

.photo-item:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

.photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-remove {
    position: absolute;
    top: 5px;
    right: 5px;
    background: var(--danger-red);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.photo-item:hover .photo-remove {
    opacity: 1;
}

/* ===== SETTINGS ===== */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.setting-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.setting-group label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-weight: 500;
    margin: 0;
}

.setting-group input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
}

/* ===== SIGNATURE PREVIEW ===== */
.signature-preview {
    background: var(--light-bg);
}

.signature-card {
    background: linear-gradient(135deg, var(--dominican-red) 0%, var(--dominican-blue) 100%);
    color: white;
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
}

.signature-content h3 {
    color: white;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.signature-content p {
    margin: 8px 0;
    opacity: 0.9;
}

.websites {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

.websites a {
    color: white;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.2);
    font-size: 0.9rem;
    transition: var(--transition);
}

.websites a:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* ===== SUBMIT SECTION ===== */
.submit-section {
    text-align: center;
    background: var(--light-bg);
}

.create-gallery-button {
    background: linear-gradient(135deg, var(--dominican-red) 0%, var(--dominican-blue) 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    border-radius: 30px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 12px;
    box-shadow: var(--shadow-medium);
}

.create-gallery-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(206, 17, 38, 0.4);
}

.create-gallery-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-icon {
    font-size: 1.3rem;
}

/* ===== STATUS MESSAGES ===== */
.creation-status,
.creation-success {
    margin-top: 30px;
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
}

.creation-status {
    background: rgba(102, 126, 234, 0.1);
    border: 2px solid var(--soloylibre-primary);
}

.creation-success {
    background: rgba(40, 167, 69, 0.1);
    border: 2px solid var(--success-green);
}

.status-icon,
.success-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.status-text {
    color: var(--soloylibre-primary);
    font-weight: 600;
    font-size: 1.1rem;
}

.success-content h3 {
    color: var(--success-green);
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.success-content p {
    color: var(--text-light);
    margin-bottom: 25px;
}

.success-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* ===== DEBUG PANEL ===== */
.debug-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 500px;
    height: 100vh;
    background: white;
    box-shadow: -5px 0 25px rgba(0,0,0,0.2);
    z-index: 999999;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.debug-panel.open {
    transform: translateX(0);
}

.debug-header {
    background: var(--dark-bg);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.debug-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.close-debug {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
}

.close-debug:hover {
    background: rgba(255, 255, 255, 0.2);
}

.debug-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.debug-loading {
    text-align: center;
    padding: 40px 20px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--soloylibre-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.debug-info {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
}

.debug-section {
    margin-bottom: 25px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.debug-section-header {
    background: var(--light-bg);
    padding: 12px 16px;
    font-weight: 700;
    color: var(--text-dark);
    border-bottom: 1px solid var(--border-color);
}

.debug-section-content {
    padding: 16px;
}

.debug-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    border-bottom: 1px solid #f0f0f0;
}

.debug-item:last-child {
    border-bottom: none;
}

.debug-key {
    font-weight: 600;
    color: var(--text-dark);
}

.debug-value {
    color: var(--text-light);
}

.debug-value.true {
    color: var(--success-green);
}

.debug-value.false {
    color: var(--danger-red);
}

/* ===== FOOTER ===== */
.wizard-footer {
    background: var(--dark-bg);
    color: white;
    padding: 30px 40px;
    margin-top: 40px;
}

.footer-content {
    max-width: 1000px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
    align-items: center;
}

.photographer-info h4 {
    color: white;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.photographer-info p {
    margin: 5px 0;
    opacity: 0.9;
    font-size: 0.95rem;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.footer-links a {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    text-align: center;
    font-size: 0.9rem;
}

.footer-links a:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .soloylibre-simple-wizard {
        margin-left: 0;
    }
    
    .wizard-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 20px;
    }
    
    .header-content h1 {
        font-size: 1.8rem;
    }
    
    .wizard-container {
        padding: 20px;
    }
    
    .form-section {
        padding: 30px 20px;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .success-actions {
        flex-direction: column;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
    }
    
    .debug-panel {
        width: 100%;
    }
    
    .photos-container {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}
