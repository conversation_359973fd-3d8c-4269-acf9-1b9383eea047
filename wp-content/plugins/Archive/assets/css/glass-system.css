
/* Glass System CSS - SoloYLibre Gallery Plugin */
/* Modern glassmorphism interface styles */

.glass-system-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.glass-overlay-disabled {
    display: none !important;
}

.glass-fullscreen-disabled {
    position: static !important;
    z-index: auto !important;
    background: transparent !important;
}

/* Override old overlay styles */
.fullscreen-overlay,
.wizard-overlay,
.bulk-overlay {
    display: none !important;
}

/* Ensure glass cards are visible */
.glass-container,
.glass-card,
.glass-button {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .glass-container {
        margin: 5px;
        padding: 15px;
    }
    
    .glass-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}
