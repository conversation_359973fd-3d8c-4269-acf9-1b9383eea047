/**
 * SoloYLibre Fullscreen Wizard Styles
 * Developed by <PERSON><PERSON><PERSON><PERSON> AI for <PERSON> (JoseTusabe)
 */

/* ===== VARIABLES ===== */
:root {
    --dominican-red: #CE1126;
    --dominican-blue: #002D62;
    --dominican-white: #FFFFFF;
    --soloylibre-primary: #667eea;
    --soloylibre-secondary: #764ba2;
    --success-green: #28a745;
    --warning-yellow: #ffc107;
    --danger-red: #dc3545;
    --dark-bg: #2d3748;
    --light-bg: #f8f9fa;
    --border-radius: 12px;
    --shadow-light: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-medium: 0 8px 25px rgba(0,0,0,0.15);
    --shadow-heavy: 0 15px 35px rgba(0,0,0,0.2);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --z-wizard: 999999;
}

/* ===== FULLSCREEN WIZARD CONTAINER ===== */
.soloylibre-wizard-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, var(--soloylibre-primary) 0%, var(--soloylibre-secondary) 100%);
    z-index: var(--z-wizard);
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #333;
    overflow: hidden;
}

/* ===== WIZARD HEADER ===== */
.wizard-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-light);
}

.wizard-brand h1 {
    margin: 0;
    font-size: 1.8rem;
    color: var(--soloylibre-primary);
    font-weight: 900;
}

.wizard-brand p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.wizard-controls {
    display: flex;
    gap: 10px;
}

.wizard-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 0.9rem;
}

.wizard-btn-primary {
    background: var(--soloylibre-primary);
    color: white;
}

.wizard-btn-primary:hover {
    background: var(--soloylibre-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.wizard-btn-secondary {
    background: var(--light-bg);
    color: #333;
    border: 1px solid #ddd;
}

.wizard-btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.wizard-btn-danger {
    background: var(--danger-red);
    color: white;
}

.wizard-btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.wizard-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* ===== PROGRESS BAR ===== */
.wizard-progress {
    background: rgba(255, 255, 255, 0.9);
    padding: 20px 30px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-bar {
    background: #e9ecef;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 20px;
}

.progress-fill {
    background: linear-gradient(90deg, var(--soloylibre-primary), var(--soloylibre-secondary));
    height: 100%;
    width: 16.66%; /* 1/6 steps */
    transition: width 0.5s ease;
    border-radius: 4px;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex: 1;
    position: relative;
}

.step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e9ecef;
    border: 3px solid #e9ecef;
    transition: var(--transition);
    position: relative;
}

.progress-step.active .step-circle {
    background: var(--soloylibre-primary);
    border-color: var(--soloylibre-primary);
}

.progress-step.completed .step-circle {
    background: var(--success-green);
    border-color: var(--success-green);
}

.progress-step.completed .step-circle::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 0.8rem;
}

.step-label {
    font-size: 0.8rem;
    color: #666;
    text-align: center;
    font-weight: 500;
}

.progress-step.active .step-label {
    color: var(--soloylibre-primary);
    font-weight: 700;
}

/* ===== WIZARD CONTENT ===== */
.wizard-content {
    flex: 1;
    overflow-y: auto;
    background: white;
    position: relative;
}

.wizard-step {
    display: none;
    height: 100%;
    overflow-y: auto;
}

.wizard-step.active {
    display: block;
}

.step-content {
    padding: 40px;
    max-width: 1200px;
    margin: 0 auto;
    min-height: calc(100vh - 200px);
}

/* ===== WELCOME STEP ===== */
.welcome-hero {
    text-align: center;
    margin-bottom: 50px;
}

.dominican-flag {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
}

.welcome-hero h2 {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--soloylibre-primary);
    font-weight: 900;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: #666;
    margin-bottom: 20px;
}

.hero-description {
    font-size: 1.1rem;
    color: #888;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.feature-card {
    background: var(--light-bg);
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
    border-left: 4px solid var(--soloylibre-primary);
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.feature-card.clickable {
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.feature-card.clickable:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-left-color: #CE1126;
}

.feature-card.clickable:active {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    transition: transform 0.3s ease;
}

.feature-card.clickable:hover .feature-icon {
    transform: scale(1.1);
}

.feature-card h3 {
    color: var(--soloylibre-primary);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.feature-action {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.feature-card:hover .feature-action {
    opacity: 1;
    transform: translateY(0);
}

.feature-action .btn {
    background: linear-gradient(135deg, #CE1126, #002D62);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.feature-action .btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(206, 17, 38, 0.3);
}

/* ===== PHOTO SELECTION ===== */
.photo-selection-controls {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
    align-items: center;
}

.search-box {
    display: flex;
    flex: 1;
    min-width: 300px;
}

.search-box input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-right: none;
    border-radius: 25px 0 0 25px;
    font-size: 1rem;
    outline: none;
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--soloylibre-primary);
}

.search-btn {
    padding: 12px 20px;
    background: var(--soloylibre-primary);
    color: white;
    border: none;
    border-radius: 0 25px 25px 0;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--soloylibre-secondary);
}

.filter-controls {
    display: flex;
    gap: 15px;
}

.filter-controls select {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    background: white;
    font-size: 0.9rem;
    outline: none;
    cursor: pointer;
    transition: var(--transition);
}

.filter-controls select:focus {
    border-color: var(--soloylibre-primary);
}

.selected-counter {
    background: var(--soloylibre-primary);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    margin-bottom: 20px;
    display: inline-block;
}

.photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.photo-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
    background: white;
    box-shadow: var(--shadow-light);
}

.photo-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.photo-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

.photo-item:hover img {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.photo-item:hover .photo-overlay {
    opacity: 1;
}

.photo-checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 25px;
    height: 25px;
    background: white;
    border: 2px solid #ddd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    z-index: 10;
}

.photo-item.selected .photo-checkbox {
    background: var(--success-green);
    border-color: var(--success-green);
    color: white;
}

.photo-item.selected .photo-checkbox::after {
    content: '✓';
    font-weight: bold;
    font-size: 0.8rem;
}

.photo-item.selected {
    border: 3px solid var(--success-green);
}

/* ===== LOADING SPINNER ===== */
.loading-spinner {
    text-align: center;
    padding: 50px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e9ecef;
    border-top: 4px solid var(--soloylibre-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

/* ===== ORGANIZE SECTION ===== */
.organize-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.organize-section {
    background: var(--light-bg);
    padding: 30px;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--soloylibre-primary);
}

.organize-section h3 {
    color: var(--soloylibre-primary);
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.selected-photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
}

.selected-photo-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: var(--shadow-light);
}

.selected-photo-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}

.remove-photo {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background: var(--danger-red);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    outline: none;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--soloylibre-primary);
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* ===== SETTINGS FORM ===== */
.settings-form {
    max-width: 600px;
}

.photographer-signature {
    background: linear-gradient(135deg, var(--dominican-red) 0%, var(--dominican-blue) 100%);
    color: white;
    padding: 20px;
    border-radius: var(--border-radius);
    margin-top: 30px;
}

.photographer-signature h4 {
    color: white;
    margin-bottom: 15px;
}

.signature-preview {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    backdrop-filter: blur(10px);
}

.signature-preview p {
    margin: 5px 0;
    font-size: 0.9rem;
}

/* ===== CREATION PREVIEW ===== */
.creation-preview {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.preview-section {
    background: var(--light-bg);
    padding: 30px;
    border-radius: var(--border-radius);
}

.post-preview-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #ddd;
    min-height: 300px;
}

.creation-status {
    background: var(--light-bg);
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
}

.creation-progress {
    padding: 40px 20px;
}

.progress-circle {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
}

.progress-circle .spinner {
    width: 80px;
    height: 80px;
    border-width: 6px;
}

.creation-result {
    padding: 40px 20px;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.creation-result h3 {
    color: var(--success-green);
    margin-bottom: 15px;
}

.result-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 30px;
}

/* ===== COMPLETION ===== */
.completion-hero {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.success-animation {
    font-size: 6rem;
    margin-bottom: 30px;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

.completion-hero h2 {
    font-size: 2.5rem;
    color: var(--success-green);
    margin-bottom: 15px;
}

.completion-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin: 40px 0;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--soloylibre-primary);
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.completion-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 40px;
}

/* ===== WIZARD NAVIGATION ===== */
.wizard-navigation {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 -4px 15px rgba(0,0,0,0.1);
}

.nav-info {
    font-weight: 600;
    color: #666;
}

/* ===== MINIMIZED STATE ===== */
.wizard-minimized {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--soloylibre-primary);
    color: white;
    padding: 15px 20px;
    border-radius: 25px;
    box-shadow: var(--shadow-heavy);
    z-index: var(--z-wizard);
    cursor: pointer;
    transition: var(--transition);
}

.wizard-minimized:hover {
    transform: translateY(-3px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.minimized-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.minimized-icon {
    font-size: 1.2rem;
}

.minimized-text {
    font-weight: 600;
}

.restore-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    transition: var(--transition);
}

.restore-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .wizard-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .wizard-brand h1 {
        font-size: 1.5rem;
    }
    
    .step-content {
        padding: 20px;
    }
    
    .welcome-hero h2 {
        font-size: 2rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .photo-selection-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .organize-grid,
    .creation-preview {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .completion-stats {
        flex-direction: column;
        gap: 20px;
    }
    
    .completion-actions {
        flex-direction: column;
    }
    
    .wizard-navigation {
        padding: 15px 20px;
    }
    
    .progress-steps {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .step-label {
        font-size: 0.7rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.wizard-step.active {
    animation: fadeInUp 0.5s ease;
}

.photo-item {
    animation: slideInRight 0.3s ease;
}

.photo-item:nth-child(even) {
    animation-delay: 0.1s;
}

.photo-item:nth-child(3n) {
    animation-delay: 0.2s;
}

/* ===== NOTIFICATIONS ===== */
.wizard-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    padding: 15px 20px;
    box-shadow: var(--shadow-heavy);
    z-index: calc(var(--z-wizard) + 1);
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 300px;
    max-width: 500px;
    border-left: 4px solid #007cba;
    animation: slideInRight 0.3s ease;
}

.wizard-notification-success {
    border-left-color: var(--success-green);
}

.wizard-notification-warning {
    border-left-color: var(--warning-yellow);
}

.wizard-notification-error {
    border-left-color: var(--danger-red);
}

.notification-icon {
    font-size: 1.2rem;
}

.notification-message {
    flex: 1;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.notification-close:hover {
    background: #f0f0f0;
    color: #333;
}

/* ===== MODAL STYLES FOR NEW FEATURES ===== */
.soloylibre-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.soloylibre-modal {
    background: white;
    border-radius: 15px;
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #CE1126, #002D62);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.close-modal {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-modal:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-content {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px 30px;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Smart Selection Modal */
.smart-selection-modal {
    width: 80%;
    max-width: 1000px;
}

.selection-stats {
    background: #e3f2fd;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
    color: #1976d2;
}

.smart-photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.smart-photo-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
}

.smart-photo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.smart-photo-item.selected {
    border-color: #CE1126;
    transform: translateY(-5px);
}

.smart-photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.smart-photo-item .photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
}

.smart-photo-item:hover .photo-overlay {
    opacity: 1;
}

.select-photo-btn {
    background: #CE1126;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.select-photo-btn:hover {
    background: #a00e1f;
    transform: scale(1.05);
}

.select-photo-btn.selected {
    background: #28a745;
}

/* Dominican Styles Modal */
.dominican-styles-modal {
    width: 85%;
    max-width: 1200px;
}

.dominican-styles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.dominican-style-item {
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.dominican-style-item:hover {
    transform: scale(1.05);
}

.style-preview {
    height: 200px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
}

.style-overlay {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 20px;
    text-align: center;
    border-radius: 10px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dominican-style-item:hover .style-overlay {
    opacity: 1;
}

.style-overlay h4 {
    margin: 0 0 10px 0;
    font-size: 1.2rem;
}

.style-overlay p {
    margin: 0 0 15px 0;
    font-size: 0.9rem;
}

/* Automatic Posts Modal */
.automatic-posts-modal {
    width: 60%;
    max-width: 600px;
}

.auto-post-options {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.option-group label {
    font-weight: 600;
    color: #495057;
}

.option-group select {
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
}

/* Loading Modal */
.loading-modal {
    width: 300px;
    text-align: center;
    padding: 40px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #CE1126;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* Success Modal */
.success-modal {
    width: 70%;
    max-width: 600px;
}

.success-info {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.success-info h4 {
    color: #155724;
    margin: 0 0 10px 0;
}

.success-info p {
    color: #155724;
    margin: 5px 0;
}

.featured-image-info {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #CE1126;
    text-align: center;
}

.featured-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 10px;
    object-fit: cover;
}

.featured-image-info p {
    color: #495057 !important;
    font-size: 14px;
    margin: 0;
}

.featured-image-success {
    margin: 15px 0;
    padding: 10px;
    background: #e8f5e8;
    border-radius: 6px;
    border-left: 3px solid #28a745;
    text-align: center;
}

.featured-preview-small {
    max-width: 120px;
    max-height: 80px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
    margin-bottom: 8px;
    object-fit: cover;
}

.featured-image-success p {
    color: #155724 !important;
    font-size: 13px;
    margin: 0;
    font-weight: 600;
}

.success-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 20px 0;
}

/* Success Notification */
.success-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 10001;
    animation: slideInRight 0.3s ease;
}

/* Responsive for Modals */
@media (max-width: 768px) {
    .soloylibre-modal {
        width: 95%;
        max-height: 95%;
    }

    .smart-photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .dominican-styles-grid {
        grid-template-columns: 1fr;
    }

    .automatic-posts-modal {
        width: 90%;
    }

    .modal-content {
        padding: 20px;
    }

    .modal-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .success-actions {
        flex-direction: column;
    }

    .automatic-posts-expanded-modal {
        width: 98%;
        max-height: 98vh;
    }

    .auto-post-options {
        grid-template-columns: 1fr;
    }

    .photos-controls {
        grid-template-columns: 1fr;
    }

    .expanded-photos-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 10px;
    }

    .categories-tabs {
        flex-direction: column;
    }

    .category-actions {
        flex-direction: column;
    }
}

/* Advanced Auto Post Modal Styles */
.automatic-posts-expanded-modal {
    width: 95%;
    max-width: 1400px;
    max-height: 95vh;
    overflow-y: auto;
}

.auto-config-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #CE1126;
}

.auto-config-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.auto-post-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.option-group label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.option-group input,
.option-group select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
}

.option-group small {
    color: #6c757d;
    font-size: 12px;
}

.load-photos-section {
    text-align: center;
    margin-top: 20px;
}

.photos-stats {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 20px;
    font-weight: 600;
    color: #495057;
}

.photos-preview-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.photos-preview-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.photos-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.expanded-photos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.expanded-photo-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    background: #f8f9fa;
}

.expanded-photo-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.expanded-photo-item.selected {
    border-color: #28a745;
    box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
}

.expanded-photo-item.private {
    border-color: #6f42c1;
    box-shadow: 0 8px 20px rgba(111, 66, 193, 0.3);
}

.expanded-photo-item.unwanted {
    border-color: #fd7e14;
    box-shadow: 0 8px 20px rgba(253, 126, 20, 0.3);
}

.expanded-photo-item.deleted {
    border-color: #dc3545;
    box-shadow: 0 8px 20px rgba(220, 53, 69, 0.3);
}

.expanded-photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
}

.expanded-photo-item:hover .photo-overlay {
    opacity: 1;
}

.photo-info {
    color: white;
    font-size: 12px;
}

.photo-title {
    display: block;
    font-weight: 600;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.photo-size {
    display: block;
    opacity: 0.8;
}

.photo-actions {
    display: flex;
    justify-content: space-between;
    gap: 5px;
}

.action-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: white;
    transform: scale(1.1);
}

.select-btn:hover {
    background: #28a745;
    color: white;
}

.private-btn:hover {
    background: #6f42c1;
    color: white;
}

.unwanted-btn:hover {
    background: #fd7e14;
    color: white;
}

.delete-btn:hover {
    background: #dc3545;
    color: white;
}

.categories-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #ffc107;
}

.categories-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.categories-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.category-tab {
    background: #e9ecef;
    border: none;
    padding: 10px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    color: #495057;
}

.category-tab:hover {
    background: #dee2e6;
}

.category-tab.active {
    background: #CE1126;
    color: white;
}

.category-content {
    min-height: 200px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.category-grid {
    display: none;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
}

.category-grid.active {
    display: grid;
}

.category-photo-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;
}

.category-photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-from-category {
    position: absolute;
    top: 2px;
    right: 2px;
    background: #dc3545;
    color: white;
    border: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}
