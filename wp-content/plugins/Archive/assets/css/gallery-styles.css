/**
 * SoloYLibre Gallery Styles
 * Professional photo gallery plugin for Jose L Encarnacion
 */

/* ==========================================================================
   Base Styles
   ========================================================================== */

.soloylibre-gallery {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.soloylibre-gallery *,
.soloylibre-gallery *::before,
.soloylibre-gallery *::after {
    box-sizing: border-box;
}

.soloylibre-no-photos {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-size: 16px;
}

/* ==========================================================================
   TikTok Style Gallery
   ========================================================================== */

.soloylibre-tiktok-style {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
    height: 100vh;
    position: relative;
}

.soloylibre-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.soloylibre-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: bold;
    color: #ff6b6b;
}

.brand-icon {
    font-size: 28px;
}

.soloylibre-membership-badge .membership-badge {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
}

.soloylibre-tiktok-container {
    margin-top: 80px;
    height: calc(100vh - 80px);
    overflow-y: auto;
    scroll-snap-type: y mandatory;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.soloylibre-tiktok-container::-webkit-scrollbar {
    display: none;
}

.soloylibre-photo-item {
    height: 100vh;
    width: 100%;
    position: relative;
    scroll-snap-align: start;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
}

.soloylibre-photo-item .photo-content {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.soloylibre-photo-item .photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 40px 20px 20px;
    color: white;
}

.photo-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 8px;
}

.photo-description {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 15px;
}

.photo-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.side-actions {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.side-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.side-btn:hover {
    background: rgba(255, 107, 107, 0.8);
    transform: scale(1.1);
}

.membership-lock {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.lock-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #ff6b6b;
}

.upgrade-btn {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upgrade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
}

.photographer-credit {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 12px;
    backdrop-filter: blur(10px);
}

/* ==========================================================================
   Grid Style Gallery
   ========================================================================== */

.soloylibre-grid-style {
    background: #0a0a0a;
    color: white;
    min-height: 100vh;
}

.soloylibre-grid-header {
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    padding: 20px 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.5);
}

.soloylibre-grid-header .nav-content,
.soloylibre-grid-header > div {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.soloylibre-brand-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.brand-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    color: white;
}

.brand-info h2 {
    font-size: 28px;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.brand-info p {
    font-size: 14px;
    color: #888;
    margin: 0;
}

.soloylibre-filters {
    background: #1a1a1a;
    padding: 20px 0;
    border-bottom: 1px solid #333;
}

.soloylibre-filters > div {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-tabs {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-tab {
    background: transparent;
    border: 2px solid #333;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-tab.active,
.filter-tab:hover {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

.album-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.album-dropdown {
    background: #2d2d2d;
    border: 1px solid #444;
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
}

.soloylibre-grid-container {
    max-width: 1400px;
    margin: 40px auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.soloylibre-photo-card {
    background: #1a1a1a;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.soloylibre-photo-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.2);
}

.photo-wrapper {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
}

.photo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.soloylibre-photo-card:hover .photo-image {
    transform: scale(1.05);
}

.soloylibre-photo-card .photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 20px;
}

.soloylibre-photo-card:hover .photo-overlay {
    opacity: 1;
}

.photo-info {
    padding: 20px;
}

.photo-info .photo-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #ff6b6b;
}

.photo-info .photo-description {
    font-size: 14px;
    color: #ccc;
    margin-bottom: 15px;
}

.photo-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #888;
}

.premium-lock {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.photographer-tag {
    position: absolute;
    top: 15px;
    left: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 11px;
    backdrop-filter: blur(10px);
}

.soloylibre-load-more {
    text-align: center;
    margin: 40px 0;
}

.soloylibre-load-more-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 30px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.soloylibre-load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

/* ==========================================================================
   Masonry Style Gallery
   ========================================================================== */

.soloylibre-masonry-style {
    background: #f8f9fa;
    color: #333;
    min-height: 100vh;
}

.soloylibre-navbar {
    background: white;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-content {
    max-width: 1600px;
    margin: 0 auto;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.brand-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.brand-section .brand-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: bold;
}

.brand-section .brand-info h1 {
    font-size: 24px;
    color: #2d3748;
    margin: 0 0 2px 0;
}

.brand-section .brand-info p {
    font-size: 14px;
    color: #718096;
    margin: 0;
}

.nav-actions .membership-badge {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.soloylibre-controls-bar {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 20px 0;
}

.controls-content {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.category-filters {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-chip {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    color: #4a5568;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
}

.filter-chip.active,
.filter-chip:hover {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.view-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.album-select {
    background: white;
    border: 2px solid #e2e8f0;
    padding: 8px 12px;
    border-radius: 8px;
    color: #4a5568;
    cursor: pointer;
    font-size: 14px;
}

.soloylibre-masonry-container {
    max-width: 1600px;
    margin: 40px auto;
    padding: 0 30px;
    columns: 4;
    column-gap: 20px;
}

.soloylibre-masonry-container .soloylibre-photo-item {
    break-inside: avoid;
    margin-bottom: 20px;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    height: auto;
}

.soloylibre-masonry-container .soloylibre-photo-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.soloylibre-masonry-container .photo-wrapper {
    position: relative;
    overflow: hidden;
}

.soloylibre-masonry-container .photo-image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.soloylibre-masonry-container .soloylibre-photo-item:hover .photo-image {
    transform: scale(1.02);
}

.soloylibre-masonry-container .photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent 50%, rgba(0, 0, 0, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 20px;
}

.soloylibre-masonry-container .soloylibre-photo-item:hover .photo-overlay {
    opacity: 1;
}

.overlay-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.overlay-btn {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    color: #333;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.overlay-btn:hover {
    background: white;
    transform: scale(1.05);
}

.photo-content {
    padding: 20px;
}

.photo-content .photo-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.photo-content .photo-description {
    font-size: 14px;
    color: #718096;
    margin-bottom: 15px;
    line-height: 1.5;
}

.photo-content .photo-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #a0aec0;
}

.photo-stats {
    display: flex;
    gap: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.location-tag {
    position: absolute;
    top: 12px;
    left: 12px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    backdrop-filter: blur(10px);
}

.premium-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    backdrop-filter: blur(5px);
}

.premium-icon {
    font-size: 32px;
    margin-bottom: 12px;
    color: #ff6b6b;
}

.premium-text {
    text-align: center;
    margin-bottom: 15px;
    color: #4a5568;
}

.premium-text h4 {
    font-size: 16px;
    margin-bottom: 4px;
}

.premium-text p {
    font-size: 12px;
    color: #718096;
}

.premium-upgrade {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.premium-upgrade:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
}

.soloylibre-load-more-section {
    text-align: center;
    margin: 60px 0;
}

.soloylibre-load-more-section .soloylibre-load-more-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.soloylibre-load-more-section .soloylibre-load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.soloylibre-loading-indicator {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.2);
    padding: 10px 20px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    display: none;
    z-index: 1000;
}

.soloylibre-loading-indicator.active {
    display: block;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 1200px) {
    .soloylibre-masonry-container {
        columns: 3;
    }
}

@media (max-width: 768px) {
    .soloylibre-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        margin: 20px auto;
    }
    
    .soloylibre-masonry-container {
        columns: 2;
        padding: 0 20px;
    }
    
    .controls-content,
    .soloylibre-filters > div {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-tabs,
    .category-filters {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .side-actions {
        right: 10px;
        gap: 15px;
    }
    
    .side-btn {
        width: 40px;
        height: 40px;
    }
    
    .photo-overlay {
        padding: 20px 15px 15px;
    }
}

@media (max-width: 480px) {
    .soloylibre-masonry-container {
        columns: 1;
    }
    
    .soloylibre-grid-container {
        grid-template-columns: 1fr;
    }
}
