/**
 * SoloYLibre Fullscreen Wizard JavaScript
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

(function($) {
    'use strict';
    
    // Wizard state
    let wizardState = {
        currentStep: 0,
        steps: ['welcome', 'select_photos', 'organize', 'settings', 'create_post', 'complete'],
        selectedPhotos: [],
        formData: {}
    };
    
    // Initialize wizard when document is ready
    $(document).ready(function() {
        initializeWizard();
    });
    
    /**
     * Initialize wizard functionality
     */
    function initializeWizard() {
        bindEvents();
        updateProgressBar();
        updateNavigationButtons();
        loadPhotosForSelection();
    }
    
    /**
     * Bind all event handlers
     */
    function bindEvents() {
        // Navigation buttons
        $('#next-step').on('click', nextStep);
        $('#prev-step').on('click', prevStep);
        
        // <PERSON> controls
        $('#minimize-wizard').on('click', minimizeWizard);
        $('#close-wizard').on('click', closeWizard);
        $('#restore-wizard').on('click', restoreWizard);
        
        // Photo selection
        $(document).on('click', '.photo-item', togglePhotoSelection);
        $('#photo-search').on('input', debounce(searchPhotos, 300));
        $('#photo-category-filter, #photo-date-filter').on('change', filterPhotos);
        
        // Form inputs
        $('#post-title, #post-content, #post-tags').on('input', updatePostPreview);
        $('#gallery-style, #gallery-columns').on('change', updateGalleryPreview);
        
        // Action buttons
        $('#create-another').on('click', resetWizard);
        $('#go-to-dashboard').on('click', goToDashboard);
        
        // Auto-save form data
        $('input, textarea, select').on('change', saveCurrentStepData);
    }
    
    /**
     * Move to next step
     */
    function nextStep() {
        if (wizardState.currentStep < wizardState.steps.length - 1) {
            // Validate current step
            if (validateCurrentStep()) {
                wizardState.currentStep++;
                showStep(wizardState.currentStep);
                updateProgressBar();
                updateNavigationButtons();
                
                // Special actions for specific steps
                if (wizardState.steps[wizardState.currentStep] === 'organize') {
                    displaySelectedPhotos();
                } else if (wizardState.steps[wizardState.currentStep] === 'create_post') {
                    updatePostPreview();
                } else if (wizardState.steps[wizardState.currentStep] === 'complete') {
                    createPost();
                }
            }
        }
    }
    
    /**
     * Move to previous step
     */
    function prevStep() {
        if (wizardState.currentStep > 0) {
            wizardState.currentStep--;
            showStep(wizardState.currentStep);
            updateProgressBar();
            updateNavigationButtons();
        }
    }
    
    /**
     * Show specific step
     */
    function showStep(stepIndex) {
        $('.wizard-step').removeClass('active');
        $('.wizard-step[data-step="' + wizardState.steps[stepIndex] + '"]').addClass('active');
        
        // Update progress steps
        $('.progress-step').removeClass('active completed');
        $('.progress-step').each(function(index) {
            if (index < stepIndex) {
                $(this).addClass('completed');
            } else if (index === stepIndex) {
                $(this).addClass('active');
            }
        });
        
        // Update step info
        $('#current-step-info').text(`Paso ${stepIndex + 1} de ${wizardState.steps.length}`);
    }
    
    /**
     * Update progress bar
     */
    function updateProgressBar() {
        const progress = ((wizardState.currentStep + 1) / wizardState.steps.length) * 100;
        $('#wizard-progress-fill').css('width', progress + '%');
    }
    
    /**
     * Update navigation buttons
     */
    function updateNavigationButtons() {
        $('#prev-step').prop('disabled', wizardState.currentStep === 0);
        
        if (wizardState.currentStep === wizardState.steps.length - 1) {
            $('#next-step').hide();
        } else {
            $('#next-step').show();
            
            // Change button text based on step
            if (wizardState.currentStep === wizardState.steps.length - 2) {
                $('#next-step').html('🚀 Crear Galería');
            } else {
                $('#next-step').html('Siguiente →');
            }
        }
    }
    
    /**
     * Validate current step
     */
    function validateCurrentStep() {
        const currentStepName = wizardState.steps[wizardState.currentStep];
        
        switch (currentStepName) {
            case 'select_photos':
                if (wizardState.selectedPhotos.length === 0) {
                    showNotification('Por favor selecciona al menos una foto', 'warning');
                    return false;
                }
                break;
                
            case 'settings':
                const title = $('#post-title').val().trim();
                if (!title) {
                    showNotification('Por favor ingresa un título para el post', 'warning');
                    $('#post-title').focus();
                    return false;
                }
                break;
        }
        
        return true;
    }
    
    /**
     * Load photos for selection
     */
    function loadPhotosForSelection() {
        $('#photos-loading').show();
        $('#photo-grid').empty();
        
        $.ajax({
            url: soloylibre_wizard.ajax_url,
            type: 'POST',
            data: {
                action: 'wizard_get_photos',
                nonce: soloylibre_wizard.nonce,
                search: $('#photo-search').val(),
                category: $('#photo-category-filter').val(),
                date_filter: $('#photo-date-filter').val()
            },
            success: function(response) {
                $('#photos-loading').hide();
                
                if (response.success) {
                    displayPhotos(response.data);
                } else {
                    showNotification('Error al cargar las fotos', 'error');
                }
            },
            error: function() {
                $('#photos-loading').hide();
                showNotification('Error de conexión', 'error');
            }
        });
    }
    
    /**
     * Display photos in grid
     */
    function displayPhotos(photos) {
        const $grid = $('#photo-grid');
        $grid.empty();
        
        if (photos.length === 0) {
            $grid.html('<div class="no-photos"><p>No se encontraron fotos</p></div>');
            return;
        }
        
        photos.forEach(function(photo) {
            const isSelected = wizardState.selectedPhotos.includes(photo.id);
            const $photoItem = $(`
                <div class="photo-item ${isSelected ? 'selected' : ''}" data-photo-id="${photo.id}">
                    <img src="${photo.thumbnail}" alt="${photo.alt || photo.title}" loading="lazy">
                    <div class="photo-overlay">
                        <div class="photo-info">
                            <h4>${photo.title}</h4>
                        </div>
                    </div>
                    <div class="photo-checkbox"></div>
                </div>
            `);
            
            $grid.append($photoItem);
        });
        
        updateSelectedCounter();
    }
    
    /**
     * Toggle photo selection
     */
    function togglePhotoSelection() {
        const $item = $(this);
        const photoId = parseInt($item.data('photo-id'));
        
        if ($item.hasClass('selected')) {
            // Deselect
            $item.removeClass('selected');
            wizardState.selectedPhotos = wizardState.selectedPhotos.filter(id => id !== photoId);
        } else {
            // Select
            $item.addClass('selected');
            wizardState.selectedPhotos.push(photoId);
        }
        
        updateSelectedCounter();
        saveCurrentStepData();
    }
    
    /**
     * Update selected photos counter
     */
    function updateSelectedCounter() {
        $('#selected-count').text(wizardState.selectedPhotos.length);
    }
    
    /**
     * Search photos
     */
    function searchPhotos() {
        loadPhotosForSelection();
    }
    
    /**
     * Filter photos
     */
    function filterPhotos() {
        loadPhotosForSelection();
    }
    
    /**
     * Display selected photos in organize step
     */
    function displaySelectedPhotos() {
        const $container = $('#selected-photos-preview');
        $container.empty();
        
        if (wizardState.selectedPhotos.length === 0) {
            $container.html('<p>No hay fotos seleccionadas</p>');
            return;
        }
        
        // Get photo data for selected photos
        const selectedPhotoData = [];
        $('.photo-item.selected').each(function() {
            const $item = $(this);
            const photoId = $item.data('photo-id');
            const imgSrc = $item.find('img').attr('src');
            const title = $item.find('h4').text();
            
            selectedPhotoData.push({
                id: photoId,
                src: imgSrc,
                title: title
            });
        });
        
        selectedPhotoData.forEach(function(photo) {
            const $photoItem = $(`
                <div class="selected-photo-item" data-photo-id="${photo.id}">
                    <img src="${photo.src}" alt="${photo.title}">
                    <button class="remove-photo" data-photo-id="${photo.id}">×</button>
                </div>
            `);
            
            $container.append($photoItem);
        });
        
        // Bind remove photo events
        $('.remove-photo').on('click', function(e) {
            e.stopPropagation();
            const photoId = parseInt($(this).data('photo-id'));
            removeSelectedPhoto(photoId);
        });
    }
    
    /**
     * Remove photo from selection
     */
    function removeSelectedPhoto(photoId) {
        wizardState.selectedPhotos = wizardState.selectedPhotos.filter(id => id !== photoId);
        $(`.selected-photo-item[data-photo-id="${photoId}"]`).remove();
        $(`.photo-item[data-photo-id="${photoId}"]`).removeClass('selected');
        updateSelectedCounter();
        saveCurrentStepData();
    }
    
    /**
     * Update post preview
     */
    function updatePostPreview() {
        const title = $('#post-title').val() || 'Título del post';
        const content = $('#post-content').val() || 'Descripción de la galería...';
        const galleryStyle = $('#gallery-style').val() || 'grid';
        const includeSignature = $('#include-signature').is(':checked');
        
        let preview = `
            <h2>${title}</h2>
            <p>${content}</p>
            <div class="gallery-preview">
                <p><strong>Galería (${wizardState.selectedPhotos.length} fotos)</strong></p>
                <p>Estilo: ${galleryStyle}</p>
                <div class="preview-photos">
        `;
        
        // Show first few selected photos as preview
        const previewPhotos = wizardState.selectedPhotos.slice(0, 6);
        previewPhotos.forEach(function(photoId) {
            const $photoItem = $(`.photo-item[data-photo-id="${photoId}"]`);
            if ($photoItem.length) {
                const imgSrc = $photoItem.find('img').attr('src');
                preview += `<img src="${imgSrc}" style="width: 80px; height: 80px; object-fit: cover; margin: 5px; border-radius: 5px;">`;
            }
        });
        
        preview += '</div></div>';
        
        if (includeSignature) {
            preview += `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #CE1126; margin-top: 20px;">
                    <h4 style="color: #CE1126;">📸 Fotografía por Jose L Encarnacion (JoseTusabe)</h4>
                    <p><strong>SoloYLibre Photography</strong></p>
                    <p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
                    <p>📞 718-713-5500 | 📧 <EMAIL></p>
                </div>
            `;
        }
        
        $('#post-preview').html(preview);
    }
    
    /**
     * Create post with gallery
     */
    function createPost() {
        $('#creation-progress').removeClass('hidden');
        $('#creation-success').addClass('hidden');
        
        const postData = {
            action: 'wizard_create_post',
            nonce: soloylibre_wizard.nonce,
            photo_ids: wizardState.selectedPhotos,
            post_title: $('#post-title').val(),
            post_content: $('#post-content').val(),
            post_category: $('#post-category').val(),
            post_tags: $('#post-tags').val(),
            post_status: $('#post-status').val(),
            gallery_style: $('#gallery-style').val(),
            include_signature: $('#include-signature').is(':checked') ? 1 : 0
        };
        
        $.ajax({
            url: soloylibre_wizard.ajax_url,
            type: 'POST',
            data: postData,
            success: function(response) {
                $('#creation-progress').addClass('hidden');
                
                if (response.success) {
                    $('#creation-success').removeClass('hidden');
                    $('#view-post-link').attr('href', response.data.post_url);
                    $('#edit-post-link').attr('href', response.data.edit_url);
                    $('#final-photo-count').text(response.data.photo_count);
                    
                    showNotification('¡Post creado exitosamente!', 'success');
                } else {
                    showNotification('Error al crear el post: ' + (response.data || 'Error desconocido'), 'error');
                }
            },
            error: function() {
                $('#creation-progress').addClass('hidden');
                showNotification('Error de conexión al crear el post', 'error');
            }
        });
    }
    
    /**
     * Save current step data
     */
    function saveCurrentStepData() {
        const currentStepName = wizardState.steps[wizardState.currentStep];
        const stepData = {
            selectedPhotos: wizardState.selectedPhotos
        };
        
        // Collect form data based on current step
        if (currentStepName === 'settings') {
            stepData.postTitle = $('#post-title').val();
            stepData.postContent = $('#post-content').val();
            stepData.postCategory = $('#post-category').val();
            stepData.postTags = $('#post-tags').val();
            stepData.postStatus = $('#post-status').val();
            stepData.includeSignature = $('#include-signature').is(':checked');
        }
        
        if (currentStepName === 'organize') {
            stepData.galleryStyle = $('#gallery-style').val();
            stepData.galleryColumns = $('#gallery-columns').val();
            stepData.showCaptions = $('#show-captions').is(':checked');
            stepData.enableLightbox = $('#enable-lightbox').is(':checked');
        }
        
        // Save to server
        $.ajax({
            url: soloylibre_wizard.ajax_url,
            type: 'POST',
            data: {
                action: 'wizard_save_step',
                nonce: soloylibre_wizard.nonce,
                step: currentStepName,
                data: stepData
            }
        });
    }
    
    /**
     * Minimize wizard
     */
    function minimizeWizard() {
        $('#soloylibre-fullscreen-wizard').hide();
        $('#wizard-minimized').removeClass('hidden');
    }
    
    /**
     * Restore wizard
     */
    function restoreWizard() {
        $('#wizard-minimized').addClass('hidden');
        $('#soloylibre-fullscreen-wizard').show();
    }
    
    /**
     * Close wizard
     */
    function closeWizard() {
        if (confirm('¿Estás seguro de que quieres cerrar el wizard? Se perderá el progreso actual.')) {
            window.location.href = soloylibre_wizard.ajax_url.replace('admin-ajax.php', 'admin.php');
        }
    }
    
    /**
     * Reset wizard for new gallery
     */
    function resetWizard() {
        wizardState.currentStep = 0;
        wizardState.selectedPhotos = [];
        wizardState.formData = {};
        
        // Reset form fields
        $('#post-title, #post-content, #post-tags').val('');
        $('#post-category, #post-status, #gallery-style').prop('selectedIndex', 0);
        $('#include-signature, #show-captions, #enable-lightbox').prop('checked', true);
        
        // Reset UI
        $('.photo-item').removeClass('selected');
        $('#selected-photos-preview').empty();
        $('#post-preview').empty();
        
        showStep(0);
        updateProgressBar();
        updateNavigationButtons();
        updateSelectedCounter();
        
        showNotification('Wizard reiniciado. ¡Crea una nueva galería!', 'success');
    }
    
    /**
     * Go to dashboard
     */
    function goToDashboard() {
        window.location.href = soloylibre_wizard.ajax_url.replace('admin-ajax.php', 'admin.php');
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="wizard-notification wizard-notification-${type}">
                <span class="notification-icon">
                    ${type === 'success' ? '✅' : type === 'warning' ? '⚠️' : type === 'error' ? '❌' : 'ℹ️'}
                </span>
                <span class="notification-message">${message}</span>
                <button class="notification-close">×</button>
            </div>
        `);
        
        $('body').append($notification);
        
        // Auto-remove after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // Manual close
        $notification.find('.notification-close').on('click', function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        });
    }
    
    /**
     * Debounce function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = function() {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
})(jQuery);
