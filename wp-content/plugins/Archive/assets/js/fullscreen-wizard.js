/**
 * SoloYLibre Fullscreen Wizard JavaScript
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

(function($) {
    'use strict';
    
    // Wizard state
    let wizardState = {
        currentStep: 0,
        steps: ['welcome', 'select_photos', 'organize', 'settings', 'create_post', 'complete'],
        selectedPhotos: [],
        formData: {}
    };
    
    // Initialize wizard when document is ready
    $(document).ready(function() {
        initializeWizard();
    });
    
    /**
     * Initialize wizard functionality
     */
    function initializeWizard() {
        bindEvents();
        updateProgressBar();
        updateNavigationButtons();
        loadPhotosForSelection();
    }
    
    /**
     * Bind all event handlers
     */
    function bindEvents() {
        // Navigation buttons
        $('#next-step').on('click', nextStep);
        $('#prev-step').on('click', prevStep);
        
        // <PERSON> controls
        $('#minimize-wizard').on('click', minimizeWizard);
        $('#close-wizard').on('click', closeWizard);
        $('#restore-wizard').on('click', restoreWizard);

        // Modal close buttons
        $(document).on('click', '.close-modal', function() {
            $(this).closest('.soloylibre-modal-overlay').remove();
        });

        $(document).on('click', '.soloylibre-modal-overlay', function(e) {
            if (e.target === this) {
                $(this).remove();
            }
        });
        
        // Photo selection
        $(document).on('click', '.photo-item', togglePhotoSelection);
        $('#photo-search').on('input', debounce(searchPhotos, 300));
        $('#photo-category-filter, #photo-date-filter').on('change', filterPhotos);
        
        // Form inputs
        $('#post-title, #post-content, #post-tags').on('input', updatePostPreview);
        $('#gallery-style, #gallery-columns').on('change', updateGalleryPreview);
        
        // Action buttons
        $('#create-another').on('click', resetWizard);
        $('#go-to-dashboard').on('click', goToDashboard);
        
        // Auto-save form data
        $('input, textarea, select').on('change', saveCurrentStepData);
    }
    
    /**
     * Move to next step
     */
    function nextStep() {
        if (wizardState.currentStep < wizardState.steps.length - 1) {
            // Validate current step
            if (validateCurrentStep()) {
                wizardState.currentStep++;
                showStep(wizardState.currentStep);
                updateProgressBar();
                updateNavigationButtons();
                
                // Special actions for specific steps
                if (wizardState.steps[wizardState.currentStep] === 'organize') {
                    displaySelectedPhotos();
                } else if (wizardState.steps[wizardState.currentStep] === 'create_post') {
                    updatePostPreview();
                } else if (wizardState.steps[wizardState.currentStep] === 'complete') {
                    createPost();
                }
            }
        }
    }
    
    /**
     * Move to previous step
     */
    function prevStep() {
        if (wizardState.currentStep > 0) {
            wizardState.currentStep--;
            showStep(wizardState.currentStep);
            updateProgressBar();
            updateNavigationButtons();
        }
    }
    
    /**
     * Show specific step
     */
    function showStep(stepIndex) {
        $('.wizard-step').removeClass('active');
        $('.wizard-step[data-step="' + wizardState.steps[stepIndex] + '"]').addClass('active');
        
        // Update progress steps
        $('.progress-step').removeClass('active completed');
        $('.progress-step').each(function(index) {
            if (index < stepIndex) {
                $(this).addClass('completed');
            } else if (index === stepIndex) {
                $(this).addClass('active');
            }
        });
        
        // Update step info
        $('#current-step-info').text(`Paso ${stepIndex + 1} de ${wizardState.steps.length}`);
    }
    
    /**
     * Update progress bar
     */
    function updateProgressBar() {
        const progress = ((wizardState.currentStep + 1) / wizardState.steps.length) * 100;
        $('#wizard-progress-fill').css('width', progress + '%');
    }
    
    /**
     * Update navigation buttons
     */
    function updateNavigationButtons() {
        $('#prev-step').prop('disabled', wizardState.currentStep === 0);
        
        if (wizardState.currentStep === wizardState.steps.length - 1) {
            $('#next-step').hide();
        } else {
            $('#next-step').show();
            
            // Change button text based on step
            if (wizardState.currentStep === wizardState.steps.length - 2) {
                $('#next-step').html('🚀 Crear Galería');
            } else {
                $('#next-step').html('Siguiente →');
            }
        }
    }
    
    /**
     * Validate current step
     */
    function validateCurrentStep() {
        const currentStepName = wizardState.steps[wizardState.currentStep];
        
        switch (currentStepName) {
            case 'select_photos':
                if (wizardState.selectedPhotos.length === 0) {
                    showNotification('Por favor selecciona al menos una foto', 'warning');
                    return false;
                }
                break;
                
            case 'settings':
                const title = $('#post-title').val().trim();
                if (!title) {
                    showNotification('Por favor ingresa un título para el post', 'warning');
                    $('#post-title').focus();
                    return false;
                }
                break;
        }
        
        return true;
    }
    
    /**
     * Load photos for selection
     */
    function loadPhotosForSelection() {
        $('#photos-loading').show();
        $('#photo-grid').empty();
        
        $.ajax({
            url: soloylibre_wizard.ajax_url,
            type: 'POST',
            data: {
                action: 'wizard_get_photos',
                nonce: soloylibre_wizard.nonce,
                search: $('#photo-search').val(),
                category: $('#photo-category-filter').val(),
                date_filter: $('#photo-date-filter').val()
            },
            success: function(response) {
                $('#photos-loading').hide();
                
                if (response.success) {
                    displayPhotos(response.data);
                } else {
                    showNotification('Error al cargar las fotos', 'error');
                }
            },
            error: function() {
                $('#photos-loading').hide();
                showNotification('Error de conexión', 'error');
            }
        });
    }
    
    /**
     * Display photos in grid
     */
    function displayPhotos(photos) {
        const $grid = $('#photo-grid');
        $grid.empty();
        
        if (photos.length === 0) {
            $grid.html('<div class="no-photos"><p>No se encontraron fotos</p></div>');
            return;
        }
        
        photos.forEach(function(photo) {
            const isSelected = wizardState.selectedPhotos.includes(photo.id);
            const $photoItem = $(`
                <div class="photo-item ${isSelected ? 'selected' : ''}" data-photo-id="${photo.id}">
                    <img src="${photo.thumbnail}" alt="${photo.alt || photo.title}" loading="lazy">
                    <div class="photo-overlay">
                        <div class="photo-info">
                            <h4>${photo.title}</h4>
                        </div>
                    </div>
                    <div class="photo-checkbox"></div>
                </div>
            `);
            
            $grid.append($photoItem);
        });
        
        updateSelectedCounter();
    }
    
    /**
     * Toggle photo selection
     */
    function togglePhotoSelection() {
        const $item = $(this);
        const photoId = parseInt($item.data('photo-id'));
        
        if ($item.hasClass('selected')) {
            // Deselect
            $item.removeClass('selected');
            wizardState.selectedPhotos = wizardState.selectedPhotos.filter(id => id !== photoId);
        } else {
            // Select
            $item.addClass('selected');
            wizardState.selectedPhotos.push(photoId);
        }
        
        updateSelectedCounter();
        saveCurrentStepData();
    }
    
    /**
     * Update selected photos counter
     */
    function updateSelectedCounter() {
        $('#selected-count').text(wizardState.selectedPhotos.length);
    }
    
    /**
     * Search photos
     */
    function searchPhotos() {
        loadPhotosForSelection();
    }
    
    /**
     * Filter photos
     */
    function filterPhotos() {
        loadPhotosForSelection();
    }
    
    /**
     * Display selected photos in organize step
     */
    function displaySelectedPhotos() {
        const $container = $('#selected-photos-preview');
        $container.empty();
        
        if (wizardState.selectedPhotos.length === 0) {
            $container.html('<p>No hay fotos seleccionadas</p>');
            return;
        }
        
        // Get photo data for selected photos
        const selectedPhotoData = [];
        $('.photo-item.selected').each(function() {
            const $item = $(this);
            const photoId = $item.data('photo-id');
            const imgSrc = $item.find('img').attr('src');
            const title = $item.find('h4').text();
            
            selectedPhotoData.push({
                id: photoId,
                src: imgSrc,
                title: title
            });
        });
        
        selectedPhotoData.forEach(function(photo) {
            const $photoItem = $(`
                <div class="selected-photo-item" data-photo-id="${photo.id}">
                    <img src="${photo.src}" alt="${photo.title}">
                    <button class="remove-photo" data-photo-id="${photo.id}">×</button>
                </div>
            `);
            
            $container.append($photoItem);
        });
        
        // Bind remove photo events
        $('.remove-photo').on('click', function(e) {
            e.stopPropagation();
            const photoId = parseInt($(this).data('photo-id'));
            removeSelectedPhoto(photoId);
        });
    }
    
    /**
     * Remove photo from selection
     */
    function removeSelectedPhoto(photoId) {
        wizardState.selectedPhotos = wizardState.selectedPhotos.filter(id => id !== photoId);
        $(`.selected-photo-item[data-photo-id="${photoId}"]`).remove();
        $(`.photo-item[data-photo-id="${photoId}"]`).removeClass('selected');
        updateSelectedCounter();
        saveCurrentStepData();
    }
    
    /**
     * Update post preview
     */
    function updatePostPreview() {
        const title = $('#post-title').val() || 'Título del post';
        const content = $('#post-content').val() || 'Descripción de la galería...';
        const galleryStyle = $('#gallery-style').val() || 'grid';
        const includeSignature = $('#include-signature').is(':checked');
        
        let preview = `
            <h2>${title}</h2>
            <p>${content}</p>
            <div class="gallery-preview">
                <p><strong>Galería (${wizardState.selectedPhotos.length} fotos)</strong></p>
                <p>Estilo: ${galleryStyle}</p>
                <div class="preview-photos">
        `;
        
        // Show first few selected photos as preview
        const previewPhotos = wizardState.selectedPhotos.slice(0, 6);
        previewPhotos.forEach(function(photoId) {
            const $photoItem = $(`.photo-item[data-photo-id="${photoId}"]`);
            if ($photoItem.length) {
                const imgSrc = $photoItem.find('img').attr('src');
                preview += `<img src="${imgSrc}" style="width: 80px; height: 80px; object-fit: cover; margin: 5px; border-radius: 5px;">`;
            }
        });
        
        preview += '</div></div>';
        
        if (includeSignature) {
            preview += `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #CE1126; margin-top: 20px;">
                    <h4 style="color: #CE1126;">📸 Fotografía por Jose L Encarnacion (JoseTusabe)</h4>
                    <p><strong>SoloYLibre Photography</strong></p>
                    <p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
                    <p>📞 718-713-5500 | 📧 <EMAIL></p>
                </div>
            `;
        }
        
        $('#post-preview').html(preview);
    }
    
    /**
     * Create post with gallery
     */
    function createPost() {
        $('#creation-progress').removeClass('hidden');
        $('#creation-success').addClass('hidden');
        
        const postData = {
            action: 'wizard_create_post',
            nonce: soloylibre_wizard.nonce,
            photo_ids: wizardState.selectedPhotos,
            post_title: $('#post-title').val(),
            post_content: $('#post-content').val(),
            post_category: $('#post-category').val(),
            post_tags: $('#post-tags').val(),
            post_status: $('#post-status').val(),
            gallery_style: $('#gallery-style').val(),
            include_signature: $('#include-signature').is(':checked') ? 1 : 0
        };
        
        $.ajax({
            url: soloylibre_wizard.ajax_url,
            type: 'POST',
            data: postData,
            success: function(response) {
                $('#creation-progress').addClass('hidden');
                
                if (response.success) {
                    $('#creation-success').removeClass('hidden');
                    $('#view-post-link').attr('href', response.data.post_url);
                    $('#edit-post-link').attr('href', response.data.edit_url);
                    $('#final-photo-count').text(response.data.photo_count);
                    
                    showNotification('¡Post creado exitosamente!', 'success');
                } else {
                    showNotification('Error al crear el post: ' + (response.data || 'Error desconocido'), 'error');
                }
            },
            error: function() {
                $('#creation-progress').addClass('hidden');
                showNotification('Error de conexión al crear el post', 'error');
            }
        });
    }
    
    /**
     * Save current step data
     */
    function saveCurrentStepData() {
        const currentStepName = wizardState.steps[wizardState.currentStep];
        const stepData = {
            selectedPhotos: wizardState.selectedPhotos
        };
        
        // Collect form data based on current step
        if (currentStepName === 'settings') {
            stepData.postTitle = $('#post-title').val();
            stepData.postContent = $('#post-content').val();
            stepData.postCategory = $('#post-category').val();
            stepData.postTags = $('#post-tags').val();
            stepData.postStatus = $('#post-status').val();
            stepData.includeSignature = $('#include-signature').is(':checked');
        }
        
        if (currentStepName === 'organize') {
            stepData.galleryStyle = $('#gallery-style').val();
            stepData.galleryColumns = $('#gallery-columns').val();
            stepData.showCaptions = $('#show-captions').is(':checked');
            stepData.enableLightbox = $('#enable-lightbox').is(':checked');
        }
        
        // Save to server
        $.ajax({
            url: soloylibre_wizard.ajax_url,
            type: 'POST',
            data: {
                action: 'wizard_save_step',
                nonce: soloylibre_wizard.nonce,
                step: currentStepName,
                data: stepData
            }
        });
    }
    
    /**
     * Minimize wizard
     */
    function minimizeWizard() {
        $('#soloylibre-fullscreen-wizard').hide();
        $('#wizard-minimized').removeClass('hidden');
    }
    
    /**
     * Restore wizard
     */
    function restoreWizard() {
        $('#wizard-minimized').addClass('hidden');
        $('#soloylibre-fullscreen-wizard').show();
    }
    
    /**
     * Close wizard
     */
    function closeWizard() {
        if (confirm('¿Estás seguro de que quieres cerrar el wizard? Se perderá el progreso actual.')) {
            window.location.href = soloylibre_wizard.ajax_url.replace('admin-ajax.php', 'admin.php');
        }
    }
    
    /**
     * Reset wizard for new gallery
     */
    function resetWizard() {
        wizardState.currentStep = 0;
        wizardState.selectedPhotos = [];
        wizardState.formData = {};
        
        // Reset form fields
        $('#post-title, #post-content, #post-tags').val('');
        $('#post-category, #post-status, #gallery-style').prop('selectedIndex', 0);
        $('#include-signature, #show-captions, #enable-lightbox').prop('checked', true);
        
        // Reset UI
        $('.photo-item').removeClass('selected');
        $('#selected-photos-preview').empty();
        $('#post-preview').empty();
        
        showStep(0);
        updateProgressBar();
        updateNavigationButtons();
        updateSelectedCounter();
        
        showNotification('Wizard reiniciado. ¡Crea una nueva galería!', 'success');
    }
    
    /**
     * Go to dashboard
     */
    function goToDashboard() {
        window.location.href = soloylibre_wizard.ajax_url.replace('admin-ajax.php', 'admin.php');
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="wizard-notification wizard-notification-${type}">
                <span class="notification-icon">
                    ${type === 'success' ? '✅' : type === 'warning' ? '⚠️' : type === 'error' ? '❌' : 'ℹ️'}
                </span>
                <span class="notification-message">${message}</span>
                <button class="notification-close">×</button>
            </div>
        `);
        
        $('body').append($notification);
        
        // Auto-remove after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // Manual close
        $notification.find('.notification-close').on('click', function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        });
    }
    
    /**
     * Debounce function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = function() {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Global functions for feature cards
     */

    // Función global para Selección Inteligente
    window.openSmartSelection = function() {
        showLoadingModal('🎯 Cargando Selección Inteligente...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wizard_smart_selection',
                nonce: soloylibre_wizard.nonce
            },
            success: function(response) {
                hideLoadingModal();
                if (response.success) {
                    showSmartSelectionModal(response.data);
                } else {
                    showErrorModal('Error al cargar selección inteligente: ' + response.data);
                }
            },
            error: function() {
                hideLoadingModal();
                showErrorModal('Error de conexión al cargar selección inteligente');
            }
        });
    };

    // Función global para Estilos Dominicanos
    window.openDominicanStyles = function() {
        showLoadingModal('🇩🇴 Cargando Estilos Dominicanos...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wizard_dominican_styles',
                nonce: soloylibre_wizard.nonce
            },
            success: function(response) {
                hideLoadingModal();
                if (response.success) {
                    showDominicanStylesModal(response.data);
                } else {
                    showErrorModal('Error al cargar estilos dominicanos: ' + response.data);
                }
            },
            error: function() {
                hideLoadingModal();
                showErrorModal('Error de conexión al cargar estilos dominicanos');
            }
        });
    };

    // Función global para Posts Automáticos
    window.openAutomaticPosts = function() {
        showAutomaticPostsModal();
    };

    /**
     * Modal functions
     */

    function showLoadingModal(message) {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal loading-modal">
                    <div class="loading-spinner"></div>
                    <p>${message}</p>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function hideLoadingModal() {
        $('.soloylibre-modal-overlay').remove();
    }

    function showErrorModal(message) {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal error-modal">
                    <h3>❌ Error</h3>
                    <p>${message}</p>
                    <button class="btn btn-primary close-modal">Cerrar</button>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function showSmartSelectionModal(data) {
        let photosHtml = '';
        data.photos.forEach(photo => {
            photosHtml += `
                <div class="smart-photo-item" data-photo-id="${photo.id}">
                    <img src="${photo.thumbnail}" alt="${photo.title}" title="${photo.title} (${photo.size})">
                    <div class="photo-overlay">
                        <div class="photo-info">
                            <span class="photo-title">${photo.title}</span>
                            <span class="photo-size">${photo.size}</span>
                        </div>
                        <button class="select-photo-btn" onclick="selectSmartPhoto(${photo.id})">✓ Seleccionar</button>
                    </div>
                </div>
            `;
        });

        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal smart-selection-modal">
                    <div class="modal-header">
                        <h3>🎯 Selección Inteligente</h3>
                        <button class="close-modal">×</button>
                    </div>
                    <div class="modal-content">
                        <div class="selection-stats">
                            <span>📊 ${data.selection_count} fotos seleccionadas de ${data.total_available} disponibles</span>
                        </div>
                        <div class="smart-photos-grid">
                            ${photosHtml}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary close-modal">Cancelar</button>
                        <button class="btn btn-primary" onclick="useSmartSelection()">🚀 Usar Selección</button>
                    </div>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function showDominicanStylesModal(data) {
        let stylesHtml = '';
        Object.keys(data.styles).forEach(key => {
            const style = data.styles[key];
            stylesHtml += `
                <div class="dominican-style-item" data-style="${key}">
                    <div class="style-preview" style="background: ${style.css.background}; border: ${style.css.border};">
                        <div class="style-overlay">
                            <h4>${style.name}</h4>
                            <p>${style.description}</p>
                            <button class="btn btn-primary" onclick="applyDominicanStyle('${key}')">🎨 Aplicar Estilo</button>
                        </div>
                    </div>
                </div>
            `;
        });

        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal dominican-styles-modal">
                    <div class="modal-header">
                        <h3>🇩🇴 Estilos Dominicanos</h3>
                        <button class="close-modal">×</button>
                    </div>
                    <div class="modal-content">
                        <p>Selecciona un estilo inspirado en la cultura dominicana para tu galería:</p>
                        <div class="dominican-styles-grid">
                            ${stylesHtml}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary close-modal">Cerrar</button>
                    </div>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function showAutomaticPostsModal() {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal automatic-posts-modal">
                    <div class="modal-header">
                        <h3>🤖 Posts Automáticos</h3>
                        <button class="close-modal">×</button>
                    </div>
                    <div class="modal-content">
                        <p>Crea posts de WordPress automáticamente con galerías integradas:</p>
                        <div class="auto-post-options">
                            <div class="option-group">
                                <label for="auto-photo-count">📸 Número de fotos:</label>
                                <select id="auto-photo-count">
                                    <option value="10">10 fotos</option>
                                    <option value="15" selected>15 fotos</option>
                                    <option value="20">20 fotos</option>
                                    <option value="25">25 fotos</option>
                                </select>
                            </div>
                            <div class="option-group">
                                <label for="auto-style">🎨 Estilo de galería:</label>
                                <select id="auto-style">
                                    <option value="masonry" selected>Masonry</option>
                                    <option value="grid">Grid</option>
                                    <option value="carousel">Carousel</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary close-modal">Cancelar</button>
                        <button class="btn btn-primary" onclick="createAutomaticPost()">🚀 Crear Post Automático</button>
                    </div>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    /**
     * Action functions
     */

    window.selectSmartPhoto = function(photoId) {
        const photoItem = $(`.smart-photo-item[data-photo-id="${photoId}"]`);
        photoItem.toggleClass('selected');

        const btn = photoItem.find('.select-photo-btn');
        if (photoItem.hasClass('selected')) {
            btn.text('✓ Seleccionado').addClass('selected');
        } else {
            btn.text('✓ Seleccionar').removeClass('selected');
        }
    };

    window.useSmartSelection = function() {
        const selectedPhotos = [];
        $('.smart-photo-item.selected').each(function() {
            selectedPhotos.push($(this).data('photo-id'));
        });

        if (selectedPhotos.length === 0) {
            alert('Por favor selecciona al menos una foto');
            return;
        }

        // Agregar fotos seleccionadas al wizard
        wizardState.selectedPhotos = selectedPhotos;
        updateSelectedPhotosDisplay();

        $('.soloylibre-modal-overlay').remove();
        showSuccessMessage(`✅ ${selectedPhotos.length} fotos agregadas a tu selección`);
    };

    window.applyDominicanStyle = function(styleKey) {
        // Aplicar estilo dominicano al wizard
        wizardState.formData.gallery_style = styleKey;

        $('.soloylibre-modal-overlay').remove();
        showSuccessMessage(`🇩🇴 Estilo dominicano "${styleKey}" aplicado exitosamente`);
    };

    window.createAutomaticPost = function() {
        const photoCount = $('#auto-photo-count').val();
        const style = $('#auto-style').val();

        showLoadingModal('🤖 Creando post automático...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wizard_automatic_posts',
                nonce: soloylibre_wizard.nonce,
                photo_count: photoCount,
                style: style,
                post_type: 'auto'
            },
            success: function(response) {
                hideLoadingModal();
                if (response.success) {
                    $('.soloylibre-modal-overlay').remove();
                    showAutomaticPostSuccess(response.data);
                } else {
                    showErrorModal('Error al crear post automático: ' + response.data);
                }
            },
            error: function() {
                hideLoadingModal();
                showErrorModal('Error de conexión al crear post automático');
            }
        });
    };

    function showAutomaticPostSuccess(data) {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal success-modal">
                    <div class="modal-header">
                        <h3>🎉 ¡Post Creado Exitosamente!</h3>
                    </div>
                    <div class="modal-content">
                        <div class="success-info">
                            <h4>${data.title}</h4>
                            <p>📸 ${data.photos_count} fotos publicadas</p>
                            <p>🆔 Post ID: #${data.post_id}</p>
                        </div>
                        <div class="success-actions">
                            <a href="${data.post_url}" target="_blank" class="btn btn-primary">👁️ Ver Post</a>
                            <a href="${data.edit_url}" target="_blank" class="btn btn-secondary">✏️ Editar Post</a>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-success" onclick="createAutomaticPost()">🤖 Crear Otro</button>
                        <button class="btn btn-primary close-modal">Cerrar</button>
                    </div>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function showSuccessMessage(message) {
        const notification = $(`
            <div class="success-notification">
                ${message}
            </div>
        `);

        $('body').append(notification);

        setTimeout(() => {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

})(jQuery);
