/**
 * SoloYLibre Fullscreen Wizard JavaScript
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

(function($) {
    'use strict';
    
    // Wizard state
    let wizardState = {
        currentStep: 0,
        steps: ['welcome', 'select_photos', 'organize', 'settings', 'create_post', 'complete'],
        selectedPhotos: [],
        formData: {}
    };
    
    // Initialize wizard when document is ready
    $(document).ready(function() {
        initializeWizard();
    });
    
    /**
     * Initialize wizard functionality
     */
    function initializeWizard() {
        bindEvents();
        updateProgressBar();
        updateNavigationButtons();
        loadPhotosForSelection();
        autoMinimizeOnModal();
    }
    
    /**
     * Bind all event handlers
     */
    function bindEvents() {
        // Navigation buttons
        $('#next-step').on('click', nextStep);
        $('#prev-step').on('click', prevStep);
        
        // Wizard controls
        $('#minimize-wizard').on('click', minimizeWizard);
        $('#close-wizard').on('click', closeWizard);
        $('#restore-wizard').on('click', restoreWizard);

        // Modal close buttons
        $(document).on('click', '.close-modal', function() {
            $(this).closest('.soloylibre-modal-overlay').remove();
        });

        $(document).on('click', '.soloylibre-modal-overlay', function(e) {
            if (e.target === this) {
                $(this).remove();
            }
        });
        
        // Photo selection
        $(document).on('click', '.photo-item', togglePhotoSelection);
        $('#photo-search').on('input', debounce(searchPhotos, 300));
        $('#photo-category-filter, #photo-date-filter').on('change', filterPhotos);
        
        // Form inputs
        $('#post-title, #post-content, #post-tags').on('input', updatePostPreview);
        $('#gallery-style, #gallery-columns').on('change', updateGalleryPreview);
        
        // Action buttons
        $('#create-another').on('click', resetWizard);
        $('#go-to-dashboard').on('click', goToDashboard);
        
        // Auto-save form data
        $('input, textarea, select').on('change', saveCurrentStepData);
    }
    
    /**
     * Move to next step
     */
    function nextStep() {
        if (wizardState.currentStep < wizardState.steps.length - 1) {
            // Validate current step
            if (validateCurrentStep()) {
                wizardState.currentStep++;
                showStep(wizardState.currentStep);
                updateProgressBar();
                updateNavigationButtons();
                
                // Special actions for specific steps
                if (wizardState.steps[wizardState.currentStep] === 'organize') {
                    displaySelectedPhotos();
                } else if (wizardState.steps[wizardState.currentStep] === 'create_post') {
                    updatePostPreview();
                } else if (wizardState.steps[wizardState.currentStep] === 'complete') {
                    createPost();
                }
            }
        }
    }
    
    /**
     * Move to previous step
     */
    function prevStep() {
        if (wizardState.currentStep > 0) {
            wizardState.currentStep--;
            showStep(wizardState.currentStep);
            updateProgressBar();
            updateNavigationButtons();
        }
    }
    
    /**
     * Show specific step
     */
    function showStep(stepIndex) {
        $('.wizard-step').removeClass('active');
        $('.wizard-step[data-step="' + wizardState.steps[stepIndex] + '"]').addClass('active');
        
        // Update progress steps
        $('.progress-step').removeClass('active completed');
        $('.progress-step').each(function(index) {
            if (index < stepIndex) {
                $(this).addClass('completed');
            } else if (index === stepIndex) {
                $(this).addClass('active');
            }
        });
        
        // Update step info
        $('#current-step-info').text(`Paso ${stepIndex + 1} de ${wizardState.steps.length}`);
    }
    
    /**
     * Update progress bar
     */
    function updateProgressBar() {
        const progress = ((wizardState.currentStep + 1) / wizardState.steps.length) * 100;
        $('#wizard-progress-fill').css('width', progress + '%');
    }
    
    /**
     * Update navigation buttons
     */
    function updateNavigationButtons() {
        $('#prev-step').prop('disabled', wizardState.currentStep === 0);
        
        if (wizardState.currentStep === wizardState.steps.length - 1) {
            $('#next-step').hide();
        } else {
            $('#next-step').show();
            
            // Change button text based on step
            if (wizardState.currentStep === wizardState.steps.length - 2) {
                $('#next-step').html('🚀 Crear Galería');
            } else {
                $('#next-step').html('Siguiente →');
            }
        }
    }
    
    /**
     * Validate current step
     */
    function validateCurrentStep() {
        const currentStepName = wizardState.steps[wizardState.currentStep];
        
        switch (currentStepName) {
            case 'select_photos':
                if (wizardState.selectedPhotos.length === 0) {
                    showNotification('Por favor selecciona al menos una foto', 'warning');
                    return false;
                }
                break;
                
            case 'settings':
                const title = $('#post-title').val().trim();
                if (!title) {
                    showNotification('Por favor ingresa un título para el post', 'warning');
                    $('#post-title').focus();
                    return false;
                }
                break;
        }
        
        return true;
    }
    
    /**
     * Load photos for selection
     */
    function loadPhotosForSelection() {
        $('#photos-loading').show();
        $('#photo-grid').empty();
        
        $.ajax({
            url: soloylibre_wizard.ajax_url,
            type: 'POST',
            data: {
                action: 'wizard_get_photos',
                nonce: soloylibre_wizard.nonce,
                search: $('#photo-search').val(),
                category: $('#photo-category-filter').val(),
                date_filter: $('#photo-date-filter').val()
            },
            success: function(response) {
                $('#photos-loading').hide();
                
                if (response.success) {
                    displayPhotos(response.data);
                } else {
                    showNotification('Error al cargar las fotos', 'error');
                }
            },
            error: function() {
                $('#photos-loading').hide();
                showNotification('Error de conexión', 'error');
            }
        });
    }
    
    /**
     * Display photos in grid
     */
    function displayPhotos(photos) {
        const $grid = $('#photo-grid');
        $grid.empty();
        
        if (photos.length === 0) {
            $grid.html('<div class="no-photos"><p>No se encontraron fotos</p></div>');
            return;
        }
        
        photos.forEach(function(photo) {
            const isSelected = wizardState.selectedPhotos.includes(photo.id);
            const $photoItem = $(`
                <div class="photo-item ${isSelected ? 'selected' : ''}" data-photo-id="${photo.id}">
                    <img src="${photo.thumbnail}" alt="${photo.alt || photo.title}" loading="lazy">
                    <div class="photo-overlay">
                        <div class="photo-info">
                            <h4>${photo.title}</h4>
                        </div>
                    </div>
                    <div class="photo-checkbox"></div>
                </div>
            `);
            
            $grid.append($photoItem);
        });
        
        updateSelectedCounter();
    }
    
    /**
     * Toggle photo selection
     */
    function togglePhotoSelection() {
        const $item = $(this);
        const photoId = parseInt($item.data('photo-id'));
        
        if ($item.hasClass('selected')) {
            // Deselect
            $item.removeClass('selected');
            wizardState.selectedPhotos = wizardState.selectedPhotos.filter(id => id !== photoId);
        } else {
            // Select
            $item.addClass('selected');
            wizardState.selectedPhotos.push(photoId);
        }
        
        updateSelectedCounter();
        saveCurrentStepData();
    }
    
    /**
     * Update selected photos counter
     */
    function updateSelectedCounter() {
        $('#selected-count').text(wizardState.selectedPhotos.length);
    }
    
    /**
     * Search photos
     */
    function searchPhotos() {
        loadPhotosForSelection();
    }
    
    /**
     * Filter photos
     */
    function filterPhotos() {
        loadPhotosForSelection();
    }
    
    /**
     * Display selected photos in organize step
     */
    function displaySelectedPhotos() {
        const $container = $('#selected-photos-preview');
        $container.empty();
        
        if (wizardState.selectedPhotos.length === 0) {
            $container.html('<p>No hay fotos seleccionadas</p>');
            return;
        }
        
        // Get photo data for selected photos
        const selectedPhotoData = [];
        $('.photo-item.selected').each(function() {
            const $item = $(this);
            const photoId = $item.data('photo-id');
            const imgSrc = $item.find('img').attr('src');
            const title = $item.find('h4').text();
            
            selectedPhotoData.push({
                id: photoId,
                src: imgSrc,
                title: title
            });
        });
        
        selectedPhotoData.forEach(function(photo) {
            const $photoItem = $(`
                <div class="selected-photo-item" data-photo-id="${photo.id}">
                    <img src="${photo.src}" alt="${photo.title}">
                    <button class="remove-photo" data-photo-id="${photo.id}">×</button>
                </div>
            `);
            
            $container.append($photoItem);
        });
        
        // Bind remove photo events
        $('.remove-photo').on('click', function(e) {
            e.stopPropagation();
            const photoId = parseInt($(this).data('photo-id'));
            removeSelectedPhoto(photoId);
        });
    }
    
    /**
     * Remove photo from selection
     */
    function removeSelectedPhoto(photoId) {
        wizardState.selectedPhotos = wizardState.selectedPhotos.filter(id => id !== photoId);
        $(`.selected-photo-item[data-photo-id="${photoId}"]`).remove();
        $(`.photo-item[data-photo-id="${photoId}"]`).removeClass('selected');
        updateSelectedCounter();
        saveCurrentStepData();
    }
    
    /**
     * Update post preview
     */
    function updatePostPreview() {
        const title = $('#post-title').val() || 'Título del post';
        const content = $('#post-content').val() || 'Descripción de la galería...';
        const galleryStyle = $('#gallery-style').val() || 'grid';
        const includeSignature = $('#include-signature').is(':checked');
        
        let preview = `
            <h2>${title}</h2>
            <p>${content}</p>
            <div class="gallery-preview">
                <p><strong>Galería (${wizardState.selectedPhotos.length} fotos)</strong></p>
                <p>Estilo: ${galleryStyle}</p>
                <div class="preview-photos">
        `;
        
        // Show first few selected photos as preview
        const previewPhotos = wizardState.selectedPhotos.slice(0, 6);
        previewPhotos.forEach(function(photoId) {
            const $photoItem = $(`.photo-item[data-photo-id="${photoId}"]`);
            if ($photoItem.length) {
                const imgSrc = $photoItem.find('img').attr('src');
                preview += `<img src="${imgSrc}" style="width: 80px; height: 80px; object-fit: cover; margin: 5px; border-radius: 5px;">`;
            }
        });
        
        preview += '</div></div>';
        
        if (includeSignature) {
            preview += `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #CE1126; margin-top: 20px;">
                    <h4 style="color: #CE1126;">📸 Fotografía por Jose L Encarnacion (JoseTusabe)</h4>
                    <p><strong>SoloYLibre Photography</strong></p>
                    <p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>
                    <p>📞 718-713-5500 | 📧 <EMAIL></p>
                </div>
            `;
        }
        
        $('#post-preview').html(preview);
    }
    
    /**
     * Create post with gallery
     */
    function createPost() {
        $('#creation-progress').removeClass('hidden');
        $('#creation-success').addClass('hidden');
        
        const postData = {
            action: 'wizard_create_post',
            nonce: soloylibre_wizard.nonce,
            photo_ids: wizardState.selectedPhotos,
            post_title: $('#post-title').val(),
            post_content: $('#post-content').val(),
            post_category: $('#post-category').val(),
            post_tags: $('#post-tags').val(),
            post_status: $('#post-status').val(),
            gallery_style: $('#gallery-style').val(),
            include_signature: $('#include-signature').is(':checked') ? 1 : 0
        };
        
        $.ajax({
            url: soloylibre_wizard.ajax_url,
            type: 'POST',
            data: postData,
            success: function(response) {
                $('#creation-progress').addClass('hidden');
                
                if (response.success) {
                    $('#creation-success').removeClass('hidden');
                    $('#view-post-link').attr('href', response.data.post_url);
                    $('#edit-post-link').attr('href', response.data.edit_url);
                    $('#final-photo-count').text(response.data.photo_count);

                    // Show featured image info if available
                    if (response.data.featured_image_url) {
                        const featuredImageHtml = `
                            <div class="featured-image-success">
                                <img src="${response.data.featured_image_url}" alt="${response.data.featured_image_title}" class="featured-preview-small">
                                <p>🖼️ <strong>Imagen destacada:</strong> ${response.data.featured_image_title}</p>
                            </div>
                        `;
                        $('#creation-success .success-content').append(featuredImageHtml);
                    }

                    showNotification('¡Post creado exitosamente con imagen destacada!', 'success');
                } else {
                    showNotification('Error al crear el post: ' + (response.data || 'Error desconocido'), 'error');
                }
            },
            error: function() {
                $('#creation-progress').addClass('hidden');
                showNotification('Error de conexión al crear el post', 'error');
            }
        });
    }
    
    /**
     * Save current step data
     */
    function saveCurrentStepData() {
        const currentStepName = wizardState.steps[wizardState.currentStep];
        const stepData = {
            selectedPhotos: wizardState.selectedPhotos
        };
        
        // Collect form data based on current step
        if (currentStepName === 'settings') {
            stepData.postTitle = $('#post-title').val();
            stepData.postContent = $('#post-content').val();
            stepData.postCategory = $('#post-category').val();
            stepData.postTags = $('#post-tags').val();
            stepData.postStatus = $('#post-status').val();
            stepData.includeSignature = $('#include-signature').is(':checked');
        }
        
        if (currentStepName === 'organize') {
            stepData.galleryStyle = $('#gallery-style').val();
            stepData.galleryColumns = $('#gallery-columns').val();
            stepData.showCaptions = $('#show-captions').is(':checked');
            stepData.enableLightbox = $('#enable-lightbox').is(':checked');
        }
        
        // Save to server
        $.ajax({
            url: soloylibre_wizard.ajax_url,
            type: 'POST',
            data: {
                action: 'wizard_save_step',
                nonce: soloylibre_wizard.nonce,
                step: currentStepName,
                data: stepData
            }
        });
    }
    
    /**
     * Minimize wizard
     */
    function minimizeWizard() {
        $('#soloylibre-fullscreen-wizard').hide();
        $('#wizard-minimized').removeClass('hidden');
    }

    /**
     * Auto-minimize wizard when modal opens
     */
    function autoMinimizeOnModal() {
        // Auto-minimizar cuando se abre un modal
        $(document).on('DOMNodeInserted', function(e) {
            if ($(e.target).hasClass('soloylibre-modal-overlay')) {
                setTimeout(function() {
                    minimizeWizard();
                }, 300);
            }
        });

        // También minimizar cuando se hace clic en opciones principales
        $(document).on('click', '.option-card', function() {
            setTimeout(function() {
                if ($('.soloylibre-modal-overlay').length > 0) {
                    minimizeWizard();
                }
            }, 500);
        });
    }
    
    /**
     * Restore wizard
     */
    function restoreWizard() {
        $('#wizard-minimized').addClass('hidden');
        $('#soloylibre-fullscreen-wizard').show();
    }
    
    /**
     * Close wizard
     */
    function closeWizard() {
        if (confirm('¿Estás seguro de que quieres cerrar el wizard? Se perderá el progreso actual.')) {
            window.location.href = soloylibre_wizard.ajax_url.replace('admin-ajax.php', 'admin.php');
        }
    }
    
    /**
     * Reset wizard for new gallery
     */
    function resetWizard() {
        wizardState.currentStep = 0;
        wizardState.selectedPhotos = [];
        wizardState.formData = {};
        
        // Reset form fields
        $('#post-title, #post-content, #post-tags').val('');
        $('#post-category, #post-status, #gallery-style').prop('selectedIndex', 0);
        $('#include-signature, #show-captions, #enable-lightbox').prop('checked', true);
        
        // Reset UI
        $('.photo-item').removeClass('selected');
        $('#selected-photos-preview').empty();
        $('#post-preview').empty();
        
        showStep(0);
        updateProgressBar();
        updateNavigationButtons();
        updateSelectedCounter();
        
        showNotification('Wizard reiniciado. ¡Crea una nueva galería!', 'success');
    }
    
    /**
     * Go to dashboard
     */
    function goToDashboard() {
        window.location.href = soloylibre_wizard.ajax_url.replace('admin-ajax.php', 'admin.php');
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="wizard-notification wizard-notification-${type}">
                <span class="notification-icon">
                    ${type === 'success' ? '✅' : type === 'warning' ? '⚠️' : type === 'error' ? '❌' : 'ℹ️'}
                </span>
                <span class="notification-message">${message}</span>
                <button class="notification-close">×</button>
            </div>
        `);
        
        $('body').append($notification);
        
        // Auto-remove after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // Manual close
        $notification.find('.notification-close').on('click', function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        });
    }
    
    /**
     * Debounce function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = function() {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Global functions for feature cards
     */

    // Función global para Selección Inteligente
    window.openSmartSelection = function() {
        showLoadingModal('🎯 Cargando Selección Inteligente...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wizard_smart_selection',
                nonce: soloylibre_wizard.nonce
            },
            success: function(response) {
                hideLoadingModal();
                if (response.success) {
                    showSmartSelectionModal(response.data);
                } else {
                    showErrorModal('Error al cargar selección inteligente: ' + response.data);
                }
            },
            error: function() {
                hideLoadingModal();
                showErrorModal('Error de conexión al cargar selección inteligente');
            }
        });
    };

    // Función global para Estilos Dominicanos
    window.openDominicanStyles = function() {
        showLoadingModal('🇩🇴 Cargando Estilos Dominicanos...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wizard_dominican_styles',
                nonce: soloylibre_wizard.nonce
            },
            success: function(response) {
                hideLoadingModal();
                if (response.success) {
                    showDominicanStylesModal(response.data);
                } else {
                    showErrorModal('Error al cargar estilos dominicanos: ' + response.data);
                }
            },
            error: function() {
                hideLoadingModal();
                showErrorModal('Error de conexión al cargar estilos dominicanos');
            }
        });
    };

    // Función global para Posts Automáticos Rápidos
    window.openQuickAutoPosts = function() {
        showQuickAutoPostsModal();
    };

    // Función global para Posts Automáticos Avanzados
    window.openAdvancedAutoPosts = function() {
        showAdvancedAutoPostsModal();
    };

    // Función global para Estadísticas de Fotos
    window.openPhotoStatistics = function() {
        showPhotoStatisticsModal();
    };

    // Función global para Gestor de Fotos Masivo
    window.openBulkPhotoManager = function() {
        showBulkPhotoManagerModal();
    };

    /**
     * Modal functions
     */

    function showQuickAutoPostsModal() {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal quick-auto-posts-modal">
                    <div class="modal-header">
                        <h3>⚡ Posts Automáticos Rápidos</h3>
                        <button class="close-modal">×</button>
                    </div>
                    <div class="modal-content">
                        <div class="quick-stats-section">
                            <h4>📊 Estadísticas Rápidas</h4>
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number" id="quick-total-photos">-</div>
                                    <div class="stat-label">Total Fotos</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number" id="quick-published-photos">-</div>
                                    <div class="stat-label">Publicadas</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number" id="quick-available-photos">-</div>
                                    <div class="stat-label">Disponibles</div>
                                </div>
                            </div>
                        </div>

                        <div class="quick-config-section">
                            <h4>⚙️ Configuración Rápida</h4>
                            <div class="quick-options">
                                <div class="option-row">
                                    <label for="quick-photo-count">📸 Número de fotos:</label>
                                    <input type="number" id="quick-photo-count" min="5" max="50" value="10">
                                </div>
                                <div class="option-row">
                                    <label for="quick-style">🎨 Estilo:</label>
                                    <select id="quick-style">
                                        <option value="masonry">Masonry</option>
                                        <option value="grid">Grid</option>
                                        <option value="carousel">Carousel</option>
                                    </select>
                                </div>
                                <div class="option-row">
                                    <label for="quick-columns">📊 Columnas:</label>
                                    <select id="quick-columns">
                                        <option value="3">3 columnas</option>
                                        <option value="4" selected>4 columnas</option>
                                        <option value="5">5 columnas</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="quick-actions">
                            <button id="create-quick-post" class="btn btn-primary btn-large">
                                ⚡ Crear Post Automático Ahora
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary close-modal">Cancelar</button>
                    </div>
                </div>
            </div>
        `);

        // Load quick stats
        loadQuickPhotoStats();

        // Event handlers
        modal.on('click', '.close-modal', function() {
            modal.remove();
        });

        modal.on('click', '#create-quick-post', function() {
            createQuickAutoPost();
        });

        $('body').append(modal);
    }

    function showLoadingModal(message) {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal loading-modal">
                    <div class="loading-spinner"></div>
                    <p>${message}</p>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function hideLoadingModal() {
        $('.soloylibre-modal-overlay').remove();
    }

    function showErrorModal(message) {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal error-modal">
                    <h3>❌ Error</h3>
                    <p>${message}</p>
                    <button class="btn btn-primary close-modal">Cerrar</button>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function showSmartSelectionModal(data) {
        let photosHtml = '';
        data.photos.forEach(photo => {
            photosHtml += `
                <div class="smart-photo-item" data-photo-id="${photo.id}">
                    <img src="${photo.thumbnail}" alt="${photo.title}" title="${photo.title} (${photo.size})">
                    <div class="photo-overlay">
                        <div class="photo-info">
                            <span class="photo-title">${photo.title}</span>
                            <span class="photo-size">${photo.size}</span>
                        </div>
                        <button class="select-photo-btn" onclick="selectSmartPhoto(${photo.id})">✓ Seleccionar</button>
                    </div>
                </div>
            `;
        });

        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal smart-selection-modal">
                    <div class="modal-header">
                        <h3>🎯 Selección Inteligente</h3>
                        <button class="close-modal">×</button>
                    </div>
                    <div class="modal-content">
                        <div class="selection-stats">
                            <span>📊 ${data.selection_count} fotos seleccionadas de ${data.total_available} disponibles</span>
                        </div>
                        <div class="smart-photos-grid">
                            ${photosHtml}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary close-modal">Cancelar</button>
                        <button class="btn btn-primary" onclick="useSmartSelection()">🚀 Usar Selección</button>
                    </div>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function showDominicanStylesModal(data) {
        let stylesHtml = '';
        Object.keys(data.styles).forEach(key => {
            const style = data.styles[key];
            stylesHtml += `
                <div class="dominican-style-item" data-style="${key}">
                    <div class="style-preview" style="background: ${style.css.background}; border: ${style.css.border};">
                        <div class="style-overlay">
                            <h4>${style.name}</h4>
                            <p>${style.description}</p>
                            <button class="btn btn-primary" onclick="applyDominicanStyle('${key}')">🎨 Aplicar Estilo</button>
                        </div>
                    </div>
                </div>
            `;
        });

        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal dominican-styles-modal">
                    <div class="modal-header">
                        <h3>🇩🇴 Estilos Dominicanos</h3>
                        <button class="close-modal">×</button>
                    </div>
                    <div class="modal-content">
                        <p>Selecciona un estilo inspirado en la cultura dominicana para tu galería:</p>
                        <div class="dominican-styles-grid">
                            ${stylesHtml}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary close-modal">Cerrar</button>
                    </div>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function showAdvancedAutoPostsModal() {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal automatic-posts-expanded-modal">
                    <div class="modal-header">
                        <h3>🤖 Posts Automáticos Avanzados</h3>
                        <button class="close-modal">×</button>
                    </div>
                    <div class="modal-content">
                        <!-- Configuration Section -->
                        <div class="auto-config-section">
                            <h4>⚙️ Configuración</h4>
                            <div class="auto-post-options">
                                <div class="option-group">
                                    <label for="auto-photo-count-input">📸 Número de fotos:</label>
                                    <input type="number" id="auto-photo-count-input" min="5" max="100" value="15" placeholder="Ej: 15">
                                    <small>Mínimo 5, máximo 100 fotos</small>
                                </div>
                                <div class="option-group">
                                    <label for="auto-style">🎨 Estilo:</label>
                                    <select id="auto-style">
                                        <option value="masonry">Masonry</option>
                                        <option value="grid">Grid</option>
                                        <option value="carousel">Carousel</option>
                                    </select>
                                </div>
                                <div class="option-group">
                                    <label for="auto-columns">📊 Columnas:</label>
                                    <select id="auto-columns">
                                        <option value="2">2 columnas</option>
                                        <option value="3">3 columnas</option>
                                        <option value="4" selected>4 columnas</option>
                                        <option value="5">5 columnas</option>
                                        <option value="6">6 columnas</option>
                                    </select>
                                </div>
                            </div>
                            <div class="load-photos-section">
                                <button id="load-photos-preview" class="btn btn-primary">🔍 Cargar Vista Previa de Fotos</button>
                                <div id="photos-stats" class="photos-stats" style="display: none;">
                                    <span id="total-available">0 fotos disponibles</span>
                                    <span id="photos-categorized">0 categorizadas</span>
                                </div>
                            </div>
                        </div>

                        <!-- Photos Preview Section -->
                        <div id="photos-preview-section" class="photos-preview-section" style="display: none;">
                            <h4>📸 Vista Previa de Fotos</h4>
                            <div class="photos-controls">
                                <div class="control-group">
                                    <label>🔍 Buscar:</label>
                                    <input type="text" id="photos-search" placeholder="Buscar por título...">
                                </div>
                                <div class="control-group">
                                    <label>🔄 Ordenar:</label>
                                    <select id="photos-order">
                                        <option value="desc">Más recientes</option>
                                        <option value="asc">Más antiguas</option>
                                        <option value="random">Aleatorio</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <button id="select-all-photos" class="btn btn-secondary">✅ Seleccionar Todas</button>
                                    <button id="deselect-all-photos" class="btn btn-secondary">❌ Deseleccionar Todas</button>
                                </div>
                            </div>

                            <div id="photos-loading" class="loading-section" style="display: none;">
                                <div class="loading-spinner"></div>
                                <p>Cargando fotos...</p>
                            </div>

                            <div id="photos-grid" class="expanded-photos-grid"></div>

                            <div class="load-more-section">
                                <button id="load-more-photos" class="btn btn-secondary" style="display: none;">
                                    📥 Cargar Más Fotos
                                </button>
                            </div>
                        </div>

                        <!-- Categories Section -->
                        <div id="categories-section" class="categories-section" style="display: none;">
                            <h4>📂 Categorías de Fotos</h4>
                            <div class="categories-tabs">
                                <button class="category-tab active" data-category="selected">✅ Seleccionadas (<span id="selected-count">0</span>)</button>
                                <button class="category-tab" data-category="private">🔒 Privadas (<span id="private-count">0</span>)</button>
                                <button class="category-tab" data-category="unwanted">❌ No Deseadas (<span id="unwanted-count">0</span>)</button>
                                <button class="category-tab" data-category="deleted">🗑️ Para Borrar (<span id="deleted-count">0</span>)</button>
                            </div>

                            <div class="category-content">
                                <div id="category-selected" class="category-grid active"></div>
                                <div id="category-private" class="category-grid"></div>
                                <div id="category-unwanted" class="category-grid"></div>
                                <div id="category-deleted" class="category-grid"></div>
                            </div>

                            <div class="category-actions">
                                <button id="create-album-from-private" class="btn btn-info">📚 Crear Álbum de Privadas</button>
                                <button id="create-album-from-unwanted" class="btn btn-warning">📚 Crear Álbum de No Deseadas</button>
                                <button id="permanently-delete-photos" class="btn btn-danger">🗑️ Eliminar Permanentemente</button>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary close-modal">Cancelar</button>
                        <button id="create-auto-post-advanced" class="btn btn-primary" disabled>🚀 Crear Post Automático</button>
                    </div>
                </div>
            </div>
        `);
        $('body').append(modal);

        // Initialize event handlers
        initializeAdvancedAutoPostModal();
    }

    /**
     * Initialize Advanced Auto Post Modal
     */
    function initializeAdvancedAutoPostModal() {
        let currentPage = 1;
        let allPhotos = [];
        let categorizedPhotos = {
            selected: [],
            private: [],
            unwanted: [],
            deleted: []
        };

        // Load photos preview
        $('#load-photos-preview').on('click', function() {
            loadPhotosPreview();
        });

        // Search and filter
        $('#photos-search, #photos-order').on('change input', function() {
            filterPhotos();
        });

        // Select/Deselect all
        $('#select-all-photos').on('click', function() {
            selectAllPhotos();
        });

        $('#deselect-all-photos').on('click', function() {
            deselectAllPhotos();
        });

        // Load more photos
        $('#load-more-photos').on('click', function() {
            loadMorePhotos();
        });

        // Category tabs
        $('.category-tab').on('click', function() {
            switchCategoryTab($(this).data('category'));
        });

        // Category actions
        $('#create-album-from-private').on('click', function() {
            createAlbumFromCategory('private');
        });

        $('#create-album-from-unwanted').on('click', function() {
            createAlbumFromCategory('unwanted');
        });

        $('#permanently-delete-photos').on('click', function() {
            permanentlyDeletePhotos();
        });

        // Create auto post
        $('#create-auto-post-advanced').on('click', function() {
            createAdvancedAutoPost();
        });

        function loadPhotosPreview() {
            $('#photos-loading').show();
            $('#load-photos-preview').prop('disabled', true).text('🔄 Cargando...');

            $.post(ajaxurl, {
                action: 'soloylibre_get_all_photos_preview',
                nonce: soloylibre_wizard.nonce
            }, function(response) {
                $('#photos-loading').hide();
                $('#load-photos-preview').prop('disabled', false).text('🔍 Cargar Vista Previa de Fotos');

                if (response.success) {
                    allPhotos = response.data.photos;
                    displayPhotosGrid(allPhotos);
                    updatePhotosStats(response.data);
                    $('#photos-preview-section').show();
                    $('#categories-section').show();
                } else {
                    showNotification('Error al cargar fotos: ' + response.data, 'error');
                }
            }).fail(function() {
                $('#photos-loading').hide();
                $('#load-photos-preview').prop('disabled', false).text('🔍 Cargar Vista Previa de Fotos');
                showNotification('Error de conexión', 'error');
            });
        }

        function displayPhotosGrid(photos) {
            const grid = $('#photos-grid');
            grid.empty();

            photos.forEach(function(photo) {
                const photoItem = $(`
                    <div class="expanded-photo-item" data-photo-id="${photo.id}">
                        <img src="${photo.thumbnail}" alt="${photo.title}" title="${photo.title}">
                        <div class="photo-overlay">
                            <div class="photo-info">
                                <span class="photo-title">${photo.title}</span>
                                <span class="photo-size">${photo.size}</span>
                            </div>
                            <div class="photo-actions">
                                <button class="action-btn select-btn" data-action="select" title="Seleccionar para álbum">✅</button>
                                <button class="action-btn private-btn" data-action="private" title="Marcar como privada">🔒</button>
                                <button class="action-btn unwanted-btn" data-action="unwanted" title="Marcar como no deseada">❌</button>
                                <button class="action-btn delete-btn" data-action="delete" title="Marcar para borrar">🗑️</button>
                            </div>
                        </div>
                    </div>
                `);

                grid.append(photoItem);
            });

            // Add click handlers for action buttons
            $('.action-btn').on('click', function(e) {
                e.stopPropagation();
                const action = $(this).data('action');
                const photoId = parseInt($(this).closest('.expanded-photo-item').data('photo-id'));
                categorizePhoto(photoId, action);
            });
        }

        function categorizePhoto(photoId, category) {
            // Remove from all categories first
            Object.keys(categorizedPhotos).forEach(cat => {
                categorizedPhotos[cat] = categorizedPhotos[cat].filter(id => id !== photoId);
            });

            // Add to new category
            if (category !== 'remove') {
                categorizedPhotos[category].push(photoId);
            }

            // Update visual state
            const photoItem = $(`.expanded-photo-item[data-photo-id="${photoId}"]`);
            photoItem.removeClass('selected private unwanted deleted');

            if (category !== 'remove') {
                photoItem.addClass(category);
            }

            // Update category displays
            updateCategoryDisplays();
            updateCreateButton();
        }

        function updateCategoryDisplays() {
            Object.keys(categorizedPhotos).forEach(category => {
                const count = categorizedPhotos[category].length;
                $(`#${category}-count`).text(count);

                // Update category grid
                const categoryGrid = $(`#category-${category}`);
                categoryGrid.empty();

                categorizedPhotos[category].forEach(photoId => {
                    const photo = allPhotos.find(p => p.id === photoId);
                    if (photo) {
                        const categoryItem = $(`
                            <div class="category-photo-item" data-photo-id="${photoId}">
                                <img src="${photo.thumbnail}" alt="${photo.title}" title="${photo.title}">
                                <button class="remove-from-category" data-photo-id="${photoId}">×</button>
                            </div>
                        `);
                        categoryGrid.append(categoryItem);
                    }
                });
            });

            // Add remove handlers
            $('.remove-from-category').on('click', function() {
                const photoId = parseInt($(this).data('photo-id'));
                categorizePhoto(photoId, 'remove');
            });
        }

        function updateCreateButton() {
            const hasSelected = categorizedPhotos.selected.length > 0;
            $('#create-auto-post-advanced').prop('disabled', !hasSelected);
        }

        function switchCategoryTab(category) {
            $('.category-tab').removeClass('active');
            $(`.category-tab[data-category="${category}"]`).addClass('active');

            $('.category-grid').removeClass('active');
            $(`#category-${category}`).addClass('active');
        }

        function createAlbumFromCategory(category) {
            if (categorizedPhotos[category].length === 0) {
                showNotification(`No hay fotos en la categoría ${category}`, 'warning');
                return;
            }

            const photoIds = categorizedPhotos[category];
            const categoryNames = {
                private: 'Privadas',
                unwanted: 'No Deseadas'
            };

            // Create album with these photos
            $.post(ajaxurl, {
                action: 'soloylibre_create_category_album',
                nonce: soloylibre_wizard.nonce,
                photo_ids: photoIds,
                category: category,
                category_name: categoryNames[category]
            }, function(response) {
                if (response.success) {
                    showNotification(`Álbum de ${categoryNames[category]} creado exitosamente`, 'success');
                } else {
                    showNotification('Error al crear álbum: ' + response.data, 'error');
                }
            });
        }

        function createAdvancedAutoPost() {
            const photoCount = parseInt($('#auto-photo-count-input').val());
            const style = $('#auto-style').val();
            const columns = $('#auto-columns').val();
            const selectedPhotos = categorizedPhotos.selected;

            if (selectedPhotos.length === 0) {
                showNotification('Selecciona al menos una foto', 'warning');
                return;
            }

            $('#create-auto-post-advanced').prop('disabled', true).text('🔄 Creando...');

            $.post(ajaxurl, {
                action: 'soloylibre_create_advanced_auto_post',
                nonce: soloylibre_wizard.nonce,
                photo_ids: selectedPhotos,
                style: style,
                columns: columns,
                categorized_photos: categorizedPhotos
            }, function(response) {
                $('#create-auto-post-advanced').prop('disabled', false).text('🚀 Crear Post Automático');

                if (response.success) {
                    showAutomaticPostSuccess(response.data);
                    $('.soloylibre-modal-overlay').remove();
                } else {
                    showNotification('Error: ' + response.data, 'error');
                }
            }).fail(function() {
                $('#create-auto-post-advanced').prop('disabled', false).text('🚀 Crear Post Automático');
                showNotification('Error de conexión', 'error');
            });
        }
    }

    /**
     * Action functions
     */

    window.selectSmartPhoto = function(photoId) {
        const photoItem = $(`.smart-photo-item[data-photo-id="${photoId}"]`);
        photoItem.toggleClass('selected');

        const btn = photoItem.find('.select-photo-btn');
        if (photoItem.hasClass('selected')) {
            btn.text('✓ Seleccionado').addClass('selected');
        } else {
            btn.text('✓ Seleccionar').removeClass('selected');
        }
    };

    window.useSmartSelection = function() {
        const selectedPhotos = [];
        $('.smart-photo-item.selected').each(function() {
            selectedPhotos.push($(this).data('photo-id'));
        });

        if (selectedPhotos.length === 0) {
            alert('Por favor selecciona al menos una foto');
            return;
        }

        // Agregar fotos seleccionadas al wizard
        wizardState.selectedPhotos = selectedPhotos;
        updateSelectedPhotosDisplay();

        $('.soloylibre-modal-overlay').remove();
        showSuccessMessage(`✅ ${selectedPhotos.length} fotos agregadas a tu selección`);
    };

    window.applyDominicanStyle = function(styleKey) {
        // Aplicar estilo dominicano al wizard
        wizardState.formData.gallery_style = styleKey;

        $('.soloylibre-modal-overlay').remove();
        showSuccessMessage(`🇩🇴 Estilo dominicano "${styleKey}" aplicado exitosamente`);
    };

    window.createAutomaticPost = function() {
        const photoCount = $('#auto-photo-count').val();
        const style = $('#auto-style').val();

        showLoadingModal('🤖 Creando post automático...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wizard_automatic_posts',
                nonce: soloylibre_wizard.nonce,
                photo_count: photoCount,
                style: style,
                post_type: 'auto'
            },
            success: function(response) {
                hideLoadingModal();
                if (response.success) {
                    $('.soloylibre-modal-overlay').remove();
                    showAutomaticPostSuccess(response.data);
                } else {
                    showErrorModal('Error al crear post automático: ' + response.data);
                }
            },
            error: function() {
                hideLoadingModal();
                showErrorModal('Error de conexión al crear post automático');
            }
        });
    };

    function showAutomaticPostSuccess(data) {
        const featuredImageHtml = data.featured_image_url ?
            `<div class="featured-image-info">
                <img src="${data.featured_image_url}" alt="${data.featured_image_title}" class="featured-preview">
                <p>🖼️ <strong>Imagen destacada:</strong> ${data.featured_image_title}</p>
            </div>` : '';

        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal success-modal">
                    <div class="modal-header">
                        <h3>🎉 ¡Post Creado Exitosamente!</h3>
                    </div>
                    <div class="modal-content">
                        <div class="success-info">
                            <h4>${data.title}</h4>
                            <p>📸 ${data.photos_count} fotos publicadas</p>
                            <p>🆔 Post ID: #${data.post_id}</p>
                            ${featuredImageHtml}
                        </div>
                        <div class="success-actions">
                            <a href="${data.post_url}" target="_blank" class="btn btn-primary">👁️ Ver Post</a>
                            <a href="${data.edit_url}" target="_blank" class="btn btn-secondary">✏️ Editar Post</a>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-success" onclick="createAutomaticPost()">🤖 Crear Otro</button>
                        <button class="btn btn-primary close-modal">Cerrar</button>
                    </div>
                </div>
            </div>
        `);
        $('body').append(modal);
    }

    function showPhotoStatisticsModal() {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal photo-statistics-modal">
                    <div class="modal-header">
                        <h3>📊 Estadísticas Detalladas de Fotos</h3>
                        <button class="close-modal">×</button>
                    </div>
                    <div class="modal-content">
                        <div class="stats-overview">
                            <h4>📈 Resumen General</h4>
                            <div class="overview-grid">
                                <div class="overview-card">
                                    <div class="overview-icon">📸</div>
                                    <div class="overview-content">
                                        <div class="overview-number" id="stats-total-photos">-</div>
                                        <div class="overview-label">Total de Fotos</div>
                                    </div>
                                </div>
                                <div class="overview-card">
                                    <div class="overview-icon">✅</div>
                                    <div class="overview-content">
                                        <div class="overview-number" id="stats-published-photos">-</div>
                                        <div class="overview-label">Fotos Publicadas</div>
                                    </div>
                                </div>
                                <div class="overview-card">
                                    <div class="overview-icon">🔒</div>
                                    <div class="overview-content">
                                        <div class="overview-number" id="stats-private-photos">-</div>
                                        <div class="overview-label">Fotos Privadas</div>
                                    </div>
                                </div>
                                <div class="overview-card">
                                    <div class="overview-icon">❌</div>
                                    <div class="overview-content">
                                        <div class="overview-number" id="stats-unwanted-photos">-</div>
                                        <div class="overview-label">No Deseadas</div>
                                    </div>
                                </div>
                                <div class="overview-card">
                                    <div class="overview-icon">🗑️</div>
                                    <div class="overview-content">
                                        <div class="overview-number" id="stats-deleted-photos">-</div>
                                        <div class="overview-label">Para Eliminar</div>
                                    </div>
                                </div>
                                <div class="overview-card">
                                    <div class="overview-icon">🆓</div>
                                    <div class="overview-content">
                                        <div class="overview-number" id="stats-available-photos">-</div>
                                        <div class="overview-label">Disponibles</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stats-details">
                            <h4>📋 Detalles por Categoría</h4>
                            <div class="details-tabs">
                                <button class="detail-tab active" data-tab="recent">📅 Recientes</button>
                                <button class="detail-tab" data-tab="popular">⭐ Populares</button>
                                <button class="detail-tab" data-tab="sizes">📏 Tamaños</button>
                                <button class="detail-tab" data-tab="dates">📆 Por Fechas</button>
                            </div>

                            <div class="detail-content">
                                <div id="detail-recent" class="detail-panel active">
                                    <div class="recent-photos-grid" id="recent-photos-list">
                                        <div class="loading-placeholder">Cargando fotos recientes...</div>
                                    </div>
                                </div>
                                <div id="detail-popular" class="detail-panel">
                                    <div class="popular-photos-list" id="popular-photos-list">
                                        <div class="loading-placeholder">Cargando fotos populares...</div>
                                    </div>
                                </div>
                                <div id="detail-sizes" class="detail-panel">
                                    <div class="sizes-chart" id="sizes-chart">
                                        <div class="loading-placeholder">Analizando tamaños...</div>
                                    </div>
                                </div>
                                <div id="detail-dates" class="detail-panel">
                                    <div class="dates-chart" id="dates-chart">
                                        <div class="loading-placeholder">Analizando fechas...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stats-actions">
                            <button id="refresh-stats" class="btn btn-primary">🔄 Actualizar Estadísticas</button>
                            <button id="export-stats" class="btn btn-secondary">📤 Exportar Datos</button>
                            <button id="reset-categories" class="btn btn-warning">🔄 Reset Categorías</button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary close-modal">Cerrar</button>
                    </div>
                </div>
            </div>
        `);

        // Load statistics
        loadDetailedPhotoStatistics();

        // Event handlers
        modal.on('click', '.close-modal', function() {
            modal.remove();
        });

        modal.on('click', '.detail-tab', function() {
            const tab = $(this).data('tab');
            $('.detail-tab').removeClass('active');
            $('.detail-panel').removeClass('active');
            $(this).addClass('active');
            $(`#detail-${tab}`).addClass('active');

            // Load tab content if needed
            loadTabContent(tab);
        });

        modal.on('click', '#refresh-stats', function() {
            loadDetailedPhotoStatistics();
        });

        modal.on('click', '#export-stats', function() {
            exportPhotoStatistics();
        });

        modal.on('click', '#reset-categories', function() {
            resetPhotoCategories();
        });

        $('body').append(modal);
    }

    function showBulkPhotoManagerModal() {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal bulk-manager-modal">
                    <div class="modal-header">
                        <h3>📁 Gestor de Fotos Masivo</h3>
                        <button class="close-modal">×</button>
                    </div>
                    <div class="modal-content">
                        <div class="bulk-controls">
                            <h4>🔧 Acciones Masivas</h4>
                            <div class="bulk-actions-grid">
                                <button class="bulk-action-btn" data-action="select-all">
                                    <span class="action-icon">✅</span>
                                    <span class="action-text">Seleccionar Todas</span>
                                </button>
                                <button class="bulk-action-btn" data-action="mark-private">
                                    <span class="action-icon">🔒</span>
                                    <span class="action-text">Marcar como Privadas</span>
                                </button>
                                <button class="bulk-action-btn" data-action="mark-unwanted">
                                    <span class="action-icon">❌</span>
                                    <span class="action-text">Marcar como No Deseadas</span>
                                </button>
                                <button class="bulk-action-btn" data-action="create-albums">
                                    <span class="action-icon">📚</span>
                                    <span class="action-text">Crear Álbumes</span>
                                </button>
                                <button class="bulk-action-btn" data-action="batch-publish">
                                    <span class="action-icon">🚀</span>
                                    <span class="action-text">Publicar en Lotes</span>
                                </button>
                                <button class="bulk-action-btn" data-action="organize-dates">
                                    <span class="action-icon">📅</span>
                                    <span class="action-text">Organizar por Fechas</span>
                                </button>
                            </div>
                        </div>

                        <div class="bulk-photo-grid" id="bulk-photo-grid">
                            <div class="loading-placeholder">Cargando fotos para gestión masiva...</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary close-modal">Cerrar</button>
                        <button id="apply-bulk-actions" class="btn btn-primary" disabled>🔧 Aplicar Acciones</button>
                    </div>
                </div>
            </div>
        `);

        // Load photos for bulk management
        loadBulkPhotoGrid();

        // Event handlers
        modal.on('click', '.close-modal', function() {
            modal.remove();
        });

        modal.on('click', '.bulk-action-btn', function() {
            const action = $(this).data('action');
            handleBulkAction(action);
        });

        modal.on('click', '#apply-bulk-actions', function() {
            applyBulkActions();
        });

        $('body').append(modal);
    }

    function showSuccessMessage(message) {
        const notification = $(`
            <div class="success-notification">
                ${message}
            </div>
        `);

        $('body').append(notification);

        setTimeout(() => {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    // Support functions for new modals
    function loadQuickPhotoStats() {
        $.post(ajaxurl, {
            action: 'soloylibre_get_photo_stats',
            nonce: soloylibre_wizard.nonce
        }, function(response) {
            if (response.success) {
                const stats = response.data;
                $('#quick-total-photos').text(stats.total || 0);
                $('#quick-published-photos').text(stats.published || 0);
                $('#quick-available-photos').text(stats.available || 0);
            }
        });
    }

    function createQuickAutoPost() {
        const photoCount = $('#quick-photo-count').val();
        const style = $('#quick-style').val();
        const columns = $('#quick-columns').val();

        $('#create-quick-post').prop('disabled', true).text('🔄 Creando...');

        $.post(ajaxurl, {
            action: 'soloylibre_create_quick_auto_post',
            nonce: soloylibre_wizard.nonce,
            photo_count: photoCount,
            style: style,
            columns: columns
        }, function(response) {
            if (response.success) {
                showNotification('✅ Post automático creado exitosamente!', 'success');
                $('.soloylibre-modal-overlay').remove();

                // Show success modal with post link
                showPostCreatedModal(response.data.post_id, response.data.post_url);
            } else {
                showNotification('❌ Error: ' + response.data, 'error');
            }
        }).always(function() {
            $('#create-quick-post').prop('disabled', false).text('⚡ Crear Post Automático Ahora');
        });
    }

    function showPostCreatedModal(postId, postUrl) {
        const modal = $(`
            <div class="soloylibre-modal-overlay">
                <div class="soloylibre-modal post-created-modal">
                    <div class="modal-header">
                        <h3>🎉 ¡Post Creado Exitosamente!</h3>
                    </div>
                    <div class="modal-content">
                        <div class="success-content">
                            <div class="success-icon">✅</div>
                            <h4>Tu post automático ha sido creado</h4>
                            <p>Post ID: <strong>${postId}</strong></p>
                            <div class="post-actions">
                                <a href="${postUrl}" target="_blank" class="btn btn-primary">👁️ Ver Post</a>
                                <a href="${postUrl.replace('/wp/', '/wp/wp-admin/post.php?post=' + postId + '&action=edit')}" target="_blank" class="btn btn-secondary">✏️ Editar Post</a>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary close-modal">Cerrar</button>
                    </div>
                </div>
            </div>
        `);

        modal.on('click', '.close-modal', function() {
            modal.remove();
        });

        $('body').append(modal);
    }

})(jQuery);
