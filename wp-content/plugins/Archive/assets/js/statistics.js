/**
 * SoloYLibre Gallery Pro - Statistics Dashboard JavaScript
 * Interactive charts and data visualization
 * Developed by JEYKO AI for Jose <PERSON> (JoseTusabe)
 */

(function($) {
    'use strict';
    
    let photosChart = null;
    let categoriesChart = null;
    
    $(document).ready(function() {
        initializeStatistics();
    });
    
    /**
     * Initialize statistics dashboard
     */
    function initializeStatistics() {
        bindEvents();
        initializeCharts();
        addAnimations();
    }
    
    /**
     * Bind event handlers
     */
    function bindEvents() {
        // Refresh stats button
        $('#refresh-stats').on('click', refreshStatistics);
        
        // Export stats button
        $('#export-stats').on('click', exportStatistics);
        
        // Chart period selector
        $('#chart-period').on('change', updatePhotosChart);
        
        // View all buttons
        $('#view-all-photos').on('click', function() {
            window.location.href = soloylibre_stats.ajax_url.replace('admin-ajax.php', 'edit.php?post_type=soloylibre_photo');
        });
        
        $('#view-all-albums').on('click', function() {
            window.location.href = soloylibre_stats.ajax_url.replace('admin-ajax.php', 'admin.php?page=soloylibre-albums');
        });
    }
    
    /**
     * Initialize charts
     */
    function initializeCharts() {
        initializePhotosChart();
        initializeCategoriesChart();
    }
    
    /**
     * Initialize photos activity chart
     */
    function initializePhotosChart() {
        const ctx = document.getElementById('photos-activity-chart');
        if (!ctx) return;
        
        // Get initial data
        getChartData('photos', 30).then(function(data) {
            photosChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: 'Fotos Publicadas',
                        data: data.data,
                        borderColor: soloylibre_stats.colors.dominican_red,
                        backgroundColor: soloylibre_stats.colors.dominican_red + '20',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: soloylibre_stats.colors.dominican_red,
                        pointBorderColor: soloylibre_stats.colors.dominican_white,
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: soloylibre_stats.colors.dominican_blue,
                            titleColor: soloylibre_stats.colors.dominican_white,
                            bodyColor: soloylibre_stats.colors.dominican_white,
                            borderColor: soloylibre_stats.colors.dominican_red,
                            borderWidth: 2,
                            cornerRadius: 8,
                            displayColors: false,
                            callbacks: {
                                title: function(context) {
                                    return 'Fecha: ' + context[0].label;
                                },
                                label: function(context) {
                                    return 'Fotos: ' + context.parsed.y;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: '#e9ecef',
                                borderDash: [5, 5]
                            },
                            ticks: {
                                color: '#666666',
                                font: {
                                    size: 12
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#e9ecef',
                                borderDash: [5, 5]
                            },
                            ticks: {
                                color: '#666666',
                                font: {
                                    size: 12
                                },
                                stepSize: 1
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    animation: {
                        duration: 2000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        });
    }
    
    /**
     * Initialize categories pie chart
     */
    function initializeCategoriesChart() {
        const ctx = document.getElementById('categories-chart');
        if (!ctx) return;
        
        // Get categories data
        getChartData('categories').then(function(data) {
            categoriesChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.labels,
                    datasets: [{
                        data: data.data,
                        backgroundColor: [
                            soloylibre_stats.colors.dominican_red,
                            soloylibre_stats.colors.dominican_blue,
                            soloylibre_stats.colors.primary,
                            soloylibre_stats.colors.secondary,
                            '#28a745',
                            '#ffc107',
                            '#dc3545',
                            '#6f42c1',
                            '#fd7e14',
                            '#20c997'
                        ],
                        borderWidth: 3,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 5,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle',
                                font: {
                                    size: 12,
                                    weight: '600'
                                },
                                color: '#333333'
                            }
                        },
                        tooltip: {
                            backgroundColor: soloylibre_stats.colors.dominican_blue,
                            titleColor: soloylibre_stats.colors.dominican_white,
                            bodyColor: soloylibre_stats.colors.dominican_white,
                            borderColor: soloylibre_stats.colors.dominican_red,
                            borderWidth: 2,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    cutout: '60%',
                    animation: {
                        animateRotate: true,
                        animateScale: true,
                        duration: 2000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        });
    }
    
    /**
     * Get chart data via AJAX
     */
    function getChartData(type, period = null) {
        return new Promise(function(resolve, reject) {
            $.ajax({
                url: soloylibre_stats.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_statistics_data',
                    nonce: soloylibre_stats.nonce,
                    type: type,
                    period: period
                },
                success: function(response) {
                    if (response.success) {
                        resolve(response.data);
                    } else {
                        reject(response.data);
                    }
                },
                error: function() {
                    reject('Error de conexión');
                }
            });
        });
    }
    
    /**
     * Update photos chart with new period
     */
    function updatePhotosChart() {
        const period = $('#chart-period').val();
        
        // Show loading state
        showChartLoading('photos-activity-chart');
        
        getChartData('photos', period).then(function(data) {
            if (photosChart) {
                photosChart.data.labels = data.labels;
                photosChart.data.datasets[0].data = data.data;
                photosChart.update('active');
            }
            hideChartLoading('photos-activity-chart');
        }).catch(function(error) {
            console.error('Error updating chart:', error);
            hideChartLoading('photos-activity-chart');
            showNotification('Error al actualizar el gráfico', 'error');
        });
    }
    
    /**
     * Refresh all statistics
     */
    function refreshStatistics() {
        const $button = $('#refresh-stats');
        const originalText = $button.text();
        
        $button.prop('disabled', true).text('🔄 Actualizando...');
        
        // Add loading overlay
        showPageLoading();
        
        // Simulate refresh delay and reload page
        setTimeout(function() {
            window.location.reload();
        }, 2000);
    }
    
    /**
     * Export statistics
     */
    function exportStatistics() {
        const $button = $('#export-stats');
        const originalText = $button.text();
        
        $button.prop('disabled', true).text('📊 Exportando...');
        
        $.ajax({
            url: soloylibre_stats.ajax_url,
            type: 'POST',
            data: {
                action: 'export_statistics',
                nonce: soloylibre_stats.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Create and download CSV file
                    const blob = new Blob([response.data.csv_content], { type: 'text/csv;charset=utf-8;' });
                    const link = document.createElement('a');
                    const url = URL.createObjectURL(blob);
                    
                    link.setAttribute('href', url);
                    link.setAttribute('download', response.data.filename);
                    link.style.visibility = 'hidden';
                    
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    showNotification('📊 Estadísticas exportadas exitosamente', 'success');
                } else {
                    showNotification('Error al exportar estadísticas', 'error');
                }
                
                $button.prop('disabled', false).text(originalText);
            },
            error: function() {
                showNotification('Error de conexión al exportar', 'error');
                $button.prop('disabled', false).text(originalText);
            }
        });
    }
    
    /**
     * Show chart loading state
     */
    function showChartLoading(chartId) {
        const $chart = $('#' + chartId);
        const $container = $chart.parent();
        
        if (!$container.find('.chart-loading').length) {
            $container.append(`
                <div class="chart-loading">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                    </div>
                </div>
            `);
        }
        
        $chart.css('opacity', '0.3');
    }
    
    /**
     * Hide chart loading state
     */
    function hideChartLoading(chartId) {
        const $chart = $('#' + chartId);
        const $container = $chart.parent();
        
        $container.find('.chart-loading').remove();
        $chart.css('opacity', '1');
    }
    
    /**
     * Show page loading overlay
     */
    function showPageLoading() {
        const loadingHtml = `
            <div class="page-loading-overlay" id="page-loading">
                <div class="loading-content">
                    <div class="dominican-flag-spinner">🇩🇴</div>
                    <h3>Actualizando Estadísticas...</h3>
                    <p>Obteniendo los datos más recientes de SoloYLibre</p>
                    <div class="loading-bar">
                        <div class="loading-progress"></div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(loadingHtml);
        
        // Animate progress bar
        setTimeout(function() {
            $('.loading-progress').css('width', '100%');
        }, 500);
    }
    
    /**
     * Add animations to elements
     */
    function addAnimations() {
        // Add CSS for loading overlay
        const loadingCSS = `
            <style id="statistics-loading-styles">
                .chart-loading {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 10;
                }
                
                .page-loading-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    z-index: 999999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .loading-content {
                    background: white;
                    padding: 40px;
                    border-radius: 20px;
                    text-align: center;
                    max-width: 400px;
                    width: 90%;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                }
                
                .dominican-flag-spinner {
                    font-size: 4rem;
                    margin-bottom: 20px;
                    animation: spin 2s linear infinite;
                }
                
                .loading-content h3 {
                    color: #CE1126;
                    margin-bottom: 15px;
                    font-size: 1.5rem;
                }
                
                .loading-content p {
                    color: #666;
                    margin-bottom: 30px;
                }
                
                .loading-bar {
                    background: #e9ecef;
                    height: 8px;
                    border-radius: 4px;
                    overflow: hidden;
                }
                
                .loading-progress {
                    background: linear-gradient(90deg, #CE1126, #002D62);
                    height: 100%;
                    width: 0%;
                    transition: width 2s ease;
                    border-radius: 4px;
                }
                
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
        
        $('head').append(loadingCSS);
        
        // Animate stat cards on scroll
        animateOnScroll();
    }
    
    /**
     * Animate elements on scroll
     */
    function animateOnScroll() {
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1
        });
        
        // Observe stat cards and other elements
        $('.stat-card, .chart-container, .table-container, .insight-card').each(function() {
            this.style.opacity = '0';
            this.style.transform = 'translateY(30px)';
            this.style.transition = 'all 0.6s ease';
            observer.observe(this);
        });
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        
        const $notification = $(`
            <div class="statistics-notification statistics-notification-${type}">
                <span class="notification-icon">${icons[type]}</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close">×</button>
            </div>
        `);
        
        // Add notification styles if not already added
        if (!$('#statistics-notification-styles').length) {
            $('head').append(`
                <style id="statistics-notification-styles">
                    .statistics-notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: white;
                        border-radius: 10px;
                        padding: 15px 20px;
                        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                        z-index: 999999;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        min-width: 300px;
                        max-width: 500px;
                        border-left: 4px solid #007cba;
                        animation: slideInRight 0.3s ease;
                    }
                    
                    .statistics-notification-success { border-left-color: #28a745; }
                    .statistics-notification-error { border-left-color: #dc3545; }
                    .statistics-notification-warning { border-left-color: #ffc107; }
                    
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    
                    .notification-icon { font-size: 1.2rem; }
                    .notification-message { flex: 1; font-weight: 500; }
                    .notification-close {
                        background: none;
                        border: none;
                        font-size: 1.2rem;
                        cursor: pointer;
                        color: #666;
                        padding: 0;
                        width: 20px;
                        height: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        transition: all 0.3s ease;
                    }
                    .notification-close:hover {
                        background: #f0f0f0;
                        color: #333;
                    }
                </style>
            `);
        }
        
        $('body').append($notification);
        
        // Auto-remove after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // Manual close
        $notification.find('.notification-close').on('click', function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        });
    }
    
})(jQuery);
