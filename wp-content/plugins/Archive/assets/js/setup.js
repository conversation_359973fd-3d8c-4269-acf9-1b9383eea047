/**
 * SoloYLibre Gallery Pro - Setup JavaScript
 * Handles initial setup functionality
 * Developed by JEYKO AI for Jose <PERSON> (JoseTusabe)
 */

(function($) {
    'use strict';
    
    $(document).ready(function() {
        initializeSetup();
    });
    
    /**
     * Initialize setup functionality
     */
    function initializeSetup() {
        bindSetupEvents();
        addSetupAnimations();
    }
    
    /**
     * Bind setup events
     */
    function bindSetupEvents() {
        // Skip setup button
        $('.setup-skip-btn').on('click', function(e) {
            e.preventDefault();
            handleSkipSetup();
        });
        
        // Complete setup button (if on setup page)
        $('.complete-setup-btn').on('click', function(e) {
            e.preventDefault();
            handleCompleteSetup();
        });
        
        // Dismiss notice
        $(document).on('click', '.soloylibre-setup-notice .notice-dismiss', function() {
            handleSkipSetup();
        });
    }
    
    /**
     * Handle skip setup
     */
    function handleSkipSetup() {
        const $button = $('.setup-skip-btn');
        const originalText = $button.text();
        
        $button.prop('disabled', true).text('Omitiendo...');
        
        $.ajax({
            url: soloylibre_setup.ajax_url,
            type: 'POST',
            data: {
                action: 'soloylibre_skip_setup',
                nonce: soloylibre_setup.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    
                    // Hide the notice
                    $('.soloylibre-setup-notice').fadeOut(500);
                    
                    // Redirect after a delay
                    setTimeout(function() {
                        if (response.data.redirect_url) {
                            window.location.href = response.data.redirect_url;
                        }
                    }, 2000);
                } else {
                    showNotification(soloylibre_setup.texts.error, 'error');
                    $button.prop('disabled', false).text(originalText);
                }
            },
            error: function() {
                showNotification(soloylibre_setup.texts.error, 'error');
                $button.prop('disabled', false).text(originalText);
            }
        });
    }
    
    /**
     * Handle complete setup
     */
    function handleCompleteSetup() {
        const $button = $('.complete-setup-btn');
        const originalText = $button.text();
        
        $button.prop('disabled', true).text(soloylibre_setup.texts.completing);
        
        // Show progress animation
        showSetupProgress();
        
        $.ajax({
            url: soloylibre_setup.ajax_url,
            type: 'POST',
            data: {
                action: 'soloylibre_complete_setup',
                nonce: soloylibre_setup.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    
                    // Show completion animation
                    showSetupComplete();
                    
                    // Redirect after animation
                    setTimeout(function() {
                        if (response.data.redirect_url) {
                            window.location.href = response.data.redirect_url;
                        }
                    }, 3000);
                } else {
                    showNotification(soloylibre_setup.texts.error, 'error');
                    $button.prop('disabled', false).text(originalText);
                    hideSetupProgress();
                }
            },
            error: function() {
                showNotification(soloylibre_setup.texts.error, 'error');
                $button.prop('disabled', false).text(originalText);
                hideSetupProgress();
            }
        });
    }
    
    /**
     * Show setup progress
     */
    function showSetupProgress() {
        const progressHtml = `
            <div class="setup-progress-overlay" id="setup-progress">
                <div class="setup-progress-content">
                    <div class="dominican-flag-animation">🇩🇴</div>
                    <h3>Configurando SoloYLibre Gallery Pro...</h3>
                    <div class="progress-steps">
                        <div class="progress-step active" data-step="1">
                            <div class="step-icon">📸</div>
                            <span>Configurando fotógrafo</span>
                        </div>
                        <div class="progress-step" data-step="2">
                            <div class="step-icon">🎨</div>
                            <span>Creando categorías</span>
                        </div>
                        <div class="progress-step" data-step="3">
                            <div class="step-icon">🏷️</div>
                            <span>Configurando etiquetas</span>
                        </div>
                        <div class="progress-step" data-step="4">
                            <div class="step-icon">📝</div>
                            <span>Creando post de bienvenida</span>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(progressHtml);
        
        // Animate progress steps
        animateProgressSteps();
    }
    
    /**
     * Animate progress steps
     */
    function animateProgressSteps() {
        let currentStep = 1;
        const totalSteps = 4;
        
        const stepInterval = setInterval(function() {
            // Complete current step
            $(`.progress-step[data-step="${currentStep}"]`).addClass('completed').removeClass('active');
            
            currentStep++;
            
            if (currentStep <= totalSteps) {
                // Activate next step
                $(`.progress-step[data-step="${currentStep}"]`).addClass('active');
                
                // Update progress bar
                const progress = (currentStep / totalSteps) * 100;
                $('.progress-fill').css('width', progress + '%');
            } else {
                // All steps completed
                $('.progress-fill').css('width', '100%');
                clearInterval(stepInterval);
            }
        }, 800);
    }
    
    /**
     * Show setup complete
     */
    function showSetupComplete() {
        $('#setup-progress .setup-progress-content').html(`
            <div class="setup-complete-animation">
                <div class="success-icon">✅</div>
                <h2>¡Configuración Completada!</h2>
                <p>SoloYLibre Gallery Pro está listo para usar</p>
                <div class="photographer-info">
                    <h3>📸 ${soloylibre_setup.photographer.name}</h3>
                    <p>${soloylibre_setup.photographer.brand}</p>
                    <p>🇩🇴 ${soloylibre_setup.photographer.location}</p>
                </div>
                <div class="redirect-info">
                    <p>Redirigiendo al wizard en 3 segundos...</p>
                </div>
            </div>
        `);
    }
    
    /**
     * Hide setup progress
     */
    function hideSetupProgress() {
        $('#setup-progress').fadeOut(500, function() {
            $(this).remove();
        });
    }
    
    /**
     * Add setup animations
     */
    function addSetupAnimations() {
        // Add CSS for animations
        const animationCSS = `
            <style id="soloylibre-setup-animations">
                .setup-progress-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    z-index: 999999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .setup-progress-content {
                    background: white;
                    padding: 40px;
                    border-radius: 20px;
                    text-align: center;
                    max-width: 500px;
                    width: 90%;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                }
                
                .dominican-flag-animation {
                    font-size: 4rem;
                    margin-bottom: 20px;
                    animation: wave 2s ease-in-out infinite;
                }
                
                @keyframes wave {
                    0%, 100% { transform: rotate(0deg); }
                    25% { transform: rotate(-10deg); }
                    75% { transform: rotate(10deg); }
                }
                
                .setup-progress-content h3 {
                    color: #CE1126;
                    margin-bottom: 30px;
                    font-size: 1.5rem;
                }
                
                .progress-steps {
                    margin: 30px 0;
                }
                
                .progress-step {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    padding: 10px;
                    margin-bottom: 10px;
                    border-radius: 10px;
                    transition: all 0.3s ease;
                    opacity: 0.5;
                }
                
                .progress-step.active {
                    background: rgba(206, 17, 38, 0.1);
                    opacity: 1;
                    transform: scale(1.05);
                }
                
                .progress-step.completed {
                    background: rgba(40, 167, 69, 0.1);
                    opacity: 1;
                    transform: scale(1);
                }
                
                .progress-step.completed .step-icon::after {
                    content: '✅';
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    font-size: 0.8rem;
                }
                
                .step-icon {
                    font-size: 1.5rem;
                    position: relative;
                }
                
                .progress-bar {
                    background: #e9ecef;
                    height: 8px;
                    border-radius: 4px;
                    overflow: hidden;
                    margin-top: 20px;
                }
                
                .progress-fill {
                    background: linear-gradient(90deg, #CE1126, #002D62);
                    height: 100%;
                    width: 0%;
                    transition: width 0.5s ease;
                    border-radius: 4px;
                }
                
                .setup-complete-animation {
                    animation: fadeInUp 0.5s ease;
                }
                
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                
                .success-icon {
                    font-size: 4rem;
                    margin-bottom: 20px;
                    animation: bounce 1s ease;
                }
                
                @keyframes bounce {
                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                    40% { transform: translateY(-20px); }
                    60% { transform: translateY(-10px); }
                }
                
                .setup-complete-animation h2 {
                    color: #28a745;
                    margin-bottom: 15px;
                }
                
                .photographer-info {
                    background: linear-gradient(135deg, #CE1126 0%, #002D62 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 15px;
                    margin: 20px 0;
                }
                
                .photographer-info h3 {
                    color: white;
                    margin-bottom: 10px;
                }
                
                .photographer-info p {
                    margin: 5px 0;
                    opacity: 0.9;
                }
                
                .redirect-info {
                    margin-top: 20px;
                    color: #666;
                    font-style: italic;
                }
            </style>
        `;
        
        $('head').append(animationCSS);
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        
        const $notification = $(`
            <div class="soloylibre-notification soloylibre-notification-${type}">
                <span class="notification-icon">${icons[type]}</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close">×</button>
            </div>
        `);
        
        // Add notification styles if not already added
        if (!$('#soloylibre-notification-styles').length) {
            $('head').append(`
                <style id="soloylibre-notification-styles">
                    .soloylibre-notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: white;
                        border-radius: 10px;
                        padding: 15px 20px;
                        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                        z-index: 999999;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        min-width: 300px;
                        max-width: 500px;
                        border-left: 4px solid #007cba;
                        animation: slideInRight 0.3s ease;
                    }
                    
                    .soloylibre-notification-success { border-left-color: #28a745; }
                    .soloylibre-notification-error { border-left-color: #dc3545; }
                    .soloylibre-notification-warning { border-left-color: #ffc107; }
                    
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    
                    .notification-icon { font-size: 1.2rem; }
                    .notification-message { flex: 1; font-weight: 500; }
                    .notification-close {
                        background: none;
                        border: none;
                        font-size: 1.2rem;
                        cursor: pointer;
                        color: #666;
                        padding: 0;
                        width: 20px;
                        height: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        transition: all 0.3s ease;
                    }
                    .notification-close:hover {
                        background: #f0f0f0;
                        color: #333;
                    }
                </style>
            `);
        }
        
        $('body').append($notification);
        
        // Auto-remove after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // Manual close
        $notification.find('.notification-close').on('click', function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        });
    }
    
})(jQuery);
