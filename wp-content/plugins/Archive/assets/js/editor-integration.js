/**
 * SoloYLibre Gallery Editor Integration
 * JavaScript for WordPress editor integration
 * 
 * @package SoloYLibre_Gallery_Pro
 * @version 5.3.0
 * <AUTHOR> AI for Jose L <PERSON> (JoseTusabe)
 */

(function($) {
    'use strict';
    
    let currentPage = 1;
    let totalPages = 1;
    let selectedPhotos = [];
    let autoGeneratedPhotos = [];
    let currentDominicanStyle = '';
    
    $(document).ready(function() {
        initEditorIntegration();
    });
    
    /**
     * Initialize editor integration
     */
    function initEditorIntegration() {
        // Classic editor button click
        $(document).on('click', '.soloylibre-editor-btn', function() {
            openSoloYLibreModal();
        });
        
        // Tab switching
        $(document).on('click', '.tab-btn', function() {
            switchTab($(this).data('tab'));
        });
        
        // Photo controls
        $(document).on('change', '#photo-search, #photos-per-page, #photo-order', function() {
            loadPhotos(true);
        });
        
        // Load more photos
        $(document).on('click', '#load-more-photos', function() {
            loadPhotos(false);
        });
        
        // Photo selection
        $(document).on('click', '.editor-photo-item', function() {
            togglePhotoSelection($(this));
        });
        
        // Auto generate
        $(document).on('click', '#generate-auto-gallery', function() {
            generateAutoGallery();
        });
        
        // Dominican style selection
        $(document).on('click', '.style-option', function() {
            selectDominicanStyle($(this));
        });
        
        // Insert gallery
        $(document).on('click', '#insert-gallery', function() {
            insertGallery();
        });
        
        // Modal close
        $(document).on('click', '.close-modal', function() {
            closeSoloYLibreModal();
        });
        
        // Close modal on overlay click
        $(document).on('click', '.soloylibre-modal-overlay', function(e) {
            if (e.target === this) {
                closeSoloYLibreModal();
            }
        });
    }
    
    /**
     * Open SoloYLibre modal
     */
    function openSoloYLibreModal() {
        $('#soloylibre-editor-modal').show();
        resetModal();
        loadPhotos(true);
    }
    
    /**
     * Close SoloYLibre modal
     */
    window.closeSoloYLibreModal = function() {
        $('#soloylibre-editor-modal').hide();
        resetModal();
    };
    
    /**
     * Reset modal state
     */
    function resetModal() {
        selectedPhotos = [];
        autoGeneratedPhotos = [];
        currentDominicanStyle = '';
        currentPage = 1;
        
        // Reset tabs
        $('.tab-btn').removeClass('active');
        $('.tab-btn[data-tab="select"]').addClass('active');
        $('.tab-content').removeClass('active');
        $('#tab-select').addClass('active');
        
        // Reset controls
        $('#photo-search').val('');
        $('#photos-per-page').val('50');
        $('#photo-order').val('desc');
        $('#auto-photo-count').val('15');
        
        // Reset UI
        updatePhotoStats();
        updateInsertButton();
        $('#photos-grid').empty();
        $('#auto-preview').hide();
        $('.style-option').removeClass('selected');
    }
    
    /**
     * Switch tabs
     */
    function switchTab(tabName) {
        $('.tab-btn').removeClass('active');
        $('.tab-btn[data-tab="' + tabName + '"]').addClass('active');
        
        $('.tab-content').removeClass('active');
        $('#tab-' + tabName).addClass('active');
        
        if (tabName === 'select' && $('#photos-grid').is(':empty')) {
            loadPhotos(true);
        }
    }
    
    /**
     * Load photos from server
     */
    function loadPhotos(reset = false) {
        if (reset) {
            currentPage = 1;
            $('#photos-grid').empty();
            $('#load-more-photos').hide();
        }
        
        $('#photos-loading').show();
        
        const data = {
            action: 'soloylibre_editor_get_photos',
            nonce: soloylibre_editor.nonce,
            page: currentPage,
            per_page: $('#photos-per-page').val(),
            search: $('#photo-search').val(),
            order: $('#photo-order').val()
        };
        
        $.post(soloylibre_editor.ajax_url, data, function(response) {
            $('#photos-loading').hide();
            
            if (response.success) {
                displayPhotos(response.data.photos, !reset);
                updatePhotoStats(response.data);
                
                if (response.data.has_more) {
                    $('#load-more-photos').show();
                    currentPage++;
                } else {
                    $('#load-more-photos').hide();
                }
                
                totalPages = response.data.total_pages;
            } else {
                showNotification('Error al cargar fotos: ' + response.data, 'error');
            }
        }).fail(function() {
            $('#photos-loading').hide();
            showNotification('Error de conexión al cargar fotos', 'error');
        });
    }
    
    /**
     * Display photos in grid
     */
    function displayPhotos(photos, append = false) {
        const grid = $('#photos-grid');
        
        if (!append) {
            grid.empty();
        }
        
        photos.forEach(function(photo) {
            const isSelected = selectedPhotos.includes(photo.id);
            const photoItem = $(`
                <div class="editor-photo-item ${isSelected ? 'selected' : ''}" data-photo-id="${photo.id}">
                    <img src="${photo.thumbnail}" alt="${photo.title}" title="${photo.title} (${photo.size})">
                    <div class="photo-overlay">
                        <div class="photo-info">
                            <span class="photo-title">${photo.title}</span>
                            <span class="photo-size">${photo.size}</span>
                        </div>
                        <div class="photo-actions">
                            <button class="select-btn ${isSelected ? 'selected' : ''}" title="Seleccionar">
                                ${isSelected ? '✓' : '+'}
                            </button>
                        </div>
                    </div>
                </div>
            `);
            
            grid.append(photoItem);
        });
    }
    
    /**
     * Toggle photo selection
     */
    function togglePhotoSelection($photoItem) {
        const photoId = parseInt($photoItem.data('photo-id'));
        const isSelected = $photoItem.hasClass('selected');
        
        if (isSelected) {
            // Deselect
            $photoItem.removeClass('selected');
            $photoItem.find('.select-btn').removeClass('selected').text('+');
            selectedPhotos = selectedPhotos.filter(id => id !== photoId);
        } else {
            // Select
            $photoItem.addClass('selected');
            $photoItem.find('.select-btn').addClass('selected').text('✓');
            selectedPhotos.push(photoId);
        }
        
        updatePhotoStats();
        updateInsertButton();
    }
    
    /**
     * Update photo statistics
     */
    function updatePhotoStats(data = null) {
        if (data) {
            $('#photos-available').text(`${data.total_available} fotos disponibles`);
        }
        $('#photos-selected').text(`${selectedPhotos.length} seleccionadas`);
    }
    
    /**
     * Update insert button state
     */
    function updateInsertButton() {
        const hasPhotos = selectedPhotos.length > 0 || autoGeneratedPhotos.length > 0;
        $('#insert-gallery').prop('disabled', !hasPhotos);
    }
    
    /**
     * Generate auto gallery
     */
    function generateAutoGallery() {
        const photoCount = parseInt($('#auto-photo-count').val());
        const style = $('#auto-style').val();
        const columns = parseInt($('#auto-columns').val());
        
        if (photoCount < 5 || photoCount > 50) {
            showNotification('El número de fotos debe estar entre 5 y 50', 'error');
            return;
        }
        
        $('#generate-auto-gallery').prop('disabled', true).text('🔄 Generando...');
        
        const data = {
            action: 'soloylibre_editor_auto_generate',
            nonce: soloylibre_editor.nonce,
            photo_count: photoCount,
            style: style,
            columns: columns
        };
        
        $.post(soloylibre_editor.ajax_url, data, function(response) {
            $('#generate-auto-gallery').prop('disabled', false).text('🎲 Generar Galería Automática');
            
            if (response.success) {
                autoGeneratedPhotos = response.data.photo_ids;
                displayAutoPreview(response.data.photos);
                showNotification(response.data.message, 'success');
                updateInsertButton();
            } else {
                showNotification('Error: ' + response.data, 'error');
            }
        }).fail(function() {
            $('#generate-auto-gallery').prop('disabled', false).text('🎲 Generar Galería Automática');
            showNotification('Error de conexión', 'error');
        });
    }
    
    /**
     * Display auto-generated photos preview
     */
    function displayAutoPreview(photos) {
        const previewGrid = $('#auto-photos-preview');
        previewGrid.empty();
        
        photos.forEach(function(photo) {
            const photoItem = $(`
                <div class="auto-photo-item">
                    <img src="${photo.thumbnail}" alt="${photo.title}" title="${photo.title}">
                </div>
            `);
            previewGrid.append(photoItem);
        });
        
        $('#auto-preview').show();
    }
    
    /**
     * Select Dominican style
     */
    function selectDominicanStyle($styleOption) {
        $('.style-option').removeClass('selected');
        $styleOption.addClass('selected');
        currentDominicanStyle = $styleOption.data('style');
        
        showNotification(`Estilo ${currentDominicanStyle} seleccionado`, 'success');
    }
    
    /**
     * Insert gallery into editor
     */
    function insertGallery() {
        const photosToUse = autoGeneratedPhotos.length > 0 ? autoGeneratedPhotos : selectedPhotos;
        
        if (photosToUse.length === 0) {
            showNotification('Selecciona fotos o genera una galería automática', 'error');
            return;
        }
        
        $('#insert-gallery').prop('disabled', true).text('🔄 Insertando...');
        
        const data = {
            action: 'soloylibre_editor_create_gallery',
            nonce: soloylibre_editor.nonce,
            photo_ids: photosToUse,
            style: $('#gallery-style').val(),
            columns: $('#gallery-columns').val(),
            interactions: $('#enable-interactions').is(':checked'),
            dominican_style: currentDominicanStyle
        };
        
        $.post(soloylibre_editor.ajax_url, data, function(response) {
            $('#insert-gallery').prop('disabled', false).text('🚀 Insertar Galería');
            
            if (response.success) {
                insertShortcodeIntoEditor(response.data.shortcode);
                showNotification(response.data.message, 'success');
                closeSoloYLibreModal();
            } else {
                showNotification('Error: ' + response.data, 'error');
            }
        }).fail(function() {
            $('#insert-gallery').prop('disabled', false).text('🚀 Insertar Galería');
            showNotification('Error de conexión', 'error');
        });
    }
    
    /**
     * Insert shortcode into editor
     */
    function insertShortcodeIntoEditor(shortcode) {
        // Classic Editor
        if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor && !tinyMCE.activeEditor.isHidden()) {
            tinyMCE.activeEditor.execCommand('mceInsertContent', false, shortcode);
        } else if ($('#content').length) {
            const textarea = $('#content')[0];
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const text = textarea.value;
            
            textarea.value = text.substring(0, start) + shortcode + text.substring(end);
            textarea.selectionStart = textarea.selectionEnd = start + shortcode.length;
            textarea.focus();
        }
        
        // Gutenberg Editor
        if (typeof wp !== 'undefined' && wp.data && wp.data.select('core/editor')) {
            const { insertBlocks } = wp.data.dispatch('core/editor');
            const { createBlock } = wp.blocks;
            
            const block = createBlock('core/shortcode', {
                text: shortcode
            });
            
            insertBlocks(block);
        }
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const notification = $(`
            <div class="soloylibre-notification ${type}">
                ${message}
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(function() {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }
    
})(jQuery);
