/**
 * SoloYLibre Gallery Frontend JavaScript
 * Professional photo gallery plugin for Jose L Encarnac<PERSON>
 */

(function($) {
    'use strict';

    // Gallery object
    var SoloYLibreGallery = {
        
        // Initialize gallery
        init: function() {
            this.bindEvents();
            this.initializeGalleries();
        },
        
        // Bind events
        bindEvents: function() {
            $(document).on('click', '.filter-tab, .filter-chip', this.handleFilterClick);
            $(document).on('change', '.album-dropdown, .album-select', this.handleAlbumChange);
            $(document).on('click', '.soloylibre-load-more-btn', this.handleLoadMore);
            $(document).on('click', '.like-btn', this.handleLike);
            $(document).on('click', '.share-btn', this.handleShare);
            $(document).on('click', '.soloylibre-photo-item, .soloylibre-photo-card', this.handlePhotoClick);
            
            // Keyboard navigation for TikTok style
            $(document).on('keydown', this.handleKeyboardNavigation);
        },
        
        // Initialize all galleries on page
        initializeGalleries: function() {
            $('.soloylibre-gallery').each(function() {
                var $gallery = $(this);
                var style = $gallery.data('style');
                
                switch(style) {
                    case 'tiktok':
                        SoloYLibreGallery.initTikTokGallery($gallery);
                        break;
                    case 'grid':
                        SoloYLibreGallery.initGridGallery($gallery);
                        break;
                    case 'masonry':
                        SoloYLibreGallery.initMasonryGallery($gallery);
                        break;
                    case 'carousel':
                        SoloYLibreGallery.initCarouselGallery($gallery);
                        break;
                    case 'lightbox':
                        SoloYLibreGallery.initLightboxGallery($gallery);
                        break;
                }
                
                // Set initial page
                $gallery.data('page', 1);
            });
        },
        
        // Initialize TikTok style gallery
        initTikTokGallery: function($gallery) {
            var $container = $gallery.find('.soloylibre-tiktok-container');
            var isLoading = false;
            
            // Infinite scroll
            $container.on('scroll', function() {
                var scrollTop = $container.scrollTop();
                var scrollHeight = $container[0].scrollHeight;
                var clientHeight = $container.height();
                
                if (scrollTop + clientHeight >= scrollHeight - 100 && !isLoading) {
                    isLoading = true;
                    SoloYLibreGallery.loadMorePhotos($gallery, function() {
                        isLoading = false;
                    });
                }
            });
            
            // Smooth scroll snap behavior
            var isScrolling = false;
            $container.on('scroll', function() {
                if (!isScrolling) {
                    isScrolling = true;
                    setTimeout(function() {
                        isScrolling = false;
                    }, 100);
                }
            });
            
            // Auto-play videos if any
            this.handleVideoAutoplay($container);
        },
        
        // Initialize Grid gallery
        initGridGallery: function($gallery) {
            // Masonry layout if needed
            if (typeof $.fn.masonry !== 'undefined') {
                $gallery.find('.soloylibre-grid-container').masonry({
                    itemSelector: '.soloylibre-photo-card',
                    columnWidth: '.soloylibre-photo-card',
                    percentPosition: true
                });
            }
            
            // Lazy loading
            this.initLazyLoading($gallery);
        },
        
        // Initialize Masonry gallery
        initMasonryGallery: function($gallery) {
            var $container = $gallery.find('.soloylibre-masonry-container');
            
            // Wait for images to load
            $container.imagesLoaded(function() {
                // Initialize masonry if library is available
                if (typeof $.fn.masonry !== 'undefined') {
                    $container.masonry({
                        itemSelector: '.soloylibre-photo-item',
                        columnWidth: '.soloylibre-photo-item',
                        percentPosition: true
                    });
                }
            });
            
            // Lazy loading
            this.initLazyLoading($gallery);
        },
        
        // Initialize Carousel gallery
        initCarouselGallery: function($gallery) {
            // Implement carousel functionality
            console.log('Carousel gallery initialized');
        },
        
        // Initialize Lightbox gallery
        initLightboxGallery: function($gallery) {
            // Implement lightbox functionality
            console.log('Lightbox gallery initialized');
        },
        
        // Handle filter clicks
        handleFilterClick: function(e) {
            e.preventDefault();
            
            var $this = $(this);
            var $gallery = $this.closest('.soloylibre-gallery');
            var category = $this.data('category') || '';
            
            // Update active state
            $this.siblings().removeClass('active');
            $this.addClass('active');
            
            // Update gallery data
            $gallery.data('category', category);
            $gallery.data('page', 1);
            
            // Clear current photos
            SoloYLibreGallery.clearGalleryPhotos($gallery);
            
            // Load filtered photos
            SoloYLibreGallery.loadMorePhotos($gallery);
        },
        
        // Handle album change
        handleAlbumChange: function(e) {
            var $this = $(this);
            var $gallery = $this.closest('.soloylibre-gallery');
            var album = $this.val();
            
            // Update gallery data
            $gallery.data('album', album);
            $gallery.data('page', 1);
            
            // Clear current photos
            SoloYLibreGallery.clearGalleryPhotos($gallery);
            
            // Load album photos
            SoloYLibreGallery.loadMorePhotos($gallery);
        },
        
        // Handle load more button
        handleLoadMore: function(e) {
            e.preventDefault();
            
            var $this = $(this);
            var $gallery = $this.closest('.soloylibre-gallery');
            
            $this.prop('disabled', true).text('Cargando...');
            
            SoloYLibreGallery.loadMorePhotos($gallery, function(success) {
                if (success) {
                    $this.prop('disabled', false).text('Cargar Más Fotos');
                } else {
                    $this.text('No hay más fotos').prop('disabled', true);
                }
            });
        },
        
        // Handle photo like
        handleLike: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $this = $(this);
            var photoId = $this.data('photo-id') || $this.closest('[data-photo-id]').data('photo-id');
            
            if (!photoId) return;
            
            // Animate like
            $this.addClass('liked');
            
            // AJAX call to update like count
            $.ajax({
                url: soloylibre_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'like_photo',
                    nonce: soloylibre_ajax.nonce,
                    photo_id: photoId
                },
                success: function(response) {
                    if (response.success) {
                        // Update like count in UI
                        var currentText = $this.text();
                        var newCount = response.data.likes_count;
                        $this.text(currentText.replace(/\d+/, newCount));
                    }
                }
            });
        },
        
        // Handle photo share
        handleShare: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $this = $(this);
            var $photoItem = $this.closest('[data-photo-id]');
            var photoId = $photoItem.data('photo-id');
            var photoTitle = $photoItem.find('.photo-title').text();
            
            // Create share URL
            var shareUrl = window.location.href + '?photo=' + photoId;
            var shareText = 'Mira esta increíble fotografía de ' + photoTitle + ' por SoloYLibre Photography';
            
            // Check if Web Share API is available
            if (navigator.share) {
                navigator.share({
                    title: photoTitle,
                    text: shareText,
                    url: shareUrl
                });
            } else {
                // Fallback to copy to clipboard
                this.copyToClipboard(shareUrl);
                this.showNotification('Enlace copiado al portapapeles');
            }
        },
        
        // Handle photo click
        handlePhotoClick: function(e) {
            // Don't trigger if clicking on buttons
            if ($(e.target).is('button') || $(e.target).closest('button').length) {
                return;
            }
            
            var $this = $(this);
            var photoId = $this.data('photo-id');
            
            // Check if it's premium content
            if ($this.find('.premium-lock, .premium-overlay, .membership-lock').length) {
                return; // Don't open premium content
            }
            
            // Open lightbox or navigate to photo page
            SoloYLibreGallery.openPhotoLightbox(photoId, $this);
        },
        
        // Handle keyboard navigation
        handleKeyboardNavigation: function(e) {
            var $tiktokGallery = $('.soloylibre-tiktok-style');
            
            if ($tiktokGallery.length && $tiktokGallery.is(':visible')) {
                var $container = $tiktokGallery.find('.soloylibre-tiktok-container');
                var $items = $container.find('.soloylibre-photo-item');
                var itemHeight = $container.height();
                
                switch(e.keyCode) {
                    case 38: // Up arrow
                        e.preventDefault();
                        $container.scrollTop($container.scrollTop() - itemHeight);
                        break;
                    case 40: // Down arrow
                        e.preventDefault();
                        $container.scrollTop($container.scrollTop() + itemHeight);
                        break;
                    case 32: // Space bar
                        e.preventDefault();
                        $container.scrollTop($container.scrollTop() + itemHeight);
                        break;
                }
            }
        },
        
        // Load more photos via AJAX
        loadMorePhotos: function($gallery, callback) {
            var page = $gallery.data('page') || 1;
            var style = $gallery.data('style');
            var album = $gallery.data('album') || '';
            var category = $gallery.data('category') || '';
            var limit = $gallery.data('limit') || 12;
            
            // Show loading indicator
            $gallery.find('.soloylibre-loading-indicator').addClass('active');
            
            $.ajax({
                url: soloylibre_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'load_more_photos',
                    nonce: soloylibre_ajax.nonce,
                    page: page + 1,
                    style: style,
                    album: album,
                    category: category,
                    limit: limit
                },
                success: function(response) {
                    $gallery.find('.soloylibre-loading-indicator').removeClass('active');
                    
                    if (response.success && response.data.html) {
                        // Append new photos
                        var $container = SoloYLibreGallery.getPhotoContainer($gallery);
                        var $newItems = $(response.data.html);
                        
                        $container.append($newItems);
                        
                        // Update page number
                        $gallery.data('page', page + 1);
                        
                        // Reinitialize masonry if needed
                        if (style === 'masonry' && typeof $.fn.masonry !== 'undefined') {
                            $container.masonry('appended', $newItems);
                        }
                        
                        // Initialize lazy loading for new items
                        SoloYLibreGallery.initLazyLoading($gallery, $newItems);
                        
                        if (callback) callback(true);
                        
                        // Hide load more button if no more photos
                        if (!response.data.has_more) {
                            $gallery.find('.soloylibre-load-more-btn')
                                   .text(soloylibre_ajax.no_more_text)
                                   .prop('disabled', true);
                        }
                    } else {
                        if (callback) callback(false);
                    }
                },
                error: function() {
                    $gallery.find('.soloylibre-loading-indicator').removeClass('active');
                    if (callback) callback(false);
                }
            });
        },
        
        // Get photo container based on gallery style
        getPhotoContainer: function($gallery) {
            var style = $gallery.data('style');
            
            switch(style) {
                case 'tiktok':
                    return $gallery.find('.soloylibre-tiktok-container');
                case 'grid':
                    return $gallery.find('.soloylibre-grid-container');
                case 'masonry':
                    return $gallery.find('.soloylibre-masonry-container');
                default:
                    return $gallery.find('.soloylibre-grid-container');
            }
        },
        
        // Clear gallery photos
        clearGalleryPhotos: function($gallery) {
            var $container = this.getPhotoContainer($gallery);
            $container.empty();
            
            // Reset load more button
            $gallery.find('.soloylibre-load-more-btn')
                   .text('Cargar Más Fotos')
                   .prop('disabled', false);
        },
        
        // Initialize lazy loading
        initLazyLoading: function($gallery, $items) {
            var $images = $items ? $items.find('img') : $gallery.find('img');
            
            $images.each(function() {
                var $img = $(this);
                if ($img.data('src') && !$img.attr('src')) {
                    // Implement intersection observer for lazy loading
                    if ('IntersectionObserver' in window) {
                        var observer = new IntersectionObserver(function(entries) {
                            entries.forEach(function(entry) {
                                if (entry.isIntersecting) {
                                    var img = entry.target;
                                    img.src = img.dataset.src;
                                    img.classList.remove('lazy');
                                    observer.unobserve(img);
                                }
                            });
                        });
                        
                        observer.observe($img[0]);
                    } else {
                        // Fallback for older browsers
                        $img.attr('src', $img.data('src'));
                    }
                }
            });
        },
        
        // Handle video autoplay
        handleVideoAutoplay: function($container) {
            var $videos = $container.find('video');
            
            $videos.each(function() {
                var video = this;
                
                // Play video when in viewport
                var observer = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            video.play();
                        } else {
                            video.pause();
                        }
                    });
                }, { threshold: 0.5 });
                
                observer.observe(video);
            });
        },
        
        // Open photo lightbox
        openPhotoLightbox: function(photoId, $photoElement) {
            // Create lightbox HTML
            var $lightbox = $('<div class="soloylibre-lightbox">');
            var $overlay = $('<div class="lightbox-overlay">');
            var $content = $('<div class="lightbox-content">');
            var $close = $('<button class="lightbox-close">&times;</button>');
            
            // Get photo data
            var photoSrc = $photoElement.find('img').attr('src');
            var photoTitle = $photoElement.find('.photo-title').text();
            var photoDescription = $photoElement.find('.photo-description').text();
            
            // Build lightbox content
            $content.html(`
                <img src="${photoSrc}" alt="${photoTitle}" class="lightbox-image">
                <div class="lightbox-info">
                    <h3>${photoTitle}</h3>
                    <p>${photoDescription}</p>
                </div>
            `);
            
            $lightbox.append($overlay, $content, $close);
            $('body').append($lightbox);
            
            // Show lightbox
            setTimeout(function() {
                $lightbox.addClass('active');
            }, 10);
            
            // Close lightbox events
            $close.add($overlay).on('click', function() {
                $lightbox.removeClass('active');
                setTimeout(function() {
                    $lightbox.remove();
                }, 300);
            });
            
            // Keyboard close
            $(document).on('keydown.lightbox', function(e) {
                if (e.keyCode === 27) { // ESC key
                    $close.click();
                    $(document).off('keydown.lightbox');
                }
            });
        },
        
        // Copy to clipboard utility
        copyToClipboard: function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text);
            } else {
                // Fallback
                var $temp = $('<textarea>');
                $('body').append($temp);
                $temp.val(text).select();
                document.execCommand('copy');
                $temp.remove();
            }
        },
        
        // Show notification
        showNotification: function(message) {
            var $notification = $('<div class="soloylibre-notification">').text(message);
            $('body').append($notification);
            
            setTimeout(function() {
                $notification.addClass('show');
            }, 10);
            
            setTimeout(function() {
                $notification.removeClass('show');
                setTimeout(function() {
                    $notification.remove();
                }, 300);
            }, 3000);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SoloYLibreGallery.init();
    });
    
    // Make gallery object globally available
    window.SoloYLibreGallery = SoloYLibreGallery;
    
})(jQuery);
