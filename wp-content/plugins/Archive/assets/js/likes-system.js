
/**
 * SoloYLibre Likes System JavaScript
 * Handles heart/like interactions
 */

jQuery(document).ready(function($) {
    // Handle like button clicks
    $(document).on('click', '.soloylibre-like-btn', function(e) {
        e.preventDefault();
        
        const $btn = $(this);
        const photoId = $btn.data('photo-id');
        const $counter = $btn.find('.like-count');
        
        if (!$btn.hasClass('processing')) {
            $btn.addClass('processing');
            
            $.ajax({
                url: soloylibreGlass.ajax_url || ajaxurl,
                type: 'POST',
                data: {
                    action: 'glass_add_like',
                    nonce: soloylibreGlass.nonce || soloylibre_interactions.nonce,
                    photo_id: photoId
                },
                success: function(response) {
                    if (response.success) {
                        $counter.text(response.data.like_count);
                        $btn.addClass('liked');
                        
                        // Add heart animation
                        $btn.addClass('heart-animation');
                        setTimeout(() => $btn.removeClass('heart-animation'), 600);
                        
                        // Show success message
                        showLikeMessage('💖 Like agregado!', 'success');
                    } else {
                        showLikeMessage('❌ Error: ' + response.data, 'error');
                    }
                },
                error: function() {
                    showLikeMessage('❌ Error de conexión', 'error');
                },
                complete: function() {
                    $btn.removeClass('processing');
                }
            });
        }
    });
    
    function showLikeMessage(message, type) {
        const $message = $('<div class="like-message like-message-' + type + '">' + message + '</div>');
        $('body').append($message);
        
        setTimeout(() => $message.fadeOut(() => $message.remove()), 2000);
    }
});
