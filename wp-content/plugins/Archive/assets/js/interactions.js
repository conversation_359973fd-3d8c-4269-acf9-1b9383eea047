/**
 * SoloYLibre Gallery Interactions JavaScript
 * Handles user reactions, likes, and engagement features
 */

(function($) {
    'use strict';

    // Interactions object
    var SoloYLibreInteractions = {
        
        // Initialize interactions
        init: function() {
            this.bindEvents();
            this.loadReactions();
        },
        
        // Bind events
        bindEvents: function() {
            $(document).on('click', '.reaction-btn', this.handleReactionClick);
            $(document).on('click', '.like-btn', this.handleLikeClick);
            $(document).on('click', '.reactions-toggle', this.toggleReactionsPanel);
            $(document).on('click', '.reaction-option', this.selectReaction);
            $(document).on('mouseenter', '.photo-item, .photo-card', this.showReactionButton);
            $(document).on('mouseleave', '.photo-item, .photo-card', this.hideReactionButton);
        },
        
        // Load reactions for all photos
        loadReactions: function() {
            $('.soloylibre-photo-item, .soloylibre-photo-card').each(function() {
                var photoId = $(this).data('photo-id');
                if (photoId) {
                    SoloYLibreInteractions.loadPhotoReactions(photoId);
                }
            });
        },
        
        // Load reactions for specific photo
        loadPhotoReactions: function(photoId) {
            $.ajax({
                url: soloylibre_interactions.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_photo_reactions',
                    nonce: soloylibre_interactions.nonce,
                    photo_id: photoId
                },
                success: function(response) {
                    if (response.success) {
                        SoloYLibreInteractions.updateReactionsDisplay(photoId, response.data);
                    }
                }
            });
        },
        
        // Handle reaction button click
        handleReactionClick: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $btn = $(this);
            var photoId = $btn.closest('[data-photo-id]').data('photo-id');
            var reactionType = $btn.data('reaction') || 'like';
            
            if (!photoId) return;
            
            // Check if user is logged in
            if (!soloylibre_interactions.user_id && reactionType !== 'like') {
                alert(soloylibre_interactions.strings.login_required);
                return;
            }
            
            SoloYLibreInteractions.addReaction(photoId, reactionType);
        },
        
        // Handle like button click (simplified)
        handleLikeClick: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $btn = $(this);
            var photoId = $btn.closest('[data-photo-id]').data('photo-id');
            
            if (!photoId) return;
            
            SoloYLibreInteractions.addReaction(photoId, 'like');
        },
        
        // Add reaction to photo
        addReaction: function(photoId, reactionType) {
            // Optimistic UI update
            this.animateReaction(photoId, reactionType);
            
            $.ajax({
                url: soloylibre_interactions.ajax_url,
                type: 'POST',
                data: {
                    action: 'add_photo_reaction',
                    nonce: soloylibre_interactions.nonce,
                    photo_id: photoId,
                    reaction_type: reactionType
                },
                success: function(response) {
                    if (response.success) {
                        SoloYLibreInteractions.updateReactionsDisplay(photoId, response.data);
                        SoloYLibreInteractions.showReactionFeedback(reactionType);
                    } else {
                        // If already reacted, try to remove reaction
                        if (response.data && response.data.includes('already_reacted')) {
                            SoloYLibreInteractions.removeReaction(photoId, reactionType);
                        } else {
                            alert(soloylibre_interactions.strings.error + ': ' + response.data);
                        }
                    }
                },
                error: function() {
                    alert(soloylibre_interactions.strings.error);
                }
            });
        },
        
        // Remove reaction from photo
        removeReaction: function(photoId, reactionType) {
            $.ajax({
                url: soloylibre_interactions.ajax_url,
                type: 'POST',
                data: {
                    action: 'remove_photo_reaction',
                    nonce: soloylibre_interactions.nonce,
                    photo_id: photoId,
                    reaction_type: reactionType
                },
                success: function(response) {
                    if (response.success) {
                        SoloYLibreInteractions.updateReactionsDisplay(photoId, response.data);
                    }
                }
            });
        },
        
        // Update reactions display
        updateReactionsDisplay: function(photoId, reactionsData) {
            var $photoElement = $('[data-photo-id="' + photoId + '"]');
            var $reactionsContainer = $photoElement.find('.reactions-display');
            
            // Create reactions container if it doesn't exist
            if ($reactionsContainer.length === 0) {
                $reactionsContainer = $('<div class="reactions-display"></div>');
                $photoElement.find('.photo-overlay, .photo-info').append($reactionsContainer);
            }
            
            // Clear existing reactions
            $reactionsContainer.empty();
            
            if (reactionsData.total > 0) {
                var reactionsHtml = '<div class="reactions-summary">';
                
                // Show top 3 reactions
                var reactionCount = 0;
                for (var reactionType in reactionsData.reactions) {
                    if (reactionCount >= 3) break;
                    
                    var reaction = reactionsData.reactions[reactionType];
                    reactionsHtml += '<span class="reaction-item" data-reaction="' + reactionType + '">';
                    reactionsHtml += '<span class="reaction-emoji">' + reaction.emoji + '</span>';
                    reactionsHtml += '<span class="reaction-count">' + reaction.count + '</span>';
                    reactionsHtml += '</span>';
                    
                    reactionCount++;
                }
                
                reactionsHtml += '<span class="total-reactions">' + reactionsData.total + '</span>';
                reactionsHtml += '</div>';
                
                $reactionsContainer.html(reactionsHtml);
                $reactionsContainer.show();
            } else {
                $reactionsContainer.hide();
            }
            
            // Update like button count
            var likeCount = reactionsData.reactions.like ? reactionsData.reactions.like.count : 0;
            $photoElement.find('.like-btn').each(function() {
                var $btn = $(this);
                var currentText = $btn.text();
                var newText = currentText.replace(/\d+/, likeCount);
                if (newText === currentText && likeCount > 0) {
                    newText = '❤️ ' + likeCount;
                }
                $btn.text(newText);
            });
        },
        
        // Animate reaction
        animateReaction: function(photoId, reactionType) {
            var $photoElement = $('[data-photo-id="' + photoId + '"]');
            var reaction = soloylibre_interactions.reactions[reactionType];
            
            if (!reaction) return;
            
            // Create floating reaction animation
            var $floatingReaction = $('<div class="floating-reaction">' + reaction.emoji + '</div>');
            $floatingReaction.css({
                position: 'absolute',
                fontSize: '24px',
                zIndex: 1000,
                pointerEvents: 'none',
                animation: 'floatUp 1.5s ease-out forwards'
            });
            
            // Position relative to photo
            var photoOffset = $photoElement.offset();
            var randomX = Math.random() * 100 - 50; // Random horizontal offset
            
            $floatingReaction.css({
                left: photoOffset.left + ($photoElement.width() / 2) + randomX,
                top: photoOffset.top + ($photoElement.height() / 2)
            });
            
            $('body').append($floatingReaction);
            
            // Remove after animation
            setTimeout(function() {
                $floatingReaction.remove();
            }, 1500);
            
            // Add pulse effect to photo
            $photoElement.addClass('reaction-pulse');
            setTimeout(function() {
                $photoElement.removeClass('reaction-pulse');
            }, 600);
        },
        
        // Show reaction feedback
        showReactionFeedback: function(reactionType) {
            var reaction = soloylibre_interactions.reactions[reactionType];
            if (!reaction) return;
            
            // Create feedback notification
            var $feedback = $('<div class="reaction-feedback">' + reaction.emoji + ' ' + reaction.label + '</div>');
            $feedback.css({
                position: 'fixed',
                top: '20px',
                right: '20px',
                background: 'rgba(0, 0, 0, 0.8)',
                color: 'white',
                padding: '10px 20px',
                borderRadius: '20px',
                zIndex: 10000,
                fontSize: '14px',
                fontWeight: 'bold'
            });
            
            $('body').append($feedback);
            
            // Animate in
            $feedback.css({
                opacity: 0,
                transform: 'translateX(100px)'
            }).animate({
                opacity: 1,
                transform: 'translateX(0)'
            }, 300);
            
            // Remove after delay
            setTimeout(function() {
                $feedback.animate({
                    opacity: 0,
                    transform: 'translateX(100px)'
                }, 300, function() {
                    $feedback.remove();
                });
            }, 2000);
        },
        
        // Toggle reactions panel
        toggleReactionsPanel: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $btn = $(this);
            var $panel = $btn.siblings('.reactions-panel');
            
            if ($panel.length === 0) {
                // Create reactions panel
                $panel = SoloYLibreInteractions.createReactionsPanel();
                $btn.after($panel);
            }
            
            $panel.toggle();
        },
        
        // Create reactions panel
        createReactionsPanel: function() {
            var $panel = $('<div class="reactions-panel"></div>');
            
            for (var reactionType in soloylibre_interactions.reactions) {
                var reaction = soloylibre_interactions.reactions[reactionType];
                var $option = $('<button class="reaction-option" data-reaction="' + reactionType + '">');
                $option.html('<span class="reaction-emoji">' + reaction.emoji + '</span><span class="reaction-label">' + reaction.label + '</span>');
                $panel.append($option);
            }
            
            return $panel;
        },
        
        // Select reaction from panel
        selectReaction: function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $option = $(this);
            var reactionType = $option.data('reaction');
            var photoId = $option.closest('[data-photo-id]').data('photo-id');
            
            if (photoId && reactionType) {
                SoloYLibreInteractions.addReaction(photoId, reactionType);
            }
            
            // Hide panel
            $option.closest('.reactions-panel').hide();
        },
        
        // Show reaction button on hover
        showReactionButton: function() {
            var $element = $(this);
            var $reactionBtn = $element.find('.reaction-btn, .like-btn');
            
            if ($reactionBtn.length === 0) {
                // Create reaction button
                $reactionBtn = $('<button class="reaction-btn quick-like" data-reaction="like">❤️</button>');
                $reactionBtn.css({
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    background: 'rgba(255, 255, 255, 0.9)',
                    border: 'none',
                    borderRadius: '50%',
                    width: '40px',
                    height: '40px',
                    fontSize: '18px',
                    cursor: 'pointer',
                    zIndex: 100,
                    opacity: 0,
                    transition: 'all 0.3s ease'
                });
                
                $element.css('position', 'relative').append($reactionBtn);
            }
            
            $reactionBtn.css('opacity', 1);
        },
        
        // Hide reaction button
        hideReactionButton: function() {
            var $element = $(this);
            var $reactionBtn = $element.find('.quick-like');
            
            $reactionBtn.css('opacity', 0);
        }
    };
    
    // CSS animations
    var animationCSS = `
        @keyframes floatUp {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            50% {
                opacity: 1;
                transform: translateY(-30px) scale(1.2);
            }
            100% {
                opacity: 0;
                transform: translateY(-60px) scale(0.8);
            }
        }
        
        .reaction-pulse {
            animation: reactionPulse 0.6s ease-out;
        }
        
        @keyframes reactionPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .reactions-display {
            margin-top: 10px;
        }
        
        .reactions-summary {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .reaction-item {
            display: flex;
            align-items: center;
            gap: 3px;
            background: rgba(255, 255, 255, 0.1);
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .reaction-emoji {
            font-size: 14px;
        }
        
        .reaction-count {
            font-weight: bold;
        }
        
        .total-reactions {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-left: 5px;
        }
        
        .reactions-panel {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 10px;
            display: none;
            z-index: 1000;
            min-width: 200px;
        }
        
        .reaction-option {
            display: flex;
            align-items: center;
            gap: 8px;
            width: 100%;
            background: transparent;
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s ease;
            margin-bottom: 5px;
        }
        
        .reaction-option:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .reaction-option:last-child {
            margin-bottom: 0;
        }
        
        .reaction-label {
            font-size: 12px;
        }
        
        .floating-reaction {
            user-select: none;
            pointer-events: none;
        }
        
        .reaction-feedback {
            animation: slideInRight 0.3s ease-out;
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    `;
    
    // Inject CSS
    $('<style>').text(animationCSS).appendTo('head');
    
    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof soloylibre_interactions !== 'undefined') {
            SoloYLibreInteractions.init();
        }
    });
    
    // Make interactions object globally available
    window.SoloYLibreInteractions = SoloYLibreInteractions;
    
})(jQuery);
