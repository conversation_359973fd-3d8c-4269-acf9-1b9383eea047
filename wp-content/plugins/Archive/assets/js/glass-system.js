/**
 * Glass System JavaScript - SoloYLibre Gallery Plugin
 * Modern glassmorphism interface without overlays
 * Developed by JEYKO AI for Jose L <PERSON> (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Global variables
    window.soloylibreGlassSystem = {
        currentPage: '',
        selectedPhotos: [],
        isLoading: false,
        
        // Initialize the system
        init: function() {
            this.currentPage = this.getCurrentPage();
            this.bindEvents();
            this.loadInitialData();
            console.log('🎨 SoloYLibre Glass System initialized for:', this.currentPage);
        },
        
        // Get current page from URL
        getCurrentPage: function() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('page') || 'soloylibre-dashboard';
        },
        
        // Bind global events
        bindEvents: function() {
            // Global notification close buttons
            $(document).on('click', '.glass-notification button', function() {
                $(this).closest('.glass-notification').fadeOut(300, function() {
                    $(this).remove();
                });
            });
            
            // Auto-hide notifications after 5 seconds
            $(document).on('DOMNodeInserted', '.glass-notification', function() {
                const $notification = $(this);
                setTimeout(() => {
                    if ($notification.is(':visible')) {
                        $notification.fadeOut(300, function() {
                            $(this).remove();
                        });
                    }
                }, 5000);
            });
            
            // Photo selection in grids
            $(document).on('click', '.glass-photo-item[data-photo-id]', function(e) {
                if (!$(e.target).is('button') && !$(e.target).hasClass('like-btn')) {
                    const photoId = parseInt($(this).data('photo-id'));
                    this.togglePhotoSelection(photoId, $(this));
                }
            }.bind(this));

            // Like button clicks
            $(document).on('click', '.glass-like-btn', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.handleLikeClick($(e.target));
            }.bind(this));
            
            // Keyboard shortcuts
            $(document).on('keydown', function(e) {
                // Ctrl+A to select all photos
                if (e.ctrlKey && e.key === 'a' && $('.glass-photo-grid').length) {
                    e.preventDefault();
                    this.selectAllVisiblePhotos();
                }
                
                // Escape to clear selection
                if (e.key === 'Escape') {
                    this.clearSelection();
                }
            }.bind(this));
        },
        
        // Load initial data based on current page
        loadInitialData: function() {
            switch (this.currentPage) {
                case 'soloylibre-dashboard':
                    this.loadDashboardStats();
                    break;
                case 'soloylibre-stats':
                    this.loadFullStats();
                    break;
                case 'soloylibre-photo-manager':
                    // Photos loaded on demand
                    break;
                default:
                    console.log('📄 Page loaded:', this.currentPage);
            }
        },
        
        // Load dashboard statistics
        loadDashboardStats: function() {
            this.makeAjaxCall('glass_get_stats', {
                type: 'dashboard'
            }, function(response) {
                if (response.success) {
                    $('#total-photos').text(response.data.total_photos);
                    $('#published-photos').text(response.data.published_photos);
                    $('#private-photos').text(response.data.private_photos);
                    $('#total-views').text(response.data.total_views);
                }
            });
        },
        
        // Load full statistics
        loadFullStats: function() {
            this.makeAjaxCall('glass_get_stats', {
                type: 'full'
            }, function(response) {
                if (response.success) {
                    const data = response.data;
                    $('#stat-total').text(data.total_photos);
                    $('#stat-published').text(data.published_photos);
                    $('#stat-private').text(data.private_photos);
                    $('#stat-unwanted').text(data.unwanted_photos);
                    $('#stat-available').text(data.available_photos);
                    $('#stat-views').text(data.total_views);
                    $('#publication-rate').text(data.publication_rate);
                    $('#avg-photos-per-post').text(data.avg_photos_per_post);
                    $('#total-posts').text(data.total_posts);
                }
            });
        },
        
        // Toggle photo selection
        togglePhotoSelection: function(photoId, $element) {
            const index = this.selectedPhotos.indexOf(photoId);
            
            if (index > -1) {
                // Remove from selection
                this.selectedPhotos.splice(index, 1);
                $element.removeClass('selected');
            } else {
                // Add to selection
                this.selectedPhotos.push(photoId);
                $element.addClass('selected');
            }
            
            this.updateSelectionUI();
        },
        
        // Select all visible photos
        selectAllVisiblePhotos: function() {
            const $visiblePhotos = $('.glass-photo-item[data-photo-id]:visible');
            
            $visiblePhotos.each((index, element) => {
                const photoId = parseInt($(element).data('photo-id'));
                if (!this.selectedPhotos.includes(photoId)) {
                    this.selectedPhotos.push(photoId);
                    $(element).addClass('selected');
                }
            });
            
            this.updateSelectionUI();
            this.showNotification(`✅ ${$visiblePhotos.length} fotos seleccionadas`, 'success');
        },
        
        // Clear selection
        clearSelection: function() {
            this.selectedPhotos = [];
            $('.glass-photo-item').removeClass('selected');
            this.updateSelectionUI();
            this.showNotification('❌ Selección limpiada', 'info');
        },

        // Handle like button clicks
        handleLikeClick: function($button) {
            const photoId = $button.data('photo-id');
            const $counter = $button.find('.like-count');

            if ($button.hasClass('processing')) {
                return;
            }

            $button.addClass('processing');

            this.makeAjaxCall('glass_add_like', {
                photo_id: photoId
            }, (response) => {
                if (response.success) {
                    // Update counter
                    $counter.text(response.data.like_count);

                    // Add animation
                    $button.addClass('liked heart-animation');
                    setTimeout(() => {
                        $button.removeClass('heart-animation');
                    }, 600);

                    // Show success message
                    this.showNotification('💖 Like agregado!', 'success');
                } else {
                    this.showNotification('❌ Error agregando like', 'error');
                }
            }, () => {
                this.showNotification('❌ Error de conexión', 'error');
            });

            setTimeout(() => {
                $button.removeClass('processing');
            }, 1000);
        },
        
        // Update selection UI
        updateSelectionUI: function() {
            const count = this.selectedPhotos.length;
            $('#selected-count').text(count);
            
            // Update selection indicators
            $('.selection-count').text(count);
            $('.selection-indicator').toggle(count > 0);
        },
        
        // Show notification
        showNotification: function(message, type = 'info', container = null) {
            const $container = container ? $(container) : $('.glass-container').first();
            
            const notification = $(`
                <div class="glass-notification ${type}">
                    <span>${message}</span>
                    <button type="button" style="background: none; border: none; color: white; cursor: pointer; font-size: 18px; margin-left: 15px;">×</button>
                </div>
            `);
            
            // Find or create notifications container
            let $notificationsContainer = $container.find('.glass-notifications');
            if (!$notificationsContainer.length) {
                $notificationsContainer = $('<div class="glass-notifications"></div>');
                $container.prepend($notificationsContainer);
            }
            
            $notificationsContainer.prepend(notification);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        },
        
        // Make AJAX call with error handling
        makeAjaxCall: function(action, data = {}, successCallback = null, errorCallback = null) {
            if (this.isLoading) {
                console.log('⏳ AJAX call blocked - already loading');
                return;
            }
            
            this.isLoading = true;
            
            const ajaxData = {
                action: action,
                nonce: soloylibreGlass.nonce,
                ...data
            };
            
            $.post(soloylibreGlass.ajax_url, ajaxData)
                .done((response) => {
                    if (response.success) {
                        if (successCallback) {
                            successCallback(response);
                        }
                    } else {
                        const errorMsg = response.data || 'Error desconocido';
                        this.showNotification(`❌ Error: ${errorMsg}`, 'error');
                        
                        if (errorCallback) {
                            errorCallback(response);
                        }
                    }
                })
                .fail((xhr, status, error) => {
                    const errorMsg = `Error de conexión: ${error}`;
                    this.showNotification(`❌ ${errorMsg}`, 'error');
                    console.error('AJAX Error:', xhr, status, error);
                    
                    if (errorCallback) {
                        errorCallback({success: false, data: errorMsg});
                    }
                })
                .always(() => {
                    this.isLoading = false;
                });
        },
        
        // Load photos with filters
        loadPhotos: function(options = {}) {
            const defaultOptions = {
                count: 50,
                filter: 'all',
                sort: 'date_desc',
                type: 'manager',
                container: '#photos-grid'
            };
            
            const settings = {...defaultOptions, ...options};
            
            $(settings.container).html('<div class="glass-loading">⏳ Cargando fotos...</div>');
            
            this.makeAjaxCall('glass_load_photos', settings, (response) => {
                if (response.success) {
                    this.displayPhotos(response.data.photos, settings.container);
                    
                    // Update photo count if element exists
                    if ($('#photo-count').length) {
                        $('#photo-count').text(response.data.photos.length);
                    }
                } else {
                    $(settings.container).html('❌ Error cargando fotos');
                }
            });
        },
        
        // Display photos in grid
        displayPhotos: function(photos, container) {
            let html = '';
            
            photos.forEach(photo => {
                const isSelected = this.selectedPhotos.includes(photo.id);
                const selectedClass = isSelected ? 'selected' : '';
                const selectedIndicator = isSelected ? 
                    '<div class="selection-indicator">✓</div>' : '';
                
                html += `
                    <div class="glass-photo-item ${selectedClass}" data-photo-id="${photo.id}">
                        <img src="${photo.thumbnail}" alt="${photo.title}" loading="lazy">
                        <div class="glass-photo-info">
                            <div class="photo-title">${photo.title}</div>
                            <div class="photo-status">${photo.status || 'Disponible'}</div>
                            ${photo.ai_score ? `<div class="ai-score">AI: ${photo.ai_score}</div>` : ''}
                            <div class="photo-actions">
                                <button class="glass-like-btn" data-photo-id="${photo.id}">
                                    ❤️ <span class="like-count">${photo.like_count || 0}</span>
                                </button>
                            </div>
                        </div>
                        ${selectedIndicator}
                    </div>
                `;
            });
            
            $(container).html(html);
            this.updateSelectionUI();
        },
        
        // Create post with selected photos
        createPost: function(options = {}) {
            if (this.selectedPhotos.length === 0) {
                this.showNotification('❌ Selecciona fotos primero', 'error');
                return;
            }
            
            const defaultOptions = {
                type: 'quick',
                title: 'AutoPost - JoseTusabe Photography',
                content: 'Colección fotográfica de JoseTusabe Photography'
            };
            
            const settings = {...defaultOptions, ...options};
            
            this.showNotification('⏳ Creando post...', 'info');
            
            const action = settings.type === 'advanced' ? 'glass_create_advanced_post' : 'glass_create_quick_post';
            
            this.makeAjaxCall(action, {
                photo_ids: this.selectedPhotos,
                ...settings
            }, (response) => {
                if (response.success) {
                    this.showNotification('✅ Post creado exitosamente!', 'success');
                    
                    // Open post in new tab after delay
                    setTimeout(() => {
                        window.open(response.data.edit_url, '_blank');
                    }, 1000);
                    
                    // Clear selection
                    this.clearSelection();
                }
            });
        },
        
        // Utility: Format number with commas
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },
        
        // Utility: Get photographer info
        getPhotographerInfo: function() {
            return soloylibreGlass.photographer || {
                name: 'Jose L Encarnacion',
                alias: 'JoseTusabe',
                brand: 'JoseTusabe Photography'
            };
        }
    };
    
    // Initialize the system
    window.soloylibreGlassSystem.init();
    
    // Add CSS for selected photos
    $('<style>').text(`
        .glass-photo-item.selected {
            background: rgba(40, 167, 69, 0.3) !important;
            border: 2px solid rgba(40, 167, 69, 0.8) !important;
            transform: scale(1.05);
        }
        
        .selection-indicator {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(40, 167, 69, 0.9);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .glass-photo-item {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .glass-loading {
            text-align: center;
            padding: 40px;
            font-size: 1.2em;
            opacity: 0.8;
        }
        
        .glass-notifications {
            position: relative;
            z-index: 1000;
        }
        
        .ai-score {
            font-size: 10px;
            opacity: 0.7;
            color: #00ff88;
        }
        
        .photo-status {
            font-size: 10px;
            opacity: 0.8;
        }
        
        .photo-title {
            font-size: 11px;
            font-weight: 500;
        }

        .glass-like-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 10px;
            font-weight: 500;
            margin-top: 5px;
            display: inline-flex;
            align-items: center;
            gap: 3px;
        }

        .glass-like-btn:hover {
            background: linear-gradient(135deg, #ff5252, #d32f2f);
            transform: scale(1.05);
        }

        .glass-like-btn.liked {
            background: linear-gradient(135deg, #e91e63, #ad1457);
        }

        .glass-like-btn.processing {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .glass-like-btn.heart-animation {
            animation: heartBeat 0.6s ease;
        }

        @keyframes heartBeat {
            0% { transform: scale(1); }
            25% { transform: scale(1.3); }
            50% { transform: scale(1.1); }
            75% { transform: scale(1.4); }
            100% { transform: scale(1); }
        }

        .photo-actions {
            margin-top: 5px;
            text-align: center;
        }
    `).appendTo('head');
});

// Global helper functions for backward compatibility
function showGlassNotification(message, type) {
    if (window.soloylibreGlassSystem) {
        window.soloylibreGlassSystem.showNotification(message, type);
    }
}

function loadGlassPhotos(options) {
    if (window.soloylibreGlassSystem) {
        window.soloylibreGlassSystem.loadPhotos(options);
    }
}

function createGlassPost(options) {
    if (window.soloylibreGlassSystem) {
        window.soloylibreGlassSystem.createPost(options);
    }
}
