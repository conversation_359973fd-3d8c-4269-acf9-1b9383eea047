/**
 * SoloYLibre Simple Wizard JavaScript
 * One-page wizard functionality
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

(function($) {
    'use strict';
    
    let selectedPhotos = [];
    let mediaFrame;
    
    $(document).ready(function() {
        initializeWizard();
    });
    
    /**
     * Initialize wizard functionality
     */
    function initializeWizard() {
        bindEvents();
        setupMediaUploader();
    }
    
    /**
     * Bind event handlers
     */
    function bindEvents() {
        // Photo selection
        $('#select-photos-btn').on('click', openMediaUploader);
        
        // Form submission
        $('#simple-wizard-form').on('submit', handleFormSubmit);
        
        // Debug button
        $('#debug-btn').on('click', toggleDebugPanel);
        $('#close-debug').on('click', closeDebugPanel);
        
        // Success actions
        $('#create-another-btn').on('click', resetForm);
        
        // Photo removal
        $(document).on('click', '.photo-remove', removePhoto);
        
        // Signature toggle
        $('#include_signature').on('change', toggleSignaturePreview);
    }
    
    /**
     * Setup WordPress media uploader
     */
    function setupMediaUploader() {
        if (typeof wp !== 'undefined' && wp.media) {
            mediaFrame = wp.media({
                title: 'Seleccionar Fotos para Galería',
                button: {
                    text: 'Usar estas fotos'
                },
                multiple: true,
                library: {
                    type: 'image'
                }
            });
            
            mediaFrame.on('select', function() {
                const selection = mediaFrame.state().get('selection');
                selectedPhotos = [];
                
                selection.map(function(attachment) {
                    attachment = attachment.toJSON();
                    selectedPhotos.push({
                        id: attachment.id,
                        url: attachment.url,
                        thumbnail: attachment.sizes && attachment.sizes.thumbnail ? 
                                 attachment.sizes.thumbnail.url : attachment.url,
                        title: attachment.title,
                        alt: attachment.alt
                    });
                });
                
                displaySelectedPhotos();
            });
        }
    }
    
    /**
     * Open media uploader
     */
    function openMediaUploader(e) {
        e.preventDefault();
        
        if (mediaFrame) {
            mediaFrame.open();
        } else {
            showNotification('Error: Media uploader no disponible', 'error');
        }
    }
    
    /**
     * Display selected photos
     */
    function displaySelectedPhotos() {
        const $container = $('#photos-container');
        const $preview = $('#selected-photos-preview');
        const $count = $('#photo-count');
        
        $container.empty();
        
        if (selectedPhotos.length === 0) {
            $preview.hide();
            return;
        }
        
        $count.text(selectedPhotos.length);
        $preview.show();
        
        selectedPhotos.forEach(function(photo, index) {
            const $photoItem = $(`
                <div class="photo-item" data-photo-id="${photo.id}" data-index="${index}">
                    <img src="${photo.thumbnail}" alt="${photo.alt || photo.title}" loading="lazy">
                    <button type="button" class="photo-remove" data-index="${index}">×</button>
                </div>
            `);
            
            $container.append($photoItem);
        });
        
        // Animate new photos
        $('.photo-item').each(function(index) {
            $(this).css({
                opacity: 0,
                transform: 'scale(0.8)'
            }).delay(index * 100).animate({
                opacity: 1
            }, 300).css('transform', 'scale(1)');
        });
    }
    
    /**
     * Remove photo from selection
     */
    function removePhoto(e) {
        e.preventDefault();
        const index = parseInt($(this).data('index'));
        
        selectedPhotos.splice(index, 1);
        displaySelectedPhotos();
        
        showNotification('Foto removida de la selección', 'info');
    }
    
    /**
     * Handle form submission
     */
    function handleFormSubmit(e) {
        e.preventDefault();
        
        // Validate form
        if (!validateForm()) {
            return;
        }
        
        // Show creation status
        showCreationStatus();
        
        // Collect form data
        const formData = {
            action: 'simple_wizard_create_gallery',
            nonce: soloylibre_simple_wizard.nonce,
            gallery_title: $('#gallery_title').val(),
            gallery_description: $('#gallery_description').val(),
            gallery_style: $('#gallery_style').val(),
            gallery_category: $('#gallery_category').val(),
            photo_ids: selectedPhotos.map(photo => photo.id),
            include_signature: $('#include_signature').is(':checked'),
            enable_lightbox: $('#enable_lightbox').is(':checked'),
            show_metadata: $('#show_metadata').is(':checked'),
            post_status: $('#post_status').val()
        };
        
        // Submit via AJAX
        $.ajax({
            url: soloylibre_simple_wizard.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                hideCreationStatus();
                
                if (response.success) {
                    showCreationSuccess(response.data);
                } else {
                    showNotification('Error: ' + (response.data || 'Error desconocido'), 'error');
                }
            },
            error: function() {
                hideCreationStatus();
                showNotification('Error de conexión al crear la galería', 'error');
            }
        });
    }
    
    /**
     * Validate form
     */
    function validateForm() {
        const title = $('#gallery_title').val().trim();
        
        if (!title) {
            showNotification('Por favor ingresa un título para la galería', 'warning');
            $('#gallery_title').focus();
            return false;
        }
        
        if (selectedPhotos.length === 0) {
            showNotification('Por favor selecciona al menos una foto', 'warning');
            $('#select-photos-btn').focus();
            return false;
        }
        
        return true;
    }
    
    /**
     * Show creation status
     */
    function showCreationStatus() {
        $('#create-gallery-btn').prop('disabled', true);
        $('#creation-status').show();
        $('#creation-success').hide();
        
        // Scroll to status
        $('html, body').animate({
            scrollTop: $('#creation-status').offset().top - 100
        }, 500);
    }
    
    /**
     * Hide creation status
     */
    function hideCreationStatus() {
        $('#create-gallery-btn').prop('disabled', false);
        $('#creation-status').hide();
    }
    
    /**
     * Show creation success
     */
    function showCreationSuccess(data) {
        $('#creation-success').show();
        
        // Update links
        $('#view-gallery-link').attr('href', data.post_url);
        $('#edit-post-link').attr('href', data.edit_url);
        
        // Show success notification
        showNotification(`¡Galería "${data.gallery_title}" creada exitosamente con ${data.photo_count} fotos!`, 'success');
        
        // Scroll to success message
        $('html, body').animate({
            scrollTop: $('#creation-success').offset().top - 100
        }, 500);
    }
    
    /**
     * Reset form for new gallery
     */
    function resetForm() {
        // Reset form fields
        $('#simple-wizard-form')[0].reset();
        
        // Reset photo selection
        selectedPhotos = [];
        displaySelectedPhotos();
        
        // Hide status messages
        $('#creation-status').hide();
        $('#creation-success').hide();
        
        // Scroll to top
        $('html, body').animate({
            scrollTop: 0
        }, 500);
        
        showNotification('Formulario reiniciado. ¡Crea una nueva galería!', 'info');
    }
    
    /**
     * Toggle signature preview
     */
    function toggleSignaturePreview() {
        const $preview = $('.signature-preview');
        
        if ($(this).is(':checked')) {
            $preview.slideDown(300);
        } else {
            $preview.slideUp(300);
        }
    }
    
    /**
     * Toggle debug panel
     */
    function toggleDebugPanel() {
        const $panel = $('#debug-panel');
        
        if ($panel.hasClass('open')) {
            closeDebugPanel();
        } else {
            openDebugPanel();
        }
    }
    
    /**
     * Open debug panel
     */
    function openDebugPanel() {
        const $panel = $('#debug-panel');
        $panel.addClass('open');
        
        // Load debug info
        loadDebugInfo();
    }
    
    /**
     * Close debug panel
     */
    function closeDebugPanel() {
        const $panel = $('#debug-panel');
        $panel.removeClass('open');
    }
    
    /**
     * Load debug information
     */
    function loadDebugInfo() {
        const $loading = $('.debug-loading');
        const $info = $('#debug-info');
        
        $loading.show();
        $info.hide();
        
        $.ajax({
            url: soloylibre_simple_wizard.ajax_url,
            type: 'POST',
            data: {
                action: 'simple_wizard_debug',
                nonce: soloylibre_simple_wizard.nonce
            },
            success: function(response) {
                $loading.hide();
                
                if (response.success) {
                    displayDebugInfo(response.data);
                    $info.show();
                } else {
                    $info.html('<p style="color: red;">Error al cargar información de debug</p>').show();
                }
            },
            error: function() {
                $loading.hide();
                $info.html('<p style="color: red;">Error de conexión</p>').show();
            }
        });
    }
    
    /**
     * Display debug information
     */
    function displayDebugInfo(debug) {
        const $info = $('#debug-info');
        let html = '';
        
        // WordPress Info
        html += createDebugSection('WordPress', debug.wordpress);
        
        // Plugin Info
        html += createDebugSection('Plugin', debug.plugin);
        
        // Classes Status
        html += createDebugSection('Classes', debug.classes);
        
        // Database
        html += createDebugSection('Database Tables', debug.database);
        
        // Post Types
        html += createDebugSection('Post Types', debug.post_types);
        
        // Options
        html += createDebugSection('Options', debug.options);
        
        // PHP Info
        html += createDebugSection('PHP', debug.php);
        
        // Permissions
        html += createDebugSection('File Permissions', debug.permissions);
        
        // Recent Errors
        if (debug.errors && debug.errors.length > 0) {
            html += '<div class="debug-section">';
            html += '<div class="debug-section-header">Recent Errors</div>';
            html += '<div class="debug-section-content">';
            debug.errors.forEach(function(error) {
                html += '<div style="color: red; margin-bottom: 5px; font-size: 0.8rem;">' + escapeHtml(error) + '</div>';
            });
            html += '</div></div>';
        }
        
        $info.html(html);
    }
    
    /**
     * Create debug section HTML
     */
    function createDebugSection(title, data) {
        let html = '<div class="debug-section">';
        html += '<div class="debug-section-header">' + title + '</div>';
        html += '<div class="debug-section-content">';
        
        if (typeof data === 'object') {
            for (const key in data) {
                const value = data[key];
                let valueClass = '';
                let displayValue = value;
                
                if (typeof value === 'boolean') {
                    valueClass = value ? 'true' : 'false';
                    displayValue = value ? 'Yes' : 'No';
                } else if (typeof value === 'object') {
                    displayValue = JSON.stringify(value, null, 2);
                }
                
                html += '<div class="debug-item">';
                html += '<span class="debug-key">' + escapeHtml(key) + ':</span>';
                html += '<span class="debug-value ' + valueClass + '">' + escapeHtml(displayValue) + '</span>';
                html += '</div>';
            }
        } else {
            html += '<div class="debug-item">';
            html += '<span class="debug-value">' + escapeHtml(data) + '</span>';
            html += '</div>';
        }
        
        html += '</div></div>';
        return html;
    }
    
    /**
     * Escape HTML
     */
    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        
        return String(text).replace(/[&<>"']/g, function(m) {
            return map[m];
        });
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        
        const $notification = $(`
            <div class="simple-wizard-notification simple-wizard-notification-${type}">
                <span class="notification-icon">${icons[type]}</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close">×</button>
            </div>
        `);
        
        // Add notification styles if not already added
        if (!$('#simple-wizard-notification-styles').length) {
            $('head').append(`
                <style id="simple-wizard-notification-styles">
                    .simple-wizard-notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: white;
                        border-radius: 10px;
                        padding: 15px 20px;
                        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                        z-index: 999999;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        min-width: 300px;
                        max-width: 500px;
                        border-left: 4px solid #007cba;
                        animation: slideInRight 0.3s ease;
                    }
                    
                    .simple-wizard-notification-success { border-left-color: #28a745; }
                    .simple-wizard-notification-error { border-left-color: #dc3545; }
                    .simple-wizard-notification-warning { border-left-color: #ffc107; }
                    
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    
                    .notification-icon { font-size: 1.2rem; }
                    .notification-message { flex: 1; font-weight: 500; }
                    .notification-close {
                        background: none;
                        border: none;
                        font-size: 1.2rem;
                        cursor: pointer;
                        color: #666;
                        padding: 0;
                        width: 20px;
                        height: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        transition: all 0.3s ease;
                    }
                    .notification-close:hover {
                        background: #f0f0f0;
                        color: #333;
                    }
                </style>
            `);
        }
        
        $('body').append($notification);
        
        // Auto-remove after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // Manual close
        $notification.find('.notification-close').on('click', function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        });
    }
    
})(jQuery);
