<?php
/**
 * SoloYLibre Gallery Pro - User Interaction Test
 * Tests all user interactions and admin functionality
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

class SoloYLibre_User_Interaction_Test {
    
    private $test_results = array();
    private $admin_pages = array();
    private $ajax_endpoints = array();
    
    public function __construct() {
        $this->admin_pages = array(
            'soloylibre-main' => 'Dashboard Principal',
            'soloylibre-wizard' => 'Wizard Fullscreen',
            'soloylibre-statistics' => 'Estadísticas',
            'soloylibre-photographer-info' => 'Información del Fotógrafo',
            'soloylibre-gallery-styles' => 'Estilos de Galería',
            'soloylibre-tools' => 'Herramientas',
            'soloylibre-audit' => 'Audit del Sistema'
        );
        
        $this->ajax_endpoints = array(
            'wizard_get_photos' => 'Obtener fotos para wizard',
            'wizard_create_post' => 'Crear post desde wizard',
            'wizard_automatic_posts' => 'Posts automáticos',
            'soloylibre_get_all_photos_preview' => 'Vista previa de fotos',
            'soloylibre_create_advanced_auto_post' => 'Post automático avanzado',
            'soloylibre_complete_setup' => 'Completar configuración inicial',
            'soloylibre_reset_published_photos' => 'Reset fotos publicadas'
        );
    }
    
    /**
     * Run all interaction tests
     */
    public function run_tests() {
        $this->test_admin_pages();
        $this->test_ajax_endpoints();
        $this->test_shortcodes();
        $this->test_user_capabilities();
        $this->test_javascript_functionality();
        $this->test_css_loading();
        $this->test_database_interactions();
        $this->test_file_uploads();
        
        return array(
            'timestamp' => current_time('mysql'),
            'results' => $this->test_results,
            'summary' => $this->generate_summary()
        );
    }
    
    /**
     * Test admin pages accessibility
     */
    private function test_admin_pages() {
        foreach ($this->admin_pages as $page_slug => $page_name) {
            $page_url = admin_url("admin.php?page=$page_slug");
            
            // Check if page is registered
            global $menu, $submenu;
            $page_found = false;
            
            foreach ($menu as $menu_item) {
                if (strpos($menu_item[2], $page_slug) !== false) {
                    $page_found = true;
                    break;
                }
            }
            
            if (!$page_found && isset($submenu['soloylibre-main'])) {
                foreach ($submenu['soloylibre-main'] as $submenu_item) {
                    if ($submenu_item[2] === $page_slug) {
                        $page_found = true;
                        break;
                    }
                }
            }
            
            if ($page_found) {
                $this->test_results[] = array(
                    'category' => 'admin_pages',
                    'test' => $page_name,
                    'status' => 'success',
                    'message' => "Página accesible: $page_url"
                );
            } else {
                $this->test_results[] = array(
                    'category' => 'admin_pages',
                    'test' => $page_name,
                    'status' => 'error',
                    'message' => "Página no encontrada en menú: $page_slug"
                );
            }
        }
    }
    
    /**
     * Test AJAX endpoints
     */
    private function test_ajax_endpoints() {
        foreach ($this->ajax_endpoints as $action => $description) {
            $has_action = has_action("wp_ajax_$action");
            
            if ($has_action) {
                $this->test_results[] = array(
                    'category' => 'ajax_endpoints',
                    'test' => $description,
                    'status' => 'success',
                    'message' => "AJAX endpoint registrado: $action"
                );
                
                // Test nonce generation for this action
                $nonce = wp_create_nonce('soloylibre_wizard_nonce');
                if ($nonce) {
                    $this->test_results[] = array(
                        'category' => 'ajax_endpoints',
                        'test' => "$description - Nonce",
                        'status' => 'success',
                        'message' => "Nonce generado correctamente para $action"
                    );
                }
            } else {
                $this->test_results[] = array(
                    'category' => 'ajax_endpoints',
                    'test' => $description,
                    'status' => 'error',
                    'message' => "AJAX endpoint no registrado: $action"
                );
            }
        }
    }
    
    /**
     * Test shortcodes
     */
    private function test_shortcodes() {
        global $shortcode_tags;
        
        $required_shortcodes = array(
            'soloylibre_gallery' => 'Galería principal'
        );
        
        foreach ($required_shortcodes as $shortcode => $description) {
            if (isset($shortcode_tags[$shortcode])) {
                $this->test_results[] = array(
                    'category' => 'shortcodes',
                    'test' => $description,
                    'status' => 'success',
                    'message' => "Shortcode registrado: [$shortcode]"
                );
                
                // Test shortcode execution
                $test_atts = array('ids' => '1,2,3', 'style' => 'masonry');
                $output = do_shortcode("[{$shortcode} ids='1,2,3' style='masonry']");
                
                if (!empty($output) && strpos($output, 'soloylibre-gallery') !== false) {
                    $this->test_results[] = array(
                        'category' => 'shortcodes',
                        'test' => "$description - Ejecución",
                        'status' => 'success',
                        'message' => "Shortcode ejecuta correctamente"
                    );
                } else {
                    $this->test_results[] = array(
                        'category' => 'shortcodes',
                        'test' => "$description - Ejecución",
                        'status' => 'warning',
                        'message' => "Shortcode no genera output esperado"
                    );
                }
            } else {
                $this->test_results[] = array(
                    'category' => 'shortcodes',
                    'test' => $description,
                    'status' => 'error',
                    'message' => "Shortcode no registrado: [$shortcode]"
                );
            }
        }
    }
    
    /**
     * Test user capabilities
     */
    private function test_user_capabilities() {
        $current_user = wp_get_current_user();
        
        if ($current_user->ID == 0) {
            $this->test_results[] = array(
                'category' => 'user_capabilities',
                'test' => 'Usuario logueado',
                'status' => 'warning',
                'message' => 'No hay usuario logueado para test de capacidades'
            );
            return;
        }
        
        $required_capabilities = array(
            'edit_posts' => 'Editar posts',
            'upload_files' => 'Subir archivos',
            'manage_options' => 'Gestionar opciones (admin)'
        );
        
        foreach ($required_capabilities as $cap => $description) {
            if (current_user_can($cap)) {
                $this->test_results[] = array(
                    'category' => 'user_capabilities',
                    'test' => $description,
                    'status' => 'success',
                    'message' => "Usuario tiene capacidad: $cap"
                );
            } else {
                $this->test_results[] = array(
                    'category' => 'user_capabilities',
                    'test' => $description,
                    'status' => 'warning',
                    'message' => "Usuario carece de capacidad: $cap"
                );
            }
        }
    }
    
    /**
     * Test JavaScript functionality
     */
    private function test_javascript_functionality() {
        $js_files = array(
            'assets/js/fullscreen-wizard.js' => 'Wizard JavaScript',
            'assets/js/setup.js' => 'Setup JavaScript',
            'assets/js/admin.js' => 'Admin JavaScript'
        );
        
        $plugin_dir = plugin_dir_path(__FILE__);
        
        foreach ($js_files as $file => $description) {
            $file_path = $plugin_dir . $file;
            
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                // Check for jQuery
                if (strpos($content, '$') !== false || strpos($content, 'jQuery') !== false) {
                    $this->test_results[] = array(
                        'category' => 'javascript',
                        'test' => "$description - jQuery",
                        'status' => 'success',
                        'message' => "jQuery detectado en $file"
                    );
                }
                
                // Check for AJAX calls
                if (strpos($content, 'ajax') !== false || strpos($content, 'post(') !== false) {
                    $this->test_results[] = array(
                        'category' => 'javascript',
                        'test' => "$description - AJAX",
                        'status' => 'success',
                        'message' => "Funcionalidad AJAX detectada en $file"
                    );
                }
                
                // Check for event handlers
                if (strpos($content, 'addEventListener') !== false || strpos($content, '.on(') !== false) {
                    $this->test_results[] = array(
                        'category' => 'javascript',
                        'test' => "$description - Eventos",
                        'status' => 'success',
                        'message' => "Event handlers detectados en $file"
                    );
                }
            } else {
                $this->test_results[] = array(
                    'category' => 'javascript',
                    'test' => $description,
                    'status' => 'warning',
                    'message' => "Archivo JavaScript no encontrado: $file"
                );
            }
        }
    }
    
    /**
     * Test CSS loading
     */
    private function test_css_loading() {
        $css_files = array(
            'assets/css/fullscreen-wizard.css' => 'Wizard CSS',
            'assets/css/admin.css' => 'Admin CSS',
            'assets/css/gallery-styles.css' => 'Gallery CSS'
        );
        
        $plugin_dir = plugin_dir_path(__FILE__);
        
        foreach ($css_files as $file => $description) {
            $file_path = $plugin_dir . $file;
            
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                $size = filesize($file_path);
                
                if ($size > 1000) {
                    $this->test_results[] = array(
                        'category' => 'css',
                        'test' => $description,
                        'status' => 'success',
                        'message' => "CSS cargado correctamente: $file ($size bytes)"
                    );
                } else {
                    $this->test_results[] = array(
                        'category' => 'css',
                        'test' => $description,
                        'status' => 'warning',
                        'message' => "CSS muy pequeño: $file ($size bytes)"
                    );
                }
            } else {
                $this->test_results[] = array(
                    'category' => 'css',
                    'test' => $description,
                    'status' => 'warning',
                    'message' => "Archivo CSS no encontrado: $file"
                );
            }
        }
    }
    
    /**
     * Test database interactions
     */
    private function test_database_interactions() {
        global $wpdb;
        
        // Test basic database operations
        $test_option = 'soloylibre_test_' . time();
        $test_value = 'test_value_' . rand(1000, 9999);
        
        // Test option creation
        $result = update_option($test_option, $test_value);
        if ($result) {
            $this->test_results[] = array(
                'category' => 'database',
                'test' => 'Crear opción',
                'status' => 'success',
                'message' => 'Opción creada correctamente en base de datos'
            );
        }
        
        // Test option retrieval
        $retrieved = get_option($test_option);
        if ($retrieved === $test_value) {
            $this->test_results[] = array(
                'category' => 'database',
                'test' => 'Leer opción',
                'status' => 'success',
                'message' => 'Opción leída correctamente de base de datos'
            );
        }
        
        // Test option deletion
        $deleted = delete_option($test_option);
        if ($deleted) {
            $this->test_results[] = array(
                'category' => 'database',
                'test' => 'Eliminar opción',
                'status' => 'success',
                'message' => 'Opción eliminada correctamente de base de datos'
            );
        }
        
        // Test post meta operations
        $test_post_id = 1; // Assuming post ID 1 exists
        $test_meta_key = 'soloylibre_test_meta_' . time();
        $test_meta_value = 'test_meta_value_' . rand(1000, 9999);
        
        $meta_result = update_post_meta($test_post_id, $test_meta_key, $test_meta_value);
        if ($meta_result) {
            $this->test_results[] = array(
                'category' => 'database',
                'test' => 'Post meta',
                'status' => 'success',
                'message' => 'Post meta actualizado correctamente'
            );
            
            // Clean up
            delete_post_meta($test_post_id, $test_meta_key);
        }
    }
    
    /**
     * Test file upload functionality
     */
    private function test_file_uploads() {
        $upload_dir = wp_upload_dir();
        
        if (is_writable($upload_dir['basedir'])) {
            $this->test_results[] = array(
                'category' => 'file_uploads',
                'test' => 'Directorio de uploads',
                'status' => 'success',
                'message' => 'Directorio de uploads escribible: ' . $upload_dir['basedir']
            );
        } else {
            $this->test_results[] = array(
                'category' => 'file_uploads',
                'test' => 'Directorio de uploads',
                'status' => 'error',
                'message' => 'Directorio de uploads no escribible: ' . $upload_dir['basedir']
            );
        }
        
        // Test plugin-specific upload directory
        $plugin_upload_dir = $upload_dir['basedir'] . '/soloylibre-gallery';
        if (file_exists($plugin_upload_dir)) {
            if (is_writable($plugin_upload_dir)) {
                $this->test_results[] = array(
                    'category' => 'file_uploads',
                    'test' => 'Directorio plugin uploads',
                    'status' => 'success',
                    'message' => 'Directorio plugin escribible: ' . $plugin_upload_dir
                );
            } else {
                $this->test_results[] = array(
                    'category' => 'file_uploads',
                    'test' => 'Directorio plugin uploads',
                    'status' => 'warning',
                    'message' => 'Directorio plugin no escribible: ' . $plugin_upload_dir
                );
            }
        } else {
            $this->test_results[] = array(
                'category' => 'file_uploads',
                'test' => 'Directorio plugin uploads',
                'status' => 'info',
                'message' => 'Directorio plugin no existe (se creará cuando sea necesario)'
            );
        }
    }
    
    /**
     * Generate test summary
     */
    private function generate_summary() {
        $summary = array(
            'total_tests' => count($this->test_results),
            'success' => 0,
            'warning' => 0,
            'error' => 0,
            'info' => 0,
            'categories' => array()
        );
        
        foreach ($this->test_results as $result) {
            $summary[$result['status']]++;
            
            if (!isset($summary['categories'][$result['category']])) {
                $summary['categories'][$result['category']] = array(
                    'success' => 0,
                    'warning' => 0,
                    'error' => 0,
                    'info' => 0
                );
            }
            $summary['categories'][$result['category']][$result['status']]++;
        }
        
        $summary['success_rate'] = $summary['total_tests'] > 0 ? 
            round(($summary['success'] / $summary['total_tests']) * 100, 1) : 0;
        
        return $summary;
    }
}

// Run test if accessed directly
if (isset($_GET['run_test']) && $_GET['run_test'] == '1') {
    $tester = new SoloYLibre_User_Interaction_Test();
    $report = $tester->run_tests();
    
    header('Content-Type: application/json');
    echo json_encode($report, JSON_PRETTY_PRINT);
    exit;
}
?>
