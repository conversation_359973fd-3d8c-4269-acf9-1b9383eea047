<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoloYLibre Gallery Pro - Demo Showcase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }

        .showcase-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
        }

        .showcase-header h1 {
            font-size: 48px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .showcase-header .subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .photographer-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            display: inline-block;
            margin-top: 20px;
        }

        .photographer-info h3 {
            margin-bottom: 10px;
        }

        .websites {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 10px;
        }

        .website-link {
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 12px;
            border-radius: 15px;
            color: white;
            text-decoration: none;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .website-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .features-section {
            padding: 60px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
        }

        .styles-showcase {
            background: white;
            padding: 60px 20px;
            margin: 40px 0;
        }

        .styles-showcase h2 {
            text-align: center;
            font-size: 36px;
            margin-bottom: 40px;
            color: #333;
        }

        .style-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .style-tab {
            background: #f0f0f0;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .style-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .style-preview {
            max-width: 800px;
            margin: 0 auto;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .style-preview img {
            width: 100%;
            height: auto;
            display: block;
        }

        .membership-integration {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            color: white;
            padding: 60px 20px;
            text-align: center;
        }

        .membership-integration h2 {
            font-size: 36px;
            margin-bottom: 20px;
        }

        .membership-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            max-width: 1000px;
            margin: 40px auto 0;
        }

        .membership-feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .membership-feature h3 {
            margin-bottom: 10px;
            font-size: 18px;
        }

        .security-section {
            padding: 60px 20px;
            background: #1a1a1a;
            color: white;
        }

        .security-section h2 {
            text-align: center;
            font-size: 36px;
            margin-bottom: 40px;
            color: #ff6b6b;
        }

        .security-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .security-feature {
            background: #2d2d2d;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #ff6b6b;
        }

        .security-feature h3 {
            color: #ff6b6b;
            margin-bottom: 10px;
        }

        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 20px;
            text-align: center;
        }

        .cta-section h2 {
            font-size: 42px;
            margin-bottom: 20px;
        }

        .cta-section p {
            font-size: 18px;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .cta-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .cta-button.primary {
            background: #ff6b6b;
        }

        .cta-button.primary:hover {
            background: #ff5252;
        }

        .footer {
            background: #1a1a1a;
            color: white;
            padding: 40px 20px;
            text-align: center;
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .footer h3 {
            margin-bottom: 20px;
            color: #ff6b6b;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #ff6b6b;
        }

        @media (max-width: 768px) {
            .showcase-header h1 {
                font-size: 32px;
                flex-direction: column;
                gap: 10px;
            }

            .features-grid,
            .membership-features,
            .security-features {
                grid-template-columns: 1fr;
            }

            .style-tabs {
                flex-direction: column;
                align-items: center;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .footer-links {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="showcase-header">
        <h1>
            <span>📸</span>
            SoloYLibre Gallery Pro
        </h1>
        <p class="subtitle">Plugin Profesional de Galería de Fotos para WordPress</p>
        
        <div class="photographer-info">
            <h3>Desarrollado para Jose L Encarnacion (JoseTusabe)</h3>
            <p>📍 San José de Ocoa, Dom. Rep. / USA • 📞 718-713-5500</p>
            <div class="websites">
                <a href="#" class="website-link">josetusabe.com</a>
                <a href="#" class="website-link">soloylibre.com</a>
                <a href="#" class="website-link">1and1photo.com</a>
                <a href="#" class="website-link">joselencarnacion.com</a>
            </div>
        </div>
    </header>

    <!-- Features Section -->
    <section class="features-section">
        <div class="features-grid">
            <div class="feature-card">
                <span class="feature-icon">🎨</span>
                <h3 class="feature-title">Múltiples Estilos</h3>
                <p class="feature-description">
                    5 estilos diferentes de galería: TikTok Style, Grid Portfolio, Masonry Layout, 
                    Carousel y Lightbox. Cada uno optimizado para diferentes tipos de contenido.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">🔒</span>
                <h3 class="feature-title">Protección Avanzada</h3>
                <p class="feature-description">
                    Sistema completo de protección de contenido con revisión manual obligatoria, 
                    prevención de publicación accidental y control granular de acceso.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">👥</span>
                <h3 class="feature-title">Integración de Membresías</h3>
                <p class="feature-description">
                    Compatible con Paid Memberships Pro, MemberPress y Restrict Content Pro. 
                    Control de acceso por niveles de membresía.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">📁</span>
                <h3 class="feature-title">Gestión de Álbumes</h3>
                <p class="feature-description">
                    Organiza tus fotos en álbumes personalizados con reordenamiento drag & drop, 
                    fotos de portada y configuraciones específicas.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">📱</span>
                <h3 class="feature-title">Responsive Design</h3>
                <p class="feature-description">
                    Diseño completamente responsive que se adapta perfectamente a móviles, 
                    tablets y desktop. Optimizado para todas las pantallas.
                </p>
            </div>

            <div class="feature-card">
                <span class="feature-icon">⚡</span>
                <h3 class="feature-title">Alto Rendimiento</h3>
                <p class="feature-description">
                    Carga lazy de imágenes, infinite scroll optimizado, y código limpio 
                    para máximo rendimiento y velocidad de carga.
                </p>
            </div>
        </div>
    </section>

    <!-- Styles Showcase -->
    <section class="styles-showcase">
        <h2>🎨 Estilos de Galería Disponibles</h2>
        
        <div class="style-tabs">
            <button class="style-tab active" data-style="tiktok">TikTok Style</button>
            <button class="style-tab" data-style="grid">Grid Portfolio</button>
            <button class="style-tab" data-style="masonry">Masonry Layout</button>
            <button class="style-tab" data-style="carousel">Carousel</button>
            <button class="style-tab" data-style="lightbox">Lightbox</button>
        </div>

        <div class="style-preview">
            <div class="style-content" id="style-tiktok">
                <img src="https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=800&h=600&fit=crop" 
                     alt="TikTok Style Preview">
            </div>
            <div class="style-content" id="style-grid" style="display: none;">
                <img src="https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?w=800&h=600&fit=crop" 
                     alt="Grid Style Preview">
            </div>
            <div class="style-content" id="style-masonry" style="display: none;">
                <img src="https://images.unsplash.com/photo-1618556450994-a6a128ef0d9d?w=800&h=600&fit=crop" 
                     alt="Masonry Style Preview">
            </div>
            <div class="style-content" id="style-carousel" style="display: none;">
                <img src="https://images.unsplash.com/photo-1618556450991-2f1af64e8191?w=800&h=600&fit=crop" 
                     alt="Carousel Style Preview">
            </div>
            <div class="style-content" id="style-lightbox" style="display: none;">
                <img src="https://images.unsplash.com/photo-1618556450994-a6a128ef0d9d?w=800&h=600&fit=crop" 
                     alt="Lightbox Style Preview">
            </div>
        </div>
    </section>

    <!-- Membership Integration -->
    <section class="membership-integration">
        <h2>👥 Integración con Paid Memberships Pro</h2>
        <p>Control total sobre quién puede ver tu contenido premium</p>

        <div class="membership-features">
            <div class="membership-feature">
                <h3>🎯 Plan Recomendado: Plus</h3>
                <p>$299/año con 50% descuento primer año. Incluye Member Directory, Content Drip-feed y Variable Pricing.</p>
            </div>

            <div class="membership-feature">
                <h3>🔐 Restricciones Granulares</h3>
                <p>Asigna diferentes niveles de acceso a cada foto o álbum completo.</p>
            </div>

            <div class="membership-feature">
                <h3>💎 Contenido Premium</h3>
                <p>Monetiza tu trabajo con contenido exclusivo para miembros pagos.</p>
            </div>

            <div class="membership-feature">
                <h3>📈 Badges de Membresía</h3>
                <p>Muestra el estatus de membresía de cada usuario en la galería.</p>
            </div>
        </div>
    </section>

    <!-- Security Section -->
    <section class="security-section">
        <h2>🛡️ Protección de Contenido Avanzada</h2>

        <div class="security-features">
            <div class="security-feature">
                <h3>✅ Revisión Manual Obligatoria</h3>
                <p>Ninguna foto se publica sin tu revisión y aprobación explícita. Sistema de doble confirmación para máxima seguridad.</p>
            </div>

            <div class="security-feature">
                <h3>🚫 Prevención de Accidentes</h3>
                <p>Bloqueo automático de publicación de contenido no autorizado. Protege tu privacidad y reputación profesional.</p>
            </div>

            <div class="security-feature">
                <h3>📝 Logs de Actividad</h3>
                <p>Registro completo de todos los cambios de protección y accesos. Monitoreo total de tu contenido.</p>
            </div>

            <div class="security-feature">
                <h3>🔒 Niveles de Protección</h3>
                <p>Múltiples niveles: Público, Solo Miembros, Premium, Privado y No Publicable. Control granular total.</p>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <h2>¿Listo para Proteger y Monetizar tu Fotografía?</h2>
        <p>Únete a fotógrafos profesionales que confían en SoloYLibre Gallery Pro</p>
        
        <div class="cta-buttons">
            <a href="#" class="cta-button primary">Instalar Plugin</a>
            <a href="#" class="cta-button">Ver Documentación</a>
            <a href="#" class="cta-button">Contactar Soporte</a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <h3>SoloYLibre Gallery Pro</h3>
            <p>Desarrollado por JEYKO AI específicamente para Jose L Encarnacion</p>
            
            <div class="footer-links">
                <a href="#">Documentación</a>
                <a href="#">Soporte</a>
                <a href="#">Actualizaciones</a>
                <a href="#">Contacto</a>
            </div>
            
            <p style="margin-top: 20px; opacity: 0.7; font-size: 14px;">
                © 2024 SoloYLibre Photography - Plugin profesional para fotógrafos
            </p>
        </div>
    </footer>

    <script>
        // Style tabs functionality
        document.querySelectorAll('.style-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                document.querySelectorAll('.style-tab').forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Hide all style contents
                document.querySelectorAll('.style-content').forEach(content => {
                    content.style.display = 'none';
                });
                
                // Show selected style content
                const styleId = 'style-' + this.dataset.style;
                document.getElementById(styleId).style.display = 'block';
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all feature cards
        document.querySelectorAll('.feature-card, .membership-feature, .security-feature').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
