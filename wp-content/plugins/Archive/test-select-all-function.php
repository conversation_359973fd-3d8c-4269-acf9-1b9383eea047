<?php
/**
 * Test Select All Function - SoloYLibre Gallery Plugin
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>🔧 Prueba de Función Select-All</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Probando funcionalidad de selección masiva</p>";

// 1. Test AJAX endpoint registration
echo "<h2>🔍 1. Verificando Registro de Endpoint AJAX</h2>";

if (has_action('wp_ajax_bulk_select_all')) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Endpoint bulk_select_all está registrado correctamente</strong>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ <strong>Endpoint bulk_select_all NO está registrado</strong>";
    echo "</div>";
}

// 2. Test class existence
echo "<h2>📋 2. Verificando Clase del Gestor Masivo</h2>";

if (class_exists('SoloYLibre_Bulk_Photo_Manager')) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Clase SoloYLibre_Bulk_Photo_Manager existe</strong>";
    
    // Check if method exists
    if (method_exists('SoloYLibre_Bulk_Photo_Manager', 'ajax_select_all')) {
        echo "<br>✅ <strong>Método ajax_select_all existe en la clase</strong>";
    } else {
        echo "<br>❌ <strong>Método ajax_select_all NO existe en la clase</strong>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ <strong>Clase SoloYLibre_Bulk_Photo_Manager NO existe</strong>";
    echo "</div>";
}

// 3. Simulate AJAX call
echo "<h2>🧪 3. Simulando Llamada AJAX</h2>";

// Create nonce for testing
$test_nonce = wp_create_nonce('bulk_manager_nonce');

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>Nonce generado para prueba:</strong> $test_nonce";
echo "</div>";

// 4. Test with sample data
echo "<h2>📊 4. Probando con Datos de Muestra</h2>";

global $wpdb;

// Get sample photos
$sample_photos = $wpdb->get_results("
    SELECT ID, post_title 
    FROM {$wpdb->posts} 
    WHERE post_type = 'attachment' 
    AND post_mime_type LIKE 'image%' 
    LIMIT 10
");

if (!empty($sample_photos)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>Fotos de muestra encontradas:</strong> " . count($sample_photos);
    echo "<ul>";
    foreach ($sample_photos as $photo) {
        echo "<li>ID: {$photo->ID} - {$photo->post_title}</li>";
    }
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "⚠️ <strong>No se encontraron fotos de muestra</strong>";
    echo "</div>";
}

// 5. Test the actual function
echo "<h2>🔧 5. Probando Función Select-All Directamente</h2>";

if (class_exists('SoloYLibre_Bulk_Photo_Manager')) {
    try {
        // Simulate POST data
        $_POST['nonce'] = $test_nonce;
        $_POST['filter'] = 'all';
        
        // Create instance and test
        $bulk_manager = new SoloYLibre_Bulk_Photo_Manager();
        
        if (method_exists($bulk_manager, 'ajax_select_all')) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "✅ <strong>Método ajax_select_all está disponible para prueba</strong>";
            echo "</div>";
            
            // Test the method (capture output)
            ob_start();
            try {
                $bulk_manager->ajax_select_all();
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "❌ <strong>Error ejecutando ajax_select_all:</strong> " . $e->getMessage();
                echo "</div>";
            }
            $output = ob_get_clean();
            
            if (!empty($output)) {
                echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<strong>Salida del método:</strong><br>";
                echo "<pre>" . htmlspecialchars($output) . "</pre>";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "❌ <strong>Método ajax_select_all NO está disponible</strong>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ <strong>Error creando instancia:</strong> " . $e->getMessage();
        echo "</div>";
    }
}

// 6. Check JavaScript functionality
echo "<h2>📱 6. Verificando JavaScript del Frontend</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "⚠️ <strong>Nota:</strong> Para probar completamente la función select-all, necesitas:";
echo "<ul>";
echo "<li>1. Ir al Gestor de Fotos Masivo</li>";
echo "<li>2. Abrir las herramientas de desarrollador del navegador (F12)</li>";
echo "<li>3. Hacer clic en 'Seleccionar Todas'</li>";
echo "<li>4. Verificar si hay errores en la consola</li>";
echo "</ul>";
echo "</div>";

// 7. Create a test button
echo "<h2>🎯 7. Botón de Prueba Directo</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<button onclick='testSelectAllFunction()' style='background: #007cba; color: white; padding: 15px 30px; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;'>";
echo "🧪 Probar Select-All via AJAX";
echo "</button>";
echo "</div>";

echo "<div id='test-results' style='margin: 20px 0;'></div>";

// 8. JavaScript for testing
echo "<script>
function testSelectAllFunction() {
    const resultsDiv = document.getElementById('test-results');
    resultsDiv.innerHTML = '<div style=\"background: #e3f2fd; padding: 15px; border-radius: 5px;\">⏳ Probando función select-all...</div>';
    
    // Make AJAX call
    fetch('" . admin_url('admin-ajax.php') . "', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            action: 'bulk_select_all',
            nonce: '$test_nonce',
            filter: 'all'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultsDiv.innerHTML = `
                <div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>
                    ✅ <strong>¡Función select-all funciona correctamente!</strong><br>
                    📊 Fotos seleccionadas: \${data.data.count}<br>
                    🔍 Filtro aplicado: \${data.data.filter}<br>
                    💬 Mensaje: \${data.data.message}
                </div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>
                    ❌ <strong>Error en función select-all:</strong><br>
                    \${data.data || 'Error desconocido'}
                </div>
            `;
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = `
            <div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>
                ❌ <strong>Error de conexión:</strong><br>
                \${error.message}
            </div>
        `;
    });
}
</script>";

// 9. Recommendations
echo "<h2>💡 9. Recomendaciones</h2>";

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3 style='color: #0c5460; margin: 0 0 15px 0;'>💡 Si la función select-all muestra 'en desarrollo':</h3>";
echo "<ol style='color: #0c5460;'>";
echo "<li>Verifica que no haya JavaScript personalizado que esté interceptando el evento</li>";
echo "<li>Revisa si hay algún mensaje hardcodeado en el frontend</li>";
echo "<li>Comprueba que el nonce se esté generando correctamente</li>";
echo "<li>Asegúrate de que no hay conflictos con otros plugins</li>";
echo "<li>Verifica que la función selectAllPhotos() en JavaScript esté llamando al endpoint correcto</li>";
echo "</ol>";
echo "</div>";

// 10. Quick links
echo "<h2>🔗 10. Enlaces Rápidos para Pruebas</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-bulk-manager' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>📁 Ir al Gestor Masivo</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-wizard' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>🧙‍♂️ Ir al Wizard</a>";
echo "</div>";

// 11. Final message
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Prueba de función select-all completada</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";

// Clean up POST data
unset($_POST['nonce'], $_POST['filter']);
?>
