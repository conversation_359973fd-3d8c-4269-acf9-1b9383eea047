<?php
/**
 * Create Installation Package - SoloYLibre Gallery Plugin
 * Generate complete ZIP package for online installation
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

echo "<h1>📦 Creando Paquete de Instalación</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Generando ZIP completo para instalación online</p>";

$plugin_dir = dirname(__FILE__);
$zip_filename = 'soloylibre-gallery-pro-v5.4.0-complete.zip';
$zip_path = $plugin_dir . '/' . $zip_filename;

// Remove old zip if exists
if (file_exists($zip_path)) {
    unlink($zip_path);
}

$zip = new ZipArchive();
$result = $zip->open($zip_path, ZipArchive::CREATE | ZipArchive::OVERWRITE);

if ($result !== TRUE) {
    die("❌ Error creando archivo ZIP: $result");
}

echo "<h2>📁 Agregando Archivos al Paquete</h2>";

$files_added = 0;
$files_skipped = 0;
$total_size = 0;

// Files to exclude from the package
$exclude_patterns = array(
    '/\.git/',
    '/node_modules/',
    '/\.DS_Store',
    '/Thumbs.db',
    '/\.zip$/',
    '/create-installation-package\.php$/',
    '/verify-.*\.php$/',
    '/test-.*\.php$/',
    '/debug-.*\.php$/',
    '/audit-.*\.php$/',
    '/fix-.*\.php$/',
    '/auto-.*\.php$/',
    '/setup-.*\.php$/',
    '/install-.*\.php$/',
    '/wp-config\.php$/',
    '/wp-content\/db\.php$/',
    '/docker-compose\.yml$/',
    '/\.md$/',
    '/\.html$/'
);

function should_exclude($file_path, $exclude_patterns) {
    foreach ($exclude_patterns as $pattern) {
        if (preg_match($pattern, $file_path)) {
            return true;
        }
    }
    return false;
}

function add_directory_to_zip($zip, $dir, $base_dir, &$files_added, &$files_skipped, &$total_size, $exclude_patterns) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($iterator as $file) {
        $file_path = $file->getRealPath();
        $relative_path = str_replace($base_dir . DIRECTORY_SEPARATOR, '', $file_path);
        $relative_path = str_replace('\\', '/', $relative_path); // Normalize path separators
        
        if (should_exclude($relative_path, $exclude_patterns)) {
            $files_skipped++;
            continue;
        }
        
        if ($file->isDir()) {
            $zip->addEmptyDir('soloylibre-gallery-pro/' . $relative_path);
        } else {
            $zip->addFile($file_path, 'soloylibre-gallery-pro/' . $relative_path);
            $files_added++;
            $total_size += $file->getSize();
            
            if ($files_added % 10 == 0) {
                echo "<div style='background: #e3f2fd; padding: 5px; margin: 2px 0; border-radius: 3px;'>";
                echo "📄 Agregado: $relative_path";
                echo "</div>";
                flush();
            }
        }
    }
}

// Add main plugin files
echo "<h3>📋 Agregando Archivos Principales</h3>";
add_directory_to_zip($zip, $plugin_dir, $plugin_dir, $files_added, $files_skipped, $total_size, $exclude_patterns);

// Create installation instructions
$installation_instructions = "# SoloYLibre Gallery Pro - Instrucciones de Instalación

## 🇩🇴 JoseTusabe Photography - Sistema Profesional de Gestión Fotográfica

### Versión: 5.4.0
### Desarrollado por: JEYKO AI para Jose L Encarnacion (JoseTusabe)

## 📦 Instalación

1. **Subir Plugin:**
   - Ve a WordPress Admin > Plugins > Añadir nuevo
   - Haz clic en \"Subir plugin\"
   - Selecciona el archivo ZIP
   - Haz clic en \"Instalar ahora\"

2. **Activar Plugin:**
   - Haz clic en \"Activar plugin\"
   - El plugin se configurará automáticamente

3. **Acceder al Dashboard:**
   - Ve a \"SoloYLibre\" en el menú de WordPress
   - Explora todas las funcionalidades

## ✨ Características Principales

### 🎯 Posts Rápidos
- Vista previa de seguridad obligatoria
- Selección ilimitada de fotos
- Exclusión automática de fotos privadas
- Títulos creativos automáticos

### 📊 Dashboard Mejorado
- Gráficos interactivos con Chart.js
- Animaciones glassmorphism
- Estadísticas en tiempo real
- Feed de actividad

### 💖 Sistema de Likes
- Likes ilimitados por foto
- Tracking en base de datos
- Estadísticas de interacción

### 🧙‍♂️ Wizard Inteligente
- Selección automática de fotos
- Interfaz moderna sin popups
- Configuración avanzada

## 🔧 Configuración Automática

El plugin se configura automáticamente con:
- ✅ Información del fotógrafo (Jose L Encarnacion)
- ✅ Categorías y tags por defecto
- ✅ Tablas de base de datos
- ✅ Estadísticas iniciales
- ✅ Sistema de likes

## 📱 Compatibilidad

- ✅ WordPress 5.0+
- ✅ PHP 7.4+
- ✅ Responsive design
- ✅ Todos los navegadores modernos

## 🆘 Soporte

Para soporte técnico:
- 📧 Email: <EMAIL>
- 📱 Teléfono: ************
- 🌐 Web: josetusabe.com | soloylibre.com

## 🇩🇴 Personalización Dominicana

El plugin está completamente personalizado para:
- Jose L Encarnacion (JoseTusabe)
- JoseTusabe Photography
- San José de Ocoa, República Dominicana
- Temática fotográfica dominicana

¡Disfruta de tu nuevo sistema profesional de gestión fotográfica!
";

$zip->addFromString('soloylibre-gallery-pro/INSTALLATION.md', $installation_instructions);

// Create changelog
$changelog = "# Changelog - SoloYLibre Gallery Pro v5.4.0

## 🎉 Nuevas Características

### ⚡ Posts Rápidos Mejorados
- ✅ Vista previa de seguridad obligatoria
- ✅ Selección ilimitada de fotos (removido límite de 50)
- ✅ Mensajes informativos cuando no hay suficientes fotos
- ✅ Botón para resetear solo fotos publicadas
- ✅ Exclusión automática de fotos privadas y no deseadas

### 💖 Sistema de Likes Ilimitados
- ✅ Usuarios pueden dar likes ilimitados
- ✅ Tracking completo en base de datos
- ✅ Estadísticas de interacción en tiempo real
- ✅ Integración con sistema glass

### 📊 Dashboard con Gráficos Modernos
- ✅ Chart.js integrado para gráficos interactivos
- ✅ Animaciones glassmorphism avanzadas
- ✅ Tarjetas de estadísticas animadas
- ✅ Feed de actividad en tiempo real
- ✅ Optimización de rendimiento

### 🧙‍♂️ Wizard Renovado
- ✅ Interfaz moderna sin popups
- ✅ Formato azul consistente con el resto del sistema
- ✅ Selección inteligente de fotos
- ✅ Configuración avanzada

### 🎨 Títulos Creativos Automáticos
- ✅ Generación automática de títulos únicos
- ✅ Temática dominicana y lugares de RD
- ✅ Variedad de patrones creativos
- ✅ Personalización para JoseTusabe Photography

## 🔧 Mejoras Técnicas

### 🚀 Rendimiento
- ✅ Gráficos optimizados para carga rápida
- ✅ Lazy loading de imágenes
- ✅ Caché de estadísticas
- ✅ Compresión de assets

### 🔒 Seguridad
- ✅ Vista previa obligatoria antes de publicar
- ✅ Validación completa de datos
- ✅ Protección contra fotos no deseadas
- ✅ Nonces de seguridad en AJAX

### 📱 Responsive
- ✅ Diseño 100% responsive
- ✅ Optimización para móviles
- ✅ Touch-friendly interfaces
- ✅ Adaptación automática de layouts

## 🇩🇴 Personalización JoseTusabe

### 👤 Información del Fotógrafo
- ✅ Jose L Encarnacion (JoseTusabe)
- ✅ JoseTusabe Photography
- ✅ San José de Ocoa, República Dominicana
- ✅ Contacto: <EMAIL> | ************

### 🌐 Sitios Web Integrados
- ✅ josetusabe.com
- ✅ soloylibre.com
- ✅ 1and1photo.com
- ✅ joselencarnacion.com

### 🏷️ Categorías y Tags
- ✅ Categorías dominicanas por defecto
- ✅ Tags de fotografía profesional
- ✅ Temática caribeña y tropical

## 📦 Instalación Automática

- ✅ Configuración inicial automática
- ✅ No requiere setup manual
- ✅ Tablas de base de datos auto-creadas
- ✅ Estadísticas inicializadas
- ✅ Listo para usar inmediatamente

Desarrollado con ❤️ por JEYKO AI para Jose L Encarnacion (JoseTusabe)
";

$zip->addFromString('soloylibre-gallery-pro/CHANGELOG.md', $changelog);

// Close ZIP
$zip->close();

echo "<h2>📊 Estadísticas del Paquete</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$files_added</div>";
echo "<div>📄 Archivos Incluidos</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$files_skipped</div>";
echo "<div>🚫 Archivos Excluidos</div>";
echo "</div>";

$size_mb = round($total_size / 1024 / 1024, 2);
echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>{$size_mb}MB</div>";
echo "<div>💾 Tamaño Total</div>";
echo "</div>";

$zip_size_mb = round(filesize($zip_path) / 1024 / 1024, 2);
echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>{$zip_size_mb}MB</div>";
echo "<div>📦 ZIP Comprimido</div>";
echo "</div>";

echo "</div>";

echo "<h2>✅ Paquete Creado Exitosamente</h2>";

echo "<div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h3 style='margin: 0 0 15px 0; color: #155724;'>🎉 ¡Paquete de Instalación Listo!</h3>";
echo "<p style='margin: 0; font-size: 18px; color: #155724;'>";
echo "<strong>Archivo:</strong> $zip_filename<br>";
echo "<strong>Tamaño:</strong> {$zip_size_mb}MB<br>";
echo "<strong>Archivos:</strong> $files_added incluidos";
echo "</p>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='$zip_filename' download class='button button-primary' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; font-size: 16px;'>📥 Descargar Paquete ZIP</a>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
echo "<h3>📋 Instrucciones de Instalación</h3>";
echo "<ol>";
echo "<li><strong>Descargar:</strong> Haz clic en el botón de descarga arriba</li>";
echo "<li><strong>Subir:</strong> Ve a WordPress Admin > Plugins > Añadir nuevo > Subir plugin</li>";
echo "<li><strong>Instalar:</strong> Selecciona el archivo ZIP y haz clic en 'Instalar ahora'</li>";
echo "<li><strong>Activar:</strong> Haz clic en 'Activar plugin'</li>";
echo "<li><strong>Usar:</strong> Ve al menú 'SoloYLibre' y disfruta todas las funcionalidades</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
echo "<h3>🚀 Características Incluidas</h3>";
echo "<ul>";
echo "<li>✅ <strong>Posts Rápidos:</strong> Con vista previa de seguridad y selección ilimitada</li>";
echo "<li>✅ <strong>Dashboard Moderno:</strong> Con gráficos Chart.js y animaciones glassmorphism</li>";
echo "<li>✅ <strong>Sistema de Likes:</strong> Ilimitados con tracking en base de datos</li>";
echo "<li>✅ <strong>Wizard Inteligente:</strong> Interfaz moderna sin popups</li>";
echo "<li>✅ <strong>Títulos Creativos:</strong> Generación automática con temática dominicana</li>";
echo "<li>✅ <strong>Configuración Automática:</strong> Listo para usar sin setup manual</li>";
echo "<li>✅ <strong>Personalización JoseTusabe:</strong> Completamente personalizado</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Paquete de instalación completo</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>📧 <EMAIL> | 📱 ************</p>";
echo "</div>";
?>
