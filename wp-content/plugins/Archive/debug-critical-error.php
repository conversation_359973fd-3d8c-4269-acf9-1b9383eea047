<?php
/**
 * Debug Critical Error - SoloYLibre Gallery Plugin
 * Find and fix critical WordPress errors
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>🔍 Diagnóstico de Error Crítico</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Encontrando y reparando errores</p>";

// Check if WordPress is loaded
if (!defined('ABSPATH')) {
    echo "<h2>⚠️ Cargando WordPress...</h2>";
    try {
        require_once('../../../wp-config.php');
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ WordPress cargado correctamente";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ Error cargando WordPress: " . $e->getMessage();
        echo "</div>";
        die();
    }
}

echo "<h2>🔧 1. Verificando Archivos del Plugin</h2>";

$plugin_files = array(
    'soloylibre-gallery-plugin.php' => 'Archivo principal',
    'includes/class-glass-menu-system.php' => 'Sistema Glass',
    'includes/class-user-interactions.php' => 'Interacciones de usuario',
    'includes/class-initial-setup.php' => 'Setup inicial'
);

$errors_found = array();
$files_ok = array();

foreach ($plugin_files as $file => $description) {
    $file_path = dirname(__FILE__) . '/' . $file;
    
    echo "<h3>📄 Verificando: $description</h3>";
    
    if (!file_exists($file_path)) {
        $errors_found[] = "❌ Archivo no encontrado: $file";
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Archivo no encontrado: $file";
        echo "</div>";
        continue;
    }
    
    // Check PHP syntax
    $output = array();
    $return_var = 0;
    exec("php -l " . escapeshellarg($file_path) . " 2>&1", $output, $return_var);
    
    if ($return_var !== 0) {
        $errors_found[] = "❌ Error de sintaxis en $file: " . implode(' ', $output);
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ <strong>Error de sintaxis en $file:</strong><br>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
        echo "</div>";
    } else {
        $files_ok[] = "✅ $file - Sintaxis correcta";
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ $file - Sintaxis correcta";
        echo "</div>";
    }
}

echo "<h2>🔍 2. Verificando Funciones y Clases</h2>";

// Check if classes exist
$required_classes = array(
    'SoloYLibre_Gallery_Plugin',
    'SoloYLibre_Glass_Menu_System',
    'SoloYLibre_User_Interactions'
);

foreach ($required_classes as $class) {
    if (class_exists($class)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Clase encontrada: $class";
        echo "</div>";
    } else {
        $errors_found[] = "❌ Clase no encontrada: $class";
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Clase no encontrada: $class";
        echo "</div>";
    }
}

echo "<h2>🗄️ 3. Verificando Base de Datos</h2>";

global $wpdb;

try {
    // Test database connection
    $result = $wpdb->get_var("SELECT 1");
    if ($result == 1) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Conexión a base de datos OK";
        echo "</div>";
    }
    
    // Check if our tables exist
    $tables_to_check = array(
        $wpdb->prefix . 'soloylibre_interactions' => 'Tabla de interacciones',
        $wpdb->prefix . 'soloylibre_albums' => 'Tabla de álbumes'
    );
    
    foreach ($tables_to_check as $table => $description) {
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
        if ($table_exists) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ $description existe";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "⚠️ $description no existe (se creará automáticamente)";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    $errors_found[] = "❌ Error de base de datos: " . $e->getMessage();
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ Error de base de datos: " . $e->getMessage();
    echo "</div>";
}

echo "<h2>🔧 4. Verificando Hooks y Actions</h2>";

// Check if WordPress hooks are working
if (function_exists('add_action')) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Sistema de hooks de WordPress funcionando";
    echo "</div>";
} else {
    $errors_found[] = "❌ Sistema de hooks de WordPress no disponible";
}

echo "<h2>🚀 5. Intentando Reparación Automática</h2>";

if (!empty($errors_found)) {
    echo "<h3>❌ Errores Encontrados:</h3>";
    foreach ($errors_found as $error) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo $error;
        echo "</div>";
    }
    
    echo "<h3>🔧 Aplicando Reparaciones:</h3>";
    
    // Try to fix common issues
    
    // 1. Recreate database tables
    echo "<h4>🗄️ Recreando tablas de base de datos...</h4>";
    
    $interactions_table = $wpdb->prefix . 'soloylibre_interactions';
    $sql = "CREATE TABLE IF NOT EXISTS $interactions_table (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        photo_id bigint(20) NOT NULL,
        user_id bigint(20) DEFAULT NULL,
        user_ip varchar(45) DEFAULT NULL,
        reaction_type varchar(20) NOT NULL DEFAULT 'like',
        interaction_type varchar(20) DEFAULT NULL,
        interaction_count int(11) DEFAULT 1,
        is_simulated tinyint(1) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY photo_id (photo_id),
        KEY user_id (user_id),
        KEY reaction_type (reaction_type),
        KEY created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    $result = dbDelta($sql);
    
    if (!empty($result)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Tabla de interacciones reparada";
        echo "</div>";
    }
    
    // 2. Reset plugin options
    echo "<h4>⚙️ Reseteando opciones del plugin...</h4>";
    
    $default_options = array(
        'soloylibre_gallery_setup_complete' => true,
        'soloylibre_gallery_configured' => true,
        'soloylibre_setup_complete' => true,
        'soloylibre_gallery_version' => '5.4.0',
        'soloylibre_published_photos' => array(),
        'soloylibre_private_photos' => array(),
        'soloylibre_unwanted_photos' => array()
    );
    
    foreach ($default_options as $option => $value) {
        update_option($option, $value);
    }
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Opciones del plugin reseteadas";
    echo "</div>";
    
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ <strong>No se encontraron errores en los archivos del plugin</strong>";
    echo "</div>";
}

echo "<h2>🔍 6. Verificando Conflictos con Otros Plugins</h2>";

// Get active plugins
$active_plugins = get_option('active_plugins', array());
$problematic_plugins = array();

$known_conflicts = array(
    'jetpack/jetpack.php' => 'Jetpack',
    'wordfence/wordfence.php' => 'Wordfence',
    'wp-super-cache/wp-cache.php' => 'WP Super Cache',
    'w3-total-cache/w3-total-cache.php' => 'W3 Total Cache'
);

foreach ($active_plugins as $plugin) {
    if (isset($known_conflicts[$plugin])) {
        $problematic_plugins[] = $known_conflicts[$plugin];
    }
}

if (!empty($problematic_plugins)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "⚠️ <strong>Plugins que pueden causar conflictos:</strong><br>";
    echo implode(', ', $problematic_plugins);
    echo "<br><em>Considera desactivarlos temporalmente para probar</em>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ No se detectaron plugins problemáticos";
    echo "</div>";
}

echo "<h2>🎯 7. Soluciones Recomendadas</h2>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔧 Pasos para Resolver el Error:</h3>";
echo "<ol>";
echo "<li><strong>Desactivar el plugin:</strong> Ve a Plugins y desactiva SoloYLibre Gallery</li>";
echo "<li><strong>Verificar logs:</strong> Revisa wp-content/debug.log para errores específicos</li>";
echo "<li><strong>Reactivar:</strong> Activa el plugin nuevamente</li>";
echo "<li><strong>Limpiar caché:</strong> Si usas plugins de caché, límpialos</li>";
echo "<li><strong>Verificar permisos:</strong> Asegúrate de que los archivos tengan permisos correctos</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🚨 Si el Error Persiste:</h3>";
echo "<ol>";
echo "<li><strong>Modo debug:</strong> Activa WP_DEBUG en wp-config.php</li>";
echo "<li><strong>Plugin por defecto:</strong> Cambia a un tema por defecto temporalmente</li>";
echo "<li><strong>Memoria PHP:</strong> Aumenta memory_limit en php.ini</li>";
echo "<li><strong>Reinstalar:</strong> Desinstala y reinstala el plugin</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/plugins.php' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔧 Ir a Plugins</a>";
echo "<a href='../../../wp-admin/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🏠 Dashboard</a>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Diagnóstico de errores completado</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
