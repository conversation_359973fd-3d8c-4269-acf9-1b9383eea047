<?php
/**
 * SoloYLibre Gallery Pro - Diagnóstico Completo
 * Verifica todos los sistemas del plugin
 * Developed by JEYKO AI for Jose L Encarnac<PERSON> (JoseTusabe)
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

class SoloYLibre_Complete_Diagnostic {
    
    private $results = array();
    
    public function run_complete_diagnostic() {
        $this->test_ajax_endpoints();
        $this->test_database_interactions();
        $this->test_posts_automaticos();
        $this->test_user_interactions_persistence();
        $this->test_nonce_generation();
        $this->test_javascript_loading();
        
        return array(
            'timestamp' => current_time('mysql'),
            'results' => $this->results,
            'summary' => $this->generate_summary()
        );
    }
    
    /**
     * Test AJAX endpoints
     */
    private function test_ajax_endpoints() {
        $endpoints = array(
            'soloylibre_get_all_photos_preview' => 'Vista previa de fotos',
            'soloylibre_create_advanced_auto_post' => 'Posts automáticos avanzados',
            'gallery_interaction' => 'Interacciones de usuario',
            'wizard_get_photos' => 'Obtener fotos wizard',
            'wizard_create_post' => 'Crear post wizard'
        );
        
        foreach ($endpoints as $action => $description) {
            $has_action = has_action("wp_ajax_$action");
            
            if ($has_action) {
                $this->results[] = array(
                    'category' => 'ajax_endpoints',
                    'test' => $description,
                    'status' => 'success',
                    'message' => "AJAX endpoint '$action' registrado correctamente"
                );
            } else {
                $this->results[] = array(
                    'category' => 'ajax_endpoints',
                    'test' => $description,
                    'status' => 'error',
                    'message' => "AJAX endpoint '$action' NO registrado"
                );
            }
        }
    }
    
    /**
     * Test database interactions table
     */
    private function test_database_interactions() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_interactions';
        
        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        
        if ($table_exists) {
            $this->results[] = array(
                'category' => 'database',
                'test' => 'Tabla de interacciones',
                'status' => 'success',
                'message' => "Tabla '$table_name' existe"
            );
            
            // Check table structure
            $columns = $wpdb->get_results("DESCRIBE $table_name");
            $required_columns = array('id', 'photo_id', 'interaction_type', 'interaction_count', 'created_at', 'updated_at');
            $existing_columns = array_column($columns, 'Field');
            
            $missing_columns = array_diff($required_columns, $existing_columns);
            
            if (empty($missing_columns)) {
                $this->results[] = array(
                    'category' => 'database',
                    'test' => 'Estructura de tabla',
                    'status' => 'success',
                    'message' => 'Todas las columnas requeridas están presentes'
                );
            } else {
                $this->results[] = array(
                    'category' => 'database',
                    'test' => 'Estructura de tabla',
                    'status' => 'error',
                    'message' => 'Columnas faltantes: ' . implode(', ', $missing_columns)
                );
            }
            
            // Test data persistence
            $test_photo_id = 999999;
            $test_action = 'test_view';
            
            // Insert test data
            $wpdb->query($wpdb->prepare(
                "INSERT INTO $table_name 
                 (photo_id, interaction_type, interaction_count, user_ip, user_agent, created_at, updated_at)
                 VALUES (%d, %s, 1, %s, %s, %s, %s)
                 ON DUPLICATE KEY UPDATE 
                 interaction_count = interaction_count + 1,
                 updated_at = %s",
                $test_photo_id,
                $test_action,
                '127.0.0.1',
                'Test Agent',
                current_time('mysql'),
                current_time('mysql'),
                current_time('mysql')
            ));
            
            // Check if data was inserted
            $test_count = $wpdb->get_var($wpdb->prepare(
                "SELECT interaction_count FROM $table_name 
                 WHERE photo_id = %d AND interaction_type = %s",
                $test_photo_id, $test_action
            ));
            
            if ($test_count > 0) {
                $this->results[] = array(
                    'category' => 'database',
                    'test' => 'Persistencia de datos',
                    'status' => 'success',
                    'message' => "Datos se guardan correctamente (count: $test_count)"
                );
                
                // Clean up test data
                $wpdb->delete($table_name, array('photo_id' => $test_photo_id, 'interaction_type' => $test_action));
            } else {
                $this->results[] = array(
                    'category' => 'database',
                    'test' => 'Persistencia de datos',
                    'status' => 'error',
                    'message' => 'Los datos no se están guardando correctamente'
                );
            }
            
        } else {
            $this->results[] = array(
                'category' => 'database',
                'test' => 'Tabla de interacciones',
                'status' => 'error',
                'message' => "Tabla '$table_name' NO existe"
            );
        }
    }
    
    /**
     * Test posts automáticos functionality
     */
    private function test_posts_automaticos() {
        // Test if photos exist
        $photos = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => 5,
            'post_status' => 'inherit'
        ));
        
        if (!empty($photos)) {
            $this->results[] = array(
                'category' => 'posts_automaticos',
                'test' => 'Fotos disponibles',
                'status' => 'success',
                'message' => count($photos) . ' fotos encontradas para posts automáticos'
            );
        } else {
            $this->results[] = array(
                'category' => 'posts_automaticos',
                'test' => 'Fotos disponibles',
                'status' => 'warning',
                'message' => 'No hay fotos disponibles para posts automáticos'
            );
        }
        
        // Test options for categorized photos
        $published_photos = get_option('soloylibre_published_photos', array());
        $private_photos = get_option('soloylibre_private_photos', array());
        $unwanted_photos = get_option('soloylibre_unwanted_photos', array());
        $deleted_photos = get_option('soloylibre_deleted_photos', array());
        
        $this->results[] = array(
            'category' => 'posts_automaticos',
            'test' => 'Fotos categorizadas',
            'status' => 'info',
            'message' => sprintf(
                'Publicadas: %d, Privadas: %d, No deseadas: %d, Eliminadas: %d',
                count($published_photos),
                count($private_photos),
                count($unwanted_photos),
                count($deleted_photos)
            )
        );
    }
    
    /**
     * Test user interactions persistence
     */
    private function test_user_interactions_persistence() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'soloylibre_interactions';
        
        // Get some real interaction data
        $interactions = $wpdb->get_results(
            "SELECT photo_id, interaction_type, interaction_count 
             FROM $table_name 
             ORDER BY updated_at DESC 
             LIMIT 5"
        );
        
        if (!empty($interactions)) {
            $this->results[] = array(
                'category' => 'user_interactions',
                'test' => 'Datos de interacciones',
                'status' => 'success',
                'message' => count($interactions) . ' interacciones encontradas en base de datos'
            );
            
            foreach ($interactions as $interaction) {
                $this->results[] = array(
                    'category' => 'user_interactions',
                    'test' => "Foto {$interaction->photo_id}",
                    'status' => 'info',
                    'message' => "{$interaction->interaction_type}: {$interaction->interaction_count} veces"
                );
            }
        } else {
            $this->results[] = array(
                'category' => 'user_interactions',
                'test' => 'Datos de interacciones',
                'status' => 'warning',
                'message' => 'No hay datos de interacciones en la base de datos'
            );
        }
    }
    
    /**
     * Test nonce generation
     */
    private function test_nonce_generation() {
        $nonce = wp_create_nonce('soloylibre_wizard_nonce');
        
        if (!empty($nonce)) {
            $this->results[] = array(
                'category' => 'security',
                'test' => 'Generación de nonce',
                'status' => 'success',
                'message' => "Nonce generado correctamente: " . substr($nonce, 0, 10) . "..."
            );
            
            // Test nonce verification
            if (wp_verify_nonce($nonce, 'soloylibre_wizard_nonce')) {
                $this->results[] = array(
                    'category' => 'security',
                    'test' => 'Verificación de nonce',
                    'status' => 'success',
                    'message' => 'Nonce se verifica correctamente'
                );
            } else {
                $this->results[] = array(
                    'category' => 'security',
                    'test' => 'Verificación de nonce',
                    'status' => 'error',
                    'message' => 'Nonce no se verifica correctamente'
                );
            }
        } else {
            $this->results[] = array(
                'category' => 'security',
                'test' => 'Generación de nonce',
                'status' => 'error',
                'message' => 'No se pudo generar nonce'
            );
        }
    }
    
    /**
     * Test JavaScript loading
     */
    private function test_javascript_loading() {
        $js_files = array(
            'fullscreen-wizard.js' => 'Wizard JavaScript',
            'gallery-frontend.js' => 'Frontend JavaScript',
            'interactions.js' => 'Interactions JavaScript'
        );
        
        $plugin_dir = plugin_dir_path(__FILE__);
        
        foreach ($js_files as $file => $description) {
            $file_path = $plugin_dir . 'assets/js/' . $file;
            
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                $size = filesize($file_path);
                
                if ($size > 1000) {
                    $this->results[] = array(
                        'category' => 'javascript',
                        'test' => $description,
                        'status' => 'success',
                        'message' => "JavaScript cargado: $file ($size bytes)"
                    );
                } else {
                    $this->results[] = array(
                        'category' => 'javascript',
                        'test' => $description,
                        'status' => 'warning',
                        'message' => "JavaScript muy pequeño: $file ($size bytes)"
                    );
                }
            } else {
                $this->results[] = array(
                    'category' => 'javascript',
                    'test' => $description,
                    'status' => 'error',
                    'message' => "JavaScript no encontrado: $file"
                );
            }
        }
    }
    
    /**
     * Generate summary
     */
    private function generate_summary() {
        $summary = array(
            'total_tests' => count($this->results),
            'success' => 0,
            'warning' => 0,
            'error' => 0,
            'info' => 0,
            'categories' => array()
        );
        
        foreach ($this->results as $result) {
            $summary[$result['status']]++;
            
            if (!isset($summary['categories'][$result['category']])) {
                $summary['categories'][$result['category']] = array(
                    'success' => 0,
                    'warning' => 0,
                    'error' => 0,
                    'info' => 0
                );
            }
            $summary['categories'][$result['category']][$result['status']]++;
        }
        
        $summary['health_score'] = $summary['total_tests'] > 0 ? 
            round((($summary['success'] + $summary['info']) / $summary['total_tests']) * 100, 1) : 0;
        
        return $summary;
    }
}

// Run diagnostic if accessed directly
if (isset($_GET['run_diagnostic']) && $_GET['run_diagnostic'] == '1') {
    $diagnostic = new SoloYLibre_Complete_Diagnostic();
    $report = $diagnostic->run_complete_diagnostic();
    
    header('Content-Type: application/json');
    echo json_encode($report, JSON_PRETTY_PRINT);
    exit;
}
?>
