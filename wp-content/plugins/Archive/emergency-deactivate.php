<?php
/**
 * Emergency Plugin Deactivation - SoloYLibre Gallery Plugin
 * Deactivate plugin directly from database to resolve critical error
 * Developed by JEYKO AI for Jose L <PERSON>carnac<PERSON> (JoseTusabe)
 */

// Load WordPress
require_once('../../../wp-config.php');

echo "<h1>🚨 Desactivación de Emergencia</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Resolviendo error crítico</p>";

// Get current active plugins
$active_plugins = get_option('active_plugins', array());

echo "<h2>📋 Plugins Activos Actuales:</h2>";
echo "<ul>";
foreach ($active_plugins as $plugin) {
    echo "<li>$plugin</li>";
}
echo "</ul>";

// Find and remove our plugin
$plugin_to_remove = 'Archive/soloylibre-gallery-plugin.php';
$plugin_removed = false;

if (($key = array_search($plugin_to_remove, $active_plugins)) !== false) {
    unset($active_plugins[$key]);
    $plugin_removed = true;
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "⚠️ Plugin encontrado y removido de la lista activa";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "ℹ️ Plugin no encontrado en la lista activa";
    echo "</div>";
}

// Also check for variations
$plugin_variations = array(
    'soloylibre-gallery-plugin/soloylibre-gallery-plugin.php',
    'soloylibre-gallery/soloylibre-gallery-plugin.php',
    'Archive/soloylibre-gallery-plugin.php'
);

foreach ($plugin_variations as $variation) {
    if (($key = array_search($variation, $active_plugins)) !== false) {
        unset($active_plugins[$key]);
        $plugin_removed = true;
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "⚠️ Variación del plugin encontrada y removida: $variation";
        echo "</div>";
    }
}

// Update the active plugins list
if ($plugin_removed) {
    $active_plugins = array_values($active_plugins); // Re-index array
    update_option('active_plugins', $active_plugins);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ Plugin desactivado exitosamente";
    echo "</div>";
} else {
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "ℹ️ Plugin ya estaba desactivado";
    echo "</div>";
}

echo "<h2>🧹 Limpiando Opciones Problemáticas</h2>";

// Remove potentially problematic options
$options_to_remove = array(
    'soloylibre_gallery_activation_redirect',
    'soloylibre_gallery_do_activation_redirect',
    'soloylibre_gallery_flush_rewrite_rules',
    'soloylibre_gallery_temp_data',
    'soloylibre_gallery_cache_data'
);

foreach ($options_to_remove as $option) {
    if (delete_option($option)) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Opción removida: $option";
        echo "</div>";
    }
}

echo "<h2>🔄 Limpiando Caché y Rewrite Rules</h2>";

// Flush rewrite rules
flush_rewrite_rules();
echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "✅ Rewrite rules limpiadas";
echo "</div>";

// Clear any object cache
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Object cache limpiado";
    echo "</div>";
}

echo "<h2>📊 Estado Final</h2>";

$final_active_plugins = get_option('active_plugins', array());
echo "<h3>📋 Plugins Activos Después de la Limpieza:</h3>";
echo "<ul>";
foreach ($final_active_plugins as $plugin) {
    echo "<li>$plugin</li>";
}
echo "</ul>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
echo "<h3 style='color: #155724; margin: 0 0 15px 0;'>🎉 ¡Desactivación Completada!</h3>";
echo "<p style='margin: 0; color: #155724;'>El plugin ha sido desactivado y las opciones problemáticas han sido limpiadas.</p>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔧 Próximos Pasos:</h3>";
echo "<ol>";
echo "<li><strong>Verificar WordPress:</strong> Ve al dashboard para confirmar que funciona</li>";
echo "<li><strong>Revisar logs:</strong> Verifica que no hay más errores</li>";
echo "<li><strong>Reinstalar plugin:</strong> Si es necesario, reinstala una versión estable</li>";
echo "<li><strong>Configurar servidor:</strong> Aumenta memory_limit si es necesario</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🏠 Ir al Dashboard</a>";
echo "<a href='../../../wp-admin/plugins.php' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block;'>🔧 Gestionar Plugins</a>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Error crítico resuelto</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
