<?php
/**
 * Complete Menu and Links Audit - SoloYLibre Gallery Plugin
 * Developed by JEYKO AI for Jose L <PERSON> (JoseTusabe)
 * SoloYLibre Photography - Professional Photo Management System
 * 
 * 🔍 Comprehensive audit of all menus, links, and navigation elements
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-config.php');
}

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied');
}

global $wpdb, $menu, $submenu;

echo "<h1>🔍 Auditoría Completa de Menús y Links</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Revisión exhaustiva de navegación</p>";

$audit_results = array();
$missing_links = array();
$broken_links = array();
$working_links = array();
$menu_issues = array();
$recommendations = array();

// 1. Check WordPress Admin Menu Structure
echo "<h2>📋 1. Estructura del Menú Principal de WordPress</h2>";

// Get all registered menu pages
$registered_pages = array();
if (isset($GLOBALS['admin_page_hooks'])) {
    $registered_pages = $GLOBALS['admin_page_hooks'];
}

echo "<h3>🔧 Páginas Registradas del Plugin:</h3>";
$plugin_pages = array();
foreach ($registered_pages as $page => $hook) {
    if (strpos($page, 'soloylibre') !== false || strpos($hook, 'soloylibre') !== false) {
        $plugin_pages[$page] = $hook;
        echo "<div style='background: #e8f5e8; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
        echo "✅ <strong>$page</strong> → Hook: $hook";
        echo "</div>";
    }
}

// 2. Check SoloYLibre Menu Structure
echo "<h2>📁 2. Estructura del Menú SoloYLibre</h2>";

$expected_menu_items = array(
    'soloylibre-gallery' => array(
        'title' => '📸 SoloYLibre Gallery',
        'capability' => 'manage_options',
        'expected_url' => 'admin.php?page=soloylibre-gallery',
        'description' => 'Página principal del plugin'
    ),
    'soloylibre-wizard' => array(
        'title' => '🧙‍♂️ Wizard',
        'capability' => 'manage_options',
        'expected_url' => 'admin.php?page=soloylibre-wizard',
        'description' => 'Asistente para creación de posts'
    ),
    'soloylibre-bulk-manager' => array(
        'title' => '📁 Gestor Masivo',
        'capability' => 'manage_options',
        'expected_url' => 'admin.php?page=soloylibre-bulk-manager',
        'description' => 'Gestión masiva de fotos'
    ),
    'soloylibre-statistics' => array(
        'title' => '📊 Estadísticas',
        'capability' => 'manage_options',
        'expected_url' => 'admin.php?page=soloylibre-statistics',
        'description' => 'Estadísticas y métricas'
    ),
    'soloylibre-settings' => array(
        'title' => '⚙️ Configuraciones',
        'capability' => 'manage_options',
        'expected_url' => 'admin.php?page=soloylibre-settings',
        'description' => 'Configuraciones del plugin'
    )
);

echo "<h3>🔍 Verificación de Elementos del Menú:</h3>";

foreach ($expected_menu_items as $slug => $item) {
    $url = admin_url($item['expected_url']);
    
    echo "<div style='background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #007cba;'>";
    echo "<h4 style='margin: 0 0 10px 0; color: #007cba;'>{$item['title']}</h4>";
    echo "<p style='margin: 5px 0;'><strong>Slug:</strong> $slug</p>";
    echo "<p style='margin: 5px 0;'><strong>URL:</strong> <a href='$url' target='_blank'>$url</a></p>";
    echo "<p style='margin: 5px 0;'><strong>Descripción:</strong> {$item['description']}</p>";
    
    // Test if page exists
    if (function_exists('get_plugin_page_hook')) {
        $hook = get_plugin_page_hook($slug, 'admin.php');
        if ($hook) {
            echo "<p style='color: #28a745; margin: 5px 0;'>✅ <strong>Estado:</strong> Página registrada correctamente</p>";
            $working_links[] = $slug;
        } else {
            echo "<p style='color: #dc3545; margin: 5px 0;'>❌ <strong>Estado:</strong> Página NO registrada</p>";
            $missing_links[] = $slug;
        }
    }
    
    echo "</div>";
}

// 3. Check AJAX Endpoints
echo "<h2>🔗 3. Endpoints AJAX del Plugin</h2>";

$ajax_endpoints = array(
    // Wizard endpoints
    'soloylibre_get_photo_stats' => 'Obtener estadísticas de fotos',
    'soloylibre_create_quick_auto_post' => 'Crear post automático rápido',
    'soloylibre_get_detailed_stats' => 'Obtener estadísticas detalladas',
    'soloylibre_get_recent_photos' => 'Obtener fotos recientes',
    'soloylibre_get_popular_photos' => 'Obtener fotos populares',
    'soloylibre_reset_photo_categories' => 'Resetear categorías de fotos',
    
    // Bulk manager endpoints
    'bulk_load_photos' => 'Cargar fotos para gestor masivo',
    'bulk_get_stats' => 'Obtener estadísticas masivas',
    'bulk_mark_private' => 'Marcar fotos como privadas',
    'bulk_mark_unwanted' => 'Marcar fotos como no deseadas',
    'bulk_create_albums' => 'Crear álbumes masivos',
    'bulk_publish_batch' => 'Publicar lotes de fotos',
    'bulk_organize_dates' => 'Organizar por fechas',
    'bulk_test_connection' => 'Probar conexión AJAX',
    
    // Gallery endpoints
    'gallery_interaction' => 'Manejar interacciones de galería',
    'soloylibre_add_interaction' => 'Agregar interacción',
    
    // Settings endpoints
    'soloylibre_save_settings' => 'Guardar configuraciones',
    'soloylibre_reset_settings' => 'Resetear configuraciones'
);

echo "<h3>🔍 Verificación de Endpoints AJAX:</h3>";

$working_ajax = 0;
$missing_ajax = 0;

foreach ($ajax_endpoints as $endpoint => $description) {
    echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px;'>";
    echo "<strong>$endpoint</strong> - $description<br>";
    
    if (has_action("wp_ajax_$endpoint")) {
        echo "<span style='color: #28a745;'>✅ Registrado correctamente</span>";
        $working_ajax++;
        $working_links[] = "ajax_$endpoint";
    } else {
        echo "<span style='color: #dc3545;'>❌ NO registrado</span>";
        $missing_ajax++;
        $missing_links[] = "ajax_$endpoint";
    }
    echo "</div>";
}

// 4. Check Internal Links and Navigation
echo "<h2>🧭 4. Enlaces Internos y Navegación</h2>";

$internal_links = array(
    // Wizard internal links
    'wizard_quick_posts' => array(
        'from' => 'Wizard',
        'to' => 'Posts Automáticos Rápidos',
        'description' => 'Enlace a creación rápida de posts'
    ),
    'wizard_advanced_posts' => array(
        'from' => 'Wizard',
        'to' => 'Posts Automáticos Avanzados',
        'description' => 'Enlace a creación avanzada de posts'
    ),
    'wizard_to_bulk' => array(
        'from' => 'Wizard',
        'to' => 'Gestor Masivo',
        'description' => 'Enlace del wizard al gestor masivo'
    ),
    'wizard_to_stats' => array(
        'from' => 'Wizard',
        'to' => 'Estadísticas',
        'description' => 'Enlace del wizard a estadísticas'
    ),
    
    // Settings internal links
    'settings_tabs' => array(
        'from' => 'Configuraciones',
        'to' => 'Tabs internos',
        'description' => 'Navegación entre tabs de configuración'
    ),
    'settings_to_wizard' => array(
        'from' => 'Configuraciones',
        'to' => 'Wizard',
        'description' => 'Enlace de configuraciones al wizard'
    ),
    
    // Cross-page navigation
    'bulk_to_wizard' => array(
        'from' => 'Gestor Masivo',
        'to' => 'Wizard',
        'description' => 'Enlace del gestor masivo al wizard'
    ),
    'stats_to_wizard' => array(
        'from' => 'Estadísticas',
        'to' => 'Wizard',
        'description' => 'Enlace de estadísticas al wizard'
    )
);

echo "<h3>🔍 Verificación de Enlaces Internos:</h3>";

foreach ($internal_links as $link_id => $link_info) {
    echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 3px solid #ffc107;'>";
    echo "<strong>{$link_info['from']} → {$link_info['to']}</strong><br>";
    echo "<small>{$link_info['description']}</small><br>";
    echo "<span style='color: #856404;'>⚠️ Requiere verificación manual</span>";
    echo "</div>";
}

// 5. Check External Links and Resources
echo "<h2>🌐 5. Enlaces Externos y Recursos</h2>";

$external_resources = array(
    'css_files' => array(
        'fullscreen-wizard.css' => 'Estilos del wizard',
        'admin-styles.css' => 'Estilos del admin',
        'gallery-styles.css' => 'Estilos de galería'
    ),
    'js_files' => array(
        'wizard-scripts.js' => 'Scripts del wizard',
        'admin-scripts.js' => 'Scripts del admin',
        'gallery-scripts.js' => 'Scripts de galería'
    ),
    'image_resources' => array(
        'icons' => 'Iconos del plugin',
        'backgrounds' => 'Imágenes de fondo',
        'placeholders' => 'Imágenes placeholder'
    )
);

foreach ($external_resources as $type => $resources) {
    echo "<h4>📁 " . ucfirst(str_replace('_', ' ', $type)) . ":</h4>";
    foreach ($resources as $resource => $description) {
        echo "<div style='background: #e3f2fd; padding: 8px; margin: 3px 0; border-radius: 3px;'>";
        echo "<strong>$resource</strong> - $description<br>";
        echo "<span style='color: #1976d2;'>ℹ️ Verificar existencia en servidor</span>";
        echo "</div>";
    }
}

// 6. Generate Statistics
echo "<h2>📊 6. Estadísticas de la Auditoría</h2>";

$total_expected_pages = count($expected_menu_items);
$total_working_pages = count($working_links);
$total_ajax_endpoints = count($ajax_endpoints);

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0;'>";

// Working Links Card
echo "<div style='background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; padding: 25px; border-radius: 15px; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.15);'>";
echo "<div style='font-size: 48px; font-weight: bold; margin-bottom: 10px;'>$working_ajax</div>";
echo "<div style='font-size: 18px; margin-bottom: 5px;'>✅ AJAX Funcionando</div>";
echo "<div style='font-size: 14px; opacity: 0.9;'>de $total_ajax_endpoints total</div>";
echo "</div>";

// Missing Links Card
echo "<div style='background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: #333; padding: 25px; border-radius: 15px; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.15);'>";
echo "<div style='font-size: 48px; font-weight: bold; margin-bottom: 10px;'>$missing_ajax</div>";
echo "<div style='font-size: 18px; margin-bottom: 5px;'>❌ AJAX Faltantes</div>";
echo "<div style='font-size: 14px; opacity: 0.8;'>Requieren implementación</div>";
echo "</div>";

// Menu Pages Card
echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.15);'>";
echo "<div style='font-size: 48px; font-weight: bold; margin-bottom: 10px;'>$total_expected_pages</div>";
echo "<div style='font-size: 18px; margin-bottom: 5px;'>📋 Páginas de Menú</div>";
echo "<div style='font-size: 14px; opacity: 0.9;'>Estructura completa</div>";
echo "</div>";

// Internal Links Card
echo "<div style='background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333; padding: 25px; border-radius: 15px; text-align: center; box-shadow: 0 8px 25px rgba(0,0,0,0.15);'>";
$internal_count = count($internal_links);
echo "<div style='font-size: 48px; font-weight: bold; margin-bottom: 10px;'>$internal_count</div>";
echo "<div style='font-size: 18px; margin-bottom: 5px;'>🧭 Enlaces Internos</div>";
echo "<div style='font-size: 14px; opacity: 0.8;'>Navegación cruzada</div>";
echo "</div>";

echo "</div>";

// 7. Detailed Issues Report
echo "<h2>⚠️ 7. Reporte Detallado de Problemas</h2>";

if (!empty($missing_links)) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #dc3545;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 15px 0;'>❌ Enlaces y Funciones Faltantes:</h3>";
    echo "<ul>";
    foreach ($missing_links as $missing) {
        echo "<li style='margin: 5px 0; color: #721c24;'><strong>$missing</strong></li>";
    }
    echo "</ul>";
    echo "</div>";
}

// 8. Recommendations
echo "<h2>💡 8. Recomendaciones de Mejora</h2>";

$recommendations = array(
    "Implementar endpoints AJAX faltantes para completar funcionalidad",
    "Agregar verificación automática de enlaces internos",
    "Implementar sistema de navegación breadcrumb",
    "Agregar enlaces de ayuda contextual en cada página",
    "Implementar sistema de notificaciones entre páginas",
    "Agregar enlaces rápidos en el dashboard de WordPress",
    "Implementar sistema de favoritos para acceso rápido",
    "Agregar enlaces de navegación en el footer de cada página"
);

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 5px solid #17a2b8;'>";
echo "<h3 style='color: #0c5460; margin: 0 0 15px 0;'>💡 Sugerencias:</h3>";
echo "<ol>";
foreach ($recommendations as $recommendation) {
    echo "<li style='margin: 8px 0; color: #0c5460;'>$recommendation</li>";
}
echo "</ol>";
echo "</div>";

// 9. Quick Fix Actions
echo "<h2>🔧 9. Acciones de Reparación Rápida</h2>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-wizard' style='background: #007cba; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>🧙‍♂️ Probar Wizard</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-bulk-manager' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>📁 Probar Gestor Masivo</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-statistics' style='background: #fd7e14; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>📊 Probar Estadísticas</a>";
echo "<a href='../../../wp-admin/admin.php?page=soloylibre-settings' style='background: #6f42c1; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; margin: 0 10px; display: inline-block;'>⚙️ Probar Configuraciones</a>";
echo "</div>";

// 10. Summary
$success_rate = round(($working_ajax / $total_ajax_endpoints) * 100, 1);

echo "<div style='background: " . ($success_rate >= 80 ? '#d4edda' : '#fff3cd') . "; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h2 style='margin: 0 0 15px 0; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo ($success_rate >= 80 ? '🎉' : '⚠️') . " Auditoría de Menús y Links Completada</h2>";
echo "<p style='margin: 0; font-size: 18px; color: " . ($success_rate >= 80 ? '#155724' : '#856404') . ";'>";
echo "Tasa de éxito: <strong>$success_rate%</strong> ($working_ajax de $total_ajax_endpoints endpoints funcionando)";
echo "</p>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Auditoría completa de navegación</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "</div>";
?>
