<?php
/**
 * SoloYLibre Gallery Pro - Configuración Inicial
 * Script de configuración automática para Jose L Encarnacion (JoseTusabe)
 * Desarrollado por JEYKO AI
 */

echo "<h1>⚙️ Configuración Inicial - SoloYLibre Gallery Pro v3.0.0</h1>";
echo "<p><strong>Fotógrafo:</strong> <PERSON> (JoseTusabe)</p>";
echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Configuración para localhost:8888
$WORDPRESS_URL = 'http://localhost:8888/wp/wordpress/';
$ADMIN_URL = $WORDPRESS_URL . 'wp-admin/';

echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h2>🎯 Configuración Detectada</h2>";
echo "<p><strong>WordPress URL:</strong> <a href='$WORDPRESS_URL' target='_blank'>$WORDPRESS_URL</a></p>";
echo "<p><strong>Admin URL:</strong> <a href='$ADMIN_URL' target='_blank'>$ADMIN_URL</a></p>";
echo "</div>";

echo "<h2>📋 Verificación de Archivos Críticos</h2>";

// Archivos críticos para el funcionamiento
$critical_files = array(
    'soloylibre-gallery-plugin.php' => 'Archivo principal del plugin',
    'includes/class-database.php' => 'Gestor de base de datos',
    'includes/class-admin.php' => 'Clase de administración',
    'includes/class-dashboard.php' => 'Dashboard principal',
    'assets/css/admin.css' => 'Estilos de administración',
    'assets/js/admin.js' => 'JavaScript de administración'
);

$all_critical_present = true;

echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);'>";
echo "<thead style='background: #667eea; color: white;'>";
echo "<tr>";
echo "<th style='padding: 12px; border: 1px solid #ddd;'>Estado</th>";
echo "<th style='padding: 12px; border: 1px solid #ddd;'>Archivo</th>";
echo "<th style='padding: 12px; border: 1px solid #ddd;'>Descripción</th>";
echo "</tr>";
echo "</thead>";
echo "<tbody>";

foreach ($critical_files as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? "✅" : "❌";
    
    if (!$exists) {
        $all_critical_present = false;
    }
    
    echo "<tr>";
    echo "<td style='text-align: center; font-size: 18px; padding: 12px; border: 1px solid #ddd;'>$status</td>";
    echo "<td style='padding: 12px; border: 1px solid #ddd;'><code>$file</code></td>";
    echo "<td style='padding: 12px; border: 1px solid #ddd;'>$description</td>";
    echo "</tr>";
}

echo "</tbody>";
echo "</table>";

if ($all_critical_present) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✅ Todos los Archivos Críticos Presentes</h3>";
    echo "<p>El plugin está listo para ser instalado y configurado.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>❌ Archivos Críticos Faltantes</h3>";
    echo "<p>Algunos archivos críticos no están presentes. El plugin puede no funcionar correctamente.</p>";
    echo "</div>";
}

echo "<h2>🚀 Pasos de Instalación</h2>";

echo "<div style='background: #f0f8ff; padding: 25px; border-radius: 12px; border-left: 4px solid #667eea; margin: 20px 0;'>";
echo "<h3>📁 Paso 1: Copiar Plugin a WordPress</h3>";
echo "<div style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 6px; font-family: monospace; margin: 10px 0;'>";
echo "cp -r . /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/soloylibre-gallery-pro/";
echo "</div>";
echo "<p><strong>Importante:</strong> Asegúrate de copiar TODOS los archivos del plugin.</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 25px; border-radius: 12px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
echo "<h3>🔐 Paso 2: Dar Permisos Correctos</h3>";
echo "<div style='background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 6px; font-family: monospace; margin: 10px 0;'>";
echo "chmod -R 755 /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/soloylibre-gallery-pro/";
echo "</div>";
echo "<p>Esto asegura que WordPress pueda leer y ejecutar todos los archivos del plugin.</p>";
echo "</div>";

echo "<div style='background: #e8f5e8; padding: 25px; border-radius: 12px; border-left: 4px solid #28a745; margin: 20px 0;'>";
echo "<h3>🔌 Paso 3: Activar Plugin en WordPress</h3>";
echo "<ol>";
echo "<li>Ve a <a href='$ADMIN_URL" . "plugins.php' target='_blank' style='color: #667eea; font-weight: bold;'>WordPress Admin → Plugins</a></li>";
echo "<li>Busca 'SoloYLibre Gallery Pro v3.0.0'</li>";
echo "<li>Haz clic en 'Activar'</li>";
echo "<li>El plugin creará automáticamente:</li>";
echo "<ul>";
echo "<li>Usuario: admin_soloylibre</li>";
echo "<li>Tablas de base de datos</li>";
echo "<li>Datos de ejemplo</li>";
echo "</ul>";
echo "</ol>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 12px; margin: 20px 0;'>";
echo "<h3>🎯 Paso 4: Acceder al Dashboard</h3>";
echo "<p>Una vez activado el plugin, podrás acceder a:</p>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;'>";
echo "<a href='$ADMIN_URL" . "admin.php?page=soloylibre-gallery-dashboard' target='_blank' style='background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; text-decoration: none; color: white; text-align: center; display: block;'>📊 Dashboard</a>";
echo "<a href='$ADMIN_URL" . "admin.php?page=soloylibre-gallery-wizard' target='_blank' style='background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; text-decoration: none; color: white; text-align: center; display: block;'>🧙‍♂️ Asistente</a>";
echo "<a href='$ADMIN_URL" . "admin.php?page=soloylibre-gallery-photos' target='_blank' style='background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; text-decoration: none; color: white; text-align: center; display: block;'>📸 Fotos</a>";
echo "<a href='$ADMIN_URL" . "admin.php?page=soloylibre-gallery-albums' target='_blank' style='background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; text-decoration: none; color: white; text-align: center; display: block;'>📁 Álbumes</a>";
echo "</div>";
echo "</div>";

echo "<h2>🔐 Credenciales Automáticas</h2>";

echo "<div style='background: rgba(255,255,255,0.9); padding: 25px; border-radius: 12px; border-left: 4px solid #764ba2; margin: 20px 0;'>";
echo "<h3>👤 Usuario Administrador</h3>";
echo "<p>El plugin crea automáticamente un usuario administrador con la siguiente información:</p>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 15px;'>";
echo "<div>";
echo "<p><strong>Usuario:</strong> admin_soloylibre</p>";
echo "<p><strong>Contraseña:</strong> JoseTusabe2025!</p>";
echo "</div>";
echo "<div>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Rol:</strong> Administrador</p>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<h2>📸 Información del Fotógrafo Integrada</h2>";

echo "<div style='background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 25px; border-radius: 12px; margin: 20px 0;'>";
echo "<h3>🎨 Personalización Completa</h3>";
echo "<p>Todo el plugin está personalizado específicamente para Jose L Encarnacion (JoseTusabe):</p>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 15px;'>";

echo "<div style='background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;'>";
echo "<h4>👤 Datos Personales</h4>";
echo "<p>• Nombre: Jose L Encarnacion</p>";
echo "<p>• Alias: JoseTusabe</p>";
echo "<p>• Marca: SoloYLibre Photography</p>";
echo "<p>• Ubicación: San José de Ocoa, Dom. Rep. / USA</p>";
echo "</div>";

echo "<div style='background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;'>";
echo "<h4>📞 Contacto</h4>";
echo "<p>• Teléfono: ************</p>";
echo "<p>• Email: <EMAIL></p>";
echo "<p>• Servidor: Synology RS3618xs</p>";
echo "<p>• Recursos: 56GB RAM, 36TB Storage</p>";
echo "</div>";

echo "<div style='background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px;'>";
echo "<h4>🌐 Sitios Web</h4>";
echo "<p>• josetusabe.com</p>";
echo "<p>• soloylibre.com</p>";
echo "<p>• 1and1photo.com</p>";
echo "<p>• joselencarnacion.com</p>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "<h2>🎯 Características Principales</h2>";

$features = array(
    '📸 Gestión de Fotos' => 'Sistema de 4 estados, carga masiva, organización automática',
    '📁 Álbumes Publicables' => 'Creación, edición y publicación de álbumes con un clic',
    '🎨 Dashboard Moderno' => 'Interfaz glassmorphism con información personalizada',
    '💝 Interacciones' => '6 tipos de reacciones y engagement automático',
    '🔒 Seguridad' => 'Autenticación 2FA, protección multinivel',
    '📊 Analytics' => 'Métricas en tiempo real y estadísticas detalladas'
);

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";
foreach ($features as $title => $description) {
    echo "<div style='background: rgba(255,255,255,0.9); padding: 20px; border-radius: 12px; border-left: 4px solid #667eea;'>";
    echo "<h4 style='margin: 0 0 10px 0; color: #333;'>$title</h4>";
    echo "<p style='margin: 0; color: #666; font-size: 14px;'>$description</p>";
    echo "</div>";
}
echo "</div>";

echo "<h2>🚀 ¡Listo para Comenzar!</h2>";

echo "<div style='background: #e8f5e8; padding: 30px; border-radius: 16px; border-left: 4px solid #28a745; margin: 20px 0; text-align: center;'>";
echo "<h3 style='margin: 0 0 20px 0; color: #155724;'>🎉 Plugin Configurado y Listo</h3>";
echo "<p style='font-size: 16px; margin: 0 0 25px 0;'>SoloYLibre Gallery Pro v3.0.0 está completamente configurado para Jose L Encarnacion (JoseTusabe).</p>";
echo "<a href='$ADMIN_URL" . "plugins.php' target='_blank' style='background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block; margin: 5px;'>🔌 Activar Plugin</a>";
echo "<a href='$ADMIN_URL" . "admin.php?page=soloylibre-gallery-dashboard' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; display: inline-block; margin: 5px;'>📊 Ver Dashboard</a>";
echo "</div>";

echo "<hr style='margin: 40px 0; border: none; border-top: 2px solid #ddd;'>";
echo "<div style='text-align: center; color: #666;'>";
echo "<p><em>⚙️ Configuración inicial por JEYKO AI para Jose L Encarnacion (JoseTusabe)</em></p>";
echo "<p><em>📸 SoloYLibre Photography - San José de Ocoa, Dom. Rep. 🇩🇴 / USA 🇺🇸</em></p>";
echo "<p><em>🚀 Plugin Version 3.0.0 - Configurado para localhost:8888</em></p>";
echo "</div>";

// CSS para mejorar la apariencia
echo "<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 0;
    padding: 40px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

h1 { 
    text-align: center;
    color: #333;
    font-size: 32px;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

h2 { 
    color: #333; 
    margin-top: 40px;
    font-size: 24px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

h3, h4 {
    color: #333;
}

p { 
    margin: 10px 0; 
}

a { 
    color: #667eea; 
    text-decoration: none; 
    transition: all 0.3s ease;
}

a:hover { 
    color: #764ba2;
}

code {
    background: rgba(0,0,0,0.1);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 14px;
}

table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

th, td {
    text-align: left;
}

ul, ol { 
    margin: 15px 0; 
    padding-left: 25px;
}

li {
    margin: 8px 0;
}

strong {
    color: #333;
}
</style>";
?>
