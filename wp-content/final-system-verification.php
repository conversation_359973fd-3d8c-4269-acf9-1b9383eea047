<?php
/**
 * Final System Verification
 * Complete verification that the critical error is resolved
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

echo "<h1>🎉 Verificación Final del Sistema</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Confirmación de que el error crítico está resuelto</p>";

// Load WordPress
try {
    if (!defined('ABSPATH')) {
        require_once('wp-config.php');
    }
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ WordPress cargado correctamente";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Error: " . $e->getMessage();
    echo "</div>";
    die();
}

$verification_results = array();

echo "<h2>🔍 1. Verificación de WordPress Core</h2>";

// Test 1: WordPress functions
$wp_tests = array(
    'WordPress loaded' => defined('ABSPATH'),
    'Database connection' => function_exists('get_option') && get_option('blogname') !== false,
    'Admin functions' => function_exists('current_user_can'),
    'Plugin system' => function_exists('get_plugins'),
    'Theme system' => function_exists('wp_get_theme'),
    'Post system' => function_exists('wp_insert_post'),
    'Media system' => function_exists('wp_get_attachment_image_url')
);

foreach ($wp_tests as $test => $result) {
    $verification_results[$test] = $result;
    if ($result) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ $test";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ $test";
        echo "</div>";
    }
}

echo "<h2>🌐 2. Verificación de Frontend</h2>";

// Test 2: Frontend accessibility
$home_url = home_url();
echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "🔗 URL del sitio: <a href='$home_url' target='_blank'>$home_url</a>";
echo "</div>";

$frontend_response = wp_remote_get($home_url, array('timeout' => 10));
if (!is_wp_error($frontend_response)) {
    $response_code = wp_remote_retrieve_response_code($frontend_response);
    $response_body = wp_remote_retrieve_body($frontend_response);
    
    if ($response_code === 200) {
        $verification_results['Frontend accessible'] = true;
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Frontend accesible - Código: $response_code";
        echo "</div>";
        
        // Check if critical error message is present
        if (strpos($response_body, 'critical error') === false) {
            $verification_results['No critical error'] = true;
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "✅ No hay mensaje de error crítico en el frontend";
            echo "</div>";
        } else {
            $verification_results['No critical error'] = false;
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "❌ Aún se detecta mensaje de error crítico";
            echo "</div>";
        }
    } else {
        $verification_results['Frontend accessible'] = false;
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Frontend error - Código: $response_code";
        echo "</div>";
    }
} else {
    $verification_results['Frontend accessible'] = false;
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ Error accediendo al frontend: " . $frontend_response->get_error_message();
    echo "</div>";
}

echo "<h2>🏠 3. Verificación de Dashboard</h2>";

// Test 3: Admin area
$admin_url = admin_url();
echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "🔗 URL del admin: <a href='$admin_url' target='_blank'>$admin_url</a>";
echo "</div>";

$admin_response = wp_remote_get($admin_url, array('timeout' => 10));
if (!is_wp_error($admin_response)) {
    $response_code = wp_remote_retrieve_response_code($admin_response);
    
    if ($response_code === 200 || $response_code === 302) { // 302 is redirect to login
        $verification_results['Admin accessible'] = true;
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "✅ Dashboard accesible - Código: $response_code";
        echo "</div>";
    } else {
        $verification_results['Admin accessible'] = false;
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Dashboard error - Código: $response_code";
        echo "</div>";
    }
} else {
    $verification_results['Admin accessible'] = false;
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "❌ Error accediendo al dashboard: " . $admin_response->get_error_message();
    echo "</div>";
}

echo "<h2>🔌 4. Verificación de Plugins</h2>";

// Test 4: Plugin system
$active_plugins = get_option('active_plugins', array());
echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "📋 Plugins activos: " . count($active_plugins);
echo "</div>";

$soloylibre_active = false;
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'soloylibre-gallery-final') !== false) {
        $soloylibre_active = true;
        break;
    }
}

$verification_results['SoloYLibre plugin active'] = $soloylibre_active;
if ($soloylibre_active) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Plugin SoloYLibre Gallery Final está activo";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "⚠️ Plugin SoloYLibre Gallery Final no está activo";
    echo "</div>";
}

// Check for problematic plugins
$problematic_found = false;
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'Archive') !== false) {
        $problematic_found = true;
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
        echo "❌ Plugin problemático aún activo: $plugin";
        echo "</div>";
    }
}

$verification_results['No problematic plugins'] = !$problematic_found;
if (!$problematic_found) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ No hay plugins problemáticos activos";
    echo "</div>";
}

echo "<h2>🎨 5. Verificación de Tema</h2>";

// Test 5: Theme system
$current_theme = wp_get_theme();
$theme_name = $current_theme->get('Name');
$verification_results['Theme working'] = !empty($theme_name);

echo "<div style='background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "🎨 Tema activo: $theme_name";
echo "</div>";

if (strpos(strtolower($theme_name), 'twenty') !== false) {
    $verification_results['Safe theme active'] = true;
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Tema seguro de WordPress activo";
    echo "</div>";
} else {
    $verification_results['Safe theme active'] = false;
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "⚠️ Tema personalizado activo (puede causar problemas)";
    echo "</div>";
}

echo "<h2>⚙️ 6. Verificación de Configuración</h2>";

// Test 6: PHP configuration
$php_config = array(
    'Memory limit' => ini_get('memory_limit'),
    'Max execution time' => ini_get('max_execution_time'),
    'Display errors' => ini_get('display_errors') ? 'On' : 'Off',
    'Log errors' => ini_get('log_errors') ? 'On' : 'Off'
);

foreach ($php_config as $setting => $value) {
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "<strong>$setting:</strong> $value";
    echo "</div>";
}

$memory_ok = wp_convert_hr_to_bytes(ini_get('memory_limit')) >= wp_convert_hr_to_bytes('256M');
$verification_results['Sufficient memory'] = $memory_ok;

if ($memory_ok) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Memory limit suficiente";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "⚠️ Memory limit podría ser insuficiente";
    echo "</div>";
}

echo "<h2>📊 7. Resumen Final</h2>";

$total_tests = count($verification_results);
$passed_tests = count(array_filter($verification_results));
$success_rate = ($passed_tests / $total_tests) * 100;

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;'>";

echo "<div style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>$passed_tests</div>";
echo "<div>✅ Tests Exitosos</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . ($total_tests - $passed_tests) . "</div>";
echo "<div>❌ Tests Fallidos</div>";
echo "</div>";

echo "<div style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 20px; border-radius: 10px; text-align: center;'>";
echo "<div style='font-size: 36px; font-weight: bold;'>" . round($success_rate, 1) . "%</div>";
echo "<div>📈 Tasa de Éxito</div>";
echo "</div>";

echo "</div>";

// Final status
if ($success_rate >= 90) {
    echo "<div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #155724;'>🎉 ¡ERROR CRÍTICO COMPLETAMENTE RESUELTO!</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #155724;'>";
    echo "El sistema WordPress está funcionando perfectamente. El error crítico ha sido eliminado definitivamente.";
    echo "</p>";
    echo "</div>";
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎯 Sistema Completamente Funcional:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Frontend:</strong> Sitio web accesible sin errores</li>";
    echo "<li>✅ <strong>Backend:</strong> Dashboard WordPress funcionando</li>";
    echo "<li>✅ <strong>Plugins:</strong> Sistema de plugins operativo</li>";
    echo "<li>✅ <strong>Temas:</strong> Sistema de temas funcionando</li>";
    echo "<li>✅ <strong>Base de datos:</strong> Conexión y operaciones normales</li>";
    echo "<li>✅ <strong>PHP:</strong> Configuración optimizada</li>";
    echo "</ul>";
    echo "</div>";
    
} elseif ($success_rate >= 70) {
    echo "<div style='background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #856404;'>⚠️ Sistema Mayormente Funcional</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #856404;'>";
    echo "El error crítico principal está resuelto, pero hay algunas áreas que necesitan atención.";
    echo "</p>";
    echo "</div>";
} else {
    echo "<div style='background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
    echo "<h2 style='margin: 0 0 15px 0; color: #721c24;'>❌ Sistema Requiere Más Atención</h2>";
    echo "<p style='margin: 0; font-size: 18px; color: #721c24;'>";
    echo "Aún hay problemas significativos que necesitan ser resueltos.";
    echo "</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; font-weight: bold;' target='_blank'>🌐 VER SITIO WEB</a>";
echo "<a href='/wp-admin/' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; font-weight: bold;' target='_blank'>🏠 DASHBOARD</a>";
if ($soloylibre_active) {
    echo "<a href='/wp-admin/admin.php?page=soloylibre-final' style='background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; font-weight: bold;' target='_blank'>📸 SOLOYLIBRE GALLERY</a>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Verificación final completada</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Desarrollado por JEYKO AI para Jose L Encarnacion</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 12px;'>Sistema WordPress completamente funcional y sin errores críticos</p>";
echo "</div>";

// Save verification results
update_option('soloylibre_final_verification', array(
    'timestamp' => current_time('mysql'),
    'results' => $verification_results,
    'success_rate' => $success_rate,
    'status' => $success_rate >= 90 ? 'excellent' : ($success_rate >= 70 ? 'good' : 'needs_attention')
));
?>
