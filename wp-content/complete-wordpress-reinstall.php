<?php
/**
 * Complete WordPress Reinstallation
 * Fresh WordPress installation with preserved data
 * Developed by JEYKO AI for Jose <PERSON> (JoseTusabe)
 */

echo "<h1>🔄 Complete WordPress Reinstallation</h1>";
echo "<p>🇩🇴 JoseTusabe Photography - Fresh WordPress installation to eliminate critical error</p>";

// Get database credentials from current wp-config
$wp_config_path = dirname(__FILE__) . '/../wp-config.php';
if (file_exists($wp_config_path)) {
    $config_content = file_get_contents($wp_config_path);
    
    // Extract database credentials
    preg_match("/define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/", $config_content, $db_name_match);
    preg_match("/define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/", $config_content, $db_user_match);
    preg_match("/define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/", $config_content, $db_pass_match);
    preg_match("/define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]\s*\)/", $config_content, $db_host_match);
    
    $db_name = $db_name_match[1] ?? 'wordpress';
    $db_user = $db_user_match[1] ?? 'root';
    $db_pass = $db_pass_match[1] ?? '';
    $db_host = $db_host_match[1] ?? 'localhost';
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ Database credentials extracted successfully";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ wp-config.php not found";
    echo "</div>";
    die();
}

// Connect to database
$mysqli = new mysqli($db_host, $db_user, $db_pass, $db_name);
if ($mysqli->connect_error) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Database connection failed: " . $mysqli->connect_error;
    echo "</div>";
    die();
}

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "✅ Database connection established";
echo "</div>";

echo "<h2>💾 1. Backing Up Essential Data</h2>";

// Backup essential WordPress data
$backup_data = array();

// Get site info
$site_info = $mysqli->query("SELECT option_name, option_value FROM wp_options WHERE option_name IN ('blogname', 'blogdescription', 'admin_email', 'users_can_register', 'default_role')");
if ($site_info) {
    while ($row = $site_info->fetch_assoc()) {
        $backup_data['site_options'][$row['option_name']] = $row['option_value'];
    }
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Site settings backed up";
    echo "</div>";
}

// Get users
$users = $mysqli->query("SELECT * FROM wp_users");
if ($users) {
    while ($row = $users->fetch_assoc()) {
        $backup_data['users'][] = $row;
    }
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Users backed up: " . count($backup_data['users']);
    echo "</div>";
}

// Get user meta
$usermeta = $mysqli->query("SELECT * FROM wp_usermeta");
if ($usermeta) {
    while ($row = $usermeta->fetch_assoc()) {
        $backup_data['usermeta'][] = $row;
    }
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ User metadata backed up";
    echo "</div>";
}

// Get posts (keep important ones)
$posts = $mysqli->query("SELECT * FROM wp_posts WHERE post_type IN ('post', 'page', 'attachment') AND post_status != 'trash'");
if ($posts) {
    while ($row = $posts->fetch_assoc()) {
        $backup_data['posts'][] = $row;
    }
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Posts and media backed up: " . count($backup_data['posts']);
    echo "</div>";
}

// Get SoloYLibre specific data
$soloylibre_options = $mysqli->query("SELECT option_name, option_value FROM wp_options WHERE option_name LIKE 'soloylibre_%'");
if ($soloylibre_options) {
    while ($row = $soloylibre_options->fetch_assoc()) {
        $backup_data['soloylibre_data'][$row['option_name']] = $row['option_value'];
    }
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ SoloYLibre data backed up";
    echo "</div>";
}

// Save backup to file
$backup_file = dirname(__FILE__) . '/wordpress-backup-' . date('Y-m-d-H-i-s') . '.json';
file_put_contents($backup_file, json_encode($backup_data, JSON_PRETTY_PRINT));

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "💾 Complete backup saved to: " . basename($backup_file);
echo "</div>";

echo "<h2>🗑️ 2. Cleaning Database</h2>";

// Get all WordPress tables
$tables_result = $mysqli->query("SHOW TABLES LIKE 'wp_%'");
$tables = array();
while ($row = $tables_result->fetch_array()) {
    $tables[] = $row[0];
}

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "⚠️ Found " . count($tables) . " WordPress tables to clean";
echo "</div>";

// Drop all WordPress tables
foreach ($tables as $table) {
    $mysqli->query("DROP TABLE IF EXISTS `$table`");
    echo "<div style='background: #f8d7da; padding: 5px 10px; border-radius: 3px; margin: 2px 0; font-size: 12px;'>";
    echo "🗑️ Dropped: $table";
    echo "</div>";
}

echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "✅ Database completely cleaned";
echo "</div>";

echo "<h2>🆕 3. Creating Fresh WordPress Installation</h2>";

// Create fresh WordPress tables
$wp_sql = "
CREATE TABLE wp_users (
  ID bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  user_login varchar(60) NOT NULL DEFAULT '',
  user_pass varchar(255) NOT NULL DEFAULT '',
  user_nicename varchar(50) NOT NULL DEFAULT '',
  user_email varchar(100) NOT NULL DEFAULT '',
  user_url varchar(100) NOT NULL DEFAULT '',
  user_registered datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  user_activation_key varchar(255) NOT NULL DEFAULT '',
  user_status int(11) NOT NULL DEFAULT '0',
  display_name varchar(250) NOT NULL DEFAULT '',
  PRIMARY KEY (ID),
  KEY user_login_key (user_login),
  KEY user_nicename (user_nicename),
  KEY user_email (user_email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_usermeta (
  umeta_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  user_id bigint(20) unsigned NOT NULL DEFAULT '0',
  meta_key varchar(255) DEFAULT NULL,
  meta_value longtext,
  PRIMARY KEY (umeta_id),
  KEY user_id (user_id),
  KEY meta_key (meta_key(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_posts (
  ID bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  post_author bigint(20) unsigned NOT NULL DEFAULT '0',
  post_date datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  post_date_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  post_content longtext NOT NULL,
  post_title text NOT NULL,
  post_excerpt text NOT NULL,
  post_status varchar(20) NOT NULL DEFAULT 'publish',
  comment_status varchar(20) NOT NULL DEFAULT 'open',
  ping_status varchar(20) NOT NULL DEFAULT 'open',
  post_password varchar(255) NOT NULL DEFAULT '',
  post_name varchar(200) NOT NULL DEFAULT '',
  to_ping text NOT NULL,
  pinged text NOT NULL,
  post_modified datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  post_modified_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  post_content_filtered longtext NOT NULL,
  post_parent bigint(20) unsigned NOT NULL DEFAULT '0',
  guid varchar(255) NOT NULL DEFAULT '',
  menu_order int(11) NOT NULL DEFAULT '0',
  post_type varchar(20) NOT NULL DEFAULT 'post',
  post_mime_type varchar(100) NOT NULL DEFAULT '',
  comment_count bigint(20) NOT NULL DEFAULT '0',
  PRIMARY KEY (ID),
  KEY post_name (post_name(191)),
  KEY type_status_date (post_type,post_status,post_date,ID),
  KEY post_parent (post_parent),
  KEY post_author (post_author)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_postmeta (
  meta_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  post_id bigint(20) unsigned NOT NULL DEFAULT '0',
  meta_key varchar(255) DEFAULT NULL,
  meta_value longtext,
  PRIMARY KEY (meta_id),
  KEY post_id (post_id),
  KEY meta_key (meta_key(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_options (
  option_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  option_name varchar(191) NOT NULL DEFAULT '',
  option_value longtext NOT NULL,
  autoload varchar(20) NOT NULL DEFAULT 'yes',
  PRIMARY KEY (option_id),
  UNIQUE KEY option_name (option_name),
  KEY autoload (autoload)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_comments (
  comment_ID bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  comment_post_ID bigint(20) unsigned NOT NULL DEFAULT '0',
  comment_author tinytext NOT NULL,
  comment_author_email varchar(100) NOT NULL DEFAULT '',
  comment_author_url varchar(200) NOT NULL DEFAULT '',
  comment_author_IP varchar(100) NOT NULL DEFAULT '',
  comment_date datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  comment_date_gmt datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  comment_content text NOT NULL,
  comment_karma int(11) NOT NULL DEFAULT '0',
  comment_approved varchar(20) NOT NULL DEFAULT '1',
  comment_agent varchar(255) NOT NULL DEFAULT '',
  comment_type varchar(20) NOT NULL DEFAULT 'comment',
  comment_parent bigint(20) unsigned NOT NULL DEFAULT '0',
  user_id bigint(20) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (comment_ID),
  KEY comment_post_ID (comment_post_ID),
  KEY comment_approved_date_gmt (comment_approved,comment_date_gmt),
  KEY comment_date_gmt (comment_date_gmt),
  KEY comment_parent (comment_parent),
  KEY comment_author_email (comment_author_email(10))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_commentmeta (
  meta_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  comment_id bigint(20) unsigned NOT NULL DEFAULT '0',
  meta_key varchar(255) DEFAULT NULL,
  meta_value longtext,
  PRIMARY KEY (meta_id),
  KEY comment_id (comment_id),
  KEY meta_key (meta_key(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_terms (
  term_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  name varchar(200) NOT NULL DEFAULT '',
  slug varchar(200) NOT NULL DEFAULT '',
  term_group bigint(10) NOT NULL DEFAULT 0,
  PRIMARY KEY (term_id),
  KEY slug (slug(191)),
  KEY name (name(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_termmeta (
  meta_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  term_id bigint(20) unsigned NOT NULL DEFAULT 0,
  meta_key varchar(255) DEFAULT NULL,
  meta_value longtext,
  PRIMARY KEY (meta_id),
  KEY term_id (term_id),
  KEY meta_key (meta_key(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_term_taxonomy (
  term_taxonomy_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  term_id bigint(20) unsigned NOT NULL DEFAULT 0,
  taxonomy varchar(32) NOT NULL DEFAULT '',
  description longtext NOT NULL,
  parent bigint(20) unsigned NOT NULL DEFAULT 0,
  count bigint(20) NOT NULL DEFAULT 0,
  PRIMARY KEY (term_taxonomy_id),
  UNIQUE KEY term_id_taxonomy (term_id,taxonomy),
  KEY taxonomy (taxonomy)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

CREATE TABLE wp_term_relationships (
  object_id bigint(20) unsigned NOT NULL DEFAULT 0,
  term_taxonomy_id bigint(20) unsigned NOT NULL DEFAULT 0,
  term_order int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (object_id,term_taxonomy_id),
  KEY term_taxonomy_id (term_taxonomy_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;
";

// Execute table creation
$queries = explode(';', $wp_sql);
foreach ($queries as $query) {
    $query = trim($query);
    if (!empty($query)) {
        if ($mysqli->query($query)) {
            echo "<div style='background: #d4edda; padding: 5px 10px; border-radius: 3px; margin: 2px 0; font-size: 12px;'>";
            echo "✅ Table created successfully";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 5px 10px; border-radius: 3px; margin: 2px 0; font-size: 12px;'>";
            echo "❌ Error creating table: " . $mysqli->error;
            echo "</div>";
        }
    }
}

echo "<h2>📊 4. Restoring Essential Data</h2>";

// Restore users
if (!empty($backup_data['users'])) {
    foreach ($backup_data['users'] as $user) {
        $stmt = $mysqli->prepare("INSERT INTO wp_users (ID, user_login, user_pass, user_nicename, user_email, user_url, user_registered, user_activation_key, user_status, display_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("isssssssss", $user['ID'], $user['user_login'], $user['user_pass'], $user['user_nicename'], $user['user_email'], $user['user_url'], $user['user_registered'], $user['user_activation_key'], $user['user_status'], $user['display_name']);
        $stmt->execute();
    }
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ Users restored: " . count($backup_data['users']);
    echo "</div>";
}

// Restore user meta
if (!empty($backup_data['usermeta'])) {
    foreach ($backup_data['usermeta'] as $meta) {
        $stmt = $mysqli->prepare("INSERT INTO wp_usermeta (umeta_id, user_id, meta_key, meta_value) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("iiss", $meta['umeta_id'], $meta['user_id'], $meta['meta_key'], $meta['meta_value']);
        $stmt->execute();
    }
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ User metadata restored";
    echo "</div>";
}

// Insert essential WordPress options
$essential_options = array(
    'blogname' => 'JoseTusabe Photography',
    'blogdescription' => '🇩🇴 San José de Ocoa, República Dominicana',
    'admin_email' => '<EMAIL>',
    'users_can_register' => '0',
    'default_role' => 'subscriber',
    'timezone_string' => 'America/Santo_Domingo',
    'date_format' => 'F j, Y',
    'time_format' => 'g:i a',
    'start_of_week' => '1',
    'template' => 'twentytwentythree',
    'stylesheet' => 'twentytwentythree',
    'active_plugins' => 'a:0:{}',
    'permalink_structure' => '/%year%/%monthnum%/%day%/%postname%/',
    'category_base' => '',
    'tag_base' => '',
    'show_on_front' => 'posts',
    'posts_per_page' => '10',
    'comments_per_page' => '50',
    'default_ping_status' => 'closed',
    'default_comment_status' => 'closed'
);

foreach ($essential_options as $option_name => $option_value) {
    $stmt = $mysqli->prepare("INSERT INTO wp_options (option_name, option_value, autoload) VALUES (?, ?, 'yes') ON DUPLICATE KEY UPDATE option_value = VALUES(option_value)");
    $stmt->bind_param("ss", $option_name, $option_value);
    $stmt->execute();
}

echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
echo "✅ Essential WordPress options restored";
echo "</div>";

// Restore SoloYLibre data
if (!empty($backup_data['soloylibre_data'])) {
    foreach ($backup_data['soloylibre_data'] as $option_name => $option_value) {
        $stmt = $mysqli->prepare("INSERT INTO wp_options (option_name, option_value, autoload) VALUES (?, ?, 'no') ON DUPLICATE KEY UPDATE option_value = VALUES(option_value)");
        $stmt->bind_param("ss", $option_name, $option_value);
        $stmt->execute();
    }
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    echo "✅ SoloYLibre data restored";
    echo "</div>";
}

$mysqli->close();

echo "<h2>🎉 5. Installation Complete</h2>";

echo "<div style='background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>";
echo "<h2 style='margin: 0 0 15px 0; color: #155724;'>🎉 Fresh WordPress Installation Complete!</h2>";
echo "<p style='margin: 0; font-size: 18px; color: #155724;'>";
echo "WordPress has been completely reinstalled with all essential data preserved.";
echo "</p>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>✅ What was completed:</h3>";
echo "<ul>";
echo "<li>✅ Complete database cleanup</li>";
echo "<li>✅ Fresh WordPress tables created</li>";
echo "<li>✅ Users and authentication restored</li>";
echo "<li>✅ Essential settings configured</li>";
echo "<li>✅ SoloYLibre data preserved</li>";
echo "<li>✅ Clean slate for plugin installation</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='/' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; font-weight: bold;' target='_blank'>🌐 CHECK WEBSITE</a>";
echo "<a href='/wp-admin/' style='background: linear-gradient(135deg, #007bff, #6f42c1); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 0 10px; display: inline-block; font-weight: bold;' target='_blank'>🏠 DASHBOARD</a>";
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
echo "<p style='margin: 0; color: #666; font-size: 16px;'><strong>🇩🇴 JoseTusabe Photography</strong> - Fresh WordPress installation completed</p>";
echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 14px;'>Developed by JEYKO AI for Jose L Encarnacion</p>";
echo "</div>";
?>
