
[06-Jul-2025 14:52:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:36 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 14:52:36 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 14:52:46 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Editor_Integration' does not have a method 'enqueue_block_assets' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 324
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:52:51 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 14:52:51 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 14:52:56 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:22 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:22 UTC] SoloYLibre: Featured image set for post 355 using photo 282
[06-Jul-2025 14:53:29 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:39 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 14:53:39 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 14:53:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:41 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 9
[06-Jul-2025 14:53:41 UTC] WordPress database error Deadlock found when trying to get lock; try restarting transaction for query UPDATE wp_soloylibre_interactions
                 SET interaction_count = interaction_count + 1,
                     updated_at = '2025-07-06 14:53:41'
                 WHERE photo_id = 287 AND interaction_type = 'view' made by do_action('wp_ajax_gallery_interaction'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Enhanced_Shortcode->handle_interaction
[06-Jul-2025 14:53:41 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 24
[06-Jul-2025 14:53:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:43 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 36
[06-Jul-2025 14:53:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:43 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 11
[06-Jul-2025 14:53:43 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 37
[06-Jul-2025 14:53:45 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:45 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 14:53:45 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 14:53:45 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Editor_Integration' does not have a method 'enqueue_block_assets' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 324
[06-Jul-2025 14:53:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:50 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 56
[06-Jul-2025 14:53:50 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 10
[06-Jul-2025 14:53:50 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 25
[06-Jul-2025 14:53:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:53 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 11
[06-Jul-2025 14:53:53 UTC] WordPress database error Deadlock found when trying to get lock; try restarting transaction for query UPDATE wp_soloylibre_interactions
                 SET interaction_count = interaction_count + 1,
                     updated_at = '2025-07-06 14:53:53'
                 WHERE photo_id = 283 AND interaction_type = 'view' made by do_action('wp_ajax_gallery_interaction'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Enhanced_Shortcode->handle_interaction
[06-Jul-2025 14:53:53 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 57
[06-Jul-2025 14:53:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:54 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 37
[06-Jul-2025 14:53:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:54 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 12
[06-Jul-2025 14:53:54 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 38
[06-Jul-2025 14:53:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:54 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 30
[06-Jul-2025 14:53:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:54 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 9
[06-Jul-2025 14:53:54 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 14
[06-Jul-2025 14:53:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:54 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 27
[06-Jul-2025 14:53:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:57 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 30
[06-Jul-2025 14:53:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:58 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: like, Count: 20
[06-Jul-2025 14:53:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:53:59 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: like, Count: 21
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: like, Count: 22
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: like, Count: 23
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: like, Count: 24
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: like, Count: 25
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:00 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: like, Count: 26
[06-Jul-2025 14:54:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:01 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 26
[06-Jul-2025 14:54:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:02 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 27
[06-Jul-2025 14:54:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:03 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 58
[06-Jul-2025 14:54:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:05 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 12
[06-Jul-2025 14:54:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:08 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 12
[06-Jul-2025 14:54:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:11 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: like, Count: 4
[06-Jul-2025 14:54:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:15 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 10
[06-Jul-2025 14:54:15 UTC] WordPress database error Deadlock found when trying to get lock; try restarting transaction for query UPDATE wp_soloylibre_interactions
                 SET interaction_count = interaction_count + 1,
                     updated_at = '2025-07-06 14:54:15'
                 WHERE photo_id = 280 AND interaction_type = 'view' made by do_action('wp_ajax_gallery_interaction'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Enhanced_Shortcode->handle_interaction
[06-Jul-2025 14:54:15 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 15
[06-Jul-2025 14:54:15 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 31
[06-Jul-2025 14:54:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:17 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 3
[06-Jul-2025 14:54:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:18 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 4
[06-Jul-2025 14:54:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:18 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 5
[06-Jul-2025 14:54:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:18 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 6
[06-Jul-2025 14:54:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:18 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 7
[06-Jul-2025 14:54:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:19 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 8
[06-Jul-2025 14:54:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:19 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 9
[06-Jul-2025 14:54:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:19 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 10
[06-Jul-2025 14:54:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:21 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 11
[06-Jul-2025 14:54:21 UTC] WordPress database error Deadlock found when trying to get lock; try restarting transaction for query UPDATE wp_soloylibre_interactions
                 SET interaction_count = interaction_count + 1,
                     updated_at = '2025-07-06 14:54:21'
                 WHERE photo_id = 281 AND interaction_type = 'view' made by do_action('wp_ajax_gallery_interaction'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Enhanced_Shortcode->handle_interaction
[06-Jul-2025 14:54:21 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 28
[06-Jul-2025 14:54:21 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 16
[06-Jul-2025 14:54:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:23 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 11
[06-Jul-2025 14:54:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:23 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 12
[06-Jul-2025 14:54:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:23 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 13
[06-Jul-2025 14:54:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:23 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 14
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 15
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 16
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 17
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 18
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:24 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 19
[06-Jul-2025 14:54:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:25 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 20
[06-Jul-2025 14:54:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:25 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 21
[06-Jul-2025 14:54:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:25 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 22
[06-Jul-2025 14:54:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:26 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 29
[06-Jul-2025 14:54:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:26 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 30
[06-Jul-2025 14:54:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:26 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 31
[06-Jul-2025 14:54:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:26 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 32
[06-Jul-2025 14:54:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:27 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 33
[06-Jul-2025 14:54:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:27 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 34
[06-Jul-2025 14:54:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:27 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 35
[06-Jul-2025 14:54:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:27 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 36
[06-Jul-2025 14:54:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:27 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 37
[06-Jul-2025 14:54:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:27 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 38
[06-Jul-2025 14:54:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:28 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 39
[06-Jul-2025 14:54:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:28 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 40
[06-Jul-2025 14:54:29 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:54:30 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 12
[06-Jul-2025 14:54:30 UTC] WordPress database error Deadlock found when trying to get lock; try restarting transaction for query UPDATE wp_soloylibre_interactions
                 SET interaction_count = interaction_count + 1,
                     updated_at = '2025-07-06 14:54:30'
                 WHERE photo_id = 280 AND interaction_type = 'view' made by do_action('wp_ajax_gallery_interaction'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Enhanced_Shortcode->handle_interaction
[06-Jul-2025 14:54:30 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 17
[06-Jul-2025 14:54:30 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 32
[06-Jul-2025 14:54:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:55:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:55:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:55:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:55:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
