[06-Jul-2025 10:46:07 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 7
[06-Jul-2025 10:46:09 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 25
[06-Jul-2025 10:46:09 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 20
[06-Jul-2025 10:46:09 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 15
[06-Jul-2025 10:46:09 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 12
[06-Jul-2025 10:46:09 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 14
[06-Jul-2025 10:46:09 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 21
[06-Jul-2025 10:46:10 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 8
[06-Jul-2025 10:46:11 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 9
[06-Jul-2025 10:46:12 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 10
[06-Jul-2025 10:46:12 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 11
[06-Jul-2025 10:46:12 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 12
[06-Jul-2025 10:46:12 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 13
[06-Jul-2025 10:46:12 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 14
[06-Jul-2025 10:46:13 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 15
[06-Jul-2025 10:46:13 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 16
[06-Jul-2025 10:46:13 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 17
[06-Jul-2025 10:46:13 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 18
[06-Jul-2025 10:46:13 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 19
[06-Jul-2025 10:46:15 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 13
[06-Jul-2025 10:46:15 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 22
[06-Jul-2025 10:46:15 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 21
[06-Jul-2025 10:46:15 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 15
[06-Jul-2025 10:46:15 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 16
[06-Jul-2025 10:46:15 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 26
[06-Jul-2025 10:46:17 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 20
[06-Jul-2025 10:46:19 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 21
[06-Jul-2025 10:46:19 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 22
[06-Jul-2025 10:46:19 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 23
[06-Jul-2025 10:46:19 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 24
[06-Jul-2025 10:46:20 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 25
[06-Jul-2025 10:46:20 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 26
[06-Jul-2025 10:46:21 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 23
[06-Jul-2025 10:46:21 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 27
[06-Jul-2025 10:46:21 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 22
[06-Jul-2025 10:46:21 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 14
[06-Jul-2025 10:46:21 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 16
[06-Jul-2025 10:46:21 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 17
[06-Jul-2025 10:46:23 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 27
[06-Jul-2025 10:46:24 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 28
[06-Jul-2025 10:46:24 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 29
[06-Jul-2025 10:46:24 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 30
[06-Jul-2025 10:46:24 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 31
[06-Jul-2025 10:46:24 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 32
[06-Jul-2025 10:46:26 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: share, Count: 3
[06-Jul-2025 10:46:30 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: share, Count: 4
[06-Jul-2025 10:46:35 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 18
[06-Jul-2025 10:46:35 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 17
[06-Jul-2025 10:46:35 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 28
[06-Jul-2025 10:46:35 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 15
[06-Jul-2025 10:46:35 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 24
[06-Jul-2025 10:46:35 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 23
[06-Jul-2025 10:46:37 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 33
[06-Jul-2025 10:46:41 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 29
[06-Jul-2025 10:46:41 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 16
[06-Jul-2025 10:46:41 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 19
[06-Jul-2025 10:46:41 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 25
[06-Jul-2025 10:46:41 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 18
[06-Jul-2025 10:46:41 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 24
[06-Jul-2025 10:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 25
[06-Jul-2025 10:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 26
[06-Jul-2025 10:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 27
[06-Jul-2025 10:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 28
[06-Jul-2025 10:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 29
[06-Jul-2025 10:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 30
[06-Jul-2025 10:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 31
[06-Jul-2025 10:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 32
[06-Jul-2025 10:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 33
[06-Jul-2025 10:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 34
[06-Jul-2025 10:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 35
[06-Jul-2025 10:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 36
[06-Jul-2025 10:46:52 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 22
[06-Jul-2025 10:46:52 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 12
[06-Jul-2025 10:46:52 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 15
[06-Jul-2025 10:46:52 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 23
[06-Jul-2025 10:46:52 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 16
[06-Jul-2025 10:46:52 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 13
[06-Jul-2025 10:46:52 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 20
[06-Jul-2025 10:46:52 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 37
[06-Jul-2025 10:46:52 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 30
[06-Jul-2025 10:46:54 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 24
[06-Jul-2025 10:46:54 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 21
[06-Jul-2025 10:46:55 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 5
[06-Jul-2025 10:46:56 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 6
[06-Jul-2025 10:46:56 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 7
[06-Jul-2025 10:46:57 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: share, Count: 1
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 17
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 14
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 19
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 17
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 26
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 22
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 38
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 31
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 18
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 25
[06-Jul-2025 10:46:59 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 15
[06-Jul-2025 10:47:04 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 19
[06-Jul-2025 10:47:04 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 16
[06-Jul-2025 10:47:04 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 26
[06-Jul-2025 10:47:04 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 32
[06-Jul-2025 10:47:04 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 23
[06-Jul-2025 10:47:04 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 39
[06-Jul-2025 10:47:06 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: like, Count: 3
[06-Jul-2025 10:47:09 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 20
[06-Jul-2025 10:47:09 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 40
[06-Jul-2025 10:47:09 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 27
[06-Jul-2025 10:47:09 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 17
[06-Jul-2025 10:47:09 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 24
[06-Jul-2025 10:47:09 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 33
[06-Jul-2025 10:47:10 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 21
[06-Jul-2025 10:47:10 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 28
[06-Jul-2025 10:47:10 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 18
[06-Jul-2025 10:47:10 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 34
[06-Jul-2025 10:47:10 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 25
[06-Jul-2025 10:47:10 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 41
[06-Jul-2025 10:47:12 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: like, Count: 4
[06-Jul-2025 10:47:26 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 18
[06-Jul-2025 10:47:26 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 20
[06-Jul-2025 10:47:26 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 27
[06-Jul-2025 10:47:27 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 19
[06-Jul-2025 10:47:27 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 22
[06-Jul-2025 10:47:27 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 26
[06-Jul-2025 10:47:27 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 29
[06-Jul-2025 10:47:27 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 35
[06-Jul-2025 10:47:27 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 42
[06-Jul-2025 10:47:28 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 23
[06-Jul-2025 10:47:28 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 30
[06-Jul-2025 10:47:28 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 27
[06-Jul-2025 10:47:28 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 20
[06-Jul-2025 10:47:30 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 24
[06-Jul-2025 10:47:30 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 31
[06-Jul-2025 10:47:30 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 21
[06-Jul-2025 10:47:30 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 28
[06-Jul-2025 10:47:30 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 36
[06-Jul-2025 10:47:30 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 43
[06-Jul-2025 10:47:31 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 19
[06-Jul-2025 10:47:31 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 21
[06-Jul-2025 10:47:31 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 28
[06-Jul-2025 10:47:31 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 21
[06-Jul-2025 10:47:31 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 20
[06-Jul-2025 10:47:31 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 25
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 162, Action: view, Count: 21
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 164, Action: view, Count: 23
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 163, Action: view, Count: 23
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 160, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 161, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 159, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 156, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 157, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 158, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 152, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 155, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 153, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 150, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 151, Action: view, Count: 1
[06-Jul-2025 10:47:32 UTC] SoloYLibre: Interacción registrada - Photo: 149, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 147, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 146, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 148, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 145, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 144, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 143, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 141, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 142, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 140, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 138, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 139, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 137, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 135, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 134, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 136, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 131, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 132, Action: view, Count: 1
[06-Jul-2025 10:47:33 UTC] SoloYLibre: Interacción registrada - Photo: 133, Action: view, Count: 1
[06-Jul-2025 10:47:34 UTC] SoloYLibre: Interacción registrada - Photo: 128, Action: view, Count: 1
[06-Jul-2025 10:47:34 UTC] SoloYLibre: Interacción registrada - Photo: 130, Action: view, Count: 1
[06-Jul-2025 10:47:34 UTC] SoloYLibre: Interacción registrada - Photo: 129, Action: view, Count: 1
[06-Jul-2025 10:47:34 UTC] SoloYLibre: Interacción registrada - Photo: 125, Action: view, Count: 1
[06-Jul-2025 10:47:34 UTC] SoloYLibre: Interacción registrada - Photo: 127, Action: view, Count: 1
[06-Jul-2025 10:47:57 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:48:00 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:48:05 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 44
[06-Jul-2025 10:48:05 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 32
[06-Jul-2025 10:48:05 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 29
[06-Jul-2025 10:48:07 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 6
[06-Jul-2025 10:48:11 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 45
[06-Jul-2025 10:48:11 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 30
[06-Jul-2025 10:48:11 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 33
[06-Jul-2025 10:48:12 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 34
[06-Jul-2025 10:48:12 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 31
[06-Jul-2025 10:48:12 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 46
[06-Jul-2025 10:48:14 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 7
[06-Jul-2025 10:48:15 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 22
[06-Jul-2025 10:48:15 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 20
[06-Jul-2025 10:48:15 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 37
[06-Jul-2025 10:48:16 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 29
[06-Jul-2025 10:48:16 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 26
[06-Jul-2025 10:48:16 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 21
[06-Jul-2025 10:48:16 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 22
[06-Jul-2025 10:48:23 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 23
[06-Jul-2025 10:48:28 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 24
[06-Jul-2025 10:48:35 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 3
[06-Jul-2025 10:48:36 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 4
[06-Jul-2025 10:48:37 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 5
[06-Jul-2025 10:48:37 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 6
[06-Jul-2025 10:48:37 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 7
[06-Jul-2025 10:48:37 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 8
[06-Jul-2025 10:48:38 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 9
[06-Jul-2025 10:48:38 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 10
[06-Jul-2025 10:48:38 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 11
[06-Jul-2025 10:48:38 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 12
[06-Jul-2025 10:48:38 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 13
[06-Jul-2025 10:48:39 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 14
[06-Jul-2025 10:48:39 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 15
[06-Jul-2025 10:48:40 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 16
[06-Jul-2025 10:48:40 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 17
[06-Jul-2025 10:48:40 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 18
[06-Jul-2025 10:48:40 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 19
[06-Jul-2025 10:48:40 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 20
[06-Jul-2025 10:48:41 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 21
[06-Jul-2025 10:48:41 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 22
[06-Jul-2025 10:48:41 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 23
[06-Jul-2025 10:48:41 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 24
[06-Jul-2025 10:48:41 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 25
[06-Jul-2025 10:48:42 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 26
[06-Jul-2025 10:48:42 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 27
[06-Jul-2025 10:48:44 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 25
[06-Jul-2025 10:48:47 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: like, Count: 28
[06-Jul-2025 10:48:49 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 22
[06-Jul-2025 10:48:49 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 30
[06-Jul-2025 10:48:49 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 27
[06-Jul-2025 10:48:52 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: like, Count: 2
[06-Jul-2025 10:48:54 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: like, Count: 3
[06-Jul-2025 10:48:55 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: like, Count: 4
[06-Jul-2025 10:48:55 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: like, Count: 5
[06-Jul-2025 10:48:55 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: like, Count: 6
[06-Jul-2025 10:48:56 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: like, Count: 7
[06-Jul-2025 10:48:57 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 23
[06-Jul-2025 10:48:57 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 21
[06-Jul-2025 10:48:57 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 38
[06-Jul-2025 10:48:58 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:49:07 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 25
[06-Jul-2025 10:49:07 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 22
[06-Jul-2025 10:49:07 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 35
[06-Jul-2025 10:49:08 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 39
[06-Jul-2025 10:49:08 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 32
[06-Jul-2025 10:49:08 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 47
[06-Jul-2025 10:49:22 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: like, Count: 5
[06-Jul-2025 10:49:24 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 23
[06-Jul-2025 10:49:24 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 36
[06-Jul-2025 10:49:24 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 26
[06-Jul-2025 10:49:25 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: like, Count: 6
[06-Jul-2025 10:49:26 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 27
[06-Jul-2025 10:49:28 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: like, Count: 2
[06-Jul-2025 10:49:29 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 40
[06-Jul-2025 10:49:29 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 33
[06-Jul-2025 10:49:29 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 48
[06-Jul-2025 10:50:23 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:50:24 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:50:28 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:50:28 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:50:30 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 28
[06-Jul-2025 10:50:30 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 24
[06-Jul-2025 10:50:30 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 28
[06-Jul-2025 10:50:30 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 37
[06-Jul-2025 10:50:30 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 49
[06-Jul-2025 10:50:30 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 34
[06-Jul-2025 10:50:32 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: like, Count: 5
[06-Jul-2025 10:50:33 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 41
[06-Jul-2025 10:50:33 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 22
[06-Jul-2025 10:50:33 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 24
[06-Jul-2025 10:50:33 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 31
[06-Jul-2025 10:50:33 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 23
[06-Jul-2025 10:51:08 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 29
[06-Jul-2025 10:51:08 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 29
[06-Jul-2025 10:51:08 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 25
[06-Jul-2025 10:51:10 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:51:35 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 26
[06-Jul-2025 10:51:35 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 30
[06-Jul-2025 10:51:35 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 30
[06-Jul-2025 10:51:36 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 38
[06-Jul-2025 10:51:36 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 35
[06-Jul-2025 10:51:36 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 50
[06-Jul-2025 10:51:36 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 25
[06-Jul-2025 10:51:36 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 42
[06-Jul-2025 10:51:36 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 23
[06-Jul-2025 10:51:38 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:52:03 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 31
[06-Jul-2025 10:52:03 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 27
[06-Jul-2025 10:52:03 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 31
[06-Jul-2025 10:52:04 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 39
[06-Jul-2025 10:52:04 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 36
[06-Jul-2025 10:52:04 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 51
[06-Jul-2025 10:52:04 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 24
[06-Jul-2025 10:52:04 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 43
[06-Jul-2025 10:52:04 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 26
[06-Jul-2025 10:52:05 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:52:06 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:52:25 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 32
[06-Jul-2025 10:52:25 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 32
[06-Jul-2025 10:52:25 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 28
[06-Jul-2025 10:52:26 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 52
[06-Jul-2025 10:52:26 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 37
[06-Jul-2025 10:52:26 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 40
[06-Jul-2025 10:52:26 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 25
[06-Jul-2025 10:52:26 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 44
[06-Jul-2025 10:52:26 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 27
[06-Jul-2025 10:52:27 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 32
[06-Jul-2025 10:52:27 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 24
[06-Jul-2025 10:52:28 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: like, Count: 4
[06-Jul-2025 10:52:29 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: like, Count: 6
[06-Jul-2025 10:52:30 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: like, Count: 7
[06-Jul-2025 10:52:30 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: like, Count: 8
[06-Jul-2025 10:53:12 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 41
[06-Jul-2025 10:53:12 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 38
[06-Jul-2025 10:53:12 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 53
[06-Jul-2025 10:53:13 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 45
[06-Jul-2025 10:53:13 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 26
[06-Jul-2025 10:53:13 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 28
[06-Jul-2025 10:53:14 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 8
[06-Jul-2025 10:53:15 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 9
[06-Jul-2025 10:53:15 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 10
[06-Jul-2025 10:53:16 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 33
[06-Jul-2025 10:53:16 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 25
[06-Jul-2025 10:53:16 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 33
[06-Jul-2025 10:53:16 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 26
[06-Jul-2025 10:53:24 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 42
[06-Jul-2025 10:53:24 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 54
[06-Jul-2025 10:53:24 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 39
[06-Jul-2025 10:53:24 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 29
[06-Jul-2025 10:53:24 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 27
[06-Jul-2025 10:53:24 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 46
[06-Jul-2025 10:53:25 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 34
[06-Jul-2025 10:53:25 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 34
[06-Jul-2025 10:53:25 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 26
[06-Jul-2025 10:53:33 UTC] SoloYLibre: Interacción registrada - Photo: 100, Action: view, Count: 1
[06-Jul-2025 10:53:33 UTC] SoloYLibre: Interacción registrada - Photo: 98, Action: view, Count: 1
[06-Jul-2025 10:53:33 UTC] SoloYLibre: Interacción registrada - Photo: 99, Action: view, Count: 1
[06-Jul-2025 10:53:35 UTC] SoloYLibre: Interacción registrada - Photo: 100, Action: like, Count: 1
[06-Jul-2025 10:53:35 UTC] SoloYLibre: Interacción registrada - Photo: 100, Action: like, Count: 2
[06-Jul-2025 10:53:35 UTC] SoloYLibre: Interacción registrada - Photo: 100, Action: like, Count: 3
[06-Jul-2025 10:53:35 UTC] SoloYLibre: Interacción registrada - Photo: 100, Action: like, Count: 4
[06-Jul-2025 10:53:35 UTC] SoloYLibre: Interacción registrada - Photo: 100, Action: like, Count: 5
[06-Jul-2025 10:53:36 UTC] SoloYLibre: Interacción registrada - Photo: 100, Action: like, Count: 6
[06-Jul-2025 10:53:36 UTC] SoloYLibre: Interacción registrada - Photo: 96, Action: view, Count: 1
[06-Jul-2025 10:53:36 UTC] SoloYLibre: Interacción registrada - Photo: 95, Action: view, Count: 1
[06-Jul-2025 10:53:36 UTC] SoloYLibre: Interacción registrada - Photo: 97, Action: view, Count: 1
[06-Jul-2025 10:53:36 UTC] SoloYLibre: Interacción registrada - Photo: 94, Action: view, Count: 1
[06-Jul-2025 10:53:36 UTC] SoloYLibre: Interacción registrada - Photo: 93, Action: view, Count: 1
[06-Jul-2025 10:53:36 UTC] SoloYLibre: Interacción registrada - Photo: 92, Action: view, Count: 1
[06-Jul-2025 10:53:36 UTC] SoloYLibre: Interacción registrada - Photo: 91, Action: view, Count: 1
[06-Jul-2025 10:53:39 UTC] SoloYLibre: Interacción registrada - Photo: 99, Action: like, Count: 1
[06-Jul-2025 10:53:40 UTC] SoloYLibre: Interacción registrada - Photo: 99, Action: like, Count: 2
[06-Jul-2025 10:53:40 UTC] SoloYLibre: Interacción registrada - Photo: 99, Action: like, Count: 3
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 91, Action: view, Count: 2
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 94, Action: view, Count: 2
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 93, Action: view, Count: 2
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 92, Action: view, Count: 2
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 96, Action: view, Count: 2
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 95, Action: view, Count: 2
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 97, Action: view, Count: 2
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 100, Action: view, Count: 2
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 98, Action: view, Count: 2
[06-Jul-2025 10:56:16 UTC] SoloYLibre: Interacción registrada - Photo: 99, Action: view, Count: 2
[06-Jul-2025 10:56:18 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 43
[06-Jul-2025 10:56:18 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 55
[06-Jul-2025 10:56:18 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 40
[06-Jul-2025 10:56:48 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:57:01 UTC] SoloYLibre: Interacción registrada - Photo: 10, Action: view, Count: 1
[06-Jul-2025 10:57:04 UTC] SoloYLibre: Interacción registrada - Photo: 10, Action: like, Count: 1
[06-Jul-2025 10:57:05 UTC] SoloYLibre: Interacción registrada - Photo: 10, Action: like, Count: 2
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 44
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 27
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 35
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 47
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 41
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 30
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 35
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 27
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 56
[06-Jul-2025 10:57:10 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 28
[06-Jul-2025 10:57:13 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 45
[06-Jul-2025 10:57:13 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 29
[06-Jul-2025 10:57:13 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 57
[06-Jul-2025 10:57:13 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 48
[06-Jul-2025 10:57:13 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 31
[06-Jul-2025 10:57:13 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 42
[06-Jul-2025 10:57:36 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:57:38 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:57:38 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:57:40 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 43
[06-Jul-2025 10:57:40 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 30
[06-Jul-2025 10:57:40 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 49
[06-Jul-2025 10:57:40 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 32
[06-Jul-2025 10:57:40 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 36
[06-Jul-2025 10:57:40 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 58
[06-Jul-2025 10:57:43 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 28
[06-Jul-2025 10:57:43 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 28
[06-Jul-2025 10:57:43 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 36
[06-Jul-2025 10:57:43 UTC] SoloYLibre: Interacción registrada - Photo: 164, Action: view, Count: 24
[06-Jul-2025 10:57:43 UTC] SoloYLibre: Interacción registrada - Photo: 162, Action: view, Count: 22
[06-Jul-2025 10:57:43 UTC] SoloYLibre: Interacción registrada - Photo: 163, Action: view, Count: 24
[06-Jul-2025 10:57:43 UTC] SoloYLibre: Interacción registrada - Photo: 159, Action: view, Count: 2
[06-Jul-2025 10:57:43 UTC] SoloYLibre: Interacción registrada - Photo: 160, Action: view, Count: 2
[06-Jul-2025 10:57:43 UTC] SoloYLibre: Interacción registrada - Photo: 161, Action: view, Count: 2
[06-Jul-2025 10:57:44 UTC] SoloYLibre: Interacción registrada - Photo: 164, Action: like, Count: 5
[06-Jul-2025 10:57:45 UTC] SoloYLibre: Interacción registrada - Photo: 164, Action: like, Count: 6
[06-Jul-2025 10:57:46 UTC] SoloYLibre: Interacción registrada - Photo: 164, Action: like, Count: 7
[06-Jul-2025 10:57:46 UTC] SoloYLibre: Interacción registrada - Photo: 158, Action: view, Count: 2
[06-Jul-2025 10:57:49 UTC] SoloYLibre: Interacción registrada - Photo: 158, Action: view, Count: 3
[06-Jul-2025 10:57:50 UTC] SoloYLibre: Interacción registrada - Photo: 158, Action: view, Count: 4
[06-Jul-2025 10:57:50 UTC] SoloYLibre: Interacción registrada - Photo: 158, Action: view, Count: 5
[06-Jul-2025 10:57:50 UTC] SoloYLibre: Interacción registrada - Photo: 158, Action: view, Count: 6
[06-Jul-2025 10:57:51 UTC] SoloYLibre: Interacción registrada - Photo: 158, Action: view, Count: 7
[06-Jul-2025 10:57:55 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 8
[06-Jul-2025 10:57:55 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 9
[06-Jul-2025 10:57:55 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 10
[06-Jul-2025 10:57:55 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 11
[06-Jul-2025 10:57:55 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 12
[06-Jul-2025 10:57:56 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 13
[06-Jul-2025 10:57:56 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 14
[06-Jul-2025 10:57:56 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 15
[06-Jul-2025 10:57:56 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 16
[06-Jul-2025 10:57:56 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 17
[06-Jul-2025 10:57:57 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 18
[06-Jul-2025 10:57:57 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 19
[06-Jul-2025 10:57:57 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 20
[06-Jul-2025 10:57:57 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: like, Count: 21
[06-Jul-2025 10:58:02 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 34
[06-Jul-2025 10:58:23 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 31
[06-Jul-2025 10:58:23 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 50
[06-Jul-2025 10:58:23 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 37
[06-Jul-2025 10:58:23 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 44
[06-Jul-2025 10:58:23 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 59
[06-Jul-2025 10:58:23 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 33
[06-Jul-2025 10:58:24 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 35
[06-Jul-2025 10:58:26 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 10:58:43 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 46
[06-Jul-2025 10:58:43 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 45
[06-Jul-2025 10:58:43 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 29
[06-Jul-2025 10:58:43 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 33
[06-Jul-2025 10:58:43 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 32
[06-Jul-2025 10:58:43 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 34
[06-Jul-2025 10:58:43 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 60
[06-Jul-2025 10:58:43 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 51
[06-Jul-2025 10:58:45 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: like, Count: 7
[06-Jul-2025 10:58:46 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 37
[06-Jul-2025 10:58:46 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 29
[06-Jul-2025 10:58:46 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 29
[06-Jul-2025 10:58:46 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 38
[06-Jul-2025 10:58:46 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 61
[06-Jul-2025 10:58:46 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 39
[06-Jul-2025 10:58:46 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 34
[06-Jul-2025 10:58:47 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 30
[06-Jul-2025 10:58:47 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 30
[06-Jul-2025 10:58:47 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 52
[06-Jul-2025 10:58:47 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 33
[06-Jul-2025 10:58:47 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 38
[06-Jul-2025 10:58:47 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 47
[06-Jul-2025 10:58:47 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 30
[06-Jul-2025 10:58:47 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 46
[06-Jul-2025 10:58:47 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 35
[06-Jul-2025 10:58:48 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 48
[06-Jul-2025 10:58:48 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 35
[06-Jul-2025 10:58:48 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 31
[06-Jul-2025 10:58:49 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 62
[06-Jul-2025 10:58:49 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 53
[06-Jul-2025 10:58:49 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 47
[06-Jul-2025 10:58:49 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 31
[06-Jul-2025 10:58:49 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 36
[06-Jul-2025 10:58:49 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 39
[06-Jul-2025 10:58:49 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 34
[06-Jul-2025 10:58:49 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 40
[06-Jul-2025 10:58:49 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 31
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 49
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 36
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 32
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 33
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 37
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 50
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 54
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 48
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 63
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 49
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 64
[06-Jul-2025 10:58:50 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 55
[06-Jul-2025 10:58:53 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 36
[06-Jul-2025 11:02:04 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:04 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:04 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 276 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 11:02:17 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:17 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:17 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:18 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:18 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 276 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 11:02:22 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:22 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 276 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 11:02:22 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:22 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 276 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 11:02:35 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:35 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:35 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:35 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:02:35 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 276 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 11:02:45 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 35
[06-Jul-2025 11:02:45 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 51
[06-Jul-2025 11:02:45 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 65
[06-Jul-2025 11:02:45 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 50
[06-Jul-2025 11:02:45 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 56
[06-Jul-2025 11:02:45 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 37
[06-Jul-2025 11:02:46 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 40
[06-Jul-2025 11:02:46 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 32
[06-Jul-2025 11:02:46 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 41
[06-Jul-2025 11:02:46 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 32
[06-Jul-2025 11:02:51 UTC] SoloYLibre: Interacción registrada - Photo: 267, Action: view, Count: 1
[06-Jul-2025 11:02:51 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 1
[06-Jul-2025 11:02:51 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 1
[06-Jul-2025 11:02:51 UTC] SoloYLibre: Interacción registrada - Photo: 268, Action: view, Count: 1
[06-Jul-2025 11:02:51 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 1
[06-Jul-2025 11:02:51 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 1
[06-Jul-2025 11:02:53 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 1
[06-Jul-2025 11:02:53 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 1
[06-Jul-2025 11:02:53 UTC] SoloYLibre: Interacción registrada - Photo: 270, Action: view, Count: 1
[06-Jul-2025 11:02:53 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 1
[06-Jul-2025 11:02:53 UTC] SoloYLibre: Interacción registrada - Photo: 269, Action: view, Count: 1
[06-Jul-2025 11:02:53 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 1
[06-Jul-2025 11:02:54 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 2
[06-Jul-2025 11:02:54 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 2
[06-Jul-2025 11:02:54 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 2
[06-Jul-2025 11:02:54 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 2
[06-Jul-2025 11:02:54 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 2
[06-Jul-2025 11:02:54 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 2
[06-Jul-2025 11:02:54 UTC] SoloYLibre: Interacción registrada - Photo: 270, Action: view, Count: 2
[06-Jul-2025 11:02:54 UTC] SoloYLibre: Interacción registrada - Photo: 269, Action: view, Count: 2
[06-Jul-2025 11:02:54 UTC] SoloYLibre: Interacción registrada - Photo: 268, Action: view, Count: 2
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 267, Action: view, Count: 2
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 2
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 2
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 3
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 3
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 3
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 4
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 4
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 4
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 4
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 3
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 4
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 3
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 4
[06-Jul-2025 11:02:55 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 4
[06-Jul-2025 11:03:03 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: like, Count: 1
[06-Jul-2025 11:03:04 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: like, Count: 2
[06-Jul-2025 11:03:07 UTC] SoloYLibre: Interacción registrada - Photo: 269, Action: like, Count: 1
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 3
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 3
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 268, Action: view, Count: 3
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 267, Action: view, Count: 3
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 270, Action: view, Count: 3
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 269, Action: view, Count: 3
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 5
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 5
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 5
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 5
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 5
[06-Jul-2025 11:03:12 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 5
[06-Jul-2025 11:03:18 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 36
[06-Jul-2025 11:03:18 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 38
[06-Jul-2025 11:03:18 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 52
[06-Jul-2025 11:03:18 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 57
[06-Jul-2025 11:03:18 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 51
[06-Jul-2025 11:03:18 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 66
[06-Jul-2025 11:04:59 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '************', 'camera', 1, '2025-07-06 11:04:59') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 11:04:59 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '*************', 'wow', 1, '2025-07-06 11:04:59') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 11:05:14 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: like, Count: 1
[06-Jul-2025 11:05:14 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: like, Count: 2
[06-Jul-2025 11:05:17 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: like, Count: 1
[06-Jul-2025 11:05:32 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: like, Count: 1
[06-Jul-2025 11:05:33 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: like, Count: 2
[06-Jul-2025 11:05:33 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: like, Count: 3
[06-Jul-2025 11:05:34 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: like, Count: 4
[06-Jul-2025 11:06:29 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:06:31 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:06:34 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 1
[06-Jul-2025 11:06:34 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 1
[06-Jul-2025 11:06:34 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 1
[06-Jul-2025 11:06:36 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: like, Count: 1
[06-Jul-2025 11:06:38 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 2
[06-Jul-2025 11:06:40 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 1
[06-Jul-2025 11:06:40 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 1
[06-Jul-2025 11:06:40 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 1
[06-Jul-2025 11:06:40 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 1
[06-Jul-2025 11:06:40 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 1
[06-Jul-2025 11:06:40 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 1
[06-Jul-2025 11:06:41 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 1
[06-Jul-2025 11:06:41 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 1
[06-Jul-2025 11:06:41 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 1
[06-Jul-2025 11:06:44 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 6
[06-Jul-2025 11:06:44 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 6
[06-Jul-2025 11:06:44 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 6
[06-Jul-2025 11:06:44 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 6
[06-Jul-2025 11:06:44 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 6
[06-Jul-2025 11:06:44 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 6
[06-Jul-2025 11:06:45 UTC] SoloYLibre: Interacción registrada - Photo: 270, Action: view, Count: 4
[06-Jul-2025 11:06:49 UTC] SoloYLibre: Interacción registrada - Photo: 270, Action: like, Count: 1
[06-Jul-2025 11:06:53 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 1
[06-Jul-2025 11:06:54 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: like, Count: 2
[06-Jul-2025 11:08:30 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 41
[06-Jul-2025 11:08:30 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 53
[06-Jul-2025 11:08:30 UTC] SoloYLibre: Interacción registrada - Photo: 176, Action: view, Count: 38
[06-Jul-2025 11:08:30 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 34
[06-Jul-2025 11:08:30 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 67
[06-Jul-2025 11:08:30 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 52
[06-Jul-2025 11:08:30 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 37
[06-Jul-2025 11:08:30 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 58
[06-Jul-2025 11:08:30 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 39
[06-Jul-2025 11:08:31 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 33
[06-Jul-2025 11:08:31 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 42
[06-Jul-2025 11:08:32 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 11
[06-Jul-2025 11:08:34 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 54
[06-Jul-2025 11:08:42 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 3
[06-Jul-2025 11:08:42 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 2
[06-Jul-2025 11:08:42 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 2
[06-Jul-2025 11:08:44 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 2
[06-Jul-2025 11:08:44 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 2
[06-Jul-2025 11:08:44 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 2
[06-Jul-2025 11:08:44 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 2
[06-Jul-2025 11:08:44 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 2
[06-Jul-2025 11:08:44 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 2
[06-Jul-2025 11:08:44 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 2
[06-Jul-2025 11:08:44 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 2
[06-Jul-2025 11:08:44 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 2
[06-Jul-2025 11:08:47 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 3
[06-Jul-2025 11:08:47 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 3
[06-Jul-2025 11:08:47 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 3
[06-Jul-2025 11:08:47 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 3
[06-Jul-2025 11:08:47 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 3
[06-Jul-2025 11:08:47 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 3
[06-Jul-2025 11:08:48 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 4
[06-Jul-2025 11:08:48 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 4
[06-Jul-2025 11:08:48 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 4
[06-Jul-2025 11:08:48 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 4
[06-Jul-2025 11:08:48 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 4
[06-Jul-2025 11:08:48 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 4
[06-Jul-2025 11:09:11 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 1
[06-Jul-2025 11:09:12 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 2
[06-Jul-2025 11:09:12 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 3
[06-Jul-2025 11:09:12 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 4
[06-Jul-2025 11:09:13 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 5
[06-Jul-2025 11:09:13 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 6
[06-Jul-2025 11:09:13 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 7
[06-Jul-2025 11:09:13 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 8
[06-Jul-2025 11:09:13 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 9
[06-Jul-2025 11:09:14 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 10
[06-Jul-2025 11:09:14 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 11
[06-Jul-2025 11:09:14 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 12
[06-Jul-2025 11:09:14 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 13
[06-Jul-2025 11:09:14 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 14
[06-Jul-2025 11:09:14 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 15
[06-Jul-2025 11:09:16 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 1
[06-Jul-2025 11:09:16 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 2
[06-Jul-2025 11:09:16 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 3
[06-Jul-2025 11:09:16 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 4
[06-Jul-2025 11:09:16 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 5
[06-Jul-2025 11:09:17 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 6
[06-Jul-2025 11:09:17 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 7
[06-Jul-2025 11:09:17 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 8
[06-Jul-2025 11:09:17 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 9
[06-Jul-2025 11:09:17 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 10
[06-Jul-2025 11:09:17 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: like, Count: 11
[06-Jul-2025 11:09:18 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 3
[06-Jul-2025 11:09:18 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 3
[06-Jul-2025 11:09:19 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 3
[06-Jul-2025 11:09:29 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 7
[06-Jul-2025 11:09:29 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 7
[06-Jul-2025 11:09:29 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 7
[06-Jul-2025 11:09:29 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 5
[06-Jul-2025 11:09:29 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 4
[06-Jul-2025 11:09:29 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 5
[06-Jul-2025 11:09:29 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 4
[06-Jul-2025 11:09:29 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 5
[06-Jul-2025 11:09:29 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 4
[06-Jul-2025 11:09:31 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 8
[06-Jul-2025 11:09:31 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 8
[06-Jul-2025 11:09:31 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 8
[06-Jul-2025 11:09:31 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 6
[06-Jul-2025 11:09:31 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 5
[06-Jul-2025 11:09:31 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 6
[06-Jul-2025 11:09:31 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 6
[06-Jul-2025 11:09:31 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 5
[06-Jul-2025 11:09:31 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 5
[06-Jul-2025 11:09:48 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:09:48 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:10:21 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:10:21 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:10:26 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:10:26 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:10:55 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:10:57 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:11:00 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:11:00 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:11:02 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 3
[06-Jul-2025 11:11:02 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 6
[06-Jul-2025 11:11:02 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 9
[06-Jul-2025 11:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 6
[06-Jul-2025 11:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 5
[06-Jul-2025 11:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 7
[06-Jul-2025 11:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 5
[06-Jul-2025 11:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 3
[06-Jul-2025 11:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 5
[06-Jul-2025 11:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 7
[06-Jul-2025 11:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 6
[06-Jul-2025 11:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 7
[06-Jul-2025 11:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 7
[06-Jul-2025 11:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 9
[06-Jul-2025 11:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 7
[06-Jul-2025 11:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 7
[06-Jul-2025 11:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 9
[06-Jul-2025 11:11:08 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: like, Count: 5
[06-Jul-2025 11:11:10 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: like, Count: 3
[06-Jul-2025 11:11:30 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 4
[06-Jul-2025 11:11:30 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 4
[06-Jul-2025 11:11:30 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 4
[06-Jul-2025 11:11:30 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 5
[06-Jul-2025 11:11:30 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 5
[06-Jul-2025 11:11:30 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 5
[06-Jul-2025 11:11:31 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 7
[06-Jul-2025 11:11:31 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 7
[06-Jul-2025 11:11:31 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 7
[06-Jul-2025 11:11:31 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 6
[06-Jul-2025 11:11:31 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 7
[06-Jul-2025 11:11:31 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 7
[06-Jul-2025 11:11:36 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 6
[06-Jul-2025 11:11:36 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 6
[06-Jul-2025 11:11:36 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 8
[06-Jul-2025 11:11:36 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 6
[06-Jul-2025 11:11:36 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 8
[06-Jul-2025 11:11:36 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 8
[06-Jul-2025 11:11:36 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 8
[06-Jul-2025 11:11:36 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 8
[06-Jul-2025 11:11:37 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 8
[06-Jul-2025 11:11:37 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 7
[06-Jul-2025 11:11:37 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 7
[06-Jul-2025 11:11:37 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 7
[06-Jul-2025 11:11:38 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 9
[06-Jul-2025 11:11:38 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 9
[06-Jul-2025 11:11:38 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 7
[06-Jul-2025 11:11:39 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 9
[06-Jul-2025 11:11:40 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 8
[06-Jul-2025 11:11:40 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 9
[06-Jul-2025 11:11:40 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 7
[06-Jul-2025 11:11:41 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 8
[06-Jul-2025 11:11:41 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 9
[06-Jul-2025 11:11:41 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 7
[06-Jul-2025 11:11:41 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 9
[06-Jul-2025 11:11:41 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 8
[06-Jul-2025 11:11:47 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 10
[06-Jul-2025 11:11:47 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 11
[06-Jul-2025 11:11:47 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 12
[06-Jul-2025 11:11:47 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 13
[06-Jul-2025 11:11:47 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 14
[06-Jul-2025 11:11:48 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 15
[06-Jul-2025 11:11:48 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 16
[06-Jul-2025 11:11:48 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 17
[06-Jul-2025 11:11:48 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 18
[06-Jul-2025 11:11:48 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 19
[06-Jul-2025 11:11:51 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 9
[06-Jul-2025 11:11:51 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 20
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 8
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 10
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 9
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 10
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 8
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 9
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 10
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 8
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 10
[06-Jul-2025 11:11:53 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 10
[06-Jul-2025 11:11:58 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 10
[06-Jul-2025 11:11:58 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 21
[06-Jul-2025 11:11:58 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 10
[06-Jul-2025 11:11:58 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 9
[06-Jul-2025 11:11:58 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 11
[06-Jul-2025 11:11:58 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 11
[06-Jul-2025 11:11:58 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 10
[06-Jul-2025 11:11:58 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 10
[06-Jul-2025 11:12:00 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 1
[06-Jul-2025 11:12:01 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 11
[06-Jul-2025 11:12:01 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 11
[06-Jul-2025 11:12:01 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 10
[06-Jul-2025 11:12:02 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 2
[06-Jul-2025 11:12:05 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 12
[06-Jul-2025 11:12:05 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 11
[06-Jul-2025 11:12:05 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 12
[06-Jul-2025 11:12:06 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 13
[06-Jul-2025 11:12:06 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 12
[06-Jul-2025 11:12:06 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 13
[06-Jul-2025 11:12:07 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 14
[06-Jul-2025 11:12:07 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 14
[06-Jul-2025 11:12:07 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 13
[06-Jul-2025 11:12:09 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: share, Count: 1
[06-Jul-2025 11:12:12 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 15
[06-Jul-2025 11:12:12 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 14
[06-Jul-2025 11:12:12 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 15
[06-Jul-2025 11:12:14 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: share, Count: 2
[06-Jul-2025 11:12:20 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 10
[06-Jul-2025 11:12:20 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 12
[06-Jul-2025 11:12:21 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 8
[06-Jul-2025 11:12:22 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: like, Count: 1
[06-Jul-2025 11:12:22 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: like, Count: 2
[06-Jul-2025 11:12:23 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: like, Count: 3
[06-Jul-2025 11:12:23 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: like, Count: 4
[06-Jul-2025 11:12:43 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:12:45 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:12:47 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:12:47 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:12:48 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 15
[06-Jul-2025 11:12:48 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 9
[06-Jul-2025 11:12:48 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 9
[06-Jul-2025 11:13:09 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:13:12 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 10
[06-Jul-2025 11:13:12 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 10
[06-Jul-2025 11:13:12 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 16
[06-Jul-2025 11:13:17 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 1
[06-Jul-2025 11:13:48 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:13:49 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:13:52 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:13:52 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:13:54 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 11
[06-Jul-2025 11:13:54 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 13
[06-Jul-2025 11:13:54 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 17
[06-Jul-2025 11:13:55 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 11
[06-Jul-2025 11:13:55 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 11
[06-Jul-2025 11:13:55 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 11
[06-Jul-2025 11:13:57 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 16
[06-Jul-2025 11:13:57 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 12
[06-Jul-2025 11:13:57 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 22
[06-Jul-2025 11:13:58 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 11
[06-Jul-2025 11:14:10 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 18
[06-Jul-2025 11:14:10 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 16
[06-Jul-2025 11:14:10 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 17
[06-Jul-2025 11:14:11 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 12
[06-Jul-2025 11:14:11 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 14
[06-Jul-2025 11:14:12 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 9
[06-Jul-2025 11:14:12 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 12
[06-Jul-2025 11:14:12 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 12
[06-Jul-2025 11:14:12 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 12
[06-Jul-2025 11:14:12 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 8
[06-Jul-2025 11:14:12 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 10
[06-Jul-2025 11:14:13 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 12
[06-Jul-2025 11:14:13 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 11
[06-Jul-2025 11:14:13 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 8
[06-Jul-2025 11:14:13 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 23
[06-Jul-2025 11:14:15 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 11
[06-Jul-2025 11:14:15 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 13
[06-Jul-2025 11:19:29 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 15
[06-Jul-2025 11:19:29 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 14
[06-Jul-2025 11:19:29 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 11
[06-Jul-2025 11:19:29 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 13
[06-Jul-2025 11:19:29 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 18
[06-Jul-2025 11:19:29 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 13
[06-Jul-2025 11:19:29 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 13
[06-Jul-2025 11:19:31 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 13
[06-Jul-2025 11:19:31 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 13
[06-Jul-2025 11:19:31 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 19
[06-Jul-2025 11:19:33 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: like, Count: 5
[06-Jul-2025 11:19:34 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 24
[06-Jul-2025 11:19:34 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 11
[06-Jul-2025 11:19:35 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 14
[06-Jul-2025 11:19:35 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 12
[06-Jul-2025 11:19:35 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 20
[06-Jul-2025 11:19:35 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 16
[06-Jul-2025 11:19:35 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 14
[06-Jul-2025 11:19:35 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 14
[06-Jul-2025 11:19:36 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 17
[06-Jul-2025 11:19:36 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 15
[06-Jul-2025 11:19:36 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 21
[06-Jul-2025 11:19:36 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 13
[06-Jul-2025 11:19:36 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 14
[06-Jul-2025 11:19:36 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 22
[06-Jul-2025 11:19:36 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 16
[06-Jul-2025 11:19:37 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 18
[06-Jul-2025 11:19:37 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 15
[06-Jul-2025 11:19:37 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 15
[06-Jul-2025 11:19:37 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 16
[06-Jul-2025 11:19:37 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 15
[06-Jul-2025 11:19:37 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 23
[06-Jul-2025 11:19:37 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 16
[06-Jul-2025 11:19:37 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 19
[06-Jul-2025 11:19:37 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 17
[06-Jul-2025 11:19:38 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 25
[06-Jul-2025 11:19:38 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 14
[06-Jul-2025 11:19:38 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 15
[06-Jul-2025 11:19:38 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 12
[06-Jul-2025 11:19:38 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 19
[06-Jul-2025 11:19:38 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 14
[06-Jul-2025 11:20:32 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:20:32 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:20:32 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 297 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 11:20:39 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:20:39 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:20:39 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 298 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 11:20:53 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:20:53 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:21:51 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:22:07 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 20
[06-Jul-2025 11:22:07 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 24
[06-Jul-2025 11:22:07 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 18
[06-Jul-2025 11:22:12 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 21
[06-Jul-2025 11:22:12 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 19
[06-Jul-2025 11:22:12 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 25
[06-Jul-2025 11:22:17 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:22:21 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:22:21 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:22:22 UTC] SoloYLibre: Interacción registrada - Photo: 231, Action: view, Count: 1
[06-Jul-2025 11:22:22 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 1
[06-Jul-2025 11:22:22 UTC] SoloYLibre: Interacción registrada - Photo: 161, Action: view, Count: 3
[06-Jul-2025 11:22:22 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: view, Count: 1
[06-Jul-2025 11:22:22 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 1
[06-Jul-2025 11:22:22 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 13
[06-Jul-2025 11:22:24 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 34
[06-Jul-2025 11:22:24 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 1
[06-Jul-2025 11:22:24 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 42
[06-Jul-2025 11:22:25 UTC] SoloYLibre: Interacción registrada - Photo: 160, Action: view, Count: 3
[06-Jul-2025 11:22:25 UTC] SoloYLibre: Interacción registrada - Photo: 267, Action: view, Count: 4
[06-Jul-2025 11:22:25 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 4
[06-Jul-2025 11:22:26 UTC] SoloYLibre: Interacción registrada - Photo: 159, Action: view, Count: 3
[06-Jul-2025 11:22:26 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 33
[06-Jul-2025 11:22:30 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: like, Count: 1
[06-Jul-2025 11:22:31 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: like, Count: 2
[06-Jul-2025 11:22:31 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: like, Count: 3
[06-Jul-2025 11:22:32 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: like, Count: 4
[06-Jul-2025 11:22:32 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: like, Count: 5
[06-Jul-2025 11:22:32 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: like, Count: 6
[06-Jul-2025 11:22:38 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: like, Count: 1
[06-Jul-2025 11:22:40 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 1
[06-Jul-2025 11:22:59 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:23:12 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:23:12 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:23:16 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:23:16 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:23:26 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 26
[06-Jul-2025 11:23:26 UTC] SoloYLibre: Interacción registrada - Photo: 260, Action: view, Count: 1
[06-Jul-2025 11:23:26 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 16
[06-Jul-2025 11:23:26 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 17
[06-Jul-2025 11:23:37 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 12
[06-Jul-2025 11:23:37 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 26
[06-Jul-2025 11:23:37 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 12
[06-Jul-2025 11:23:38 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 2
[06-Jul-2025 11:23:38 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 15
[06-Jul-2025 11:23:42 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 2
[06-Jul-2025 11:23:43 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 3
[06-Jul-2025 11:23:43 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 4
[06-Jul-2025 11:23:43 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 5
[06-Jul-2025 11:23:43 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 6
[06-Jul-2025 11:23:43 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 7
[06-Jul-2025 11:23:43 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 8
[06-Jul-2025 11:23:44 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 9
[06-Jul-2025 11:23:44 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 10
[06-Jul-2025 11:23:44 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 11
[06-Jul-2025 11:23:44 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 12
[06-Jul-2025 11:23:44 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 13
[06-Jul-2025 11:23:44 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 14
[06-Jul-2025 11:23:45 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 15
[06-Jul-2025 11:26:12 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:26:12 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:26:21 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:26:21 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:26:21 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 302 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 11:26:24 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:26:24 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:26:30 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:26:30 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:27:06 UTC] SoloYLibre: Interacción registrada - Photo: 161, Action: view, Count: 4
[06-Jul-2025 11:27:06 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 14
[06-Jul-2025 11:27:06 UTC] SoloYLibre: Interacción registrada - Photo: 231, Action: view, Count: 2
[06-Jul-2025 11:27:06 UTC] SoloYLibre: Interacción registrada - Photo: 160, Action: view, Count: 4
[06-Jul-2025 11:27:06 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: view, Count: 2
[06-Jul-2025 11:27:06 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 3
[06-Jul-2025 11:27:06 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 2
[06-Jul-2025 11:27:16 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 16
[06-Jul-2025 11:27:17 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: like, Count: 17
[06-Jul-2025 11:27:26 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 4
[06-Jul-2025 11:27:27 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 5
[06-Jul-2025 11:27:28 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 6
[06-Jul-2025 11:27:28 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 7
[06-Jul-2025 11:27:29 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 8
[06-Jul-2025 11:27:29 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 9
[06-Jul-2025 11:27:29 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 10
[06-Jul-2025 11:27:29 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 11
[06-Jul-2025 11:27:30 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 12
[06-Jul-2025 11:27:30 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 13
[06-Jul-2025 11:27:30 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 14
[06-Jul-2025 11:27:30 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 15
[06-Jul-2025 11:27:31 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 16
[06-Jul-2025 11:27:31 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 17
[06-Jul-2025 11:27:31 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 18
[06-Jul-2025 11:27:31 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 19
[06-Jul-2025 11:27:32 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 15
[06-Jul-2025 11:27:34 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: share, Count: 1
[06-Jul-2025 11:27:54 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 3
[06-Jul-2025 11:27:54 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 20
[06-Jul-2025 11:27:54 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 16
[06-Jul-2025 11:27:54 UTC] SoloYLibre: Interacción registrada - Photo: 161, Action: view, Count: 5
[06-Jul-2025 11:27:55 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 35
[06-Jul-2025 11:27:56 UTC] SoloYLibre: Interacción registrada - Photo: 231, Action: view, Count: 3
[06-Jul-2025 11:27:56 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 2
[06-Jul-2025 11:27:56 UTC] SoloYLibre: Interacción registrada - Photo: 166, Action: view, Count: 43
[06-Jul-2025 11:28:01 UTC] SoloYLibre: Interacción registrada - Photo: 160, Action: view, Count: 5
[06-Jul-2025 11:28:01 UTC] SoloYLibre: Interacción registrada - Photo: 267, Action: view, Count: 5
[06-Jul-2025 11:28:01 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 5
[06-Jul-2025 11:28:01 UTC] SoloYLibre: Interacción registrada - Photo: 159, Action: view, Count: 4
[06-Jul-2025 11:28:01 UTC] SoloYLibre: Interacción registrada - Photo: 165, Action: view, Count: 34
[06-Jul-2025 11:28:01 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: view, Count: 3
[06-Jul-2025 11:29:12 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:29:22 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 13
[06-Jul-2025 11:29:22 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 27
[06-Jul-2025 11:29:22 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 17
[06-Jul-2025 11:29:24 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 16
[06-Jul-2025 11:29:24 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 17
[06-Jul-2025 11:29:24 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 16
[06-Jul-2025 11:29:25 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 18
[06-Jul-2025 11:29:25 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 22
[06-Jul-2025 11:29:25 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 17
[06-Jul-2025 11:30:20 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:30:30 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 28
[06-Jul-2025 11:30:30 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 19
[06-Jul-2025 11:30:30 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 27
[06-Jul-2025 11:30:31 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 20
[06-Jul-2025 11:30:31 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 18
[06-Jul-2025 11:30:31 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 23
[06-Jul-2025 11:30:31 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 17
[06-Jul-2025 11:30:31 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 17
[06-Jul-2025 11:30:31 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 17
[06-Jul-2025 11:30:31 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 20
[06-Jul-2025 11:30:36 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 16
[06-Jul-2025 11:30:36 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 17
[06-Jul-2025 11:30:37 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 18
[06-Jul-2025 11:30:37 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 19
[06-Jul-2025 11:30:37 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: like, Count: 20
[06-Jul-2025 11:30:41 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: like, Count: 1
[06-Jul-2025 11:30:42 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: like, Count: 1
[06-Jul-2025 11:30:46 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 28
[06-Jul-2025 11:30:46 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 29
[06-Jul-2025 11:30:47 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 30
[06-Jul-2025 11:30:47 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 31
[06-Jul-2025 11:30:47 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 32
[06-Jul-2025 11:30:47 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 33
[06-Jul-2025 11:31:51 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:31:59 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 24
[06-Jul-2025 11:31:59 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 18
[06-Jul-2025 11:31:59 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 34
[06-Jul-2025 11:32:02 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 29
[06-Jul-2025 11:32:02 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 21
[06-Jul-2025 11:32:02 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 18
[06-Jul-2025 11:32:04 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 13
[06-Jul-2025 11:32:15 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 14
[06-Jul-2025 11:32:15 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 30
[06-Jul-2025 11:32:15 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 19
[06-Jul-2025 11:32:16 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 19
[06-Jul-2025 11:32:16 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 18
[06-Jul-2025 11:32:16 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 18
[06-Jul-2025 11:32:16 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 19
[06-Jul-2025 11:32:16 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 20
[06-Jul-2025 11:32:16 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 25
[06-Jul-2025 11:32:57 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:33:03 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 20
[06-Jul-2025 11:33:03 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 21
[06-Jul-2025 11:33:03 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 21
[06-Jul-2025 11:33:05 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 26
[06-Jul-2025 11:33:05 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 1
[06-Jul-2025 11:33:05 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 35
[06-Jul-2025 11:33:07 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: like, Count: 2
[06-Jul-2025 11:33:08 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: like, Count: 3
[06-Jul-2025 11:38:13 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 21
[06-Jul-2025 11:38:13 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 22
[06-Jul-2025 11:38:13 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 36
[06-Jul-2025 11:38:13 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 27
[06-Jul-2025 11:38:13 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 22
[06-Jul-2025 11:38:13 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 2
[06-Jul-2025 11:38:15 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: like, Count: 2
[06-Jul-2025 11:38:16 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: like, Count: 3
[06-Jul-2025 11:38:16 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: like, Count: 4
[06-Jul-2025 11:38:16 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: like, Count: 5
[06-Jul-2025 11:38:16 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: like, Count: 6
[06-Jul-2025 11:38:16 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: like, Count: 7
[06-Jul-2025 11:38:21 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 20
[06-Jul-2025 11:38:21 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 28
[06-Jul-2025 11:38:21 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 37
[06-Jul-2025 11:39:33 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 21
[06-Jul-2025 11:39:33 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 29
[06-Jul-2025 11:39:33 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 38
[06-Jul-2025 11:39:34 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 30
[06-Jul-2025 11:39:34 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 22
[06-Jul-2025 11:39:34 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 39
[06-Jul-2025 11:41:06 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:41:16 UTC] SoloYLibre: Interacción registrada - Photo: 243, Action: view, Count: 1
[06-Jul-2025 11:41:16 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 3
[06-Jul-2025 11:41:16 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 40
[06-Jul-2025 11:41:18 UTC] SoloYLibre: Interacción registrada - Photo: 245, Action: view, Count: 1
[06-Jul-2025 11:41:18 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 23
[06-Jul-2025 11:41:18 UTC] SoloYLibre: Interacción registrada - Photo: 248, Action: view, Count: 1
[06-Jul-2025 11:41:18 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 31
[06-Jul-2025 11:41:18 UTC] SoloYLibre: Interacción registrada - Photo: 238, Action: view, Count: 1
[06-Jul-2025 11:41:18 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 23
[06-Jul-2025 11:41:18 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 18
[06-Jul-2025 11:41:19 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 1
[06-Jul-2025 11:41:19 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 1
[06-Jul-2025 11:41:19 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 1
[06-Jul-2025 11:41:19 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 3
[06-Jul-2025 11:41:19 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 1
[06-Jul-2025 11:41:19 UTC] SoloYLibre: Interacción registrada - Photo: 247, Action: view, Count: 1
[06-Jul-2025 11:42:06 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:42:06 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:42:43 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:42:43 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:44:38 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 22
[06-Jul-2025 11:44:38 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 24
[06-Jul-2025 11:44:38 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 41
[06-Jul-2025 11:44:39 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 32
[06-Jul-2025 11:44:39 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 21
[06-Jul-2025 11:44:39 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 19
[06-Jul-2025 11:44:41 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 25
[06-Jul-2025 11:44:41 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 23
[06-Jul-2025 11:44:41 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 42
[06-Jul-2025 11:44:41 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 33
[06-Jul-2025 11:44:42 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 19
[06-Jul-2025 11:44:42 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 22
[06-Jul-2025 11:44:42 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 20
[06-Jul-2025 11:44:42 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 31
[06-Jul-2025 11:44:42 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 23
[06-Jul-2025 11:44:42 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 15
[06-Jul-2025 11:44:42 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 22
[06-Jul-2025 11:44:42 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 19
[06-Jul-2025 11:44:44 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: like, Count: 2
[06-Jul-2025 11:44:44 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: like, Count: 3
[06-Jul-2025 11:44:44 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: like, Count: 4
[06-Jul-2025 11:44:46 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 24
[06-Jul-2025 11:44:46 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 26
[06-Jul-2025 11:44:46 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 43
[06-Jul-2025 11:44:46 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 23
[06-Jul-2025 11:44:46 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 21
[06-Jul-2025 11:44:46 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 34
[06-Jul-2025 11:44:49 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: like, Count: 4
[06-Jul-2025 11:44:49 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: like, Count: 5
[06-Jul-2025 11:44:50 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: like, Count: 6
[06-Jul-2025 11:46:30 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:46:38 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 25
[06-Jul-2025 11:46:38 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 10
[06-Jul-2025 11:46:38 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 20
[06-Jul-2025 11:46:38 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 14
[06-Jul-2025 11:46:38 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 6
[06-Jul-2025 11:46:46 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: like, Count: 1
[06-Jul-2025 11:46:47 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: like, Count: 2
[06-Jul-2025 11:46:47 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: like, Count: 3
[06-Jul-2025 11:46:47 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: like, Count: 4
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 44
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 20
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 263, Action: view, Count: 1
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 261, Action: view, Count: 1
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 27
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 269, Action: view, Count: 4
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 35
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 257, Action: view, Count: 1
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 270, Action: view, Count: 5
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 22
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 23
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 9
[06-Jul-2025 11:46:48 UTC] SoloYLibre: Interacción registrada - Photo: 258, Action: view, Count: 1
[06-Jul-2025 11:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 24
[06-Jul-2025 11:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: view, Count: 4
[06-Jul-2025 11:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 9
[06-Jul-2025 11:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 32
[06-Jul-2025 11:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 20
[06-Jul-2025 11:46:49 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 4
[06-Jul-2025 11:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 24
[06-Jul-2025 11:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 268, Action: view, Count: 4
[06-Jul-2025 11:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 238, Action: view, Count: 2
[06-Jul-2025 11:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 16
[06-Jul-2025 11:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 267, Action: view, Count: 6
[06-Jul-2025 11:46:50 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 4
[06-Jul-2025 11:46:51 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 15
[06-Jul-2025 11:46:51 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 4
[06-Jul-2025 11:46:51 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 2
[06-Jul-2025 11:46:56 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:47:28 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:47:28 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:47:31 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:47:31 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:47:39 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:47:39 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:48:25 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:48:25 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:48:39 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:48:39 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:48:39 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 309 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 11:48:54 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 36
[06-Jul-2025 11:48:54 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 25
[06-Jul-2025 11:48:54 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 45
[06-Jul-2025 11:49:09 UTC] SoloYLibre: Interacción registrada - Photo: 243, Action: view, Count: 2
[06-Jul-2025 11:49:09 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 4
[06-Jul-2025 11:49:09 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 46
[06-Jul-2025 11:49:12 UTC] SoloYLibre: Interacción registrada - Photo: 245, Action: view, Count: 2
[06-Jul-2025 11:49:12 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 28
[06-Jul-2025 11:49:12 UTC] SoloYLibre: Interacción registrada - Photo: 248, Action: view, Count: 2
[06-Jul-2025 11:49:14 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 2
[06-Jul-2025 11:49:14 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 3
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 4
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 37
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 238, Action: view, Count: 3
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 24
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 23
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 3
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 2
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 2
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 5
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 2
[06-Jul-2025 11:49:15 UTC] SoloYLibre: Interacción registrada - Photo: 247, Action: view, Count: 2
[06-Jul-2025 11:49:19 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 1
[06-Jul-2025 11:49:20 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 2
[06-Jul-2025 11:49:20 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 3
[06-Jul-2025 11:49:20 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 4
[06-Jul-2025 11:49:20 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 5
[06-Jul-2025 11:49:20 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 6
[06-Jul-2025 11:49:21 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 7
[06-Jul-2025 11:49:21 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 8
[06-Jul-2025 11:49:21 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 9
[06-Jul-2025 11:49:22 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 3
[06-Jul-2025 11:49:22 UTC] SoloYLibre: Interacción registrada - Photo: 247, Action: view, Count: 3
[06-Jul-2025 11:49:22 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 6
[06-Jul-2025 11:49:24 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 10
[06-Jul-2025 11:49:27 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 11
[06-Jul-2025 11:49:27 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 12
[06-Jul-2025 11:49:27 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 13
[06-Jul-2025 11:49:27 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 14
[06-Jul-2025 11:49:27 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 15
[06-Jul-2025 11:49:28 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 16
[06-Jul-2025 11:49:28 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 17
[06-Jul-2025 11:49:28 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 18
[06-Jul-2025 11:49:28 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 19
[06-Jul-2025 11:49:29 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 20
[06-Jul-2025 11:49:29 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 21
[06-Jul-2025 11:49:29 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 22
[06-Jul-2025 11:49:29 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 23
[06-Jul-2025 11:49:34 UTC] SoloYLibre: Interacción registrada - Photo: 247, Action: view, Count: 4
[06-Jul-2025 11:49:34 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 7
[06-Jul-2025 11:49:34 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 4
[06-Jul-2025 11:49:37 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: like, Count: 24
[06-Jul-2025 11:49:40 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 5
[06-Jul-2025 11:49:40 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 6
[06-Jul-2025 11:49:40 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 7
[06-Jul-2025 11:49:41 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: share, Count: 1
[06-Jul-2025 11:49:47 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 3
[06-Jul-2025 11:49:47 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 3
[06-Jul-2025 11:49:47 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 4
[06-Jul-2025 11:49:47 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 24
[06-Jul-2025 11:49:47 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 25
[06-Jul-2025 11:49:47 UTC] SoloYLibre: Interacción registrada - Photo: 238, Action: view, Count: 4
[06-Jul-2025 11:49:47 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 38
[06-Jul-2025 11:49:49 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: like, Count: 1
[06-Jul-2025 11:49:51 UTC] SoloYLibre: Interacción registrada - Photo: 238, Action: view, Count: 5
[06-Jul-2025 11:49:51 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 26
[06-Jul-2025 11:49:51 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 4
[06-Jul-2025 11:49:51 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 4
[06-Jul-2025 11:49:51 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 5
[06-Jul-2025 11:49:51 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 39
[06-Jul-2025 11:49:51 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 25
[06-Jul-2025 11:49:52 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 5
[06-Jul-2025 11:49:52 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 27
[06-Jul-2025 11:49:52 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 40
[06-Jul-2025 11:49:52 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 6
[06-Jul-2025 11:49:52 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 26
[06-Jul-2025 11:49:52 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 5
[06-Jul-2025 11:49:52 UTC] SoloYLibre: Interacción registrada - Photo: 238, Action: view, Count: 6
[06-Jul-2025 11:49:54 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 6
[06-Jul-2025 11:49:54 UTC] SoloYLibre: Interacción registrada - Photo: 238, Action: view, Count: 7
[06-Jul-2025 11:49:54 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 6
[06-Jul-2025 11:49:54 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 28
[06-Jul-2025 11:49:54 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 41
[06-Jul-2025 11:49:54 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 7
[06-Jul-2025 11:49:55 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 27
[06-Jul-2025 11:49:56 UTC] SoloYLibre: Interacción registrada - Photo: 245, Action: view, Count: 3
[06-Jul-2025 11:49:56 UTC] SoloYLibre: Interacción registrada - Photo: 248, Action: view, Count: 3
[06-Jul-2025 11:49:57 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 29
[06-Jul-2025 11:49:57 UTC] SoloYLibre: Interacción registrada - Photo: 243, Action: view, Count: 3
[06-Jul-2025 11:49:57 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 5
[06-Jul-2025 11:49:57 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 47
[06-Jul-2025 11:50:11 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 3
[06-Jul-2025 11:50:11 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 4
[06-Jul-2025 11:50:11 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 5
[06-Jul-2025 11:50:12 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 6
[06-Jul-2025 11:50:12 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 7
[06-Jul-2025 11:50:12 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 8
[06-Jul-2025 11:50:12 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 9
[06-Jul-2025 11:50:12 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 10
[06-Jul-2025 11:50:13 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 11
[06-Jul-2025 11:50:13 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 12
[06-Jul-2025 11:50:13 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 13
[06-Jul-2025 11:50:13 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 14
[06-Jul-2025 11:50:13 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 15
[06-Jul-2025 11:50:13 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 16
[06-Jul-2025 11:50:14 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 17
[06-Jul-2025 11:50:14 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 18
[06-Jul-2025 11:50:14 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 19
[06-Jul-2025 11:50:14 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 20
[06-Jul-2025 11:50:14 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 21
[06-Jul-2025 11:50:14 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 22
[06-Jul-2025 11:50:15 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 23
[06-Jul-2025 11:50:15 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 24
[06-Jul-2025 11:50:15 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 25
[06-Jul-2025 11:50:15 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 26
[06-Jul-2025 11:50:15 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 27
[06-Jul-2025 11:50:17 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 6
[06-Jul-2025 11:50:17 UTC] SoloYLibre: Interacción registrada - Photo: 243, Action: view, Count: 4
[06-Jul-2025 11:50:17 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 48
[06-Jul-2025 11:50:19 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 28
[06-Jul-2025 11:50:44 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:50:44 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:50:49 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 11:50:49 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 11:51:34 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 11:51:38 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:51:40 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:51:44 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:51:45 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:51:45 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:51:49 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:51:51 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:51:53 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:51:54 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:51:54 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:52:24 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:52:24 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:52:25 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:52:28 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1555
[06-Jul-2025 11:52:51 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1646
[06-Jul-2025 11:53:15 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1646
[06-Jul-2025 11:53:35 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1646
[06-Jul-2025 11:54:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:12 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:12 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:17 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:18 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:18 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:18 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:20 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:20 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:21 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:34 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:54:34 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:55:05 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:55:16 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:55:16 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:55:17 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:55:17 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:55:51 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:55:52 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:55:52 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:55:52 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:08 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:22 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:23 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:23 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:28 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:29 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:29 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:36 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:36 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:56 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:56 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:56 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:58 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:58 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:59 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:56:59 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:57:07 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:57:08 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:57:13 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:57:28 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:57:44 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:58:04 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:58:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:58:34 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:58:37 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:59:15 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:59:16 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:59:16 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 11:59:58 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:00:02 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:00:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:00:10 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:00:14 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:00:15 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:06 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:07 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:07 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:07 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:07 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:08 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:08 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:08 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:08 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:08 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:09 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:10 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:19 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:20 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:20 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:20 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:26 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:01:38 UTC] PHP Parse error:  syntax error, unexpected end of file, expecting function (T_FUNCTION) or const (T_CONST) in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1660
[06-Jul-2025 12:02:09 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1651
[06-Jul-2025 12:02:33 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1651
[06-Jul-2025 12:02:33 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1651
[06-Jul-2025 12:02:33 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1651
[06-Jul-2025 12:02:33 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1651
[06-Jul-2025 12:02:58 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1651
[06-Jul-2025 12:02:58 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1651
[06-Jul-2025 12:03:07 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1651
[06-Jul-2025 12:04:00 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1588
[06-Jul-2025 12:04:01 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1588
[06-Jul-2025 12:04:10 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1588
[06-Jul-2025 12:04:11 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1588
[06-Jul-2025 12:05:22 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1550
[06-Jul-2025 12:05:23 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1550
[06-Jul-2025 12:06:09 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:06:09 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:06:09 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:06:11 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:06:12 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:06:12 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:06:42 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:06:43 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:06:43 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:06:45 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:07:17 UTC] PHP Fatal error:  Cannot redeclare SoloYLibre_Statistics_Dashboard::enqueue_dashboard_assets() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/includes/class-statistics-dashboard.php on line 1215
[06-Jul-2025 12:07:18 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '**************', 'love', 1, '2025-07-06 12:07:18') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 12:07:18 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '**************', 'amazing', 1, '2025-07-06 12:07:18') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 12:07:18 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '*************', 'camera', 1, '2025-07-06 12:07:18') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 12:07:18 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '***********', 'amazing', 1, '2025-07-06 12:07:18') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 12:07:18 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '**************', 'love', 1, '2025-07-06 12:07:18') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 12:07:18 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '*************', 'like', 1, '2025-07-06 12:07:18') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 12:07:18 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '*************', 'fire', 1, '2025-07-06 12:07:18') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 12:07:18 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '***********', 'camera', 1, '2025-07-06 12:07:18') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 12:07:19 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '**************', 'camera', 1, '2025-07-06 12:07:19') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 12:07:47 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 8
[06-Jul-2025 12:07:47 UTC] SoloYLibre: Interacción registrada - Photo: 245, Action: view, Count: 4
[06-Jul-2025 12:07:47 UTC] SoloYLibre: Interacción registrada - Photo: 243, Action: view, Count: 5
[06-Jul-2025 12:07:47 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 8
[06-Jul-2025 12:07:47 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 8
[06-Jul-2025 12:07:47 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 49
[06-Jul-2025 12:07:47 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 7
[06-Jul-2025 12:07:48 UTC] SoloYLibre: Interacción registrada - Photo: 248, Action: view, Count: 4
[06-Jul-2025 12:07:48 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 30
[06-Jul-2025 12:07:53 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:07:53 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:07:55 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:07:55 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:08:11 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:08:11 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:08:12 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:08:12 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:08:12 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:08:12 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:08:19 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:08:19 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:08:41 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:08:41 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:09:26 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:09:26 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:09:31 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:09:31 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:09:32 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 38
[06-Jul-2025 12:09:32 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 53
[06-Jul-2025 12:09:32 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 35
[06-Jul-2025 12:09:32 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 59
[06-Jul-2025 12:09:32 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 55
[06-Jul-2025 12:09:32 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 68
[06-Jul-2025 12:09:57 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:10:02 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:10:02 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:10:04 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Auth_Manager' does not have a method 'add_2fa_field' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 324
[06-Jul-2025 12:10:05 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 56
[06-Jul-2025 12:10:05 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 69
[06-Jul-2025 12:10:05 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 39
[06-Jul-2025 12:10:05 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 60
[06-Jul-2025 12:10:05 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 54
[06-Jul-2025 12:10:05 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 36
[06-Jul-2025 12:10:06 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 10
[06-Jul-2025 12:10:06 UTC] SoloYLibre: Interacción registrada - Photo: 255, Action: view, Count: 1
[06-Jul-2025 12:10:06 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 26
[06-Jul-2025 12:10:07 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 10
[06-Jul-2025 12:10:07 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 31
[06-Jul-2025 12:10:07 UTC] SoloYLibre: Interacción registrada - Photo: 254, Action: view, Count: 1
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 50
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 253, Action: view, Count: 1
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 270, Action: view, Count: 6
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 42
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 269, Action: view, Count: 5
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 252, Action: view, Count: 1
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 28
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 25
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 268, Action: view, Count: 5
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 29
[06-Jul-2025 12:10:08 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 21
[06-Jul-2025 12:10:09 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 7
[06-Jul-2025 12:10:09 UTC] SoloYLibre: Interacción registrada - Photo: 267, Action: view, Count: 7
[06-Jul-2025 12:10:09 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 33
[06-Jul-2025 12:10:09 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:10:09 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:10:16 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 26
[06-Jul-2025 12:10:16 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 5
[06-Jul-2025 12:10:16 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 9
[06-Jul-2025 12:10:17 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 24
[06-Jul-2025 12:10:17 UTC] SoloYLibre: Interacción registrada - Photo: 248, Action: view, Count: 5
[06-Jul-2025 12:10:17 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 7
[06-Jul-2025 12:10:18 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: like, Count: 1
[06-Jul-2025 12:10:18 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: like, Count: 2
[06-Jul-2025 12:10:18 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: like, Count: 3
[06-Jul-2025 12:10:18 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: like, Count: 4
[06-Jul-2025 12:10:19 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: like, Count: 5
[06-Jul-2025 12:10:19 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 17
[06-Jul-2025 12:10:19 UTC] SoloYLibre: Interacción registrada - Photo: 247, Action: view, Count: 5
[06-Jul-2025 12:10:19 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 5
[06-Jul-2025 12:10:19 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 21
[06-Jul-2025 12:10:19 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 16
[06-Jul-2025 12:10:19 UTC] SoloYLibre: Interacción registrada - Photo: 263, Action: view, Count: 2
[06-Jul-2025 12:10:19 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 8
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 245, Action: view, Count: 5
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: view, Count: 5
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 261, Action: view, Count: 2
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 260, Action: view, Count: 2
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 15
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 244, Action: view, Count: 1
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 21
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 243, Action: view, Count: 6
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 259, Action: view, Count: 1
[06-Jul-2025 12:10:20 UTC] SoloYLibre: Interacción registrada - Photo: 258, Action: view, Count: 2
[06-Jul-2025 12:10:22 UTC] SoloYLibre: Interacción registrada - Photo: 258, Action: like, Count: 1
[06-Jul-2025 12:10:22 UTC] SoloYLibre: Interacción registrada - Photo: 258, Action: like, Count: 2
[06-Jul-2025 12:10:22 UTC] SoloYLibre: Interacción registrada - Photo: 258, Action: like, Count: 3
[06-Jul-2025 12:10:22 UTC] SoloYLibre: Interacción registrada - Photo: 258, Action: like, Count: 4
[06-Jul-2025 12:10:22 UTC] SoloYLibre: Interacción registrada - Photo: 258, Action: like, Count: 5
[06-Jul-2025 12:10:23 UTC] SoloYLibre: Interacción registrada - Photo: 258, Action: view, Count: 3
[06-Jul-2025 12:10:24 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 11
[06-Jul-2025 12:10:24 UTC] SoloYLibre: Interacción registrada - Photo: 257, Action: view, Count: 2
[06-Jul-2025 12:10:24 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 7
[06-Jul-2025 12:10:24 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 9
[06-Jul-2025 12:10:25 UTC] SoloYLibre: Interacción registrada - Photo: 256, Action: view, Count: 1
[06-Jul-2025 12:10:25 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 9
[06-Jul-2025 12:10:25 UTC] SoloYLibre: Interacción registrada - Photo: 239, Action: view, Count: 1
[06-Jul-2025 12:10:27 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 55
[06-Jul-2025 12:10:27 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 37
[06-Jul-2025 12:10:27 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 61
[06-Jul-2025 12:10:28 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 57
[06-Jul-2025 12:10:28 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 70
[06-Jul-2025 12:10:29 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 37
[06-Jul-2025 12:10:29 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 38
[06-Jul-2025 12:10:29 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 39
[06-Jul-2025 12:10:31 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 38
[06-Jul-2025 12:10:31 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 56
[06-Jul-2025 12:10:31 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 58
[06-Jul-2025 12:10:31 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 40
[06-Jul-2025 12:10:31 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 71
[06-Jul-2025 12:10:31 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 62
[06-Jul-2025 12:10:34 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 12
[06-Jul-2025 12:10:34 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 13
[06-Jul-2025 12:10:34 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 14
[06-Jul-2025 12:10:34 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 15
[06-Jul-2025 12:10:35 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 36
[06-Jul-2025 12:10:35 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 40
[06-Jul-2025 12:10:35 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 43
[06-Jul-2025 12:10:38 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 57
[06-Jul-2025 12:10:38 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 39
[06-Jul-2025 12:10:38 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 59
[06-Jul-2025 12:10:38 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 63
[06-Jul-2025 12:10:38 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 41
[06-Jul-2025 12:10:38 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 72
[06-Jul-2025 12:10:39 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 16
[06-Jul-2025 12:10:40 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 17
[06-Jul-2025 12:10:40 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 18
[06-Jul-2025 12:10:40 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 19
[06-Jul-2025 12:10:40 UTC] SoloYLibre: Interacción registrada - Photo: 169, Action: view, Count: 41
[06-Jul-2025 12:10:40 UTC] SoloYLibre: Interacción registrada - Photo: 167, Action: view, Count: 37
[06-Jul-2025 12:10:40 UTC] SoloYLibre: Interacción registrada - Photo: 168, Action: view, Count: 44
[06-Jul-2025 12:10:41 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 40
[06-Jul-2025 12:10:41 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 60
[06-Jul-2025 12:10:43 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: like, Count: 3
[06-Jul-2025 12:10:43 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: like, Count: 4
[06-Jul-2025 12:10:44 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: like, Count: 5
[06-Jul-2025 12:10:44 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 73
[06-Jul-2025 12:10:44 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 58
[06-Jul-2025 12:10:45 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 59
[06-Jul-2025 12:10:45 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 41
[06-Jul-2025 12:10:45 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 61
[06-Jul-2025 12:10:47 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 20
[06-Jul-2025 12:10:48 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 21
[06-Jul-2025 12:10:48 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 22
[06-Jul-2025 12:10:48 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 23
[06-Jul-2025 12:10:48 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 24
[06-Jul-2025 12:10:48 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 25
[06-Jul-2025 12:10:49 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 26
[06-Jul-2025 12:10:49 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 42
[06-Jul-2025 12:10:49 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 64
[06-Jul-2025 12:10:49 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 60
[06-Jul-2025 12:10:49 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 62
[06-Jul-2025 12:10:49 UTC] SoloYLibre: Interacción registrada - Photo: 170, Action: view, Count: 42
[06-Jul-2025 12:10:49 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 74
[06-Jul-2025 12:10:50 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 40
[06-Jul-2025 12:10:51 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 41
[06-Jul-2025 12:10:51 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 42
[06-Jul-2025 12:10:51 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 43
[06-Jul-2025 12:10:51 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 44
[06-Jul-2025 12:10:51 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 45
[06-Jul-2025 12:10:51 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: like, Count: 46
[06-Jul-2025 12:10:52 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 61
[06-Jul-2025 12:10:52 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 63
[06-Jul-2025 12:10:52 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 43
[06-Jul-2025 12:10:53 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 65
[06-Jul-2025 12:10:53 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 75
[06-Jul-2025 12:10:54 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 27
[06-Jul-2025 12:10:54 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 28
[06-Jul-2025 12:10:54 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 29
[06-Jul-2025 12:10:54 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 30
[06-Jul-2025 12:10:55 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 31
[06-Jul-2025 12:10:55 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 32
[06-Jul-2025 12:10:55 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 33
[06-Jul-2025 12:10:55 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 34
[06-Jul-2025 12:10:55 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: like, Count: 35
[06-Jul-2025 12:10:56 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 64
[06-Jul-2025 12:10:56 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 65
[06-Jul-2025 12:10:56 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 66
[06-Jul-2025 12:10:57 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 67
[06-Jul-2025 12:10:57 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 68
[06-Jul-2025 12:10:57 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 44
[06-Jul-2025 12:10:57 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 62
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 45
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 63
[06-Jul-2025 12:10:59 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 69
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:10:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:00 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 64
[06-Jul-2025 12:11:00 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 70
[06-Jul-2025 12:11:00 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 46
[06-Jul-2025 12:11:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:00 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 47
[06-Jul-2025 12:11:00 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 65
[06-Jul-2025 12:11:00 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 71
[06-Jul-2025 12:11:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:01 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 72
[06-Jul-2025 12:11:01 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 66
[06-Jul-2025 12:11:01 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 48
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 73
[06-Jul-2025 12:11:02 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 67
[06-Jul-2025 12:11:02 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 49
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:02 UTC] SoloYLibre: Interacción registrada - Photo: 172, Action: view, Count: 76
[06-Jul-2025 12:11:02 UTC] SoloYLibre: Interacción registrada - Photo: 171, Action: view, Count: 66
[06-Jul-2025 12:11:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:03 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 50
[06-Jul-2025 12:11:03 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 68
[06-Jul-2025 12:11:03 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 74
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 253, Action: view, Count: 2
[06-Jul-2025 12:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 269, Action: view, Count: 6
[06-Jul-2025 12:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 289, Action: view, Count: 27
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 252, Action: view, Count: 2
[06-Jul-2025 12:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 268, Action: view, Count: 6
[06-Jul-2025 12:11:04 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 32
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 30
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 267, Action: view, Count: 8
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 51
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 43
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 8
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 6
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 29
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 284, Action: view, Count: 26
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 8
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 10
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:05 UTC] SoloYLibre: Interacción registrada - Photo: 283, Action: view, Count: 22
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 248, Action: view, Count: 6
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 6
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 282, Action: view, Count: 34
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 27
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 247, Action: view, Count: 6
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 263, Action: view, Count: 3
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 280, Action: view, Count: 25
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 9
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: view, Count: 6
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 279, Action: view, Count: 18
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 261, Action: view, Count: 3
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 245, Action: view, Count: 6
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 278, Action: view, Count: 22
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 244, Action: view, Count: 2
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 277, Action: view, Count: 17
[06-Jul-2025 12:11:06 UTC] SoloYLibre: Interacción registrada - Photo: 260, Action: view, Count: 3
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 16
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre: Interacción registrada - Photo: 259, Action: view, Count: 2
[06-Jul-2025 12:11:07 UTC] SoloYLibre: Interacción registrada - Photo: 243, Action: view, Count: 7
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre: Interacción registrada - Photo: 258, Action: view, Count: 4
[06-Jul-2025 12:11:07 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 8
[06-Jul-2025 12:11:07 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 22
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:07 UTC] SoloYLibre: Interacción registrada - Photo: 257, Action: view, Count: 3
[06-Jul-2025 12:11:07 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 10
[06-Jul-2025 12:11:07 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 12
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre: Interacción registrada - Photo: 256, Action: view, Count: 2
[06-Jul-2025 12:11:08 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 10
[06-Jul-2025 12:11:08 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 11
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre: Interacción registrada - Photo: 255, Action: view, Count: 2
[06-Jul-2025 12:11:08 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 11
[06-Jul-2025 12:11:08 UTC] SoloYLibre: Interacción registrada - Photo: 239, Action: view, Count: 2
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:08 UTC] SoloYLibre: Interacción registrada - Photo: 270, Action: view, Count: 7
[06-Jul-2025 12:11:08 UTC] SoloYLibre: Interacción registrada - Photo: 254, Action: view, Count: 2
[06-Jul-2025 12:11:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:09 UTC] SoloYLibre: Interacción registrada - Photo: 175, Action: view, Count: 51
[06-Jul-2025 12:11:09 UTC] SoloYLibre: Interacción registrada - Photo: 173, Action: view, Count: 69
[06-Jul-2025 12:11:09 UTC] SoloYLibre: Interacción registrada - Photo: 174, Action: view, Count: 75
[06-Jul-2025 12:11:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:15 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:11:15 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:11:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:33 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:11:33 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:11:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:34 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:11:34 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:11:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:48 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:11:49 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:11:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:08 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:08 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:08 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:08 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:08 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:12:08 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:12:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:15 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:15 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:15 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:15 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:15 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:12:15 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:12:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:16 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:16 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:16 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:16 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:16 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:12:16 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:12:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:17 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:12:17 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:12:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:18 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:18 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:18 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:18 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:18 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:12:18 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:12:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:48 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:48 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:48 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:48 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:48 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method SoloYLibre_Gallery_Plugin::render_statistics_page() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/soloylibre-gallery-plugin.php:582
Stack trace:
#0 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php(324): SoloYLibre_Gallery_Plugin->redirect_to_stats('')
#1 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#2 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /Applications/MAMP/htdocs/wp/wordpress/wp-admin/admin.php(260): do_action('%f0%9f%93%b8-so...')
#4 {main}
  thrown in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/soloylibre-gallery-plugin.php on line 582
[06-Jul-2025 12:12:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:50 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:50 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:50 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:12:50 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:12:50 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method SoloYLibre_Gallery_Plugin::render_statistics_page() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/soloylibre-gallery-plugin.php:582
Stack trace:
#0 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php(324): SoloYLibre_Gallery_Plugin->redirect_to_stats('')
#1 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#2 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /Applications/MAMP/htdocs/wp/wordpress/wp-admin/admin.php(260): do_action('%f0%9f%93%b8-so...')
#4 {main}
  thrown in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/soloylibre-gallery-plugin.php on line 582
[06-Jul-2025 12:12:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:12:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:07 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:07 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:07 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:07 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:13 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:13 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:13 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:13 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:13 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:13 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:21 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:21 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:21 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:21 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:24 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 11
[06-Jul-2025 12:13:24 UTC] SoloYLibre: Interacción registrada - Photo: 243, Action: view, Count: 8
[06-Jul-2025 12:13:24 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 52
[06-Jul-2025 12:13:24 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 11
[06-Jul-2025 12:13:24 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 11
[06-Jul-2025 12:13:24 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 10
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre: Interacción registrada - Photo: 248, Action: view, Count: 7
[06-Jul-2025 12:13:25 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 33
[06-Jul-2025 12:13:25 UTC] SoloYLibre: Interacción registrada - Photo: 245, Action: view, Count: 7
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 44
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre: Interacción registrada - Photo: 238, Action: view, Count: 8
[06-Jul-2025 12:13:25 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 31
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 30
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:25 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 9
[06-Jul-2025 12:13:25 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 9
[06-Jul-2025 12:13:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:26 UTC] SoloYLibre: Interacción registrada - Photo: 247, Action: view, Count: 7
[06-Jul-2025 12:13:29 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:29 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:29 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:29 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:29 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: like, Count: 29
[06-Jul-2025 12:13:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:34 UTC] SoloYLibre: Interacción registrada - Photo: 243, Action: view, Count: 9
[06-Jul-2025 12:13:34 UTC] SoloYLibre: Interacción registrada - Photo: 246, Action: view, Count: 11
[06-Jul-2025 12:13:34 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 53
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 34
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 245, Action: view, Count: 8
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 248, Action: view, Count: 8
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 45
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 251, Action: view, Count: 32
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 238, Action: view, Count: 9
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 31
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 12
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 242, Action: view, Count: 10
[06-Jul-2025 12:13:36 UTC] SoloYLibre: Interacción registrada - Photo: 250, Action: view, Count: 10
[06-Jul-2025 12:13:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:37 UTC] SoloYLibre: Interacción registrada - Photo: 247, Action: view, Count: 8
[06-Jul-2025 12:13:37 UTC] SoloYLibre: Interacción registrada - Photo: 249, Action: view, Count: 12
[06-Jul-2025 12:13:37 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 12
[06-Jul-2025 12:13:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:42 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:42 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:42 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:42 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:42 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method SoloYLibre_Gallery_Plugin::render_statistics_page() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/soloylibre-gallery-plugin.php:582
Stack trace:
#0 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php(324): SoloYLibre_Gallery_Plugin->redirect_to_stats('')
#1 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#2 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /Applications/MAMP/htdocs/wp/wordpress/wp-admin/admin.php(260): do_action('%f0%9f%93%b8-so...')
#4 {main}
  thrown in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/soloylibre-gallery-plugin.php on line 582
[06-Jul-2025 12:13:44 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:48 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:48 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:48 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:48 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:48 UTC] PHP Fatal error:  Uncaught Error: Call to undefined method SoloYLibre_Gallery_Plugin::render_statistics_page() in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/soloylibre-gallery-plugin.php:582
Stack trace:
#0 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php(324): SoloYLibre_Gallery_Plugin->redirect_to_stats('')
#1 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#2 /Applications/MAMP/htdocs/wp/wordpress/wp-includes/plugin.php(517): WP_Hook->do_action(Array)
#3 /Applications/MAMP/htdocs/wp/wordpress/wp-admin/admin.php(260): do_action('%f0%9f%93%b8-so...')
#4 {main}
  thrown in /Applications/MAMP/htdocs/wp/wordpress/wp-content/plugins/Archive/soloylibre-gallery-plugin.php on line 582
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:13:49 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:10 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:10 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:11 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:11 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:11 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:11 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:25 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:25 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:25 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:25 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:27 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:27 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:27 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:27 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:28 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:28 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:28 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:28 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:32 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:32 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:32 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:32 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:32 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:14:32 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:14:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:34 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:34 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:34 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:14:34 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:14:40 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:40 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:44 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:44 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:45 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:45 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:45 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:45 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:14:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:03 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:15:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:08 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:08 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:08 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:08 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:12 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:12 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:12 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:12 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:16 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:16 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:16 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:16 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:23 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:23 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:23 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:23 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:26 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:26 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:26 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:26 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:27 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:28 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:28 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:28 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:28 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:29 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:29 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:29 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:29 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:29 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:29 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:30 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:31 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:31 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:31 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:31 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:31 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:31 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:32 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:33 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:33 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:33 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:33 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:34 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:35 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:35 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:35 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:35 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:36 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:36 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:36 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:36 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:15:37 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:16:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:16:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:16:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:16:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:18:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:18:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:18:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:18:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:20:13 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:20:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:22:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:22:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:22:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:22:51 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:22:51 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:22:51 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:22:51 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:22:51 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:00 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:00 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:00 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:00 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:00 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:01 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:01 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:01 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:01 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:01 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:02 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:02 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:02 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:02 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:02 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:02 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:02 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:03 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:03 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:03 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:03 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:03 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:03 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:03 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:03 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:04 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:04 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:04 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:04 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:04 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:04 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:04 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:04 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:04 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:04 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:04 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:04 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:05 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:05 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:05 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:05 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:23:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:23:05 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:23:05 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:23:05 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:24:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:33 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:24:33 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:24:33 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 12:24:33 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 12:24:33 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 12:24:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:43 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:24:43 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:24:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:24:56 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:25:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:25:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:26:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:26:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:27:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:27:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:28:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:28:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:29:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:29:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:30:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:30:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:31:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:31:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:32:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:32:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:32:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:32:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:32:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:33:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:20 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:21 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 12:34:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:26 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:34:26 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:34:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:28 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:34:28 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:34:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:33 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:34:33 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:34:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:35 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: view, Count: 35
[06-Jul-2025 12:34:35 UTC] SoloYLibre: Interacción registrada - Photo: 281, Action: view, Count: 28
[06-Jul-2025 12:34:35 UTC] SoloYLibre: Interacción registrada - Photo: 240, Action: view, Count: 13
[06-Jul-2025 12:34:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:37 UTC] SoloYLibre: Interacción registrada - Photo: 275, Action: view, Count: 17
[06-Jul-2025 12:34:37 UTC] SoloYLibre: Interacción registrada - Photo: 287, Action: view, Count: 54
[06-Jul-2025 12:34:37 UTC] SoloYLibre: Interacción registrada - Photo: 241, Action: view, Count: 13
[06-Jul-2025 12:34:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:37 UTC] SoloYLibre: Interacción registrada - Photo: 285, Action: view, Count: 32
[06-Jul-2025 12:34:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:37 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: view, Count: 23
[06-Jul-2025 12:34:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:37 UTC] SoloYLibre: Interacción registrada - Photo: 286, Action: view, Count: 46
[06-Jul-2025 12:34:40 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:40 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:40 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: like, Count: 3
[06-Jul-2025 12:34:40 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: like, Count: 4
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: like, Count: 5
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: like, Count: 6
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: like, Count: 7
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: like, Count: 8
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:41 UTC] SoloYLibre: Interacción registrada - Photo: 274, Action: like, Count: 9
[06-Jul-2025 12:34:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:49 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 5
[06-Jul-2025 12:34:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:49 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 6
[06-Jul-2025 12:34:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:49 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 7
[06-Jul-2025 12:34:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:50 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 8
[06-Jul-2025 12:34:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:50 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 9
[06-Jul-2025 12:34:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:50 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 10
[06-Jul-2025 12:34:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:50 UTC] SoloYLibre: Interacción registrada - Photo: 288, Action: like, Count: 11
[06-Jul-2025 12:34:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:55 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:34:55 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:34:55 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:34:55 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:34:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:57 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:34:57 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:34:57 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:34:57 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:34:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:34:58 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:34:58 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:35:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:35:01 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:35:01 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:35:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:35:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:35:06 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:35:06 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:35:06 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 12:35:06 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 12:35:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:35:08 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 12:35:08 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 12:44:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:45:13 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:45:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:45:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:47:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:47:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:47:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:49:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:49:26 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:49:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:51:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:51:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:51:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:53:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:53:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:53:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:55:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:55:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 12:55:29 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:05:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:05:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:05:51 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '**************', 'fire', 1, '2025-07-06 13:05:51') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 13:05:51 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query INSERT INTO `wp_soloylibre_interactions` (`photo_id`, `user_id`, `user_ip`, `reaction_type`, `is_simulated`, `created_at`) VALUES (276, NULL, '**************', 'camera', 1, '2025-07-06 13:05:51') made by do_action_ref_array('soloylibre_generate_random_interactions'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_User_Interactions->generate_random_interactions_cron, SoloYLibre_User_Interactions->generate_random_interactions, SoloYLibre_User_Interactions->add_reaction
[06-Jul-2025 13:06:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:06:49 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:06:49 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:06:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:06:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:06:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:06:53 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:06:53 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:06:53 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 13:06:53 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 13:06:53 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 13:06:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:06:54 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:06:54 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:06:54 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 13:06:54 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 13:06:54 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 13:06:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:03 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:07:03 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:07:03 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:07:03 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:13 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:36 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:07:36 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:07:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:37 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:07:37 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:07:38 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:07:39 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:07:39 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:07:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:09:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:11:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:11:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:11:45 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:11:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:11:50 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:11:50 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:11:56 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:12:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:12:49 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:12:49 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:13:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:13:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:16 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:14:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:30 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:14:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:33 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:14:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:34 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:14:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:36 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:14:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:36 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:14:40 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:40 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:41 UTC] SoloYLibre: Interacción registrada - Photo: 223, Action: view, Count: 1
[06-Jul-2025 13:14:41 UTC] SoloYLibre: Interacción registrada - Photo: 228, Action: view, Count: 1
[06-Jul-2025 13:14:41 UTC] SoloYLibre: Interacción registrada - Photo: 218, Action: view, Count: 1
[06-Jul-2025 13:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:42 UTC] SoloYLibre: Interacción registrada - Photo: 222, Action: view, Count: 1
[06-Jul-2025 13:14:42 UTC] SoloYLibre: Interacción registrada - Photo: 217, Action: view, Count: 1
[06-Jul-2025 13:14:42 UTC] SoloYLibre: Interacción registrada - Photo: 227, Action: view, Count: 1
[06-Jul-2025 13:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:42 UTC] SoloYLibre: Interacción registrada - Photo: 216, Action: view, Count: 1
[06-Jul-2025 13:14:42 UTC] SoloYLibre: Interacción registrada - Photo: 226, Action: view, Count: 1
[06-Jul-2025 13:14:42 UTC] SoloYLibre: Interacción registrada - Photo: 221, Action: view, Count: 1
[06-Jul-2025 13:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:42 UTC] SoloYLibre: Interacción registrada - Photo: 225, Action: view, Count: 1
[06-Jul-2025 13:14:42 UTC] SoloYLibre: Interacción registrada - Photo: 215, Action: view, Count: 1
[06-Jul-2025 13:14:42 UTC] SoloYLibre: Interacción registrada - Photo: 220, Action: view, Count: 1
[06-Jul-2025 13:14:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:43 UTC] SoloYLibre: Interacción registrada - Photo: 214, Action: view, Count: 1
[06-Jul-2025 13:14:43 UTC] SoloYLibre: Interacción registrada - Photo: 224, Action: view, Count: 1
[06-Jul-2025 13:14:43 UTC] SoloYLibre: Interacción registrada - Photo: 219, Action: view, Count: 1
[06-Jul-2025 13:14:45 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:45 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:45 UTC] SoloYLibre: Interacción registrada - Photo: 220, Action: like, Count: 1
[06-Jul-2025 13:14:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:14:46 UTC] SoloYLibre: Interacción registrada - Photo: 220, Action: like, Count: 2
[06-Jul-2025 13:14:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:15:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:15:00 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:15:00 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:15:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:15:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:15:21 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:15:21 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:15:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:15:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:15:38 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:15:38 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:15:38 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:15:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:15:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:13 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:20 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:38 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:39 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:16:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:16:47 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:16:47 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:17:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:17:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:19:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:20:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:21:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:22:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:23:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:24:25 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:24:25 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:31 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:24:31 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:24:31 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:24:32 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 13:24:32 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 13:24:32 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 13:24:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:25:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:25:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:26:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:26:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:26:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:27:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:27:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:28:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:28:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:28:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:29:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:29:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:30:05 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:30:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:30:38 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:31:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:31:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:32:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:32:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:32:39 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:33:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:33:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:34:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:34:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:34:40 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:35:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:35:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:36:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:36:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:36:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:37:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:37:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:38:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:38:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:38:42 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:39:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:39:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:40:10 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:40:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:40:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:41:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:41:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:42:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:42:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:42:44 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:43:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:43:56 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:44:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:44:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:44:45 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:06 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:45:06 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:45:06 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 13:45:06 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 13:45:08 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:08 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:45:08 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:45:08 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 13:45:08 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 13:45:08 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 13:45:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:09 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:45:09 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:45:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:12 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:45:12 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:45:13 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:13 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:45:13 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:45:13 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 13:45:13 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 13:45:13 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 13:45:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:19 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:45:19 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:45:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:20 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:20 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:45:20 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:45:20 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:45:20 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Editor_Integration' does not have a method 'enqueue_block_assets' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 324
[06-Jul-2025 13:45:20 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:21 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:27 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:31 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:41 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:55 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:45:55 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:45:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:56 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:45:57 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:45:57 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:45:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:04 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:46:04 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:46:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:07 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:12 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:46:12 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:46:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:13 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:44 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:44 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:46:44 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:46:44 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:46 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:50 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:46:50 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:46:50 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 13:46:50 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 13:46:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:52 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:46:52 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:46:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:53 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:46:53 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:46:53 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:46:54 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Editor_Integration' does not have a method 'enqueue_block_assets' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 324
[06-Jul-2025 13:46:54 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'add_meta_boxes' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:46:54 UTC] WordPress database error Unknown column 'reaction_type' in 'field list' for query SELECT reaction_type, COUNT(*) as count 
             FROM wp_soloylibre_interactions 
             WHERE photo_id = 327 
             GROUP BY reaction_type 
             ORDER BY count DESC made by require('wp-admin/edit-form-blocks.php'), the_block_editor_meta_boxes, do_meta_boxes, SoloYLibre_User_Interactions->render_interactions_meta_box, SoloYLibre_User_Interactions->get_photo_reactions
[06-Jul-2025 13:46:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:54 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:55 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:46:55 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:46:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:46:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:47:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:48:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:48:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:49:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:50:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:50:31 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:50:31 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:50:31 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:50:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:50:48 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:50:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:50:51 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:50:51 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:50:51 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1450
[06-Jul-2025 13:50:51 UTC] PHP Warning:  Cannot modify header information - headers already sent by (output started at /Applications/MAMP/htdocs/wp/wordpress/wp-includes/script-loader.php:3015) in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/pluggable.php on line 1453
[06-Jul-2025 13:50:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:50:57 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:50:57 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:51:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:03 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:51:03 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:51:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:09 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:51:09 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:51:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:14 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:51:14 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:51:20 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:20 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:51:20 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:51:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:49 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:51:49 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:51:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:51:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:52:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:52:09 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:52:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:52:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:52:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:52:17 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:52:17 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:52:49 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:53:32 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:53:57 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:54:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:54:10 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:54:15 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:54:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:54:50 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:55:33 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:55:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:56:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:56:11 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:56:16 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:56:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:56:51 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:30 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:30 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:57:30 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:57:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:37 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:57:37 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:57:43 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:43 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:57:43 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:57:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:52 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:57:52 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:57:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:53 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:57:59 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:57:59 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:58:03 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:12 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:14 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:58:14 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:58:17 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:19 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:20 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:20 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:58:20 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:58:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:24 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:58:24 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:58:31 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:31 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:58:31 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:58:31 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:40 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:40 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:58:40 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:58:52 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:55 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:58:55 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:58:55 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:58:55 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:58:55 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:59:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:04 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:04 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:59:04 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:59:04 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 13:59:04 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 13:59:04 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 13:59:14 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:14 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:59:14 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:59:14 UTC] WordPress database error Unknown column 'views' in 'field list' for query SELECT SUM(views) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_views
[06-Jul-2025 13:59:14 UTC] WordPress database error Unknown column 'likes' in 'field list' for query SELECT SUM(likes) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_likes
[06-Jul-2025 13:59:14 UTC] WordPress database error Unknown column 'shares' in 'field list' for query SELECT SUM(shares) FROM wp_soloylibre_interactions made by do_action('%f0%9f%93%b8-soloylibre_page_soloylibre-statistics'), WP_Hook->do_action, WP_Hook->apply_filters, SoloYLibre_Gallery_Plugin->redirect_to_stats, SoloYLibre_Gallery_Plugin->render_statistics_page, SoloYLibre_Gallery_Plugin->get_comprehensive_statistics, SoloYLibre_Gallery_Plugin->get_total_shares
[06-Jul-2025 13:59:18 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:22 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:22 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:22 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:22 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:22 UTC] SoloYLibre: Interacción registrada - Photo: 269, Action: view, Count: 7
[06-Jul-2025 13:59:22 UTC] SoloYLibre: Interacción registrada - Photo: 273, Action: view, Count: 13
[06-Jul-2025 13:59:22 UTC] SoloYLibre: Interacción registrada - Photo: 265, Action: view, Count: 9
[06-Jul-2025 13:59:22 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:22 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:22 UTC] SoloYLibre: Interacción registrada - Photo: 268, Action: view, Count: 7
[06-Jul-2025 13:59:22 UTC] SoloYLibre: Interacción registrada - Photo: 263, Action: view, Count: 4
[06-Jul-2025 13:59:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:23 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:23 UTC] SoloYLibre: Interacción registrada - Photo: 267, Action: view, Count: 9
[06-Jul-2025 13:59:23 UTC] SoloYLibre: Interacción registrada - Photo: 262, Action: view, Count: 7
[06-Jul-2025 13:59:23 UTC] SoloYLibre: Interacción registrada - Photo: 272, Action: view, Count: 12
[06-Jul-2025 13:59:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:24 UTC] SoloYLibre: Interacción registrada - Photo: 271, Action: view, Count: 12
[06-Jul-2025 13:59:24 UTC] SoloYLibre: Interacción registrada - Photo: 261, Action: view, Count: 4
[06-Jul-2025 13:59:24 UTC] SoloYLibre: Interacción registrada - Photo: 266, Action: view, Count: 7
[06-Jul-2025 13:59:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:24 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:24 UTC] SoloYLibre: Interacción registrada - Photo: 260, Action: view, Count: 4
[06-Jul-2025 13:59:24 UTC] SoloYLibre: Interacción registrada - Photo: 270, Action: view, Count: 8
[06-Jul-2025 13:59:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:25 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:25 UTC] SoloYLibre: Interacción registrada - Photo: 264, Action: view, Count: 7
[06-Jul-2025 13:59:25 UTC] SoloYLibre: Interacción registrada - Photo: 259, Action: view, Count: 3
[06-Jul-2025 13:59:28 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:34 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:35 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:36 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:37 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:47 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:47 UTC] SoloYLibre_Statistics_Dashboard: Adding statistics page to menu
[06-Jul-2025 13:59:47 UTC] SoloYLibre_Statistics_Dashboard: Page added with hook: %f0%9f%93%b8-soloylibre_page_soloylibre-statistics
[06-Jul-2025 13:59:58 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 13:59:58 UTC] PHP Warning:  call_user_func_array() expects parameter 1 to be a valid callback, class 'SoloYLibre_Gallery_Plugin' does not have a method 'save_photo_meta' in /Applications/MAMP/htdocs/wp/wordpress/wp-includes/class-wp-hook.php on line 326
[06-Jul-2025 13:59:59 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:00 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:00 UTC] SoloYLibre: Interacción registrada - Photo: 208, Action: view, Count: 1
[06-Jul-2025 14:00:00 UTC] SoloYLibre: Interacción registrada - Photo: 213, Action: view, Count: 1
[06-Jul-2025 14:00:00 UTC] SoloYLibre: Interacción registrada - Photo: 203, Action: view, Count: 1
[06-Jul-2025 14:00:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:01 UTC] SoloYLibre: Interacción registrada - Photo: 207, Action: view, Count: 1
[06-Jul-2025 14:00:01 UTC] SoloYLibre: Interacción registrada - Photo: 212, Action: view, Count: 1
[06-Jul-2025 14:00:01 UTC] SoloYLibre: Interacción registrada - Photo: 202, Action: view, Count: 1
[06-Jul-2025 14:00:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:01 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:01 UTC] SoloYLibre: Interacción registrada - Photo: 206, Action: view, Count: 1
[06-Jul-2025 14:00:01 UTC] SoloYLibre: Interacción registrada - Photo: 211, Action: view, Count: 1
[06-Jul-2025 14:00:01 UTC] SoloYLibre: Interacción registrada - Photo: 201, Action: view, Count: 1
[06-Jul-2025 14:00:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:02 UTC] SoloYLibre: Interacción registrada - Photo: 200, Action: view, Count: 1
[06-Jul-2025 14:00:02 UTC] SoloYLibre: Interacción registrada - Photo: 205, Action: view, Count: 1
[06-Jul-2025 14:00:02 UTC] SoloYLibre: Interacción registrada - Photo: 210, Action: view, Count: 1
[06-Jul-2025 14:00:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:02 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:02 UTC] SoloYLibre: Interacción registrada - Photo: 204, Action: view, Count: 1
[06-Jul-2025 14:00:02 UTC] SoloYLibre: Interacción registrada - Photo: 199, Action: view, Count: 1
[06-Jul-2025 14:00:02 UTC] SoloYLibre: Interacción registrada - Photo: 209, Action: view, Count: 1
[06-Jul-2025 14:00:06 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
[06-Jul-2025 14:00:20 UTC] SoloYLibre_Statistics_Dashboard: Constructor called
