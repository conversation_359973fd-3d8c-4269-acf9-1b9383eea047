<?php
/**
 * Script simple de debug para SoloYLibre Gallery Pro
 */

// Cargar WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "🔍 DEBUG SIMPLE - PLUGIN SOLOYLIBRE GALLERY PRO\n\n";

// 1. Verificar plugin activo
$plugin_file = 'Archive/soloylibre-gallery-plugin.php';
$is_active = is_plugin_active($plugin_file);
echo "1. Plugin activo: " . ($is_active ? 'SÍ' : 'NO') . "\n";

// 2. Verificar archivos principales
$plugin_dir = WP_PLUGIN_DIR . '/Archive/';
$main_file = $plugin_dir . 'soloylibre-gallery-plugin.php';
echo "2. Archivo principal existe: " . (file_exists($main_file) ? 'SÍ' : 'NO') . "\n";

// 3. Verificar clases principales
$classes = array(
    'includes/class-gallery-styles.php',
    'includes/class-content-protection.php',
    'includes/class-album-manager.php'
);

echo "3. Archivos de clases:\n";
foreach ($classes as $class_file) {
    $path = $plugin_dir . $class_file;
    echo "   - $class_file: " . (file_exists($path) ? 'SÍ' : 'NO') . "\n";
}

// 4. Verificar páginas admin
$admin_files = array(
    'admin/admin-page.php',
    'admin/settings-page.php'
);

echo "4. Páginas admin:\n";
foreach ($admin_files as $admin_file) {
    $path = $plugin_dir . $admin_file;
    echo "   - $admin_file: " . (file_exists($path) ? 'SÍ' : 'NO') . "\n";
}

// 5. Verificar custom post type
$post_types = get_post_types();
$has_photo_type = in_array('soloylibre_photo', $post_types);
echo "5. Custom post type 'soloylibre_photo': " . ($has_photo_type ? 'SÍ' : 'NO') . "\n";

// 6. Verificar opciones
$options = get_option('soloylibre_gallery_photographer_name');
echo "6. Opciones configuradas: " . ($options ? 'SÍ' : 'NO') . "\n";

// 7. Verificar errores recientes
echo "7. Verificando errores...\n";

// Intentar cargar una clase para ver errores
try {
    if (file_exists($plugin_dir . 'includes/class-gallery-styles.php')) {
        require_once $plugin_dir . 'includes/class-gallery-styles.php';
        echo "   - class-gallery-styles.php: CARGA OK\n";
    }
} catch (Exception $e) {
    echo "   - ERROR en class-gallery-styles.php: " . $e->getMessage() . "\n";
}

// 8. Verificar si el plugin se inicializa
if (class_exists('SoloYLibre_Gallery_Plugin')) {
    echo "8. Clase principal SoloYLibre_Gallery_Plugin: SÍ\n";
    
    try {
        $instance = SoloYLibre_Gallery_Plugin::get_instance();
        echo "   - Instancia creada: SÍ\n";
    } catch (Exception $e) {
        echo "   - ERROR creando instancia: " . $e->getMessage() . "\n";
    }
} else {
    echo "8. Clase principal SoloYLibre_Gallery_Plugin: NO\n";
}

// 9. Verificar menús admin
global $menu, $submenu;
$has_menu = false;
if (is_array($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && strpos($menu_item[2], 'soloylibre') !== false) {
            $has_menu = true;
            break;
        }
    }
}
echo "9. Menú admin registrado: " . ($has_menu ? 'SÍ' : 'NO') . "\n";

// 10. Verificar hooks
global $wp_filter;
$has_init_hook = false;
if (isset($wp_filter['init'])) {
    foreach ($wp_filter['init']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function']) && 
                is_object($callback['function'][0]) && 
                get_class($callback['function'][0]) === 'SoloYLibre_Gallery_Plugin') {
                $has_init_hook = true;
                break 2;
            }
        }
    }
}
echo "10. Hook 'init' registrado: " . ($has_init_hook ? 'SÍ' : 'NO') . "\n";

echo "\n🎯 CONCLUSIÓN:\n";
if ($is_active && file_exists($main_file) && $has_photo_type) {
    echo "✅ Plugin parece estar funcionando básicamente\n";
} else {
    echo "❌ Plugin tiene problemas críticos\n";
}

echo "\n🔗 Para probar el admin, ve a:\n";
echo "http://localhost:8888/wp/wordpress/wp-admin/admin.php?page=soloylibre-gallery\n";

?>
