<?php
/**
 * Script para crear página de galería de muestra
 * Desarrollado por JEYKO AI para <PERSON>nac<PERSON> (JoseTusabe)
 */

// Cargar WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "📄 Creando página de galería SoloYLibre...\n\n";

// Contenido de la página
$page_content = '
<div style="text-align: center; margin-bottom: 40px;">
    <h1 style="color: #667eea; font-size: 3rem; margin-bottom: 10px;">📸 SoloYLibre Photography</h1>
    <h2 style="color: #764ba2; font-size: 1.5rem; margin-bottom: 20px;"><PERSON>nac<PERSON> (JoseTusabe)</h2>
    <p style="font-size: 1.2rem; color: #666;">🇩🇴 San José de Ocoa, República Dominicana / USA 🇺🇸</p>
    <p style="font-size: 1rem; color: #888;">📞 ************ | 📧 <EMAIL></p>
</div>

<div style="background: linear-gradient(135deg, #CE1126 0%, #FFFFFF 50%, #002D62 100%); padding: 20px; border-radius: 15px; margin-bottom: 40px; text-align: center;">
    <h3 style="color: white; text-shadow: 2px 2px 4px rgba(0,0,0,0.5); margin: 0;">🇩🇴 Galería Dominicana Oficial</h3>
</div>

<h3>🎨 Galería Estilo Dominicano</h3>
[soloylibre_gallery style="dominican"]

<hr style="margin: 60px 0; border: none; height: 2px; background: linear-gradient(to right, #CE1126, #002D62);">

<h3>📱 Galería Estilo TikTok</h3>
[soloylibre_gallery style="tiktok" limit="3"]

<hr style="margin: 60px 0; border: none; height: 2px; background: linear-gradient(to right, #667eea, #764ba2);">

<h3>🏞️ Galería Grid Profesional</h3>
[soloylibre_gallery style="grid"]

<hr style="margin: 60px 0; border: none; height: 2px; background: linear-gradient(to right, #CE1126, #002D62);">

<h3>🌅 Solo Paisajes</h3>
[soloylibre_gallery style="professional" category="paisajes"]

<hr style="margin: 60px 0; border: none; height: 2px; background: linear-gradient(to right, #667eea, #764ba2);">

<h3>🎭 Solo Cultura Dominicana</h3>
[soloylibre_gallery style="masonry" category="cultura"]

<div style="background: rgba(102, 126, 234, 0.1); padding: 30px; border-radius: 15px; margin-top: 60px; text-align: center;">
    <h3 style="color: #667eea; margin-bottom: 20px;">🌐 Visita Nuestros Sitios Web</h3>
    <p>
        <a href="https://josetusabe.com" target="_blank" style="color: #CE1126; text-decoration: none; margin: 0 10px;">🌐 josetusabe.com</a> |
        <a href="https://soloylibre.com" target="_blank" style="color: #002D62; text-decoration: none; margin: 0 10px;">🌐 soloylibre.com</a> |
        <a href="https://1and1photo.com" target="_blank" style="color: #CE1126; text-decoration: none; margin: 0 10px;">📸 1and1photo.com</a> |
        <a href="https://joselencarnacion.com" target="_blank" style="color: #002D62; text-decoration: none; margin: 0 10px;">👨‍💼 joselencarnacion.com</a>
    </p>
    <p style="margin-top: 20px; color: #666;">
        <strong>📍 Ubicación:</strong> San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸<br>
        <strong>📞 Teléfono:</strong> ************<br>
        <strong>📧 Email:</strong> <EMAIL>
    </p>
</div>

<div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
    <p style="color: #666; font-style: italic;">
        "Capturando la belleza de República Dominicana y el mundo, una foto a la vez."<br>
        <strong>- Jose L Encarnacion (JoseTusabe)</strong>
    </p>
</div>
';

// Crear la página
$page_data = array(
    'post_title' => 'SoloYLibre Gallery - Jose L Encarnacion Photography',
    'post_content' => $page_content,
    'post_status' => 'publish',
    'post_type' => 'page',
    'post_author' => 1,
    'post_slug' => 'soloylibre-gallery'
);

// Verificar si la página ya existe
$existing_page = get_page_by_path('soloylibre-gallery');

if ($existing_page) {
    // Actualizar página existente
    $page_data['ID'] = $existing_page->ID;
    $page_id = wp_update_post($page_data);
    echo "✅ Página actualizada: SoloYLibre Gallery (ID: $page_id)\n";
} else {
    // Crear nueva página
    $page_id = wp_insert_post($page_data);
    echo "✅ Página creada: SoloYLibre Gallery (ID: $page_id)\n";
}

if ($page_id) {
    // Agregar metadata personalizada
    update_post_meta($page_id, '_soloylibre_page_type', 'gallery_showcase');
    update_post_meta($page_id, '_soloylibre_photographer', 'Jose L Encarnacion');
    
    echo "\n🎉 ¡Página de galería creada exitosamente!\n";
    echo "🔗 URL de la página: " . get_permalink($page_id) . "\n";
    echo "⚙️ Editar página: " . admin_url('post.php?post=' . $page_id . '&action=edit') . "\n";
    
    // Crear también una página de inicio personalizada
    $home_content = '
    <div style="text-align: center; padding: 60px 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px; margin-bottom: 40px;">
        <h1 style="font-size: 4rem; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">📸 SoloYLibre</h1>
        <h2 style="font-size: 2rem; margin-bottom: 10px; opacity: 0.9;">Jose L Encarnacion Photography</h2>
        <p style="font-size: 1.3rem; margin-bottom: 30px; opacity: 0.8;">🇩🇴 Capturando la belleza de República Dominicana</p>
        <a href="/wp/wordpress/soloylibre-gallery/" style="background: #CE1126; color: white; padding: 15px 30px; border-radius: 25px; text-decoration: none; font-weight: bold; font-size: 1.1rem; display: inline-block; transition: all 0.3s ease;">
            🖼️ Ver Galería Completa
        </a>
    </div>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin: 40px 0;">
        <div style="text-align: center; padding: 30px; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
            <h3 style="color: #CE1126; margin-bottom: 15px;">🇩🇴 Fotografía Dominicana</h3>
            <p>Especializado en capturar la esencia y belleza de República Dominicana, desde paisajes hasta cultura.</p>
        </div>
        <div style="text-align: center; padding: 30px; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
            <h3 style="color: #002D62; margin-bottom: 15px;">📱 Estilo Moderno</h3>
            <p>Galerías con diseño moderno, incluyendo estilos TikTok, Grid profesional y más.</p>
        </div>
        <div style="text-align: center; padding: 30px; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
            <h3 style="color: #667eea; margin-bottom: 15px;">🌍 Internacional</h3>
            <p>Desde San José de Ocoa hasta USA, documentando experiencias y momentos únicos.</p>
        </div>
    </div>
    
    <div style="text-align: center; margin: 60px 0;">
        <h3 style="color: #333; margin-bottom: 30px;">🖼️ Vista Previa de la Galería</h3>
        [soloylibre_gallery style="grid" limit="6"]
        <div style="margin-top: 30px;">
            <a href="/wp/wordpress/soloylibre-gallery/" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; border-radius: 25px; text-decoration: none; font-weight: bold;">
                Ver Todas las Fotos →
            </a>
        </div>
    </div>
    ';
    
    $home_data = array(
        'post_title' => 'Inicio - SoloYLibre Photography',
        'post_content' => $home_content,
        'post_status' => 'publish',
        'post_type' => 'page',
        'post_author' => 1,
        'post_slug' => 'inicio-soloylibre'
    );
    
    $home_page_id = wp_insert_post($home_data);
    if ($home_page_id) {
        echo "✅ Página de inicio creada (ID: $home_page_id)\n";
        echo "🏠 URL inicio: " . get_permalink($home_page_id) . "\n";
    }
    
} else {
    echo "❌ Error creando la página\n";
}

echo "\n📋 Páginas creadas:\n";
echo "   🏠 Inicio: " . home_url('/inicio-soloylibre/') . "\n";
echo "   🖼️ Galería: " . home_url('/soloylibre-gallery/') . "\n";
echo "   ⚙️ Admin: " . admin_url('admin.php?page=soloylibre-gallery') . "\n";

?>
