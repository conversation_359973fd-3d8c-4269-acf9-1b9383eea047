<?php
/**
 * Test Title Visibility - Frontend Gallery Title
 * Prueba de visibilidad del título - Título de galería frontend
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

require_once('wp-load.php');

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>📸 Prueba de Visibilidad del Título - SoloYLibre Gallery Pro</title>";
echo "<meta charset=\"UTF-8\">";
echo "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">";

wp_head();

echo "<style>";
echo "body { margin: 0; padding: 20px; background: #f8f9fa; color: #333; font-family: Arial, sans-serif; }";
echo ".container { max-width: 1400px; margin: 0 auto; }";
echo ".test-header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 30px; border-radius: 15px; margin-bottom: 30px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }";
echo ".test-section { background: white; border-radius: 15px; padding: 25px; margin: 20px 0; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }";
echo ".test-title { color: #CE1126; font-size: 1.5rem; margin-bottom: 15px; }";
echo ".shortcode-display { background: #2d3748; color: #e2e8f0; padding: 12px; border-radius: 8px; font-family: monospace; margin: 10px 0; font-size: 0.9rem; }";
echo ".info { color: #17a2b8; }";
echo ".success { color: #28a745; }";
echo ".warning { color: #ffc107; }";
echo "</style>";

echo "<script src=\"https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js\"></script>";

echo "</head>";
echo "<body>";

echo "<div class=\"container\">";

echo "<div class=\"test-header\">";
echo "<h1>📸 Prueba de Visibilidad del Título</h1>";
echo "<h2>SoloYLibre Gallery Pro v5.1.1</h2>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe) - Verificación de Títulos Frontend</p>";
echo "</div>";

// Obtener fotos de ejemplo
$all_photos = get_posts(array(
    'post_type' => 'attachment',
    'post_mime_type' => 'image',
    'posts_per_page' => 12,
    'post_status' => 'inherit'
));

if (!empty($all_photos)) {
    $photo_ids = implode(',', wp_list_pluck($all_photos, 'ID'));
    
    // 1. Título principal mejorado
    echo "<div class=\"test-section\">";
    echo "<h2 class=\"test-title\">1. 📸 Título Principal Mejorado</h2>";
    echo "<p class=\"success\">✅ Título con z-index: 1000, text-shadow mejorado y fondo semi-transparente</p>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"dominican\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\"]</div>";
    
    echo do_shortcode("[soloylibre_gallery style=\"dominican\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 6)) . "\" columns=\"3\"]");
    echo "</div>";
    
    // 2. Diferentes estilos con títulos visibles
    echo "<div class=\"test-section\">";
    echo "<h2 class=\"test-title\">2. 🎨 Todos los Estilos con Títulos Visibles</h2>";
    echo "<p class=\"info\">Verificando que el título se vea correctamente en todos los estilos</p>";
    
    echo "<h4>🧱 Masonry (Por Defecto)</h4>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"masonry\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 4)) . "\" columns=\"4\"]</div>";
    echo do_shortcode("[soloylibre_gallery style=\"masonry\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 4)) . "\" columns=\"4\"]");
    
    echo "<h4>📱 Grid Moderno</h4>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"grid\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 4, 4)) . "\" columns=\"4\"]</div>";
    echo do_shortcode("[soloylibre_gallery style=\"grid\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 4, 4)) . "\" columns=\"4\"]");
    
    echo "<h4>💼 Profesional</h4>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"professional\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 8, 4)) . "\" columns=\"4\"]</div>";
    echo do_shortcode("[soloylibre_gallery style=\"professional\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 8, 4)) . "\" columns=\"4\"]");
    
    echo "</div>";
    
    // 3. Prueba con diferentes fondos
    echo "<div class=\"test-section\">";
    echo "<h2 class=\"test-title\">3. 🌈 Títulos con Diferentes Fondos</h2>";
    echo "<p class=\"info\">Verificando visibilidad con fondos personalizados</p>";
    
    echo "<h4>🔵 Fondo Azul</h4>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"dominican\" background_color=\"#e3f2fd\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 3)) . "\" columns=\"3\"]</div>";
    echo do_shortcode("[soloylibre_gallery style=\"dominican\" background_color=\"#e3f2fd\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 0, 3)) . "\" columns=\"3\"]");
    
    echo "<h4>🌅 Gradiente Personalizado</h4>";
    echo "<div class=\"shortcode-display\">[soloylibre_gallery style=\"masonry\" background_gradient=\"linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 3, 3)) . "\" columns=\"3\"]</div>";
    echo do_shortcode("[soloylibre_gallery style=\"masonry\" background_gradient=\"linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)\" ids=\"" . implode(',', array_slice(wp_list_pluck($all_photos, 'ID'), 3, 3)) . "\" columns=\"3\"]");
    
    echo "</div>";
    
} else {
    echo "<div class=\"test-section\">";
    echo "<h2 class=\"test-title\">❌ No hay fotos disponibles</h2>";
    echo "<p>Sube algunas imágenes a la biblioteca de medios para probar la visibilidad del título.</p>";
    echo "</div>";
}

// Información técnica
echo "<div class=\"test-section\">";
echo "<h2 class=\"test-title\">🔧 Mejoras Implementadas para el Título</h2>";

echo "<div style=\"display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;\">";

echo "<div>";
echo "<h4>🎯 Z-Index Mejorado</h4>";
echo "<ul>";
echo "<li><strong>z-index: 1000</strong> - Siempre al frente</li>";
echo "<li><strong>position: relative</strong> - Control de posición</li>";
echo "<li><strong>!important</strong> - Fuerza la aplicación</li>";
echo "<li><strong>visibility: visible</strong> - Siempre visible</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>✨ Text-Shadow Mejorado</h4>";
echo "<ul>";
echo "<li><strong>3px 3px 6px</strong> - Sombra más pronunciada</li>";
echo "<li><strong>rgba(0,0,0,0.5)</strong> - Mayor opacidad</li>";
echo "<li><strong>Mejor contraste</strong> - Legible en cualquier fondo</li>";
echo "<li><strong>Efecto 3D</strong> - Más profesional</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>🎨 Header Mejorado</h4>";
echo "<ul>";
echo "<li><strong>backdrop-filter: blur(10px)</strong> - Efecto glassmorphism</li>";
echo "<li><strong>rgba(255,255,255,0.95)</strong> - Fondo semi-transparente</li>";
echo "<li><strong>box-shadow</strong> - Elevación visual</li>";
echo "<li><strong>border-radius: 15px</strong> - Bordes redondeados</li>";
echo "</ul>";
echo "</div>";

echo "<div>";
echo "<h4>🛡️ CSS Defensivo</h4>";
echo "<ul>";
echo "<li><strong>Selectores específicos</strong> - Para todos los estilos</li>";
echo "<li><strong>!important</strong> - Sobrescribe otros CSS</li>";
echo "<li><strong>display: block</strong> - Fuerza visualización</li>";
echo "<li><strong>opacity: 1</strong> - Opacidad completa</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "</div>";

// Código CSS aplicado
echo "<div class=\"test-section\">";
echo "<h2 class=\"test-title\">📝 Código CSS Aplicado</h2>";

echo "<h4>🎯 CSS Principal del Título:</h4>";
echo "<div class=\"shortcode-display\">";
echo ".gallery-title {<br>";
echo "&nbsp;&nbsp;position: relative !important;<br>";
echo "&nbsp;&nbsp;z-index: 1000 !important;<br>";
echo "&nbsp;&nbsp;text-shadow: 3px 3px 6px rgba(0,0,0,0.5);<br>";
echo "&nbsp;&nbsp;font-weight: 800;<br>";
echo "&nbsp;&nbsp;display: block !important;<br>";
echo "&nbsp;&nbsp;visibility: visible !important;<br>";
echo "&nbsp;&nbsp;opacity: 1 !important;<br>";
echo "}";
echo "</div>";

echo "<h4>🏠 CSS del Header:</h4>";
echo "<div class=\"shortcode-display\">";
echo ".gallery-header {<br>";
echo "&nbsp;&nbsp;z-index: 1000;<br>";
echo "&nbsp;&nbsp;background: rgba(255, 255, 255, 0.95);<br>";
echo "&nbsp;&nbsp;backdrop-filter: blur(10px);<br>";
echo "&nbsp;&nbsp;box-shadow: 0 8px 25px rgba(0,0,0,0.1);<br>";
echo "}";
echo "</div>";

echo "</div>";

echo "</div>"; // container

// JavaScript
echo "<script>";
echo "console.log('📸 SoloYLibre: Página de prueba de títulos cargada');";

echo "// Confetti de celebración";
echo "setTimeout(function() {";
echo "    if (typeof confetti !== 'undefined') {";
echo "        confetti({";
echo "            particleCount: 100,";
echo "            spread: 70,";
echo "            origin: { y: 0.6 },";
echo "            colors: ['#CE1126', '#002D62', '#FFD700']";
echo "        });";
echo "    }";
echo "}, 1000);";

echo "// Verificar visibilidad de títulos";
echo "jQuery(document).ready(function($) {";
echo "    setTimeout(function() {";
echo "        $('.gallery-title').each(function() {";
echo "            var title = $(this);";
echo "            var zIndex = title.css('z-index');";
echo "            var visibility = title.css('visibility');";
echo "            var opacity = title.css('opacity');";
echo "            ";
echo "            console.log('Título encontrado:', {";
echo "                text: title.text(),";
echo "                zIndex: zIndex,";
echo "                visibility: visibility,";
echo "                opacity: opacity,";
echo "                isVisible: title.is(':visible')";
echo "            });";
echo "        });";
echo "    }, 2000);";
echo "});";

echo "</script>";

wp_footer();

echo "</body>";
echo "</html>";
?>
