<?php
// Prueba rápida del plugin SoloYLibre
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>⚡ Prueba Rápida - SoloYLibre</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f0f0f0; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }";
echo ".status { padding: 15px; margin: 10px 0; border-radius: 8px; }";
echo ".success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }";
echo ".error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }";
echo ".warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }";
echo ".btn { background: #CE1126; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }";
echo ".btn:hover { background: #002D62; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class=\"container\">";
echo "<div class=\"header\">";
echo "<h1>⚡ Prueba Rápida - SoloYLibre Gallery Pro</h1>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe)</p>";
echo "<p>⏰ " . date("Y-m-d H:i:s") . "</p>";
echo "</div>";

// Test WordPress
echo "<h2>🔧 Estado de WordPress</h2>";
try {
    require_once("wp-load.php");
    echo "<div class=\"status success\">✅ WordPress cargado correctamente</div>";
    $wp_ok = true;
} catch (Exception $e) {
    echo "<div class=\"status error\">❌ Error: " . $e->getMessage() . "</div>";
    $wp_ok = false;
}

if ($wp_ok) {
    // Test Plugin
    echo "<h2>🔌 Estado del Plugin</h2>";
    $active_plugins = get_option("active_plugins", array());
    $plugin_active = false;
    foreach ($active_plugins as $plugin) {
        if (strpos($plugin, "Archive") !== false) {
            $plugin_active = true;
            break;
        }
    }
    
    if ($plugin_active) {
        echo "<div class=\"status success\">✅ Plugin SoloYLibre activo</div>";
    } else {
        echo "<div class=\"status error\">❌ Plugin SoloYLibre no está activo</div>";
    }
    
    // Test Classes
    echo "<h2>📚 Clases del Plugin</h2>";
    $classes = array(
        "SoloYLibre_Gallery_Plugin" => "Plugin Principal",
        "SoloYLibre_Simple_Wizard" => "Wizard Simplificado",
        "SoloYLibre_API_Manager" => "API Manager",
        "SoloYLibre_Statistics_Dashboard" => "Dashboard de Estadísticas"
    );
    
    foreach ($classes as $class => $name) {
        if (class_exists($class)) {
            echo "<div class=\"status success\">✅ $name ($class)</div>";
        } else {
            echo "<div class=\"status error\">❌ $name ($class) no encontrada</div>";
        }
    }
    
    // Test Database
    echo "<h2>🗄️ Base de Datos</h2>";
    global $wpdb;
    $tables = array("wp_soloylibre_albums", "wp_soloylibre_photos", "wp_soloylibre_interactions");
    
    foreach ($tables as $table) {
        $exists = $wpdb->get_var("SHOW TABLES LIKE \"$table\"") == $table;
        if ($exists) {
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
            echo "<div class=\"status success\">✅ " . basename($table) . " ($count registros)</div>";
        } else {
            echo "<div class=\"status error\">❌ " . basename($table) . " no existe</div>";
        }
    }
    
    // Test User
    echo "<h2>👤 Usuario Actual</h2>";
    $current_user = wp_get_current_user();
    if ($current_user->ID > 0) {
        echo "<div class=\"status success\">✅ Logueado como: " . $current_user->user_login . "</div>";
        if (current_user_can("manage_options")) {
            echo "<div class=\"status success\">✅ Permisos de administrador</div>";
        } else {
            echo "<div class=\"status warning\">⚠️ Sin permisos de administrador</div>";
        }
    } else {
        echo "<div class=\"status warning\">⚠️ No hay usuario logueado</div>";
    }
    
    // Test API
    echo "<h2>🌐 API REST</h2>";
    if (class_exists("SoloYLibre_API_Manager")) {
        echo "<div class=\"status success\">✅ API Manager disponible</div>";
        echo "<div class=\"status success\">✅ Namespace: soloylibre/v1</div>";
    } else {
        echo "<div class=\"status error\">❌ API Manager no disponible</div>";
    }
}

// Check for recent errors
echo "<h2>🐛 Errores Recientes</h2>";
$error_files = array("wp-content/debug.log", "error_log");
$errors_found = false;

foreach ($error_files as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (!empty(trim($content))) {
            $errors_found = true;
            echo "<div class=\"status error\">❌ Errores encontrados en $file</div>";
            echo "<pre style=\"background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto;\">";
            echo htmlspecialchars(substr($content, -500)); // Últimos 500 caracteres
            echo "</pre>";
        }
    }
}

if (!$errors_found) {
    echo "<div class=\"status success\">✅ No se encontraron errores recientes</div>";
}

// Action buttons
echo "<h2>🔗 Acciones</h2>";
echo "<a href=\"auto-login-soloylibre.php\" class=\"btn\">🔑 Login Automático</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-simple-wizard\" class=\"btn\">🧙‍♂️ Wizard</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-statistics\" class=\"btn\">📊 Estadísticas</a>";
echo "<a href=\"monitor-errores.php\" class=\"btn\">📊 Monitor</a>";

echo "<hr>";
echo "<p style=\"text-align: center; color: #666;\">🇩🇴 SoloYLibre Photography - Jose L Encarnacion (JoseTusabe)</p>";
echo "<p style=\"text-align: center; color: #666;\">📞 718-713-5500 | 📧 <EMAIL></p>";

echo "</div>";
echo "</body>";
echo "</html>";
?>