<?php
/**
 * Debug Statistics Page
 * Página de debug para verificar el estado de las estadísticas
 */

require_once('wp-load.php');

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🔍 Debug Estadísticas - SoloYLibre</title>";
echo "<meta charset=\"UTF-8\">";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f1f1f1; }";
echo ".debug-section { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".warning { color: #ffc107; }";
echo ".info { color: #17a2b8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔍 Debug Estadísticas SoloYLibre</h1>";

// 1. Verificar si WordPress está cargado
echo "<div class=\"debug-section\">";
echo "<h3>1. Estado de WordPress</h3>";
if (function_exists('wp_get_current_user')) {
    echo "<p class=\"success\">✅ WordPress cargado correctamente</p>";
    echo "<p>Versión: " . get_bloginfo('version') . "</p>";
} else {
    echo "<p class=\"error\">❌ WordPress no está cargado</p>";
}
echo "</div>";

// 2. Verificar si el plugin está activo
echo "<div class=\"debug-section\">";
echo "<h3>2. Estado del Plugin</h3>";
if (function_exists('is_plugin_active')) {
    if (is_plugin_active('Archive/soloylibre-gallery-plugin.php')) {
        echo "<p class=\"success\">✅ Plugin SoloYLibre activo</p>";
    } else {
        echo "<p class=\"error\">❌ Plugin SoloYLibre inactivo</p>";
    }
} else {
    echo "<p class=\"warning\">⚠️ No se puede verificar estado del plugin</p>";
}
echo "</div>";

// 3. Verificar si la clase existe
echo "<div class=\"debug-section\">";
echo "<h3>3. Estado de la Clase</h3>";
if (class_exists('SoloYLibre_Statistics_Dashboard')) {
    echo "<p class=\"success\">✅ Clase SoloYLibre_Statistics_Dashboard existe</p>";
    
    // Intentar crear una instancia
    try {
        $dashboard = new SoloYLibre_Statistics_Dashboard();
        echo "<p class=\"success\">✅ Instancia creada correctamente</p>";
    } catch (Exception $e) {
        echo "<p class=\"error\">❌ Error al crear instancia: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p class=\"error\">❌ Clase SoloYLibre_Statistics_Dashboard no existe</p>";
}
echo "</div>";

// 4. Verificar archivos
echo "<div class=\"debug-section\">";
echo "<h3>4. Verificación de Archivos</h3>";

$files_to_check = array(
    'wp-content/plugins/Archive/soloylibre-gallery-plugin.php' => 'Plugin Principal',
    'wp-content/plugins/Archive/includes/class-statistics-dashboard.php' => 'Dashboard de Estadísticas'
);

foreach ($files_to_check as $file => $name) {
    if (file_exists($file)) {
        echo "<p class=\"success\">✅ $name: Archivo existe</p>";
        echo "<p class=\"info\">Tamaño: " . number_format(filesize($file)) . " bytes</p>";
    } else {
        echo "<p class=\"error\">❌ $name: Archivo no encontrado</p>";
    }
}
echo "</div>";

// 5. Verificar menús de admin
echo "<div class=\"debug-section\">";
echo "<h3>5. Menús de Admin</h3>";
if (function_exists('get_option')) {
    global $menu, $submenu;
    
    echo "<h4>Menús principales:</h4>";
    if (isset($menu) && is_array($menu)) {
        foreach ($menu as $menu_item) {
            if (isset($menu_item[2]) && strpos($menu_item[2], 'soloylibre') !== false) {
                echo "<p class=\"success\">✅ Menú encontrado: " . $menu_item[0] . " (" . $menu_item[2] . ")</p>";
            }
        }
    }
    
    echo "<h4>Submenús:</h4>";
    if (isset($submenu) && is_array($submenu)) {
        foreach ($submenu as $parent => $items) {
            if (strpos($parent, 'soloylibre') !== false) {
                echo "<p class=\"info\">📁 Parent: $parent</p>";
                foreach ($items as $item) {
                    echo "<p class=\"success\">✅ Submenú: " . $item[0] . " (" . $item[2] . ")</p>";
                }
            }
        }
    }
} else {
    echo "<p class=\"warning\">⚠️ No se pueden verificar menús</p>";
}
echo "</div>";

// 6. Verificar hooks
echo "<div class=\"debug-section\">";
echo "<h3>6. Hooks Registrados</h3>";
if (function_exists('has_action')) {
    $hooks_to_check = array(
        'admin_menu',
        'admin_enqueue_scripts',
        'wp_ajax_refresh_statistics',
        'wp_ajax_export_statistics'
    );
    
    foreach ($hooks_to_check as $hook) {
        if (has_action($hook)) {
            echo "<p class=\"success\">✅ Hook '$hook' tiene acciones registradas</p>";
        } else {
            echo "<p class=\"warning\">⚠️ Hook '$hook' sin acciones</p>";
        }
    }
} else {
    echo "<p class=\"warning\">⚠️ No se pueden verificar hooks</p>";
}
echo "</div>";

// 7. Intentar acceso directo
echo "<div class=\"debug-section\">";
echo "<h3>7. Acceso Directo a Estadísticas</h3>";
echo "<p><a href=\"wp-admin/admin.php?page=soloylibre-statistics\" target=\"_blank\">🔗 Ir a Estadísticas</a></p>";
echo "<p><a href=\"wp-admin/admin.php?page=soloylibre-main\" target=\"_blank\">🔗 Ir a Menú Principal</a></p>";
echo "<p><a href=\"wp-admin/\" target=\"_blank\">🔗 Ir a WordPress Admin</a></p>";
echo "</div>";

// 8. Logs recientes
echo "<div class=\"debug-section\">";
echo "<h3>8. Logs de Errores Recientes</h3>";
$log_file = '/Applications/MAMP/logs/php_error.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    if (empty(trim($log_content))) {
        echo "<p class=\"success\">✅ No hay errores en los logs</p>";
    } else {
        $lines = explode("\n", $log_content);
        $recent_lines = array_slice($lines, -10);
        echo "<pre style=\"background: #f8f9fa; padding: 10px; border-radius: 4px;\">";
        foreach ($recent_lines as $line) {
            if (!empty(trim($line))) {
                echo htmlspecialchars($line) . "\n";
            }
        }
        echo "</pre>";
    }
} else {
    echo "<p class=\"warning\">⚠️ Archivo de log no encontrado</p>";
}
echo "</div>";

echo "</body>";
echo "</html>";
?>
