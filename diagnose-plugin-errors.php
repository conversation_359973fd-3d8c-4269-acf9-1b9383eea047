<?php
/**
 * Script de diagnóstico para SoloYLibre Gallery Pro
 * Identifica todos los errores y problemas del plugin
 */

// Cargar WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "🔍 DIAGNÓSTICO COMPLETO - PLUGIN SOLOYLIBRE GALLERY PRO\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// 1. Verificar estado del plugin
echo "1. 📊 ESTADO DEL PLUGIN:\n";
$plugin_file = 'Archive/soloylibre-gallery-plugin.php';
$plugin_path = WP_PLUGIN_DIR . '/' . $plugin_file;
$is_active = is_plugin_active($plugin_file);

echo "   🔌 Plugin activo: " . ($is_active ? '✅ SÍ' : '❌ NO') . "\n";
echo "   📁 Archivo principal existe: " . (file_exists($plugin_path) ? '✅ SÍ' : '❌ NO') . "\n";

if (file_exists($plugin_path)) {
    echo "   📏 Tamaño archivo: " . filesize($plugin_path) . " bytes\n";
}

// 2. Verificar archivos de clases
echo "\n2. 📁 ARCHIVOS DE CLASES:\n";
$plugin_dir = WP_PLUGIN_DIR . '/Archive/';
$required_classes = array(
    'includes/class-gallery-styles.php' => 'SoloYLibre_Gallery_Styles',
    'includes/class-content-protection.php' => 'SoloYLibre_Content_Protection',
    'includes/class-album-manager.php' => 'SoloYLibre_Album_Manager',
    'includes/class-photo-states-manager.php' => 'SoloYLibre_Photo_States_Manager',
    'includes/class-user-interactions.php' => 'SoloYLibre_User_Interactions',
    'includes/class-membership-integration.php' => 'SoloYLibre_Membership_Integration',
    'includes/class-admin.php' => 'SoloYLibre_Gallery_Admin',
    'includes/class-dashboard.php' => 'SoloYLibre_Gallery_Dashboard',
    'includes/class-database.php' => 'SoloYLibre_Gallery_Database'
);

foreach ($required_classes as $file => $class_name) {
    $file_path = $plugin_dir . $file;
    $file_exists = file_exists($file_path);
    echo "   " . ($file_exists ? '✅' : '❌') . " $file\n";
    
    if ($file_exists) {
        // Verificar si la clase está definida en el archivo
        $file_content = file_get_contents($file_path);
        $class_defined = strpos($file_content, "class $class_name") !== false;
        echo "      📝 Clase $class_name definida: " . ($class_defined ? '✅ SÍ' : '❌ NO') . "\n";
        
        // Verificar errores de sintaxis
        $syntax_check = php_check_syntax($file_path);
        echo "      🔍 Sintaxis correcta: " . ($syntax_check ? '✅ SÍ' : '❌ NO') . "\n";
    }
}

// 3. Verificar archivos de assets
echo "\n3. 🎨 ARCHIVOS DE ASSETS:\n";
$asset_files = array(
    'assets/css/gallery-styles.css' => 'Estilos principales',
    'assets/css/admin-styles.css' => 'Estilos admin',
    'assets/js/gallery-frontend.js' => 'JavaScript frontend',
    'assets/js/admin-scripts.js' => 'JavaScript admin'
);

foreach ($asset_files as $file => $description) {
    $file_path = $plugin_dir . $file;
    $exists = file_exists($file_path);
    echo "   " . ($exists ? '✅' : '❌') . " $file - $description\n";
    if ($exists) {
        echo "      📏 Tamaño: " . filesize($file_path) . " bytes\n";
    }
}

// 4. Verificar páginas de admin
echo "\n4. 📄 PÁGINAS DE ADMINISTRACIÓN:\n";
$admin_pages = array(
    'admin/admin-page.php' => 'Página principal',
    'admin/settings-page.php' => 'Página de configuración',
    'admin/albums-page.php' => 'Página de álbumes',
    'admin/modern-dashboard.php' => 'Dashboard moderno'
);

foreach ($admin_pages as $file => $description) {
    $file_path = $plugin_dir . $file;
    $exists = file_exists($file_path);
    echo "   " . ($exists ? '✅' : '❌') . " $file - $description\n";
}

// 5. Verificar si las clases se pueden cargar
echo "\n5. 🔄 CARGA DE CLASES:\n";
foreach ($required_classes as $file => $class_name) {
    $file_path = $plugin_dir . $file;
    if (file_exists($file_path)) {
        try {
            require_once $file_path;
            $class_exists = class_exists($class_name);
            echo "   " . ($class_exists ? '✅' : '❌') . " $class_name\n";
            
            if (!$class_exists) {
                echo "      ⚠️ Error: Clase no se pudo cargar\n";
            }
        } catch (Exception $e) {
            echo "   ❌ $class_name - Error: " . $e->getMessage() . "\n";
        } catch (ParseError $e) {
            echo "   ❌ $class_name - Error de sintaxis: " . $e->getMessage() . "\n";
        }
    }
}

// 6. Verificar base de datos
echo "\n6. 🗄️ BASE DE DATOS:\n";
global $wpdb;

$tables = array(
    $wpdb->prefix . 'soloylibre_albums' => 'Álbumes',
    $wpdb->prefix . 'soloylibre_photo_meta' => 'Metadata de fotos',
    $wpdb->prefix . 'soloylibre_user_interactions' => 'Interacciones',
    $wpdb->prefix . 'soloylibre_projects' => 'Proyectos',
    $wpdb->prefix . 'soloylibre_photographer_settings' => 'Configuraciones'
);

foreach ($tables as $table => $description) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
    echo "   " . ($exists ? '✅' : '❌') . " $table - $description\n";
    
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
        echo "      📊 Registros: $count\n";
    }
}

// 7. Verificar custom post types
echo "\n7. 📝 CUSTOM POST TYPES:\n";
$post_types = get_post_types(array('_builtin' => false), 'objects');
$soloylibre_post_type_exists = isset($post_types['soloylibre_photo']);

echo "   " . ($soloylibre_post_type_exists ? '✅' : '❌') . " soloylibre_photo\n";

if ($soloylibre_post_type_exists) {
    $photo_count = wp_count_posts('soloylibre_photo');
    echo "      📊 Fotos publicadas: " . ($photo_count->publish ?? 0) . "\n";
    echo "      📊 Fotos en borrador: " . ($photo_count->draft ?? 0) . "\n";
}

// 8. Verificar taxonomías
echo "\n8. 🏷️ TAXONOMÍAS:\n";
$taxonomies = array('photo_category', 'photo_tag');

foreach ($taxonomies as $taxonomy) {
    $exists = taxonomy_exists($taxonomy);
    echo "   " . ($exists ? '✅' : '❌') . " $taxonomy\n";
    
    if ($exists) {
        $terms = get_terms(array('taxonomy' => $taxonomy, 'hide_empty' => false));
        echo "      📊 Términos: " . count($terms) . "\n";
    }
}

// 9. Verificar opciones del plugin
echo "\n9. ⚙️ OPCIONES DEL PLUGIN:\n";
$options = array(
    'soloylibre_gallery_gallery_style',
    'soloylibre_gallery_photos_per_page',
    'soloylibre_gallery_photographer_name',
    'soloylibre_gallery_photographer_brand'
);

foreach ($options as $option) {
    $value = get_option($option);
    $exists = $value !== false;
    echo "   " . ($exists ? '✅' : '❌') . " $option\n";
    if ($exists) {
        echo "      💾 Valor: " . (is_array($value) ? json_encode($value) : $value) . "\n";
    }
}

// 10. Verificar errores de PHP
echo "\n10. 🐛 ERRORES RECIENTES:\n";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $errors = file_get_contents($error_log);
    $recent_errors = array_slice(explode("\n", $errors), -10);
    
    foreach ($recent_errors as $error) {
        if (!empty($error) && strpos($error, 'soloylibre') !== false) {
            echo "   ⚠️ $error\n";
        }
    }
} else {
    echo "   ℹ️ No se encontró log de errores o está vacío\n";
}

// 11. Verificar permisos de archivos
echo "\n11. 🔒 PERMISOS DE ARCHIVOS:\n";
$plugin_dir_perms = substr(sprintf('%o', fileperms($plugin_dir)), -4);
echo "   📁 Directorio plugin: $plugin_dir_perms\n";

$wp_content_perms = substr(sprintf('%o', fileperms(WP_CONTENT_DIR)), -4);
echo "   📁 wp-content: $wp_content_perms\n";

// 12. Verificar hooks y acciones
echo "\n12. 🪝 HOOKS Y ACCIONES:\n";
$hooks_to_check = array(
    'init',
    'wp_enqueue_scripts',
    'admin_enqueue_scripts',
    'admin_menu'
);

foreach ($hooks_to_check as $hook) {
    global $wp_filter;
    $has_soloylibre_hooks = false;
    
    if (isset($wp_filter[$hook])) {
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                if (is_array($callback['function']) && 
                    is_object($callback['function'][0]) && 
                    strpos(get_class($callback['function'][0]), 'SoloYLibre') !== false) {
                    $has_soloylibre_hooks = true;
                    break 2;
                }
            }
        }
    }
    
    echo "   " . ($has_soloylibre_hooks ? '✅' : '❌') . " $hook\n";
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "🎯 RESUMEN DE PROBLEMAS ENCONTRADOS\n";
echo str_repeat("=", 70) . "\n";

// Generar lista de problemas
$problems = array();

if (!$is_active) {
    $problems[] = "Plugin no está activado";
}

foreach ($required_classes as $file => $class_name) {
    if (!file_exists($plugin_dir . $file)) {
        $problems[] = "Archivo faltante: $file";
    } elseif (!class_exists($class_name)) {
        $problems[] = "Clase no se puede cargar: $class_name";
    }
}

foreach ($tables as $table => $description) {
    if ($wpdb->get_var("SHOW TABLES LIKE '$table'") != $table) {
        $problems[] = "Tabla de BD faltante: $table";
    }
}

if (!$soloylibre_post_type_exists) {
    $problems[] = "Custom post type 'soloylibre_photo' no registrado";
}

if (empty($problems)) {
    echo "🎉 ¡No se encontraron problemas críticos!\n";
} else {
    echo "⚠️ PROBLEMAS ENCONTRADOS:\n";
    foreach ($problems as $i => $problem) {
        echo "   " . ($i + 1) . ". $problem\n";
    }
}

echo "\n📞 INFORMACIÓN DE CONTACTO:\n";
echo "   👨‍💻 Desarrollador: JEYKO AI\n";
echo "   📧 Cliente: Jose L Encarnacion (JoseTusabe)\n";
echo "   📱 Teléfono: ************\n";
echo "   🌐 Sitios: josetusabe.com, soloylibre.com\n";

?>
