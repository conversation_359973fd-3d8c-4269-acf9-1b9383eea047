<?php
// Página de prueba funcional para SoloYLibre
echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🧪 Prueba Funcional - SoloYLibre Gallery Pro</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f0f0f0; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }";
echo ".test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }";
echo ".success { background: #d4edda; border-color: #c3e6cb; color: #155724; }";
echo ".error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }";
echo ".btn { background: #CE1126; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; display: inline-block; margin: 10px 5px; font-weight: 600; }";
echo ".btn:hover { background: #002D62; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class=\"container\">";
echo "<div class=\"header\">";
echo "<h1>🧪 Prueba Funcional - SoloYLibre Gallery Pro</h1>";
echo "<p>🇩🇴 Jose L Encarnacion (JoseTusabe) - Verificación del Sistema</p>";
echo "</div>";

// Test 1: WordPress Loading
echo "<div class=\"test-section\">";
echo "<h3>🔧 Test 1: Carga de WordPress</h3>";
try {
    require_once("wp-load.php");
    echo "<p class=\"success\">✅ WordPress cargado correctamente</p>";
    $wp_test_passed = true;
} catch (Exception $e) {
    echo "<p class=\"error\">❌ Error cargando WordPress: " . $e->getMessage() . "</p>";
    $wp_test_passed = false;
}
echo "</div>";

if ($wp_test_passed) {
    // Test 2: Plugin Status
    echo "<div class=\"test-section\">";
    echo "<h3>🔌 Test 2: Estado del Plugin</h3>";
    $active_plugins = get_option("active_plugins", array());
    $soloylibre_active = false;
    foreach ($active_plugins as $plugin) {
        if (strpos($plugin, "Archive") !== false) {
            $soloylibre_active = true;
            break;
        }
    }
    
    if ($soloylibre_active) {
        echo "<p class=\"success\">✅ Plugin SoloYLibre activo</p>";
    } else {
        echo "<p class=\"error\">❌ Plugin SoloYLibre no está activo</p>";
    }
    echo "</div>";
    
    // Test 3: Classes Loading
    echo "<div class=\"test-section\">";
    echo "<h3>📚 Test 3: Clases del Plugin</h3>";
    $classes = array(
        "SoloYLibre_Gallery_Plugin",
        "SoloYLibre_Simple_Wizard", 
        "SoloYLibre_Photo_Wizard",
        "SoloYLibre_Statistics_Dashboard"
    );
    
    $classes_loaded = 0;
    foreach ($classes as $class) {
        if (class_exists($class)) {
            echo "<p class=\"success\">✅ $class cargada</p>";
            $classes_loaded++;
        } else {
            echo "<p class=\"error\">❌ $class no encontrada</p>";
        }
    }
    
    if ($classes_loaded == count($classes)) {
        echo "<p class=\"success\"><strong>✅ Todas las clases cargadas correctamente</strong></p>";
    }
    echo "</div>";
    
    // Test 4: Database Tables
    echo "<div class=\"test-section\">";
    echo "<h3>🗄️ Test 4: Tablas de Base de Datos</h3>";
    global $wpdb;
    
    $tables = array(
        "wp_soloylibre_albums",
        "wp_soloylibre_photos",
        "wp_soloylibre_interactions"
    );
    
    $tables_ok = 0;
    foreach ($tables as $table) {
        $exists = $wpdb->get_var("SHOW TABLES LIKE \"$table\"") == $table;
        if ($exists) {
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
            echo "<p class=\"success\">✅ $table existe ($count registros)</p>";
            $tables_ok++;
        } else {
            echo "<p class=\"error\">❌ $table no existe</p>";
        }
    }
    
    if ($tables_ok == count($tables)) {
        echo "<p class=\"success\"><strong>✅ Todas las tablas funcionando</strong></p>";
    }
    echo "</div>";
    
    // Test 5: User Access
    echo "<div class=\"test-section\">";
    echo "<h3>👤 Test 5: Acceso de Usuario</h3>";
    $current_user = wp_get_current_user();
    if ($current_user->ID > 0) {
        echo "<p class=\"success\">✅ Usuario logueado: " . $current_user->user_login . "</p>";
        if (current_user_can("manage_options")) {
            echo "<p class=\"success\">✅ Permisos de administrador</p>";
        } else {
            echo "<p class=\"error\">❌ Sin permisos de administrador</p>";
        }
    } else {
        echo "<p class=\"error\">❌ No hay usuario logueado</p>";
    }
    echo "</div>";
}

// Test Results Summary
echo "<div class=\"test-section\">";
echo "<h3>📊 Resumen de Pruebas</h3>";
if ($wp_test_passed && $soloylibre_active && $classes_loaded >= 3 && $tables_ok >= 2) {
    echo "<p class=\"success\"><strong>🎉 ¡TODAS LAS PRUEBAS PASARON!</strong></p>";
    echo "<p>El plugin SoloYLibre Gallery Pro está funcionando correctamente.</p>";
} else {
    echo "<p class=\"error\"><strong>⚠️ Algunas pruebas fallaron</strong></p>";
    echo "<p>Revisa los errores anteriores y contacta soporte.</p>";
}
echo "</div>";

// Action Links
echo "<h2>🔗 Acciones Disponibles</h2>";
echo "<a href=\"wp-admin/\" class=\"btn\">🌐 Admin WordPress</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-simple-wizard\" class=\"btn\">🧙‍♂️ Wizard Simple</a>";
echo "<a href=\"wp-admin/admin.php?page=soloylibre-statistics\" class=\"btn\">📊 Estadísticas</a>";
echo "<a href=\"auto-login-soloylibre.php\" class=\"btn\">🔑 Login Automático</a>";

// Contact Info
echo "<hr>";
echo "<h3>📞 Información de Contacto</h3>";
echo "<div style=\"background: #f8f9fa; padding: 20px; border-radius: 10px;\">";
echo "<h4>👨‍💻 Jose L Encarnacion (JoseTusabe)</h4>";
echo "<p>🏢 <strong>SoloYLibre Photography</strong></p>";
echo "<p>📍 San José de Ocoa, República Dominicana 🇩🇴 / USA 🇺🇸</p>";
echo "<p>📞 718-713-5500 | 📧 <EMAIL></p>";
echo "<p>🌐 josetusabe.com | soloylibre.com | 1and1photo.com | joselencarnacion.com</p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>