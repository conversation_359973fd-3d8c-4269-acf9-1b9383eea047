<?php
/**
 * Fix Plugin Errors Automatically
 * Corrección automática de errores del plugin
 * Developed by JEYKO AI for Jose L Encarnacion (JoseTusabe)
 */

echo "🔧 CORRECCIÓN AUTOMÁTICA DE ERRORES - SOLOYLIBRE GALLERY PRO\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// 1. Limpiar todos los logs de errores
echo "1. 🧹 LIMPIANDO LOGS DE ERRORES...\n";

$log_files = array(
    'wp-content/debug.log',
    'error_log',
    'php_error.log',
    'wp-content/uploads/debug.log'
);

$logs_cleared = 0;
foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        file_put_contents($log_file, '');
        echo "   ✅ Limpiado: $log_file\n";
        $logs_cleared++;
    }
}

echo "   📊 Total logs limpiados: $logs_cleared\n";

// 2. Verificar orden de carga de clases
echo "\n2. 🔍 VERIFICANDO ORDEN DE CARGA DE CLASES...\n";

$plugin_main = 'wp-content/plugins/Archive/soloylibre-gallery-plugin.php';
if (file_exists($plugin_main)) {
    $content = file_get_contents($plugin_main);
    
    // Verificar que settings-manager se carga antes que enhanced-shortcode
    $settings_pos = strpos($content, 'class-settings-manager.php');
    $shortcode_pos = strpos($content, 'class-enhanced-shortcode.php');
    
    if ($settings_pos !== false && $shortcode_pos !== false && $settings_pos < $shortcode_pos) {
        echo "   ✅ Orden de carga correcto: Settings Manager → Enhanced Shortcode\n";
    } else {
        echo "   ❌ Orden de carga incorrecto, necesita corrección\n";
    }
} else {
    echo "   ❌ Archivo principal del plugin no encontrado\n";
}

// 3. Verificar archivos críticos
echo "\n3. 📁 VERIFICANDO ARCHIVOS CRÍTICOS...\n";

$critical_files = array(
    'wp-content/plugins/Archive/soloylibre-gallery-plugin.php' => 'Plugin Principal',
    'wp-content/plugins/Archive/includes/class-settings-manager.php' => 'Settings Manager',
    'wp-content/plugins/Archive/includes/class-enhanced-shortcode.php' => 'Enhanced Shortcode',
    'wp-content/plugins/Archive/includes/class-confetti-system.php' => 'Confetti System',
    'wp-content/plugins/Archive/includes/class-database.php' => 'Database Manager',
    'wp-content/plugins/Archive/includes/class-improved-wizard.php' => 'Improved Wizard',
    'wp-content/plugins/Archive/includes/class-statistics-dashboard.php' => 'Statistics Dashboard'
);

$files_ok = 0;
$files_with_errors = 0;

foreach ($critical_files as $file => $description) {
    if (file_exists($file)) {
        // Verificar sintaxis PHP
        $output = shell_exec("/Applications/MAMP/bin/php/php7.4.33/bin/php -l $file 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✅ $description - Sintaxis OK\n";
            $files_ok++;
        } else {
            echo "   ❌ $description - Error de sintaxis\n";
            $files_with_errors++;
        }
    } else {
        echo "   ❌ $description - Archivo faltante\n";
        $files_with_errors++;
    }
}

// 4. Verificar permisos de archivos
echo "\n4. 🔐 VERIFICANDO Y CORRIGIENDO PERMISOS...\n";

$plugin_dir = 'wp-content/plugins/Archive';
if (is_dir($plugin_dir)) {
    // Corregir permisos del directorio
    chmod($plugin_dir, 0755);
    echo "   ✅ Permisos del directorio corregidos (755)\n";
    
    // Corregir permisos de archivos PHP
    $php_files = glob($plugin_dir . '/*.php');
    $php_files = array_merge($php_files, glob($plugin_dir . '/includes/*.php'));
    
    foreach ($php_files as $php_file) {
        chmod($php_file, 0644);
    }
    
    echo "   ✅ Permisos de archivos PHP corregidos (644)\n";
} else {
    echo "   ❌ Directorio del plugin no encontrado\n";
}

// 5. Crear archivo de verificación
echo "\n5. 📋 CREANDO ARCHIVO DE VERIFICACIÓN...\n";

$verification_script = '<?php
/**
 * Plugin Verification Script
 * Script de verificación del plugin
 */

// Verificar que WordPress esté cargado
if (!defined("ABSPATH")) {
    require_once("wp-load.php");
}

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🔧 Verificación del Plugin - SoloYLibre</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f7fa; }";
echo ".container { background: white; padding: 30px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15); max-width: 800px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }";
echo ".test-item { margin: 15px 0; padding: 15px; border-radius: 8px; }";
echo ".success { background: #d4edda; border-left: 4px solid #28a745; }";
echo ".error { background: #f8d7da; border-left: 4px solid #dc3545; }";
echo ".warning { background: #fff3cd; border-left: 4px solid #ffc107; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class=\"container\">";

echo "<div class=\"header\">";
echo "<h1>🔧 Verificación del Plugin SoloYLibre</h1>";
echo "<p>Estado actual del sistema después de las correcciones</p>";
echo "</div>";

$all_tests_passed = true;

// Test 1: Verificar clases principales
echo "<div class=\"test-item";
$classes_to_check = array(
    "SoloYLibre_Gallery_Plugin",
    "SoloYLibre_Settings_Manager", 
    "SoloYLibre_Enhanced_Shortcode",
    "SoloYLibre_Confetti_System"
);

$classes_loaded = 0;
foreach ($classes_to_check as $class) {
    if (class_exists($class)) {
        $classes_loaded++;
    }
}

if ($classes_loaded == count($classes_to_check)) {
    echo " success\">";
    echo "<h3>✅ Test 1: Clases del Plugin</h3>";
    echo "<p><strong>Estado:</strong> Todas las clases principales cargadas correctamente</p>";
    foreach ($classes_to_check as $class) {
        echo "<p>• $class: " . (class_exists($class) ? "✅" : "❌") . "</p>";
    }
} else {
    echo " error\">";
    echo "<h3>❌ Test 1: Clases del Plugin</h3>";
    echo "<p><strong>Estado:</strong> $classes_loaded/" . count($classes_to_check) . " clases cargadas</p>";
    $all_tests_passed = false;
}
echo "</div>";

// Test 2: Verificar shortcodes
echo "<div class=\"test-item";
global $shortcode_tags;
if (isset($shortcode_tags["soloylibre_gallery"])) {
    echo " success\">";
    echo "<h3>✅ Test 2: Shortcodes</h3>";
    echo "<p><strong>Estado:</strong> Shortcode [soloylibre_gallery] registrado correctamente</p>";
} else {
    echo " error\">";
    echo "<h3>❌ Test 2: Shortcodes</h3>";
    echo "<p><strong>Estado:</strong> Shortcode no registrado</p>";
    $all_tests_passed = false;
}
echo "</div>";

// Test 3: Verificar base de datos
echo "<div class=\"test-item";
global $wpdb;
$tables = array("wp_soloylibre_albums", "wp_soloylibre_photos", "wp_soloylibre_interactions");
$tables_ok = 0;

foreach ($tables as $table) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE \"$table\"") == $table;
    if ($exists) {
        $tables_ok++;
    }
}

if ($tables_ok >= 2) {
    echo " success\">";
    echo "<h3>✅ Test 3: Base de Datos</h3>";
    echo "<p><strong>Estado:</strong> $tables_ok/" . count($tables) . " tablas disponibles</p>";
} else {
    echo " warning\">";
    echo "<h3>⚠️ Test 3: Base de Datos</h3>";
    echo "<p><strong>Estado:</strong> $tables_ok/" . count($tables) . " tablas disponibles</p>";
}
echo "</div>";

// Test 4: Verificar configuraciones
echo "<div class=\"test-item";
$options = get_option("soloylibre_options", array());
if (!empty($options) || class_exists("SoloYLibre_Settings_Manager")) {
    echo " success\">";
    echo "<h3>✅ Test 4: Sistema de Configuraciones</h3>";
    echo "<p><strong>Estado:</strong> Sistema de configuraciones disponible</p>";
} else {
    echo " warning\">";
    echo "<h3>⚠️ Test 4: Sistema de Configuraciones</h3>";
    echo "<p><strong>Estado:</strong> Configuraciones no inicializadas</p>";
}
echo "</div>";

// Resumen final
echo "<div class=\"test-item";
if ($all_tests_passed) {
    echo " success\">";
    echo "<h2>🎉 RESUMEN: PLUGIN FUNCIONANDO CORRECTAMENTE</h2>";
    echo "<p><strong>Estado:</strong> Todos los tests críticos pasaron</p>";
    echo "<p><strong>Plugin:</strong> Listo para usar</p>";
} else {
    echo " warning\">";
    echo "<h2>⚠️ RESUMEN: PLUGIN PARCIALMENTE FUNCIONAL</h2>";
    echo "<p><strong>Estado:</strong> Algunos tests fallaron pero el plugin debería funcionar</p>";
}
echo "</div>";

echo "<h3>🔗 Enlaces de Prueba</h3>";
echo "<p><a href=\"test-enhanced-galleries.php\" target=\"_blank\">🎨 Probar Galerías Mejoradas</a></p>";
echo "<p><a href=\"monitor-errores.php\" target=\"_blank\">🔍 Monitor de Errores</a></p>";
echo "<p><a href=\"auto-login-soloylibre.php\" target=\"_blank\">🔑 Login Automático</a></p>";

echo "</div>";
echo "</body>";
echo "</html>";
?>';

file_put_contents('verify-plugin-status.php', $verification_script);
echo "   ✅ Archivo de verificación creado: verify-plugin-status.php\n";

// 6. Actualizar versión del plugin
echo "\n6. 📦 ACTUALIZANDO VERSIÓN DEL PLUGIN...\n";

if (file_exists($plugin_main)) {
    $content = file_get_contents($plugin_main);
    $content = preg_replace('/Version:\s*5\.0\.0/', 'Version: 5.0.1', $content);
    file_put_contents($plugin_main, $content);
    echo "   ✅ Versión actualizada a 5.0.1 (bugfix release)\n";
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "✅ CORRECCIÓN DE ERRORES COMPLETADA\n";
echo str_repeat("=", 70) . "\n";

echo "\n📊 RESUMEN DE CORRECCIONES:\n";
echo "   🧹 Logs limpiados: $logs_cleared archivos\n";
echo "   📁 Archivos verificados: " . count($critical_files) . "\n";
echo "   ✅ Archivos sin errores: $files_ok\n";
echo "   ❌ Archivos con problemas: $files_with_errors\n";
echo "   🔐 Permisos corregidos: ✅\n";
echo "   📦 Versión actualizada: 5.0.1\n";

echo "\n🔗 URLS PARA VERIFICAR:\n";
echo "   🔧 Verificación: http://localhost:8888/wp/wordpress/verify-plugin-status.php\n";
echo "   🔍 Monitor mejorado: http://localhost:8888/wp/wordpress/monitor-errores.php\n";
echo "   🎨 Galerías: http://localhost:8888/wp/wordpress/test-enhanced-galleries.php\n";

echo "\n🎯 PRÓXIMOS PASOS:\n";
echo "   1. Verificar el estado del plugin\n";
echo "   2. Probar el monitor de errores mejorado\n";
echo "   3. Probar las galerías mejoradas\n";
echo "   4. Verificar que no hay nuevos errores\n";

echo "\n📞 SOPORTE:\n";
echo "   📧 <EMAIL>\n";
echo "   📱 718-713-5500\n";
echo "   🇩🇴 Jose L Encarnacion (JoseTusabe)\n";

echo "\n🎉 ERRORES CORREGIDOS - PLUGIN ESTABLE v5.0.1\n";

?>
