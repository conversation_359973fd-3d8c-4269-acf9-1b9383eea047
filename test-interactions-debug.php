<?php
/**
 * Test Interactions Debug
 * Prueba y debug de interacciones
 * Developed by JEYKO AI for <PERSON> (JoseTusabe)
 */

require_once('wp-load.php');

echo "<!DOCTYPE html>";
echo "<html>";
echo "<head>";
echo "<title>🧪 Debug de Interacciones - SoloYLibre</title>";
echo "<meta charset=\"UTF-8\">";
echo "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">";

wp_head();

echo "<style>";
echo "body { margin: 0; padding: 20px; background: #1a1a1a; color: #00ff00; font-family: 'Courier New', monospace; }";
echo ".container { max-width: 1200px; margin: 0 auto; }";
echo ".header { background: linear-gradient(135deg, #CE1126 0%, #002D62 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }";
echo ".debug-section { background: #2d2d2d; border: 1px solid #444; border-radius: 8px; margin: 20px 0; padding: 20px; }";
echo ".debug-title { color: #00ff00; font-size: 1.3rem; margin-bottom: 15px; }";
echo ".test-gallery { background: #333; padding: 20px; border-radius: 8px; margin: 15px 0; }";
echo ".btn { background: #CE1126; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }";
echo ".btn:hover { background: #002D62; }";
echo ".success { color: #28a745; }";
echo ".error { color: #dc3545; }";
echo ".warning { color: #ffc107; }";
echo "</style>";

echo "<script src=\"https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js\"></script>";

echo "</head>";
echo "<body>";

echo "<div class=\"container\">";

echo "<div class=\"header\">";
echo "<h1>🧪 Debug de Interacciones SoloYLibre</h1>";
echo "<p>Prueba y diagnóstico del sistema de interacciones</p>";
echo "</div>";

// 1. Verificar configuración AJAX
echo "<div class=\"debug-section\">";
echo "<h2 class=\"debug-title\">1. 🔗 Configuración AJAX</h2>";

global $wp_scripts;
$jquery_data = $wp_scripts->get_data('jquery', 'data');

echo "<p><strong>AJAX URL:</strong> " . admin_url('admin-ajax.php') . "</p>";
echo "<p><strong>Nonce:</strong> " . wp_create_nonce('gallery_interaction') . "</p>";
echo "<p><strong>jQuery cargado:</strong> " . (wp_script_is('jquery', 'enqueued') ? '✅ Sí' : '❌ No') . "</p>";

// Verificar si las acciones AJAX están registradas
$ajax_registered = has_action('wp_ajax_gallery_interaction') && has_action('wp_ajax_nopriv_gallery_interaction');
echo "<p><strong>Acciones AJAX registradas:</strong> " . ($ajax_registered ? '✅ Sí' : '❌ No') . "</p>";

echo "</div>";

// 2. Verificar base de datos
echo "<div class=\"debug-section\">";
echo "<h2 class=\"debug-title\">2. 📊 Estado de Base de Datos</h2>";

global $wpdb;
$table_name = $wpdb->prefix . 'soloylibre_interactions';

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
echo "<p><strong>Tabla existe:</strong> " . ($table_exists ? '✅ Sí' : '❌ No') . "</p>";

if ($table_exists) {
    $total_interactions = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    $total_photos = $wpdb->get_var("SELECT COUNT(DISTINCT photo_id) FROM $table_name");
    
    echo "<p><strong>Total interacciones:</strong> $total_interactions</p>";
    echo "<p><strong>Fotos con interacciones:</strong> $total_photos</p>";
    
    // Mostrar últimas interacciones
    $recent = $wpdb->get_results("SELECT * FROM $table_name ORDER BY updated_at DESC LIMIT 5");
    
    if (!empty($recent)) {
        echo "<h3>Últimas interacciones:</h3>";
        echo "<table style=\"width: 100%; border-collapse: collapse; color: #00ff00;\">";
        echo "<tr style=\"border-bottom: 1px solid #444;\">";
        echo "<th style=\"padding: 8px; text-align: left;\">Foto ID</th>";
        echo "<th style=\"padding: 8px; text-align: left;\">Tipo</th>";
        echo "<th style=\"padding: 8px; text-align: left;\">Contador</th>";
        echo "<th style=\"padding: 8px; text-align: left;\">Fecha</th>";
        echo "</tr>";
        
        foreach ($recent as $interaction) {
            echo "<tr style=\"border-bottom: 1px solid #333;\">";
            echo "<td style=\"padding: 8px;\">{$interaction->photo_id}</td>";
            echo "<td style=\"padding: 8px;\">{$interaction->interaction_type}</td>";
            echo "<td style=\"padding: 8px;\">{$interaction->interaction_count}</td>";
            echo "<td style=\"padding: 8px;\">{$interaction->updated_at}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
}

echo "</div>";

// 3. Galería de prueba
echo "<div class=\"debug-section\">";
echo "<h2 class=\"debug-title\">3. 🎨 Galería de Prueba</h2>";

$test_photos = get_posts(array(
    'post_type' => 'attachment',
    'post_mime_type' => 'image',
    'posts_per_page' => 3,
    'post_status' => 'inherit'
));

if (!empty($test_photos)) {
    $photo_ids = implode(',', wp_list_pluck($test_photos, 'ID'));
    
    echo "<p><strong>Fotos de prueba:</strong> " . count($test_photos) . " imágenes</p>";
    echo "<p><strong>IDs:</strong> $photo_ids</p>";
    
    echo "<div class=\"test-gallery\">";
    echo do_shortcode("[soloylibre_gallery style=\"dominican\" ids=\"$photo_ids\" columns=\"3\" enable_interactions=\"true\" auto_confetti=\"true\"]");
    echo "</div>";
    
} else {
    echo "<p class=\"error\">❌ No se encontraron fotos para la prueba</p>";
}

echo "</div>";

// 4. Prueba manual de AJAX
echo "<div class=\"debug-section\">";
echo "<h2 class=\"debug-title\">4. 🔧 Prueba Manual de AJAX</h2>";

if (!empty($test_photos)) {
    $test_photo_id = $test_photos[0]->ID;
    
    echo "<p><strong>Foto de prueba:</strong> ID $test_photo_id</p>";
    echo "<button class=\"btn\" onclick=\"testAjaxInteraction($test_photo_id, 'like')\">❤️ Probar Like</button>";
    echo "<button class=\"btn\" onclick=\"testAjaxInteraction($test_photo_id, 'view')\">👁️ Probar Vista</button>";
    echo "<button class=\"btn\" onclick=\"testAjaxInteraction($test_photo_id, 'share')\">📤 Probar Compartir</button>";
    
    echo "<div id=\"ajax-results\" style=\"margin-top: 15px; padding: 10px; background: #333; border-radius: 5px;\"></div>";
}

echo "</div>";

// 5. Console log
echo "<div class=\"debug-section\">";
echo "<h2 class=\"debug-title\">5. 📝 Console Log</h2>";
echo "<p>Abre las herramientas de desarrollador (F12) y revisa la consola para ver los logs de debug.</p>";
echo "<button class=\"btn\" onclick=\"console.clear(); alert('Console limpiado');\">🧹 Limpiar Console</button>";
echo "</div>";

echo "</div>"; // container

// JavaScript de debug
echo "<script>";
echo "console.log('🧪 SoloYLibre Debug: Página cargada');";
echo "console.log('AJAX Config:', soloylibre_ajax);";

echo "function testAjaxInteraction(photoId, action) {";
echo "    console.log('🧪 Probando interacción:', {photoId: photoId, action: action});";
echo "    ";
echo "    var resultsDiv = document.getElementById('ajax-results');";
echo "    resultsDiv.innerHTML = '⏳ Enviando solicitud...';";
echo "    ";
echo "    jQuery.post(soloylibre_ajax.ajax_url, {";
echo "        action: 'gallery_interaction',";
echo "        photo_id: photoId,";
echo "        action_type: action,";
echo "        nonce: soloylibre_ajax.nonce";
echo "    }, function(response) {";
echo "        console.log('🧪 Respuesta recibida:', response);";
echo "        ";
echo "        if (response.success) {";
echo "            resultsDiv.innerHTML = '<span class=\"success\">✅ Éxito: ' + response.data.message + '<br>Nuevo contador: ' + response.data.new_count + '</span>';";
echo "            ";
echo "            if (typeof confetti !== 'undefined') {";
echo "                confetti({";
echo "                    particleCount: 50,";
echo "                    spread: 70,";
echo "                    origin: { y: 0.6 },";
echo "                    colors: ['#CE1126', '#002D62', '#00ff00']";
echo "                });";
echo "            }";
echo "        } else {";
echo "            resultsDiv.innerHTML = '<span class=\"error\">❌ Error: ' + (response.data || 'Error desconocido') + '</span>';";
echo "        }";
echo "    }).fail(function(xhr, status, error) {";
echo "        console.error('🧪 Error AJAX:', {xhr: xhr, status: status, error: error});";
echo "        resultsDiv.innerHTML = '<span class=\"error\">❌ Error de conexión: ' + error + '</span>';";
echo "    });";
echo "}";

echo "// Monitor de clics en botones de interacción";
echo "jQuery(document).ready(function($) {";
echo "    console.log('🧪 jQuery listo, monitoreando interacciones...');";
echo "    ";
echo "    $(document).on('click', '.interaction-btn', function() {";
echo "        console.log('🧪 Clic detectado en botón de interacción:', this);";
echo "    });";
echo "});";

echo "</script>";

wp_footer();

echo "</body>";
echo "</html>";
?>
